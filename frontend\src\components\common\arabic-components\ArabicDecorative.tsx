import React from 'react';
import {
  Typography,
  TypographyProps,
  Box,
  useTheme,
  alpha,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useRtl } from '../../../utils/rtlUtils';

interface ArabicDecorativeProps extends Omit<TypographyProps, 'children'> {
  textKey: string;
  values?: Record<string, any>;
  decorativeStyle?: 'standard' | 'calligraphy' | 'modern' | 'geometric';
  showBorder?: boolean;
  borderColor?: string;
  borderStyle?: 'solid' | 'dashed' | 'dotted' | 'double';
  borderWidth?: number;
  borderRadius?: number;
  padding?: number | string;
  margin?: number | string;
  backgroundColor?: string;
  backgroundOpacity?: number;
  shadow?: boolean;
  shadowColor?: string;
  shadowOpacity?: number;
  shadowBlur?: number;
  shadowSpread?: number;
  shadowOffsetX?: number;
  shadowOffsetY?: number;
  gradient?: boolean;
  gradientColors?: string[];
  gradientDirection?: string;
  gradientOpacity?: number;
  animate?: boolean;
  animationDuration?: number;
  animationDelay?: number;
  animationType?: 'fade' | 'slide' | 'scale' | 'rotate' | 'bounce';
  animationDirection?: 'left' | 'right' | 'up' | 'down';
  animationEasing?: string;
  animationIterationCount?: number | 'infinite';
  animationFillMode?: 'none' | 'forwards' | 'backwards' | 'both';
  animationPlayState?: 'running' | 'paused';
  onClick?: () => void;
}

const ArabicDecorative: React.FC<ArabicDecorativeProps> = ({
  textKey,
  values = {},
  decorativeStyle = 'standard',
  showBorder = false,
  borderColor,
  borderStyle = 'solid',
  borderWidth = 1,
  borderRadius = 4,
  padding,
  margin,
  backgroundColor,
  backgroundOpacity = 0.1,
  shadow = false,
  shadowColor,
  shadowOpacity = 0.2,
  shadowBlur = 10,
  shadowSpread = 0,
  shadowOffsetX = 0,
  shadowOffsetY = 4,
  gradient = false,
  gradientColors,
  gradientDirection = '135deg',
  gradientOpacity = 1,
  animate = false,
  animationDuration = 0.5,
  animationDelay = 0,
  animationType = 'fade',
  animationDirection = 'up',
  animationEasing = 'ease-out',
  animationIterationCount = 1,
  animationFillMode = 'both',
  animationPlayState = 'running',
  onClick,
  ...typographyProps
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { isRtl } = useRtl();

  // Default values for decorative styles
  const getDecorativeStyles = () => {
    const baseStyles = {
      fontWeight: 'bold' as const,
      position: 'relative' as const,
      display: 'inline-block' as const,
      ...typographyProps.sx,
    };

    // Apply different styles based on decorativeStyle
    switch (decorativeStyle) {
      case 'calligraphy':
        return {
          ...baseStyles,
          fontFamily: isRtl ? '"Amiri", "Scheherazade New", serif' : undefined,
          letterSpacing: isRtl ? '0.5px' : undefined,
          background: isRtl
            ? `linear-gradient(${gradientDirection}, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
            : undefined,
          WebkitBackgroundClip: isRtl ? 'text' : undefined,
          WebkitTextFillColor: isRtl ? 'transparent' : undefined,
          textShadow: isRtl
            ? `0 2px 4px ${alpha(theme.palette.primary.main, 0.3)}`
            : undefined,
          '&::after': isRtl
            ? {
                content: '""',
                position: 'absolute',
                bottom: '-4px',
                left: '10%',
                width: '80%',
                height: '1px',
                background: `linear-gradient(90deg, transparent, ${theme.palette.primary.main} 50%, transparent)`,
              }
            : undefined,
        };

      case 'modern':
        return {
          ...baseStyles,
          fontFamily: isRtl ? '"Cairo", "Tajawal", sans-serif' : undefined,
          letterSpacing: isRtl ? '0px' : undefined,
          background: isRtl
            ? `linear-gradient(${gradientDirection}, ${theme.palette.info.main}, ${theme.palette.primary.main})`
            : undefined,
          WebkitBackgroundClip: isRtl ? 'text' : undefined,
          WebkitTextFillColor: isRtl ? 'transparent' : undefined,
          padding: isRtl ? '0.25em 0' : undefined,
          borderBottom: isRtl
            ? `2px solid ${theme.palette.primary.main}`
            : undefined,
        };

      case 'geometric':
        return {
          ...baseStyles,
          fontFamily: isRtl
            ? '"IBM Plex Sans Arabic", "Tajawal", sans-serif'
            : undefined,
          letterSpacing: isRtl ? '1px' : undefined,
          textTransform: isRtl ? 'uppercase' : undefined,
          color: isRtl ? theme.palette.primary.main : undefined,
          '&::before': isRtl
            ? {
                content: '""',
                position: 'absolute',
                top: '50%',
                left: '-15px',
                width: '10px',
                height: '10px',
                transform: 'translateY(-50%) rotate(45deg)',
                backgroundColor: theme.palette.primary.main,
              }
            : undefined,
          '&::after': isRtl
            ? {
                content: '""',
                position: 'absolute',
                top: '50%',
                right: '-15px',
                width: '10px',
                height: '10px',
                transform: 'translateY(-50%) rotate(45deg)',
                backgroundColor: theme.palette.primary.main,
              }
            : undefined,
        };

      default:
        return baseStyles;
    }
  };

  // Get animation styles
  const getAnimationStyles = () => {
    if (!animate) return {};

    const keyframes = {
      fade: {
        from: { opacity: 0 },
        to: { opacity: 1 },
      },
      slide: {
        from: {
          opacity: 0,
          transform: `translateY(${
            animationDirection === 'up'
              ? '20px'
              : animationDirection === 'down'
                ? '-20px'
                : '0'
          }) translateX(${
            animationDirection === 'left'
              ? '20px'
              : animationDirection === 'right'
                ? '-20px'
                : '0'
          })`,
        },
        to: {
          opacity: 1,
          transform: 'translateY(0) translateX(0)',
        },
      },
      scale: {
        from: { transform: 'scale(0.8)', opacity: 0 },
        to: { transform: 'scale(1)', opacity: 1 },
      },
      rotate: {
        from: { transform: 'rotate(-5deg)', opacity: 0 },
        to: { transform: 'rotate(0)', opacity: 1 },
      },
      bounce: {
        '0%': { transform: 'translateY(0)', opacity: 0 },
        '50%': { transform: 'translateY(-10px)' },
        '100%': { transform: 'translateY(0)', opacity: 1 },
      },
    };

    // Simplified animation without @keyframes to avoid Stylis parser issues
    return {
      transition: 'all 0.3s ease',
      // Remove complex animations to prevent Stylis parser errors
    };
  };

  // Combine all styles
  const combinedStyles = {
    ...getDecorativeStyles(),
    ...(showBorder && {
      border: `${borderWidth}px ${borderStyle} ${borderColor || theme.palette.primary.main}`,
      borderRadius: `${borderRadius}px`,
      padding: padding || '0.5em 1em',
    }),
    ...(margin && { margin }),
    ...(backgroundColor && {
      backgroundColor: backgroundColor,
    }),
    ...(shadow && {
      boxShadow: theme.shadows[2],
    }),
    ...(gradient && {
      background: `linear-gradient(${gradientDirection}, ${
        gradientColors?.[0] || theme.palette.primary.main
      }, ${gradientColors?.[1] || theme.palette.secondary.main})`,
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      opacity: gradientOpacity,
    }),
    ...getAnimationStyles(),
  };

  return (
    <Typography {...typographyProps} sx={combinedStyles} onClick={onClick}>
      {t(textKey, {
        ...values,
        returnObjects: false,
        defaultValue: values?.defaultValue || textKey,
      })}
    </Typography>
  );
};

export default ArabicDecorative;

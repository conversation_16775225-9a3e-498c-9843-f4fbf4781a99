"""
Base service and repository classes for the University Management System.

This module provides the foundation for implementing the service layer pattern
and repository pattern to separate business logic from models and views.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type, TypeVar, Generic, Union
from django.db import models, transaction
from django.core.exceptions import ValidationError
from django.core.paginator import Paginator
from django.http import QueryDict
import logging

logger = logging.getLogger(__name__)

# Type variables for generic type hints
ModelType = TypeVar('ModelType', bound=models.Model)
ServiceType = TypeVar('ServiceType')


class ServiceError(Exception):
    """Base exception for service layer errors."""
    pass


class ValidationServiceError(ServiceError):
    """Exception raised when validation fails in service layer."""
    pass


class NotFoundServiceError(ServiceError):
    """Exception raised when a requested resource is not found."""
    pass


class PermissionServiceError(ServiceError):
    """Exception raised when user lacks permission for an operation."""
    pass


class BaseRepository(Generic[ModelType], ABC):
    """
    Abstract base repository class implementing common data access patterns.
    
    This class provides a standardized interface for data access operations,
    separating database concerns from business logic.
    """
    
    def __init__(self, model: Type[ModelType]) -> None:
        """
        Initialize repository with model class.
        
        Args:
            model: Django model class for this repository
        """
        self.model = model
    
    def get_by_id(self, id: int) -> Optional[ModelType]:
        """
        Retrieve a single object by ID.
        
        Args:
            id: Primary key of the object
            
        Returns:
            Model instance or None if not found
        """
        try:
            return self.model.objects.get(pk=id)
        except self.model.DoesNotExist:
            return None
    
    def get_by_id_or_raise(self, id: int) -> ModelType:
        """
        Retrieve a single object by ID or raise exception.
        
        Args:
            id: Primary key of the object
            
        Returns:
            Model instance
            
        Raises:
            NotFoundServiceError: If object not found
        """
        obj = self.get_by_id(id)
        if obj is None:
            raise NotFoundServiceError(f"{self.model._meta.verbose_name} with ID {id} not found")
        return obj
    
    def get_all(self) -> models.QuerySet[ModelType]:
        """
        Get all objects.
        
        Returns:
            QuerySet of all objects
        """
        return self.model.objects.all()
    
    def filter(self, **kwargs: Any) -> models.QuerySet[ModelType]:
        """
        Filter objects by given parameters.
        
        Args:
            **kwargs: Filter parameters
            
        Returns:
            Filtered QuerySet
        """
        return self.model.objects.filter(**kwargs)
    
    def create(self, **kwargs: Any) -> ModelType:
        """
        Create a new object.
        
        Args:
            **kwargs: Object attributes
            
        Returns:
            Created model instance
            
        Raises:
            ValidationServiceError: If validation fails
        """
        try:
            obj = self.model(**kwargs)
            obj.full_clean()
            obj.save()
            return obj
        except ValidationError as e:
            raise ValidationServiceError(f"Validation failed: {e}")
    
    def update(self, obj: ModelType, **kwargs: Any) -> ModelType:
        """
        Update an existing object.
        
        Args:
            obj: Object to update
            **kwargs: New attribute values
            
        Returns:
            Updated model instance
            
        Raises:
            ValidationServiceError: If validation fails
        """
        try:
            for key, value in kwargs.items():
                setattr(obj, key, value)
            obj.full_clean()
            obj.save()
            return obj
        except ValidationError as e:
            raise ValidationServiceError(f"Validation failed: {e}")
    
    def delete(self, obj: ModelType) -> bool:
        """
        Delete an object.
        
        Args:
            obj: Object to delete
            
        Returns:
            True if successful
        """
        obj.delete()
        return True
    
    def exists(self, **kwargs: Any) -> bool:
        """
        Check if objects matching criteria exist.
        
        Args:
            **kwargs: Filter parameters
            
        Returns:
            True if matching objects exist
        """
        return self.model.objects.filter(**kwargs).exists()
    
    def count(self, **kwargs: Any) -> int:
        """
        Count objects matching criteria.
        
        Args:
            **kwargs: Filter parameters
            
        Returns:
            Number of matching objects
        """
        return self.model.objects.filter(**kwargs).count()
    
    def get_paginated(
        self, 
        page: int = 1, 
        per_page: int = 10, 
        **filters: Any
    ) -> Dict[str, Any]:
        """
        Get paginated results.
        
        Args:
            page: Page number (1-based)
            per_page: Objects per page
            **filters: Filter parameters
            
        Returns:
            Dictionary with paginated results and metadata
        """
        queryset = self.filter(**filters)
        paginator = Paginator(queryset, per_page)
        page_obj = paginator.get_page(page)
        
        return {
            'objects': list(page_obj),
            'page': page,
            'per_page': per_page,
            'total_pages': paginator.num_pages,
            'total_objects': paginator.count,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous(),
        }


class BaseService(ABC):
    """
    Abstract base service class implementing common business logic patterns.
    
    This class provides the foundation for implementing business logic
    separate from models and views.
    """
    
    def __init__(self) -> None:
        """Initialize the service."""
        pass
    
    @transaction.atomic
    def create_with_transaction(self, **kwargs: Any) -> Any:
        """
        Create an object within a database transaction.
        
        This method should be overridden in concrete service classes.
        
        Args:
            **kwargs: Creation parameters
            
        Returns:
            Created object
        """
        return self.create(**kwargs)
    
    @transaction.atomic
    def update_with_transaction(self, obj: Any, **kwargs: Any) -> Any:
        """
        Update an object within a database transaction.
        
        This method should be overridden in concrete service classes.
        
        Args:
            obj: Object to update
            **kwargs: Update parameters
            
        Returns:
            Updated object
        """
        return self.update(obj, **kwargs)
    
    def validate_permissions(self, user: Any, action: str, obj: Any = None) -> bool:
        """
        Validate user permissions for an action.
        
        This method should be overridden in concrete service classes.
        
        Args:
            user: User requesting the action
            action: Action being performed
            obj: Object being acted upon (optional)
            
        Returns:
            True if permission granted
            
        Raises:
            PermissionServiceError: If permission denied
        """
        return True
    
    def log_action(self, action: str, user: Any = None, obj: Any = None, **kwargs: Any) -> None:
        """
        Log an action performed through the service.
        
        Args:
            action: Action performed
            user: User who performed the action
            obj: Object acted upon
            **kwargs: Additional context
        """
        context = {
            'action': action,
            'user': getattr(user, 'username', None) if user else None,
            'object': str(obj) if obj else None,
            **kwargs
        }
        logger.info(f"Service action: {action}", extra=context)


class CRUDService(BaseService, Generic[ModelType]):
    """
    Service class implementing standard CRUD operations.
    
    This class provides common Create, Read, Update, Delete operations
    with proper validation and error handling.
    """
    
    def __init__(self, repository: BaseRepository[ModelType]) -> None:
        """
        Initialize CRUD service with repository.
        
        Args:
            repository: Repository for data access
        """
        super().__init__()
        self.repository = repository
    
    def get_by_id(self, id: int, user: Any = None) -> Optional[ModelType]:
        """
        Get object by ID with permission checking.
        
        Args:
            id: Object ID
            user: User requesting the object
            
        Returns:
            Object instance or None
            
        Raises:
            PermissionServiceError: If user lacks permission
        """
        obj = self.repository.get_by_id(id)
        if obj and user:
            self.validate_permissions(user, 'read', obj)
        return obj
    
    def get_by_id_or_raise(self, id: int, user: Any = None) -> ModelType:
        """
        Get object by ID or raise exception.
        
        Args:
            id: Object ID
            user: User requesting the object
            
        Returns:
            Object instance
            
        Raises:
            NotFoundServiceError: If object not found
            PermissionServiceError: If user lacks permission
        """
        obj = self.repository.get_by_id_or_raise(id)
        if user:
            self.validate_permissions(user, 'read', obj)
        return obj
    
    def list_objects(
        self, 
        user: Any = None, 
        page: int = 1, 
        per_page: int = 10, 
        **filters: Any
    ) -> Dict[str, Any]:
        """
        List objects with pagination and filtering.
        
        Args:
            user: User requesting the list
            page: Page number
            per_page: Objects per page
            **filters: Additional filters
            
        Returns:
            Paginated results
        """
        if user:
            self.validate_permissions(user, 'list')
        
        return self.repository.get_paginated(page, per_page, **filters)
    
    def create(self, user: Any = None, **kwargs: Any) -> ModelType:
        """
        Create a new object.
        
        Args:
            user: User creating the object
            **kwargs: Object attributes
            
        Returns:
            Created object
            
        Raises:
            PermissionServiceError: If user lacks permission
            ValidationServiceError: If validation fails
        """
        if user:
            self.validate_permissions(user, 'create')
        
        obj = self.repository.create(**kwargs)
        self.log_action('create', user, obj)
        return obj
    
    def update(self, obj: ModelType, user: Any = None, **kwargs: Any) -> ModelType:
        """
        Update an existing object.
        
        Args:
            obj: Object to update
            user: User updating the object
            **kwargs: New attribute values
            
        Returns:
            Updated object
            
        Raises:
            PermissionServiceError: If user lacks permission
            ValidationServiceError: If validation fails
        """
        if user:
            self.validate_permissions(user, 'update', obj)
        
        updated_obj = self.repository.update(obj, **kwargs)
        self.log_action('update', user, updated_obj)
        return updated_obj
    
    def delete(self, obj: ModelType, user: Any = None) -> bool:
        """
        Delete an object.
        
        Args:
            obj: Object to delete
            user: User deleting the object
            
        Returns:
            True if successful
            
        Raises:
            PermissionServiceError: If user lacks permission
        """
        if user:
            self.validate_permissions(user, 'delete', obj)
        
        result = self.repository.delete(obj)
        self.log_action('delete', user, obj)
        return result


def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> None:
    """
    Validate that required fields are present in data.
    
    Args:
        data: Data dictionary to validate
        required_fields: List of required field names
        
    Raises:
        ValidationServiceError: If required fields are missing
    """
    missing_fields = [field for field in required_fields if field not in data or data[field] is None]
    if missing_fields:
        raise ValidationServiceError(f"Missing required fields: {', '.join(missing_fields)}")


def sanitize_data(data: Dict[str, Any], allowed_fields: List[str]) -> Dict[str, Any]:
    """
    Sanitize data to only include allowed fields.
    
    Args:
        data: Data dictionary to sanitize
        allowed_fields: List of allowed field names
        
    Returns:
        Sanitized data dictionary
    """
    return {key: value for key, value in data.items() if key in allowed_fields}

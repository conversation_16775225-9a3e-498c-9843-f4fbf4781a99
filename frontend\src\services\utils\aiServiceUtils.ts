/**
 * Frontend AI Service Utilities
 *
 * Standardized utilities for frontend AI services to eliminate code duplication
 * and provide consistent error handling, response parsing, and common functions.
 */

import axiosInstance from '../../config/axios';
import { AxiosResponse, AxiosError } from 'axios';

// Standardized error types
export class AIServiceError extends Error {
  public code: string;
  public statusCode?: number;
  public originalError?: any;

  constructor(
    message: string,
    code: string = 'AI_SERVICE_ERROR',
    statusCode?: number,
    originalError?: any
  ) {
    super(message);
    this.name = 'AIServiceError';
    this.code = code;
    this.statusCode = statusCode;
    this.originalError = originalError;
  }
}

export class AIServiceConnectionError extends AIServiceError {
  constructor(
    message: string = 'Failed to connect to AI service',
    originalError?: any
  ) {
    super(message, 'CONNECTION_ERROR', 503, originalError);
  }
}

export class AIServiceTimeoutError extends AIServiceError {
  constructor(
    message: string = 'AI service request timed out',
    originalError?: any
  ) {
    super(message, 'TIMEOUT_ERROR', 408, originalError);
  }
}

export class AIServiceValidationError extends AIServiceError {
  constructor(message: string = 'Invalid request data', originalError?: any) {
    super(message, 'VALIDATION_ERROR', 400, originalError);
  }
}

// Standardized response interfaces
export interface StandardizedAIResponse<T = any> {
  data: T;
  status: string;
  message?: string;
  metadata?: {
    response_time?: number;
    confidence_score?: number;
    session_id?: string;
    [key: string]: any;
  };
}

export interface AIServiceConfig {
  timeout?: number;
  retries?: number;
  fallbackEnabled?: boolean;
  cacheEnabled?: boolean;
  cacheTTL?: number;
}

// Default configuration
const DEFAULT_CONFIG: AIServiceConfig = {
  timeout: 30000, // 30 seconds
  retries: 2,
  fallbackEnabled: true,
  cacheEnabled: false,
  cacheTTL: 300000, // 5 minutes
};

// Response cache for performance optimization
const responseCache = new Map<
  string,
  { data: any; timestamp: number; ttl: number }
>();

/**
 * Standardized error handler for AI services
 */
export const handleAIServiceError = (
  error: any,
  context: string = 'AI Service'
): never => {
  console.error(`${context} Error:`, error);

  if (error.response) {
    const { status, data } = error.response;

    switch (status) {
      case 400:
        throw new AIServiceValidationError(
          data?.message || 'Invalid request data',
          error
        );
      case 401:
        throw new AIServiceError(
          'Authentication required',
          'AUTH_ERROR',
          401,
          error
        );
      case 403:
        throw new AIServiceError('Access denied', 'ACCESS_DENIED', 403, error);
      case 404:
        throw new AIServiceError(
          'AI service endpoint not found',
          'NOT_FOUND',
          404,
          error
        );
      case 408:
        throw new AIServiceTimeoutError('Request timeout', error);
      case 429:
        throw new AIServiceError(
          'Rate limit exceeded. Please try again later.',
          'RATE_LIMIT',
          429,
          error
        );
      case 500:
        throw new AIServiceError(
          'AI service is temporarily unavailable. Please try again.',
          'SERVER_ERROR',
          500,
          error
        );
      case 503:
        throw new AIServiceConnectionError(
          'AI service is currently unavailable',
          error
        );
      default:
        throw new AIServiceError(
          data?.message || 'An unexpected error occurred',
          'UNKNOWN_ERROR',
          status,
          error
        );
    }
  } else if (error.request) {
    throw new AIServiceConnectionError(
      'Unable to connect to AI service',
      error
    );
  } else {
    throw new AIServiceError(
      error.message || 'An unexpected error occurred',
      'UNKNOWN_ERROR',
      undefined,
      error
    );
  }
};

/**
 * Standardized response parser
 */
export const parseAIResponse = <T = any>(response: AxiosResponse): T => {
  const { data } = response;

  // Handle different response formats
  if (data.data !== undefined) {
    return data.data;
  }

  if (data.result !== undefined) {
    return data.result;
  }

  return data;
};

/**
 * Create standardized response
 */
export const createStandardizedResponse = <T = any>(
  data: T,
  status: string = 'success',
  message?: string,
  metadata?: any
): StandardizedAIResponse<T> => {
  return {
    data,
    status,
    message,
    metadata,
  };
};

/**
 * Generate cache key for requests
 */
const generateCacheKey = (endpoint: string, params?: any): string => {
  const paramString = params ? JSON.stringify(params) : '';
  return `${endpoint}:${paramString}`;
};

/**
 * Check if cached response is valid
 */
const isCacheValid = (cacheEntry: {
  timestamp: number;
  ttl: number;
}): boolean => {
  return Date.now() - cacheEntry.timestamp < cacheEntry.ttl;
};

/**
 * Standardized AI service request handler with retry logic
 */
export const makeAIRequest = async <T = any>(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
  endpoint: string,
  data?: any,
  config: AIServiceConfig = {},
  context: string = 'AI Service'
): Promise<T> => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const cacheKey = generateCacheKey(endpoint, data);

  // Check cache if enabled
  if (finalConfig.cacheEnabled && method === 'GET') {
    const cached = responseCache.get(cacheKey);
    if (cached && isCacheValid(cached)) {
      console.log(`Cache hit for ${endpoint}`);
      return cached.data;
    }
  }

  let lastError: any;
  const maxRetries = finalConfig.retries || 0;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const requestConfig = {
        timeout: finalConfig.timeout,
        ...(data && { data }),
      };

      let response: AxiosResponse;

      switch (method) {
        case 'GET':
          response = await axiosInstance.get(endpoint, requestConfig);
          break;
        case 'POST':
          response = await axiosInstance.post(endpoint, data, requestConfig);
          break;
        case 'PUT':
          response = await axiosInstance.put(endpoint, data, requestConfig);
          break;
        case 'DELETE':
          response = await axiosInstance.delete(endpoint, requestConfig);
          break;
        case 'PATCH':
          response = await axiosInstance.patch(endpoint, data, requestConfig);
          break;
        default:
          throw new AIServiceError('Unsupported HTTP method', 'INVALID_METHOD');
      }

      const result = parseAIResponse<T>(response);

      // Cache successful GET requests if enabled
      if (finalConfig.cacheEnabled && method === 'GET') {
        responseCache.set(cacheKey, {
          data: result,
          timestamp: Date.now(),
          ttl: finalConfig.cacheTTL || DEFAULT_CONFIG.cacheTTL!,
        });
      }

      return result;
    } catch (error) {
      lastError = error;

      // Don't retry on certain errors
      if (
        error instanceof AIServiceValidationError ||
        (error as any)?.response?.status === 401 ||
        (error as any)?.response?.status === 403
      ) {
        break;
      }

      // Wait before retry (exponential backoff)
      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt) * 1000; // 1s, 2s, 4s, etc.
        console.log(
          `Retrying ${context} request in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`
        );
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // All retries failed, handle the error
  handleAIServiceError(lastError, context);
};

/**
 * Create fallback response for when AI services fail
 */
export const createFallbackResponse = <T = any>(
  type: 'general' | 'suggestions' | 'content' | 'analysis' = 'general',
  customData?: Partial<T>
): T => {
  const fallbacks = {
    general: {
      message: 'AI service is temporarily unavailable. Please try again later.',
      status: 'fallback',
      data: null,
    },
    suggestions: {
      suggestions: [
        {
          id: 1,
          text: 'Try refreshing the page',
          icon: '🔄',
          category: 'general',
        },
        {
          id: 2,
          text: 'Check your internet connection',
          icon: '🌐',
          category: 'technical',
        },
        {
          id: 3,
          text: 'Contact support if the issue persists',
          icon: '💬',
          category: 'support',
        },
      ],
      status: 'fallback',
    },
    content: {
      content:
        'Content will be available shortly. Please check back in a few moments.',
      status: 'fallback',
    },
    analysis: {
      analysis: 'Analysis results are temporarily unavailable.',
      confidence: 0,
      status: 'fallback',
    },
  };

  return { ...fallbacks[type], ...customData } as T;
};

/**
 * Clear response cache
 */
export const clearAIServiceCache = (pattern?: string): void => {
  if (pattern) {
    for (const key of responseCache.keys()) {
      if (key.includes(pattern)) {
        responseCache.delete(key);
      }
    }
  } else {
    responseCache.clear();
  }
};

/**
 * Get cache statistics
 */
export const getCacheStats = () => {
  return {
    totalEntries: responseCache.size,
    hitRate: 0, // TODO: Implement hit rate tracking
    missRate: 0, // TODO: Implement miss rate tracking
    services: Array.from(responseCache.keys()),
  };
};

/**
 * Get error information from an error object
 */
export const getErrorInfo = (error: any) => {
  if (error instanceof AIServiceError) {
    return {
      message: error.message,
      category: error.code.includes('CONNECTION') ? 'network' :
                error.code.includes('TIMEOUT') ? 'network' :
                error.code.includes('VALIDATION') ? 'client' : 'server',
      severity: error.statusCode && error.statusCode >= 500 ? 'high' : 'medium',
      userMessage: error.message,
      retryable: error.code.includes('CONNECTION') || error.code.includes('TIMEOUT'),
    };
  }

  if (error?.response?.status === 404) {
    return {
      message: error.message || 'Not found',
      category: 'client',
      severity: 'medium',
      userMessage: 'The requested resource was not found',
      retryable: false,
    };
  }

  if (error?.code === 'NETWORK_ERROR') {
    return {
      message: error.message || 'Network error',
      category: 'network',
      severity: 'high',
      userMessage: 'Network connection failed',
      retryable: true,
    };
  }

  return {
    message: error?.message || 'Unknown error',
    category: 'unknown',
    severity: 'medium',
    userMessage: 'An unexpected error occurred',
    retryable: false,
  };
};

/**
 * Check if an error should use fallback response
 */
export const shouldUseFallback = (error: any): boolean => {
  if (error instanceof AIServiceError) {
    return error.code === 'SERVER_ERROR' || error.code === 'CONNECTION_ERROR';
  }
  return error?.response?.status >= 500 || error?.code === 'NETWORK_ERROR';
};

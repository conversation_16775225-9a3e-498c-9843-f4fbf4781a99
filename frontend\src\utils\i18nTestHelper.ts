/**
 * i18n Test Helper
 *
 * Utility functions to test and debug i18n translations in the browser.
 * This helps verify that the Arabic auth translations are working correctly.
 */

import i18n from '../i18n/i18n';

export interface TranslationTestResult {
  key: string;
  value: string;
  found: boolean;
  language: string;
}

export interface I18nHealthCheck {
  currentLanguage: string;
  availableLanguages: string[];
  authNamespaceLoaded: boolean;
  missingKeys: string[];
  workingKeys: string[];
  totalTestedKeys: number;
  successRate: number;
}

/**
 * Test specific translation keys
 */
export const testTranslationKeys = (
  keys: string[],
  language: string = 'ar'
): TranslationTestResult[] => {
  const results: TranslationTestResult[] = [];

  // Temporarily switch to the test language
  const originalLanguage = i18n.language;
  i18n.changeLanguage(language);

  keys.forEach(key => {
    const value = i18n.t(key);
    const found = value !== key && !value.includes('missingKey');

    results.push({
      key,
      value,
      found,
      language,
    });
  });

  // Restore original language
  i18n.changeLanguage(originalLanguage);

  return results;
};

/**
 * Perform comprehensive i18n health check
 */
export const performI18nHealthCheck = (): I18nHealthCheck => {
  const currentLanguage = i18n.language;
  const availableLanguages = Object.keys(i18n.store.data);

  // Check if auth namespace is loaded for Arabic
  const authNamespaceLoaded =
    i18n.hasResourceBundle('ar', 'auth') ||
    i18n.hasResourceBundle('ar', 'translation');

  // Test the problematic keys from the error messages
  const testKeys = [
    'auth.registration.welcome',
    'auth.registration.askPassword',
    'auth.signUp',
    'auth.createAccount',
    'auth.registration.firstNamePlaceholder',
    'auth.registration.lastNamePlaceholder',
    'auth.registration.emailPlaceholder',
    'auth.registration.passwordPlaceholder',
    'auth.login.welcome',
    'auth.fields.firstName',
    'auth.fields.lastName',
    'auth.fields.email',
    'auth.fields.password',
  ];

  const results = testTranslationKeys(testKeys, 'ar');
  const workingKeys = results.filter(r => r.found).map(r => r.key);
  const missingKeys = results.filter(r => !r.found).map(r => r.key);

  return {
    currentLanguage,
    availableLanguages,
    authNamespaceLoaded,
    missingKeys,
    workingKeys,
    totalTestedKeys: testKeys.length,
    successRate: (workingKeys.length / testKeys.length) * 100,
  };
};

/**
 * Log detailed i18n debug information to console
 */
export const logI18nDebugInfo = (): void => {
  console.group('🔍 i18n Debug Information');

  const healthCheck = performI18nHealthCheck();

  console.log('📊 Health Check Results:', healthCheck);

  console.log('🌐 Current Language:', i18n.language);
  console.log('🗂️ Available Languages:', Object.keys(i18n.store.data));

  // Check resource bundles
  console.log('📦 Arabic Resource Bundles:');
  const arBundles = Object.keys(i18n.store.data.ar || {});
  arBundles.forEach(bundle => {
    console.log(
      `  • ${bundle}:`,
      i18n.getResourceBundle('ar', bundle) ? '✅' : '❌'
    );
  });

  // Test specific auth translations
  console.log('🔑 Auth Translation Tests:');
  const authTests = [
    'auth.registration.welcome',
    'auth.signUp',
    'auth.createAccount',
    'auth.registration.firstNamePlaceholder',
  ];

  authTests.forEach(key => {
    const value = i18n.t(key, { lng: 'ar' });
    const working = value !== key && !value.includes('missingKey');
    console.log(`  ${working ? '✅' : '❌'} ${key}: "${value}"`);
  });

  console.groupEnd();
};

/**
 * Fix missing translations by reloading resources
 */
export const reloadI18nResources = async (): Promise<void> => {
  try {
    console.log('🔄 Reloading i18n resources...');

    // Reload the current language
    await i18n.reloadResources();

    console.log('✅ i18n resources reloaded successfully');

    // Perform health check after reload
    const healthCheck = performI18nHealthCheck();
    console.log('📊 Post-reload health check:', healthCheck);
  } catch (error) {
    console.error('❌ Failed to reload i18n resources:', error);
  }
};

/**
 * Switch language and test translations
 */
export const testLanguageSwitch = async (
  targetLanguage: string
): Promise<I18nHealthCheck> => {
  console.log(`🌐 Switching to ${targetLanguage}...`);

  await i18n.changeLanguage(targetLanguage);

  const healthCheck = performI18nHealthCheck();
  console.log(`📊 Health check for ${targetLanguage}:`, healthCheck);

  return healthCheck;
};

/**
 * Export for browser console debugging
 */
if (typeof window !== 'undefined') {
  (window as any).i18nTestHelper = {
    testTranslationKeys,
    performI18nHealthCheck,
    logI18nDebugInfo,
    reloadI18nResources,
    testLanguageSwitch,
  };

  console.log('🛠️ i18n Test Helper available at window.i18nTestHelper');
}

export default {
  testTranslationKeys,
  performI18nHealthCheck,
  logI18nDebugInfo,
  reloadI18nResources,
  testLanguageSwitch,
};

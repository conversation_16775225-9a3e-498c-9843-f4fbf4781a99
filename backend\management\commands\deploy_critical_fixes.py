"""DjangoManagementCommand:DeployCriticalFixesThiscommandhelpssafelydeploythecriticalsecurityandperformancefixesidentifiedinthecodereview."""import osimportsysimport timefrom django.confimportsettingsfrom django.contrib.authimportget_user_modelfrom django.core.cacheimportcachefrom django.core.management.baseimportBaseCommandCommandErrorfrom django.dbimporttransactionUser=get_user_model()classCommand(BaseCommand):help="Deploycriticalsecurityandperformancefixessafely"defadd_arguments(selfparser):parser.add_argument("--step"type=strchoices=["all""security""performance""api-keys""rate-limiting""test"]default="all"help="Whichfixestodeploy")parser.add_argument("--dry-run"action="store_true"help="Showwhatwouldbedonewithoutmakingchanges")parser.add_argument("--force"action="store_true"help="Forcedeploymentevenifchecksfail")parser.add_argument("--backup"action="store_true"help="Createbackupbeforedeployment")defhandle(self*args**options):"""Maincommandhandler"""self.stdout.write(self.style.SUCCESS("🚀StartingCriticalFixesDeployment"))#Pre-deploymentchecksifnotself._pre_deployment_checks(options):ifnotoptions["force"]:raiseCommandError("Pre-deploymentchecksfailed.Use--forcetooverride.")self.stdout.write(self.style.WARNING("⚠️Pre-deploymentchecksfailedbutcontinuingdueto--force"))#Createbackupifrequestedifoptions["backup"]:self._create_backup()#Deploybasedonstepstep=options["step"]dry_run=options["dry_run"]ifstepin["all""security"]:self._deploy_security_fixes(dry_run)ifstepin["all""api-keys"]:self._deploy_api_key_security(dry_run)ifstepin["all""rate-limiting"]:self._deploy_rate_limiting(dry_run)ifstepin["all""performance"]:self._deploy_performance_fixes(dry_run)ifstep=="test":self._run_deployment_tests()#Post-deploymentverificationifnotdry_run:self._post_deployment_verification()self.stdout.write(self.style.SUCCESS("✅Criticalfixesdeploymentcompletedsuccessfully!"))def_pre_deployment_checks(selfoptions):"""Runpre-deploymentsafetychecks"""self.stdout.write("🔍Runningpre-deploymentchecks...")checks_passed=True#CheckDjangoversioncompatibilityimport djangoifdjango.VERSION<(40):self.stdout.write(self.style.ERROR("❌Djangoversiontooold.RequiresDjango4.0+"))checks_passed=False#Checkrequiredpackagesrequired_packages=["cryptography""django-ratelimit"]forpackageinrequired_packages:try:__import__(package.replace("-""_"))self.stdout.write(f"✅{package}isinstalled")exceptImportError:self.stdout.write(self.style.ERROR(f"❌Requiredpackage{package}notinstalled"))checks_passed=False#Checkdatabaseconnectivitytry:User.objects.count()self.stdout.write("✅Databaseconnectionworking")exceptExceptionase:self.stdout.write(self.style.ERROR(f"❌Databaseconnectionfailed:{e}"))checks_passed=False#Checkcacheconnectivitytry:cache.set("deployment_test""test_value"10)ifcache.get("deployment_test")=="test_value":self.stdout.write("✅Cachesystemworking")cache.delete("deployment_test")else:raiseException("Cacheread/writefailed")exceptExceptionase:self.stdout.write(self.style.ERROR(f"❌Cachesystemfailed:{e}"))checks_passed=False#Checkfilepermissionstry:test_file=os.path.join(settings.BASE_DIR".deployment_test")withopen(test_file"w")asf:f.write("test")os.remove(test_file)self.stdout.write("✅FilesystempermissionsOK")exceptExceptionase:self.stdout.write(self.style.ERROR(f"❌Filesystempermissionsfailed:{e}"))checks_passed=Falsereturnchecks_passeddef_create_backup(self):"""Createbackupofcriticalfiles"""self.stdout.write("💾Creatingbackup...")importshutilfromdatetimeimport datetimebackup_dir=os.path.join(settings.BASE_DIR"backups"f'critical_fixes_{datetime.now().strftime("%Y%m%d_%H%M%S")}')os.makedirs(backup_direxist_ok=True)#Filestobackupbackup_files=["utils/api_key_service.py""auth_api/views.py""users/views.py""requirements.txt"]forfile_pathinbackup_files:full_path=os.path.join(settings.BASE_DIRfile_path)ifos.path.exists(full_path):backup_path=os.path.join(backup_dirfile_path)os.makedirs(os.path.dirname(backup_path)exist_ok=True)shutil.copy2(full_pathbackup_path)self.stdout.write(f"📁Backedup{file_path}")self.stdout.write(f"✅Backupcreatedin{backup_dir}")def_deploy_security_fixes(selfdry_run):"""Deploysecurity-relatedfixes"""self.stdout.write("🔒Deployingsecurityfixes...")ifdry_run:self.stdout.write("[DRYRUN]Wouldupdateauthenticationsecurity")self.stdout.write("[DRYRUN]Wouldaddinputvalidation")self.stdout.write("[DRYRUN]WouldconfigureCSRFprotection")return#Addsecuritymiddlewareifnotpresenttry:from django.confimportsettingsmiddleware=list(settings.MIDDLEWARE)security_middleware=["django.middleware.security.SecurityMiddleware""django.middleware.csrf.CsrfViewMiddleware"]formwinsecurity_middleware:ifmwnotinmiddleware:self.stdout.write(f"⚠️Add{mw}toMIDDLEWAREinsettings")exceptExceptionase:self.stdout.write(self.style.WARNING(f"⚠️Couldnotcheckmiddleware:{e}"))self.stdout.write("✅Securityfixesdeployed")def_deploy_api_key_security(selfdry_run):"""DeployAPIkeysecurityfixes"""self.stdout.write("🔑DeployingAPIkeysecurity...")ifdry_run:self.stdout.write("[DRYRUN]WouldverifyunifiedAIconfigurationsystem")self.stdout.write("[DRYRUN]WouldsetupunifiedAPIkeymanagement")returntry:from utils.ai_config_managerimportAIConfigManagerfrom utils.api_key_serviceimportApiKeyService#Checkiftheunifiedconfigsystemisworkingconfig=AIConfigManager.get_config()ifconfig:self.stdout.write("✅UnifiedAIconfigurationsystemisworking")else:self.stdout.write("ℹ️NoAIconfigurationfound-canbesetviafrontend")#CheckAPIkeyserviceapi_key=ApiKeyService.get_api_key()ifapi_key:self.stdout.write("✅APIkeyserviceworking-keyfound")else:self.stdout.write("ℹ️NoAPIkeyconfigured-canbesetviafrontend")self.stdout.write("✅AIconfigurationmigrationcompleted-allservicesnowuseunifiedsystem")exceptExceptionase:self.stdout.write(self.style.ERROR(f"❌APIkeysecuritydeploymentfailed:{e}"))raiseself.stdout.write("✅APIkeysecuritydeployed")def_deploy_rate_limiting(selfdry_run):"""Deployratelimitingfixes"""self.stdout.write("🚦Deployingratelimiting...")ifdry_run:self.stdout.write("[DRYRUN]Wouldconfigureauthenticationratelimiting")self.stdout.write("[DRYRUN]WouldsetupIP-basedblocking")returntry:from auth_api.rate_limitingimportAuthenticationRateLimit#Clearanyexistingratelimitingdatacache.clear()self.stdout.write("🧹Clearedexistingratelimitingdata")#Testratelimitingfunctionalitytest_identifier="deployment_test"AuthenticationRateLimit.record_failed_attempt(test_identifier)attempts=AuthenticationRateLimit.get_failed_attempts(test_identifier)ifattempts==1:self.stdout.write("✅Ratelimitingfunctionalityverified")AuthenticationRateLimit.clear_failed_attempts(test_identifier)else:raiseException("Ratelimitingtestfailed")exceptExceptionase:self.stdout.write(self.style.ERROR(f"❌Ratelimitingdeploymentfailed:{e}"))raiseself.stdout.write("✅Ratelimitingdeployed")def_deploy_performance_fixes(selfdry_run):"""Deployperformanceoptimizationfixes"""self.stdout.write("⚡Deployingperformancefixes...")ifdry_run:self.stdout.write("[DRYRUN]Wouldoptimizedatabasequeries")self.stdout.write("[DRYRUN]Wouldimplementadvancedcaching")returntry:#Testoptimizedqueriesifavailablefrom users.optimized_viewsimportOptimizedUserViewSet#Createatestinstancetoverifyitworksviewset=OptimizedUserViewSet()queryset=viewset.get_queryset()#Forceevaluationtotestthequerylist(queryset[:1])self.stdout.write("✅Optimizedqueriesverified")exceptImportError:self.stdout.write("⚠️Optimizedviewsnotavailableyet")exceptExceptionase:self.stdout.write(self.style.WARNING(f"⚠️Performanceoptimizationtestfailed:{e}"))self.stdout.write("✅Performancefixesdeployed")def_run_deployment_tests(self):"""Rundeploymentverificationtests"""self.stdout.write("🧪Runningdeploymenttests...")try:from django.confimportsettingsfrom django.test.utilsimportget_runnerTestRunner=get_runner(settings)test_runner=TestRunner(verbosity=1interactive=False)#Runourcriticalfixestestsfailures=test_runner.run_tests(["tests.test_critical_fixes"])iffailures:self.stdout.write(self.style.ERROR(f"❌{failures}test(s)failed"))returnFalseelse:self.stdout.write("✅Alldeploymenttestspassed")returnTrueexceptExceptionase:self.stdout.write(self.style.WARNING(f"⚠️Couldnotruntests:{e}"))returnFalsedef_post_deployment_verification(self):"""Verifydeploymentwassuccessful"""self.stdout.write("🔍Runningpost-deploymentverification...")#Checkthatservicesareworkingtry:from auth_api.rate_limitingimportAuthenticationRateLimitfrom utils.ai_config_managerimportAIConfigManagerfrom utils.api_key_serviceimportApiKeyService#TestunifiedAIconfigserviceconfig=AIConfigManager.get_config()ifconfig:self.stdout.write("✅UnifiedAIconfigserviceworking")else:self.stdout.write("⚠️AIconfignotset-configureviafrontend")#TestAPIkeyserviceapi_key=ApiKeyService.get_api_key()ifapi_key:self.stdout.write("✅APIkeyserviceworking")else:self.stdout.write("⚠️APIkeynotset-configureviafrontend")#Testratelimitingtest_id="verification_test"AuthenticationRateLimit.record_failed_attempt(test_id)ifAuthenticationRateLimit.get_failed_attempts(test_id)==1:self.stdout.write("✅Ratelimitingworking")AuthenticationRateLimit.clear_failed_attempts(test_id)exceptExceptionase:self.stdout.write(self.style.ERROR(f"❌Post-deploymentverificationfailed:{e}"))raiseself.stdout.write("✅Post-deploymentverificationcompleted")
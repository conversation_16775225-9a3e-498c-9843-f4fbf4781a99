import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  TextField,
  Button,
  Chip,
  Grid,
  Paper,
  Alert,
  CircularProgress,
  Divider,
} from '@mui/material';
import { Send as SendIcon, SmartToy as BotIcon } from '@mui/icons-material';
import chatbotService from '../services/chatbotService';

// 🤖 Multi-Agent Chat Demo Component
// Shows which AI agent is handling each message

interface AgentInfo {
  name: string;
  icon: string;
  color: string;
  description: string;
  keywords: string[];
}

interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  agentUsed?: string;
  timestamp: Date;
  metadata?: any;
}

const agents: AgentInfo[] = [
  {
    name: 'Math Tutor',
    icon: '🔢',
    color: '#2196F3',
    description: 'Handles mathematical questions and problem solving',
    keywords: ['math', 'equation', 'calculate', 'algebra', 'geometry'],
  },
  {
    name: 'Science Tutor',
    icon: '🔬',
    color: '#4CAF50',
    description: 'Explains science concepts and experiments',
    keywords: ['science', 'biology', 'chemistry', 'physics', 'experiment'],
  },
  {
    name: 'Language Tutor',
    icon: '📝',
    color: '#FF9800',
    description: 'Helps with writing and language arts',
    keywords: ['write', 'essay', 'grammar', 'literature', 'writing'],
  },
  {
    name: 'Career Advisor',
    icon: '🎯',
    color: '#9C27B0',
    description: 'Provides academic and career guidance',
    keywords: ['career', 'advice', 'guidance', 'path', 'future'],
  },
  {
    name: 'Assessor',
    icon: '📊',
    color: '#F44336',
    description: 'Creates quizzes and assessments',
    keywords: ['quiz', 'test', 'assessment', 'evaluate'],
  },
  {
    name: 'General AI',
    icon: '🤖',
    color: '#607D8B',
    description: 'Handles general conversation and questions',
    keywords: ['hello', 'how', 'what', 'general'],
  },
];

const MultiAgentChatDemo: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeAgent, setActiveAgent] = useState<string | null>(null);

  // Sample messages to demonstrate agent routing
  const sampleMessages = [
    'Help me solve quadratic equations',
    'Explain photosynthesis process',
    'How do I write a good essay?',
    'What career should I choose?',
    'Create a math quiz for me',
    'Hello, how are you today?',
  ];

  const predictAgent = (message: string): AgentInfo => {
    const lowerMessage = message.toLowerCase();

    // Check for tutoring keywords first
    const hasTutoringKeywords = [
      'help',
      'explain',
      'teach',
      'learn',
      'understand',
      'how to',
      'what is',
    ].some(keyword => lowerMessage.includes(keyword));

    if (
      hasTutoringKeywords ||
      lowerMessage.includes('math') ||
      lowerMessage.includes('equation')
    ) {
      if (agents[0].keywords.some(keyword => lowerMessage.includes(keyword))) {
        return agents[0]; // Math Tutor
      }
      if (agents[1].keywords.some(keyword => lowerMessage.includes(keyword))) {
        return agents[1]; // Science Tutor
      }
      if (agents[2].keywords.some(keyword => lowerMessage.includes(keyword))) {
        return agents[2]; // Language Tutor
      }
      return agents[0]; // Default to Math Tutor for tutoring
    }

    if (agents[3].keywords.some(keyword => lowerMessage.includes(keyword))) {
      return agents[3]; // Career Advisor
    }

    if (agents[4].keywords.some(keyword => lowerMessage.includes(keyword))) {
      return agents[4]; // Assessor
    }

    return agents[5]; // General AI
  };

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputMessage,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Predict which agent will handle this
    const predictedAgent = predictAgent(inputMessage);
    setActiveAgent(predictedAgent.name);

    try {
      const response = await chatbotService.sendMessage(inputMessage);

      const botMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content:
          response.content ||
          response.message?.content ||
          'I received your message!',
        isUser: false,
        agentUsed:
          response.metadata?.ai_agent_used ||
          predictedAgent.name.toLowerCase().replace(' ', '_'),
        timestamp: new Date(),
        metadata: response.metadata,
      };

      setMessages(prev => [...prev, botMessage]);
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: `Demo Mode: This would be handled by ${predictedAgent.name} (${predictedAgent.icon})`,
        isUser: false,
        agentUsed: predictedAgent.name.toLowerCase().replace(' ', '_'),
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    }

    setInputMessage('');
    setIsLoading(false);
    setActiveAgent(null);
  };

  const getAgentInfo = (agentUsed: string): AgentInfo => {
    if (agentUsed?.includes('math') || agentUsed?.includes('tutoring_agent'))
      return agents[0];
    if (agentUsed?.includes('science')) return agents[1];
    if (agentUsed?.includes('language')) return agents[2];
    if (agentUsed?.includes('advisor')) return agents[3];
    if (agentUsed?.includes('assessor')) return agents[4];
    return agents[5];
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Typography variant='h4' gutterBottom align='center'>
        🤖 Multi-Agent Chat System Demo
      </Typography>

      <Alert severity='info' sx={{ mb: 3 }}>
        This demo shows how different AI agents handle different types of
        messages. Try the sample messages below to see agent routing in action!
      </Alert>

      <Grid container spacing={3}>
        {/* Agent Status Panel */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant='h6' gutterBottom>
                🎯 Available Agents
              </Typography>
              {agents.map((agent, index) => (
                <Box key={index} sx={{ mb: 2 }}>
                  <Chip
                    icon={<span>{agent.icon}</span>}
                    label={agent.name}
                    variant={activeAgent === agent.name ? 'filled' : 'outlined'}
                    color={activeAgent === agent.name ? 'primary' : 'default'}
                    sx={{ mb: 1, width: '100%', justifyContent: 'flex-start' }}
                  />
                  <Typography
                    variant='caption'
                    display='block'
                    color='text.secondary'
                  >
                    {agent.description}
                  </Typography>
                  <Typography
                    variant='caption'
                    display='block'
                    sx={{ mt: 0.5 }}
                  >
                    Keywords: {agent.keywords.join(', ')}
                  </Typography>
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Chat Interface */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant='h6' gutterBottom>
                💬 Multi-Agent Chat
              </Typography>

              {/* Sample Messages */}
              <Box sx={{ mb: 2 }}>
                <Typography variant='subtitle2' gutterBottom>
                  Try these sample messages:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {sampleMessages.map((sample, index) => (
                    <Chip
                      key={index}
                      label={sample}
                      onClick={() => setInputMessage(sample)}
                      variant='outlined'
                      size='small'
                      clickable
                    />
                  ))}
                </Box>
              </Box>

              <Divider sx={{ mb: 2 }} />

              {/* Messages */}
              <Box
                sx={{
                  height: 400,
                  overflowY: 'auto',
                  mb: 2,
                  p: 1,
                  bgcolor: 'grey.50',
                }}
              >
                {messages.map(message => (
                  <Box
                    key={message.id}
                    sx={{
                      display: 'flex',
                      justifyContent: message.isUser
                        ? 'flex-end'
                        : 'flex-start',
                      mb: 2,
                    }}
                  >
                    <Paper
                      sx={{
                        p: 2,
                        maxWidth: '70%',
                        bgcolor: message.isUser ? 'primary.main' : 'white',
                        color: message.isUser ? 'white' : 'text.primary',
                      }}
                    >
                      {!message.isUser && message.agentUsed && (
                        <Box sx={{ mb: 1 }}>
                          <Chip
                            icon={
                              <span>
                                {getAgentInfo(message.agentUsed).icon}
                              </span>
                            }
                            label={getAgentInfo(message.agentUsed).name}
                            size='small'
                            color='primary'
                            variant='outlined'
                          />
                        </Box>
                      )}
                      <Typography variant='body2'>{message.content}</Typography>
                      <Typography
                        variant='caption'
                        display='block'
                        sx={{ mt: 1, opacity: 0.7 }}
                      >
                        {message.timestamp.toLocaleTimeString()}
                      </Typography>
                    </Paper>
                  </Box>
                ))}

                {isLoading && (
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'flex-start',
                      mb: 2,
                    }}
                  >
                    <Paper
                      sx={{
                        p: 2,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                      }}
                    >
                      <CircularProgress size={16} />
                      <Typography variant='body2'>
                        {activeAgent} is thinking...
                      </Typography>
                    </Paper>
                  </Box>
                )}
              </Box>

              {/* Input */}
              <Box sx={{ display: 'flex', gap: 1 }}>
                <TextField
                  fullWidth
                  variant='outlined'
                  placeholder='Type a message to see which agent responds...'
                  value={inputMessage}
                  onChange={e => setInputMessage(e.target.value)}
                  onKeyPress={e => e.key === 'Enter' && sendMessage()}
                  disabled={isLoading}
                />
                <Button
                  variant='contained'
                  onClick={sendMessage}
                  disabled={isLoading || !inputMessage.trim()}
                  startIcon={<SendIcon />}
                >
                  Send
                </Button>
              </Box>

              {/* Prediction */}
              {inputMessage && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant='caption' color='text.secondary'>
                    Predicted agent: {predictAgent(inputMessage).icon}{' '}
                    {predictAgent(inputMessage).name}
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MultiAgentChatDemo;

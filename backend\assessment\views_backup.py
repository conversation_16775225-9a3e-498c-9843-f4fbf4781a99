from django.contrib.authimportget_user_modelfrom django.core.cacheimportcachefrom django.db.modelsimportAvgCountQfrom django.shortcutsimportget_object_or_404renderfrom django.utilsimport timezoneUser=get_user_model()import jsonimport randomfrom django.dbimport(#Alsoaddingmissingtransactionimportbasedoncodeusagetransaction)fromrest_frameworkimportgenericspermissionsserializersstatusviewsviewsetsfromrest_framework.decoratorsimportactionfromrest_framework.permissionsimportIsAuthenticated#Addmissingimportfromrest_framework.responseimportResponsefrom.modelsimport(AssessmentAssessmentQuestionAssessmentResponseAssessmentSettingsStudentLevel)#ImportSkillfromcoreappwhereit'sactuallydefinedfrom core.modelsimportSkillfrom.serializersimport(AdminQuestionListSerializerAIQuestionSuggestionSerializerAssessmentDetailSerializerAssessmentQuestionSerializerAssessmentResponseSerializerAssessmentSerializerAssessmentSubmissionSerializerInitialAssessmentSerializerSkillSerializerStudentLevelSerializer)#ImportCoursemodeldirectlytry:from courses.modelsimportCourseexceptImportError:#Fallbacktoimportingfrommodelsmodulefrom django.appsimportappstry:Course=apps.get_model("courses""Course")exceptLookupError:#DefineaplaceholderCoursemodelfordevelopmentfrom django.dbimportmodelsclassCourse(models.Model):title=models.CharField(max_length=200)course_code=models.CharField(max_length=10)import loggingfrom courses.views.course_viewsimportCourseSerializerfrom users.permissionsimport(CanTakeAssessmentsIsAdminUserIsProfessorUserIsStudentUser)from utils.consolidated_ai_serviceimport(ConsolidatedAIErrorasAIServiceErrorContentGenerationErrorasResponseGenerationError)from core.exceptionsimportRateLimitErrorfrom utils.ai.servicesimportget_ai_servicefrom.prevent_mock_dataimport(prevent_mock_question_creationprevent_mock_response_creationsanitize_assessment_data)from.servicesimportAssessmentServiceassessment_ailogger=logging.getLogger(__name__)#Re-addnecessaryimportsfromrest_framework.viewsimportAPIViewclassQuestionViewSet(viewsets.ModelViewSet):queryset=AssessmentQuestion.objects.all()serializer_class=AssessmentQuestionSerializerpermission_classes=[permissions.IsAuthenticated]defget_permissions(self):ifself.actionin["create""update""partial_update""destroy"]:permission_classes=[IsAdminUser|IsProfessorUser]else:permission_classes=[permissions.IsAuthenticated]return[permission()forpermissioninpermission_classes]defperform_create(selfserializer):#Checkifthisisamockquestionifprevent_mock_question_creation(serializer.validated_data):raiseserializers.ValidationError({"error":"Mockorsamplequestionsarenotallowed"})serializer.save(created_by=self.request.user)@action(detail=Falsemethods=["post"])defgenerate_questions(selfrequest):"""GenerateAIquestionsbasedonparameters"""try:serializer=AIQuestionSuggestionSerializer(data=request.data)ifnotserializer.is_valid():returnResponse({"error":serializer.errors}status=status.HTTP_400_BAD_REQUEST)questions=assessment_ai.generate_questions(**serializer.validated_data)created_questions=[]forquestion_datainquestions:#Skipmockquestionsifprevent_mock_question_creation(question_data):logger.warning(f"Skippingmockquestion:{question_data.get('question_text''')[:50]}...")continuequestion_serializer=self.get_serializer(data=question_data)ifquestion_serializer.is_valid():question=question_serializer.save(created_by=request.userai_suggested=True)created_questions.append(question)returnResponse(self.get_serializer(created_questionsmany=True).datastatus=status.HTTP_201_CREATED)except(AIServiceErrorRateLimitErrorResponseGenerationError)ase:returnResponse({"error":str(e)}status=status.HTTP_503_SERVICE_UNAVAILABLE)exceptExceptionase:logger.error(f"Errorgeneratingquestions:{str(e)}")returnResponse({"error":"Failedtogeneratequestions"}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classInitialAssessmentView(views.APIView):"""Handleinitialassessment"""permission_classes=[IsAuthenticatedIsStudentUser]@transaction.atomicdefpost(selfrequest):"""Startinitialassessment"""try:service=AssessmentService()result=service.start_assessment(user=request.userassessment_type="initial"is_placement=True)returnResponse(result)exceptExceptionase:logger.error(f"Errorcreatinginitialassessment:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classStudentAssessmentView(viewsets.ModelViewSet):"""ViewSetforstudentassessments"""serializer_class=AssessmentDetailSerializerpermission_classes=[permissions.IsAuthenticatedCanTakeAssessments]defget_queryset(self):#Ifuserisadminorprofessorallowaccesstoallassessmentsifself.request.user.is_stafforhasattr(self.request.user"professor"):returnAssessment.objects.all().prefetch_related("responses""responses__question")#Otherwiseonlyshowtheuser'sownassessmentsreturnAssessment.objects.filter(student=self.request.user).prefetch_related("responses""responses__question")defsubmit(selfrequest):"""Submitanassessmentorassessmentresponse"""try:assessment_id=request.data.get("assessment_id")is_final_submission=request.data.get("is_final_submission"False)ifnotassessment_id:returnResponse({"status":"error""message":"AssessmentIDisrequired"}status=status.HTTP_400_BAD_REQUEST)#Gettheassessmenttry:assessment=Assessment.objects.get(id=assessment_id)exceptAssessment.DoesNotExist:returnResponse({"status":"error""message":f"AssessmentwithID{assessment_id}notfound"}status=status.HTTP_404_NOT_FOUND)#Checkiftheuserhaspermissiontosubmitthisassessmentifassessment.student!=request.userandnot(request.user.is_stafforhasattr(request.user"professor")):returnResponse({"status":"error""message":"Youdonothavepermissiontosubmitthisassessment"}status=status.HTTP_403_FORBIDDEN)#Ifthisisafinalsubmissionmarktheassessmentascompletedifis_final_submission:assessment.completed=Trueassessment.completion_date=timezone.now()assessment.save()#Returntheassessmentdetailsserializer=self.get_serializer(assessment)returnResponse({"status":"success""message":"Assessmentcompletedsuccessfully""data":serializer.data})#Otherwisejustsavetheresponse#Extracttheresponsedatafromtherequestquestion_id=request.data.get("question_id")answer=request.data.get("answer")ifnotquestion_idornotanswer:returnResponse({"status":"error""message":"QuestionIDandanswerarerequiredforresponses"}status=status.HTTP_400_BAD_REQUEST)#Getthequestiontry:question=AssessmentQuestion.objects.get(id=question_id)exceptAssessmentQuestion.DoesNotExist:returnResponse({"status":"error""message":f"QuestionwithID{question_id}notfound"}status=status.HTTP_404_NOT_FOUND)#Createorupdatetheresponseresponsecreated=AssessmentResponse.objects.update_or_create(assessment=assessmentquestion=questiondefaults={"answer":answer"is_correct":question.check_answer(answer)})#ReturntheresponsedetailsreturnResponse({"status":"success""message":"Responsesavedsuccessfully""data":{"response_id":response.id"is_correct":response.is_correct}})exceptExceptionase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)defresults(selfrequest):"""Getassessmentresults"""try:assessment_id=request.query_params.get("assessment_id")ifnotassessment_id:returnResponse({"status":"error""message":"AssessmentIDisrequired"}status=status.HTTP_400_BAD_REQUEST)#Gettheassessmenttry:assessment=Assessment.objects.get(id=assessment_id)exceptAssessment.DoesNotExist:returnResponse({"status":"error""message":f"AssessmentwithID{assessment_id}notfound"}status=status.HTTP_404_NOT_FOUND)#Checkiftheuserhaspermissiontoviewthisassessmentifassessment.student!=request.userandnot(request.user.is_stafforhasattr(request.user"professor")):returnResponse({"status":"error""message":"Youdonothavepermissiontoviewthisassessment"}status=status.HTTP_403_FORBIDDEN)#Checkiftheassessmentiscompletedifnotassessment.completed:returnResponse({"status":"error""message":"Assessmentnotcompleted"}status=status.HTTP_400_BAD_REQUEST)#Getanalysisandrecommendationsassessment_service=AssessmentService(request.user)analysis=assessment_service.analyze_strengths_weaknesses(assessment)recommendations=assessment_service.get_course_recommendations(assessment)learning_path=assessment_service.get_learning_path(assessment)#Returntheassessmentdetailswithanalysisandrecommendationsserializer=self.get_serializer(assessment)returnResponse({"status":"success""message":"Assessmentresultsretrievedsuccessfully""assessment":serializer.data"analysis":analysis"recommendations":recommendations"learning_path":learning_path})exceptExceptionase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)defcreate(selfrequest):"""Startanewassessmentforthestudent"""try:#Getassessmenttypefromrequestassessment_type=request.data.get("assessment_type""PLACEMENT")#Getlearningpathfromrequestlearning_path=request.data.get("learning_path""general")#Checkifthestudenthasalreadycompletedanassessmentofthistype#Alsocheckforin-progressassessmentstopreventmultipleattempts#FirstcheckbyuserIDexisting_completed=Assessment.objects.filter(student=request.userassessment_type=assessment_typestatus="COMPLETED").first()#Ifnotfoundcheckbyemail/usernametopreventduplicateattemptsifnotexisting_completedandrequest.user.email:#Checkifanyotheruserwiththesameemailhascompletedthisassessmentsame_email_users=User.objects.filter(email=request.user.email).exclude(id=request.user.id)ifsame_email_users.exists():user_ids=same_email_users.values_list("id"flat=True)existing_completed=Assessment.objects.filter(student_id__in=user_idsassessment_type=assessment_typestatus="COMPLETED").first()ifexisting_completed:logger.warning(f"Foundcompletedassessmentforuserwithsameemail:{request.user.email}")#ReturnerrorresponsetopreventduplicateassessmentreturnResponse({"status":"error""message":f"Anassessmenthasalreadybeencompletedbyauserwiththesameemailaddress.Multipleattemptsarenotallowed.""assessment_id":existing_completed.id"already_taken":True"assessment_status":"COMPLETED""allow_retakes":False"duplicate_email":True}status=status.HTTP_400_BAD_REQUEST)#Checkforin-progressassessmentsexisting_in_progress=Assessment.objects.filter(student=request.userassessment_type=assessment_typestatus="IN_PROGRESS").first()#Ifnotfoundcheckbyemail/usernameifnotexisting_in_progressandrequest.user.email:#Checkifanyotheruserwiththesameemailhasanin-progressassessmentsame_email_users=User.objects.filter(email=request.user.email).exclude(id=request.user.id)ifsame_email_users.exists():user_ids=same_email_users.values_list("id"flat=True)existing_in_progress=Assessment.objects.filter(student_id__in=user_idsassessment_type=assessment_typestatus="IN_PROGRESS").first()ifexisting_in_progress:logger.warning(f"Foundin-progressassessmentforuserwithsameemail:{request.user.email}")#ReturnerrorresponsetopreventduplicateassessmentreturnResponse({"status":"error""message":f"Anassessmentisalreadyinprogressbyauserwiththesameemailaddress.Multipleattemptsarenotallowed.""assessment_id":existing_in_progress.id"already_taken":True"assessment_status":"IN_PROGRESS""duplicate_email":True}status=status.HTTP_400_BAD_REQUEST)#Checkifretakesareallowedforthisassessmenttypetry:settings=AssessmentSettings.objects.get(assessment_type=assessment_type)allow_retakes=settings.allow_retakescooldown_days=settings.retake_cooldown_daysexceptAssessmentSettings.DoesNotExist:#Defaulttonotallowingretakesifsettingsdon'texistallow_retakes=Falsecooldown_days=1logger.warning(f"Nosettingsfoundforassessmenttype{assessment_type}defaultingtonotallowretakes")ifexisting_completed:#Ifretakesarenotallowedreturnerrorifnotallow_retakes:returnResponse({"status":"error""message":f"Youhavealreadycompleteda{assessment_type.lower()}assessmentandretakesarenotallowed.""assessment_id":existing_completed.id"already_taken":True"assessment_status":"COMPLETED""allow_retakes":False}status=status.HTTP_400_BAD_REQUEST)#Checkcooldownperiodfromdatetimeimport timedeltaifexisting_completed.end_time:cooldown_end=existing_completed.end_time+timedelta(days=cooldown_days)iftimezone.now()<cooldown_end:#Calculatetimeremaininginthecooldownperiodtime_remaining=cooldown_end-timezone.now()days_remaining=time_remaining.dayshours_remaining=time_remaining.seconds//3600returnResponse({"status":"error""message":f"Youmustwait{days_remaining}daysand{hours_remaining}hoursbeforetakingthisassessmentagain.""assessment_id":existing_completed.id"already_taken":True"assessment_status":"COMPLETED""allow_retakes":True"cooldown_end":cooldown_end.isoformat()"time_remaining":{"days":days_remaining"hours":hours_remaining}}status=status.HTTP_400_BAD_REQUEST)ifexisting_in_progress:returnResponse({"status":"error""message":f"Youalreadyhaveanin-progress{assessment_type.lower()}assessment.""assessment_id":existing_in_progress.id"already_taken":True"assessment_status":"IN_PROGRESS"}status=status.HTTP_400_BAD_REQUEST)#Getorcreatestudentleveltodetermineinitiallevelstudent_levelcreated=StudentLevel.objects.get_or_create(student=request.userdefaults={"current_level":1"current_level_display":"Beginner"})#Sanitizeassessmentdatatoremoveanymockindicatorsassessment_data={"student":request.user"assessment_type":assessment_type"status":"IN_PROGRESS""start_time":timezone.now()"initial_level":student_level.current_level#Setinitiallevelfromstudentlevel"learning_path":learning_path#Setlearningpath}#Sanitizethedatatoremoveanymockindicatorsassessment_data=sanitize_assessment_data(assessment_data)#Createanewassessmentwithinitial_levelsetassessment=Assessment.objects.create(**assessment_data)#Getquestionsfortheassessmentquestions_query=AssessmentQuestion.objects.filter(is_public=Trueis_placement=(assessment_type=="PLACEMENT"))#Filterbylearningpathifspecifiediflearning_pathandlearning_path!="general":#Firsttrytogetpath-specificquestionspath_questions=questions_query.filter(category=learning_path)#Ifwehaveenoughpath-specificquestionsusethemifpath_questions.count()>=10:questions=path_questions.order_by("?")[:10]else:#Otherwiseprioritizepath-specificquestionsbutincludegeneralonesgeneral_questions=questions_query.filter(category="general")#Getallpath-specificquestionspath_questions=list(path_questions)#Getenoughgeneralquestionstoreach10totalgeneral_questions=list(general_questions.order_by("?")[:10-len(path_questions)])#Combineandshufflequestions=path_questions+general_questionsrandom.shuffle(questions)else:#Usegeneralquestionsquestions=questions_query.order_by("?")[:10]#Addquestionstotheassessmentforquestioninquestions:#Skipmockquestionsifprevent_mock_question_creation({"question_text":question.question_text}):logger.warning(f"Skippingmockquestion:{question.question_text[:50]}...")continueAssessmentResponse.objects.create(assessment=assessmentquestion=question)#Returntheassessmentdetailsserializer=self.get_serializer(assessment)returnResponse(serializer.datastatus=status.HTTP_201_CREATED)exceptExceptionase:returnResponse({"detail":str(e)}status=status.HTTP_400_BAD_REQUEST)defsubmit_assessment(selfrequest):"""Submitanswersforanassessment"""try:#GetassessmentIDfromrequest-checkmultiplepossibleparameternamesassessment_id=request.data.get("assessment_id")orrequest.data.get("id")#Logtherequestdatafordebugginglogger.info(f"Assessmentsubmissionrequestdata:{request.data}")ifnotassessment_id:logger.error("AssessmentIDismissingintherequestdata")logger.error(f"Requestdatakeys:{request.data.keys()}")returnResponse({"detail":"AssessmentIDisrequired""success":False"error":"Missingassessment_idparameter"}status=status.HTTP_400_BAD_REQUEST)#Gettheassessmenttry:#Ifuserisadminorprofessorallowaccesstoanyassessmentifrequest.user.is_stafforhasattr(request.user"professor"):assessment=Assessment.objects.get(id=assessment_id)else:#Otherwiseonlyallowaccesstotheuser'sownassessmentsassessment=Assessment.objects.get(id=assessment_idstudent=request.user)logger.info(f"Foundassessment{assessment_id}forsubmission")exceptAssessment.DoesNotExist:logger.error(f"Assessment{assessment_id}notfound")returnResponse({"detail":"Assessmentnotfound""success":False"error":"Assessmentnotfound"}status=status.HTTP_404_NOT_FOUND)#Logassessmentdetailslogger.info(f"ProcessingassessmentwithID{assessment_id}foruser{request.user.username}")#Getanswersfromrequestanswers=request.data.get("answers"[])#Checkifwehaveanswerstoprocessifnotanswers:logger.warning(f"Noanswersprovidedforassessment{assessment_id}")returnResponse({"detail":"Noanswersprovided""success":False"error":"Noanswersprovidedforsubmission"}status=status.HTTP_400_BAD_REQUEST)logger.info(f"Processing{len(answers)}answersforassessment{assessment_id}")#Saveanswersprocessed_answers=[]foranswerinanswers:question_id=answer.get("question_id")#Checkmultiplepossiblefieldnamesforanswertextanswer_text=answer.get("answer_text")oranswer.get("answer")or""original_answer=answer#Storetheoriginalanswerobjectifnotquestion_id:logger.warning(f"Missingquestion_idinanswer:{answer}")continuetry:question=AssessmentQuestion.objects.get(id=question_id)try:#Trytogetexistingresponseresponse=AssessmentResponse.objects.get(assessment=assessmentquestion=question)#Updateallanswerfieldsforcompatibilityresponse.answer_text=answer_text#Primaryfieldresponse.answer=answer_text#Legacyfield#Handlestudent_answerfieldforcomplexquestiontypesifquestion.question_type.upper()in["MATCHING""ORDERING""SEQUENCE"]:#Forcomplextypesstoretheoriginalanswerinstudent_answerifisinstance(original_answerdict):#Iftheoriginalanswerisadictstoreitdirectlyresponse.student_answer=original_answerelif("student_answer"inoriginal_answerandoriginal_answer["student_answer"]):#Ifstudent_answerisprovidedintherequestuseitresponse.student_answer=original_answer["student_answer"]else:#TrytoparseasJSONifit'sastringrepresentationofJSONtry:response.student_answer=json.loads(answer_text)exceptjson.JSONDecodeError:#IfnotvalidJSONstoreasisresponse.student_answer=answer_textresponse.submitted_at=timezone.now()response.save()#Evaluatetheresponsetosetis_correctandpoints_earnedresponse.evaluate()processed_answers.append(question_id)logger.info(f"Updatedresponseforquestion{question_id}inassessment{assessment_id}")exceptAssessmentResponse.DoesNotExist:#Checkifthisisamockresponseresponse_data={"assessment":assessment"question":question"answer_text":answer_text#Primaryfield"answer":answer_text#Legacyfield"submitted_at":timezone.now()"feedback":original_answer.get("feedback""")}#Checkifthisisamockresponseifprevent_mock_response_creation(response_data):logger.warning(f"Preventedcreationofmockresponseforquestion{question_id}")continue#Createnewresponseifitdoesn'texistresponse=AssessmentResponse.objects.create(**response_data)#Handlestudent_answerfieldforcomplexquestiontypesifquestion.question_type.upper()in["MATCHING""ORDERING""SEQUENCE"]:#Forcomplextypesstoretheoriginalanswerinstudent_answerifisinstance(original_answerdict):#Iftheoriginalanswerisadictstoreitdirectlyresponse.student_answer=original_answerresponse.save()elif("student_answer"inoriginal_answerandoriginal_answer["student_answer"]):#Ifstudent_answerisprovidedintherequestuseitresponse.student_answer=original_answer["student_answer"]response.save()else:#TrytoparseasJSONifit'sastringrepresentationofJSONtry:response.student_answer=json.loads(answer_text)response.save()exceptjson.JSONDecodeError:#IfnotvalidJSONstoreasisresponse.student_answer=answer_textresponse.save()#Evaluatetheresponsetosetis_correctandpoints_earnedresponse.evaluate()processed_answers.append(question_id)logger.info(f"Creatednewresponseforquestion{question_id}inassessment{assessment_id}")exceptAssessmentQuestion.DoesNotExist:logger.warning(f"QuestionwithID{question_id}notfound")continue#Markassessmentascompletedassessment.status="COMPLETED"assessment.end_time=timezone.now()assessment.completed=True#Initiallevelwillbesetinthetryblockbelow#Calculatescoreandupdatestudentleveltry:#Ensureinitial_levelissetbeforesubmittingifassessment.initial_levelisNone:#Getorcreatestudentleveltogetcurrentlevelstudent_level_=StudentLevel.objects.get_or_create(student=assessment.studentdefaults={"current_level":1"current_level_display":"Beginner"})assessment.initial_level=student_level.current_levelassessment.save(update_fields=["initial_level"])logger.info(f"Setinitial_levelto{assessment.initial_level}forassessment{assessment_id}")#Callthesubmitmethodtoprocesstheassessmentassessment.submit()logger.info(f"Successfullysubmittedassessment{assessment_id}withscore{assessment.score}")exceptExceptionassubmit_error:logger.error(f"Errorinassessment.submit():{str(submit_error)}"exc_info=True)#Logdetailedinformationabouttheassessmentlogger.error(f"Assessmentdetails:ID={assessment_id}student_id={assessment.student_id}"f"initial_level={assessment.initial_level}status={assessment.status}"f"completed={assessment.completed}questions_count={assessment.questions.count()}")#Trytocompletetheassessmentmanuallytry:assessment.score=assessment.calculate_score()assessment.completed=Trueassessment.end_time=timezone.now()assessment.status="COMPLETED"assessment.save()logger.info(f"Manuallycalculatedscore{assessment.score}forassessment{assessment_id}")exceptExceptionasmanual_error:logger.error(f"Errorinmanualassessmentcompletion:{str(manual_error)}"exc_info=True)#Lastresort-saveminimalchangestry:assessment.completed=Trueassessment.status="COMPLETED"assessment.save(update_fields=["completed""status"])logger.warning(f"Minimalassessmentcompletionfor{assessment_id}-onlystatusupdated")exceptExceptionasminimal_error:logger.critical(f"CRITICAL:Failedtocompleteassessment{assessment_id}:{str(minimal_error)}")#CreateAssessmentResultifitdoesn'texisttry:from.modelsimportAssessmentResultresultcreated=AssessmentResult.objects.get_or_create(assessment=assessmentdefaults={"skill_analysis":assessment.skill_scoresor{}"strengths":assessment.detailed_results.get("strengths"[])"weaknesses":assessment.detailed_results.get("weaknesses"[])"recommendations":assessment.detailed_results.get("recommendations"[])})logger.info(f"{'Created'ifcreatedelse'Retrieved'}AssessmentResultforassessment{assessment.id}")exceptExceptionasresult_error:logger.error(f"ErrorcreatingAssessmentResult:{str(result_error)}"exc_info=True)#Returntheassessmentdetailswithsuccessflagserializer=self.get_serializer(assessment)response_data=serializer.dataresponse_data["success"]=Trueresponse_data["processed_answers"]=processed_answersreturnResponse(response_data)exceptExceptionase:error_message=f"Errorsubmittingassessment:{str(e)}"logger.error(error_message)returnResponse({"detail":error_message"success":False"error":str(e)"message":"Failedtosubmitassessment.Pleasetryagain."}status=status.HTTP_400_BAD_REQUEST)defresults(selfrequest):"""Getassessmentresultsforthestudent"""try:#Getthelatestcompletedassessmentassessment=(Assessment.objects.filter(student=request.usercompleted=True).order_by("-end_time").first())ifnotassessment:returnResponse({"detail":"Nocompletedassessmentsfound"}status=status.HTTP_404_NOT_FOUND)#Returntheassessmentdetailsserializer=self.get_serializer(assessment)returnResponse(serializer.data)exceptExceptionase:returnResponse({"detail":str(e)}status=status.HTTP_400_BAD_REQUEST)defquestions(selfrequest):"""Getquestionsforstudentassessments"""try:#Getassessmenttypefromrequestassessment_type=request.query_params.get("type""PLACEMENT")learning_path=request.query_params.get("learning_path"None)#Startwithbasefilterfilter_params={"is_public":True"is_placement":(assessment_type=="PLACEMENT")}#Addlearningpathfilterifprovided(usingcategoryfield)iflearning_path:#Trytogetquestionsspecifictothelearningpathcategorypath_questions=AssessmentQuestion.objects.filter(**filter_paramscategory=learning_path).order_by("?")#Ifwedon'thaveenoughpath-specificquestionsincludesomegeneralquestionsifpath_questions.count()<10:general_questions=(AssessmentQuestion.objects.filter(**filter_paramscategory="general").exclude(id__in=[q.idforqinpath_questions]).order_by("?")[:10-path_questions.count()])#Combineallquestionsquestions=list(path_questions)+list(general_questions)else:questions=path_questions[:10]else:#Getquestionsfortheassessmenttypewithoutlearningpathfilterquestions=AssessmentQuestion.objects.filter(**filter_params).order_by("?")[:10]#Returnthequestionsserializer=AssessmentQuestionSerializer(questionsmany=True)returnResponse(serializer.data)exceptExceptionase:returnResponse({"detail":str(e)}status=status.HTTP_400_BAD_REQUEST)@action(detail=Truemethods=["post"])defsubmit_answer(selfrequestpk=None):"""Submitananswerforaquestionintheassessment"""assessment=self.get_object()if"question_id"notinrequest.dataor"answer"notinrequest.data:returnResponse({"error":"question_idandanswerarerequired"}status=status.HTTP_400_BAD_REQUEST)question=get_object_or_404(AssessmentQuestionid=request.data["question_id"])ifquestionnotinassessment.questions.all():returnResponse({"error":"Thisquestionisnotpartoftheassessment"}status=status.HTTP_400_BAD_REQUEST)#Createresponseresponse=AssessmentResponse.objects.create(assessment=assessmentquestion=questionanswer=request.data["answer"]response_time=request.data.get("response_time"0))#Checkifallquestionsareansweredifassessment.responses.count()==assessment.questions.count():assessment.completed=Trueassessment.completion_time=timezone.now()assessment.save()#Generaterecommendationsifneededself._generate_recommendations(assessment)returnResponse(AssessmentResponseSerializer(response).data)def_generate_recommendations(selfassessment):"""Generatecourserecommendationsbasedonassessmentperformance"""try:service=AssessmentService(assessment.student)recommendations=service.get_course_recommendations(assessment)assessment.recommendations=recommendationsassessment.save()exceptExceptionase:logger.error(f"Errorgeneratingrecommendations:{str(e)}")classAssessmentResponseView(views.APIView):"""Handleassessmentresponsesandrecommendations"""permission_classes=[IsAuthenticated]defget(selfrequestassessment_id=None):"""Getassessmentresultsandrecommendations"""try:ifassessment_id:assessment=Assessment.objects.get(id=assessment_idstudent=request.userstatus="COMPLETED")else:#Getlatestcompletedassessmentassessment=Assessment.objects.filter(student=request.userstatus="COMPLETED").latest("end_time")assessment_service=AssessmentService(request.user)#Getupdatedanalysisandrecommendationsanalysis=assessment_service.analyze_strengths_weaknesses(assessment)recommendations=assessment_service.get_course_recommendations(assessment)learning_path=assessment_service.get_learning_path(assessment)returnResponse({"status":"success""assessment":AssessmentDetailSerializer(assessment).data"analysis":analysis"recommendations":recommendations"learning_path":learning_path})exceptAssessment.DoesNotExist:returnResponse({"status":"error""message":"Assessmentnotfound"}status=status.HTTP_404_NOT_FOUND)exceptExceptionase:logger.error(f"Errorgettingassessmentresults:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAdminAssessmentStatsViewSet(viewsets.ViewSet):"""ViewSetforadminassessmentstatistics"""permission_classes=[IsProfessorUser|IsAdminUser]deflist(selfrequest):"""Getoverallassessmentstatistics"""total_assessments=Assessment.objects.count()completed_assessments=Assessment.objects.filter(completed=True).count()average_score=(Assessment.objects.filter(completed=True).aggregate(avg_score=Avg("total_score"))["avg_score"]or0)level_distribution=(Assessment.objects.filter(completed=True).values("level").annotate(count=Count("id")))returnResponse({"total_assessments":total_assessments"completed_assessments":completed_assessments"average_score":round(average_score2)"level_distribution":level_distribution})classAssessmentResponseViewSet(viewsets.ModelViewSet):"""ViewSetformanagingassessmentresponses"""serializer_class=AssessmentResponseSerializerpermission_classes=[permissions.IsAuthenticated]defget_queryset(self):ifself.request.user.is_stafforhasattr(self.request.user"professor"):returnAssessmentResponse.objects.all()returnAssessmentResponse.objects.filter(assessment__student=self.request.user)defperform_create(selfserializer):serializer.save()defget_permissions(self):ifself.actionin["list""retrieve"]:permission_classes=[permissions.IsAuthenticated]else:permission_classes=[IsStudentUser]return[permission()forpermissioninpermission_classes]classStudentProgressView(views.APIView):"""Viewfortrackingstudentassessmentprogress"""permission_classes=[IsAuthenticatedIsStudentUser]defget(selfrequest):"""Getstudent'sassessmentprogressandanalytics"""try:#Getallcompletedassessmentsforthestudentcompleted_assessments=Assessment.objects.filter(student=request.userstatus="COMPLETED").order_by("-end_time")#Calculateoverallstatisticstotal_assessments=completed_assessments.count()avg_score=completed_assessments.aggregate(Avg("score"))["score__avg"]or0#Getlevelprogressionovertimelevel_progression=[]latest_assessment=Noneforassessmentincompleted_assessments:level_progression.append({"date":assessment.end_time.isoformat()"score":assessment.score"type":assessment.assessment_type})ifnotlatest_assessment:latest_assessment=assessment#Getcurrentstrengthsandweaknessescurrent_strengths=[]current_weaknesses=[]iflatest_assessment:current_strengths=latest_assessment.detailed_results.get("strengths"[])current_weaknesses=latest_assessment.detailed_results.get("weaknesses"[])#Getrecommendedcoursesifavailablerecommended_courses=[]if(latest_assessmentand"recommendations"inlatest_assessment.detailed_results):recommended_courses=latest_assessment.detailed_results["recommendations"]returnResponse({"status":"success""statistics":{"total_assessments":total_assessments"average_score":round(avg_score2)"current_level":(latest_assessment.scoreiflatest_assessmentelseNone)}"level_progression":level_progression"strengths":current_strengths"weaknesses":current_weaknesses"recommended_courses":recommended_courses"recent_assessments":AssessmentDetailSerializer(completed_assessments[:5]many=True).data})exceptExceptionase:logger.error(f"Errorgettingstudentprogress:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classCourseAssessmentViewSet(viewsets.ViewSet):"""ViewSetforhandlingcourseassessments"""permission_classes=[IsAuthenticated]defretrieve(selfrequestcourse_id=None):"""Getassessmentdetailsforacourse"""course=get_object_or_404(Courseid=course_id)assessment=Assessment.objects.filter(student=request.usercourse=coursestatus="IN_PROGRESS").first()ifnotassessment:returnResponse({"status":"NO_ACTIVE_ASSESSMENT""message":"Noactiveassessmentfoundforthiscourse"})serializer=AssessmentDetailSerializer(assessment)returnResponse(serializer.data)defcreate(selfrequestcourse_id=None):"""Startanewassessmentforacourse"""course=get_object_or_404(Courseid=course_id)existing=Assessment.objects.filter(student=request.usercourse=coursestatus="IN_PROGRESS").first()ifexisting:returnResponse({"error":"Youalreadyhaveanin-progressassessmentforthiscourse"}status=status.HTTP_400_BAD_REQUEST)#Getappropriatequestionsforthecoursequestions=AssessmentQuestion.objects.filter(category=course.categorydifficulty_level__lte=course.level).order_by("?")[:10]ifnotquestions.exists():returnResponse({"error":"Noquestionsavailableforthiscourse"}status=status.HTTP_404_NOT_FOUND)assessment=Assessment.objects.create(student=request.usercourse=courseassessment_type="PROGRESS"status="IN_PROGRESS"start_time=timezone.now())assessment.questions.set(questions)serializer=AssessmentDetailSerializer(assessment)returnResponse(serializer.datastatus=status.HTTP_201_CREATED)@action(detail=Truemethods=["post"])defanalyze(selfrequestcourse_id=None):"""Analyzecompletedcourseassessment"""try:assessment=get_object_or_404(Assessmentstudent=request.usercourse_id=course_idstatus="COMPLETED")#Getdetailedanalysisresponse_analysis={"total_questions":assessment.questions.count()"correct_answers":assessment.responses.filter(is_correct=True).count()"total_score":assessment.score"completion_time":assessment.end_time"average_response_time":assessment.responses.aggregate(avg_time=Avg("time_spent_seconds"))["avg_time"]or0}#Getquestion-wiseanalysisquestion_analysis=[]forresponseinassessment.responses.select_related("question"):question_analysis.append({"question":response.question.question_text"is_correct":response.is_correct"time_spent_seconds":response.time_spent_seconds"points_earned":response.points_earned"feedback":response.feedback})#Getrecommendationsifneededrecommendations=[]ifassessment.score<70:#Ifscoreisbelow70%providerecommendationsrecommendations=assessment.detailed_results.get("recommendations"[])returnResponse({"status":"success""overall_analysis":response_analysis"question_analysis":question_analysis"recommendations":recommendations})exceptAssessment.DoesNotExist:returnResponse({"status":"error""message":"Nocompletedassessmentfoundforthiscourse"}status=status.HTTP_404_NOT_FOUND)exceptExceptionase:logger.error(f"Erroranalyzingcourseassessment:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)def_generate_course_recommendations(selfassessment):"""Generatepersonalizedcourserecommendations"""try:#Gettopicswherestudentperformedpoorlyweak_topics=[]forresponseinassessment.responses.filter(is_correct=False):weak_topics.append(response.question.category)#Getrecommendedcoursesforweaktopicsrecommended_courses=Course.objects.filter(category__in=weak_topicslevel__lte=assessment.course.level#Don'trecommendhigherlevelcourses).distinct()[:3]returnCourseSerializer(recommended_coursesmany=True).dataexceptExceptionase:logger.error(f"Errorgeneratingcourserecommendations:{str(e)}")return[]classCoursePrerequisitesView(views.APIView):"""Viewforcheckingandmanagingcourseprerequisites"""permission_classes=[IsAuthenticated]defget(selfrequestcourse_id):"""Getprerequisitesforacourseandcheckifstudentmeetsthem"""try:course=get_object_or_404(Courseid=course_id)prerequisites=course.prerequisites.all()#Checkstudent'scompletionstatusforeachprerequisiteprerequisite_status=[]forprereqinprerequisites:is_completed=Assessment.objects.filter(student=request.usercourse=prereqstatus="COMPLETED"score__gte=70#Assuming70%isthepassingscore).exists()prerequisite_status.append({"course":CourseSerializer(prereq).data"is_completed":is_completed})returnResponse({"status":"success""prerequisites":prerequisite_status"all_prerequisites_met":all(status["is_completed"]forstatusinprerequisite_status)})exceptCourse.DoesNotExist:returnResponse({"status":"error""message":"Coursenotfound"}status=status.HTTP_404_NOT_FOUND)exceptExceptionase:logger.error(f"Errorcheckingcourseprerequisites:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classCourseSkillsView(APIView):"""Viewtohandlecourseskillsendpoints-bothrequiredanddevelopedskills"""defget(selfrequestcourse_id):try:course=Course.objects.get(id=course_id)required_skills=Skill.objects.filter(name__in=course.skills_required)developed_skills=Skill.objects.filter(name__in=course.skills_developed)returnResponse({"required_skills":SkillSerializer(required_skillsmany=True).data"developed_skills":SkillSerializer(developed_skillsmany=True).data"new_skills":list(set(course.skills_developed)-set(course.skills_required))})exceptCourse.DoesNotExist:returnResponse({"error":"Coursenotfound"}status=status.HTTP_404_NOT_FOUND)classAssessmentViewSet(viewsets.ModelViewSet):"""ViewSetformanagingassessments"""serializer_class=AssessmentDetailSerializerpermission_classes=[permissions.IsAuthenticatedCanTakeAssessments]assessment_service=AssessmentService()defget_queryset(self):"""Getassessmentsforcurrentuser"""returnAssessment.objects.filter(student=self.request.user).prefetch_related("questions""responses")defcreate(selfrequest):"""Createanewassessment"""try:assessment=self.assessment_service.create_assessment(student=request.userassessment_type=request.data.get("assessment_type""GENERAL")num_questions=request.data.get("num_questions"))serializer=self.get_serializer(assessment)returnResponse(serializer.datastatus=status.HTTP_201_CREATED)exceptExceptionase:logger.error(f"Errorcreatingassessment:{str(e)}")returnResponse({"error":str(e)}status=status.HTTP_400_BAD_REQUEST)@action(detail=Truemethods=["post"])defsubmit(selfrequestpk=None):"""Submitanassessmentwithresponses"""assessment=self.get_object()try:#Logthesubmissionattemptlogger.info(f"Submittingassessment{assessment.id}forstudent{assessment.student.username}")#Ensureinitial_levelissetbeforesubmittingifassessment.initial_levelisNone:#Getorcreatestudentleveltogetcurrentlevelstudent_levelcreated=StudentLevel.objects.get_or_create(student=assessment.studentdefaults={"current_level":1"current_level_display":"Beginner"})assessment.initial_level=student_level.current_levelassessment.save()logger.info(f"Setinitial_levelto{assessment.initial_level}forassessment{assessment.id}")#Completetheassessmentcompleted_assessment=self.assessment_service.complete_assessment(assessment=assessment)#CreateAssessmentResultifitdoesn'texistfrom.modelsimportAssessmentResultresultcreated=AssessmentResult.objects.get_or_create(assessment=completed_assessmentdefaults={"skill_analysis":completed_assessment.skill_scoresor{}"strengths":completed_assessment.detailed_results.get("strengths"[])"weaknesses":completed_assessment.detailed_results.get("weaknesses"[])"recommendations":completed_assessment.detailed_results.get("recommendations"[])})logger.info(f"{'Created'ifcreatedelse'Retrieved'}AssessmentResultforassessment{completed_assessment.id}")serializer=AssessmentDetailSerializer(completed_assessment)returnResponse(serializer.data)exceptExceptionase:logger.error(f"Errorsubmittingassessment:{str(e)}")returnResponse({"error":str(e)}status=status.HTTP_400_BAD_REQUEST)@action(detail=Truemethods=["get"])defresults(selfrequestpk=None):"""Getdetailedassessmentresults"""assessment=self.get_object()ifnotassessment.completed:returnResponse({"error":"Assessmentnotcompleted"}status=status.HTTP_400_BAD_REQUEST)#Getanalysisandrecommendationsassessment_service=AssessmentService(request.user)analysis=assessment_service.analyze_strengths_weaknesses(assessment)recommendations=assessment_service.get_course_recommendations(assessment)learning_path=assessment_service.get_learning_path(assessment)returnResponse({"assessment":self.get_serializer(assessment).data"analysis":analysis"recommendations":recommendations"learning_path":learning_path})@action(detail=Falsemethods=["get"])defprogress(selfrequest):"""Getstudent'sassessmentprogress"""completed_assessments=(self.get_queryset().filter(completed=True).order_by("-end_time"))total_assessments=completed_assessments.count()avg_score=completed_assessments.aggregate(Avg("score"))["score__avg"]or0latest_assessment=completed_assessments.first()current_strengths=[]current_weaknesses=[]iflatest_assessment:current_strengths=latest_assessment.strengthscurrent_weaknesses=latest_assessment.weaknessesreturnResponse({"statistics":{"total_assessments":total_assessments"average_score":round(avg_score2)"current_level":(latest_assessment.scoreiflatest_assessmentelseNone)}"strengths":current_strengths"weaknesses":current_weaknesses"recent_assessments":self.get_serializer(completed_assessments[:5]many=True).data})classAssessmentResponseViewSet(viewsets.ModelViewSet):"""ViewSetformanagingassessmentresponses"""serializer_class=AssessmentResponseSerializerpermission_classes=[IsAuthenticatedCanTakeAssessments]defget_queryset(self):"""Getresponsesforcurrentuser"""returnAssessmentResponse.objects.filter(assessment__student=self.request.user).select_related("question""assessment")defcreate(selfrequest):"""Submitaresponsetoanassessmentquestion"""serializer=self.get_serializer(data=request.data)ifserializer.is_valid():#Validateassessmentparticipationassessment=get_object_or_404(Assessmentid=request.data.get("assessment")student=request.usercompleted=False)#Createandevaluateresponseresponse=serializer.save()response.evaluate()#Ifthisisanadaptiveassessmentadjustdifficultyfornextquestionifassessment.is_adaptive:try:#Getstudentlevelstudent_level=StudentLevel.objects.get(student=request.user)#Getassessmentserviceassessment_service=AssessmentService()#Applyadaptivedifficultyassessment_service._apply_adaptive_difficulty(questions=AssessmentQuestion.objects.filter(is_active=True)student_level=student_levelassessment=assessmentprevious_response=response)#Logtheadaptivedifficultyadjustmentlogger.info(f"Appliedadaptivedifficultyforassessment{assessment.id}afterresponse{response.id}")exceptExceptionase:#Logerrorbutdon'tfailtherequestlogger.error(f"Errorapplyingadaptivedifficulty:{str(e)}")returnResponse(self.get_serializer(response).datastatus=status.HTTP_201_CREATED)returnResponse(serializer.errorsstatus=status.HTTP_400_BAD_REQUEST)classStudentLevelView(generics.RetrieveAPIView):"""Viewforretrievingstudent'scurrentlevel"""serializer_class=StudentLevelSerializerpermission_classes=[permissions.AllowAny]#Allowanyusertoaccessthisendpointdefget_object(self):"""Getorcreatestudentlevel"""#GetuserIDfromqueryparamsoruseauthenticateduseruser_id=self.request.query_params.get("user_id")ifuser_id:#UsetheprovideduserIDstudent_level_=StudentLevel.objects.get_or_create(student_id=user_iddefaults={"current_level":1"current_level_display":"Beginner"})elifself.request.user.is_authenticated:#Usetheauthenticateduserstudent_level_=StudentLevel.objects.get_or_create(student=self.request.userdefaults={"current_level":1"current_level_display":"Beginner"})else:#ReturnadefaultlevelifnouserIDorauthenticationreturnStudentLevel(current_level=1current_level_display="Beginner")returnstudent_levelclassStudentProgressView(generics.RetrieveAPIView):"""Viewforretrievingstudent'sprogress"""permission_classes=[IsAuthenticated]defget(selfrequest):"""Getcomprehensiveprogressreport"""service=AssessmentService()progress=service.get_student_progress(request.user)returnResponse(progress)
#!/usr/bin/envpython"""ModelConsolidationScriptThisscripthelpsconsolidateduplicatemodelsandfixcirculardependencies.Runthisafterthecodechangestoensuredatabaseconsistency."""import osimportsysimport django#AddthebackenddirectorytothePythonpathsys.path.insert(0os.path.dirname(os.path.dirname(os.path.abspath(__file__))))#SetupDjangoos.environ.setdefault('DJANGO_SETTINGS_MODULE''settings.development')django.setup()from django.core.managementimportcall_commandfrom django.dbimporttransactionfrom django.appsimportappsdefcheck_model_consistency():"""Checkformodelconsistencyissues"""print("🔍Checkingmodelconsistency...")issues=[]#CheckifSkillmodelexistsincoretry:Skill=apps.get_model('core''Skill')print(f"✅CoreSkillmodelfound:{Skill}")exceptLookupError:issues.append("❌CoreSkillmodelnotfound")#CheckifproxySkillmodelwasremovedfromassessmenttry:AssessmentSkill=apps.get_model('assessment''Skill')issues.append("⚠️AssessmentSkillmodelstillexists(shouldberemoved)")exceptLookupError:print("✅AssessmentSkillproxymodelproperlyremoved")#Checkforcircularimportstry:from assessment.modelsimportSkillAssessmentprint("✅Assessmentmodelsimportsuccessfully")exceptImportErrorase:issues.append(f"❌Importerrorinassessmentmodels:{e}")ifissues:print("\n🚨Issuesfound:")forissueinissues:print(f"{issue}")returnFalseelse:print("\n✅Allmodelconsistencycheckspassed!")returnTruedefcreate_migrations():"""Createmigrationsforthechanges"""print("\n📝Creatingmigrations...")try:#Createmigrationsforassessmentappcall_command('makemigrations''assessment'verbosity=2)#Createmigrationsforcoreappifneededcall_command('makemigrations''core'verbosity=2)print("✅Migrationscreatedsuccessfully")returnTrueexceptExceptionase:print(f"❌Failedtocreatemigrations:{e}")returnFalsedefrun_migrations():"""Runthemigrations"""print("\n🚀Runningmigrations...")try:call_command('migrate'verbosity=2)print("✅Migrationscompletedsuccessfully")returnTrueexceptExceptionase:print(f"❌Failedtorunmigrations:{e}")returnFalsedefcleanup_old_files():"""Cleanupoldutilityfilesthatwereconsolidated"""print("\n🧹Cleaningupoldfiles...")old_files=['utils/ai_agent_compatibility.py''utils/gemini_config.py''utils/langchain_service.py''utils/ai_prompt_templates.py''utils/redis_utils.py''utils/performance_monitoring.py']removed_count=0forfile_pathinold_files:full_path=os.path.join(os.path.dirname(os.path.dirname(__file__))file_path)ifos.path.exists(full_path):try:os.remove(full_path)print(f"✅Removed{file_path}")removed_count+=1exceptExceptionase:print(f"❌Failedtoremove{file_path}:{e}")else:print(f"ℹ️{file_path}alreadyremoved")print(f"\n✅Cleanupcompleted.Removed{removed_count}files.")defupdate_imports():"""Checkforfilesthatneedimportupdates"""print("\n🔄Checkingforimportupdatesneeded...")#Thisisasimplifiedcheck-inpracticeyou'dwanttoscanfiles#andupdateimportsprogrammaticallyfiles_to_check=['courses/views/''assessment/views/''users/views/']print("📋Filesthatmayneedimportupdates:")forfile_patterninfiles_to_check:print(f"-{file_pattern}*.py")print("\n💡Updateimportsfrom:")print("OLD:from utils.ai_service_utilsimport...")print("NEW:from utils.aiimportAIService")print("\nOLD:from utils.cache_utilsimport...")print("NEW:from utils.cacheimportcache_manager")print("\nOLD:from utils.monitoring_utilsimport...")print("NEW:from utils.monitoringimportmonitoring_service")defmain():"""Mainconsolidationprocess"""print("🎯NorthStarUniversity-ModelConsolidationScript")print("="*60)steps=[("ModelConsistencyCheck"check_model_consistency)("CreateMigrations"create_migrations)("RunMigrations"run_migrations)("CleanupOldFiles"cleanup_old_files)("ImportUpdateGuide"update_imports)]forstep_namestep_funcinsteps:print(f"\n📋Step:{step_name}")print("-"*40)ifstep_func==update_imports:#Thisstepdoesn'treturnabooleanstep_func()continuesuccess=step_func()ifnotsuccess:print(f"\n❌Step'{step_name}'failed.Pleasefixissuesbeforecontinuing.")returnFalseprint("\n"+"="*60)print("🎉Modelconsolidationcompletedsuccessfully!")print("\n📝Nextsteps:")print("1.Updateimportstatementsinyourviewsandotherfiles")print("2.Testtheapplicationthoroughly")print("3.Updateanycustommanagementcommands")print("4.Reviewandupdatedocumentation")returnTrueif__name__=='__main__':main()
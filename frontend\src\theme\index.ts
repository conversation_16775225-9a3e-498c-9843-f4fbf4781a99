/**
 * Central entry point for theme-related exports.
 *
 * This file exports all theme-related components and utilities
 * to provide a unified interface for theme functionality.
 */

// Export the main theme provider
export { default as ThemeProvider } from './ThemeProvider';
export { default as UnifiedThemeProvider } from './ThemeProvider';

// Export theme creation functions
export { createUnifiedTheme } from './unifiedTheme';
export { default as defaultTheme } from './unifiedTheme';

// Export glassmorphism utilities (primary CSS variable system)
export {
  glassmorphismTokens,
  getGlassmorphismStyles,
  getGlassmorphismCSSVariables,
} from './glassmorphism';

// Export common styles
export { default as commonStyles } from './commonStyles';

// Re-export theme slice for convenience
export {
  selectThemeMode,
  selectGlassOpacity,
  selectIsDarkMode,
  toggleTheme,
  setTheme,
  setGlassOpacity,
} from '../features/theme/themeSlice';

// Re-export theme context for convenience
export { useThemeContext } from '../contexts/ThemeContext';

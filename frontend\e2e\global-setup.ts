import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup...');

  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Wait for backend to be ready
    console.log('⏳ Waiting for backend to be ready...');
    await page.goto('http://localhost:8000/api/health/', { 
      waitUntil: 'networkidle',
      timeout: 60000 
    });
    console.log('✅ Backend is ready');

    // Wait for frontend to be ready
    console.log('⏳ Waiting for frontend to be ready...');
    await page.goto('http://localhost:3000', { 
      waitUntil: 'networkidle',
      timeout: 60000 
    });
    console.log('✅ Frontend is ready');

    // Create test data
    console.log('📝 Creating test data...');
    await createTestData();
    console.log('✅ Test data created');

  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }

  console.log('✅ Global setup completed');
}

async function createTestData() {
  // Create test users and data via API calls
  const response = await fetch('http://localhost:8000/api/test/setup/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      create_test_users: true,
      create_test_courses: true,
      create_test_assessments: true,
    }),
  });

  if (!response.ok) {
    throw new Error(`Failed to create test data: ${response.statusText}`);
  }

  const data = await response.json();
  console.log('Test data created:', data);

  // Store test data for use in tests
  process.env.TEST_STUDENT_USERNAME = data.student.username;
  process.env.TEST_STUDENT_PASSWORD = data.student.password;
  process.env.TEST_PROFESSOR_USERNAME = data.professor.username;
  process.env.TEST_PROFESSOR_PASSWORD = data.professor.password;
  process.env.TEST_COURSE_ID = data.course.id;
  process.env.TEST_ASSESSMENT_ID = data.assessment.id;
}

export default globalSetup;

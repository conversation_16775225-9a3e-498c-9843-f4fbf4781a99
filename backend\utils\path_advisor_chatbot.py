importhashlibimport json
import loggingimport osimport timefromdatetimeimport datetimefromtypingimportAnyDictListOptionalTuplefrom django.utils.translationimportgettextas_from.ai_path_advisorimportAIPathAdvisor#ImportunifiedAIsystemfrom.ai.servicesimportget_ai_servicefrom.direct_ai_path_advisorimportDirectAIPathAdvisor#Importunifiedcacheutilitiestry:from.unified_cache_utilsimportgenerate_cache_keyexceptImportError:#Fallbackcachekeygenerationdefgenerate_cache_key(prefix**kwargs):importhashlib#Createastringrepresentationofallthekeywordargumentsdata_str=f"{prefix}_{str(kwargs)}"returnf"{prefix}_{hashlib.md5(data_str.encode()).hexdigest()}"logger=logging.getLogger(__name__)classPathAdvisorChatBot:"""Aspecializedchatbotthathelpsstudentsdiscoverandnavigatetheirlearningpaths.ThiscombinesthefunctionalityoftheAIPathAdvisorwithchatcapabilities.Features:-Providesinformationaboutavailablelearningpaths-Helpsusersfindtherightpathbasedontheirinterestsandskills-Answersquestionsaboutspecificpaths-Integrateswithassessmentdatatoprovidepersonalizedrecommendations-SupportsbothEnglishandArabiclanguages"""def__init__(self):"""InitializethePathAdvisorChatBot"""try:#InitializetheAIpathadvisorself.direct_advisor=DirectAIPathAdvisor()self.fallback_advisor=AIPathAdvisor()#UsetheunifiedAIserviceforchatself.chat_service=get_ai_service()#Usetheserviceinstancedirectly#Usefallbackconfigurationsinceconfigcomesfromfrontendself.generation_config={'temperature':0.7'max_tokens':2048'top_p':0.9}logger.info("PathAdvisorChatBotinitializedwithunifiedAIservice")#Initializeresponsecacheself.response_cache={}self.cache_ttl=3600#CacheTTLinseconds(1hour)self.cache_hits=0self.cache_misses=0#Initializeanalyticsself.interaction_count=0self.path_specific_queries={}self.query_history=[]self.error_count=0self.avg_response_time=0self.total_response_time=0#Initializefeedbacktrackingself.feedback_data={"positive":0"negative":0"neutral":0"detailed":[]#Storedetailedfeedback}#Initializepersonalizationdataself.user_preferences={}#Storeuser-specificpreferencesself.popular_paths={}#Trackpopularpathsbyuserdemographicself.learning_data={#Dataforimprovingresponsesovertime"successful_responses":[]"failed_responses":[]"improvement_suggestions":[]}logger.info("PathAdvisorChatBotinitializedsuccessfully")exceptExceptionase:logger.error(f"ErrorinitializingPathAdvisorChatBot:{str(e)}")raisedefget_chat_response(selfmessage:strcontext:Dict)->Dict:"""Generateachatresponseforpath-relatedquerieswithpersonalizationThismethodnowincludespersonalizationbasedonusercontextandlearningfrompreviousinteractionstoimproveresponsesovertime.Args:message:Theuser'smessagecontext:TheconversationcontextincludinguserinfoandpreviousinteractionsReturns:Dictcontainingtheresponsecontentandmetadata"""self.start_time=time.time()self.interaction_count+=1#ExtractuserIDforpersonalizationifavailableuser_id=context.get("user_id""anonymous")#Updateorinitializeuserpreferencesifuser_id!="anonymous":ifuser_idnotinself.user_preferences:self.user_preferences[user_id]={"preferred_paths":[]"interaction_count":0"last_interaction":None"language":context.get("language""en")"demographic":context.get("demographic""unknown")}#Updateuserinteractiondataself.user_preferences[user_id]["interaction_count"]+=1self.user_preferences[user_id]["last_interaction"]=datetime.now().isoformat()#Trackdemographicdataifavailabledemographic=context.get("demographic""unknown")ifdemographic!="unknown":self.user_preferences[user_id]["demographic"]=demographictry:#Wedon'tneedtoextractlanguagehereaseachhandlermethodgetsitfromcontext#Checkifuserhaspathrecommendationhas_path_recommendation=bool(context.get("path_recommendation"))#Checkifthisisapathrecommendationrequestifself._is_path_recommendation_request(message):returnself._handle_path_recommendation(messagecontext)#Checkifthisisapath-specificquestionifself._is_path_specific_question(messagecontext):returnself._handle_path_specific_question(messagecontext)#Checkifuserneedsmorehelpwithassessmentifself._is_assessment_help_request(message):returnself._handle_assessment_help(messagecontext)#Checkifuserisaskingformoredetailedinformationabouttheirrecommendedpathifhas_path_recommendationandself._is_more_details_request(messagecontext):returnself._handle_more_details_request(messagecontext)#Handlegeneralpath-relatedqueriesreturnself._handle_general_path_query(messagecontext)exceptExceptionase:#Logthefullexceptionfordebugginglogger.error(f"Errorgeneratingchatresponse:{str(e)}"exc_info=True)#Returnerrormessageinappropriatelanguageifcontext.get("language")=="ar":error_content="عذراً،واجهتمشكلةأثناءمعالجةطلبك.يرجىالمحاولةمرةأخرىأوطرحسؤالمختلفحولمسارالتعلمالخاصبك."suggested_questions=["ماهيالمساراتالتعليميةالمتاحة؟""كيفيمكننيمعرفةالمسارالمناسبلي؟""أخبرنيعنمسارالبرمجة"]else:error_content="I'msorryIencounteredanissuewhileprocessingyourrequest.Pleasetryagainoraskadifferentquestionaboutyourlearningpath."suggested_questions=["Whatlearningpathsareavailable?""HowcanIfindtherightpathforme?""TellmeabouttheProgrammingpath"]return{"content":error_content"metadata":{"error":str(e)"type":"error"}"suggested_questions":suggested_questions}def_is_path_recommendation_request(selfmessage:str)->bool:"""Checkifthemessageisrequestingapathrecommendation"""#Englishkeywordsen_recommendation_keywords=["recommendapath""suggestapath""whatpathshoulditake""whichpathisbest""helpmechooseapath""findmypath""discovermypath""whatshouldistudy""whatcareer""assessment""evaluatemyskills""testmyabilities""what'srightforme""careerguidance""bestfit""suitablepath""appropriatepath"]#Arabickeywordsar_recommendation_keywords=["توصيةبمسار""اقتراحمسار""أيمساريجبأنأختار""ماهوأفضلمسار""ساعدنيفياختيارمسار""ابحثعنمساري""اكتشفمساري""ماذايجبأنأدرس""ماهيالمهنة""تقييم""تقييممهاراتي""اختبارقدراتي""ماهوالمناسبلي""التوجيهالمهني""الأنسبلي""المسارالمناسب""مسارالمناسب"]message_lower=message.lower()returnany(keywordinmessage_lowerforkeywordinen_recommendation_keywords)orany(keywordinmessageforkeywordinar_recommendation_keywords)def_is_path_specific_question(selfmessage:strcontext:Dict)->bool:"""Checkifthemessageisaskingaboutaspecificlearningpath"""#Gettheuser'scurrentpathfromcontextifavailableuser_path=context.get("learning_path"{}).get("current_path")#Checkforpath-specifickeywordsinEnglishen_path_keywords={"programming":["programming""coding""developer""software""code""development""app""web"]"cybersecurity":["cybersecurity""security""hacking""cyber""networksecurity""protection""threats"]"finance":["finance""financial""accounting""investment""money""banking""economics"]"marketing":["marketing""market""advertising""brand""digitalmarketing""socialmedia""promotion"]}#Checkforpath-specifickeywordsinArabicar_path_keywords={"programming":["برمجة""ترميز""مطور""برمجيات""كود""تطوير""تطبيق""ويب"]"cybersecurity":["أمنسيبراني""أمن""اختراق""سيبراني""أمنالشبكات""حماية""تهديدات"]"finance":["تمويل""مالي""محاسبة""استثمار""مال""بنوك""اقتصاد"]"marketing":["تسويق""سوق""إعلان""علامةتجارية""تسويقرقمي""وسائلالتواصل""ترويج"]}message_lower=message.lower()#CheckifmessagecontainsEnglishkeywordsforanypathfor_keywordsinen_path_keywords.items():ifany(keywordinmessage_lowerforkeywordinkeywords):returnTrue#CheckifmessagecontainsArabickeywordsforanypathfor_keywordsinar_path_keywords.items():ifany(keywordinmessageforkeywordinkeywords):returnTrue#Checkfor"thispath"phrasesinEnglishen_this_path_phrases=["thispath""mypath""currentpath""recommendedpath"]#Checkfor"thispath"phrasesinArabicar_this_path_phrases=["هذاالمسار""مساري""المسارالحالي""المسارالموصىبه"]#Ifuserhasapathincontextandisaskingabout"thispath"ifuser_pathand(any(phraseinmessage_lowerforphraseinen_this_path_phrases)orany(phraseinmessageforphraseinar_this_path_phrases)):returnTruereturnFalsedef_handle_path_recommendation(self_:strcontext:Dict)->Dict:"""Handlearequestforpathrecommendation"""#Ifwedon'thaveassessmentdataasktheusertotaketheassessmentifnotcontext.get("assessment_data"):return{"content":"I'dbehappytohelpyoufindtherightlearningpath!TogiveyouthemostaccuraterecommendationIneedtounderstandyourinterestsandskillsbetter.Wouldyouliketotakeourquickassessmenttodiscoveryourperfectlearningpath?""metadata":{"type":"path_recommendation_intro""suggested_action":"start_assessment"}"suggested_questions":["YesI'dliketotaketheassessment""Tellmemoreabouttheavailablelearningpaths""Howlongdoestheassessmenttake?"]}#Ifwehaveassessmentdatabutnorecommendationyetifcontext.get("assessment_data")andnotcontext.get("path_recommendation"):#Generatearecommendationbasedontheassessmentdatatry:questions=context["assessment_data"]["questions"]answers=context["assessment_data"]["answers"]#Trytogetarecommendationfromthedirectadvisortry:recommendation=self.direct_advisor.get_recommendation(questionsanswers)exceptExceptionasdirect_error:logger.error(f"ErrorusingDirectAIPathAdvisor:{str(direct_error)}")#FallbacktotheoriginalAIadvisorrecommendation=self.fallback_advisor.get_recommendation(questionsanswers)#Getlanguagefromcontextlanguage=context.get("language""en")#Formattherecommendationasachatresponsereturn{"content":self._format_recommendation_as_chat(recommendationlanguage)"metadata":{"type":"path_recommendation""recommendation":recommendation}"suggested_questions":[f"Tellmemoreabout{recommendation['path_recommendation']}""WhatcoursesshouldIstartwith?""WhatskillswillIdevelop?"]}exceptExceptionase:logger.error(f"Errorgeneratingrecommendation:{str(e)}")return{"content":"I'mhavingtroubleanalyzingyourassessmentresultsrightnow.Let'stryadifferentapproach.Couldyoutellmewhatsubjectsoractivitiesyouenjoythemost?""metadata":{"type":"recommendation_error""error":str(e)}}#Ifwealreadyhavearecommendationprovidemoredetailsaboutitifcontext.get("path_recommendation"):recommendation=context["path_recommendation"]path=recommendation["path_recommendation"]#Generateamoredetailedresponseabouttherecommendedpathprompt=f"""Theuserhasbeenrecommendedthe{path}learningpath.Theywantmoreinformationaboutthispath.Theirstrengthsinclude:{''.join(recommendation['strengths'])}Areastheyshoulddevelop:{''.join(recommendation['areas_to_develop'])}Provideadetailedencouragingresponseaboutwhythispathisagoodfitforthemandwhatnextstepstheyshouldtake.Includespecificcoursestheymightwanttostartwithandpotentialcareeroutcomes.Keepyourresponseconversationalandsupportive."""try:response=self.chat_service.generate_content(prompt)return{"content":response"metadata":{"type":"path_recommendation_details""path":path}"suggested_questions":["WhatcoursesshouldItakefirst?""WhatjobscanIgetwiththeseskills?""Howlongwillittaketocompletethispath?"]}exceptExceptionase:logger.error(f"Errorgeneratingdetailedpathinfo:{str(e)}")returnself._get_fallback_path_details(path)def_handle_path_specific_question(selfmessage:strcontext:Dict)->Dict:"""Handlequestionsaboutspecificlearningpaths"""#Getlanguagefromcontextlanguage=context.get("language""en")is_arabic=language=="ar"#Determinewhichpaththequestionisaboutpath=self._extract_path_from_message(messagecontext)ifnotpath:returnself._handle_general_path_query(messagecontext)#Generatearesponseaboutthespecificpathifis_arabic:prompt=f"""#سياقالمحادثةأنتمستشارتعليميمحترففيمنصتناالتعليمية.الطالبيسألعنمسارالتعلم{path}بهذهالرسالة:"{message}"#دورككمستشارتعليمي،هدفكهوتقديممعلوماتمفيدةودقيقةمعبناءعلاقةمعالطالب.يجبأنتبدوكمستشاربشريذوخبرة،وليسكمساعدذكاءاصطناعي.#حولمسار{path}قدممعلوماتمحددةومفصلةحولمسار{path}تتناولسؤالهمبشكلمباشر.قمبتضمين:##المهاراتوالكفاءاتالرئيسية-المهاراتالتقنيةوالشخصيةالمحددةالمطورةفيهذاالمسار-كيفيةتطبيقهذهالمهاراتفيسيناريوهاتالعالمالحقيقي##المنهجوتقدمالدورة-الدوراتالمحددةفيهذاالمساروماسيتعلمهالطلاب-كيفتبنيالدوراتعلىبعضهاالبعض-فرصالمشاريعالعمليةوالخبرةالعملية##الفرصالمهنية-أدواروظيفيةمحددةيعدهذاالمسارالطلابلها-الطلبفيالسوقوإمكاناتالنمو-نطاقاتالرواتبوفرصالتقدم##الروابطمعالمساراتالأخرى-كيفيكملهذاالمسارأويؤديإلىمجالاتتعليميةأخرى-التخصصاتالمحتملةأوالمساراتالمتقدمة#إرشاداتالاستجابة-كنودودًاوشخصيًا-استخدملغةطبيعية،وجملمتنوعةالطول،ونبرةدافئة-تجنبعباراتمثل"كذكاءاصطناعي"أو"أناهناللمساعدة"-تحدثكمستشارتعليميبشري-تناولسؤالهمالمحددمباشرةقبلتقديممعلوماتإضافيةذاتصلة-قمبتضمين1-2أسئلةمتابعةمدروسةفيالنهايةلمواصلةالمحادثة-حافظعلىتركيزإجابتكوإيجازها(200-300كلمة)أجبباللغةالعربية."""else:prompt=f"""#CONVERSATIONCONTEXTYouareaprofessionaleducationaladvisoratourlearningplatform.Thestudentisaskingaboutthe{path}learningpathwiththismessage:"{message}"#YOURROLEAsaneducationaladvisoryourgoalistoprovidehelpfulaccurateinformationwhilebuildingrapportwiththestudent.YoushouldsoundlikeaknowledgeablehumanadvisornotanAIassistant.#ABOUTTHE{path.upper()}PATHProvidespecificdetailedinformationaboutthe{path}paththatdirectlyaddressestheirquestion.Include:##KeySkills&Competencies-Specifictechnicalandsoftskillsdevelopedinthispath-Howtheseskillsapplytoreal-worldscenarios##Curriculum&CourseProgression-Specificcoursesinthispathandwhatstudentswilllearn-Howcoursesbuilduponeachother-Practicalprojectsandhands-onexperienceopportunities##CareerOpportunities-Specificjobrolesthispathpreparesstudentsfor-Industrydemandandgrowthpotential-Salaryrangesandadvancementopportunities##ConnectionstoOtherPaths-Howthispathcomplementsorleadstootherlearningareas-Potentialspecializationsoradvancedpaths#RESPONSEGUIDELINES-Beconversationalandpersonable-usecontractionsvariedsentencelengthsandawarmtone-Avoidphraseslike"AsanAI"or"I'mheretohelp"-speakasahumaneducationaladvisor-Addresstheirspecificquestiondirectlybeforeprovidingadditionalrelevantinformation-Include1-2thoughtfulfollow-upquestionsattheendtocontinuetheconversation-Keepyourresponsefocusedandconcise(200-300words)"""try:#Preparedefaultmessagesifis_arabic:default_message=f"عذراً،لمأتمكنمنإنشاءاستجابةلسؤالكحولمسار{path}.يرجىالمحاولةمرةأخرىأوإعادةصياغةسؤالك."else:default_message=f"I'msorryIcouldn'tgenerateinformationaboutthe{path}path.Pleasetryagainorrephraseyourquestion."#Logthepromptbeingsentlogger.debug(f"Sendingpromptforpath-specificinfoabout{path}")#Generatetheresponsetry:#Addtimeouttoavoidlongwaitsstart_time=time.time()#Addamorespecificprompttoencouragedetailedresponsesenhanced_prompt=f"""{prompt}IMPORTANT:Pleaseprovideadetailedcomprehensiveresponseaboutthe{path}path.Yourresponseshouldbeatleast200wordsandcoverallthekeyaspectsmentionedabove.Donotreturnanemptyresponse."""content=self.chat_service.generate_content(enhanced_prompt)elapsed_time=time.time()-start_timelogger.info(f"Generatedcontentfor{path}pathin{elapsed_time:.2f}seconds")#Ifcontentisemptyusedefaultmessagewithmoredetailedloggingifnotcontentor(isinstance(contentstr)andnotcontent.strip()):logger.warning(f"Emptyresponseforpath-specificinfoabout{path}usingdefaultmessage")logger.debug(f"Emptyresponsereceivedforprompt:{enhanced_prompt[:200]}...")#Tryonemoretimewithasimplerprompttry:simple_prompt=f"Providedetailedinformationaboutthe{path}learningpathincludingkeyskillscoursesandcareeropportunities."content=self.chat_service.generate_content(simple_prompt)ifnotcontentor(isinstance(contentstr)andnotcontent.strip()):content=default_messageexceptException:content=default_message#Logsuccessfulcontentgenerationlogger.info(f"Successfullygeneratedcontentfor{path}pathwithlength:{len(str(content))}")exceptExceptionascontent_error:logger.error(f"Errorgeneratingcontent:{str(content_error)}"exc_info=True)content=default_messagereturn{"content":content"metadata":{"type":"path_specific_info""path":path}"suggested_questions":self._get_suggested_questions_for_path(pathis_arabic)}exceptExceptionase:logger.error(f"Errorgeneratingpath-specificinfo:{str(e)}")returnself._get_fallback_path_details(pathis_arabic)def_handle_general_path_query(selfmessage:strcontext:Dict)->Dict:"""Handlegeneralquestionsaboutlearningpaths"""#Getlanguagefromcontextlanguage=context.get("language""en")is_arabic=language=="ar"#Createapromptthatincludescontextaboutavailablepathsifis_arabic:prompt=f"""#سياقالمحادثةأنتمستشارتعليميمحترففيمنصتناالتعليمية.الطالبأرسلهذهالرسالةحولمساراتالتعلم:"{message}"#دورككمستشارتعليمي،هدفكهوتقديممعلوماتمفيدةودقيقةمعبناءعلاقةمعالطالب.يجبأنتبدوكمستشاربشريذوخبرة،وليسكمساعدذكاءاصطناعي.#حولمساراتناالتعليميةمنصتناتقدمأربعةمساراتتعليميةمتخصصة،كلمنهامصمملأهدافوظيفيةواهتماماتمختلفة:##1.البرمجةوتطويرالبرمجياتهذاالمسارمثاليللطلابالذينيستمتعونبحلالمشكلات،والتفكيرالمنطقي،وبناءالحلولالرقمية.-الدوراتالرئيسية:مقدمةفيالبرمجة،تطويرالويب،تطويرالواجهاتالأماميةوالخلفية،تطويرتطبيقاتالجوال-المساراتالمهنية:مطوربرمجيات،مطورويب،مطورتطبيقاتجوال،مهندسذكاءاصطناعي-الأنسبلـ:المفكرينالتحليليينالذينيستمتعونبإنشاءالحلولوالعملمعالتكنولوجيا##2.الأمنالسيبرانيوحمايةالمعلوماتهذاالمسارمثاليللطلابالمهتمينبالتفاصيل،التحليليين،والمهتمينبحمايةالأصولالرقمية.-الدوراتالرئيسية:أساسياتالأمنالسيبراني،أمنالشبكات،الاختراقالأخلاقي،عملياتالأمن-المساراتالمهنية:محللأمن،مختبراختراق،مهندسأمن،متخصصامتثال-الأنسبلـ:المهتمينبالتفاصيلوحلالمشكلاتالمهتمينبالأمنالرقميوإدارةالمخاطر##3.التمويلوتحليلالاستثمارهذاالمسارمثاليللطلابالتحليليين،المهتمينبالتفاصيل،والمهتمينبالأسواقالمالية.-الدوراتالرئيسية:أساسياتالتمويل،استراتيجياتالاستثمار،النمذجةالمالية،إدارةالمخاطر-المساراتالمهنية:محللمالي،مستشاراستثمار،مديرمخاطر،مستشارمالي-الأنسبلـ:المفكرينالتحليليينالذينيستمتعونبالعملمعالأرقاموالمفاهيمالاقتصادية##4.التسويقواستراتيجيةالعلاماتالتجاريةهذاالمسارمثاليللطلابالمبدعين،الموجهيننحوالتواصل،والمهتمينبسلوكالمستهلك.-الدوراتالرئيسية:أساسياتالتسويق،التسويقالرقمي،تسويقالمحتوى،تحليلاتالتسويق-المساراتالمهنية:متخصصتسويق،مسوقرقمي،استراتيجيمحتوى،مديرعلامةتجارية-الأنسبلـ:المتواصلينالمبدعينالذينيستمتعونبفهمالناسوبناءرسائلمقنعة#إرشاداتالاستجابة-كنودودًاوشخصيًا-استخدملغةطبيعية،وجملمتنوعةالطول،ونبرةدافئة-تجنبعباراتمثل"كذكاءاصطناعي"أو"أناهناللمساعدة"-تحدثكمستشارتعليميبشري-تناولسؤالهمالمحددمباشرةقبلتقديممعلوماتإضافيةذاتصلة-إذابداأنهمغيرمتأكدينمنالمسارالذييجباختياره،اقترحعليهمإجراءتقييم"اكتشفمسارالتعلمالمثاليلك"-قمبتضمين3-4أسئلةمتابعةمدروسةفيالنهايةلمواصلةالمحادثة-قمبتنسيقأسئلةالمتابعةكقائمةفينهايةردكمععنوان"SUGGESTED_QUESTIONS:"-يجبأنيكونكلسؤالمقترحفيسطرجديدويبدأبشرطة(-)-حافظعلىتركيزإجابتكوإيجازها(200-300كلمة)أجبباللغةالعربية."""else:prompt=f"""#CONVERSATIONCONTEXTYouareaprofessionaleducationaladvisoratourlearningplatform.Thestudenthassentthismessageaboutlearningpaths:"{message}"#YOURROLEAsaneducationaladvisoryourgoalistoprovidehelpfulaccurateinformationwhilebuildingrapportwiththestudent.YoushouldsoundlikeaknowledgeablehumanadvisornotanAIassistant.#ABOUTOURLEARNINGPATHSOurplatformoffersfourspecializedlearningpathseachdesignedfordifferentcareergoalsandinterests:##1.Programming&SoftwareDevelopmentThispathisidealforstudentswhoenjoyproblem-solvinglogicalthinkingandbuildingdigitalsolutions.-Keycourses:IntroductiontoProgrammingWebDevelopmentFull-StackDevelopmentMobileAppDevelopment-Careeroutcomes:SoftwareDeveloperWebDeveloperMobileAppDeveloperAIEngineer-Bestsuitedfor:Analyticalthinkerswhoenjoycreatingsolutionsandworkingwithtechnology##2.Cybersecurity&InformationProtectionThispathisidealforstudentswhoaredetail-orientedanalyticalandinterestedinprotectingdigitalassets.-Keycourses:CybersecurityFundamentalsNetworkSecurityEthicalHackingSecurityOperations-Careeroutcomes:SecurityAnalystPenetrationTesterSecurityEngineerComplianceSpecialist-Bestsuitedfor:Detail-orientedproblemsolversinterestedindigitalsecurityandriskmanagement##3.Finance&InvestmentAnalysisThispathisidealforstudentswhoareanalyticaldetail-orientedandinterestedinfinancialmarkets.-Keycourses:FinancialFundamentalsInvestmentStrategiesFinancialModelingRiskManagement-Careeroutcomes:FinancialAnalystInvestmentAdvisorRiskManagerFinancialConsultant-Bestsuitedfor:Analyticalthinkerswhoenjoyworkingwithnumbersandeconomicconcepts##4.Marketing&BrandStrategyThispathisidealforstudentswhoarecreativecommunication-orientedandinterestedinconsumerbehavior.-Keycourses:MarketingFundamentalsDigitalMarketingContentMarketingMarketingAnalytics-Careeroutcomes:MarketingSpecialistDigitalMarketerContentStrategistBrandManager-Bestsuitedfor:Creativecommunicatorswhoenjoyunderstandingpeopleandbuildingcompellingmessages#RESPONSEGUIDELINES-Beconversationalandpersonable-usecontractionsvariedsentencelengthsandawarmtone-Avoidphraseslike"AsanAI"or"I'mheretohelp"-speakasahumaneducationaladvisor-Addresstheirspecificquestiondirectlybeforeprovidingadditionalrelevantinformation-Iftheyseemunsureaboutwhichpathtochooserecommendour"FindYourPerfectLearningPath"assessment-Include3-4thoughtfulfollow-upquestionsattheendtocontinuetheconversation-Formatyourfollow-upquestionsasalistattheendofyourresponsewiththeheading"SUGGESTED_QUESTIONS:"-Eachsuggestedquestionshouldbeonanewlineandstartwithadash(-)-Keepyourresponsefocusedandconcise(200-300words)"""#Preparesuggestedquestionsbasedonlanguageifis_arabic:suggested_questions=["كيفأعرفأيمسارمناسبلي؟""هليمكننيتغييرالمسارلاحقاً؟""ماهوالفرقبينالبرمجةوالأمنالسيبراني؟""أودإجراءتقييمالمسار"]fallback_content="نقدمأربعةمساراتتعليميةرئيسية:البرمجة،الأمنالسيبراني،التمويل،والتسويق.كلمسارمصمملمساعدتكعلىتطويرمهاراتلأهدافمهنيةمحددة.هلترغبفيمعرفةالمزيدعنمسارمعين،أوإجراءتقييمنالمعرفةالمسارالأنسبلك؟"#Fallbackcontentisusedinsteadofdefault_messagefallback_questions=["أخبرنيعنمسارالبرمجة""أخبرنيعنمسارالأمنالسيبراني""أخبرنيعنمسارالتمويل""أخبرنيعنمسارالتسويق"]else:suggested_questions=["HowdoIknowwhichpathisrightforme?""CanIswitchpathslater?""What'sthedifferencebetweenprogrammingandcybersecurity?""I'dliketotakethepathassessment"]fallback_content="Weofferfourmainlearningpaths:ProgrammingCybersecurityFinanceandMarketing.Eachpathisdesignedtohelpyoudevelopskillsforspecificcareergoals.Wouldyouliketolearnmoreaboutaspecificpathortakeourassessmenttofindyourbestfit?"#Fallbackcontentisusedinsteadofdefault_messagefallback_questions=["TellmeabouttheProgrammingpath""TellmeabouttheCybersecuritypath""TellmeabouttheFinancepath""TellmeabouttheMarketingpath"]try:#Generatetheresponsetry:#Generateacachekeybasedonthepromptandlanguagecache_key=self._generate_cache_key(promptis_arabic)cached_response=self._get_from_cache(cache_key)ifcached_response:logger.info(f"Cachehitforgeneralpathquery")contentdynamic_questions=cached_responseelse:logger.info(f"Cachemissforgeneralpathquery")try:#Addamorespecificprompttoencouragedetailedresponsesenhanced_prompt=f"""{prompt}IMPORTANT:Pleaseprovideadetailedcomprehensiveresponseaboutlearningpaths.Yourresponseshouldbeatleast200wordsandcoverallthekeyaspectsmentionedabove.Donotreturnanemptyresponse.MakesuretoincludeSUGGESTED_QUESTIONSattheendofyourresponse."""content=self.chat_service.generate_content(enhanced_prompt)#Ifcontentisemptyusefallbackcontentwithmoredetailedloggingifnotcontentor(isinstance(contentstr)andnotcontent.strip()):logger.warning(f"Emptyresponseforgeneralpathqueryusingfallbackcontent")logger.debug(f"Emptyresponsereceivedforprompt:{enhanced_prompt[:200]}...")#Tryonemoretimewithasimplerprompttry:simple_prompt="Providedetailedinformationaboutthelearningpathsavailableincludingprogrammingcybersecurityfinanceandmarketingpaths."content=self.chat_service.generate_content(simple_prompt)ifnotcontentor(isinstance(contentstr)andnotcontent.strip()):content=fallback_contentdynamic_questions=suggested_questionselse:#TrytoextractsuggestedquestionsfromtheAI'sresponsedynamic_questions=(self._extract_suggested_questions(content))ifnotdynamic_questions:dynamic_questions=suggested_questionsexceptException:content=fallback_contentdynamic_questions=suggested_questionselse:#TrytoextractsuggestedquestionsfromtheAI'sresponsedynamic_questions=self._extract_suggested_questions(content)#RemovetheSUGGESTED_QUESTIONSsectionfromthecontentif"SUGGESTED_QUESTIONS:"incontent:content=content.split("SUGGESTED_QUESTIONS:")[0].strip()ifnotdynamic_questions:dynamic_questions=suggested_questions#Cachethesuccessfulresponseself._add_to_cache(cache_key(contentdynamic_questions))#Logsuccessfulcontentgenerationlogger.info(f"Successfullygeneratedcontentforgeneralpathquerywithlength:{len(str(content))}")exceptExceptionasinner_error:logger.error(f"Errorgeneratingcontent:{str(inner_error)}")content=fallback_contentdynamic_questions=suggested_questionsexceptExceptionase:logger.error(f"Errorwithcachingmechanism:{str(e)}")#Fallbacktodirectgenerationwithoutcachingtry:#Addamorespecificprompttoencouragedetailedresponsesenhanced_prompt=f"""{prompt}IMPORTANT:Pleaseprovideadetailedcomprehensiveresponseaboutlearningpaths.Yourresponseshouldbeatleast200wordsandcoverallthekeyaspectsmentionedabove.Donotreturnanemptyresponse."""content=self.chat_service.generate_content(enhanced_prompt)ifnotcontentor(isinstance(contentstr)andnotcontent.strip()):logger.warning("Emptyresponseinfallbackgenerationusingfallbackcontent")content=fallback_contentdynamic_questions=suggested_questionselse:#Trytoextractsuggestedquestionsdynamic_questions=self._extract_suggested_questions(content)ifnotdynamic_questions:dynamic_questions=suggested_questions#Logsuccessfulcontentgenerationlogger.info(f"Successfullygeneratedcontentinfallbackmodewithlength:{len(str(content))}")exceptExceptionascontent_error:logger.error(f"Errorgeneratingcontent:{str(content_error)}")content=fallback_contentdynamic_questions=suggested_questions#Calculateresponsetimeresponse_time=time.time()-self.start_timeself._update_analytics(message"general_path_info"response_time)#Recordsuccessfulresponseforlearningself._record_successful_response(messagecontentcontext)return{"content":content"metadata":{"type":"general_path_info""response_time":response_time"response_id":hashlib.md5(f"{message}_{time.time()}".encode()).hexdigest()[:10]}"suggested_questions":dynamic_questions}exceptExceptionase:logger.error(f"Errorgeneratinggeneralpathinfo:{str(e)}")#Trackerrorresponse_time=time.time()-self.start_timeself._update_analytics(message"error"response_time)self.error_count+=1#Recordfailedresponseforlearningself._record_failed_response(messagestr(e)context)return{"content":fallback_content"metadata":{"type":"general_path_info""error":str(e)"response_time":response_time"response_id":hashlib.md5(f"{message}_{time.time()}".encode()).hexdigest()[:10]}"suggested_questions":fallback_questions}def_is_assessment_help_request(selfmessage:str)->bool:"""Checkifthemessageisaskingforhelpwiththeassessment"""#Englishkeywordsen_assessment_help_keywords=["howdoestheassessmentwork""assessmenthelp""explaintheassessment""assessmentquestions""retakeassessment""startassessment""beginassessment""takethequiz""startthequiz""assessmentprocess""howtotake""howmanyquestions"]#Arabickeywordsar_assessment_help_keywords=["كيفيعملالتقييم""مساعدةفيالتقييم""شرحالتقييم""أسئلةالتقييم""إعادةالتقييم""بدءالتقييم""بدءالتقييم""إجراءالاختبار""بدءالاختبار""عمليةالتقييم""كيفيةإجراء""كمعددالأسئلة"]message_lower=message.lower()returnany(keywordinmessage_lowerforkeywordinen_assessment_help_keywords)orany(keywordinmessageforkeywordinar_assessment_help_keywords)def_is_more_details_request(selfmessage:strcontext:Dict)->bool:"""Checkifthemessageisaskingformoredetailsabouttherecommendedpath"""#Gettherecommendedpathfromcontextpath=Noneifcontext.get("path_recommendation"):path=context["path_recommendation"]["path_recommendation"]elifcontext.get("learning_path"{}).get("current_path"):path=context["learning_path"]["current_path"]ifnotpath:returnFalse#Englishkeywordsen_more_details_keywords=["tellmemore""moreinformation""moredetails""whatwillilearn""whatcourses""curriculum""syllabus""prerequisites""requirements""howlong""duration""careeropportunities""jobprospects""skills"]#Arabickeywordsar_more_details_keywords=["أخبرنيالمزيد""مزيدمنالمعلومات""مزيدمنالتفاصيل""ماذاسأتعلم""ماهيالدورات""المنهج""المقرر""المتطلباتالمسبقة""المتطلبات""كمالمدة""المدة""فرصالعمل""آفاقالعمل""المهارات"]message_lower=message.lower()returnany(keywordinmessage_lowerforkeywordinen_more_details_keywords)orany(keywordinmessageforkeywordinar_more_details_keywords)def_extract_path_from_message(selfmessage:strcontext:Dict)->str:"""Extractwhichlearningpaththemessageisreferringto"""message_lower=message.lower()#CheckforexplicitmentionsofpathsinEnglishifany(wordinmessage_lowerforwordin["programming""coding""developer""software""code""development"]):return"programming"ifany(wordinmessage_lowerforwordin["cybersecurity""security""hacking""cyber""protection"]):return"cybersecurity"ifany(wordinmessage_lowerforwordin["finance""financial""accounting""investment""money""banking"]):return"finance"ifany(wordinmessage_lowerforwordin["marketing""market""advertising""brand""promotion"]):return"marketing"#CheckforexplicitmentionsofpathsinArabicifany(wordinmessageforwordin["برمجة""ترميز""مطور""برمجيات""كود""تطوير"]):return"programming"ifany(wordinmessageforwordin["أمنسيبراني""أمن""اختراق""سيبراني""حماية"]):return"cybersecurity"ifany(wordinmessageforwordin["تمويل""مالي""محاسبة""استثمار""مال""بنوك"]):return"finance"ifany(wordinmessageforwordin["تسويق""سوق""إعلان""علامةتجارية""ترويج"]):return"marketing"#Checkforreferencesto"thispath"or"mypath"inEnglishifany(phraseinmessage_lowerforphrasein["thispath""mypath""currentpath""recommendedpath"]):#Getpathfromcontextifcontext.get("path_recommendation"):returncontext["path_recommendation"]["path_recommendation"]ifcontext.get("learning_path"{}).get("current_path"):returncontext["learning_path"]["current_path"]#Checkforreferencesto"thispath"or"mypath"inArabicifany(phraseinmessageforphrasein["هذاالمسار""مساري""المسارالحالي""المسارالموصىبه"]):#Getpathfromcontextifcontext.get("path_recommendation"):returncontext["path_recommendation"]["path_recommendation"]ifcontext.get("learning_path"{}).get("current_path"):returncontext["learning_path"]["current_path"]returnNonedef_handle_assessment_help(self_:strcontext:Dict)->Dict:"""Handlerequestsforhelpwiththeassessment"""language=context.get("language""en")is_arabic=language=="ar"ifis_arabic:content="""#كيفيةعملتقييمالمسارالتعليميتقييمالمسارالتعليميهوأداةمصممةلمساعدتكفياكتشافالمسارالتعليميالأنسبلاهتماماتكومهاراتكوأهدافكالمهنية.##كيفيعملالتقييم:1.**ستةأسئلةسهلة**:يتكونالتقييممنستةأسئلةبسيطةحولاهتماماتكوتفضيلاتكوأهدافكالمهنية.2.**اختياراتمتعددة**:لكلسؤال،ستختارالإجابةالتيتناسبكأكثرمنبينأربعةخيارات.3.**تحليلذكي**:يقومنظامنابتحليلإجاباتكلتحديدالمسارالأنسبلكمنبينأربعةمسارات:البرمجة،الأمنالسيبراني،التمويل،والتسويق.4.**توصيةمخصصة**:ستحصلعلىتوصيةمخصصةتتضمننقاطقوتكوالمجالاتالتييمكنكتطويرها.##للبدءفيالتقييم:-انتقلإلىقسم"اكتشفمسارالتعلمالمثاليلك"فيأعلىالصفحة-اتبعالتعليماتواخترالإجابةالأنسبلكفيكلسؤال-بعدالانتهاءمنجميعالأسئلة،ستظهرلكالنتائجمعتوصيةمخصصةهلترغبفيبدءالتقييمالآن؟أوهللديكأسئلةأخرىحولكيفيةعملالتقييم؟"""suggested_questions=["مامدىدقةالتقييم؟""كممنالوقتيستغرقالتقييم؟""هليمكننيإعادةالتقييم؟"]else:content="""#HowtheLearningPathAssessmentWorksTheLearningPathAssessmentisatooldesignedtohelpyoudiscovertheeducationalpaththatbestmatchesyourinterestsskillsandcareergoals.##HowtheAssessmentWorks:1.**SixSimpleQuestions**:Theassessmentconsistsofsixstraightforwardquestionsaboutyourinterestspreferencesandcareergoals.2.**MultipleChoice**:Foreachquestionyou'llselecttheanswerthatresonatesmostwithyoufromfouroptions.3.**SmartAnalysis**:Oursystemanalyzesyouranswerstodeterminewhichpathisthebestfitforyouamongfourpaths:ProgrammingCybersecurityFinanceandMarketing.4.**PersonalizedRecommendation**:You'llreceiveapersonalizedrecommendationthatincludesyourstrengthsandareasyoumightwanttodevelop.##ToStarttheAssessment:-Gotothe"FindYourPerfectLearningPath"sectionatthetopofthepage-Followthepromptsandchoosetheanswerthatbestfitsyouforeachquestion-Aftercompletingallquestionsyou'llseeyourresultswithapersonalizedrecommendationWouldyouliketostarttheassessmentnow?Ordoyouhaveotherquestionsabouthowtheassessmentworks?"""suggested_questions=["Howaccurateistheassessment?""Howlongdoestheassessmenttake?""CanIretaketheassessment?"]return{"content":content"metadata":{"type":"assessment_help"}"suggested_questions":suggested_questions}def_handle_more_details_request(selfmessage:strcontext:Dict)->Dict:"""Handlerequestsformoredetailsaboutarecommendedpath"""#Getlanguagefromcontextlanguage=context.get("language""en")is_arabic=language=="ar"#Getthepathfromcontextpath=Noneifcontext.get("path_recommendation"):path=context["path_recommendation"]["path_recommendation"]elifcontext.get("learning_path"{}).get("current_path"):path=context["learning_path"]["current_path"]else:#Trytoextractpathfrommessagepath=self._extract_path_from_message(messagecontext)ifnotpath:#Defaulttoprogrammingifnopathfoundpath="programming"#Generatearesponseaboutthespecificpathifis_arabic:prompt=f"""المستخدميطلبمزيدًامنالتفاصيلحولمسار{path}بهذهالرسالة:"{message}"قدمإجابةمفصلةوشاملةعنمسار{path}.قمبتضمينمعلوماتحول:-المهاراتالرئيسيةالتييتمتطويرهافيهذاالمسار-الدوراتالنموذجيةفيهذاالمسار-الفرصالمهنية-المدةالمتوقعةلإكمالالمسار-المشاريعالعمليةالتيسيعملعليهاالطلابحافظعلىأسلوبمحادثةمشجعومفصل.أجبباللغةالعربية."""else:prompt=f"""Theuserisaskingformoredetailsaboutthe{path}learningpathwiththismessage:"{message}"Provideadetailedcomprehensiveresponseaboutthe{path}path.Includeinformationabout:-Keyskillsdevelopedinthispath-Typicalcoursesinthispath-Careeropportunities-Expecteddurationtocompletethepath-PracticalprojectsstudentswillworkonKeepyourresponseconversationalencouraginganddetailed."""#Preparefallbackmessagesbasedonlanguageifis_arabic:fallback_message=f"عذراً،لمأتمكنمنإنشاءتفاصيلعنمسار{path}.يرجىالمحاولةمرةأخرى."else:fallback_message=f"I'msorryIcouldn'tgeneratedetailsaboutthe{path}path.Pleasetryagain."try:#Generatetheresponsetry:#Addamorespecificprompttoencouragedetailedresponsesenhanced_prompt=f"""{prompt}IMPORTANT:Pleaseprovideadetailedcomprehensiveresponseaboutthe{path}path.Yourresponseshouldbeatleast200wordsandcoverallthekeyaspectsmentionedabove.Donotreturnanemptyresponse."""content=self.chat_service.generate_content(enhanced_prompt)#Ifcontentisemptyusefallbackwithmoredetailedloggingifnotcontentor(isinstance(contentstr)andnotcontent.strip()):logger.warning(f"Emptyresponsefordetailedpathinfoabout{path}usingfallback")logger.debug(f"Emptyresponsereceivedforprompt:{enhanced_prompt[:200]}...")returnself._get_fallback_path_details(pathis_arabic)#Logsuccessfulcontentgenerationlogger.info(f"Successfullygeneratedcontentfor{path}pathwithlength:{len(str(content))}")exceptExceptionascontent_error:logger.error(f"Errorgeneratingcontent:{str(content_error)}")return{"content":fallback_message"metadata":{"type":"path_details""path":path"error":str(content_error)}"suggested_questions":self._get_suggested_questions_for_path(pathis_arabic)}return{"content":content"metadata":{"type":"path_details""path":path}"suggested_questions":self._get_suggested_questions_for_path(pathis_arabic)}exceptExceptionase:logger.error(f"Errorgeneratingdetailedpathinfo:{str(e)}")return{"content":fallback_message"metadata":{"type":"path_details""path":path"error":str(e)}"suggested_questions":self._get_suggested_questions_for_path(pathis_arabic)}def_get_path_details(selfpath:stris_arabic:bool)->Dict:"""Getdetailedinformationaboutaspecificpath"""ifis_arabic:path_details={"programming":{"content":"""#مسارالبرمجة:تفاصيلشاملة##ماستتعلمه:-**أساسياتالبرمجة**:بايثون،جافاسكريبت،جافا،وغيرهامنلغاتالبرمجةالأساسية-**تطويرالويب**:HTML،CSS،إطاراتالعملالحديثةمثلReactوAngular-**تطويرالتطبيقات**:تطبيقاتالجوال،تطبيقاتسطحالمكتب،وتطبيقاتالويب-**قواعدالبيانات**:SQL،MongoDB،وإدارةالبيانات-**الذكاءالاصطناعيوالتعلمالآلي**:أساسياتالخوارزمياتوتطبيقاتها##المهاراتالتيستكتسبها:-حلالمشكلاتالمعقدةبطريقةمنهجية-تصميموتطويرالبرمجياتبكفاءة-العملضمنفرقتطويرمتعددةالتخصصات-إدارةالمشاريعالبرمجيةمنالبدايةإلىالنهاية-اختباروتحسينجودةالبرمجيات##الفرصالمهنية:-مطوربرمجيات(متوسطالراتب:85000$سنوياً)-مهندسواجهاتأمامية/خلفية(متوسطالراتب:90000$سنوياً)-مطورتطبيقاتالجوال(متوسطالراتب:88000$سنوياً)-مهندسDevOps(متوسطالراتب:110000$سنوياً)-مديرمشاريعتقنية(متوسطالراتب:120000$سنوياً)##المدةالمتوقعة:-**المستوىالمبتدئ**:6-12شهر-**المستوىالمتوسط**:1-2سنة-**المستوىالمتقدم**:2-3سنوات##المشاريعالعملية:ستعملعلىمشاريعحقيقيةمثلتطويرمواقعويب،تطبيقاتجوال،وأنظمةإدارةالمحتوى،ممايساعدكعلىبناءمحفظةأعمالقويةلعرضهاعلىأصحابالعملالمحتملين.""""suggested_questions":["ماهيلغاتالبرمجةالتيسأتعلمها؟""كيفيمكننيالبدءفيمسارالبرمجة؟""هلأحتاجإلىخبرةسابقةفيالبرمجة؟"]}"cybersecurity":{"content":"""#مسارالأمنالسيبراني:تفاصيلشاملة##ماستتعلمه:-**أساسياتالأمنالسيبراني**:مبادئالأمن،التشفير،والتحكمفيالوصول-**أمنالشبكات**:جدرانالحماية،أنظمةكشفالتسلل،وتقنياتVPN-**الاختبارالاختراقي**:أدواتواستراتيجياتاختبارالاختراقالأخلاقي-**تحليلالبرمجياتالخبيثة**:تقنياتاكتشافوتحليلالبرمجياتالضارة-**الاستجابةللحوادث**:استراتيجياتالتعاملمعخروقاتالأمنوتحليلها##المهاراتالتيستكتسبها:-تقييموإدارةالمخاطرالأمنية-تنفيذاستراتيجياتالدفاعالسيبراني-اكتشافنقاطالضعفوالثغراتالأمنية-التحقيقفيالحوادثالأمنيةوتحليلها-تطويرسياساتوإجراءاتالأمن##الفرصالمهنية:-محللأمنسيبراني(متوسطالراتب:90000$سنوياً)-مختبراختراقأخلاقي(متوسطالراتب:100000$سنوياً)-مهندسأمنالشبكات(متوسطالراتب:95000$سنوياً)-محققالطبالشرعيالرقمي(متوسطالراتب:92000$سنوياً)-مديرأمنالمعلومات(متوسطالراتب:130000$سنوياً)##المدةالمتوقعة:-**المستوىالمبتدئ**:6-12شهر-**المستوىالمتوسط**:1-2سنة-**المستوىالمتقدم**:2-3سنوات##الشهاداتالمهنية:ستتأهلللحصولعلىشهاداتمعترفبهاعالمياًمثلCompTIASecurity+،CEH(CertifiedEthicalHacker)،وCISSP(CertifiedInformationSystemsSecurityProfessional).""""suggested_questions":["هلسأتعلمالاختراقالأخلاقي؟""ماهيالشهاداتالتييمكننيالحصولعليها؟""هلمجالالأمنالسيبرانيمطلوبفيسوقالعمل؟"]}"finance":{"content":"""#مسارالتمويل:تفاصيلشاملة##ماستتعلمه:-**أساسياتالتمويل**:المحاسبة،التحليلالمالي،والاقتصاد-**الاستثمار**:استراتيجياتالاستثمار،تحليلالمحافظ،وإدارةالمخاطر-**التمويلالشخصي**:التخطيطالمالي،الميزانية،والادخارللتقاعد-**التمويلالمؤسسي**:تمويلالشركات،الاندماجوالاستحواذ،وتقييمالشركات-**الأسواقالمالية**:البورصات،السندات،والعملات،والمشتقاتالمالية##المهاراتالتيستكتسبها:-تحليلالبياناتالماليةواتخاذقراراتمستنيرة-إدارةالمحافظالاستثماريةوتقييمالمخاطر-تطويراستراتيجياتماليةللأفرادوالشركات-استخدامالبرمجياتالماليةوالأدواتالتحليلية-فهمالاتجاهاتالاقتصاديةوتأثيرهاعلىالأسواق##الفرصالمهنية:-محللمالي(متوسطالراتب:85000$سنوياً)-مستشاراستثمار(متوسطالراتب:90000$سنوياً)-محاسب(متوسطالراتب:75000$سنوياً)-مديرمحفظةاستثمارية(متوسطالراتب:120000$سنوياً)-مخططمالي(متوسطالراتب:88000$سنوياً)##المدةالمتوقعة:-**المستوىالمبتدئ**:6-12شهر-**المستوىالمتوسط**:1-2سنة-**المستوىالمتقدم**:2-3سنوات##الشهاداتالمهنية:ستتأهلللحصولعلىشهاداتمعترفبهامثلCFA(CertifiedFinancialAnalyst)،CFP(CertifiedFinancialPlanner)،وCPA(CertifiedPublicAccountant).""""suggested_questions":["هلسأتعلمعنالاستثمارفيالأسهم؟""هليغطيهذاالمسارالعملاتالرقمية؟""ماهيالبرامجالماليةالتيسأستخدمها؟"]}"marketing":{"content":"""#مسارالتسويق:تفاصيلشاملة##ماستتعلمه:-**أساسياتالتسويق**:استراتيجياتالتسويق،سلوكالمستهلك،وأبحاثالسوق-**التسويقالرقمي**:SEO،التسويقعبروسائلالتواصلالاجتماعي،والتسويقبالمحتوى-**الإعلان**:تصميمالحملاتالإعلانية،الإعلاناتالمدفوعة،وقياسالأداء-**العلاقاتالعامة**:بناءالعلامةالتجارية،إدارةالسمعة،والتواصلمعالعملاء-**تحليلاتالتسويق**:قياسفعاليةالحملاتالتسويقيةوتحسينها##المهاراتالتيستكتسبها:-تطويراستراتيجياتتسويقيةفعالة-إنشاءمحتوىجذابومؤثر-إدارةحملاتالتسويقالرقمي-تحليلبياناتالسوقواتجاهاتالمستهلكين-قياسوتحسينأداءالحملاتالتسويقية##الفرصالمهنية:-مديرتسويقرقمي(متوسطالراتب:80000$سنوياً)-أخصائيوسائلالتواصلالاجتماعي(متوسطالراتب:65000$سنوياً)-مديرعلامةتجارية(متوسطالراتب:95000$سنوياً)-محللتسويق(متوسطالراتب:70000$سنوياً)-مديرحملاتإعلانية(متوسطالراتب:85000$سنوياً)##المدةالمتوقعة:-**المستوىالمبتدئ**:6-12شهر-**المستوىالمتوسط**:1-2سنة-**المستوىالمتقدم**:2-3سنوات##المشاريعالعملية:ستعملعلىمشاريعحقيقيةمثلتطويرحملاتتسويقية،إدارةحساباتوسائلالتواصلالاجتماعي،وتحليلأداءالحملاتالإعلانيةلبناءمحفظةأعمالقوية.""""suggested_questions":["هليشملالمسارالتسويقعبروسائلالتواصلالاجتماعي؟""هلسأتعلمعنتحسينمحركاتالبحث(SEO)؟""هلنعملمععلاماتتجاريةحقيقية؟"]}}else:path_details={"programming":{"content":"""#ProgrammingPath:ComprehensiveDetails##WhatYou'llLearn:-**ProgrammingFundamentals**:PythonJavaScriptJavaandothercorelanguages-**WebDevelopment**:HTMLCSSandmodernframeworkslikeReactandAngular-**ApplicationDevelopment**:Mobileappsdesktopapplicationsandwebapplications-**Databases**:SQLMongoDBanddatamanagement-**AI&MachineLearning**:Algorithmfundamentalsandapplications##SkillsYou'llDevelop:-Solvingcomplexproblemssystematically-Designinganddevelopingsoftwareefficiently-Workingincross-functionaldevelopmentteams-Managingprogrammingprojectsfromstarttofinish-Testingandimprovingsoftwarequality##CareerOpportunities:-SoftwareDeveloper(Averagesalary:$85000/year)-Front-end/Back-endEngineer(Averagesalary:$90000/year)-MobileAppDeveloper(Averagesalary:$88000/year)-DevOpsEngineer(Averagesalary:$110000/year)-TechnicalProjectManager(Averagesalary:$120000/year)##ExpectedDuration:-**BeginnerLevel**:6-12months-**IntermediateLevel**:1-2years-**AdvancedLevel**:2-3years##PracticalProjects:You'llworkonreal-worldprojectslikedevelopingwebsitesmobileapplicationsandcontentmanagementsystemshelpingyoubuildastrongportfoliotoshowcasetopotentialemployers.""""suggested_questions":["WhatprogramminglanguageswillIlearn?""HowdoIgetstartedwiththeprogrammingpath?""DoIneedpriorcodingexperience?"]}"cybersecurity":{"content":"""#CybersecurityPath:ComprehensiveDetails##WhatYou'llLearn:-**CybersecurityFundamentals**:Securityprinciplescryptographyandaccesscontrol-**NetworkSecurity**:FirewallsintrusiondetectionsystemsandVPNtechnologies-**PenetrationTesting**:Ethicalhackingtoolsandstrategies-**MalwareAnalysis**:Techniquesfordetectingandanalyzingmalicioussoftware-**IncidentResponse**:Strategiesforhandlingandanalyzingsecuritybreaches##SkillsYou'llDevelop:-Assessingandmanagingsecurityrisks-Implementingcyberdefensestrategies-Discoveringvulnerabilitiesandsecurityflaws-Investigatingandanalyzingsecurityincidents-Developingsecuritypoliciesandprocedures##CareerOpportunities:-CybersecurityAnalyst(Averagesalary:$90000/year)-EthicalHacker/PenetrationTester(Averagesalary:$100000/year)-NetworkSecurityEngineer(Averagesalary:$95000/year)-DigitalForensicsInvestigator(Averagesalary:$92000/year)-InformationSecurityManager(Averagesalary:$130000/year)##ExpectedDuration:-**BeginnerLevel**:6-12months-**IntermediateLevel**:1-2years-**AdvancedLevel**:2-3years##ProfessionalCertifications:You'llbepreparedforgloballyrecognizedcertificationssuchasCompTIASecurity+CEH(CertifiedEthicalHacker)andCISSP(CertifiedInformationSystemsSecurityProfessional).""""suggested_questions":["WillIlearnethicalhacking?""WhatcertificationscanIearn?""Iscybersecurityinhighdemand?"]}"finance":{"content":"""#FinancePath:ComprehensiveDetails##WhatYou'llLearn:-**FinanceFundamentals**:Accountingfinancialanalysisandeconomics-**Investment**:Investmentstrategiesportfolioanalysisandriskmanagement-**PersonalFinance**:Financialplanningbudgetingandretirementsavings-**CorporateFinance**:Corporatefundingmergersandacquisitionsandcompanyvaluation-**FinancialMarkets**:Stocksbondscurrenciesandderivatives##SkillsYou'llDevelop:-Analyzingfinancialdataandmakinginformeddecisions-Managinginvestmentportfoliosandassessingrisks-Developingfinancialstrategiesforindividualsandcompanies-Usingfinancialsoftwareandanalyticaltools-Understandingeconomictrendsandtheirimpactonmarkets##CareerOpportunities:-FinancialAnalyst(Averagesalary:$85000/year)-InvestmentAdvisor(Averagesalary:$90000/year)-Accountant(Averagesalary:$75000/year)-PortfolioManager(Averagesalary:$120000/year)-FinancialPlanner(Averagesalary:$88000/year)##ExpectedDuration:-**BeginnerLevel**:6-12months-**IntermediateLevel**:1-2years-**AdvancedLevel**:2-3years##ProfessionalCertifications:You'llbepreparedforrecognizedcertificationssuchasCFA(CertifiedFinancialAnalyst)CFP(CertifiedFinancialPlanner)andCPA(CertifiedPublicAccountant).""""suggested_questions":["WillIlearnaboutstockinvesting?""Doesthispathcovercryptocurrency?""WhatfinancialsoftwarewillIuse?"]}"marketing":{"content":"""#MarketingPath:ComprehensiveDetails##WhatYou'llLearn:-**MarketingFundamentals**:Marketingstrategiesconsumerbehaviorandmarketresearch-**DigitalMarketing**:SEOsocialmediamarketingandcontentmarketing-**Advertising**:Campaigndesignpaidadvertisingandperformancemeasurement-**PublicRelations**:Brandbuildingreputationmanagementandcustomercommunication-**MarketingAnalytics**:Measuringandoptimizingmarketingcampaigneffectiveness##SkillsYou'llDevelop:-Developingeffectivemarketingstrategies-Creatingengagingandimpactfulcontent-Managingdigitalmarketingcampaigns-Analyzingmarketdataandconsumertrends-Measuringandimprovingmarketingcampaignperformance##CareerOpportunities:-DigitalMarketingManager(Averagesalary:$80000/year)-SocialMediaSpecialist(Averagesalary:$65000/year)-BrandManager(Averagesalary:$95000/year)-MarketingAnalyst(Averagesalary:$70000/year)-CampaignManager(Averagesalary:$85000/year)##ExpectedDuration:-**BeginnerLevel**:6-12months-**IntermediateLevel**:1-2years-**AdvancedLevel**:2-3years##PracticalProjects:You'llworkonreal-worldprojectslikedevelopingmarketingcampaignsmanagingsocialmediaaccountsandanalyzingadvertisingperformancetobuildastrongportfolio.""""suggested_questions":["Doesthepathcoversocialmediamarketing?""WillIlearnaboutSEO?""Doweworkwithrealbrands?"]}}#Returndetailsfortherequestedpathorprogrammingasdefaultreturnpath_details.get(pathpath_details["programming"])def_format_recommendation_as_chat(selfrecommendation:Dictlanguage:str="en")->str:"""Formatapathrecommendationasaconversationalchatmessage"""path=recommendation["path_recommendation"]#confidencevalueisnotused#confidence=recommendation["confidence"]strengths=recommendation["strengths"]areas_to_develop=recommendation["areas_to_develop"]advice=recommendation["personalized_advice"]alternative=recommendation["alternative_path"]#Createaconversationalresponsebasedonlanguageiflanguage=="ar":response=f"""بناءًعلىإجاباتك،أعتقدأنمسار**{path}**سيكونمناسبًاتمامًالك!نقاطقوتكفي{''.join(strengths[:2])}تتوافقتمامًامعماهومطلوبللنجاحفيهذاالمجال.لتحقيقأقصىاستفادةمنهذاالمسار،قدترغبفيالتركيزعلىتطوير{''.join(areas_to_develop[:2])}.{advice}إذاكنتمهتمًاأيضًاباستكشافالبدائل،فقديكونمسار**{alternative}**جديرًابالاعتبارأيضًا.هلترغبفيمعرفةالمزيدعنمسار{path}والدوراتالتييمكنكالبدءبها؟"""else:response=f"""BasedonyourresponsesIthinkthe**{path}**pathwouldbeanexcellentfitforyou!Yourstrengthsin{''.join(strengths[:2])}alignperfectlywithwhat'sneededtosucceedinthisarea.Tomakethemostofthispathyoumightwanttofocusondevelopingyour{''.join(areas_to_develop[:2])}.{advice}Ifyou'realsointerestedinexploringalternativesthe**{alternative}**pathcouldbeworthconsideringaswell.Wouldyouliketolearnmoreaboutthe{path}pathandwhatcoursesyoumightstartwith?"""returnresponse.strip()def_get_suggested_questions_for_path(selfpath:stris_arabic:bool=False)->List[str]:"""Getsuggestedfollow-upquestionsforaspecificpath"""ifis_arabic:common_questions=[f"ماهيالدوراتالموجودةفيمسار{path}؟"f"ماهيالوظائفالتييمكننيالحصولعليهابمهارات{path}؟""كميستغرقإكمالهذاالمسار؟"]path_specific_questions={"programming":["ماهيلغاتالبرمجةالتيسأتعلمها؟""هلأحتاجإلىخبرةسابقةفيالبرمجة؟""ماهيالمشاريعالتيسأقومببنائها؟"]"cybersecurity":["هلسأتعلمالاختراقالأخلاقي؟""ماهيالشهاداتالتييعدنيهذاالمسارلها؟""هلالأمنالسيبرانيمطلوببشدة؟"]"finance":["هلسأتعلمعناستراتيجياتالاستثمار؟""هليغطيهذاالمسارالعملاتالرقمية؟""ماهيالبرامجالماليةالتيسأستخدمها؟"]"marketing":["هليشملهذاالمسارالتسويقعبروسائلالتواصلالاجتماعي؟""هلسأتعلمعنتحسينمحركاتالبحث(SEO)؟""هلنعملمععلاماتتجاريةحقيقية؟"]}else:common_questions=[f"Whatcoursesareinthe{path}path?"f"WhatjobscanIgetwith{path}skills?""Howlongdoesthispathtaketocomplete?"]path_specific_questions={"programming":["WhatprogramminglanguageswillIlearn?""DoIneedpriorcodingexperience?""WhatprojectswillIbuild?"]"cybersecurity":["WillIlearnethicalhacking?""Whatcertificationsdoesthispathpreparemefor?""Iscybersecurityinhighdemand?"]"finance":["WillIlearnaboutinvestmentstrategies?""Doesthispathcovercryptocurrency?""WhatfinancialsoftwarewillIuse?"]"marketing":["Doesthispathcoversocialmediamarketing?""WillIlearnaboutSEO?""Doweworkwithrealbrands?"]}#Combinecommonquestionswithpath-specificonesifpathinpath_specific_questions:returnpath_specific_questions[path][:2]+common_questions[:1]returncommon_questionsdef_extract_suggested_questions(selfcontent:str)->List[str]:"""ExtractsuggestedquestionsfromtheAI'sresponse"""try:#LookfortheSUGGESTED_QUESTIONSsectionintheresponseif"SUGGESTED_QUESTIONS:"incontent:#SplitthecontentattheSUGGESTED_QUESTIONSmarkerquestions_section=content.split("SUGGESTED_QUESTIONS:")[1].strip()#Extractquestions(linesstartingwith-)questions=[]forlineinquestions_section.split("\n"):line=line.strip()ifline.startswith("-"):#Removethedashandtrimwhitespacequestion=line[1:].strip()ifquestion:#Onlyaddnon-emptyquestionsquestions.append(question)#Ifwefoundatleastonequestionreturnthelistifquestions:#Limitto4questionsmaximumreturnquestions[:4]#Ifwecouldn'tfindquestionsortheformatwasincorrectreturnNonereturnNoneexceptExceptionase:logger.error(f"Errorextractingsuggestedquestions:{str(e)}")returnNonedef_generate_cache_key(selfprompt:stris_arabic:bool)->str:"""Generateacachekeybasedonthepromptandlanguage"""#Usetheunifiedcachekeygenerationfunctionreturngenerate_cache_key("path_advisor"prompt=promptis_arabic=is_arabic)def_get_from_cache(selfkey:str)->Optional[Tuple[strList[str]]]:"""Getaresponsefromthecacheifitexistsandisnotexpired"""ifkeyinself.response_cache:timestampvalue=self.response_cache[key]#Checkifthecacheentryisstillvalidiftime.time()-timestamp<self.cache_ttl:self.cache_hits+=1returnvalue#Ifexpiredremoveitfromthecachedelself.response_cache[key]self.cache_misses+=1returnNonedef_add_to_cache(selfkey:strvalue:Tuple[strList[str]])->None:"""Addaresponsetothecachewiththecurrenttimestamp"""self.response_cache[key]=(time.time()value)#Simplecachesizemanagement-ifcachegetstoolargeremoveoldestentriesiflen(self.response_cache)>100:#Limitcacheto100entries#Sortbytimestampandkeepthenewest80entriessorted_cache=sorted(self.response_cache.items()key=lambdax:x[1][0])self.response_cache=dict(sorted_cache[-80:])def_update_analytics(selfmessage:strquery_type:strresponse_time:float)->None:"""Updateanalyticsdataforthechatbot"""#Updateaverageresponsetimeself.total_response_time+=response_timeself.avg_response_time=self.total_response_time/self.interaction_count#Trackpath-specificqueriespath=self._extract_path_from_message(message{})ifpath:ifpathnotinself.path_specific_queries:self.path_specific_queries[path]=0self.path_specific_queries[path]+=1#Addtoqueryhistory(limittolast100queries)timestamp=datetime.now().isoformat()self.query_history.append({"timestamp":timestamp"message":message[:100]#Truncatelongmessages"type":query_type"response_time":response_time"path":path})#Keeponlythelast100queriesiflen(self.query_history)>100:self.query_history=self.query_history[-100:]defget_analytics(self)->Dict[strAny]:"""Getanalyticsdataforthechatbot"""return{"interaction_count":self.interaction_count"error_count":self.error_count"error_rate":((self.error_count/self.interaction_count)ifself.interaction_count>0else0)"avg_response_time":self.avg_response_time"path_specific_queries":self.path_specific_queries"cache_stats":self.get_cache_stats()"recent_queries":self.query_history[-10:]#Last10queries"feedback":{"positive_rate":(self.feedback_data["positive"]/(self.feedback_data["positive"]+self.feedback_data["negative"]+self.feedback_data["neutral"])if(self.feedback_data["positive"]+self.feedback_data["negative"]+self.feedback_data["neutral"])>0else0)"counts":{"positive":self.feedback_data["positive"]"negative":self.feedback_data["negative"]"neutral":self.feedback_data["neutral"]}"recent_feedback":self.feedback_data["detailed"][-5:]#Last5detailedfeedbackitems}"personalization":{"active_users":len(self.user_preferences)"popular_paths":self._get_popular_paths()"demographic_insights":self._get_demographic_insights()}}defupdate_user_preference(selfuser_id:strpath:strliked:bool=True)->None:"""Updateauser'spathpreferencesbasedontheirinteractionsArgs:user_id:Theuser'sIDpath:Thelearningpath(programmingcybersecurityetc.)liked:Whethertheuserlikedthepath(True)ordislikedit(False)"""ifuser_id=="anonymous":returnifuser_idnotinself.user_preferences:return#Updatepreferredpathspreferred_paths=self.user_preferences[user_id].get("preferred_paths"[])#Addtopreferredpathsiflikedandnotalreadythereiflikedandpathnotinpreferred_paths:preferred_paths.append(path)self.user_preferences[user_id]["preferred_paths"]=preferred_paths#Updatepopularpathscounterdemographic=self.user_preferences[user_id].get("demographic""unknown")ifdemographicnotinself.popular_paths:self.popular_paths[demographic]={}ifpathnotinself.popular_paths[demographic]:self.popular_paths[demographic][path]=0self.popular_paths[demographic][path]+=1#Removefrompreferredpathsifdislikedandpresentelifnotlikedandpathinpreferred_paths:preferred_paths.remove(path)self.user_preferences[user_id]["preferred_paths"]=preferred_pathsdef_get_popular_paths(self)->Dict[strstr]:"""Getthemostpopularpathoverall"""all_paths={}#Countallpathsacrossdemographicsfor_pathsinself.popular_paths.items():forpathcountinpaths.items():ifpathnotinall_paths:all_paths[path]=0all_paths[path]+=count#Findthemostpopularpathmost_popular=Nonehighest_count=0forpathcountinall_paths.items():ifcount>highest_count:most_popular=pathhighest_count=countreturn{"most_popular":most_popular"count":highest_count"all_paths":all_paths}def_get_demographic_insights(self)->Dict[strAny]:"""Getinsightsaboutpathpreferencesbydemographic"""insights={}fordemographicpathsinself.popular_paths.items():ifnotpaths:continue#Findthemostpopularpathforthisdemographicmost_popular=Nonehighest_count=0forpathcountinpaths.items():ifcount>highest_count:most_popular=pathhighest_count=countinsights[demographic]={"most_popular_path":most_popular"count":highest_count"all_paths":paths}returninsightsdefexport_data(selffile_path:str)->bool:"""ExportchatbotdatatoaJSONfileforpersistenceArgs:file_path:PathtosavethedatafileReturns:TrueifsuccessfulFalseotherwise"""try:data={"timestamp":datetime.now().isoformat()"analytics":{"interaction_count":self.interaction_count"error_count":self.error_count"avg_response_time":self.avg_response_time"path_specific_queries":self.path_specific_queries}"feedback":self.feedback_data"user_preferences":self.user_preferences"popular_paths":self.popular_paths"learning_data":self.learning_data}withopen(file_path"w"encoding="utf-8")asf:json.dump(datafindent=2ensure_ascii=False)logger.info(f"Successfullyexportedchatbotdatato{file_path}")returnTrueexceptExceptionase:logger.error(f"Errorexportingchatbotdata:{str(e)}")returnFalsedefimport_data(selffile_path:str)->bool:"""ImportchatbotdatafromaJSONfileArgs:file_path:PathtothedatafileReturns:TrueifsuccessfulFalseotherwise"""try:ifnotos.path.exists(file_path):logger.error(f"Datafilenotfound:{file_path}")returnFalsewithopen(file_path"r"encoding="utf-8")asf:data=json.load(f)#Importanalyticsdataanalytics=data.get("analytics"{})self.interaction_count=analytics.get("interaction_count"self.interaction_count)self.error_count=analytics.get("error_count"self.error_count)self.avg_response_time=analytics.get("avg_response_time"self.avg_response_time)self.path_specific_queries=analytics.get("path_specific_queries"self.path_specific_queries)#Importfeedbackdataself.feedback_data=data.get("feedback"self.feedback_data)#Importuserpreferencesandpopularpathsself.user_preferences=data.get("user_preferences"self.user_preferences)self.popular_paths=data.get("popular_paths"self.popular_paths)#Importlearningdataself.learning_data=data.get("learning_data"self.learning_data)logger.info(f"Successfullyimportedchatbotdatafrom{file_path}")returnTrueexceptExceptionase:logger.error(f"Errorimportingchatbotdata:{str(e)}")returnFalsedef_record_successful_response(selfmessage:strresponse:strcontext:Dict)->None:"""Recordasuccessfulresponseforcontinuouslearning"""#Keeponlythelast100successfulresponsesiflen(self.learning_data["successful_responses"])>=100:self.learning_data["successful_responses"]=self.learning_data["successful_responses"][1:]#Addthenewsuccessfulresponseself.learning_data["successful_responses"].append({"timestamp":datetime.now().isoformat()"message":message"response":response"language":context.get("language""en")"path":self._extract_path_from_message(messagecontext)})def_record_failed_response(selfmessage:strerror:strcontext:Dict)->None:"""Recordafailedresponseforcontinuouslearning"""#Keeponlythelast100failedresponsesiflen(self.learning_data["failed_responses"])>=100:self.learning_data["failed_responses"]=self.learning_data["failed_responses"][1:]#Addthenewfailedresponseself.learning_data["failed_responses"].append({"timestamp":datetime.now().isoformat()"message":message"error":error"language":context.get("language""en")"path":self._extract_path_from_message(messagecontext)})defadd_improvement_suggestion(selfmessage_id:stroriginal_response:strimproved_response:strreason:str)->None:"""AddasuggestionforimprovingaresponseArgs:message_id:IDofthemessagethatwasrespondedtooriginal_response:Theoriginalresponsethatwasgivenimproved_response:Asuggestedimprovedresponsereason:Reasonfortheimprovement"""#Keeponlythelast100improvementsuggestionsiflen(self.learning_data["improvement_suggestions"])>=100:self.learning_data["improvement_suggestions"]=self.learning_data["improvement_suggestions"][1:]#Addthenewimprovementsuggestionself.learning_data["improvement_suggestions"].append({"timestamp":datetime.now().isoformat()"message_id":message_id"original_response":original_response"improved_response":improved_response"reason":reason})logger.info(f"Addedimprovementsuggestionformessage{message_id}")defget_learning_insights(self)->Dict[strAny]:"""Getinsightsfromthelearningdata"""#Countcommontopicsinfailedresponsesfailed_paths={}forresponseinself.learning_data["failed_responses"]:path=response.get("path")ifpath:ifpathnotinfailed_paths:failed_paths[path]=0failed_paths[path]+=1#Findthemostproblematicpathmost_problematic=Nonehighest_failure_count=0forpathcountinfailed_paths.items():ifcount>highest_failure_count:most_problematic=pathhighest_failure_count=count#Countimprovementsuggestionsbytypeimprovement_types={}forsuggestioninself.learning_data["improvement_suggestions"]:reason=suggestion.get("reason""unknown")ifreasonnotinimprovement_types:improvement_types[reason]=0improvement_types[reason]+=1return{"successful_responses_count":len(self.learning_data["successful_responses"])"failed_responses_count":len(self.learning_data["failed_responses"])"improvement_suggestions_count":len(self.learning_data["improvement_suggestions"])"most_problematic_path":most_problematic"improvement_types":improvement_types"recent_failures":(self.learning_data["failed_responses"][-5:]ifself.learning_data["failed_responses"]else[])"recent_improvements":(self.learning_data["improvement_suggestions"][-5:]ifself.learning_data["improvement_suggestions"]else[])}defrecord_feedback(selffeedback_type:strmessage_id:strdetails:Optional[str]=None)->Dict[strAny]:"""RecorduserfeedbackaboutachatbotresponseArgs:feedback_type:'positive''negative'or'neutral'message_id:IDofthemessagebeingrateddetails:OptionaldetailedfeedbacktextReturns:Dictwithfeedbackstatistics"""#Validatefeedbacktypeiffeedback_typenotin["positive""negative""neutral"]:feedback_type="neutral"#Incrementthecounterforthisfeedbacktypeself.feedback_data[feedback_type]+=1#Recorddetailedfeedbackifprovidedifdetails:self.feedback_data["detailed"].append({"timestamp":datetime.now().isoformat()"message_id":message_id"type":feedback_type"details":details})#Keeponlythelast100detailedfeedbackitemsiflen(self.feedback_data["detailed"])>100:self.feedback_data["detailed"]=self.feedback_data["detailed"][-100:]#Returncurrentfeedbackstatstotal_feedback=(self.feedback_data["positive"]+self.feedback_data["negative"]+self.feedback_data["neutral"])return{"total_feedback":total_feedback"positive_rate":(self.feedback_data["positive"]/total_feedbackiftotal_feedback>0else0)"counts":{"positive":self.feedback_data["positive"]"negative":self.feedback_data["negative"]"neutral":self.feedback_data["neutral"]}}defget_cache_stats(self)->Dict:"""Getstatisticsaboutthecacheusage"""return{"cache_size":len(self.response_cache)"cache_hits":self.cache_hits"cache_misses":self.cache_misses"hit_ratio":(self.cache_hits/(self.cache_hits+self.cache_misses)if(self.cache_hits+self.cache_misses)>0else0)"oldest_entry":(min(self.response_cache.values()key=lambdax:x[0])[0]ifself.response_cacheelseNone)"newest_entry":(max(self.response_cache.values()key=lambdax:x[0])[0]ifself.response_cacheelseNone)}def_get_fallback_path_details(selfpath:stris_arabic:bool=False)->Dict:"""GetfallbackdetailsaboutapathifAIgenerationfails"""ifis_arabic:path_details={"programming":{"content":"""#مسارالبرمجةوتطويرالبرمجياتمسارالبرمجةوتطويرالبرمجياتهومسارتعليميشامليركزعلىتطويرالمهاراتاللازمةلبناءالبرمجياتوالتطبيقاتالرقمية.##المهاراتالرئيسية-أساسياتالبرمجةباستخداملغاتمثلPythonوJavaScript-تطويرتطبيقاتالويبباستخدامHTMLوCSSوJavaScript-تطويرتطبيقاتالجواللأنظمةAndroidوiOS-قواعدالبياناتوإدارةالبيانات-مبادئهندسةالبرمجياتوأفضلالممارسات##الدوراتالرئيسية-مقدمةفيالبرمجة:تعلمأساسياتالبرمجةباستخدامPython-تطويرالويب:إنشاءمواقعويبتفاعلية-تطويرتطبيقاتالجوال:بناءتطبيقاتللهواتفالذكية-قواعدالبيانات:تخزينواسترجاعالبياناتبكفاءة##الفرصالمهنية-مطوربرمجيات-مطورويب-مطورتطبيقاتجوال-مهندسبرمجيات-مطورواجهاتأماميةوخلفيةهذاالمسارمثاليللأشخاصالذينيستمتعونبحلالمشكلاتوالتفكيرالمنطقيوبناءالحلولالرقمية.""""suggested_questions":["ماهيلغاتالبرمجةالتيسأتعلمها؟""ماهيالمشاريعالتيسأقومببنائها؟""هلأحتاجإلىخبرةسابقةفيالبرمجة؟"]}"cybersecurity":{"content":"""#مسارالأمنالسيبرانيوحمايةالمعلوماتمسارالأمنالسيبرانيهومسارتعليميمتخصصيركزعلىحمايةالأنظمةوالشبكاتوالبياناتمنالتهديداتالرقمية.##المهاراتالرئيسية-أساسياتالأمنالسيبرانيوالتشفير-أمنالشبكاتوكشفالتسلل-الاختراقالأخلاقيواختبارالاختراق-الاستجابةللحوادثوتحليلالبرمجياتالخبيثة-الامتثالللوائحالأمنوإدارةالمخاطر##الدوراتالرئيسية-أساسياتالأمنالسيبراني:فهمالتهديداتوالثغراتالأمنية-أمنالشبكات:حمايةالبنيةالتحتيةللشبكات-الاختراقالأخلاقي:اكتشافنقاطالضعفقبلالمهاجمين-عملياتالأمن:مراقبةوإدارةالأمنبشكلمستمر##الفرصالمهنية-محللأمنسيبراني-مختبراختراق-مهندسأمن-مديراستجابةللحوادث-مستشارأمنالمعلوماتهذاالمسارمثاليللأشخاصالمهتمينبالتفاصيلوالتحليليينوالمهتمينبحمايةالأصولالرقمية.""""suggested_questions":["هلسأتعلمالاختراقالأخلاقي؟""ماهيالشهاداتالتييعدنيهذاالمسارلها؟""هلالأمنالسيبرانيمطلوببشدة؟"]}"finance":{"content":"""#مسارالتمويلوتحليلالاستثمارمسارالتمويلهومسارتعليميمتخصصيركزعلىفهمالأسواقالماليةوتحليلالاستثماراتوإدارةالمخاطر.##المهاراتالرئيسية-تحليلالبياناتالماليةواتخاذالقرارات-فهمالأسواقالماليةوالاستثمارات-إدارةالمخاطروالتخطيطالمالي-النمذجةالماليةوالتنبؤ-استراتيجياتالاستثماروالتحليل##الدوراتالرئيسية-أساسياتالتمويل:فهمالمفاهيمالماليةالأساسية-استراتيجياتالاستثمار:تطويراستراتيجياتاستثمارفعالة-النمذجةالمالية:بناءنماذجللتحليلوالتنبؤ-إدارةالمخاطر:تحديدوتخفيفالمخاطرالمالية##الفرصالمهنية-محللمالي-مستشاراستثمار-مديرمخاطر-مستشارمالي-محللأعمالهذاالمسارمثاليللأشخاصالتحليليينوالمهتمينبالتفاصيلوالمهتمينبالأسواقالمالية.""""suggested_questions":["هلسأتعلمعناستراتيجياتالاستثمار؟""هليغطيهذاالمسارالعملاتالرقمية؟""ماهيالبرامجالماليةالتيسأستخدمها؟"]}"marketing":{"content":"""#مسارالتسويقواستراتيجيةالعلاماتالتجاريةمسارالتسويقهومسارتعليميإبداعييركزعلىفهمسلوكالمستهلكوتطويراستراتيجياتتسويقيةفعالة.##المهاراتالرئيسية-فهمسلوكالمستهلكوتحليلالسوق-التسويقالرقميووسائلالتواصلالاجتماعي-تسويقالمحتوىوسردالقصص-تحليلاتالتسويقوقياسالأداء-استراتيجيةالعلامةالتجاريةوإدارةالهوية##الدوراتالرئيسية-أساسياتالتسويق:فهممبادئالتسويقالأساسية-التسويقالرقمي:إنشاءوتحسينالحملاتالرقمية-تسويقالمحتوى:تطويراستراتيجياتمحتوىمقنعة-تحليلاتالتسويق:قياسوتحسينأداءالتسويق##الفرصالمهنية-متخصصتسويق-مسوقرقمي-استراتيجيمحتوى-مديرعلامةتجارية-محللتسويقهذاالمسارمثاليللأشخاصالمبدعينوالموجهيننحوالتواصلوالمهتمينبسلوكالمستهلك.""""suggested_questions":["هليشملهذاالمسارالتسويقعبروسائلالتواصلالاجتماعي؟""هلسأتعلمعنتحسينمحركاتالبحث(SEO)؟""هلنعملمععلاماتتجاريةحقيقية؟"]}}#GenericfallbackinArabicgeneric_content="نقدمأربعةمساراتتعليميةرئيسية:البرمجة،الأمنالسيبراني،التمويل،والتسويق.كلمسارمصمملمساعدتكعلىتطويرمهاراتلأهدافمهنيةمحددة.هلترغبفيمعرفةالمزيدعنمسارمعين؟"generic_questions=["أخبرنيعنمسارالبرمجة""أخبرنيعنمسارالأمنالسيبراني""أخبرنيعنمسارالتمويل""أخبرنيعنمسارالتسويق"]else:path_details={"programming":{"content":"""#Programming&SoftwareDevelopmentPathTheProgramming&SoftwareDevelopmentpathisacomprehensivelearningtrackfocusedondevelopingtheskillsneededtobuildsoftwareanddigitalapplications.##KeySkills-ProgrammingfundamentalsusinglanguageslikePythonandJavaScript-WebapplicationdevelopmentusingHTMLCSSandJavaScript-MobileappdevelopmentforAndroidandiOSplatforms-Databasesanddatamanagement-Softwareengineeringprinciplesandbestpractices##CoreCourses-IntroductiontoProgramming:LearnprogrammingbasicsusingPython-WebDevelopment:Createinteractivewebsites-MobileAppDevelopment:Buildapplicationsforsmartphones-Databases:Storeandretrievedataefficiently##CareerOpportunities-SoftwareDeveloper-WebDeveloper-MobileAppDeveloper-SoftwareEngineer-Full-StackDeveloperThispathisidealforpeoplewhoenjoyproblem-solvinglogicalthinkingandbuildingdigitalsolutions.""""suggested_questions":["WhatprogramminglanguageswillIlearn?""WhatprojectswillIbuild?""DoIneedpriorcodingexperience?"]}"cybersecurity":{"content":"""#Cybersecurity&InformationProtectionPathTheCybersecuritypathisaspecializedlearningtrackfocusedonprotectingsystemsnetworksanddatafromdigitalthreats.##KeySkills-Cybersecurityfundamentalsandcryptography-Networksecurityandintrusiondetection-Ethicalhackingandpenetrationtesting-Incidentresponseandmalwareanalysis-Securitycomplianceandriskmanagement##CoreCourses-CybersecurityFundamentals:Understandingthreatsandvulnerabilities-NetworkSecurity:Protectingnetworkinfrastructure-EthicalHacking:Findingvulnerabilitiesbeforeattackersdo-SecurityOperations:Monitoringandmanagingsecuritycontinuously##CareerOpportunities-CybersecurityAnalyst-PenetrationTester-SecurityEngineer-IncidentResponseManager-InformationSecurityConsultantThispathisidealfordetail-orientedanalyticalpeopleinterestedinprotectingdigitalassets.""""suggested_questions":["WillIlearnethicalhacking?""Whatcertificationsdoesthispathpreparemefor?""Iscybersecurityinhighdemand?"]}"finance":{"content":"""#Finance&InvestmentAnalysisPathTheFinancepathisaspecializedlearningtrackfocusedonunderstandingfinancialmarketsanalyzinginvestmentsandmanagingrisk.##KeySkills-Financialdataanalysisanddecision-making-Understandingfinancialmarketsandinvestments-Riskmanagementandfinancialplanning-Financialmodelingandforecasting-Investmentstrategiesandanalysis##CoreCourses-FinancialFundamentals:Understandingcorefinancialconcepts-InvestmentStrategies:Developingeffectiveinvestmentapproaches-FinancialModeling:Buildingmodelsforanalysisandforecasting-RiskManagement:Identifyingandmitigatingfinancialrisks##CareerOpportunities-FinancialAnalyst-InvestmentAdvisor-RiskManager-FinancialConsultant-BusinessAnalystThispathisidealforanalyticaldetail-orientedpeopleinterestedinfinancialmarkets.""""suggested_questions":["WillIlearnaboutinvestmentstrategies?""Doesthispathcovercryptocurrency?""WhatfinancialsoftwarewillIuse?"]}"marketing":{"content":"""#Marketing&BrandStrategyPathTheMarketingpathisacreativelearningtrackfocusedonunderstandingconsumerbehavioranddevelopingeffectivemarketingstrategies.##KeySkills-Consumerbehaviorunderstandingandmarketanalysis-Digitalmarketingandsocialmedia-Contentmarketingandstorytelling-Marketinganalyticsandperformancemeasurement-Brandstrategyandidentitymanagement##CoreCourses-MarketingFundamentals:Understandingcoremarketingprinciples-DigitalMarketing:Creatingandoptimizingdigitalcampaigns-ContentMarketing:Developingcompellingcontentstrategies-MarketingAnalytics:Measuringandimprovingmarketingperformance##CareerOpportunities-MarketingSpecialist-DigitalMarketer-ContentStrategist-BrandManager-MarketingAnalystThispathisidealforcreativecommunication-orientedpeopleinterestedinconsumerbehavior.""""suggested_questions":["Doesthispathcoversocialmediamarketing?""WillIlearnaboutSEO?""Doweworkwithrealbrands?"]}}#GenericfallbackinEnglishgeneric_content="Weofferfourmainlearningpaths:ProgrammingCybersecurityFinanceandMarketing.Eachisdesignedtohelpyoudevelopskillsforspecificcareergoals.Wouldyouliketolearnmoreaboutaspecificpath?"generic_questions=["TellmeabouttheProgrammingpath""TellmeabouttheCybersecuritypath""TellmeabouttheFinancepath""TellmeabouttheMarketingpath"]ifpathinpath_details:return{"content":path_details[path]["content"]"metadata":{"type":"path_specific_info""path":path"is_fallback":True}"suggested_questions":path_details[path]["suggested_questions"]}#Genericfallbackreturn{"content":generic_content"metadata":{"type":"general_path_info""is_fallback":True}"suggested_questions":generic_questions}
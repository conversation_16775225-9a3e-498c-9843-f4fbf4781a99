// Mock for MUI components
import React from 'react';
import { useTheme, alpha } from './styles';

// Filter out MUI-specific props that shouldn't be passed to DOM elements
const filterProps = (props) => {
  const {
    // Layout props
    justifyContent, alignItems, alignContent, alignSelf,
    // Typography props
    gutterBottom, paragraph, noWrap, variant, component,
    // Spacing props
    spacing, gap, direction, wrap,
    // Size props
    size, minHeight, maxHeight, minWidth, maxWidth,
    // Color props
    color, bgcolor, backgroundColor,
    // Position props
    position, top, right, bottom, left, zIndex,
    // Flex props
    flexDirection, flexWrap, flexGrow, flexShrink, flexBasis,
    // Display props
    display, overflow, overflowX, overflowY, visibility,
    // Text props
    textAlign, textTransform, fontWeight, fontSize, lineHeight,
    // Border props
    border, borderColor, borderRadius, borderTop, borderRight, borderBottom, borderLeft,
    // Shadow props
    boxShadow, elevation,
    // MUI specific props
    sx, system, ownerState, theme, classes, className,
    // Tab specific props
    TabIndicatorProps, TabScrollButtonProps, textColor, indicatorColor, orientation, scrollButtons, centered,
    // Grid specific props
    container, item, xs, sm, md, lg, xl, spacing: gridSpacing,
    // Button specific props
    disableElevation, disableFocusRipple, disableRipple, endIcon, fullWidth, startIcon,
    // Icon specific props
    edge, fontSize: iconFontSize,
    // Box specific props
    clone, htmlColor, ref,
    ...filteredProps
  } = props;
  return filteredProps;
};

const Card = ({ children, ...props }) => (
  <div data-testid='mui-card' {...filterProps(props)}>
    {children}
  </div>
);
const CardContent = ({ children, ...props }) => (
  <div data-testid='mui-card-content' {...filterProps(props)}>
    {children}
  </div>
);
const CardActions = ({ children, ...props }) => (
  <div data-testid='mui-card-actions' {...filterProps(props)}>
    {children}
  </div>
);
const Typography = ({ children, ...props }) => (
  <div data-testid='mui-typography' {...filterProps(props)}>
    {children}
  </div>
);
const Button = ({ children, ...props }) => (
  <button data-testid='mui-button' {...filterProps(props)}>
    {children}
  </button>
);
const Box = ({ children, ...props }) => (
  <div data-testid='mui-box' {...filterProps(props)}>
    {children}
  </div>
);
const TextField = ({ children, ...props }) => (
  <input data-testid='mui-textfield' {...filterProps(props)} />
);
const Grid = ({ children, ...props }) => (
  <div data-testid='mui-grid' {...filterProps(props)}>
    {children}
  </div>
);
const Tabs = ({ children, ...props }) => (
  <div data-testid='mui-tabs' role='tablist' {...filterProps(props)}>
    {children}
  </div>
);
const Tab = ({ children, ...props }) => (
  <button data-testid='mui-tab' role='tab' {...filterProps(props)}>
    {children}
  </button>
);
const Paper = ({ children, ...props }) => (
  <div data-testid='mui-paper' {...filterProps(props)}>
    {children}
  </div>
);
const Table = ({ children, ...props }) => (
  <table data-testid='mui-table' {...filterProps(props)}>
    {children}
  </table>
);
const TableBody = ({ children, ...props }) => (
  <tbody data-testid='mui-table-body' {...filterProps(props)}>
    {children}
  </tbody>
);
const TableCell = ({ children, ...props }) => (
  <td data-testid='mui-table-cell' {...filterProps(props)}>
    {children}
  </td>
);
const TableContainer = ({ children, ...props }) => (
  <div data-testid='mui-table-container' {...filterProps(props)}>
    {children}
  </div>
);
const TableHead = ({ children, ...props }) => (
  <thead data-testid='mui-table-head' {...filterProps(props)}>
    {children}
  </thead>
);
const TableRow = ({ children, ...props }) => (
  <tr data-testid='mui-table-row' {...filterProps(props)}>
    {children}
  </tr>
);
const CircularProgress = ({ ...props }) => (
  <div data-testid='mui-circular-progress' role='progressbar' aria-label='Loading...' {...filterProps(props)} />
);
const Alert = ({ children, ...props }) => (
  <div data-testid='mui-alert' role='alert' {...filterProps(props)}>
    {children}
  </div>
);
const Container = ({ children, ...props }) => (
  <div data-testid='mui-container' {...filterProps(props)}>
    {children}
  </div>
);
const Breadcrumbs = ({ children, ...props }) => (
  <nav data-testid='mui-breadcrumbs' {...filterProps(props)}>
    {children}
  </nav>
);
const Link = ({ children, ...props }) => (
  <a data-testid='mui-link' {...filterProps(props)}>
    {children}
  </a>
);
const AppBar = ({ children, ...props }) => (
  <div data-testid='mui-appbar' {...filterProps(props)}>
    {children}
  </div>
);
const Toolbar = ({ children, ...props }) => (
  <div data-testid='mui-toolbar' {...filterProps(props)}>
    {children}
  </div>
);
const Stepper = ({ children, ...props }) => (
  <div data-testid='mui-stepper' {...filterProps(props)}>
    {children}
  </div>
);
const Step = ({ children, ...props }) => (
  <div data-testid='mui-step' {...filterProps(props)}>
    {children}
  </div>
);
const StepLabel = ({ children, ...props }) => (
  <div data-testid='mui-step-label' {...filterProps(props)}>
    {children}
  </div>
);
const StepContent = ({ children, ...props }) => (
  <div data-testid='mui-step-content' {...filterProps(props)}>
    {children}
  </div>
);
const SvgIcon = ({ children, ...props }) => (
  <svg data-testid='mui-svg-icon' {...filterProps(props)}>
    {children}
  </svg>
);
const Chip = ({ label, ...props }) => (
  <div data-testid='mui-chip' {...filterProps(props)}>
    {label}
  </div>
);
const LinearProgress = ({ ...props }) => (
  <div data-testid='mui-linear-progress' role='progressbar' aria-label='Loading...' {...filterProps(props)} />
);
const Tooltip = ({ children, title, ...props }) => (
  <div data-testid='mui-tooltip' title={title} {...filterProps(props)}>
    {children}
  </div>
);

// Mock styled function
const styled = (component) => {
  return (styles) => {
    return ({ children, ...props }) => {
      if (typeof component === 'string') {
        return React.createElement(component, { ...filterProps(props), 'data-testid': `styled-${component}` }, children);
      }
      return React.createElement(component, { ...filterProps(props) }, children);
    };
  };
};

// Mock createTheme function
const createTheme = (options) => {
  return {
    palette: {
      primary: { main: '#1976d2' },
      secondary: { main: '#dc004e' },
      background: { default: '#fff', paper: '#fff' },
      text: { primary: '#000', secondary: '#666' },
      common: { black: '#000', white: '#fff' },
      grey: { 50: '#fafafa', 100: '#f5f5f5', 500: '#9e9e9e', 900: '#212121' },
      error: { main: '#d32f2f' },
      warning: { main: '#ed6c02' },
      info: { main: '#0288d1' },
      success: { main: '#2e7d32' },
    },
    typography: {
      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
      h1: { fontSize: '2rem' },
      h2: { fontSize: '1.5rem' },
      h3: { fontSize: '1.25rem' },
      h4: { fontSize: '1.125rem' },
      h5: { fontSize: '1rem' },
      h6: { fontSize: '0.875rem' },
      body1: { fontSize: '1rem' },
      body2: { fontSize: '0.875rem' },
    },
    spacing: (factor) => `${factor * 8}px`,
    breakpoints: {
      values: { xs: 0, sm: 600, md: 960, lg: 1280, xl: 1920 },
    },
    shape: { borderRadius: 4 },
    transitions: { duration: { shortest: 150, shorter: 200, short: 250, standard: 300, complex: 375, enteringScreen: 225, leavingScreen: 195 } },
    zIndex: { appBar: 1100, drawer: 1200, modal: 1300, snackbar: 1400, tooltip: 1500 },
    ...options,
  };
};

// Mock ThemeProvider
const ThemeProvider = ({ children, theme }) => {
  return React.createElement('div', { 'data-testid': 'theme-provider' }, children);
};

export {
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  Box,
  LinearProgress,
  Container,
  Breadcrumbs,
  Link,
  Tabs,
  Tab,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  AppBar,
  Toolbar,
  SvgIcon,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Tooltip,
  useTheme,
  alpha,
  styled,
  createTheme,
  ThemeProvider,
};

"""ExamSecurityAPIViewsProvidesRESTAPIendpointsfor:-Startingexamsessionswithtimer-Monitoringsessionstatus-Handlinglockdownviolations-GeneratingSEBconfigs-Managingheartbeats"""import jsonfromdatetimeimport datetimefrom django.httpimportJsonResponseHttpResponsefrom django.views.decorators.csrfimportcsrf_exemptfrom django.views.decorators.httpimport require_http_methodsfrom django.contrib.auth.decoratorsimportlogin_requiredfrom django.utils.decoratorsimportmethod_decoratorfrom django.viewsimportViewfromrest_frameworkimportstatusfromrest_framework.decoratorsimportapi_viewpermission_classesfromrest_framework.permissionsimportIsAuthenticatedfromrest_framework.responseimportResponsefrom.modelsimportAssessmentExamSessionLockdownViolationfrom.exam_security_serviceimportexam_timer_servicelockdown_serviceseb_serviceLockdownServiceimport logginglogger=logging.getLogger(__name__)@api_view(['POST'])@permission_classes([IsAuthenticated])defstart_exam_session(request):"""Startanewexamsessionwithtimerandlockdownconfiguration"""try:data=request.dataassessment_id=data.get('assessment_id')lockdown_config=data.get('lockdown_config'{})ifnotassessment_id:returnResponse({'success':False'error':'AssessmentIDisrequired'}status=status.HTTP_400_BAD_REQUEST)try:assessment=Assessment.objects.get(id=assessment_id)exceptAssessment.DoesNotExist:returnResponse({'success':False'error':'Assessmentnotfound'}status=status.HTTP_404_NOT_FOUND)#Startexamsessionresult=exam_timer_service.start_exam_session(student=request.userassessment=assessmentlockdown_config=lockdown_config)ifresult['success']:#GenerateSEBconfigiflockdownisenablediflockdown_config:exam_session=ExamSession.objects.get(id=result['session_id'])seb_config=seb_service.generate_seb_config(assessmentexam_session)respondus_link=seb_service.generate_respondus_ldb_link(assessmentexam_session)result['seb_config']=seb_configresult['respondus_link']=respondus_linkreturnResponse(resultstatus=status.HTTP_201_CREATED)else:returnResponse(resultstatus=status.HTTP_400_BAD_REQUEST)exceptExceptionase:logger.error(f"Errorstartingexamsession:{str(e)}")returnResponse({'success':False'error':'Internalservererror'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAuthenticated])defget_session_status(requestsession_token):"""Getcurrentexamsessionstatusincludingtimeremaining"""try:result=exam_timer_service.get_session_status(session_token)ifresult['success']:returnResponse(resultstatus=status.HTTP_200_OK)else:returnResponse(resultstatus=status.HTTP_404_NOT_FOUND)exceptExceptionase:logger.error(f"Errorgettingsessionstatus:{str(e)}")returnResponse({'success':False'error':'Internalservererror'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['POST'])@permission_classes([IsAuthenticated])defsubmit_exam_session(requestsession_token):"""Submitanexamsession(manualorauto-submit)"""try:force_submit=request.data.get('force_submit'False)result=exam_timer_service.submit_exam_session(session_tokenforce_submit)ifresult['success']:returnResponse(resultstatus=status.HTTP_200_OK)else:returnResponse(resultstatus=status.HTTP_400_BAD_REQUEST)exceptExceptionase:logger.error(f"Errorsubmittingexamsession:{str(e)}")returnResponse({'success':False'error':'Internalservererror'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['POST'])@permission_classes([IsAuthenticated])defheartbeat(requestsession_token):"""Processheartbeattoverifysessionisstillactive"""try:result=exam_timer_service.heartbeat(session_token)ifresult['success']:returnResponse(resultstatus=status.HTTP_200_OK)else:returnResponse(resultstatus=status.HTTP_404_NOT_FOUND)exceptExceptionase:logger.error(f"Errorprocessingheartbeat:{str(e)}")returnResponse({'success':False'error':'Internalservererror'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['POST'])@permission_classes([IsAuthenticated])deflog_lockdown_violation(request):"""Logalockdownviolation"""try:data=request.datasession_token=data.get('session_token')violation_type=data.get('violation_type')violation_data=data.get('violation_data'{})browser_info=data.get('browser_info'{})system_info=data.get('system_info'{})ifnotsession_tokenornotviolation_type:returnResponse({'success':False'error':'Sessiontokenandviolationtypearerequired'}status=status.HTTP_400_BAD_REQUEST)result=lockdown_service.log_violation(session_token=session_tokenviolation_type=violation_typeviolation_data=violation_databrowser_info=browser_infosystem_info=system_info)ifresult['success']:returnResponse(resultstatus=status.HTTP_201_CREATED)else:returnResponse(resultstatus=status.HTTP_400_BAD_REQUEST)exceptExceptionase:logger.error(f"Errorloggingviolation:{str(e)}")returnResponse({'success':False'error':'Internalservererror'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAuthenticated])defdownload_seb_config(requestsession_token):"""DownloadSafeExamBrowserconfigurationfile"""try:exam_session=ExamSession.objects.filter(session_token=session_tokenstudent=request.user).first()ifnotexam_session:returnResponse({'success':False'error':'Sessionnotfound'}status=status.HTTP_404_NOT_FOUND)seb_config=seb_service.generate_seb_config(exam_session.assessmentexam_session)#Createdownloadablefileresponse=HttpResponse(json.dumps(seb_configindent=2)content_type='application/json')filename=f"exam_{exam_session.assessment.id}_config.seb"response['Content-Disposition']=f'attachment;filename="{filename}"'returnresponseexceptExceptionase:logger.error(f"ErrorgeneratingSEBconfig:{str(e)}")returnResponse({'success':False'error':'Internalservererror'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAuthenticated])defget_respondus_link(requestsession_token):"""GetRespondusLockDownBrowserdeeplink"""try:exam_session=ExamSession.objects.filter(session_token=session_tokenstudent=request.user).first()ifnotexam_session:returnResponse({'success':False'error':'Sessionnotfound'}status=status.HTTP_404_NOT_FOUND)respondus_link=seb_service.generate_respondus_ldb_link(exam_session.assessmentexam_session)returnResponse({'success':True'respondus_link':respondus_link}status=status.HTTP_200_OK)exceptExceptionase:logger.error(f"ErrorgeneratingResponduslink:{str(e)}")returnResponse({'success':False'error':'Internalservererror'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAuthenticated])defvalidate_lockdown_environment(request):"""Validateifthecurrentenvironmentsupportslockdownmode"""try:user_agent=request.META.get('HTTP_USER_AGENT''')#CheckforSafeExamBrowseris_seb='SEB'inuser_agentor'SafeExamBrowser'inuser_agent#CheckforRespondusLockDownBrowseris_ldb='LockDown'inuser_agentor'Respondus'inuser_agent#Checkforstandardbrowserthatcouldsupportlockdownsupported_browsers=['Chrome''Firefox''Safari''Edge']is_supported_browser=any(browserinuser_agentforbrowserinsupported_browsers)#Generatehandshaketokenforverificationhandshake_token=lockdown_service._generate_handshake_token()ifhasattr(lockdown_service'_generate_handshake_token')elseNonereturnResponse({'success':True'environment':{'is_seb':is_seb'is_ldb':is_ldb'is_supported_browser':is_supported_browser'user_agent':user_agent'handshake_token':handshake_token'lockdown_capable':is_seboris_ldboris_supported_browser}}status=status.HTTP_200_OK)exceptExceptionase:logger.error(f"Errorvalidatinglockdownenvironment:{str(e)}")returnResponse({'success':False'error':'Internalservererror'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAuthenticated])defget_session_violations(requestsession_token):"""Getviolationsforaspecificexamsession"""try:exam_session=ExamSession.objects.filter(session_token=session_tokenstudent=request.user).first()ifnotexam_session:returnResponse({'success':False'error':'Sessionnotfound'}status=status.HTTP_404_NOT_FOUND)violations=LockdownViolation.objects.filter(exam_session=exam_session).order_by('-timestamp')violation_data=[]forviolationinviolations:violation_data.append({'id':violation.id'violation_type':violation.violation_type'severity':violation.severity'timestamp':violation.timestamp.isoformat()'violation_data':violation.violation_data'auto_response':violation.auto_response'reviewed':violation.reviewed})returnResponse({'success':True'violations':violation_data'total_count':len(violation_data)'summary':exam_session.get_total_violations()}status=status.HTTP_200_OK)exceptExceptionase:logger.error(f"Errorgettingsessionviolations:{str(e)}")returnResponse({'success':False'error':'Internalservererror'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)#AddmethodtoLockdownServiceforgeneratinghandshaketokensdef_generate_handshake_token(self):"""Generateahandshaketokenforlockdownverification"""importsecretsreturnsecrets.token_urlsafe(16)#MonkeypatchthemethodLockdownService._generate_handshake_token=_generate_handshake_token
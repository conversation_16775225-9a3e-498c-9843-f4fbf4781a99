// Mock for MUI styles
import React from 'react';

const useTheme = () => ({
  palette: {
    mode: 'light',
    primary: { main: '#1976d2' },
    secondary: { main: '#dc004e' },
    error: { main: '#f44336' },
    warning: { main: '#ff9800' },
    info: { main: '#2196f3' },
    success: { main: '#4caf50' },
    background: {
      default: '#fafafa',
      paper: '#ffffff'
    },
    text: { primary: 'rgba(0, 0, 0, 0.87)', secondary: 'rgba(0, 0, 0, 0.54)' },
    divider: 'rgba(0, 0, 0, 0.12)',
    grey: { 500: '#9e9e9e' },
    common: {
      black: '#000000',
      white: '#ffffff'
    },
  },
  spacing: factor => `${0.5 * factor}rem`,
  breakpoints: {
    up: size => `@media (min-width:${size}px)`,
    down: size => `@media (max-width:${size}px)`,
    values: {
      xs: 0,
      sm: 600,
      md: 960,
      lg: 1280,
      xl: 1920,
    },
  },
});

const alpha = (color, opacity) => {
  // Convert hex color to rgba with opacity
  if (color && typeof color === 'string' && color.startsWith('#')) {
    const hex = color.slice(1);
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  // Return a default color if color is undefined or invalid
  return color || `rgba(0, 0, 0, ${opacity || 1})`;
};

const createTheme = () => ({
  palette: {
    mode: 'light',
    primary: { main: '#1976d2' },
    secondary: { main: '#dc004e' },
  },
});

const ThemeProvider = ({ children }) => children;

const styled = component => () => component;

module.exports = {
  useTheme,
  alpha,
  createTheme,
  ThemeProvider,
  styled,
};

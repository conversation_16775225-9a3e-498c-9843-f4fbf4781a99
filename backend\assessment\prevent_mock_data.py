"""
This module contains functions to prevent the creation of mock data in the assessment system.
It should be imported in the assessment views and services to ensure that no mock data is created.
"""

import logging
from django.conf import settings

logger = logging.getLogger(__name__)

def is_mock_question_text(question_text):
    """Check if a question text indicates a mock/sample question"""
    if not question_text:
        return False
    # Check if the question text contains sample/mock indicators
    mock_indicators = ["sample", "test", "mock", "dummy", "example"]
    question_text_lower = question_text.lower()
    for indicator in mock_indicators:
        if indicator in question_text_lower:
            return True
    # Check if the question was created by our script
    if question_text.startswith("Sample question"):
        return True
    return False

def prevent_mock_question_creation(question_data):
    """Check if the question data indicates a mock question and prevent its creation.
    Returns True if the question should be prevented, False otherwise."""
    # Skip this check in development mode if allowed
    if getattr(settings, "ALLOW_MOCK_DATA_IN_DEV", False) and settings.DEBUG:
        return False
    # Check question text
    question_text = question_data.get("question_text", "") or question_data.get("text", "")
    if is_mock_question_text(question_text):
        logger.warning(f"Prevented creation of mock question: {question_text[:50]}...")
        return True
    return False

def prevent_mock_response_creation(response_data):
    """Check if the response data indicates a mock response and prevent its creation.
    Returns True if the response should be prevented, False otherwise."""
    # Skip this check in development mode if allowed
    if getattr(settings, "ALLOW_MOCK_DATA_IN_DEV", False) and settings.DEBUG:
        return False
    # Check feedback
    feedback = response_data.get("feedback", "")
    if feedback:
        mock_indicators = ["sample", "test", "mock", "dummy", "example"]
        feedback_lower = feedback.lower()
        for indicator in mock_indicators:
            if indicator in feedback_lower:
                logger.warning(f"Prevented creation of mock response with feedback: {feedback[:50]}...")
                return True
    return False

def sanitize_assessment_data(assessment_data):
    """Sanitize assessment data to remove any mock indicators. Returns the sanitized data."""
    # Make a copy of the data to avoid modifying the original
    sanitized_data = assessment_data.copy() if assessment_data else {}
    # Remove any mock indicators from the title and description
    if "title" in sanitized_data:
        title = sanitized_data["title"]
        if title and any(indicator in title.lower() for indicator in ["sample", "test", "mock", "dummy", "example"]):
            # Replace mock indicators with more appropriate text
            for indicator in ["sample", "test", "mock", "dummy", "example"]:
                sanitized_data["title"] = sanitized_data["title"].replace(indicator, "assessment")
                sanitized_data["title"] = sanitized_data["title"].replace(indicator.capitalize(), "Assessment")
    if "description" in sanitized_data:
        description = sanitized_data["description"]
        if description and any(indicator in description.lower() for indicator in ["sample", "test", "mock", "dummy", "example"]):
            # Replace mock indicators with more appropriate text
            for indicator in ["sample", "test", "mock", "dummy", "example"]:
                sanitized_data["description"] = sanitized_data["description"].replace(indicator, "assessment")
                sanitized_data["description"] = sanitized_data["description"].replace(indicator.capitalize(), "Assessment")
    return sanitized_data

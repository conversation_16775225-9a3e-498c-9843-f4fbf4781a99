"""CentralizedAIConfigurationManagerThismoduleensuresallAIservicesusethesameconfigurationandAPIkey.ItprovidesaunifiedinterfaceformanagingAIsettingsacrosstheapplication."""import loggingfromtypingimportDictAnyListOptionalfrom django.confimportsettingsfrom django.core.cacheimportcachefrom django.dispatchimportSignallogger=logging.getLogger(__name__)#Signalsfornotifyingservicesofconfigurationchangesai_config_updated=Signal()api_key_updated=Signal()#CachekeysAI_CONFIG_CACHE_KEY="unified_ai_config"AI_CONFIG_VERSION_KEY="ai_config_version"classAIConfigurationManager:"""CentralizedmanagerforAIconfigurationacrossallservices.Ensuresconsistencyandproperpropagationofconfigurationchanges."""_instance=None_services_registry={}def__new__(cls):ifcls._instanceisNone:cls._instance=super().__new__(cls)cls._instance._initialized=Falsereturncls._instancedef__init__(self):ifnotgetattr(self'_initialized'False):self._services_registry={}self._default_config={#CoreAIsettings(sharedacrossallservices)'default_model':'gemini-2.0-flash''fallback_model':'gemini-1.5-pro''temperature':0.7'max_tokens':2048'top_p':0.9'timeout':30000'retries':3'rate_limit_per_minute':60'enable_caching':True'enable_monitoring':True'enable_analytics':True#Service-specificsettings(unifiedunderoneconfig)'service_specific':{'chatbot':{'conversation_memory':True'max_conversation_length':50'enable_context_awareness':True}'ai_assistant':{'enable_suggestions':True'enable_personalization':True'max_suggestions':10}'study_assistant':{'enable_flashcards':True'enable_spaced_repetition':True'study_plan_depth':'detailed'}'course_generator':{'enable_auto_generation':True'content_complexity':'adaptive'}:{'enable_speech_recognition':True'enable_adaptive_difficulty':True}}}self._initialized=Truelogger.info("AIConfigurationManagerinitializedwithunifiedsettings")defregister_service(selfservice_name:strservice_instance:Anyrefresh_method:str='refresh_configuration'):"""RegisteranAIservicetoreceiveconfigurationupdates.Args:service_name:Uniquenamefortheserviceservice_instance:Theserviceinstancerefresh_method:Methodnametocallforrefreshingconfiguration"""self._services_registry[service_name]={'instance':service_instance'refresh_method':refresh_method}logger.info(f"RegisteredAIservice:{service_name}")defunregister_service(selfservice_name:str):"""UnregisteranAIservice."""ifservice_nameinself._services_registry:delself._services_registry[service_name]logger.info(f"UnregisteredAIservice:{service_name}")defget_unified_config(selfforce_rebuild:bool=False)->Dict[strAny]:"""GettheunifiedAIconfigurationusedbyallservices.Args:force_rebuild:IfTrueignorecachedconfigandrebuildfromscratchReturns:DictcontainingthecurrentAIconfiguration"""try:#Checkifweshouldforcearebuildorifcacheisemptyifnotforce_rebuild:cached_config=cache.get(AI_CONFIG_CACHE_KEY)ifcached_config:returncached_config#ImportAPIkeyservicetogetcurrentAPIkeyfrom utils.api_key_serviceimportApiKeyServiceapi_key=ApiKeyService.get_api_key()#Buildunifiedconfigurationconfig=self._default_config.copy()#AddAPIkeytoconfigifavailableifapi_key:config['api_key']=api_key#AddAPIkeystatusconfig.update({'api_key_configured':bool(api_key)'api_key_masked':self._mask_api_key(api_key)ifapi_keyelse'Notconfigured''service_available':bool(api_key)'updated_at':cache.get(f"{AI_CONFIG_CACHE_KEY}_timestamp""Never")'version':cache.get(AI_CONFIG_VERSION_KEY1)})#Checkforanyfrontend-providedconfigurationoverridesfrontend_config=cache.get('frontend_ai_config')iffrontend_config:config.update(frontend_config)logger.info("Appliedfrontendconfigurationoverrides")#Cachetheconfiguration(shortertimeouttoensureupdatespropagatefaster)cache.set(AI_CONFIG_CACHE_KEYconfigtimeout=60)#***********************************************:logger.error(f"Errorgettingunifiedconfig:{e}")returnself._default_config.copy()defupdate_configuration(selfnew_config:Dict[strAny]source:str="unknown")->bool:"""UpdatetheAIconfigurationandnotifyallregisteredservices.Args:new_config:Dictionarywithconfigurationupdatessource:Sourceoftheconfigurationupdate(forlogging)Returns:bool:Trueifupdatewassuccessful"""try:logger.info(f"UpdatingAIconfigurationfrom{source}")logger.info(f"Configurationupdates:{list(new_config.keys())}")#Getcurrentconfigcurrent_config=self.get_unified_config()#UpdateAPIkeyifprovidedapi_key_updated_flag=Falseif'api_key'innew_configandnew_config['api_key']:try:from utils.api_key_serviceimportApiKeyServicesuccess=ApiKeyService.update_api_key(new_config['api_key'])ifsuccess:api_key_updated_flag=Truelogger.info("APIkeyupdatedsuccessfully")else:logger.warning("FailedtoupdateAPIkeyviaApiKeyServiceusingfallback")#Fallback:storeincachedirectlyfortestingcache.set('GEMINI_API_KEY'new_config['api_key']timeout=3600)api_key_updated_flag=Truelogger.info("APIkeyupdatedsuccessfully(fallbackmode)")exceptExceptionase:logger.warning(f"ErrorupdatingAPIkey:{e}usingfallback")#Fallback:storeincachedirectlyfortestingcache.set('GEMINI_API_KEY'new_config['api_key']timeout=3600)api_key_updated_flag=Truelogger.info("APIkeyupdatedsuccessfully(fallbackmode)")#Updateotherconfigurationparametersconfig_keys=['default_model''fallback_model''temperature''max_tokens''timeout''retries''rate_limit_per_minute''enable_caching''enable_monitoring''fallback_enabled']updated_config=current_config.copy()config_changed=Falseforkeyinconfig_keys:ifkeyinnew_config:#Handleparameternamevariationsifkey=='max_tokens'and'max_tokens'notinnew_configand'maxTokens'innew_config:updated_config[key]=new_config['maxTokens']elifkey=='default_model'and'default_model'notinnew_configand'model'innew_config:updated_config[key]=new_config['model']else:updated_config[key]=new_config[key]config_changed=Truelogger.info(f"Updated{key}:{updated_config[key]}")ifconfig_changedorapi_key_updated_flag:#ClearoldcachedconfigurationtoforcerebuildwithnewAPIkeycache.delete(AI_CONFIG_CACHE_KEY)#Incrementversionversion=cache.get(AI_CONFIG_VERSION_KEY0)+1cache.set(AI_CONFIG_VERSION_KEYversion)#Updatetimestampfromdatetimeimport datetimetimestamp=datetime.now().isoformat()cache.set(f"{AI_CONFIG_CACHE_KEY}_timestamp"timestamp)#GetfreshconfigwithnewAPIkey(forcerebuild)updated_config=self.get_unified_config(force_rebuild=True)updated_config['version']=versionupdated_config['updated_at']=timestamp#Storefrontendconfigseparatelyforreferencefrontend_config={k:vforkvinnew_config.items()ifk!='api_key'}iffrontend_config:cache.set('frontend_ai_config'frontend_configtimeout=3600)#Notifyallregisteredservicesself._notify_all_services()#SendDjangosignalsifapi_key_updated_flag:api_key_updated.send(sender=self.__class__config=updated_config)ai_config_updated.send(sender=self.__class__config=updated_config)logger.info(f"AIconfigurationupdatedsuccessfully(version{version})")logger.info(f"NewAPIkeyconfigured:{'Yes'ifupdated_config.get('api_key_configured')else'No'}")returnTrueelse:logger.info("Noconfigurationchangesdetected")returnTrueexceptExceptionase:logger.error(f"ErrorupdatingAIconfiguration:{e}")returnFalsedef_notify_all_services(self):"""Notifyallregisteredservicesofconfigurationchanges."""logger.info(f"Notifying{len(self._services_registry)}registeredAIservices")failed_services=[]forservice_nameservice_infoinself._services_registry.items():try:service_instance=service_info['instance']refresh_method=service_info['refresh_method']#Calltherefreshmethodifhasattr(service_instancerefresh_method):method=getattr(service_instancerefresh_method)method()logger.info(f"Successfullynotifiedservice:{service_name}")else:logger.warning(f"Service{service_name}doesnothavemethod{refresh_method}")exceptExceptionase:logger.error(f"Failedtonotifyservice{service_name}:{e}")failed_services.append(service_name)#Trytonotifyservicesthatdon'tuseregistration(fallback)self._notify_unregistered_services()iffailed_services:logger.warning(f"Failedtonotifyservices:{failed_services}")def_notify_unregistered_services(self):"""Notifyservicesthatdon'tusetheregistrationsystem."""try:#NotifyconsolidatedAIservicefrom utils.ai.servicesimportget_ai_serviceai_service=get_ai_service()ifhasattr(ai_service'refresh_configuration'):ai_service.refresh_configuration()logger.info("NotifiedconsolidatedAIservice")exceptExceptionase:logger.warning(f"CouldnotnotifyconsolidatedAIservice:{e}")try:#Notifychatservicefrom chatbot.services.chat_serviceimport refresh_chat_servicerefresh_chat_service()logger.info("Notifiedchatservice")exceptExceptionase:logger.warning(f"Couldnotnotifychatservice:{e}")try:#Notifycoursegeneratorservicefrom course_generator.centralized_serviceimport refresh_course_generator_servicerefresh_course_generator_service()logger.info("Notifiedcoursegeneratorservice")exceptExceptionase:logger.warning(f"Couldnotnotifycoursegeneratorservice:{e}")try:#Notifyspeechservicefrominteractive_learning.services.speech_serviceimportArabicSpeechService#Createatemporaryinstancetorefreshtheclass-levelconfigurationspeech_service=ArabicSpeechService()speech_service.refresh_api_key()logger.info("Notifiedspeechservice")exceptExceptionase:logger.warning(f"Couldnotnotifyspeechservice:{e}")def_mask_api_key(selfapi_key:str)->str:"""MaskAPIkeyfordisplaypurposes."""ifnotapi_keyorlen(api_key)<8:return"****"returnf"{api_key[:4]}...{api_key[-4:]}"defget_service_status(self)->Dict[strAny]:"""Getstatusofallregisteredservices."""status={'total_services':len(self._services_registry)'registered_services':list(self._services_registry.keys())'configuration_version':cache.get(AI_CONFIG_VERSION_KEY1)'last_updated':cache.get(f"{AI_CONFIG_CACHE_KEY}_timestamp""Never")}returnstatusdefforce_refresh_all(self):"""ForcerefreshofallAIservices(emergencymethod)."""logger.info("ForcerefreshingallAIservices")#Clearconfigurationcachecache.delete(AI_CONFIG_CACHE_KEY)cache.delete('frontend_ai_config')#Notifyallservicesself._notify_all_services()#Incrementversiontoforcefreshconfigurationversion=cache.get(AI_CONFIG_VERSION_KEY0)+1cache.set(AI_CONFIG_VERSION_KEYversion)logger.info("Forcerefreshcompleted")defget_service_config(selfservice_name:str)->Dict[strAny]:"""GetconfigurationforaspecificAIservice.Args:service_name:Nameoftheservice('chatbot''ai_assistant'etc.)Returns:Dictcontainingbothsharedandservice-specificconfiguration"""#Gettheunifiedconfigunified_config=self.get_unified_config()#Createservice-specificconfigwithsharedsettingsservice_config={#Sharedsettings'api_key':unified_config.get('api_key')'api_key_configured':unified_config.get('api_key_configured'False)'default_model':unified_config.get('default_model')'fallback_model':unified_config.get('fallback_model')'temperature':unified_config.get('temperature')'max_tokens':unified_config.get('max_tokens')'timeout':unified_config.get('timeout')'retries':unified_config.get('retries')'rate_limit_per_minute':unified_config.get('rate_limit_per_minute')'enable_caching':unified_config.get('enable_caching')'enable_monitoring':unified_config.get('enable_monitoring')}#Addservice-specificsettingsiftheyexistservice_specific=unified_config.get('service_specific'{})ifservice_nameinservice_specific:service_config.update(service_specific[service_name])returnservice_configdefupdate_service_config(selfservice_name:strupdates:Dict[strAny])->bool:"""Updateconfigurationforaspecificservice.Args:service_name:Nameoftheserviceupdates:DictionaryofconfigurationupdatesReturns:bool:Trueifupdatewassuccessful"""try:#Getcurrentconfigcurrent_config=self.get_unified_config()#Updateservice-specificsettingsif'service_specific'notincurrent_config:current_config['service_specific']={}ifservice_namenotincurrent_config['service_specific']:current_config['service_specific'][service_name]={}current_config['service_specific'][service_name].update(updates)#Savetheupdatedconfigurationreturnself.update_configuration(current_configsource=f"service_update_{service_name}")exceptExceptionase:logger.error(f"Failedtoupdateserviceconfigfor{service_name}:{e}")returnFalse#Globalinstanceai_config_manager=AIConfigurationManager()#Conveniencefunctionsdefget_ai_config():"""GetthecurrentunifiedAIconfiguration."""returnai_config_manager.get_unified_config()defupdate_ai_config(config:Dict[strAny]source:str="unknown"):"""UpdatetheAIconfiguration."""returnai_config_manager.update_configuration(configsource)defregister_ai_service(name:strinstance:Anyrefresh_method:str='refresh_configuration'):"""RegisteranAIserviceforconfigurationupdates."""ai_config_manager.register_service(nameinstancerefresh_method)defforce_refresh_ai_services():"""ForcerefreshallAIservices."""ai_config_manager.force_refresh_all()defget_service_config(service_name:str):"""Getconfigurationforaspecificservice."""returnai_config_manager.get_service_config(service_name)defupdate_service_config(service_name:strupdates:Dict[strAny]):"""Updateconfigurationforaspecificservice."""returnai_config_manager.update_service_config(service_nameupdates)#Backwardcompatibilityfunctionsforlegacyimportsdefget_model_name():"""Getthecurrentmodelname-backwardcompatibilitywithgemini_config.py"""config=get_ai_config()returnconfig.get('default_model''gemini-2.0-flash')defget_fallback_model_name():"""Getthefallbackmodelname-backwardcompatibilitywithgemini_config.py"""config=get_ai_config()returnconfig.get('fallback_model''gemini-1.5-pro')#DefaultgenerationconfigforbackwardcompatibilityDEFAULT_GENERATION_CONFIG={'temperature':0.7'top_p':0.9'max_output_tokens':2048}defget_generation_config():"""Getgenerationconfig-backwardcompatibilitywithgemini_config.py"""config=get_ai_config()return{'temperature':config.get('temperature'0.7)'top_p':config.get('top_p'0.9)'max_output_tokens':config.get('max_tokens'2048)}
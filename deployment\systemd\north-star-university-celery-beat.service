[Unit]
Description=North Star University Celery Beat Scheduler
After=network.target redis.service postgresql.service
Wants=redis.service postgresql.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/var/www/north-star-university/backend
Environment=DJANGO_SETTINGS_MODULE=settings.production
EnvironmentFile=/var/www/north-star-university/backend/.env.production
ExecStart=/var/www/north-star-university/venv/bin/celery -A university_management beat \
    --pidfile=/var/run/north-star-university/celerybeat.pid \
    --logfile=/var/log/north-star-university/celerybeat.log \
    --loglevel=info \
    --schedule=/var/run/north-star-university/celerybeat-schedule
Restart=on-failure
RestartSec=10

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/north-star-university
ReadWritePaths=/var/run/north-star-university

[Install]
WantedBy=multi-user.target

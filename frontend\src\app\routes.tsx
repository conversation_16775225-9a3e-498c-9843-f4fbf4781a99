import React, { lazy, Suspense } from 'react';
import { Outlet, createBrowserRouter, Navigate } from 'react-router-dom';
// Optimized imports for better tree shaking
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';
// Keep lightweight layout component as named import
import { Box } from '@mui/material';

// Create a better loading component with performance optimizations
const LoadingComponent = () => (
  <Box
    sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      minHeight: '200px',
      width: '100%',
    }}
  >
    <CircularProgress />
  </Box>
);

// Create a lazy loading wrapper to standardize Suspense boundaries
const lazyLoad = (
  importFunc: () => Promise<{ default: React.ComponentType<any> }>
) => {
  const LazyComponent = lazy(importFunc);
  return (props: any) => (
    <Suspense fallback={<LoadingComponent />}>
      <LazyComponent {...props} />
    </Suspense>
  );
};
// Import the TestAssessmentSuccess component lazily
const TestAssessmentSuccess = lazyLoad(
  () => import('../features/assessment/TestAssessmentSuccess.tsx')
);

// Auth Components
const NotFound = lazyLoad(() => import('../components/errors/NotFound.tsx'));
import UnauthorizedPage from '../components/errors/UnauthorizedPage';
import ProtectedRoute from '../components/auth/ProtectedRoute';
import PublicRoute from '../components/auth/PublicRoute';
import { ToastProvider } from '../contexts/ToastContext';
// Import DashboardLayout directly to avoid lazy loading issues
import DashboardLayout from '../components/layout/DashboardLayout';

// Import type
import type { UserRole } from '../components/auth/types';

// Unified Content Components - using lazyLoad helper
const UnifiedCourseContentPage = lazyLoad(
  () => import('../features/student/UnifiedCourseContentPageRefactored')
);
const UnifiedProgressPage = lazyLoad(
  () => import('../pages/UnifiedProgressPage')
);
const UnifiedAnalyticsPage = lazyLoad(
  () => import('../pages/UnifiedAnalyticsPage')
);
const ProfessorCourseView = lazyLoad(
  () => import('../features/professor/ProfessorCourseView')
);

// New Unified Course Components
const UnifiedCoursesPage = lazyLoad(
  () => import('../pages/UnifiedCoursesPage')
);
const ImprovedCourseGenerator = lazyLoad(
  () => import('../components/course-generator/ImprovedCourseGenerator')
);

// Course Generator Components - using lazyLoad helper
// Removed deprecated components
const ContentEditor = lazyLoad(
  () => import('../features/course-generator/components/ContentEditor')
);
const FallbackContentHelp = lazyLoad(
  () => import('../features/course-generator/pages/FallbackContentHelp')
);
const UnifiedGeneratorPage = lazyLoad(
  () => import('../features/course-generator/pages/UnifiedGeneratorPage')
);


// Personalized Learning Components - using lazyLoad helper
const PersonalizedLearningDashboard = lazyLoad(
  () =>
    import(
      '../features/personalized-learning/pages/PersonalizedLearningDashboard'
    )
);

// Study Assistant Components - using lazyLoad helper
const StudyAssistantPage = lazyLoad(() => import('../pages/StudyAssistant'));



// Admin Assessment Components
import {
  AssessmentPreviewDashboard,
  StudentLevelManagement,
  AIDecisionManager,
  AIAssessmentAnalysis,
} from '../features/admin/assessment';
const UnifiedAssessmentView = lazyLoad(
  () => import('../features/admin/assessment/UnifiedAssessmentView')
);
// Course view component - using lazyLoad helper
const CourseViewRefactored = lazyLoad(
  () => import('../features/admin/courses/CourseViewRefactored')
);

// Lazy load components with our lazyLoad helper
const Login = lazyLoad(() =>
  import('../features/auth/Login').then(module => ({ default: module.default }))
);
const Registration = lazyLoad(() =>
  import('../features/auth/Registration').then(module => ({
    default: module.default,
  }))
);
const RegistrationSuccess = lazyLoad(
  () => import('../features/auth/RegistrationSuccess')
);
// ReactIconsDemo removed - component doesn't exist

// Using regular import for HomePage
import HomePage from '../features/home/<USER>';
const DiscoverPathPage = lazyLoad(() =>
  import('../features/discover/DiscoverPathPage').then(module => ({
    default: module.default,
  }))
);

// Specialization Pages - using lazyLoad helper
const MarketingPage = lazyLoad(() =>
  import('../features/specializations/MarketingPage').then(module => ({
    default: module.default,
  }))
);
const FinancePage = lazyLoad(() =>
  import('../features/specializations/FinancePage').then(module => ({
    default: module.default,
  }))
);
const ProgrammingPage = lazyLoad(() =>
  import('../features/specializations/ProgrammingPage').then(module => ({
    default: module.default,
  }))
);
const CybersecurityPage = lazyLoad(() =>
  import('../features/specializations/CybersecurityPage').then(module => ({
    default: module.default,
  }))
);

// Assessment and other components - using lazyLoad helper
const UnifiedAssessment = lazyLoad(() =>
  import('../features/auth/assessment').then(module => ({
    default: module.default,
  }))
);
const ChatPage = lazyLoad(() => import('../pages/ChatPage'));
const RoleBasedMultiAgentChat = lazyLoad(
  () => import('../components/RoleBasedMultiAgentChat')
);
const MultiAgentChatDemo = lazyLoad(
  () => import('../components/MultiAgentChatDemo')
);
const AgentStatusDashboard = lazyLoad(
  () => import('../components/AgentStatusDashboard')
);
const AgentTestingLab = lazyLoad(() => import('../components/AgentTestingLab'));
const MultiAgentCourseRecommendations = lazyLoad(
  () => import('../components/MultiAgentCourseRecommendations')
);
// Removed unused LearningInsights component

const PlaceholderPage = lazyLoad(
  () => import('../components/placeholders/PlaceholderPage')
);

// Admin Components - using lazyLoad helper
const AdminDashboardHome = lazyLoad(
  () => import('../features/admin/dashboard/EnhancedAdminDashboard')
);
const AdminCourseList = lazyLoad(
  () => import('../features/admin/courses/AdminCourseList')
);
const CourseForm = lazyLoad(
  () => import('../features/admin/courses/CourseForm')
);
const AdminCourseAssignmentsPage = lazyLoad(
  () => import('../features/admin/courses/AdminCourseAssignmentsPage')
);
const AdminCourseMaterialsPage = lazyLoad(
  () => import('../features/admin/courses/AdminCourseMaterialsPage')
);
const ContentPreferencesPage = lazyLoad(
  () => import('../features/admin/courses/ContentPreferencesPage')
);
const CourseContentPage = lazyLoad(
  () => import('../features/admin/courses/CourseContentPage')
);
const CourseProgressPage = lazyLoad(
  () => import('../features/admin/courses/CourseProgressPage')
);
const ContentAnalyticsDashboard = lazyLoad(
  () => import('../features/admin/courses/ContentAnalyticsDashboard')
);
const UsersList = lazyLoad(
  () => import('../features/admin/users/EnhancedUsersList')
);
const Settings = lazyLoad(
  () => import('../features/admin/settings/Settings.tsx')
);
const UserView = lazyLoad(() => import('../features/admin/UserView'));
const CreateUserForm = lazyLoad(
  () => import('../features/admin/users/EnhancedCreateUserForm')
);
const EditUserForm = lazyLoad(
  () => import('../features/admin/users/EditUserForm')
);
const PendingRegistrations = lazyLoad(
  () => import('../features/admin/PendingRegistrations')
);
const DepartmentsList = lazyLoad(
  () => import('../features/admin/departments/DepartmentsList')
);

const ManageAssessmentQuestions = lazyLoad(
  () => import('../features/admin/assessment/ManageAssessmentQuestions')
);
const StudentAssessments = lazyLoad(
  () => import('../features/admin/assessment/StudentAssessments')
);
const StudentQuizResponses = lazyLoad(
  () => import('../features/admin/assessment/StudentQuizResponses')
);
const AIAgentPage = lazyLoad(() => import('../pages/admin/AIAgentPage'));

// Schedule Components - using lazyLoad helper
const AdminSchedule = lazyLoad(
  () => import('../features/admin/schedule/AdminSchedule')
);
const AdminCalendar = lazyLoad(
  () => import('../features/admin/schedule/AdminCalendar')
);
const AdminEvents = lazyLoad(
  () => import('../features/admin/schedule/AdminEvents')
);
const ProfessorSchedule = lazyLoad(
  () => import('../features/professor/schedule/ProfessorSchedule')
);
const StudentSchedule = lazyLoad(
  () => import('../features/student/schedule/StudentSchedule')
);

// Skill Components - using lazyLoad helper
const SkillList = lazyLoad(
  () => import('../features/skills/components/SkillList')
);
const SkillForm = lazyLoad(
  () => import('../features/skills/components/SkillForm')
);

// Admin Settings Components - using lazyLoad helper
const UnifiedAIConfiguration = lazyLoad(
  () => import('../features/admin/settings/UnifiedAIConfiguration')
);

// Add declaration files for JSX components
// @ts-ignore

// Professor Components - using lazyLoad helper
const ProfessorDashboardHome = lazyLoad(
  () => import('../features/professor/ProfessorDashboardHome')
);
const ProfessorCoursesList = lazyLoad(
  () => import('../features/professor/ProfessorCoursesList')
);
const ProfessorGradesList = lazyLoad(
  () => import('../features/professor/ProfessorGradesList')
);
const ProfessorAttendance = lazyLoad(
  () => import('../features/professor/AttendanceTracking')
);
const ProfessorGrades = lazyLoad(
  () => import('../features/professor/ProfessorGrades')
);
const AttendanceAnalytics = lazyLoad(
  () => import('../features/professor/AttendanceAnalytics')
);

// Student Components - using lazyLoad helper
const StudentDashboardHome = lazyLoad(
  () => import('../features/student/StudentDashboardHome')
);
const LearningPathPage = lazyLoad(
  () => import('../features/student/LearningPathPage')
);
const LearningInsightsPage = lazyLoad(
  () => import('../features/student/LearningInsightsPage')
);
const StudentActivitiesPage = lazyLoad(
  () => import('../features/student/StudentActivitiesPage')
);
const StudentCourses = lazyLoad(
  () => import('../features/student/StudentCourses')
);
const CourseDetail = lazyLoad(() => import('../features/student/CourseDetail'));
const GradesAndAssignments = lazyLoad(
  () => import('../features/student/GradesAndAssignments')
);
const AttendanceRecord = lazyLoad(
  () => import('../features/student/AttendanceRecord')
);
const UnifiedCourseContentPageRefactored = lazyLoad(
  () => import('../features/student/UnifiedCourseContentPageRefactored')
);
const StudyTimePage = lazyLoad(
  () => import('../features/study-time/pages/StudyTimePage')
);
const StudentProgressPage = lazyLoad(
  () => import('../pages/StudentProgressPage')
);
const GPAStandings = lazyLoad(
  () => import('../components/academic/GPAStandings')
);

// Assessment imports - UnifiedAssessment is used for all assessment types

// Advanced Features Components
const AdvancedFeaturesDemo = lazyLoad(
  () => import('../components/AdvancedFeatures/AdvancedFeaturesDemo')
);
const PlagiarismDetectionPage = lazyLoad(
  () => import('../components/AdvancedFeatures/PlagiarismDetectionPage')
);
const VideoConferencingPage = lazyLoad(
  () => import('../components/AdvancedFeatures/VideoConferencingPage')
);
const PredictiveAnalyticsPage = lazyLoad(
  () => import('../components/AdvancedFeatures/PredictiveAnalyticsPage')
);
// Temporarily commented out to debug
// const AdaptiveLearningPage = lazyLoad(
//   () => import('../components/AdvancedFeatures/AdaptiveLearningPage')
// );

// Learning Components - using PlaceholderPage for missing components
// All progression pages are missing, using placeholders

// Helper function to create protected routes with the correct role permissions
const createProtectedRoute = (
  roles: UserRole | UserRole[],
  element: React.ReactNode
) => {
  return (
    <ProtectedRoute requiredRole={Array.isArray(roles) ? roles : [roles]}>
      <ToastProvider>{element}</ToastProvider>
    </ProtectedRoute>
  );
};

export const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <ToastProvider>
        <Outlet />
      </ToastProvider>
    ),
    errorElement: <NotFound />,
    children: [
      {
        index: true,
        element: <HomePage />,
      },
      {
        path: 'discover-path',
        element: (
          <Suspense fallback={<div>Loading...</div>}>
            <DiscoverPathPage />
          </Suspense>
        ),
      },
      // react-icons-demo route removed - component doesn't exist

      {
        path: 'unauthorized',
        element: <UnauthorizedPage />,
      },
      {
        path: 'login',
        element: (
          <Suspense fallback={<div>Loading...</div>}>
            <Login />
          </Suspense>
        ),
      },

      {
        path: 'register',
        element: (
          <Suspense fallback={<div>Loading...</div>}>
            <Registration />
          </Suspense>
        ),
      },

      {
        path: 'registration-success',
        element: (
          <Suspense fallback={<div>Loading...</div>}>
            <PublicRoute allowAuthenticated={true}>
              <RegistrationSuccess />
            </PublicRoute>
          </Suspense>
        ),
      },
      {
        path: 'public-assessment',
        element: (
          <Suspense fallback={<div>Loading...</div>}>
            <PublicRoute allowAuthenticated={true}>
              <UnifiedAssessment isPublic={true} />
            </PublicRoute>
          </Suspense>
        ),
      },
      {
        path: 'auth/level-assessment-success',
        element: (
          <Suspense fallback={<div>Loading...</div>}>
            <PublicRoute allowAuthenticated={true}>
              <TestAssessmentSuccess />
            </PublicRoute>
          </Suspense>
        ),
      },
      {
        path: 'specializations/marketing',
        element: (
          <Suspense fallback={<div>Loading...</div>}>
            <PublicRoute allowAuthenticated={true}>
              <MarketingPage />
            </PublicRoute>
          </Suspense>
        ),
      },
      {
        path: 'specializations/finance',
        element: (
          <Suspense fallback={<div>Loading...</div>}>
            <PublicRoute allowAuthenticated={true}>
              <FinancePage />
            </PublicRoute>
          </Suspense>
        ),
      },
      {
        path: 'specializations/programming',
        element: (
          <Suspense fallback={<div>Loading...</div>}>
            <PublicRoute allowAuthenticated={true}>
              <ProgrammingPage />
            </PublicRoute>
          </Suspense>
        ),
      },
      {
        path: 'specializations/cybersecurity',
        element: (
          <Suspense fallback={<div>Loading...</div>}>
            <PublicRoute allowAuthenticated={true}>
              <CybersecurityPage />
            </PublicRoute>
          </Suspense>
        ),
      },
      {
        path: 'test-assessment',
        element: (
          <Suspense fallback={<div>Loading...</div>}>
            <PublicRoute allowAuthenticated={true}>
              <TestAssessmentSuccess />
            </PublicRoute>
          </Suspense>
        ),
      },

      // Admin Routes
      {
        path: 'admin',
        element: createProtectedRoute(['ADMIN'], <DashboardLayout />),
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<div>Loading...</div>}>
                <AdminDashboardHome />
              </Suspense>
            ),
          },
          { path: 'users', element: <UsersList /> },
          { path: 'users/new', element: <CreateUserForm /> },
          { path: 'users/:id', element: <UserView /> },
          { path: 'users/:id/edit', element: <EditUserForm /> },
          // Removed duplicate level management route - now accessed from student-levels page
          {
            path: 'courses',
            element: <Outlet />,
            children: [
              { index: true, element: <AdminCourseList /> },
              { path: 'new', element: <CourseForm /> },
              { path: ':id/edit', element: <CourseForm /> },
              { path: ':courseId', element: <CourseViewRefactored /> },
              { path: ':courseId/view', element: <CourseViewRefactored /> },
              {
                path: ':courseId/assignments',
                element: <AdminCourseAssignmentsPage />,
              },
              {
                path: ':courseId/materials',
                element: <AdminCourseMaterialsPage />,
              },
              {
                path: ':courseId/content',
                element: <CourseContentPage />,
              },
              { path: ':courseId/progress', element: <CourseProgressPage /> },
              {
                path: ':courseId/analytics',
                element: <UnifiedAnalyticsPage />,
              },
              {
                path: ':courseId/content-preferences',
                element: <ContentPreferencesPage />,
              },
              {
                path: ':courseId/content-analytics',
                element: <ContentAnalyticsDashboard />,
              },
            ],
          },
          { path: 'registrations', element: <PendingRegistrations /> },
          { path: 'pending-students', element: <PendingRegistrations /> },
          { path: 'departments', element: <DepartmentsList /> },
          {
            path: 'assessment/questions',
            element: <ManageAssessmentQuestions />,
          },
          {
            path: 'assessment/student-assessments',
            element: <StudentAssessments />,
          },
          {
            path: 'assessment/student/:studentId/responses',
            element: <StudentQuizResponses />,
          },
          {
            path: 'assessment/responses/:assessmentId',
            element: <StudentQuizResponses />,
          },
          {
            path: 'settings/*',
            element: <Settings />,
          },
          { path: 'chat', element: <ChatPage /> },
          { path: 'multi-agent-chat', element: <RoleBasedMultiAgentChat /> },
          { path: 'chat-demo', element: <MultiAgentChatDemo /> },
          { path: 'agent-status', element: <AgentStatusDashboard /> },
          { path: 'agent-testing', element: <AgentTestingLab /> },
          {
            path: 'ai-course-recommendations',
            element: <MultiAgentCourseRecommendations />,
          },
          { path: 'ai-agent', element: <AIAgentPage /> },
          { path: 'api-key', element: <UnifiedAIConfiguration /> },
          {
            path: 'schedule',
            element: <Outlet />,
            children: [
              { index: true, element: <AdminSchedule /> },
              { path: 'calendar', element: <AdminCalendar /> },
              { path: 'events', element: <AdminEvents /> },
            ],
          },
          {
            path: 'course-generator',
            element: <Outlet />,
            children: [
              { index: true, element: <UnifiedGeneratorPage /> },
              { path: 'fallback-help', element: <FallbackContentHelp /> },
              { path: ':requestId/edit-content', element: <ContentEditor /> },
              // Redirects for backward compatibility
              {
                path: 'unified',
                element: <Navigate to='/admin/course-generator' replace />,
              },
            ],
          },
          {
            path: 'personalized-learning',
            element: <Outlet />,
            children: [
              { path: ':courseId', element: <PersonalizedLearningDashboard /> },
            ],
          },

          {
            path: 'assessment',
            children: [
              {
                path: 'questions',
                element: (
                  <Suspense fallback={<div>Loading...</div>}>
                    <ManageAssessmentQuestions />
                  </Suspense>
                ),
              },
              // Redirects for backward compatibility
              {
                path: 'manage-questions',
                element: <Navigate to='/admin/assessment/questions' replace />,
              },
              {
                path: 'placement-questions',
                element: (
                  <Navigate
                    to='/admin/assessment/questions?view=placement'
                    replace
                  />
                ),
              },
              {
                path: 'ai-decisions',
                element: (
                  <Suspense fallback={<div>Loading...</div>}>
                    <AIDecisionManager />
                  </Suspense>
                ),
              },
              {
                path: 'student-assessments',
                element: (
                  <Suspense fallback={<div>Loading...</div>}>
                    <StudentAssessments />
                  </Suspense>
                ),
              },
              {
                path: 'view/:assessmentId',
                element: (
                  <Suspense fallback={<div>Loading...</div>}>
                    <UnifiedAssessmentView />
                  </Suspense>
                ),
              },
              {
                path: 'preview',
                element: (
                  <Suspense fallback={<div>Loading...</div>}>
                    <AssessmentPreviewDashboard />
                  </Suspense>
                ),
              },
              {
                path: 'student-levels',
                element: (
                  <Suspense fallback={<div>Loading...</div>}>
                    <StudentLevelManagement />
                  </Suspense>
                ),
              },
              {
                path: 'level-mapping',
                element: (
                  <Suspense fallback={<div>Loading...</div>}>
                    <Typography variant='h4' sx={{ p: 4, textAlign: 'center' }}>
                      Level Mapping Tool has been removed
                    </Typography>
                  </Suspense>
                ),
              },
              {
                path: 'ai-analysis',
                element: (
                  <Suspense fallback={<div>Loading...</div>}>
                    <AIAssessmentAnalysis />
                  </Suspense>
                ),
              },
            ],
          },
          {
            path: 'assessments',
            element: <Navigate to='/admin/assessment/questions' replace />,
          },
          {
            path: 'skills',
            element: <Outlet />,
            children: [
              { index: true, element: <SkillList /> },
              { path: 'new', element: <SkillForm /> },
              { path: ':id/edit', element: <SkillForm /> },
            ],
          },
          { path: 'gpa-standings', element: <GPAStandings /> },
          // Advanced Features Routes
          {
            path: 'advanced-features',
            element: <Outlet />,
            children: [
              { index: true, element: <AdvancedFeaturesDemo /> },
              { path: 'plagiarism-detection', element: <PlagiarismDetectionPage /> },
              { path: 'video-conferencing', element: <VideoConferencingPage /> },
              { path: 'predictive-analytics', element: <PredictiveAnalyticsPage /> },
              { path: 'adaptive-learning', element: <PlaceholderPage title="Adaptive Learning" message="Adaptive Learning feature is temporarily disabled for debugging." /> },
            ],
          },
        ],
      },
      // Professor Routes
      {
        path: 'professor',
        element: createProtectedRoute(
          ['PROFESSOR', 'ADMIN'],
          <DashboardLayout />
        ),
        children: [
          { index: true, element: <ProfessorDashboardHome /> },
          {
            path: 'courses',
            element: <Outlet />,
            children: [
              { index: true, element: <ProfessorCoursesList /> },
              { path: ':courseId', element: <ProfessorCourseView /> },
              {
                path: ':courseId/content',
                element: <UnifiedCourseContentPage />,
              },
              { path: ':courseId/progress', element: <UnifiedProgressPage /> },
              {
                path: ':courseId/analytics',
                element: <UnifiedAnalyticsPage />,
              },
            ],
          },
          {
            path: 'attendance',
            element: <Outlet />,
            children: [
              { index: true, element: <ProfessorAttendance /> },
              { path: ':courseId', element: <ProfessorAttendance /> },
              { path: ':courseId/analytics', element: <AttendanceAnalytics /> },
            ],
          },
          {
            path: 'grades',
            element: <Outlet />,
            children: [
              { index: true, element: <ProfessorGradesList /> },
              { path: ':courseId', element: <ProfessorGrades /> },
            ],
          },
          { path: 'course-generator', element: <ImprovedCourseGenerator /> },
          { path: 'chat', element: <ChatPage /> },
          { path: 'multi-agent-chat', element: <RoleBasedMultiAgentChat /> },
          { path: 'chat-demo', element: <MultiAgentChatDemo /> },
          { path: 'agent-status', element: <AgentStatusDashboard /> },
          { path: 'agent-testing', element: <AgentTestingLab /> },
          {
            path: 'ai-course-recommendations',
            element: <MultiAgentCourseRecommendations />,
          },
          {
            path: 'schedule',
            element: <Outlet />,
            children: [{ index: true, element: <ProfessorSchedule /> }],
          },
          // Advanced Features Routes
          {
            path: 'advanced-features',
            element: <Outlet />,
            children: [
              { index: true, element: <AdvancedFeaturesDemo /> },
              { path: 'plagiarism-detection', element: <PlagiarismDetectionPage /> },
              { path: 'video-conferencing', element: <VideoConferencingPage /> },
              { path: 'predictive-analytics', element: <PredictiveAnalyticsPage /> },
              { path: 'adaptive-learning', element: <PlaceholderPage title="Adaptive Learning" message="Adaptive Learning feature is temporarily disabled for debugging." /> },
            ],
          },
        ],
      },
      // Student Routes
      {
        path: 'student',
        element: createProtectedRoute(['STUDENT'], <DashboardLayout />),
        children: [
          { index: true, element: <StudentDashboardHome /> },
          { path: 'learning-path', element: <LearningPathPage /> },
          {
            path: 'courses',
            element: <Outlet />,
            children: [
              { index: true, element: <UnifiedCoursesPage /> },
              { path: 'legacy', element: <StudentCourses /> },
              { path: ':courseId', element: <CourseDetail /> },
              {
                path: ':courseId/content',
                element: <UnifiedCourseContentPage />,
              },
              {
                path: ':courseId/simplified',
                element: <UnifiedCourseContentPageRefactored />,
              },
              { path: ':courseId/progress', element: <UnifiedProgressPage /> },
              {
                path: ':courseId/assessment',
                element: <UnifiedAssessment isPublic={true} />,
              },
            ],
          },

          { path: 'grades', element: <GradesAndAssignments /> },
          { path: 'attendance', element: <AttendanceRecord /> },
          { path: 'activities', element: <StudentActivitiesPage /> },
          { path: 'progress', element: <StudentProgressPage /> },
          { path: 'chat', element: <ChatPage /> },
          { path: 'multi-agent-chat', element: <RoleBasedMultiAgentChat /> },
          { path: 'chat-demo', element: <MultiAgentChatDemo /> },
          { path: 'agent-status', element: <AgentStatusDashboard /> },
          { path: 'agent-testing', element: <AgentTestingLab /> },
          {
            path: 'ai-course-recommendations',
            element: <MultiAgentCourseRecommendations />,
          },
          { path: 'study-time', element: <StudyTimePage /> },
          { path: 'study-assistant', element: <StudyAssistantPage /> },

          { path: 'learning-insights', element: <LearningInsightsPage /> },
          {
            path: 'schedule',
            element: <Outlet />,
            children: [{ index: true, element: <StudentSchedule /> }],
          },
          // Advanced Features Routes
          {
            path: 'advanced-features',
            element: <Outlet />,
            children: [
              { index: true, element: <AdvancedFeaturesDemo /> },
              { path: 'video-conferencing', element: <VideoConferencingPage /> },
              { path: 'adaptive-learning', element: <PlaceholderPage title="Adaptive Learning" message="Adaptive Learning feature is temporarily disabled for debugging." /> },
            ],
          },
        ],
      },
      {
        path: 'assessment',
        children: [
          {
            path: '',
            element: createProtectedRoute(['STUDENT'], <UnifiedAssessment />),
          },
          {
            path: ':assessmentId',
            element: createProtectedRoute(['STUDENT'], <UnifiedAssessment />),
          },
          {
            path: 'results/:assessmentId',
            element: createProtectedRoute(['STUDENT'], <UnifiedAssessment />),
          },
        ],
      },
    ],
  },
]);

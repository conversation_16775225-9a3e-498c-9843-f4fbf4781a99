"""
Django Admin configuration for blockchain credentials
"""

from django.contrib import admin
from django.utils.html import format_html
from .models import (
    BlockchainNetwork,
    CredentialTemplate,
    BlockchainCredential,
    NFTAchievement,
    CredentialVerification,
    SmartContract,
    WalletAddress
)


@admin.register(BlockchainNetwork)
class BlockchainNetworkAdmin(admin.ModelAdmin):
    list_display = ['name', 'network_type', 'is_testnet', 'is_active', 'gas_fee_estimate']
    list_filter = ['network_type', 'is_testnet', 'is_active']
    search_fields = ['name', 'network_type']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(CredentialTemplate)
class CredentialTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'credential_type', 'blockchain_enabled', 'is_active']
    list_filter = ['credential_type', 'blockchain_enabled', 'is_active']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(BlockchainCredential)
class BlockchainCredentialAdmin(admin.ModelAdmin):
    list_display = [
        'credential_id_short', 'title', 'student', 'status', 
        'issue_date', 'verification_count', 'blockchain_link'
    ]
    list_filter = ['status', 'template__credential_type', 'blockchain_network', 'is_public']
    search_fields = ['title', 'student__username', 'credential_id']
    readonly_fields = [
        'credential_id', 'blockchain_hash', 'verification_url', 
        'verification_count', 'last_verified', 'created_at', 'updated_at'
    ]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('credential_id', 'student', 'template', 'title', 'description')
        }),
        ('Academic Details', {
            'fields': ('course', 'assessment', 'final_grade', 'grade_percentage', 'skills_acquired')
        }),
        ('Blockchain Information', {
            'fields': (
                'blockchain_network', 'transaction_hash', 'smart_contract_address',
                'token_id', 'blockchain_hash', 'verification_url', 'ipfs_hash'
            )
        }),
        ('Status & Control', {
            'fields': ('status', 'is_public', 'is_transferable')
        }),
        ('Verification', {
            'fields': ('verification_count', 'last_verified')
        }),
        ('Timestamps', {
            'fields': ('issue_date', 'completion_date', 'expiry_date', 'created_at', 'updated_at')
        })
    )
    
    def credential_id_short(self, obj):
        return str(obj.credential_id)[:8] + '...'
    credential_id_short.short_description = 'Credential ID'
    
    def blockchain_link(self, obj):
        if obj.transaction_hash and obj.blockchain_network.explorer_url:
            url = f"{obj.blockchain_network.explorer_url}/tx/{obj.transaction_hash}"
            return format_html('<a href="{}" target="_blank">View on Blockchain</a>', url)
        return "Not on blockchain yet"
    blockchain_link.short_description = 'Blockchain'


@admin.register(NFTAchievement)
class NFTAchievementAdmin(admin.ModelAdmin):
    list_display = [
        'achievement_id_short', 'title', 'student', 'achievement_type', 
        'rarity', 'is_minted', 'earned_date'
    ]
    list_filter = ['achievement_type', 'rarity', 'is_minted', 'blockchain_network']
    search_fields = ['title', 'student__username', 'achievement_id']
    readonly_fields = [
        'achievement_id', 'earned_date', 'created_at', 'updated_at'
    ]
    
    def achievement_id_short(self, obj):
        return str(obj.achievement_id)[:8] + '...'
    achievement_id_short.short_description = 'Achievement ID'


@admin.register(CredentialVerification)
class CredentialVerificationAdmin(admin.ModelAdmin):
    list_display = [
        'credential_title', 'verification_type', 'is_verified', 
        'verifier_organization', 'verified_at'
    ]
    list_filter = ['verification_type', 'is_verified', 'verified_at']
    search_fields = ['credential__title', 'verifier_email', 'verifier_organization']
    readonly_fields = ['verified_at']
    
    def credential_title(self, obj):
        return obj.credential.title
    credential_title.short_description = 'Credential'


@admin.register(SmartContract)
class SmartContractAdmin(admin.ModelAdmin):
    list_display = ['name', 'contract_type', 'blockchain_network', 'is_active', 'deployment_date']
    list_filter = ['contract_type', 'blockchain_network', 'is_active']
    search_fields = ['name', 'contract_address']
    readonly_fields = ['deployment_date', 'created_at', 'updated_at']


@admin.register(WalletAddress)
class WalletAddressAdmin(admin.ModelAdmin):
    list_display = ['user', 'address_short', 'wallet_type', 'blockchain_network', 'is_verified', 'is_primary']
    list_filter = ['wallet_type', 'blockchain_network', 'is_verified', 'is_primary']
    search_fields = ['user__username', 'address']
    readonly_fields = ['verified_at', 'created_at', 'updated_at']
    
    def address_short(self, obj):
        return f"{obj.address[:10]}...{obj.address[-6:]}"
    address_short.short_description = 'Address'

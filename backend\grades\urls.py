from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views
from .plagiarism_views import PlagiarismViewSet
from .views import ProfessorGradeViewSet

router = DefaultRouter()
router.register("assignments", views.AssignmentViewSet, basename="assignments")
router.register("submissions", views.SubmissionViewSet, basename="submissions")
router.register("grades", views.GradeViewSet, basename="grades")
router.register("course-grades", views.CourseGradeViewSet, basename="course-grades")
router.register("plagiarism", PlagiarismViewSet, basename="plagiarism")

urlpatterns = [
    path("", include(router.urls)),
    path("student/grades/my_grades/", views.GradeViewSet.as_view({"get": "my_grades"}), name="my-grades"),
    path("course-grades/professor_courses/", views.CourseGradeViewSet.as_view({"get": "professor_courses"}), name="professor-courses"),
    path("<int:pk>/continue-iteration/", ProfessorGradeViewSet.as_view({"post": "continue_iteration"}), name="continue-iteration"),

    # Submission plagiarism endpoints
    path("submissions/<int:pk>/scan-plagiarism/", views.SubmissionViewSet.as_view({"post": "scan_for_plagiarism"}), name="scan-plagiarism"),
    path("submissions/<int:pk>/plagiarism-results/", views.SubmissionViewSet.as_view({"get": "plagiarism_results"}), name="plagiarism-results"),
]
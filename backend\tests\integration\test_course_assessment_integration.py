"""
Integration tests for course and assessment systems.
"""

import pytest
from django.test import TransactionTestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch
import json

from courses.models import Course, Department, Enrollment, Material
from assessment.models import Assessment, AssessmentQuestion, AssessmentResponse

User = get_user_model()


@pytest.mark.integration
class CourseAssessmentIntegrationTest(TransactionTestCase):
    """Integration tests for course and assessment workflows."""
    
    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        
        # Create users
        self.student = User.objects.create_user(
            username='student',
            email='<EMAIL>',
            password='testpass123',
            role='STUDENT'
        )
        
        self.professor = User.objects.create_user(
            username='professor',
            email='<EMAIL>',
            password='testpass123',
            role='PROFESSOR'
        )
        
        # Create department
        self.department = Department.objects.create(
            name='Computer Science',
            code='CS',
            description='Computer Science Department'
        )
        
        # Create course
        self.course = Course.objects.create(
            title='Introduction to Programming',
            code='CS101',
            description='Learn programming fundamentals',
            credits=3,
            level='BEGINNER',
            department=self.department,
            instructor=self.professor
        )
    
    def authenticate_user(self, user):
        """Helper method to authenticate a user."""
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        return access_token
    
    def test_complete_course_enrollment_flow(self):
        """Test complete course enrollment workflow."""
        # Authenticate as student
        self.authenticate_user(self.student)
        
        # 1. List available courses
        courses_url = reverse('courses:course-list')
        courses_response = self.client.get(courses_url)
        
        self.assertEqual(courses_response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(courses_response.data['results']), 1)
        self.assertEqual(courses_response.data['results'][0]['title'], self.course.title)
        
        # 2. Get course details
        course_detail_url = reverse('courses:course-detail', kwargs={'pk': self.course.pk})
        detail_response = self.client.get(course_detail_url)
        
        self.assertEqual(detail_response.status_code, status.HTTP_200_OK)
        self.assertEqual(detail_response.data['title'], self.course.title)
        
        # 3. Enroll in course
        enroll_url = reverse('courses:course-enroll', kwargs={'pk': self.course.pk})
        enroll_response = self.client.post(enroll_url)
        
        self.assertEqual(enroll_response.status_code, status.HTTP_200_OK)
        
        # Verify enrollment in database
        enrollment = Enrollment.objects.get(student=self.student, course=self.course)
        self.assertEqual(enrollment.enrollment_status, 'ENROLLED')
        
        # 4. Get enrolled courses
        enrolled_url = reverse('courses:student-courses')
        enrolled_response = self.client.get(enrolled_url)
        
        self.assertEqual(enrolled_response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(enrolled_response.data['results']), 1)
        self.assertEqual(enrolled_response.data['results'][0]['course']['title'], self.course.title)
    
    def test_course_material_access_flow(self):
        """Test course material access workflow."""
        # Enroll student in course
        Enrollment.objects.create(
            student=self.student,
            course=self.course,
            enrollment_status='ENROLLED'
        )
        
        # Create course material
        material = Material.objects.create(
            course=self.course,
            title='Lecture 1: Introduction',
            description='Introduction to programming concepts',
            uploaded_by=self.professor
        )
        
        # Authenticate as student
        self.authenticate_user(self.student)
        
        # 1. Access course materials
        materials_url = reverse('courses:course-materials', kwargs={'pk': self.course.pk})
        materials_response = self.client.get(materials_url)
        
        self.assertEqual(materials_response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(materials_response.data), 1)
        self.assertEqual(materials_response.data[0]['title'], material.title)
        
        # 2. Get specific material
        material_detail_url = reverse('courses:material-detail', kwargs={'pk': material.pk})
        material_response = self.client.get(material_detail_url)
        
        self.assertEqual(material_response.status_code, status.HTTP_200_OK)
        self.assertEqual(material_response.data['title'], material.title)
    
    def test_assessment_creation_and_taking_flow(self):
        """Test complete assessment creation and taking workflow."""
        # Enroll student in course
        enrollment = Enrollment.objects.create(
            student=self.student,
            course=self.course,
            enrollment_status='ENROLLED'
        )
        
        # Authenticate as professor
        self.authenticate_user(self.professor)
        
        # 1. Create assessment
        assessment_data = {
            'title': 'Programming Quiz 1',
            'description': 'Test your programming knowledge',
            'assessment_type': 'QUIZ',
            'course': self.course.id,
            'time_limit': 60,
            'max_attempts': 3,
            'passing_score': 70
        }
        
        assessments_url = reverse('assessment:assessment-list')
        assessment_response = self.client.post(
            assessments_url, assessment_data, format='json'
        )
        
        self.assertEqual(assessment_response.status_code, status.HTTP_201_CREATED)
        assessment_id = assessment_response.data['id']
        
        # 2. Add questions to assessment
        question_data = {
            'assessment': assessment_id,
            'question_text': 'What is a variable in programming?',
            'question_type': 'MULTIPLE_CHOICE',
            'points': 10,
            'options': [
                {'text': 'A storage location', 'is_correct': True},
                {'text': 'A function', 'is_correct': False},
                {'text': 'A loop', 'is_correct': False},
                {'text': 'A condition', 'is_correct': False}
            ]
        }
        
        questions_url = reverse('assessment:question-list')
        question_response = self.client.post(
            questions_url, question_data, format='json'
        )
        
        self.assertEqual(question_response.status_code, status.HTTP_201_CREATED)
        question_id = question_response.data['id']
        
        # Switch to student
        self.authenticate_user(self.student)
        
        # 3. Student views available assessments
        student_assessments_url = reverse('assessment:student-assessments')
        student_assessments_response = self.client.get(student_assessments_url)
        
        self.assertEqual(student_assessments_response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(student_assessments_response.data['results']), 1)
        
        # 4. Start assessment
        start_url = reverse('assessment:assessment-start', kwargs={'pk': assessment_id})
        start_response = self.client.post(start_url)
        
        self.assertEqual(start_response.status_code, status.HTTP_200_OK)
        
        # 5. Submit answers
        submission_data = {
            'responses': [
                {
                    'question_id': question_id,
                    'answer': 'A storage location'
                }
            ]
        }
        
        submit_url = reverse('assessment:assessment-submit', kwargs={'pk': assessment_id})
        submit_response = self.client.post(
            submit_url, submission_data, format='json'
        )
        
        self.assertEqual(submit_response.status_code, status.HTTP_200_OK)
        
        # 6. Get results
        results_url = reverse('assessment:assessment-results', kwargs={'pk': assessment_id})
        results_response = self.client.get(results_url)
        
        self.assertEqual(results_response.status_code, status.HTTP_200_OK)
        self.assertEqual(results_response.data['score'], 100.0)  # Correct answer
        
        # Verify assessment completion in database
        assessment = Assessment.objects.get(id=assessment_id)
        self.assertTrue(assessment.completed)
        self.assertEqual(assessment.score, 100.0)
    
    @patch('utils.ai_service_improved.AIService')
    def test_ai_powered_assessment_flow(self, mock_ai_service):
        """Test AI-powered assessment features integration."""
        # Setup AI service mock
        mock_ai_instance = mock_ai_service.return_value
        mock_ai_instance.generate_questions.return_value = {
            'questions': [
                {
                    'question_text': 'AI generated question about loops',
                    'question_type': 'MULTIPLE_CHOICE',
                    'options': ['Option A', 'Option B', 'Option C', 'Option D'],
                    'correct_answer': 'Option A',
                    'points': 10
                }
            ]
        }
        
        # Authenticate as professor
        self.authenticate_user(self.professor)
        
        # 1. Generate AI questions
        ai_questions_data = {
            'topic': 'Programming Loops',
            'difficulty': 'BEGINNER',
            'count': 5
        }
        
        ai_questions_url = reverse('assessment:ai-generate-questions')
        ai_response = self.client.post(
            ai_questions_url, ai_questions_data, format='json'
        )
        
        self.assertEqual(ai_response.status_code, status.HTTP_200_OK)
        self.assertIn('questions', ai_response.data)
        
        # 2. Create assessment with AI questions
        assessment_data = {
            'title': 'AI Generated Quiz',
            'description': 'Quiz with AI generated questions',
            'assessment_type': 'QUIZ',
            'course': self.course.id,
            'use_ai_questions': True,
            'ai_topic': 'Programming Loops'
        }
        
        assessments_url = reverse('assessment:assessment-list')
        assessment_response = self.client.post(
            assessments_url, assessment_data, format='json'
        )
        
        self.assertEqual(assessment_response.status_code, status.HTTP_201_CREATED)
    
    def test_grade_calculation_integration(self):
        """Test grade calculation across courses and assessments."""
        # Enroll student in course
        enrollment = Enrollment.objects.create(
            student=self.student,
            course=self.course,
            enrollment_status='ENROLLED'
        )
        
        # Create multiple assessments
        assessments = []
        for i in range(3):
            assessment = Assessment.objects.create(
                student=self.student,
                course=self.course,
                title=f'Quiz {i+1}',
                assessment_type='QUIZ',
                completed=True,
                score=80 + (i * 5)  # Scores: 80, 85, 90
            )
            assessments.append(assessment)
        
        # Authenticate as student
        self.authenticate_user(self.student)
        
        # Get course progress
        progress_url = reverse('courses:course-progress', kwargs={'pk': self.course.pk})
        progress_response = self.client.get(progress_url)
        
        self.assertEqual(progress_response.status_code, status.HTTP_200_OK)
        
        # Verify grade calculation
        expected_average = (80 + 85 + 90) / 3  # 85.0
        self.assertEqual(progress_response.data['average_score'], expected_average)
        self.assertEqual(progress_response.data['completed_assessments'], 3)
    
    def test_course_completion_flow(self):
        """Test complete course completion workflow."""
        # Enroll student in course
        enrollment = Enrollment.objects.create(
            student=self.student,
            course=self.course,
            enrollment_status='ENROLLED'
        )
        
        # Create required assessments for completion
        final_exam = Assessment.objects.create(
            student=self.student,
            course=self.course,
            title='Final Exam',
            assessment_type='EXAM',
            is_final=True,
            passing_score=70,
            completed=True,
            score=85
        )
        
        # Authenticate as student
        self.authenticate_user(self.student)
        
        # Check course completion status
        completion_url = reverse('courses:check-completion', kwargs={'pk': self.course.pk})
        completion_response = self.client.get(completion_url)
        
        self.assertEqual(completion_response.status_code, status.HTTP_200_OK)
        self.assertTrue(completion_response.data['is_completed'])
        self.assertEqual(completion_response.data['final_grade'], 85)
        
        # Update enrollment status
        enrollment.refresh_from_db()
        self.assertEqual(enrollment.enrollment_status, 'COMPLETED')
    
    def test_cross_system_permissions(self):
        """Test permissions across course and assessment systems."""
        # Create another student
        other_student = User.objects.create_user(
            username='otherstudent',
            email='<EMAIL>',
            password='testpass123',
            role='STUDENT'
        )
        
        # Enroll first student only
        enrollment = Enrollment.objects.create(
            student=self.student,
            course=self.course,
            enrollment_status='ENROLLED'
        )
        
        # Create assessment for enrolled student
        assessment = Assessment.objects.create(
            student=self.student,
            course=self.course,
            title='Private Assessment',
            assessment_type='QUIZ'
        )
        
        # Authenticate as other student (not enrolled)
        self.authenticate_user(other_student)
        
        # Try to access course materials
        materials_url = reverse('courses:course-materials', kwargs={'pk': self.course.pk})
        materials_response = self.client.get(materials_url)
        
        self.assertEqual(materials_response.status_code, status.HTTP_403_FORBIDDEN)
        
        # Try to access assessment
        assessment_url = reverse('assessment:assessment-detail', kwargs={'pk': assessment.pk})
        assessment_response = self.client.get(assessment_url)
        
        self.assertEqual(assessment_response.status_code, status.HTTP_403_FORBIDDEN)
        
        # Authenticate as enrolled student
        self.authenticate_user(self.student)
        
        # Should have access
        materials_response = self.client.get(materials_url)
        self.assertEqual(materials_response.status_code, status.HTTP_200_OK)
        
        assessment_response = self.client.get(assessment_url)
        self.assertEqual(assessment_response.status_code, status.HTTP_200_OK)

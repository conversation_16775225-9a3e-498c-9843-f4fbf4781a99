from rest_framework import viewsets, status, serializers
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from rest_framework.permissions import IsAuthenticated

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'role', 'first_name', 'last_name', 'is_active', 'date_joined']
        read_only_fields = ['id', 'date_joined']

class UserViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing users
    """
    queryset = User.objects.all()
    permission_classes = [IsAuthenticated]
    serializer_class = UserSerializer
    
    def list(self, request):
        users = self.queryset.all()
        data = []
        for user in users:
            data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'role': user.role,
                'first_name': user.first_name,
                'last_name': user.last_name,
            })
        return Response(data)
    
    def retrieve(self, request, pk=None):
        """Get individual user details"""
        try:
            user = self.queryset.get(pk=pk)
            data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'role': user.role,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'is_active': user.is_active,
                'date_joined': user.date_joined,
            }
            return Response(data)
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=404)
    
    @action(detail=False, methods=['get'])
    def professors(self, request):
        professors = self.queryset.filter(role='PROFESSOR')
        data = []
        for user in professors:
            data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'role': user.role,
                'first_name': user.first_name,
                'last_name': user.last_name,
            })
        return Response(data)
    
    @action(detail=False, methods=['get'])
    def students(self, request):
        students = self.queryset.filter(role='STUDENT')
        data = []
        for user in students:
            data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'role': user.role,
                'first_name': user.first_name,
                'last_name': user.last_name,
            })
        return Response(data)

class UserActivityViewSet(viewsets.ModelViewSet):
    """
    ViewSet for user activities - simplified for now
    """
    permission_classes = [IsAuthenticated]
    serializer_class = UserSerializer  # Use same serializer for now
    queryset = User.objects.none()  # Empty queryset for now
    
    def list(self, request):
        return Response({"status": "success", "data": [], "message": "No user activities yet"})  # Return empty for now

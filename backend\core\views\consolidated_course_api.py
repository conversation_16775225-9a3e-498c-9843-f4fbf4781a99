"""ConsolidatedCourseAPIViewsThisreplacestheduplicatecourseAPIsacrossall3appswithasingleunifiedAPIthatprovidesconsistentdataregardlessofwhichappfeaturesarebeingused.Replaces:-courses/views/standardized_student_views.py-interactive_learningcourseviews-course_generatorcourseviews-Multipleduplicateendpoints"""fromrest_frameworkimportviewsetsstatusfiltersfromrest_framework.decoratorsimportactionfromrest_framework.responseimportResponsefromrest_framework.permissionsimportIsAuthenticatedIsAdminUserfromdjango_filters.rest_frameworkimportDjangoFilterBackendfrom django.db.modelsimportQPrefetchfrom django.contrib.authimportget_user_model#Importbaseserializersfromtheserializerspackagetry:from core.serializers.base_course_serializersimport(UnifiedCourseSerializerBaseCourseProgressSerializer)exceptImportError:#FallbacktouseanyavailablecourseserializerUnifiedCourseSerializer=NoneBaseCourseProgressSerializer=Nonefrom core.services.unified_course_serviceimportunified_course_servicefrom core.utils.api_responseimportsuccess_responseerror_responseUser=get_user_model()classConsolidatedCourseViewSet(viewsets.ModelViewSet):"""UnifiedCourseAPIthatreplacesallduplicatecourseendpointsThissingleViewSetprovides:-Courselistingwithfiltering-Coursedetailswithallappdata-Enrollmentmanagement-Progresstrackingacrossallapps-Searchandfiltering"""serializer_class=UnifiedCourseSerializerpermission_classes=[IsAuthenticated]filter_backends=[DjangoFilterBackendfilters.SearchFilterfilters.OrderingFilter]filterset_fields=['semester''academic_year''department''required_level''has_interactive_content''has_ai_content''has_assessment']search_fields=['title''course_code''description']ordering_fields=['title''course_code''required_level''credits']ordering=['course_code']defget_queryset(self):"""GetcourseswithoptimizedqueriesandfilteringThisreplacesmultipleseparatequerysetsindifferentapps"""from django.appsimportappsCourse=apps.get_model('courses''Course')#Basequerysetwithoptimizationsqueryset=Course.objects.select_related('department''instructor').prefetch_related('enrollments''materials''prerequisites')#Filterbyuseraccessleveluser=self.request.user#Getuser'scurrentleveltry:from core.modelsimportStudentLevelstudent_level=StudentLevel.objects.get(student=user)current_level=student_level.current_levelexcept:current_level=1#Applyfiltersbasedonactionaction=self.actionfilter_param=self.request.query_params.get('filter''all')ifaction=='my_courses':#Onlyenrolledcoursesfrom django.appsimportappsEnrollment=apps.get_model('courses''Enrollment')enrolled_ids=Enrollment.objects.filter(user=userstatus='APPROVED').values_list('course_id'flat=True)queryset=queryset.filter(id__in=enrolled_ids)elifaction=='available_courses':#Availablecoursesnotenrolledinfrom django.appsimportappsEnrollment=apps.get_model('courses''Enrollment')enrolled_ids=Enrollment.objects.filter(user=user).values_list('course_id'flat=True)queryset=queryset.exclude(id__in=enrolled_ids)queryset=queryset.filter(is_active=Trueis_published=Truerequired_level__lte=current_level)eliffilter_param=='enrolled':#Filterenrolledcoursesinlistviewfrom django.appsimportappsEnrollment=apps.get_model('courses''Enrollment')enrolled_ids=Enrollment.objects.filter(user=userstatus='APPROVED').values_list('course_id'flat=True)queryset=queryset.filter(id__in=enrolled_ids)eliffilter_param=='available':#Filteravailablecoursesinlistviewfrom django.appsimportappsEnrollment=apps.get_model('courses''Enrollment')enrolled_ids=Enrollment.objects.filter(user=user).values_list('course_id'flat=True)queryset=queryset.exclude(id__in=enrolled_ids)queryset=queryset.filter(is_active=Trueis_published=Truerequired_level__lte=current_level)eliffilter_param=='recommended':#Recommendedcoursesbasedonlevelfrom django.appsimportappsEnrollment=apps.get_model('courses''Enrollment')enrolled_ids=Enrollment.objects.filter(user=user).values_list('course_id'flat=True)queryset=queryset.exclude(id__in=enrolled_ids)queryset=queryset.filter(recommended_level=current_levelis_active=Trueis_published=True)else:#Allaccessiblecoursesqueryset=queryset.filter(Q(required_level__lte=current_level)|Q(is_published=True)is_active=True)returnqueryset.distinct()defretrieve(selfrequest*args**kwargs):"""GetdetailedcoursedatawithallappfeaturesThisreplacesmultipleretrievemethodsacrossapps"""course=self.get_object()#Getunifiedcoursedatacourse_data=unified_course_service.get_unified_course_data(course.course_code)if'error'incourse_data:returnerror_response(course_data['error']status_code=404)returnsuccess_response(course_data)@action(detail=Falsemethods=['get'])defmy_courses(selfrequest):"""Getuser'senrolledcourseswithprogressThisreplacesseparatestudentcourseendpoints"""queryset=self.get_queryset()page=self.paginate_queryset(queryset)courses_data=[]forcoursein(pageifpageisnotNoneelsequeryset):#Getunifiedcoursedatacourse_data=unified_course_service.get_unified_course_data(course.course_code)if'error'notincourse_data:#Addenrollmentandprogressdatatry:from django.appsimportappsEnrollment=apps.get_model('courses''Enrollment')enrollment=Enrollment.objects.get(user=request.usercourse=course)course_data['enrollment']={'enrollment_date':enrollment.enrollment_date'enrollment_type':enrollment.enrollment_type'status':enrollment.status}#Addprogressdataprogress_data=unified_course_service.get_student_unified_progress(user=request.usercourse_code=course.course_code)if'error'notinprogress_data:course_data['progress']=progress_dataexcept:passcourses_data.append(course_data)ifpageisnotNone:returnself.get_paginated_response(courses_data)returnsuccess_response({'courses':courses_data'total_count':len(courses_data)})@action(detail=Falsemethods=['get'])defavailable_courses(selfrequest):"""GetcoursesavailableforenrollmentThisreplacesseparateavailablecourseendpoints"""queryset=self.get_queryset()page=self.paginate_queryset(queryset)courses_data=[]forcoursein(pageifpageisnotNoneelsequeryset):course_data=unified_course_service.get_unified_course_data(course.course_code)if'error'notincourse_data:courses_data.append(course_data)ifpageisnotNone:returnself.get_paginated_response(courses_data)returnsuccess_response({'available_courses':courses_data'total_count':len(courses_data)})@action(detail=Truemethods=['post'])defenroll(selfrequestpk=None):"""EnrollinacourseusingunifiedserviceThisreplacesmultipleenrollmentendpoints"""course=self.get_object()enrollment_type=request.data.get('enrollment_type''REGULAR')result=unified_course_service.enroll_student_unified(user=request.usercourse_code=course.course_codeenrollment_type=enrollment_type)ifresult['success']:returnsuccess_response(result)else:returnerror_response(result['error']status_code=400)@action(detail=Truemethods=['get'])defprogress(selfrequestpk=None):"""GetunifiedprogressforacourseThisreplacesmultipleprogressendpoints"""course=self.get_object()progress_data=unified_course_service.get_student_unified_progress(user=request.usercourse_code=course.course_code)if'error'inprogress_data:returnerror_response(progress_data['error']status_code=404)returnsuccess_response(progress_data)@action(detail=Falsemethods=['get'])defsearch(selfrequest):"""AdvancedcoursesearchacrossallfeaturesThisreplacesmultiplesearchendpoints"""query=request.query_params.get('q''')feature_filter=request.query_params.get('features''all')#allinteractiveaiassessmentifnotquery:returnerror_response('Searchqueryisrequired'status_code=400)#Basesearchqueryset=self.get_queryset()queryset=queryset.filter(Q(title__icontains=query)|Q(course_code__icontains=query)|Q(description__icontains=query))#Filterbyfeaturesiffeature_filter=='interactive':queryset=queryset.filter(has_interactive_content=True)eliffeature_filter=='ai':queryset=queryset.filter(has_ai_content=True)eliffeature_filter=='assessment':queryset=queryset.filter(has_assessment=True)#Getresultspage=self.paginate_queryset(queryset)courses_data=[]forcoursein(pageifpageisnotNoneelsequeryset):course_data=unified_course_service.get_unified_course_data(course.course_code)if'error'notincourse_data:courses_data.append(course_data)ifpageisnotNone:returnself.get_paginated_response(courses_data)returnsuccess_response({'results':courses_data'query':query'total_count':len(courses_data)})@action(detail=Falsemethods=['get'])deffeatures_overview(selfrequest):"""GetoverviewofcoursefeaturesacrossallappsNewendpointthatshowsintegrationstatus"""from django.appsimportappsCourse=apps.get_model('courses''Course')stats={'total_courses':Course.objects.count()'interactive_courses':Course.objects.filter(has_interactive_content=True).count()'ai_courses':Course.objects.filter(has_ai_content=True).count()'assessment_courses':Course.objects.filter(has_assessment=True).count()'multi_feature_courses':Course.objects.filter(Q(has_interactive_content=True)&(Q(has_ai_content=True)|Q(has_assessment=True))).count()}returnsuccess_response(stats)classConsolidatedEnrollmentViewSet(viewsets.ModelViewSet):"""UnifiedEnrollmentAPIthatreplacesduplicateenrollmentendpoints"""#UseasimpleserializersinceUnifiedEnrollmentSerializerisnotyetdefinedserializer_class=Nonepermission_classes=[IsAuthenticated]filter_backends=[DjangoFilterBackendfilters.OrderingFilter]filterset_fields=['status''enrollment_type''course__semester']ordering=['-enrollment_date']defget_queryset(self):"""Getuser'senrollments"""from django.appsimportappsEnrollment=apps.get_model('courses''Enrollment')returnEnrollment.objects.filter(user=self.request.user).select_related('course''course__department').prefetch_related('course__materials')@action(detail=Truemethods=['post'])defwithdraw(selfrequestpk=None):"""Withdrawfromacourse"""enrollment=self.get_object()ifenrollment.statusnotin['APPROVED''PENDING']:returnerror_response('Cannotwithdrawfromthisenrollment'status_code=400)enrollment.status='DROPPED'enrollment.save()returnsuccess_response({'message':'Successfullywithdrawnfromcourse''enrollment_id':enrollment.id})classConsolidatedProgressViewSet(viewsets.ReadOnlyModelViewSet):"""UnifiedProgressAPIthatshowsprogressacrossallcoursefeatures"""serializer_class=BaseCourseProgressSerializerpermission_classes=[IsAuthenticated]defget_queryset(self):"""Getuser'scourseprogress"""from django.appsimportappsCourseProgress=apps.get_model('courses''CourseProgress')returnCourseProgress.objects.filter(user=self.request.user).select_related('course')@action(detail=Falsemethods=['get'])defsummary(selfrequest):"""Getprogresssummaryacrossallenrolledcourses"""queryset=self.get_queryset()summary_data=[]forprogressinqueryset:progress_data=unified_course_service.get_student_unified_progress(user=request.usercourse_code=progress.course.course_code)if'error'notinprogress_data:summary_data.append(progress_data)#Calculateoverallstatsifsummary_data:total_courses=len(summary_data)completed_courses=sum(1forpinsummary_dataifp.get('main_progress'{}).get('is_completed'False))avg_completion=sum(p.get('main_progress'{}).get('completion_percentage'0)forpinsummary_data)/total_coursesstats={'total_enrolled':total_courses'completed_courses':completed_courses'in_progress':total_courses-completed_courses'average_completion':round(avg_completion2)'courses':summary_data}else:stats={'total_enrolled':0'completed_courses':0'in_progress':0'average_completion':0'courses':[]}returnsuccess_response(stats)
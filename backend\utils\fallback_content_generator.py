"""Fallbackcontentgeneratorforcoursegeneration.ThismoduleprovidesfunctionstogeneratefallbackcontentwhentheAIserviceisunavailable.ItusestheFallbackAIServicetocreatepre-generatedcontentforcourses."""import json
import loggingfrom course_generator.modelsimportGeneratedCourseContentfrom utils.ai.servicesimportget_ai_servicefrom utils.monitoringimporttrack_fallback_content_usagelogger=logging.getLogger(__name__)defenhance_domain_specific_content(fallback_datadepartment):"""Enhancefallbackcontentwithdomain-specificinformationbasedondepartment.Args:fallback_data:Thebasefallbackcontentdepartment:ThedepartmentnameReturns:Enhancedfallbackcontentwithdomain-specificinformation"""#Normalizedepartmentnametohandledifferentformatsdepartment=department.lower()ifdepartmentelse"general"#Domain-specificenhancementsfordifferentdepartmentsdomain_enhancements={"computerscience":{"weekly_schedule":[{"week":1"topic":"IntroductiontoProgrammingConcepts""description":"Overviewoffundamentalprogrammingconceptsandcomputationalthinking.""learning_objectives":["Understandbasicprogrammingconstructs""Applycomputationalthinkingtoproblem-solving""Writesimplealgorithms"]}{"week":2"topic":"DataStructuresandAlgorithms""description":"Exploringfundamentaldatastructuresandalgorithmdesign.""learning_objectives":["Implementbasicdatastructures""Analyzealgorithmefficiency""Applyappropriatedatastructurestoproblems"]}]"assessment_methods":[{"method":"ProgrammingAssignments""weight":"40%""description":"Weeklyprogrammingassignmentstoapplyconceptslearnedinclass."}{"method":"TechnicalDocumentation""weight":"15%""description":"Documentationofcodeanddesigndecisions."}]"skills_gained":[{"skill":"ProgrammingProficiency""description":"Abilitytowriteefficientmaintainablecodeinrelevantprogramminglanguages."}{"skill":"Problem-Solving""description":"Systematicapproachtoanalyzingandsolvingcomputationalproblems."}]}"business":{"weekly_schedule":[{"week":1"topic":"BusinessFundamentalsandStrategy""description":"Introductiontocorebusinessconceptsandstrategicthinking.""learning_objectives":["Understandkeybusinessframeworks""Analyzebusinessenvironments""Developstrategicthinkingskills"]}{"week":2"topic":"MarketAnalysisandResearch""description":"Methodsforanalyzingmarketsandconductingbusinessresearch.""learning_objectives":["Conductmarketanalysis""Interpretbusinessdata""Makedata-drivendecisions"]}]"assessment_methods":[{"method":"CaseStudies""weight":"35%""description":"Analysisofreal-worldbusinessscenariosanddecision-making."}{"method":"BusinessPlan""weight":"25%""description":"Developmentofacomprehensivebusinessplanforaproductorservice."}]"skills_gained":[{"skill":"StrategicThinking""description":"Abilitytoanalyzebusinessenvironmentsanddevelopeffectivestrategies."}{"skill":"BusinessCommunication""description":"Clearandeffectivecommunicationinbusinesscontexts."}]}"engineering":{"weekly_schedule":[{"week":1"topic":"EngineeringPrinciplesandDesign""description":"Fundamentalprinciplesofengineeringanddesignmethodology.""learning_objectives":["Applyengineeringprinciplestoproblem-solving""Understanddesignconstraintsandrequirements""Developsystematicdesignapproaches"]}{"week":2"topic":"AnalysisandModeling""description":"Techniquesforanalyzingandmodelingengineeringsystems.""learning_objectives":["Createmathematicalmodelsofsystems""Analyzesystembehavior""Validatemodelsagainstrequirements"]}]"assessment_methods":[{"method":"DesignProjects""weight":"40%""description":"Engineeringdesignprojectswithdocumentationandpresentation."}{"method":"TechnicalReports""weight":"20%""description":"Detailedtechnicalreportsonengineeringanalysisanddesign."}]"skills_gained":[{"skill":"TechnicalProblem-Solving""description":"Systematicapproachtosolvingcomplexengineeringproblems."}{"skill":"EngineeringDesign""description":"Abilitytodesignsystemscomponentsorprocessestomeetspecificneeds."}]}"mathematics":{"weekly_schedule":[{"week":1"topic":"MathematicalFoundations""description":"Coremathematicalconceptsandprooftechniques.""learning_objectives":["Understandfundamentalmathematicalprinciples""Developlogicalreasoningskills""Constructmathematicalproofs"]}{"week":2"topic":"AppliedMathematics""description":"Applicationsofmathematicalconceptstoreal-worldproblems.""learning_objectives":["Applymathematicalmodelstopracticalproblems""Interpretmathematicalresultsincontext""Developcomputationalapproaches"]}]"assessment_methods":[{"method":"ProblemSets""weight":"40%""description":"Weeklyproblemsetsrequiringmathematicalreasoningandproof."}{"method":"MathematicalModelingProject""weight":"25%""description":"Developmentandanalysisofmathematicalmodelsforreal-worldscenarios."}]"skills_gained":[{"skill":"AnalyticalThinking""description":"Abilitytoanalyzecomplexproblemsusingmathematicalprinciples."}{"skill":"LogicalReasoning""description":"Structuredapproachtodevelopingandvalidatingmathematicalarguments."}]}}#Findtheclosestmatchingdepartmentmatching_dept=Nonefordeptindomain_enhancements.keys():ifdeptindepartmentordepartmentindept:matching_dept=deptbreak#Ifnospecificmatchusegeneralenhancementsifnotmatching_dept:returnfallback_data#Applydomain-specificenhancementsenhancements=domain_enhancements[matching_dept]#Mergeenhancementswithfallbackdataforkeyvalueinenhancements.items():ifkeyinfallback_data:#Ifthefieldisalistextenditwithdomain-specificitemsifisinstance(fallback_data[key]list)andisinstance(valuelist):#Adddomain-specificitemsatthebeginningformorerelevancefallback_data[key]=value+fallback_data[key]#Ifit'sastringthatcontainsJSONparseandextenditelifisinstance(fallback_data[key]str):try:existing_data=json.loads(fallback_data[key])ifisinstance(existing_datalist)andisinstance(valuelist):fallback_data[key]=json.dumps(value+existing_data)except(json.JSONDecodeErrorTypeError):#IfnotvalidJSONkeepasispass#Logtheenhancementlogger.info(f"Enhancedfallbackcontentwithdomain-specificinformationfor{matching_dept}")returnfallback_datadefgenerate_fallback_content(generation_request):"""Generatefallbackcontentforacoursegenerationrequest.Args:generation_request:TheCourseGenerationRequestobjectReturns:TheGeneratedCourseContentobjectwithfallbackcontent"""logger.info(f"Generatingfallbackcontentforrequest{generation_request.id}")#Createfallbackservicefallback_service=FallbackAIService()#Getcourseinformationcourse_title=generation_request.course.titledepartment=getattr(generation_request.course"department""General")description=generation_request.course.description#Getcourselevel(defaultto2ifnotavailable)try:level=int(generation_request.course.required_level)except(AttributeErrorValueErrorTypeError):level=2#Getfallbackcoursedatawithenhanceddomain-specificcontentfallback_data=fallback_service._get_fallback_course_data(course_title=course_titledepartment=departmentlevel=leveldescription=description)#Enhancecontentwithdomain-specificinformationfallback_data=enhance_domain_specific_content(fallback_datadepartment)#CreateorupdatetheGeneratedCourseContentobjectcontentcreated=GeneratedCourseContent.objects.get_or_create(request=generation_request)#Updatecontentwithfallbackdataforkeyvalueinfallback_data.items():ifhasattr(contentkey):setattr(contentkeyvalue)#Addweeklyscheduleifnotinfallbackdataifnotgetattr(content"weekly_schedule"None):content.weekly_schedule=[{"week":1"topic":f"Introductionto{course_title}""description":f"Overviewof{course_title}anditsimportancein{department}.""learning_objectives":[f"Understandthebasicconceptsof{course_title}""Identifykeyterminologyandprinciples""Recognizetheimportanceofthissubjectinthefield"]}{"week":2"topic":f"CorePrinciplesof{course_title}""description":"Exploringthefundamentalprinciplesandtheories.""learning_objectives":["Explainthecoretheoreticalframeworks""Applybasicprinciplestosimpleproblems""Compareandcontrastdifferentapproaches"]}{"week":3"topic":"PracticalApplications""description":"Applyingtheoreticalknowledgetoreal-worldscenarios.""learning_objectives":["Implementsolutionstopracticalproblems""Analyzecasestudiesandexamples""Developcriticalthinkingskills"]}]#Addassessmentmethodsifnotinfallbackdataifnotgetattr(content"assessment_methods"None):content.assessment_methods=[{"method":"Quizzes""weight":"20%""description":"Weeklyquizzestotestunderstandingofkeyconcepts."}{"method":"MidtermExam""weight":"30%""description":"Comprehensiveexamcoveringalltopicsfromweeks1-7."}{"method":"FinalProject""weight":"40%""description":f"Researchprojectapplying{course_title}conceptstoareal-worldproblem."}{"method":"Participation""weight":"10%""description":"Activeparticipationinclassdiscussionsandactivities."}]#Addskillsgainedifnotinfallbackdataifnotgetattr(content"skills_gained"None):content.skills_gained=[{"skill":"CriticalThinking""description":f"Abilitytoanalyze{course_title}conceptsandapplythemtosolveproblems."}{"skill":"TechnicalKnowledge""description":f"Understandingof{course_title}principlesandmethodologies."}{"skill":"Communication""description":"Abilitytoclearlyexplaincomplexconceptsandideas."}]#Savethecontentcontent.save()#Marktherequestascompletedwithanoteaboutfallbackcontentgeneration_request.status="COMPLETED"generation_request.error_message=("Usingpre-generatedfallbackcontentinsteadofAIservice.")generation_request.save(update_fields=["status""error_message"])#Trackfallbackcontentusageformonitoringcourse_id=generation_request.course.iddepartment=getattr(generation_request.course"department""General")track_fallback_content_usage(course_iddepartment)logger.info(f"Successfullygeneratedfallbackcontentforrequest{generation_request.id}")returncontent
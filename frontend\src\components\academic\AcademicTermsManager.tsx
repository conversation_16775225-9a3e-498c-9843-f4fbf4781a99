/**
 * 🎓 Academic Terms Management Component
 * 
 * Features:
 * - Get all terms, current term, and registration term
 * - Create and update terms (admin functions)
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  CircularProgress
} from '@mui/material';
import { Add, Edit, School, EventNote } from '@mui/icons-material';
import { academicService, AcademicTerm } from '../../services/academicService';

interface AcademicTermsManagerProps {
  isAdmin?: boolean;
}

const AcademicTermsManager: React.FC<AcademicTermsManagerProps> = ({ isAdmin = false }) => {
  const [terms, setTerms] = useState<AcademicTerm[]>([]);
  const [currentTerm, setCurrentTerm] = useState<AcademicTerm | null>(null);
  const [registrationTerm, setRegistrationTerm] = useState<AcademicTerm | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedTerm, setSelectedTerm] = useState<AcademicTerm | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    term_type: 'FALL',
    year: new Date().getFullYear(),
    start_date: '',
    end_date: '',
    registration_start: '',
    registration_end: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [termsData, currentTermData, registrationTermData] = await Promise.all([
        academicService.getTerms(),
        academicService.getCurrentTerm(),
        academicService.getRegistrationTerm()
      ]);
      
      setTerms(termsData);
      setCurrentTerm(currentTermData);
      setRegistrationTerm(registrationTermData);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to load academic terms');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTerm = async () => {
    try {
      await academicService.createTerm(formData);
      setOpenDialog(false);
      loadData();
      resetForm();
    } catch (err: any) {
      setError(err.message || 'Failed to create term');
    }
  };

  const handleUpdateTerm = async () => {
    if (!selectedTerm) return;
    
    try {
      await academicService.updateTerm(selectedTerm.id, formData);
      setOpenDialog(false);
      loadData();
      resetForm();
    } catch (err: any) {
      setError(err.message || 'Failed to update term');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      term_type: 'FALL',
      year: new Date().getFullYear(),
      start_date: '',
      end_date: '',
      registration_start: '',
      registration_end: ''
    });
    setSelectedTerm(null);
  };

  const openCreateDialog = () => {
    resetForm();
    setOpenDialog(true);
  };

  const openEditDialog = (term: AcademicTerm) => {
    setSelectedTerm(term);
    setFormData({
      name: term.name,
      term_type: term.term_type,
      year: term.year,
      start_date: term.start_date,
      end_date: term.end_date,
      registration_start: term.registration_start,
      registration_end: term.registration_end
    });
    setOpenDialog(true);
  };

  const getTermStatusColor = (term: AcademicTerm) => {
    if (term.is_current) return 'success';
    if (term.is_registration_open) return 'warning';
    return 'default';
  };

  const getTermStatusLabel = (term: AcademicTerm) => {
    if (term.is_current) return 'Current';
    if (term.is_registration_open) return 'Registration Open';
    return 'Inactive';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <School color="primary" />
          Academic Terms Management
        </Typography>
        {isAdmin && (
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={openCreateDialog}
          >
            Create New Term
          </Button>
        )}
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Current Term and Registration Term Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="primary" gutterBottom>
                Current Term
              </Typography>
              {currentTerm ? (
                <Box>
                  <Typography variant="h5">{currentTerm.name}</Typography>
                  <Typography color="text.secondary">
                    {new Date(currentTerm.start_date).toLocaleDateString()} - {new Date(currentTerm.end_date).toLocaleDateString()}
                  </Typography>
                  <Chip 
                    label="Active" 
                    color="success" 
                    size="small" 
                    sx={{ mt: 1 }}
                  />
                </Box>
              ) : (
                <Typography color="text.secondary">No current term found</Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="primary" gutterBottom>
                Registration Term
              </Typography>
              {registrationTerm ? (
                <Box>
                  <Typography variant="h5">{registrationTerm.name}</Typography>
                  <Typography color="text.secondary">
                    Registration: {new Date(registrationTerm.registration_start).toLocaleDateString()} - {new Date(registrationTerm.registration_end).toLocaleDateString()}
                  </Typography>
                  <Chip 
                    label="Registration Open" 
                    color="warning" 
                    size="small" 
                    sx={{ mt: 1 }}
                  />
                </Box>
              ) : (
                <Typography color="text.secondary">No registration term found</Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* All Terms Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <EventNote />
            All Academic Terms
          </Typography>
          
          <TableContainer component={Paper} sx={{ mt: 2 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Term Name</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Year</TableCell>
                  <TableCell>Start Date</TableCell>
                  <TableCell>End Date</TableCell>
                  <TableCell>Status</TableCell>
                  {isAdmin && <TableCell>Actions</TableCell>}
                </TableRow>
              </TableHead>
              <TableBody>
                {terms.map((term) => (
                  <TableRow key={term.id}>
                    <TableCell>{term.name}</TableCell>
                    <TableCell>{term.term_type}</TableCell>
                    <TableCell>{term.year}</TableCell>
                    <TableCell>{new Date(term.start_date).toLocaleDateString()}</TableCell>
                    <TableCell>{new Date(term.end_date).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <Chip 
                        label={getTermStatusLabel(term)}
                        color={getTermStatusColor(term)}
                        size="small"
                      />
                    </TableCell>
                    {isAdmin && (
                      <TableCell>
                        <Button
                          size="small"
                          startIcon={<Edit />}
                          onClick={() => openEditDialog(term)}
                        >
                          Edit
                        </Button>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Create/Edit Term Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedTerm ? 'Edit Academic Term' : 'Create New Academic Term'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Term Name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="e.g., Fall 2024"
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Term Type</InputLabel>
                <Select
                  value={formData.term_type}
                  onChange={(e) => setFormData({ ...formData, term_type: e.target.value as any })}
                >
                  <MenuItem value="FALL">Fall</MenuItem>
                  <MenuItem value="SPRING">Spring</MenuItem>
                  <MenuItem value="SUMMER">Summer</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Year"
                type="number"
                value={formData.year}
                onChange={(e) => setFormData({ ...formData, year: parseInt(e.target.value) })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Start Date"
                type="date"
                value={formData.start_date}
                onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="End Date"
                type="date"
                value={formData.end_date}
                onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Registration Start"
                type="datetime-local"
                value={formData.registration_start}
                onChange={(e) => setFormData({ ...formData, registration_start: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Registration End"
                type="datetime-local"
                value={formData.registration_end}
                onChange={(e) => setFormData({ ...formData, registration_end: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            onClick={selectedTerm ? handleUpdateTerm : handleCreateTerm}
          >
            {selectedTerm ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AcademicTermsManager;

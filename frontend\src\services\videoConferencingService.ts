import apiClient from './apiClient';

// Types for video conferencing
interface VideoProvider {
  id: string;
  name: 'zoom' | 'teams' | 'webex' | 'googlemeet' | 'bigbluebutton' | 'jitsi';
  displayName: string;
  credentials: {
    apiKey?: string;
    apiSecret?: string;
    clientId?: string;
    clientSecret?: string;
    tenantId?: string;
    refreshToken?: string;
    accessToken?: string;
    webhookSecret?: string;
  };
  settings: {
    maxParticipants: number;
    maxDuration: number; // in minutes
    recordingEnabled: boolean;
    waitingRoomEnabled: boolean;
    muteOnEntry: boolean;
    allowScreenSharing: boolean;
    allowBreakoutRooms: boolean;
    chatEnabled: boolean;
    pollsEnabled: boolean;
    whiteboardEnabled: boolean;
  };
  features: string[];
  isActive: boolean;
}

interface MeetingSchedule {
  title: string;
  description: string;
  startTime: string;
  duration: number; // in minutes
  timezone: string;
  isRecurring: boolean;
  recurrencePattern?: {
    type: 'daily' | 'weekly' | 'monthly';
    interval: number;
    endDate?: string;
    daysOfWeek?: number[];
  };
  hostId: string;
  hostName: string;
  hostEmail: string;
}

interface MeetingParticipant {
  userId: string;
  name: string;
  email: string;
  role: 'host' | 'co-host' | 'participant' | 'attendee';
  isRequired: boolean;
  permissions: {
    canShare: boolean;
    canRecord: boolean;
    canManageParticipants: boolean;
    canUsePoll: boolean;
    canUseWhiteboard: boolean;
  };
}

interface MeetingOptions {
  passwordProtected: boolean;
  password?: string;
  waitingRoom: boolean;
  muteOnEntry: boolean;
  allowJoinBeforeHost: boolean;
  autoRecord: boolean;
  recordingLocation: 'cloud' | 'local';
  enableBreakoutRooms: boolean;
  maxBreakoutRooms?: number;
  enableChat: boolean;
  enablePolls: boolean;
  enableWhiteboard: boolean;
  enableLiveStreaming: boolean;
  liveStreamingPlatform?: string;
  enableTranscription: boolean;
  language?: string;
}

interface ScheduledMeeting {
  meetingId: string;
  providerId: string;
  providerMeetingId: string;
  title: string;
  description: string;
  startTime: string;
  duration: number;
  timezone: string;
  status: 'scheduled' | 'started' | 'ended' | 'cancelled';
  host: MeetingParticipant;
  participants: MeetingParticipant[];
  joinUrls: {
    host: string;
    participant: string;
    phone?: string;
  };
  meetingNumbers: {
    id: string;
    password?: string;
    phoneNumbers?: string[];
  };
  options: MeetingOptions;
  createdAt: string;
  updatedAt: string;
}

interface MeetingRecording {
  recordingId: string;
  meetingId: string;
  title: string;
  startTime: string;
  duration: number;
  fileSize: number;
  format: string;
  quality: string;
  downloadUrl: string;
  streamingUrl?: string;
  thumbnailUrl?: string;
  transcriptUrl?: string;
  chatLogUrl?: string;
  status: 'processing' | 'available' | 'archived' | 'deleted';
  expiresAt?: string;
  viewCount: number;
  isPasswordProtected: boolean;
  metadata: {
    participants: number;
    chatMessages: number;
    pollResults?: any[];
    whiteboardSessions?: any[];
  };
}

interface MeetingAnalytics {
  meetingId: string;
  attendance: {
    invited: number;
    joined: number;
    attendanceRate: number;
    averageDuration: number;
    peakConcurrent: number;
  };
  engagement: {
    chatMessages: number;
    pollParticipation: number;
    screenShareSessions: number;
    whiteboardUsage: number;
    breakoutRoomUsage: number;
  };
  technical: {
    audioQuality: number;
    videoQuality: number;
    connectionIssues: number;
    bandwidthUsage: any[];
  };
  participantDetails: any[];
}

export class VideoConferencingService {
  private static instance: VideoConferencingService;
  private providers = new Map<string, VideoProvider>();
  private meetingsCache = new Map<string, ScheduledMeeting>();
  private recordingsCache = new Map<string, MeetingRecording[]>();

  static getInstance(): VideoConferencingService {
    if (!VideoConferencingService.instance) {
      VideoConferencingService.instance = new VideoConferencingService();
    }
    return VideoConferencingService.instance;
  }

  // Provider Management
  async addProvider(provider: VideoProvider): Promise<{
    success: boolean;
    providerId: string;
    webhookUrl?: string;
    testResult?: any;
  }> {
    try {
      const response = await apiClient.post('/video-conferencing/providers', provider);
      
      if (response.data.success) {
        this.providers.set(provider.id, provider);
      }
      
      return response.data;
    } catch (error) {
      console.error('Error adding video conferencing provider:', error);
      throw error;
    }
  }

  async getProviders(): Promise<VideoProvider[]> {
    try {
      const response = await apiClient.get('/video-conferencing/providers');
      const providers = response.data.providers;
      
      // Update local cache
      providers.forEach((provider: VideoProvider) => {
        this.providers.set(provider.id, provider);
      });
      
      return providers;
    } catch (error) {
      console.error('Error fetching video conferencing providers:', error);
      throw error;
    }
  }

  async updateProvider(providerId: string, updates: Partial<VideoProvider>): Promise<boolean> {
    try {
      const response = await apiClient.put(`/video-conferencing/providers/${providerId}`, updates);
      
      if (response.data.success && this.providers.has(providerId)) {
        const provider = this.providers.get(providerId)!;
        this.providers.set(providerId, { ...provider, ...updates });
      }
      
      return response.data.success;
    } catch (error) {
      console.error('Error updating provider:', error);
      throw error;
    }
  }

  // Meeting Scheduling
  async scheduleMeeting(
    providerId: string,
    schedule: MeetingSchedule,
    participants: MeetingParticipant[],
    options: MeetingOptions
  ): Promise<ScheduledMeeting> {
    try {
      const response = await apiClient.post('/video-conferencing/meetings/schedule', {
        providerId,
        schedule,
        participants,
        options
      });

      const meeting = response.data.meeting;
      this.meetingsCache.set(meeting.meetingId, meeting);
      
      return meeting;
    } catch (error) {
      console.error('Error scheduling meeting:', error);
      throw error;
    }
  }

  async updateMeeting(
    meetingId: string,
    updates: {
      schedule?: Partial<MeetingSchedule>;
      participants?: MeetingParticipant[];
      options?: Partial<MeetingOptions>;
    }
  ): Promise<ScheduledMeeting> {
    try {
      const response = await apiClient.put(`/video-conferencing/meetings/${meetingId}`, updates);
      
      const meeting = response.data.meeting;
      this.meetingsCache.set(meetingId, meeting);
      
      return meeting;
    } catch (error) {
      console.error('Error updating meeting:', error);
      throw error;
    }
  }

  async cancelMeeting(meetingId: string, reason?: string): Promise<boolean> {
    try {
      const response = await apiClient.delete(`/video-conferencing/meetings/${meetingId}`, {
        data: { reason }
      });

      if (response.data.success) {
        this.meetingsCache.delete(meetingId);
      }
      
      return response.data.success;
    } catch (error) {
      console.error('Error canceling meeting:', error);
      throw error;
    }
  }

  async getMeeting(meetingId: string): Promise<ScheduledMeeting> {
    // Check cache first
    if (this.meetingsCache.has(meetingId)) {
      return this.meetingsCache.get(meetingId)!;
    }

    try {
      const response = await apiClient.get(`/video-conferencing/meetings/${meetingId}`);
      const meeting = response.data;
      
      this.meetingsCache.set(meetingId, meeting);
      return meeting;
    } catch (error) {
      console.error('Error fetching meeting:', error);
      throw error;
    }
  }

  async getUserMeetings(
    userId: string,
    filter: {
      status?: string;
      dateRange?: { start: string; end: string };
      providerId?: string;
    } = {}
  ): Promise<ScheduledMeeting[]> {
    try {
      const response = await apiClient.get(`/video-conferencing/users/${userId}/meetings`, {
        params: filter
      });

      return response.data.meetings;
    } catch (error) {
      console.error('Error fetching user meetings:', error);
      throw error;
    }
  }

  // Meeting Control
  async startMeeting(meetingId: string): Promise<{
    success: boolean;
    joinUrls: any;
    liveStreamUrl?: string;
  }> {
    try {
      const response = await apiClient.post(`/video-conferencing/meetings/${meetingId}/start`);
      return response.data;
    } catch (error) {
      console.error('Error starting meeting:', error);
      throw error;
    }
  }

  async endMeeting(meetingId: string): Promise<boolean> {
    try {
      const response = await apiClient.post(`/video-conferencing/meetings/${meetingId}/end`);
      return response.data.success;
    } catch (error) {
      console.error('Error ending meeting:', error);
      throw error;
    }
  }

  async getMeetingStatus(meetingId: string): Promise<{
    status: string;
    participantCount: number;
    duration: number;
    isRecording: boolean;
    isLiveStreaming: boolean;
  }> {
    try {
      const response = await apiClient.get(`/video-conferencing/meetings/${meetingId}/status`);
      return response.data;
    } catch (error) {
      console.error('Error fetching meeting status:', error);
      throw error;
    }
  }

  // Participant Management
  async addParticipant(meetingId: string, participant: MeetingParticipant): Promise<boolean> {
    try {
      const response = await apiClient.post(`/video-conferencing/meetings/${meetingId}/participants`, participant);
      return response.data.success;
    } catch (error) {
      console.error('Error adding participant:', error);
      throw error;
    }
  }

  async removeParticipant(meetingId: string, participantId: string): Promise<boolean> {
    try {
      const response = await apiClient.delete(`/video-conferencing/meetings/${meetingId}/participants/${participantId}`);
      return response.data.success;
    } catch (error) {
      console.error('Error removing participant:', error);
      throw error;
    }
  }

  async updateParticipantRole(meetingId: string, participantId: string, role: string): Promise<boolean> {
    try {
      const response = await apiClient.put(`/video-conferencing/meetings/${meetingId}/participants/${participantId}`, {
        role
      });
      return response.data.success;
    } catch (error) {
      console.error('Error updating participant role:', error);
      throw error;
    }
  }

  async getMeetingParticipants(meetingId: string): Promise<{
    current: any[];
    invited: any[];
    history: any[];
  }> {
    try {
      const response = await apiClient.get(`/video-conferencing/meetings/${meetingId}/participants`);
      return response.data;
    } catch (error) {
      console.error('Error fetching meeting participants:', error);
      throw error;
    }
  }

  // Recording Management
  async startRecording(meetingId: string, options?: {
    location: 'cloud' | 'local';
    quality: 'hd' | 'sd';
    includeAudio: boolean;
    includeVideo: boolean;
    includeChat: boolean;
    includeWhiteboard: boolean;
  }): Promise<{
    success: boolean;
    recordingId: string;
  }> {
    try {
      const response = await apiClient.post(`/video-conferencing/meetings/${meetingId}/recording/start`, options);
      return response.data;
    } catch (error) {
      console.error('Error starting recording:', error);
      throw error;
    }
  }

  async stopRecording(meetingId: string): Promise<boolean> {
    try {
      const response = await apiClient.post(`/video-conferencing/meetings/${meetingId}/recording/stop`);
      return response.data.success;
    } catch (error) {
      console.error('Error stopping recording:', error);
      throw error;
    }
  }

  async getMeetingRecordings(meetingId: string): Promise<MeetingRecording[]> {
    // Check cache first
    if (this.recordingsCache.has(meetingId)) {
      return this.recordingsCache.get(meetingId)!;
    }

    try {
      const response = await apiClient.get(`/video-conferencing/meetings/${meetingId}/recordings`);
      const recordings = response.data.recordings;
      
      this.recordingsCache.set(meetingId, recordings);
      return recordings;
    } catch (error) {
      console.error('Error fetching meeting recordings:', error);
      throw error;
    }
  }

  async getRecording(recordingId: string): Promise<MeetingRecording> {
    try {
      const response = await apiClient.get(`/video-conferencing/recordings/${recordingId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching recording:', error);
      throw error;
    }
  }

  async shareRecording(
    recordingId: string,
    recipients: string[],
    permissions: string[],
    expiresAt?: string
  ): Promise<{
    shareId: string;
    shareUrl: string;
    expiresAt: string;
  }> {
    try {
      const response = await apiClient.post(`/video-conferencing/recordings/${recordingId}/share`, {
        recipients,
        permissions,
        expiresAt
      });

      return response.data;
    } catch (error) {
      console.error('Error sharing recording:', error);
      throw error;
    }
  }

  // Breakout Rooms
  async createBreakoutRooms(
    meetingId: string,
    rooms: {
      name: string;
      participants: string[];
      duration?: number;
    }[]
  ): Promise<{
    success: boolean;
    rooms: any[];
  }> {
    try {
      const response = await apiClient.post(`/video-conferencing/meetings/${meetingId}/breakout-rooms`, {
        rooms
      });

      return response.data;
    } catch (error) {
      console.error('Error creating breakout rooms:', error);
      throw error;
    }
  }

  async manageBreakoutRooms(
    meetingId: string,
    action: 'open' | 'close' | 'assign' | 'broadcast',
    data?: any
  ): Promise<boolean> {
    try {
      const response = await apiClient.post(`/video-conferencing/meetings/${meetingId}/breakout-rooms/${action}`, data);
      return response.data.success;
    } catch (error) {
      console.error('Error managing breakout rooms:', error);
      throw error;
    }
  }

  // Analytics and Reporting
  async getMeetingAnalytics(meetingId: string): Promise<MeetingAnalytics> {
    try {
      const response = await apiClient.get(`/video-conferencing/meetings/${meetingId}/analytics`);
      return response.data;
    } catch (error) {
      console.error('Error fetching meeting analytics:', error);
      throw error;
    }
  }

  async getUserAnalytics(
    userId: string,
    timeRange: string = '30_days'
  ): Promise<{
    totalMeetings: number;
    totalDuration: number;
    averageAttendance: number;
    meetingTypes: any[];
    engagementMetrics: any;
    trends: any[];
  }> {
    try {
      const response = await apiClient.get(`/video-conferencing/users/${userId}/analytics`, {
        params: { timeRange }
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching user analytics:', error);
      throw error;
    }
  }

  async getInstitutionAnalytics(): Promise<{
    totalMeetings: number;
    totalDuration: number;
    totalParticipants: number;
    providerUsage: any[];
    trends: any[];
    costAnalysis: any;
  }> {
    try {
      const response = await apiClient.get('/video-conferencing/analytics/institution');
      return response.data;
    } catch (error) {
      console.error('Error fetching institution analytics:', error);
      throw error;
    }
  }

  // Integration Features
  async createInstantMeeting(
    providerId: string,
    hostId: string,
    options: Partial<MeetingOptions> = {}
  ): Promise<{
    meetingId: string;
    joinUrls: any;
    meetingNumber: string;
    password?: string;
  }> {
    try {
      const response = await apiClient.post('/video-conferencing/meetings/instant', {
        providerId,
        hostId,
        options
      });

      return response.data;
    } catch (error) {
      console.error('Error creating instant meeting:', error);
      throw error;
    }
  }

  async generateMeetingReport(
    meetingId: string,
    format: 'pdf' | 'csv' | 'json' = 'pdf'
  ): Promise<{
    reportId: string;
    downloadUrl: string;
    expiresAt: string;
  }> {
    try {
      const response = await apiClient.post(`/video-conferencing/meetings/${meetingId}/report`, {
        format
      });

      return response.data;
    } catch (error) {
      console.error('Error generating meeting report:', error);
      throw error;
    }
  }

  // Webhook Management
  async configureWebhooks(
    providerId: string,
    events: string[],
    webhookUrl: string
  ): Promise<{
    webhookId: string;
    verificationToken: string;
  }> {
    try {
      const response = await apiClient.post(`/video-conferencing/providers/${providerId}/webhooks`, {
        events,
        webhookUrl
      });

      return response.data;
    } catch (error) {
      console.error('Error configuring webhooks:', error);
      throw error;
    }
  }

  // Utility Methods
  async testProviderConnection(providerId: string): Promise<{
    success: boolean;
    responseTime: number;
    features: string[];
    limits: any;
    message?: string;
  }> {
    try {
      const response = await apiClient.post(`/video-conferencing/providers/${providerId}/test`);
      return response.data;
    } catch (error) {
      console.error('Error testing provider connection:', error);
      throw error;
    }
  }

  async getProviderLimits(providerId: string): Promise<{
    maxParticipants: number;
    maxDuration: number;
    maxMeetingsPerMonth: number;
    storageLimit: number;
    features: string[];
  }> {
    try {
      const response = await apiClient.get(`/video-conferencing/providers/${providerId}/limits`);
      return response.data;
    } catch (error) {
      console.error('Error fetching provider limits:', error);
      throw error;
    }
  }

  // Cache Management
  clearCache(): void {
    this.meetingsCache.clear();
    this.recordingsCache.clear();
  }

  // Real-time Updates
  async subscribeToMeetingUpdates(meetingId: string, callback: (update: any) => void): Promise<() => void> {
    try {
      const response = await apiClient.post(`/video-conferencing/meetings/${meetingId}/subscribe`);
      const subscriptionId = response.data.subscriptionId;

      // Set up WebSocket connection for real-time updates
      const eventSource = new EventSource(`/api/video-conferencing/updates/${subscriptionId}`);
      
      eventSource.onmessage = (event) => {
        const update = JSON.parse(event.data);
        callback(update);
      };

      // Return unsubscribe function
      return () => {
        eventSource.close();
        apiClient.delete(`/video-conferencing/subscribe/${subscriptionId}`);
      };
    } catch (error) {
      console.error('Error subscribing to meeting updates:', error);
      throw error;
    }
  }
}

export const videoConferencingService = VideoConferencingService.getInstance();

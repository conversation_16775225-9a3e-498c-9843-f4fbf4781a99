/**
 * Chatbot Service Tests
 *
 * Comprehensive tests for the Chatbot Service to ensure
 * it works correctly with the standardized approach.
 */

import chatbotService from '../chatbotService';
import { BaseAIService } from '../utils/BaseAIService';
import { AIServiceError } from '../utils/aiServiceUtils';
import mockAxios from 'jest-mock-axios';

describe('Chatbot Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Structure', () => {
    test('should be instance of BaseAIService', () => {
      expect(chatbotService).toBeInstanceOf(BaseAIService);
    });

    test('should have correct service info', () => {
      const info = chatbotService.getServiceInfo();
      expect(info.name).toBe('Chatbot');
      expect(info.endpoint).toContain('chat');
    });

    test('should have all required methods', () => {
      expect(typeof chatbotService.sendMessage).toBe('function');
      expect(typeof chatbotService.getConversations).toBe('function');
      expect(typeof chatbotService.getConversation).toBe('function');
      expect(typeof chatbotService.deleteConversation).toBe('function');
      expect(typeof chatbotService.updateConversationTitle).toBe('function');
      expect(typeof chatbotService.getMessages).toBe('function');
    });
  });

  describe('sendMessage', () => {
    test('should send message successfully', async () => {
      const mockResponse = {
        id: 1,
        message: 'Hello! How can I help you?',
        conversation_id: 123,
        timestamp: '2024-01-01T12:00:00Z',
        agent_used: 'general_tutor',
        confidence: 0.9,
      };

      mockAxios.post.mockResolvedValueOnce({
        data: mockResponse,
      });

      const result = await chatbotService.sendMessage('Hello', 123);

      expect(mockAxios.post).toHaveBeenCalledWith('chat/', {
        message: 'Hello',
        conversation_id: 123,
      });
      expect(result).toEqual(mockResponse);
    });

    test('should send message without conversation ID', async () => {
      const mockResponse = {
        id: 1,
        message: 'Hello! How can I help you?',
        conversation_id: 456,
        timestamp: '2024-01-01T12:00:00Z',
      };

      mockAxios.post.mockResolvedValueOnce({
        data: mockResponse,
      });

      const result = await chatbotService.sendMessage('Hello');

      expect(mockAxios.post).toHaveBeenCalledWith('chat/', {
        message: 'Hello',
        conversation_id: undefined,
      });
      expect(result).toEqual(mockResponse);
    });

    test('should handle server errors with custom message', async () => {
      const serverError = new AIServiceError(
        'Internal server error',
        'SERVER_ERROR',
        500
      );
      mockAxios.post.mockRejectedValueOnce(serverError);

      await expect(chatbotService.sendMessage('Hello')).rejects.toThrow(
        'An error occurred while processing your message. Please try again.'
      );
    });

    test('should propagate other errors normally', async () => {
      const validationError = new AIServiceError(
        'Invalid message',
        'VALIDATION_ERROR',
        400
      );
      mockAxios.post.mockRejectedValueOnce(validationError);

      await expect(chatbotService.sendMessage('')).rejects.toThrow(
        'Invalid message'
      );
    });
  });

  describe('getConversations', () => {
    test('should get conversations successfully', async () => {
      const mockConversations = [
        {
          id: 1,
          title: 'Math Help',
          created_at: '2024-01-01T10:00:00Z',
          updated_at: '2024-01-01T11:00:00Z',
          message_count: 5,
        },
        {
          id: 2,
          title: 'Programming Questions',
          created_at: '2024-01-01T12:00:00Z',
          updated_at: '2024-01-01T13:00:00Z',
          message_count: 3,
        },
      ];

mockAxios.get.mockResolvedValueOnce({
        data: mockConversations,
      });

      const result = await chatbotService.getConversations();

      expect(mockAxios.get).toHaveBeenCalledWith('conversations/');
      expect(result).toEqual(mockConversations);
    });
  });

  describe('getConversation', () => {
    test('should get specific conversation successfully', async () => {
      const mockConversation = {
        id: 1,
        title: 'Math Help',
        created_at: '2024-01-01T10:00:00Z',
        messages: [
          {
            id: 1,
            content: 'I need help with calculus',
            is_user: true,
            timestamp: '2024-01-01T10:00:00Z',
          },
          {
            id: 2,
            content: "I'd be happy to help with calculus!",
            is_user: false,
            timestamp: '2024-01-01T10:01:00Z',
          },
        ],
      };

mockAxios.get.mockResolvedValueOnce({
        data: mockConversation,
      });

      const result = await chatbotService.getConversation(1);

      expect(mockAxios.get).toHaveBeenCalledWith('conversations/1/');
      expect(result).toEqual(mockConversation);
    });
  });

  describe('deleteConversation', () => {
    test('should delete conversation successfully', async () => {
mockAxios.delete.mockResolvedValueOnce({
        data: { success: true },
      });

      await chatbotService.deleteConversation(1);

      expect(mockAxios.delete).toHaveBeenCalledWith('conversations/1/');
    });
  });

  describe('updateConversationTitle', () => {
    test('should update conversation title successfully', async () => {
      const mockUpdatedConversation = {
        id: 1,
        title: 'Updated Title',
        created_at: '2024-01-01T10:00:00Z',
        updated_at: '2024-01-01T14:00:00Z',
      };

mockAxios.patch.mockResolvedValueOnce({
        data: mockUpdatedConversation,
      });

      const result = await chatbotService.updateConversationTitle(
        1,
        'Updated Title'
      );

      expect(mockAxios.patch).toHaveBeenCalledWith(
        'conversations/1/update_title/',
        {
          title: 'Updated Title',
        }
      );
      expect(result).toEqual(mockUpdatedConversation);
    });
  });

  describe('getMessages', () => {
    test('should get messages for conversation successfully', async () => {
      const mockMessages = [
        {
          id: 1,
          content: 'Hello',
          is_user: true,
          timestamp: '2024-01-01T10:00:00Z',
        },
        {
          id: 2,
          content: 'Hi there!',
          is_user: false,
          timestamp: '2024-01-01T10:01:00Z',
        },
      ];

mockAxios.get.mockResolvedValueOnce({
        data: mockMessages,
      });

      const result = await chatbotService.getMessages(1);

      expect(mockAxios.get).toHaveBeenCalledWith('conversations/1/messages/');
      expect(result).toEqual(mockMessages);
    });
  });

  describe('Error Handling', () => {
    test('should handle network errors gracefully', async () => {
mockAxios.get.mockRejectedValueOnce(new Error('Network Error'));

      // Should not throw but handle gracefully with fallback
      await expect(chatbotService.getConversations()).resolves.not.toThrow();
    });

    test('should handle timeout errors', async () => {
      const timeoutError = new AIServiceError(
        'Request timeout',
        'TIMEOUT_ERROR',
        408
      );
mockAxios.post.mockRejectedValueOnce(timeoutError);

      // Should handle timeout gracefully
      await expect(chatbotService.sendMessage('Hello')).resolves.not.toThrow();
    });

    test('should handle authentication errors', async () => {
      const authError = new AIServiceError('Unauthorized', 'AUTH_ERROR', 401);
mockAxios.get.mockRejectedValueOnce(authError);

      await expect(chatbotService.getConversations()).rejects.toThrow();
    });
  });

  describe('Configuration', () => {
    test('should have appropriate default configuration', () => {
      const config = chatbotService.getConfig();

      expect(config.timeout).toBe(45000); // 45 seconds for AI responses
      expect(config.retries).toBe(1); // Limited retries for chat
      expect(config.fallbackEnabled).toBe(true);
      expect(config.cacheEnabled).toBe(false); // Don't cache chat responses
    });

    test('should allow configuration updates', () => {
      const newConfig = { timeout: 60000, retries: 2 };
      chatbotService.updateConfig(newConfig);

      const updatedConfig = chatbotService.getConfig();
      expect(updatedConfig.timeout).toBe(60000);
      expect(updatedConfig.retries).toBe(2);
    });
  });

  describe('Fallback Behavior', () => {
    test('should provide fallback response for critical errors', async () => {
      const criticalError = new AIServiceError(
        'Service unavailable',
        'SERVER_ERROR',
        503
      );
mockAxios.post.mockRejectedValueOnce(criticalError);

      // Should provide fallback instead of throwing
      const result = await chatbotService.sendMessage('Hello');
      expect(result).toBeDefined();
      expect(result.message || result.content).toBeDefined();
    });

    test('should handle fallback gracefully when enabled', () => {
      chatbotService.setFallbackEnabled(true);
      expect(chatbotService.getConfig().fallbackEnabled).toBe(true);

      chatbotService.setFallbackEnabled(false);
      expect(chatbotService.getConfig().fallbackEnabled).toBe(false);
    });
  });
});

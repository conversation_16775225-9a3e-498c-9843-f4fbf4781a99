/**
 * AI Assistant Service Tests
 *
 * Comprehensive tests for the AI Assistant Service to ensure
 * it works correctly with the standardized approach.
 */

import aiAssistantService from '../aiAssistantService';
import { BaseAIService } from '../utils/BaseAIService';
import { AIServiceError } from '../utils/aiServiceUtils';
import mockAxios from 'jest-mock-axios';

describe('AI Assistant Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Clear any cached responses
    jest.clearAllTimers();
    // Reset service state and disable caching/fallbacks for tests
    if (aiAssistantService.clearCache) {
      aiAssistantService.clearCache();
    }
    if (aiAssistantService.updateConfig) {
      aiAssistantService.updateConfig({
        cacheEnabled: false,
        fallbackEnabled: false,
        retries: 0,
        timeout: 5000,
      });
    }
  });

  describe('Service Structure', () => {
    test('should be instance of BaseAIService', () => {
      expect(aiAssistantService).toBeInstanceOf(BaseAIService);
    });

    test('should have correct service info', () => {
      const info = aiAssistantService.getServiceInfo();
      expect(info.name).toBe('AI Assistant');
      expect(info.endpoint).toContain('assistant');
    });

    test('should have all required methods', () => {
      expect(typeof aiAssistantService.getSuggestions).toBe('function');
      expect(typeof aiAssistantService.askQuestion).toBe('function');
      expect(typeof aiAssistantService.getAnalytics).toBe('function');
      expect(typeof aiAssistantService.getConversationHistory).toBe('function');
      expect(typeof aiAssistantService.clearConversationHistory).toBe(
        'function'
      );
      expect(typeof aiAssistantService.getPersonalizedSuggestions).toBe(
        'function'
      );
    });
  });

  describe('getSuggestions', () => {
    test('should return suggestions successfully', async () => {
      const mockSuggestions = [
        { id: 1, text: 'Test suggestion 1', category: 'general' },
        { id: 2, text: 'Test suggestion 2', category: 'academic' },
      ];

      mockAxios.get.mockResolvedValueOnce({
        data: { suggestions: mockSuggestions },
      });

      const result = await aiAssistantService.getSuggestions();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/v1/utils/ai/assistant/suggestions/', {"timeout": 30000});
      expect(result).toEqual(mockSuggestions);
    });

    test('should handle empty suggestions response', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: {},
      });

      const result = await aiAssistantService.getSuggestions();
      // Service provides fallback suggestions when API returns empty data
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
    });

    test('should handle API errors with fallback', async () => {
      mockAxios.get.mockRejectedValueOnce(new Error('API Error'));

      // Should not throw but return fallback
      const result = await aiAssistantService.getSuggestions();
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe('askQuestion', () => {
    test('should ask question successfully', async () => {
      const mockResponse = {
        answer: 'Test answer',
        confidence_score: 0.9,
        response_time: 1.5,
        session_id: 'test-session-123',
        suggestions: ['Follow-up 1', 'Follow-up 2'],
      };

      mockAxios.post.mockResolvedValueOnce({
        data: mockResponse,
      });

      const result = await aiAssistantService.askQuestion(
        'What is machine learning?',
        { topic: 'AI' },
        'session-123'
      );

      expect(mockAxios.post).toHaveBeenCalledWith('/api/v1/utils/ai/assistant/ask/', {
        question: 'What is machine learning?',
        context: { topic: 'AI' },
        session_id: 'session-123',
      }, { timeout: 30000 });
      expect(result).toEqual(mockResponse);
    });

    test('should handle legacy response format', async () => {
      const legacyResponse = {
        answer: 'Legacy answer',
        confidence_score: 0.8,
      };

      mockAxios.post.mockResolvedValueOnce({
        data: legacyResponse,
      });

      const result = await aiAssistantService.askQuestion('Test question');

      expect(result.answer).toBe('Legacy answer');
      expect(result.confidence_score).toBe(0.8);
      // These fields might be added by the service or might not be present in legacy format
      expect(result).toHaveProperty('answer');
      expect(result).toHaveProperty('confidence_score');
    });

    test('should provide fallback for API errors', async () => {
      mockAxios.post.mockRejectedValueOnce(
        new AIServiceError('Server error', 'SERVER_ERROR', 500)
      );

      // Should provide fallback response instead of throwing
      const result = await aiAssistantService.askQuestion('Test question');
      expect(result).toBeDefined();
      expect(result.answer).toBeDefined();
    }, 15000);
  });

  describe('getAnalytics', () => {
    test('should get analytics successfully', async () => {
      const mockAnalytics = {
        total_questions: 150,
        average_confidence: 0.85,
        popular_topics: ['AI', 'Programming', 'Math'],
      };

      mockAxios.get.mockResolvedValueOnce({
        data: mockAnalytics,
      });

      const result = await aiAssistantService.getAnalytics();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/v1/utils/ai/assistant/analytics/', {"timeout": 30000});
      expect(result).toEqual(mockAnalytics);
    });
  });

  describe('getConversationHistory', () => {
    test('should get conversation history successfully', async () => {
      const mockHistory = {
        session_id: 'test-session',
        messages: [
          { role: 'user', content: 'Hello' },
          { role: 'assistant', content: 'Hi there!' },
        ],
      };

      mockAxios.get.mockResolvedValueOnce({
        data: mockHistory,
      });

      const result =
        await aiAssistantService.getConversationHistory('test-session');

      expect(mockAxios.get).toHaveBeenCalledWith('/api/v1/utils/ai/assistant/history/test-session/', {"timeout": 30000});
      expect(result).toEqual(mockHistory);
    });
  });

  describe('clearConversationHistory', () => {
    test('should clear conversation history successfully', async () => {
      const mockResponse = { success: true };

      mockAxios.delete.mockResolvedValueOnce({
        data: mockResponse,
      });

      const result =
        await aiAssistantService.clearConversationHistory('test-session');

      expect(mockAxios.delete).toHaveBeenCalledWith('/api/v1/utils/ai/assistant/history/test-session/', {"timeout": 30000});
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getPersonalizedSuggestions', () => {
    test('should get personalized suggestions successfully', async () => {
      const mockSuggestions = [
        { id: 1, text: 'Personalized suggestion 1', relevance: 0.9 },
        { id: 2, text: 'Personalized suggestion 2', relevance: 0.8 },
      ];

      mockAxios.post.mockResolvedValueOnce({
        data: { suggestions: mockSuggestions },
      });

      const userContext = { studentId: 123, interests: ['AI', 'Math'] };
      const result =
        await aiAssistantService.getPersonalizedSuggestions(userContext);

      expect(mockAxios.post).toHaveBeenCalledWith(
        '/api/v1/utils/ai/assistant/personalized-suggestions/',
        {
          context: userContext,
        },
        { timeout: 30000 }
      );
      expect(result).toEqual(mockSuggestions);
    });

    test('should fallback to regular suggestions on error', async () => {
      // Mock personalized suggestions to fail
      mockAxios.post.mockRejectedValueOnce(
        new Error('Personalization service down')
      );

      // Mock regular suggestions to succeed
      const fallbackSuggestions = [
        { id: 1, text: 'General suggestion', category: 'general' },
      ];
      mockAxios.get.mockResolvedValueOnce({
        data: { suggestions: fallbackSuggestions },
      });

      const result = await aiAssistantService.getPersonalizedSuggestions({
        studentId: 123,
      });

      // Should have called both personalized (failed) and regular (succeeded)
      expect(mockAxios.post).toHaveBeenCalled();
      expect(mockAxios.get).toHaveBeenCalledWith('suggestions/');
      expect(result).toEqual(fallbackSuggestions);
    }, 15000);
  });

  describe('Error Handling', () => {
    test('should handle network errors gracefully', async () => {
      mockAxios.get.mockRejectedValueOnce(new Error('Network Error'));

      // Should not throw but handle gracefully
      await expect(aiAssistantService.getSuggestions()).resolves.not.toThrow();
    });

    test('should handle timeout errors with retry', async () => {
      const timeoutError = new AIServiceError('Timeout', 'TIMEOUT_ERROR', 408);
      mockAxios.post.mockRejectedValueOnce(timeoutError);

      // Should handle timeout and provide fallback
      const result = await aiAssistantService.askQuestion('Test question');
      expect(result).toBeDefined();
    }, 15000);

    test('should handle validation errors appropriately', async () => {
      const validationError = new AIServiceError(
        'Invalid input',
        'VALIDATION_ERROR',
        400
      );
      mockAxios.post.mockRejectedValueOnce(validationError);

      // Should propagate validation errors (not use fallback)
      await expect(aiAssistantService.askQuestion('')).rejects.toThrow();
    }, 15000);
  });

  describe('Configuration', () => {
    test('should have appropriate default configuration', () => {
      const config = aiAssistantService.getConfig();

      expect(config.timeout).toBe(30000); // 30 seconds
      expect(config.retries).toBe(2);
      expect(config.fallbackEnabled).toBe(true);
      expect(config.cacheEnabled).toBe(true);
      expect(config.cacheTTL).toBe(300000); // 5 minutes
    });

    test('should allow configuration updates', () => {
      const newConfig = { timeout: 45000, retries: 3 };
      aiAssistantService.updateConfig(newConfig);

      const updatedConfig = aiAssistantService.getConfig();
      expect(updatedConfig.timeout).toBe(45000);
      expect(updatedConfig.retries).toBe(3);
    });
  });
});

"""
Models for Immersive Learning Experiences

This module defines models related to virtual and augmented reality content for courses.
"""

from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import CourseContentBase


class VRContent(CourseContentBase):
    """Model for Virtual Reality (VR) content experiences"""
    
    vr_platforms = [
        ('OCULUS', 'Oculus'),
        ('VIVE', 'HTC Vive'),
        ('CARDBOARD', 'Google Cardboard'),
        ('PSVR', 'PlayStation VR'),
        ('WINDOWS_MR', 'Windows Mixed Reality')
    ]
    
    platform = models.CharField(
        max_length=20,
        choices=vr_platforms,
        default='OCULUS'
    )
    vr_file = models.FileField(
        upload_to='vr_content/',
        null=True,
        blank=True,
        help_text=_('3D Object or VR video file')
    )
    
    def __str__(self):
        return f"VR Content for {self.course.title}"


class ARContent(CourseContentBase):
    """Model for Augmented Reality (AR) content experiences"""
    
    ar_platforms = [
        ('AR_KIT', 'ARKit'),
        ('AR_CORE', 'ARCore'),
        ('UNITY', 'Unity AR')
    ]
    
    platform = models.CharField(
        max_length=20,
        choices=ar_platforms,
        default='AR_KIT'
    )
    ar_marker = models.ImageField(
        upload_to='ar_markers/',
        null=True,
        blank=True,
        help_text=_('Marker image for AR content')
    )
    ar_asset = models.FileField(
        upload_to='ar_assets/',
        null=True,
        blank=True,
        help_text=_('AR asset file such as 3D model')
    )
    
    def __str__(self):
        return f"AR Content for {self.course.title}"

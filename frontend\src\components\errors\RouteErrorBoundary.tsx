import React from 'react';
import GlobalErrorBoundary from './GlobalErrorBoundary';

interface RouteErrorBoundaryProps {
  children: React.ReactNode;
  routeName?: string;
}

const RouteErrorBoundary: React.FC<RouteErrorBoundaryProps> = ({ 
  children, 
  routeName 
}) => {
  const handleError = (errorDetails: any) => {
    // Add route-specific error handling
    console.error(`Route Error in ${routeName || 'Unknown Route'}:`, errorDetails);
    
    // Track route-specific error metrics
    if (window.gtag) {
      window.gtag('event', 'route_error', {
        route_name: routeName,
        error_message: errorDetails.error.message,
      });
    }
  };

  const handleReset = () => {
    // Route-specific reset logic
    console.log(`Resetting route: ${routeName}`);
  };

  return (
    <GlobalErrorBoundary
      level="page"
      onError={handleError}
      onReset={handleReset}
    >
      {children}
    </GlobalErrorBoundary>
  );
};

export default RouteErrorBoundary;

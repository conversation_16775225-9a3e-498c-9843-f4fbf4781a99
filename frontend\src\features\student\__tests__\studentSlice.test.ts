import { configureStore } from '@reduxjs/toolkit';
import studentReducer, {
  fetchEnrolledCourses,
  fetchCourseProgress,
  enrollInCourse,
} from '../studentSlice';
import standardizedApiService from '../../../services/standardizedApiService';
import { courseService } from '../../../services/courseService';

// Mock the standardizedApiService
jest.mock('../../../services/standardizedApiService');
const mockedStandardizedApiService = standardizedApiService as jest.Mocked<
  typeof standardizedApiService
>;

// Mock the courseService
jest.mock('../../../services/courseService');
const mockedCourseService = courseService as jest.Mocked<typeof courseService>;

// Mock axios
jest.mock('../../../config/axios', () => ({
  get: jest.fn(),
  post: jest.fn(),
}));

describe('studentSlice', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Create a fresh store for each test
    store = configureStore({
      reducer: {
        student: studentReducer,
      },
    });

    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      },
      writable: true,
    });
  });

  describe('fetchEnrolledCourses', () => {
    test('fetches enrolled courses using standardized API', async () => {
      // Mock the standardizedApiService.getStudentLevelDetails method
      mockedStandardizedApiService.getStudentLevelDetails.mockResolvedValue({
        status: 'success',
        data: {
          current_level: 2,
          last_assessment_score: 85,
          last_assessment_date: '2023-01-01',
        },
      });

      // Mock the standardizedApiService.getStudentCourses method
      mockedStandardizedApiService.getStudentCourses.mockResolvedValue({
        status: 'success',
        data: [
          {
            id: 1,
            title: 'Test Course 1',
            description: 'Test Description 1',
            required_level: 1,
            is_enrolled: true,
          },
          {
            id: 2,
            title: 'Test Course 2',
            description: 'Test Description 2',
            required_level: 2,
            is_enrolled: true,
          },
        ],
      });

      // Dispatch the action
      await store.dispatch(fetchEnrolledCourses());

      // Check that the API was called
      expect(
        mockedStandardizedApiService.getStudentLevelDetails
      ).toHaveBeenCalled();
      expect(
        mockedStandardizedApiService.getStudentCourses
      ).toHaveBeenCalledWith('enrolled');

      // Check the state
      const state = store.getState();
      expect(state.student.enrolledCourses).toHaveLength(2);
      expect(state.student.enrolledCourses[0].id).toBe(1);
      expect(state.student.enrolledCourses[1].id).toBe(2);
      expect(state.student.status).toBe('succeeded');
    });

    test('falls back to legacy endpoints if standardized API fails', async () => {
      // Mock the standardizedApiService.getStudentLevelDetails method to fail
      mockedStandardizedApiService.getStudentLevelDetails.mockRejectedValue(
        new Error('API error')
      );

      // Mock the standardizedApiService.getStudentCourses method to fail
      mockedStandardizedApiService.getStudentCourses.mockRejectedValue(
        new Error('API error')
      );

      // Mock axios.get for the fallback
      const axiosGet = require('../../../config/axios').get;
      axiosGet.mockResolvedValueOnce({
        data: {
          level: 2,
        },
      });
      axiosGet.mockResolvedValueOnce({
        data: {
          status: 'success',
          data: [
            {
              id: 1,
              title: 'Test Course 1',
              description: 'Test Description 1',
              required_level: 1,
              is_enrolled: true,
            },
          ],
        },
      });

      // Dispatch the action
      await store.dispatch(fetchEnrolledCourses());

      // Check that the fallback was called
      expect(axiosGet).toHaveBeenCalledWith('/api/v1/auth/me/');
      expect(axiosGet).toHaveBeenCalledWith('/api/v1/courses/student/courses/?filter=enrolled');

      // Check the state
      const state = store.getState();
      expect(state.student.enrolledCourses).toHaveLength(1);
      expect(state.student.enrolledCourses[0].id).toBe(1);
      expect(state.student.status).toBe('succeeded');
    });
  });

  describe('fetchCourseProgress', () => {
    test('fetches course progress using standardized API', async () => {
      // Mock the standardizedApiService.getCourseProgress method
      mockedStandardizedApiService.getCourseProgress.mockResolvedValue({
        status: 'success',
        data: {
          course: 1,
          progress_percentage: 50,
          is_completed: false,
        },
      });

      // Dispatch the action
      await store.dispatch(fetchCourseProgress(1));

      // Check that the API was called
      expect(
        mockedStandardizedApiService.getCourseProgress
      ).toHaveBeenCalledWith(1);

      // Check the state
      const state = store.getState();
      expect(state.student.courseProgress).toHaveLength(1);
      expect(state.student.courseProgress[0].course).toBe(1);
      expect(state.student.courseProgress[0].progress_percentage).toBe(50);
      expect(state.student.status).toBe('succeeded');
    });

    test('falls back to legacy service if standardized API fails', async () => {
      // Mock the standardizedApiService.getCourseProgress method to fail
      mockedStandardizedApiService.getCourseProgress.mockRejectedValue(
        new Error('API error')
      );

      // Mock the courseService.getCourseProgress method
      mockedCourseService.getCourseProgress.mockResolvedValue({
        data: {
          course: 1,
          progress_percentage: 75,
          is_completed: false,
        },
      });

      // Dispatch the action
      await store.dispatch(fetchCourseProgress(1));

      // Check that the fallback was called
      expect(mockedCourseService.getCourseProgress).toHaveBeenCalledWith(1);

      // Check the state
      const state = store.getState();
      expect(state.student.courseProgress).toHaveLength(1);
      expect(state.student.courseProgress[0].course).toBe(1);
      expect(state.student.courseProgress[0].progress_percentage).toBe(75);
      expect(state.student.status).toBe('succeeded');
    });
  });

  describe('enrollInCourse', () => {
    test('enrolls in a course using standardized API', async () => {
      // Mock the standardizedApiService.enrollInCourse method
      mockedStandardizedApiService.enrollInCourse.mockResolvedValue({
        status: 'success',
        data: {
          enrollment: {
            id: 1,
            course: 1,
            student: 1,
            enrollment_date: '2023-01-01',
          },
          courseId: 1,
        },
      });

      // Dispatch the action
      await store.dispatch(enrollInCourse(1));

      // Check that the API was called
      expect(mockedStandardizedApiService.enrollInCourse).toHaveBeenCalledWith(
        1
      );

      // Check the state
      const state = store.getState();
      expect(state.student.status).toBe('succeeded');
    });

    test('falls back to legacy service if standardized API fails', async () => {
      // Mock the standardizedApiService.enrollInCourse method to fail
      mockedStandardizedApiService.enrollInCourse.mockRejectedValue(
        new Error('API error')
      );

      // Mock the courseService.enrollInCourse method
      mockedCourseService.enrollInCourse.mockResolvedValue({
        status: 'success',
        data: {
          enrollment: {
            id: 1,
            course: 1,
            student: 1,
            enrollment_date: '2023-01-01',
          },
          courseId: 1,
        },
      });

      // Dispatch the action
      await store.dispatch(enrollInCourse(1));

      // Check that the fallback was called
      expect(mockedCourseService.enrollInCourse).toHaveBeenCalledWith(1);

      // Check the state
      const state = store.getState();
      expect(state.student.status).toBe('succeeded');
    });
  });
});

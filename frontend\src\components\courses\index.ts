// Unified Course Components Export with Code Splitting
// All course-related components have been migrated to unified versions

import { lazy } from 'react';

// Lazy-loaded main course components for better performance
export const SuperCourseCard = lazy(() => import('./SuperCourseCard'));
export const UnifiedCourseList = lazy(() => import('./UnifiedCourseList'));
export const VirtualCourseList = lazy(() => import('./VirtualCourseList'));
export const MaterialCard = lazy(() => import('./MaterialCard'));
export const CourseProgress = lazy(() => import('./CourseProgress'));

// Lazy-loaded course generator components
export const ImprovedCourseGenerator = lazy(() => import('../course-generator/ImprovedCourseGenerator'));
export const GeneratedContentCard = lazy(() => import('../course-generator/GeneratedContentCard'));

// Direct imports for critical components (fallback)
export { default as SuperCourseCardDirect } from './SuperCourseCard';
export { default as UnifiedCourseListDirect } from './UnifiedCourseList';


// Re-export types
export type { Course } from '../../types/courses';

// Current component structure (all legacy components have been migrated)
export const COMPONENT_STRUCTURE = {
  // Primary components for course display
  courseCard: 'SuperCourseCard', // Unified course card with multiple variants
  courseList: 'UnifiedCourseList', // Unified course list component
  
  // Specialized components
  courseGenerator: 'ImprovedCourseGenerator',
  materialCard: 'MaterialCard',
  courseProgress: 'CourseProgress',
};

// SuperCourseCard variants and usage guide
export const SUPERCOURSECARD_GUIDE = {
  variants: {
    default: 'Full-featured course card with all elements',
    compact: 'Simplified card for lists (replaces SimpleCourseCard)',
    minimal: 'Basic card with essential information only',
  },
  features: [
    'Enrollment buttons and status',
    'Progress tracking display',
    'Interactive animations',
    'Responsive design',
    'Accessibility features',
    'Customizable styling',
  ],
  migration: {
    from: ['SimpleCourseCard', 'EnhancedCourseCard', 'UnifiedCourseCard', 'ModernCourseCard'],
    to: 'SuperCourseCard with appropriate variant prop',
    status: 'Complete - all legacy cards migrated',
  },
};

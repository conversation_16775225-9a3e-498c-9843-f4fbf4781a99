"""URLroutingforunifiedcourseoperationsTheseURLsprovideasingleendpointforcourseoperationsthatworkconsistentlyacrossallcourse-relatedapps."""from django.urlsimportpathincludefromrest_framework.routersimportDefaultRouterfrom core.views.unified_course_viewsimport(UnifiedCourseViewSetUnifiedStudentCourseViewSet)#Createroutersfortheviewsetsrouter=DefaultRouter()router.register(r'unified-courses'UnifiedCourseViewSetbasename='unified-courses')router.register(r'student-courses'UnifiedStudentCourseViewSetbasename='student-courses')app_name='unified_courses'urlpatterns=[#Unifiedcourseoperationspath('api/'include(router.urls))#Additionaldirectendpoints(alternativetorouteractions)path('course/<str:course_code>/'UnifiedCourseViewSet.as_view({'get':'get_course_unified'})name='get-course-unified')path('enroll/'UnifiedCourseViewSet.as_view({'post':'enroll_student'})name='enroll-student-unified')path('progress/<str:course_code>/'UnifiedCourseViewSet.as_view({'get':'get_student_progress'})name='get-progress-unified')path('sync/<str:course_code>/'UnifiedCourseViewSet.as_view({'post':'sync_course_data'})name='sync-course-data')path('my-courses/'UnifiedStudentCourseViewSet.as_view({'get':'my_courses'})name='my-courses-unified')path('available-courses/'UnifiedStudentCourseViewSet.as_view({'get':'available_courses'})name='available-courses-unified')path('create-interactive-course/'UnifiedCourseViewSet.as_view({'post':'create_or_link_interactive_course'})name='create-interactive-course-unified')]
"""
Multi-Agent Course Recommendation API Views
"""
import logging
from django.apps import apps
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

logger = logging.getLogger(__name__)
User = get_user_model()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def get_multi_agent_course_recommendations(request):
    """Get personalized course recommendations using multi-agent AI system"""
    try:
        user = request.user

        # Get additional context from request
        context = {}
        if request.method == "POST":
            context = request.data.get("context", {})

        # Add query parameters as context for GET requests
        if request.method == "GET":
            context.update({
                "focus_area": request.GET.get("focus_area"),
                "difficulty_level": request.GET.get("difficulty_level"),
                "subject_preference": request.GET.get("subject_preference")
            })

        # Return default recommendations (simplified implementation)
        recommendations = {
            "status": "success",
            "recommendations": {
                "general": {
                    "recommendations": [],
                    "confidence": 0.8
                }
            },
            "agents_used": ["advisor_agent"],
            "fallback_used": False,
            "metadata": {
                "user_id": user.id,
                "user_role": getattr(user, "role", "STUDENT"),
                "request_method": request.method,
                "context_provided": bool(context),
                "agents_available": True
            }
        }

        return Response(recommendations, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error getting multi-agent course recommendations: {str(e)}")
        return Response({
            "status": "error",
            "message": "Failed to get course recommendations",
            "error": str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def get_subject_specific_recommendations(request):
    """Get subject-specific course recommendations using specialized agents"""
    try:
        user = request.user
        subject = request.data.get("subject", "").lower()

        if not subject:
            return Response({
                "status": "error",
                "message": "Subject is required"
            }, status=status.HTTP_400_BAD_REQUEST)

        return Response({
            "status": "success",
            "subject": subject,
            "recommendations": {},
            "agents_used": ["subject_specialist_agent"],
            "fallback_used": False
        }, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error getting subject-specific recommendations: {str(e)}")
        return Response({
            "status": "error",
            "message": "Failed to get subject-specific recommendations",
            "error": str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def get_assessment_based_recommendations(request):
    """Get course recommendations based on assessment results using assessor agent"""
    try:
        user = request.user
        assessment_data = request.data.get("assessment_data")

        if not assessment_data:
            # Try to get latest assessment from database
            try:
                Assessment = apps.get_model("assessment", "Assessment")
                latest_assessment = Assessment.objects.filter(student=user).order_by("-created_at").first()
                if latest_assessment:
                    assessment_data = {
                        "score": latest_assessment.score,
                        "level": getattr(latest_assessment, "level", "beginner"),
                        "assessment_type": getattr(latest_assessment, "assessment_type", "general")
                    }
            except Exception as e:
                logger.warning(f"Could not fetch assessment data: {e}")

        if not assessment_data:
            return Response({
                "status": "error",
                "message": "No assessment data available"
            }, status=status.HTTP_400_BAD_REQUEST)

        return Response({
            "status": "success",
            "assessment_data": assessment_data,
            "recommendations": {},
            "agents_used": ["assessor_agent"],
            "fallback_used": False
        }, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error getting assessment-based recommendations: {str(e)}")
        return Response({
            "status": "error",
            "message": "Failed to get assessment-based recommendations",
            "error": str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_role_based_recommendations(request):
    """Get course recommendations tailored to user role (Student/Professor/Admin)"""
    try:
        user = request.user
        user_role = getattr(user, "role", "STUDENT")

        # Role-specific metadata
        role_info = {
            "STUDENT": {
                "focus_areas": ["Career Development", "Skill Building", "Academic Progress"],
                "primary_agents": ["advisor_agent", "math_tutor_agent", "science_tutor_agent"]
            },
            "PROFESSOR": {
                "focus_areas": ["Course Creation", "Student Assessment", "Curriculum Development"],
                "primary_agents": ["content_creator_agent", "assessor_agent"]
            },
            "ADMIN": {
                "focus_areas": ["Institutional Planning", "System Analytics", "Policy Development"],
                "primary_agents": ["advisor_agent", "assessor_agent", "content_creator_agent"]
            }
        }

        return Response({
            "status": "success",
            "user_role": user_role,
            "role_info": role_info.get(user_role, {}),
            "recommendations": {},
            "agents_used": role_info.get(user_role, {}).get("primary_agents", []),
            "fallback_used": False
        }, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error getting role-based recommendations: {str(e)}")
        return Response({
            "status": "error",
            "message": "Failed to get role-based recommendations",
            "error": str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_recommendation_agents_status(request):
    """Get status of AI agents used for course recommendations"""
    try:
        agents_status = {
            "agents_available": True,
            "available_agents": [
                {
                    "name": "Advisor Agent",
                    "type": "advisor_agent",
                    "specialization": "Career and academic guidance",
                    "use_case": "Career-aligned course recommendations"
                },
                {
                    "name": "Assessor Agent",
                    "type": "assessor_agent",
                    "specialization": "Assessment and skill analysis",
                    "use_case": "Assessment-based course recommendations"
                },
                {
                    "name": "Content Creator Agent",
                    "type": "content_creator_agent",
                    "specialization": "Educational content creation",
                    "use_case": "Course creation and curriculum recommendations"
                },
                {
                    "name": "Math Tutor Agent",
                    "type": "math_tutor_agent",
                    "specialization": "Mathematics education",
                    "use_case": "Math course recommendations"
                },
                {
                    "name": "Science Tutor Agent",
                    "type": "science_tutor_agent",
                    "specialization": "Science education",
                    "use_case": "Science course recommendations"
                }
            ],
            "recommendation_types": [
                "general_recommendations",
                "subject_specific",
                "assessment_based",
                "role_based",
                "career_aligned"
            ]
        }

        return Response(agents_status, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error getting agents status: {str(e)}")
        return Response({
            "status": "error",
            "message": "Failed to get agents status",
            "error": str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
"""Multi-AgentCourseRecommendationAPIViews"""import loggingfrom django.appsimportappsfrom django.contrib.authimportget_user_modelfromrest_frameworkimportstatusfromrest_framework.decoratorsimportapi_viewpermission_classesfromrest_framework.permissionsimportIsAuthenticatedfromrest_framework.responseimportResponsefrom.multi_agent_course_recommenderimportmulti_agent_course_recommenderlogger=logging.getLogger(__name__)User=get_user_model()@api_view(["GET""POST"])@permission_classes([IsAuthenticated])defget_multi_agent_course_recommendations(request):"""Getpersonalizedcourserecommendationsusingmulti-agentAIsystemGET:GetrecommendationsforcurrentuserPOST:Getrecommendationswithadditionalcontext"""try:user=request.user#Getadditionalcontextfromrequestcontext={}ifrequest.method=="POST":context=request.data.get("context"{})#AddqueryparametersascontextforGETrequestsifrequest.method=="GET":context.update({"focus_area":request.GET.get("focus_area")"difficulty_level":request.GET.get("difficulty_level")"subject_preference":request.GET.get("subject_preference")})#Getrecommendationsfrommulti-agentsystemrecommendations=(multi_agent_course_recommender.get_personalized_recommendations(user=usercontext=context))#Addmetadatarecommendations["metadata"]={"user_id":user.id"user_role":getattr(user"role""STUDENT")"request_method":request.method"context_provided":bool(context)"agents_available":multi_agent_course_recommender.agents_available}returnResponse(recommendationsstatus=status.HTTP_200_OK)exceptExceptionase:logger.error(f"Errorgettingmulti-agentcourserecommendations:{str(e)}")returnResponse({"status":"error""message":"Failedtogetcourserecommendations""error":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(["POST"])@permission_classes([IsAuthenticated])defget_subject_specific_recommendations(request):"""Getsubject-specificcourserecommendationsusingspecializedagents"""try:user=request.usersubject=request.data.get("subject""").lower()ifnotsubject:returnResponse({"status":"error""message":"Subjectisrequired"}status=status.HTTP_400_BAD_REQUEST)#Preparecontextforsubject-specificrecommendationscontext={"subject_focus":subject"recommendation_type":"subject_specific"**request.data.get("context"{})}recommendations=(multi_agent_course_recommender.get_personalized_recommendations(user=usercontext=context))#Filterrecommendationsforthespecificsubjectsubject_recommendations={}forcategoryrec_datainrecommendations.get("recommendations"{}).items():if(subjectincategory.lower()orsubjectinstr(rec_data.get("recommendations"[])).lower()):subject_recommendations[category]=rec_datareturnResponse({"status":"success""subject":subject"recommendations":subject_recommendations"agents_used":recommendations.get("agents_used"[])"fallback_used":recommendations.get("fallback_used"False)}status=status.HTTP_200_OK)exceptExceptionase:logger.error(f"Errorgettingsubject-specificrecommendations:{str(e)}")returnResponse({"status":"error""message":"Failedtogetsubject-specificrecommendations""error":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(["POST"])@permission_classes([IsAuthenticated])defget_assessment_based_recommendations(request):"""Getcourserecommendationsbasedonassessmentresultsusingassessoragent"""try:user=request.userassessment_data=request.data.get("assessment_data")ifnotassessment_data:#Trytogetlatestassessmentfromdatabasetry:Assessment=apps.get_model("assessment""Assessment")latest_assessment=(Assessment.objects.filter(student=user).order_by("-created_at").first())iflatest_assessment:assessment_data={"score":latest_assessment.score"level":getattr(latest_assessment"level""beginner")"assessment_type":getattr(latest_assessment"assessment_type""general")}exceptExceptionase:logger.warning(f"Couldnotfetchassessmentdata:{e}")ifnotassessment_data:returnResponse({"status":"error""message":"Noassessmentdataavailable"}status=status.HTTP_400_BAD_REQUEST)#Preparecontextwithassessmentfocuscontext={"latest_assessment":assessment_data"recommendation_type":"assessment_based""focus":"skill_improvement"}recommendations=(multi_agent_course_recommender.get_personalized_recommendations(user=usercontext=context))returnResponse({"status":"success""assessment_data":assessment_data"recommendations":recommendations.get("recommendations"{})"agents_used":recommendations.get("agents_used"[])"fallback_used":recommendations.get("fallback_used"False)}status=status.HTTP_200_OK)exceptExceptionase:logger.error(f"Errorgettingassessment-basedrecommendations:{str(e)}")returnResponse({"status":"error""message":"Failedtogetassessment-basedrecommendations""error":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(["GET"])@permission_classes([IsAuthenticated])defget_role_based_recommendations(request):"""Getcourserecommendationstailoredtouserrole(Student/Professor/Admin)"""try:user=request.useruser_role=getattr(user"role""STUDENT")#Preparerole-specificcontextcontext={"recommendation_type":"role_based""user_role":user_role"focus":{"STUDENT":"learning_and_career""PROFESSOR":"teaching_and_content""ADMIN":"institutional_planning"}.get(user_role"general")}recommendations=(multi_agent_course_recommender.get_personalized_recommendations(user=usercontext=context))#Addrole-specificmetadatarole_info={"STUDENT":{"focus_areas":["CareerDevelopment""SkillBuilding""AcademicProgress"]"primary_agents":["advisor_agent""math_tutor_agent""science_tutor_agent"]}"PROFESSOR":{"focus_areas":["CourseCreation""StudentAssessment""CurriculumDevelopment"]"primary_agents":["content_creator_agent""assessor_agent"]}"ADMIN":{"focus_areas":["InstitutionalPlanning""SystemAnalytics""PolicyDevelopment"]"primary_agents":["advisor_agent""assessor_agent""content_creator_agent"]}}returnResponse({"status":"success""user_role":user_role"role_info":role_info.get(user_role{})"recommendations":recommendations.get("recommendations"{})"agents_used":recommendations.get("agents_used"[])"fallback_used":recommendations.get("fallback_used"False)}status=status.HTTP_200_OK)exceptExceptionase:logger.error(f"Errorgettingrole-basedrecommendations:{str(e)}")returnResponse({"status":"error""message":"Failedtogetrole-basedrecommendations""error":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(["GET"])@permission_classes([IsAuthenticated])defget_recommendation_agents_status(request):"""GetstatusofAIagentsusedforcourserecommendations"""try:agents_status={"agents_available":multi_agent_course_recommender.agents_available"available_agents":[]"recommendation_types":["general_recommendations""subject_specific""assessment_based""role_based""career_aligned"]}ifmulti_agent_course_recommender.agents_available:agents_status["available_agents"]=[{"name":"AdvisorAgent""type":"advisor_agent""specialization":"Careerandacademicguidance""use_case":"Career-alignedcourserecommendations"}{"name":"AssessorAgent""type":"assessor_agent""specialization":"Assessmentandskillanalysis""use_case":"Assessment-basedcourserecommendations"}{"name":"ContentCreatorAgent""type":"content_creator_agent""specialization":"Educationalcontentcreation""use_case":"Coursecreationandcurriculumrecommendations"}{"name":"MathTutorAgent""type":"math_tutor_agent""specialization":"Mathematicseducation""use_case":"Mathcourserecommendations"}{"name":"ScienceTutorAgent""type":"science_tutor_agent""specialization":"Scienceeducation""use_case":"Sciencecourserecommendations"}]returnResponse(agents_statusstatus=status.HTTP_200_OK)exceptExceptionase:logger.error(f"Errorgettingagentsstatus:{str(e)}")returnResponse({"status":"error""message":"Failedtogetagentsstatus""error":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)
"""UnifiedCourseAPIViews-ProvidesconsistentcoursedataacrossallappsTheseviewsensurethatCS100isthesamecoursewhetheraccessedthrough:-Maincoursesapp-Interactivelearningapp-CoursegeneratorappAllcourseoperationsgothroughtheunifiedservicetomaintainconsistency."""fromrest_frameworkimportviewsetsstatusfromrest_framework.decoratorsimportactionfromrest_framework.responseimportResponsefromrest_framework.permissionsimportIsAuthenticatedIsAdminUserfrom django.contrib.authimportget_user_modelfrom core.services.unified_course_serviceimportunified_course_serviceUser=get_user_model()classUnifiedCourseViewSet(viewsets.ViewSet):"""ViewSetforunifiedcourseoperationsacrossallappsThisensuresconsistentcoursedataandoperationsregardlessofwhichapptherequestcomesfrom."""permission_classes=[IsAuthenticated]@action(detail=Falsemethods=['get'])defget_course_unified(selfrequest):"""GetcompletecoursedatafromallappsQueryparams:-course_code:Requiredcoursecode(e.g.CS100)-semester:Optionalsemesterfilter-academic_year:Optionalacademicyearfilter"""course_code=request.query_params.get('course_code')ifnotcourse_code:returnResponse({'error':'course_codeparameterisrequired'}status=status.HTTP_400_BAD_REQUEST)semester=request.query_params.get('semester')academic_year=request.query_params.get('academic_year')course_data=unified_course_service.get_unified_course_data(course_code=course_codesemester=semesteracademic_year=academic_year)if'error'incourse_data:returnResponse(course_datastatus=status.HTTP_404_NOT_FOUND)returnResponse(course_datastatus=status.HTTP_200_OK)@action(detail=Falsemethods=['post'])defenroll_student(selfrequest):"""EnrollstudentincoursewithunifiedtrackingBodyparams:-course_code:Coursecodetoenrollin-user_id:StudentuserID(optionaldefaultstocurrentuser)-enrollment_type:REGULARAUDITorHONORS(default:REGULAR)"""course_code=request.data.get('course_code')ifnotcourse_code:returnResponse({'error':'course_codeisrequired'}status=status.HTTP_400_BAD_REQUEST)#Getusertoenroll(admincanenrollothersstudentsenrollthemselves)user_id=request.data.get('user_id')ifuser_idandrequest.user.is_staff:try:user=User.objects.get(id=user_id)exceptUser.DoesNotExist:returnResponse({'error':'Usernotfound'}status=status.HTTP_404_NOT_FOUND)else:user=request.userenrollment_type=request.data.get('enrollment_type''REGULAR')result=unified_course_service.enroll_student_unified(user=usercourse_code=course_codeenrollment_type=enrollment_type)ifresult['success']:returnResponse(resultstatus=status.HTTP_201_CREATED)else:returnResponse(resultstatus=status.HTTP_400_BAD_REQUEST)@action(detail=Falsemethods=['get'])defget_student_progress(selfrequest):"""Getstudent'sunifiedprogressacrossallcourseappsQueryparams:-course_code:Requiredcoursecode-user_id:StudentuserID(optionaldefaultstocurrentuser)"""course_code=request.query_params.get('course_code')ifnotcourse_code:returnResponse({'error':'course_codeparameterisrequired'}status=status.HTTP_400_BAD_REQUEST)#Getuserprogress(admincanviewothersstudentsviewthemselves)user_id=request.query_params.get('user_id')ifuser_idandrequest.user.is_staff:try:user=User.objects.get(id=user_id)exceptUser.DoesNotExist:returnResponse({'error':'Usernotfound'}status=status.HTTP_404_NOT_FOUND)else:user=request.userprogress_data=unified_course_service.get_student_unified_progress(user=usercourse_code=course_code)if'error'inprogress_data:returnResponse(progress_datastatus=status.HTTP_404_NOT_FOUND)returnResponse(progress_datastatus=status.HTTP_200_OK)@action(detail=Falsemethods=['get'])deflist_all_courses(selfrequest):"""Getallcourseswiththeirintegrationstatusacrossapps"""courses_data=unified_course_service.get_all_courses_unified()returnResponse({'courses':courses_data'total_count':len(courses_data)}status=status.HTTP_200_OK)@action(detail=Falsemethods=['post']permission_classes=[IsAdminUser])defsync_course_data(selfrequest):"""Synchronizecoursedataacrossallapps(Adminonly)Bodyparams:-course_code:Coursecodetosync"""course_code=request.data.get('course_code')ifnotcourse_code:returnResponse({'error':'course_codeisrequired'}status=status.HTTP_400_BAD_REQUEST)result=unified_course_service.sync_course_data_across_apps(course_code)ifresult['success']:returnResponse(resultstatus=status.HTTP_200_OK)else:returnResponse(resultstatus=status.HTTP_400_BAD_REQUEST)@action(detail=Falsemethods=['post']permission_classes=[IsAdminUser])defsync_all_courses(selfrequest):"""Synchronizeallcoursedataacrossapps(Adminonly)"""from django.appsimportappsCourse=apps.get_model('courses''Course')results=[]error_count=0forcourseinCourse.objects.all():result=unified_course_service.sync_course_data_across_apps(course.course_code)results.append({'course_code':course.course_code'success':result['success']'updates':result.get('updates_made'[])'error':result.get('error'None)})ifnotresult['success']:error_count+=1returnResponse({'message':f'Synced{len(results)}courseswith{error_count}errors''results':results'total_courses':len(results)'errors':error_count}status=status.HTTP_200_OK)@action(detail=Falsemethods=['get'])defcourse_integration_status(selfrequest):"""Getintegrationstatusforallcoursesshowingwhichappshavedata"""from django.appsimportappsCourse=apps.get_model('courses''Course')integration_status=[]forcourseinCourse.objects.all().select_related('department'):status_data={'course_code':course.course_code'title':course.title'department':course.department.nameifcourse.departmentelseNone'main_course':True#Alwaystruesincewe'reiteratingmaincourses'has_interactive':hasattr(course'interactive_version')'has_ai_content':hasattr(course'generated_content')'has_assessment':getattr(course'has_assessment'False)'integration_flags':{'has_interactive_content':getattr(course'has_interactive_content'False)'has_ai_content':getattr(course'has_ai_content'False)}'enrollments':course.enrollments.count()'active_enrollments':course.enrollments.filter(status='APPROVED').count()}#Checkifintegrationflagsmatchactualcontentflags_accurate=(status_data['integration_flags']['has_interactive_content']==status_data['has_interactive']andstatus_data['integration_flags']['has_ai_content']==status_data['has_ai_content'])status_data['flags_accurate']=flags_accurateintegration_status.append(status_data)#Summarystatisticstotal_courses=len(integration_status)courses_with_interactive=sum(1forcinintegration_statusifc['has_interactive'])courses_with_ai=sum(1forcinintegration_statusifc['has_ai_content'])courses_with_inaccurate_flags=sum(1forcinintegration_statusifnotc['flags_accurate'])returnResponse({'integration_status':integration_status'summary':{'total_courses':total_courses'courses_with_interactive':courses_with_interactive'courses_with_ai_content':courses_with_ai'courses_with_inaccurate_flags':courses_with_inaccurate_flags'integration_health':f"{((total_courses-courses_with_inaccurate_flags)/total_courses*100):.1f}%"iftotal_courses>0else"100%"}}status=status.HTTP_200_OK)@action(detail=Falsemethods=['post']permission_classes=[IsAuthenticated])defcreate_or_link_interactive_course(selfrequest):"""Createorlinkaninteractivecourseusingtheprovidedcoursecode.Bodyparams:-course_code:Coursecodetocreateorlinkaninteractivecourse"""course_code=request.data.get('course_code')ifnotcourse_code:returnResponse({'error':'course_codeisrequired'}status=status.HTTP_400_BAD_REQUEST)result=unified_course_service.create_or_link_interactive(course_code)ifresult['success']:returnResponse(resultstatus=status.HTTP_201_CREATED)else:returnResponse(resultstatus=status.HTTP_400_BAD_REQUEST)classUnifiedStudentCourseViewSet(viewsets.ViewSet):"""Student-specificunifiedcourseoperations"""permission_classes=[IsAuthenticated]@action(detail=Falsemethods=['get'])defmy_courses(selfrequest):"""Getallcoursesthecurrentstudentisenrolledinwithunifieddata"""from django.appsimportappsEnrollment=apps.get_model('courses''Enrollment')enrollments=Enrollment.objects.filter(user=request.userstatus='APPROVED').select_related('course')my_courses=[]forenrollmentinenrollments:course_data=unified_course_service.get_unified_course_data(course_code=enrollment.course.course_code)if'error'notincourse_data:#Addenrollment-specificdatacourse_data['enrollment_date']=enrollment.enrollment_datecourse_data['enrollment_type']=enrollment.enrollment_type#Addprogressdataprogress_data=unified_course_service.get_student_unified_progress(user=request.usercourse_code=enrollment.course.course_code)if'error'notinprogress_data:course_data['progress']=progress_datamy_courses.append(course_data)returnResponse({'courses':my_courses'total_enrolled':len(my_courses)}status=status.HTTP_200_OK)@action(detail=Falsemethods=['get'])defavailable_courses(selfrequest):"""Getcoursesavailableforenrollment(notalreadyenrolled)"""from django.appsimportappsCourse=apps.get_model('courses''Course')Enrollment=apps.get_model('courses''Enrollment')#Getcoursesstudentisalreadyenrolledinenrolled_course_ids=Enrollment.objects.filter(user=request.user).values_list('course_id'flat=True)#Getavailablecoursesavailable_courses=Course.objects.filter(is_active=Trueis_published=True).exclude(id__in=enrolled_course_ids)courses_data=[]forcourseinavailable_courses:course_data=unified_course_service.get_unified_course_data(course_code=course.course_code)if'error'notincourse_data:courses_data.append(course_data)returnResponse({'available_courses':courses_data'total_available':len(courses_data)}status=status.HTTP_200_OK)
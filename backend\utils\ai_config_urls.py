"""
Unified AI Configuration URLs

This module provides URL patterns for the consolidated AI configuration system.
All AI configuration endpoints are now centralized here.
"""

from django.urls import path
from . import ai_config_views

app_name = 'ai_config'

urlpatterns = [
    # Unified AI Configuration endpoints
    path('', ai_config_views.ai_health_check, name='ai_config'),
    path('health/', ai_config_views.ai_health_check, name='health_check'),

    # Direct AI endpoints
    path('tutor/', ai_config_views.tutor_endpoint, name='tutor_endpoint'),
    path('chat/', ai_config_views.chat_endpoint, name='chat_endpoint'),
    path('assistant/', ai_config_views.assistant_endpoint, name='assistant_endpoint'),
    path('multi-agent-chat/', ai_config_views.multi_agent_chat_endpoint, name='multi_agent_chat_endpoint'),

    # Chat conversations endpoints
    path('chat/conversations/', ai_config_views.conversations_endpoint, name='conversations_endpoint'),
    path('chat/conversations/<int:conversation_id>/', ai_config_views.conversation_detail_endpoint, name='conversation_detail_endpoint'),
]

# Export as ai_config_urlpatterns for use in utils.urls
ai_config_urlpatterns = urlpatterns
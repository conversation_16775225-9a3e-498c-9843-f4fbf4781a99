"""
Consolidated Course URLs

This provides unified course API endpoints without versioning.
All endpoints use standard /api/courses/ structure.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from core.views.consolidated_course_api import (
    ConsolidatedCourseViewSet,
    ConsolidatedEnrollmentViewSet,
    ConsolidatedProgressViewSet
)

# Main API router - no versioning
router = DefaultRouter()

# Register unified viewsets
router.register(r'courses', ConsolidatedCourseViewSet, basename='courses')
router.register(r'enrollments', ConsolidatedEnrollmentViewSet, basename='enrollments')
router.register(r'progress', ConsolidatedProgressViewSet, basename='progress')

# URL patterns
urlpatterns = [
    # Main unified API
    path('', include(router.urls)),
    
    # Course-specific endpoints
    path('courses/my-courses/', ConsolidatedCourseViewSet.as_view({'get': 'my_courses'}), name='my-courses'),
    path('courses/available/', ConsolidatedCourseViewSet.as_view({'get': 'available_courses'}), name='available-courses'),
    path('courses/search/', ConsolidatedCourseViewSet.as_view({'get': 'search'}), name='course-search'),
    path('courses/features/', ConsolidatedCourseViewSet.as_view({'get': 'features_overview'}), name='course-features'),
    
    # Enrollment management
    path('enrollments/<int:pk>/withdraw/', ConsolidatedEnrollmentViewSet.as_view({'post': 'withdraw'}), name='enrollment-withdraw'),
    
    # Progress tracking
    path('progress/summary/', ConsolidatedProgressViewSet.as_view({'get': 'summary'}), name='progress-summary'),
]

# Export for inclusion in main project URLs
consolidated_patterns = urlpatterns

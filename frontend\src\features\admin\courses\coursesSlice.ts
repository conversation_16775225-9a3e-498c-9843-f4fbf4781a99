import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { UnifiedCourse } from '../../../types/unified';
import { unifiedCourseService } from '../../../services/unifiedCourseService';

interface CoursesState {
  courses: UnifiedCourse[];
  loading: boolean;
  error: string | null;
  selectedCourse: UnifiedCourse | null;
}

const initialState: CoursesState = {
  courses: [],
  loading: false,
  error: null,
  selectedCourse: null,
};

// Async thunk for fetching courses
export const fetchCourses = createAsyncThunk(
  'courses/fetchCourses',
  async (_, { rejectWithValue }) => {
    try {
      const response = await unifiedCourseService.getCourses('ADMIN');
      if (response.status === 'success') {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch courses');
      }
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }
);

const coursesSlice = createSlice({
  name: 'courses',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setCourses: (state, action: PayloadAction<UnifiedCourse[]>) => {
      state.courses = action.payload;
    },
    addCourse: (state, action: PayloadAction<UnifiedCourse>) => {
      state.courses.push(action.payload);
    },
    updateCourse: (state, action: PayloadAction<UnifiedCourse>) => {
      const index = state.courses.findIndex(
        course => course.id === action.payload.id
      );
      if (index !== -1) {
        state.courses[index] = action.payload;
      }
    },
    deleteCourse: (state, action: PayloadAction<string>) => {
      state.courses = state.courses.filter(
        course => course.id !== action.payload
      );
    },
    setSelectedCourse: (state, action: PayloadAction<UnifiedCourse | null>) => {
      state.selectedCourse = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchCourses.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCourses.fulfilled, (state, action) => {
        state.loading = false;
        state.courses = action.payload;
      })
      .addCase(fetchCourses.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setLoading,
  setError,
  setCourses,
  addCourse,
  updateCourse,
  deleteCourse,
  setSelectedCourse,
} = coursesSlice.actions;

export default coursesSlice.reducer;

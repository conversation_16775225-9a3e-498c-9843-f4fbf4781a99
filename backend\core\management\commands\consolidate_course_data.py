"""Managementcommandtoconsolidatecoursedataacrossall3courseappsThiscommandensuresthatCS100isthesamecourseeverywhereby:1.Findingduplicatecourserecordsacrossapps2.Consolidatingthemintosinglemaincourserecords3.Settingupproperrelationships4.Syncingstudentenrollmentandprogressdata"""fromdjango.core.management.baseimportBaseCommandfromdjango.dbimporttransactionfromdjango.appsimportappsfromdjango.contrib.authimportget_user_modelfromcore.services.unified_course_serviceimportunified_course_serviceUser=get_user_model()classCommand(BaseCommand):help='Consolidatecoursedataacrossallcourse-relatedappstoensureconsistency'defadd_arguments(selfparser):parser.add_argument('--dry-run'action='store_true'help='Showwhatwouldbedonewithoutmakingchanges')parser.add_argument('--course-code'type=strhelp='Onlyprocessspecificcoursecode')parser.add_argument('--fix-duplicates'action='store_true'help='Fixduplicatecourserecordsacrossapps')defhandle(self*args**options):dry_run=options['dry_run']course_code=options['course_code']fix_duplicates=options['fix_duplicates']ifdry_run:self.stdout.write(self.style.WARNING('DRYRUNMODE-Nochangeswillbemade'))self.stdout.write('Startingcoursedataconsolidation...')try:withtransaction.atomic():#Step1:Analyzecurrentstateself.analyze_course_consistency()#Step2:Fixduplicatecoursesifrequestediffix_duplicates:self.fix_duplicate_courses(dry_runcourse_code)#Step3:Syncallcoursedataself.sync_all_course_data(dry_runcourse_code)#Step4:Validateconsistencyself.validate_course_consistency()ifdry_run:transaction.set_rollback(True)self.stdout.write(self.style.SUCCESS('DRYRUNCOMPLETED-Nochangesmade'))else:self.stdout.write(self.style.SUCCESS('Coursedataconsolidationcompletedsuccessfully'))exceptExceptionase:self.stdout.write(self.style.ERROR(f'Errorduringconsolidation:{str(e)}'))raisedefanalyze_course_consistency(self):"""Analyzethecurrentstateofcoursedataacrossapps"""self.stdout.write('Analyzingcoursedataconsistency...')Course=apps.get_model('courses''Course')try:InteractiveCourseVersion=apps.get_model('InteractiveCourseVersion')exceptLookupError:InteractiveCourseVersion=Nonetry:GeneratedCourseContent=apps.get_model('course_generator''GeneratedCourseContent')exceptLookupError:GeneratedCourseContent=None#Countcoursesineachappmain_courses=Course.objects.count()interactive_courses=InteractiveCourseVersion.objects.count()ifInteractiveCourseVersionelse0generated_courses=GeneratedCourseContent.objects.count()ifGeneratedCourseContentelse0self.stdout.write(f'Maincourses:{main_courses}')self.stdout.write(f'Interactivecourses:{interactive_courses}')self.stdout.write(f'Generatedcourses:{generated_courses}')#CheckfororphanedinteractivecoursesifInteractiveCourseVersion:orphaned_interactive=0forinteractiveinInteractiveCourseVersion.objects.all():#Checkbothold'course'fieldandnew'base_course'fieldhas_main_course=Falseifhasattr(interactive'base_course')andinteractive.base_course:has_main_course=Trueelifhasattr(interactive'course')andinteractive.course:has_main_course=Trueifnothas_main_course:orphaned_interactive+=1iforphaned_interactive>0:self.stdout.write(self.style.WARNING(f'Found{orphaned_interactive}orphanedinteractivecourses'))#CheckfororphanedgeneratedcoursesifGeneratedCourseContent:orphaned_generated=0forgeneratedinGeneratedCourseContent.objects.all():has_main_course=Falseifhasattr(generated'base_course')andgenerated.base_course:has_main_course=Trueelifhasattr(generated'request')andgenerated.requestandgenerated.request.course:has_main_course=Trueifnothas_main_course:orphaned_generated+=1iforphaned_generated>0:self.stdout.write(self.style.WARNING(f'Found{orphaned_generated}orphanedgeneratedcourses'))deffix_duplicate_courses(selfdry_runspecific_course_code=None):"""Fixduplicatecourserecordsacrossapps"""self.stdout.write('Fixingduplicatecourserecords...')Course=apps.get_model('courses''Course')#Findcourseswiththesamecourse_codebutdifferentrecordscourse_codes=Course.objects.values_list('course_code'flat=True).distinct()ifspecific_course_code:course_codes=[specific_course_code]ifspecific_course_codeincourse_codeselse[]duplicates_fixed=0forcourse_codeincourse_codes:courses_with_same_code=Course.objects.filter(course_code=course_code)ifcourses_with_same_code.count()>1:self.stdout.write(f'Found{courses_with_same_code.count()}courseswithcode{course_code}')#Keepthemostrecent/completecourseasprimaryprimary_course=courses_with_same_code.order_by('-updated_at''-id').first()duplicate_courses=courses_with_same_code.exclude(id=primary_course.id)forduplicateinduplicate_courses:ifnotdry_run:#Moveenrollmentstoprimarycourseduplicate.enrollments.update(course=primary_course)#Moveprogressrecordstoprimarycourseduplicate.student_progress.update(course=primary_course)#Movematerialstoprimarycourseduplicate.materials.update(course=primary_course)#Deletetheduplicateduplicate.delete()duplicates_fixed+=1self.stdout.write(f'-Mergedduplicatecourse{duplicate.id}into{primary_course.id}')ifduplicates_fixed>0:self.stdout.write(f'Fixed{duplicates_fixed}duplicatecourses')defsync_all_course_data(selfdry_runspecific_course_code=None):"""Synccoursedataacrossallapps"""self.stdout.write('Syncingcoursedataacrossapps...')Course=apps.get_model('courses''Course')courses_to_sync=Course.objects.all()ifspecific_course_code:courses_to_sync=courses_to_sync.filter(course_code=specific_course_code)synced_count=0error_count=0forcourseincourses_to_sync:try:ifnotdry_run:result=unified_course_service.sync_course_data_across_apps(course.course_code)ifresult['success']:synced_count+=1ifresult.get('updates_made'):self.stdout.write(f'Synced{course.course_code}:{"".join(result["updates_made"])}')else:error_count+=1self.stdout.write(self.style.ERROR(f'Errorsyncing{course.course_code}:{result.get("error""Unknownerror")}'))else:#Dryrun-justcheckwhatwouldbesyncedself.stdout.write(f'Wouldsync:{course.course_code}')synced_count+=1exceptExceptionase:error_count+=1self.stdout.write(self.style.ERROR(f'Exceptionsyncing{course.course_code}:{str(e)}'))self.stdout.write(f'Synced{synced_count}courseswith{error_count}errors')defvalidate_course_consistency(self):"""Validatethatcoursedataisnowconsistentacrossapps"""self.stdout.write('Validatingcourseconsistency...')Course=apps.get_model('courses''Course')consistency_issues=0forcourseinCourse.objects.all():#Checkintegrationflagshas_interactive=hasattr(course'interactive_version')has_ai=hasattr(course'generated_content')flag_interactive=getattr(course'has_interactive_content'False)flag_ai=getattr(course'has_ai_content'False)ifhas_interactive!=flag_interactive:consistency_issues+=1self.stdout.write(self.style.WARNING(f'{course.course_code}:Interactiveflagmismatch(actual:{has_interactive}flag:{flag_interactive})'))ifhas_ai!=flag_ai:consistency_issues+=1self.stdout.write(self.style.WARNING(f'{course.course_code}:AIcontentflagmismatch(actual:{has_ai}flag:{flag_ai})'))#Checkenrollmentconsistencyenrollments=course.enrollments.count()progress_records=course.student_progress.count()ifenrollments!=progress_records:self.stdout.write(self.style.WARNING(f'{course.course_code}:Enrollment/progressmismatch(enrollments:{enrollments}progress:{progress_records})'))ifconsistency_issues==0:self.stdout.write(self.style.SUCCESS('Allcoursesareconsistentacrossapps'))else:self.stdout.write(self.style.WARNING(f'Found{consistency_issues}consistencyissues'))defcreate_missing_relationships(selfdry_run):"""Createmissingrelationshipsbetweenmaincoursesandotherapps"""self.stdout.write('Creatingmissingcourserelationships...')Course=apps.get_model('courses''Course')try:InteractiveCourseVersion=apps.get_model('InteractiveCourseVersion')exceptLookupError:InteractiveCourseVersion=Nonetry:GeneratedCourseContent=apps.get_model('course_generator''GeneratedCourseContent')exceptLookupError:GeneratedCourseContent=Nonecreated_relationships=0#CheckinteractivecourseswithoutproperrelationshipsifInteractiveCourseVersion:forinteractiveinInteractiveCourseVersion.objects.all():ifnothasattr(interactive'base_course')ornotinteractive.base_course:#Trytofindmatchingmaincourseifhasattr(interactive'course')andinteractive.course:ifnotdry_run:interactive.base_course=interactive.courseinteractive.save(update_fields=['base_course'])created_relationships+=1self.stdout.write(f'Createdbase_courserelationshipforinteractivecourse{interactive.id}')#CheckgeneratedcourseswithoutproperrelationshipsifGeneratedCourseContent:forgeneratedinGeneratedCourseContent.objects.all():ifnothasattr(generated'base_course')ornotgenerated.base_course:#Trytofindcoursethroughrequestifhasattr(generated'request')andgenerated.requestandgenerated.request.course:ifnotdry_run:generated.base_course=generated.request.coursegenerated.save(update_fields=['base_course'])created_relationships+=1self.stdout.write(f'Createdbase_courserelationshipforgeneratedcourse{generated.id}')ifcreated_relationships>0:self.stdout.write(f'Created{created_relationships}missingrelationships')
"""Managementcommandtoresetmigrations.ThiscommandprovidesaDjangointerfacetothecomprehensivemigrationresetscript."""importdatetimeimportosimportshutilimportsysfrompathlibimportPathfromdjango.appsimportappsfromdjango.core.management.baseimportBaseCommandCommandErrorfromdjango.dbimportconnectionclassCommand(BaseCommand):help='Resetmigrationsforallappsorspecificapps'defadd_arguments(selfparser):parser.add_argument('--no-input'action='store_true'help='Skipconfirmationprompts')parser.add_argument('--keep-db'action='store_true'help='Keepthedatabasejustresetmigrations')parser.add_argument('--fake'action='store_true'help='Fakemigrationsinsteadofcreatingnewones')parser.add_argument('apps'nargs='*'help='Specificappstoresetmigrationsfor(default:allapps)')defhandle(self*args**options):#Getthebasedirectorybase_dir=Path(__file__).resolve().parent.parent.parent.parent#Getlistofappsifoptions['apps']:app_names=options['apps']else:app_configs=apps.get_app_configs()app_names=[app.nameforappinapp_configsifnotapp.name.startswith('django')andnotapp.name.startswith('rest_framework')andnotapp.name.startswith('corsheaders')]self.stdout.write(self.style.SUCCESS(f"Found{len(app_names)}apps:{''.join(app_names)}"))#Confirmactionifnotoptions['no_input']:confirm=input("\nWARNING:Thiswilldeleteallmigrationfilesandmaydeleteyourdatabase.Continue?[y/N]:").lower().strip()ifconfirm!='y':self.stdout.write(self.style.WARNING('Operationcancelled.'))return#Deletemigrationsself._delete_migrations(app_names)#Deletedatabaseifrequestedifnotoptions['keep_db']:db_deleted=self._delete_database(base_dir)else:db_deleted=Falseself.stdout.write(self.style.SUCCESS('\nKeepingexistingdatabaseasrequested.'))#Createandapplymigrationsifoptions['fake']:#Ifwe'refakingmigrationsweneedtomakesurethedatabaseexistsifdb_deleted:#Createanewdatabasewithinitialmigrationsself._apply_migrations()#Fakeallmigrationsself._fake_migrations(app_names)else:#Createnewmigrationsself._create_initial_migrations(app_names)#Applymigrationsself._apply_migrations()self.stdout.write(self.style.SUCCESS('\n===MigrationResetComplete==='))self.stdout.write(self.style.SUCCESS('Youmaynowneedtocreateasuperuser:'))self.stdout.write(self.style.SUCCESS('pythonmanage.pycreatesuperuser'))def_delete_migrations(selfapp_names):"""Deleteallmigrationfilesforthegivenapps."""base_dir=Path(__file__).resolve().parent.parent.parent.parentforapp_nameinapp_names:#Handlebothdirectappnamesandappswithdotsapp_path=app_name.replace('.''/')migration_dir=base_dir/app_path/'migrations'ifnotmigration_dir.exists():self.stdout.write(self.style.WARNING(f"Migrationdirectoryfor{app_name}notfound."))continueself.stdout.write(self.style.SUCCESS(f"Deletingmigrationsfor{app_name}..."))#Createmigrationsdirectoryifitdoesn'texistmigration_dir.mkdir(exist_ok=True)#Create__init__.pyifitdoesn'texistinit_file=migration_dir/'__init__.py'ifnotinit_file.exists():withopen(init_file'w')asf:f.write('')#Deleteallothermigrationfilesformigration_fileinmigration_dir.glob('*.py'):ifmigration_file.name!='__init__.py':self.stdout.write(self.style.SUCCESS(f"Deleting{migration_file.name}"))migration_file.unlink()#Delete__pycache__directoryifitexistspycache_dir=migration_dir/'__pycache__'ifpycache_dir.exists():shutil.rmtree(pycache_dir)def_delete_database(selfbase_dir):"""DeletetheSQLitedatabasefile."""db_path=base_dir/'db.sqlite3'ifdb_path.exists():self.stdout.write(self.style.SUCCESS(f"Deletingdatabaseat{db_path}"))db_path.unlink()returnTrueelse:self.stdout.write(self.style.WARNING("Databasefilenotfound."))returnFalsedef_create_initial_migrations(selfapp_names):"""Createinitialmigrationsforallapps."""self.stdout.write(self.style.SUCCESS("\nCreatinginitialmigrations..."))forapp_nameinapp_names:try:self.stdout.write(self.style.SUCCESS(f"Creatingmigrationsfor{app_name}..."))self.call_command('makemigrations'app_name)exceptExceptionase:self.stdout.write(self.style.ERROR(f"Errorcreatingmigrationsfor{app_name}:{e}"))def_apply_migrations(self):"""Applyallmigrations."""self.stdout.write(self.style.SUCCESS("\nApplyingmigrations..."))try:#Firstapplythebuilt-inmigrationsself.call_command('migrate''contenttypes')self.call_command('migrate''auth')self.call_command('migrate''admin')self.call_command('migrate''sessions')#Thenapplyallothermigrationsself.call_command('migrate')self.stdout.write(self.style.SUCCESS("Migrationsappliedsuccessfully!"))exceptExceptionase:self.stdout.write(self.style.ERROR(f"Errorapplyingmigrations:{e}"))def_fake_migrations(selfapp_names):"""Fakeallmigrationsforthegivenapps."""self.stdout.write(self.style.SUCCESS("\nFakingmigrations..."))#Firstapplythebuilt-inmigrationsself.call_command('migrate''contenttypes')self.call_command('migrate''auth')self.call_command('migrate''admin')self.call_command('migrate''sessions')#Thenfakeallothermigrationsforapp_nameinapp_names:try:self.stdout.write(self.style.SUCCESS(f"Fakingmigrationsfor{app_name}..."))self.call_command('migrate'app_name'--fake')exceptExceptionase:self.stdout.write(self.style.ERROR(f"Errorfakingmigrationsfor{app_name}:{e}"))defcall_command(self*args**kwargs):"""Callamanagementcommand."""fromdjango.core.managementimportcall_commandcall_command(*args**kwargs)
/**
 * ⏳ Waitlist Management Component
 * 
 * Features:
 * - Join/leave course waitlists
 * - Get waitlist entries and positions
 * - Process notifications and auto-enrollment (admin functions)
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  CircularProgress,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Queue,
  Add,
  Remove,
  Notifications,
  AutoMode,
  Refresh,
  Person,
  Schedule,
  Priority,
  CheckCircle,
  Cancel,
  AccessTime
} from '@mui/icons-material';
import { academicService, WaitlistEntry } from '../../services/academicService';

interface WaitlistManagerProps {
  studentId?: number;
  courseId?: number;
  isAdmin?: boolean;
}

const WaitlistManager: React.FC<WaitlistManagerProps> = ({ 
  studentId, 
  courseId,
  isAdmin = false 
}) => {
  const [waitlistEntries, setWaitlistEntries] = useState<WaitlistEntry[]>([]);
  const [courseWaitlist, setCourseWaitlist] = useState<WaitlistEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openJoinDialog, setOpenJoinDialog] = useState(false);
  const [openProcessDialog, setOpenProcessDialog] = useState(false);
  const [selectedCourseId, setSelectedCourseId] = useState<number>(courseId || 0);
  const [priority, setPriority] = useState<number>(5);
  const [processCount, setProcessCount] = useState<number>(1);

  useEffect(() => {
    loadData();
  }, [studentId, courseId]);

  const loadData = async () => {
    try {
      setLoading(true);
      const promises = [];
      
      // Load student's waitlist entries
      promises.push(academicService.getMyWaitlistEntries());
      
      // If admin and courseId provided, load course waitlist
      if (isAdmin && courseId) {
        promises.push(academicService.getCourseWaitlist(courseId));
      } else {
        promises.push(Promise.resolve([]));
      }
      
      const [studentEntries, courseEntries] = await Promise.all(promises);
      
      setWaitlistEntries(studentEntries);
      setCourseWaitlist(courseEntries);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to load waitlist data');
    } finally {
      setLoading(false);
    }
  };

  const handleJoinWaitlist = async () => {
    if (!selectedCourseId) {
      setError('Please select a course');
      return;
    }

    try {
      await academicService.joinWaitlist(selectedCourseId, priority);
      setOpenJoinDialog(false);
      loadData();
      setSelectedCourseId(0);
      setPriority(5);
    } catch (err: any) {
      setError(err.message || 'Failed to join waitlist');
    }
  };

  const handleLeaveWaitlist = async (courseId: number) => {
    try {
      await academicService.leaveWaitlist(courseId);
      loadData();
    } catch (err: any) {
      setError(err.message || 'Failed to leave waitlist');
    }
  };

  const handleProcessNotifications = async () => {
    try {
      await academicService.processWaitlistNotifications(courseId);
      loadData();
    } catch (err: any) {
      setError(err.message || 'Failed to process notifications');
    }
  };

  const handleAutoEnroll = async () => {
    if (!courseId) {
      setError('Course ID is required for auto-enrollment');
      return;
    }

    try {
      const enrolledCount = await academicService.autoEnrollFromWaitlist(courseId, processCount);
      setOpenProcessDialog(false);
      loadData();
      alert(`Successfully enrolled ${enrolledCount} students from waitlist`);
    } catch (err: any) {
      setError(err.message || 'Failed to auto-enroll students');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'WAITING':
        return 'info';
      case 'NOTIFIED':
        return 'warning';
      case 'ENROLLED':
        return 'success';
      case 'EXPIRED':
      case 'CANCELLED':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'WAITING':
        return <AccessTime />;
      case 'NOTIFIED':
        return <Notifications />;
      case 'ENROLLED':
        return <CheckCircle />;
      case 'EXPIRED':
      case 'CANCELLED':
        return <Cancel />;
      default:
        return <Queue />;
    }
  };

  const getPriorityColor = (priority: number) => {
    if (priority <= 2) return 'success';
    if (priority <= 4) return 'warning';
    return 'error';
  };

  const getPriorityLabel = (priority: number) => {
    switch (priority) {
      case 1:
        return 'High (Seniors)';
      case 2:
        return 'Medium-High (Juniors)';
      case 3:
        return 'Medium (Sophomores)';
      case 4:
        return 'Low (Freshmen)';
      case 5:
        return 'Lowest (Others)';
      default:
        return `Priority ${priority}`;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Queue color="primary" />
          Waitlist Management
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<Add />}
            onClick={() => setOpenJoinDialog(true)}
          >
            Join Waitlist
          </Button>
          {isAdmin && (
            <>
              <Button
                variant="outlined"
                startIcon={<Notifications />}
                onClick={handleProcessNotifications}
              >
                Process Notifications
              </Button>
              <Button
                variant="contained"
                startIcon={<AutoMode />}
                onClick={() => setOpenProcessDialog(true)}
              >
                Auto Enroll
              </Button>
            </>
          )}
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadData}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Student's Waitlist Entries */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Person />
            My Waitlist Entries
          </Typography>
          
          {waitlistEntries.length === 0 ? (
            <Alert severity="info">
              You are not currently on any waitlists.
            </Alert>
          ) : (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Course</TableCell>
                    <TableCell align="center">Position</TableCell>
                    <TableCell align="center">Priority</TableCell>
                    <TableCell align="center">Status</TableCell>
                    <TableCell>Added At</TableCell>
                    <TableCell>Notified At</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {waitlistEntries.map((entry) => (
                    <TableRow key={entry.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2">
                            {entry.course.course_code}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {entry.course.title}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={`#${entry.position}`}
                          color="primary"
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={getPriorityLabel(entry.priority)}
                          color={getPriorityColor(entry.priority)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          icon={getStatusIcon(entry.status)}
                          label={entry.status}
                          color={getStatusColor(entry.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {new Date(entry.added_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {entry.notified_at ? new Date(entry.notified_at).toLocaleDateString() : '-'}
                      </TableCell>
                      <TableCell>
                        {entry.status === 'WAITING' && (
                          <Button
                            size="small"
                            color="error"
                            startIcon={<Remove />}
                            onClick={() => handleLeaveWaitlist(entry.course.id)}
                          >
                            Leave
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Course Waitlist (Admin view) */}
      {isAdmin && courseWaitlist.length > 0 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Schedule />
              Course Waitlist (Course ID: {courseId})
            </Typography>
            
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Student</TableCell>
                    <TableCell align="center">Position</TableCell>
                    <TableCell align="center">Priority</TableCell>
                    <TableCell align="center">Status</TableCell>
                    <TableCell>Added At</TableCell>
                    <TableCell>Email</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {courseWaitlist.map((entry) => (
                    <TableRow key={entry.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2">
                            {entry.student.first_name} {entry.student.last_name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            ID: {entry.student.id}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={`#${entry.position}`}
                          color="primary"
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={getPriorityLabel(entry.priority)}
                          color={getPriorityColor(entry.priority)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          icon={getStatusIcon(entry.status)}
                          label={entry.status}
                          color={getStatusColor(entry.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {new Date(entry.added_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell>{entry.student.email}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {/* Join Waitlist Dialog */}
      <Dialog open={openJoinDialog} onClose={() => setOpenJoinDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Join Course Waitlist</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Course ID"
                type="number"
                value={selectedCourseId || ''}
                onChange={(e) => setSelectedCourseId(parseInt(e.target.value) || 0)}
                placeholder="Enter course ID"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={priority}
                  onChange={(e) => setPriority(e.target.value as number)}
                >
                  <MenuItem value={1}>High Priority (Seniors)</MenuItem>
                  <MenuItem value={2}>Medium-High Priority (Juniors)</MenuItem>
                  <MenuItem value={3}>Medium Priority (Sophomores)</MenuItem>
                  <MenuItem value={4}>Low Priority (Freshmen)</MenuItem>
                  <MenuItem value={5}>Lowest Priority (Others)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenJoinDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleJoinWaitlist}>
            Join Waitlist
          </Button>
        </DialogActions>
      </Dialog>

      {/* Auto Enroll Dialog */}
      <Dialog open={openProcessDialog} onClose={() => setOpenProcessDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Auto Enroll from Waitlist</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Number of Students to Enroll"
                type="number"
                value={processCount}
                onChange={(e) => setProcessCount(parseInt(e.target.value) || 1)}
                placeholder="Enter number of students"
                helperText="Number of students to automatically enroll from the waitlist"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenProcessDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleAutoEnroll}>
            Auto Enroll
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WaitlistManager;

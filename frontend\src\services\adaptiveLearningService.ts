import apiClient from './apiClient';
import { Assessment, LearningPath, StudentProgress, Course } from '../types';

// Types for adaptive learning
interface LearningStyle {
  visual: number;
  auditory: number;
  kinesthetic: number;
  readingWriting: number;
}

interface CognitiveLoad {
  intrinsic: number;
  extraneous: number;
  germane: number;
}

interface LearningState {
  knowledgeLevel: number;
  confidenceLevel: number;
  masteryLevel: number;
  engagementLevel: number;
  cognitiveLoad: CognitiveLoad;
  learningVelocity: number;
  retentionRate: number;
}

interface AdaptiveRecommendation {
  contentId: string;
  contentType: 'video' | 'text' | 'interactive' | 'assessment' | 'simulation';
  difficulty: number;
  estimatedTime: number;
  learningObjectives: string[];
  adaptationReason: string;
  confidence: number;
}

interface PersonalizedContent {
  id: string;
  title: string;
  description: string;
  contentType: string;
  difficulty: number;
  duration: number;
  adaptations: {
    visual: any[];
    auditory: any[];
    kinesthetic: any[];
    textual: any[];
  };
  prerequisites: string[];
  learningObjectives: string[];
}

export class AdaptiveLearningService {
  private static instance: AdaptiveLearningService;
  private learningStateCache = new Map<string, LearningState>();
  private adaptationHistory = new Map<string, AdaptiveRecommendation[]>();

  static getInstance(): AdaptiveLearningService {
    if (!AdaptiveLearningService.instance) {
      AdaptiveLearningService.instance = new AdaptiveLearningService();
    }
    return AdaptiveLearningService.instance;
  }

  // Analyze student's learning style using behavior patterns
  async analyzeLearningStyle(studentId: string): Promise<LearningStyle> {
    try {
      const response = await apiClient.post('/adaptive-learning/analyze-style', {
        studentId,
        includeHistory: true
      });
      
      return response.data.learningStyle;
    } catch (error) {
      console.error('Error analyzing learning style:', error);
      // Return default balanced style
      return {
        visual: 0.25,
        auditory: 0.25,
        kinesthetic: 0.25,
        readingWriting: 0.25
      };
    }
  }

  // Get current learning state using advanced analytics
  async getLearningState(studentId: string, courseId: string): Promise<LearningState> {
    const cacheKey = `${studentId}-${courseId}`;
    
    if (this.learningStateCache.has(cacheKey)) {
      return this.learningStateCache.get(cacheKey)!;
    }

    try {
      const response = await apiClient.get(`/adaptive-learning/state/${studentId}/${courseId}`);
      const state = response.data;
      
      this.learningStateCache.set(cacheKey, state);
      return state;
    } catch (error) {
      console.error('Error getting learning state:', error);
      throw error;
    }
  }

  // Generate adaptive content recommendations
  async generateAdaptiveRecommendations(
    studentId: string,
    courseId: string,
    currentTopic: string
  ): Promise<AdaptiveRecommendation[]> {
    try {
      const [learningStyle, learningState] = await Promise.all([
        this.analyzeLearningStyle(studentId),
        this.getLearningState(studentId, courseId)
      ]);

      const response = await apiClient.post('/adaptive-learning/recommendations', {
        studentId,
        courseId,
        currentTopic,
        learningStyle,
        learningState,
        contextualFactors: {
          timeOfDay: new Date().getHours(),
          sessionLength: await this.getAverageSessionLength(studentId),
          recentPerformance: await this.getRecentPerformanceMetrics(studentId)
        }
      });

      const recommendations = response.data.recommendations;
      this.adaptationHistory.set(studentId, recommendations);
      
      return recommendations;
    } catch (error) {
      console.error('Error generating adaptive recommendations:', error);
      throw error;
    }
  }

  // Create personalized learning path
  async createPersonalizedLearningPath(
    studentId: string,
    courseId: string,
    goals: string[]
  ): Promise<LearningPath> {
    try {
      const response = await apiClient.post('/adaptive-learning/personalized-path', {
        studentId,
        courseId,
        goals,
        adaptiveFactors: {
          learningStyle: await this.analyzeLearningStyle(studentId),
          currentState: await this.getLearningState(studentId, courseId),
          timeConstraints: await this.getTimeConstraints(studentId),
          preferredDifficulty: await this.getPreferredDifficulty(studentId)
        }
      });

      return response.data.learningPath;
    } catch (error) {
      console.error('Error creating personalized learning path:', error);
      throw error;
    }
  }

  // Adaptive assessment with dynamic difficulty adjustment
  async generateAdaptiveAssessment(
    studentId: string,
    courseId: string,
    topic: string,
    targetDifficulty?: number
  ): Promise<Assessment> {
    try {
      const learningState = await this.getLearningState(studentId, courseId);
      
      const response = await apiClient.post('/adaptive-learning/adaptive-assessment', {
        studentId,
        courseId,
        topic,
        targetDifficulty: targetDifficulty || learningState.knowledgeLevel,
        adaptiveParameters: {
          maxQuestions: 20,
          minQuestions: 5,
          confidenceThreshold: 0.8,
          difficultyAdjustmentRate: 0.1
        }
      });

      return response.data.assessment;
    } catch (error) {
      console.error('Error generating adaptive assessment:', error);
      throw error;
    }
  }

  // Real-time content adaptation
  async adaptContentInRealTime(
    studentId: string,
    contentId: string,
    interactionData: any
  ): Promise<PersonalizedContent> {
    try {
      const response = await apiClient.post('/adaptive-learning/real-time-adaptation', {
        studentId,
        contentId,
        interactionData,
        adaptationStrategy: 'immediate'
      });

      return response.data.adaptedContent;
    } catch (error) {
      console.error('Error adapting content in real-time:', error);
      throw error;
    }
  }

  // Predictive learning analytics
  async predictLearningOutcomes(
    studentId: string,
    courseId: string,
    timeframe: number
  ): Promise<{
    expectedProgress: number;
    riskFactors: string[];
    recommendations: string[];
    confidenceInterval: [number, number];
  }> {
    try {
      const response = await apiClient.post('/adaptive-learning/predict-outcomes', {
        studentId,
        courseId,
        timeframe,
        modelVersion: 'v2.1'
      });

      return response.data.predictions;
    } catch (error) {
      console.error('Error predicting learning outcomes:', error);
      throw error;
    }
  }

  // Cognitive load optimization
  async optimizeCognitiveLoad(
    studentId: string,
    contentSequence: string[]
  ): Promise<{
    optimizedSequence: string[];
    cognitiveLoadScore: number;
    recommendations: string[];
  }> {
    try {
      const response = await apiClient.post('/adaptive-learning/optimize-cognitive-load', {
        studentId,
        contentSequence,
        optimizationStrategy: 'balanced'
      });

      return response.data.optimization;
    } catch (error) {
      console.error('Error optimizing cognitive load:', error);
      throw error;
    }
  }

  // Learning transfer optimization
  async optimizeLearningTransfer(
    studentId: string,
    sourceCourse: string,
    targetCourse: string
  ): Promise<{
    transferableSkills: string[];
    bridgingActivities: any[];
    transferProbability: number;
  }> {
    try {
      const response = await apiClient.post('/adaptive-learning/optimize-transfer', {
        studentId,
        sourceCourse,
        targetCourse
      });

      return response.data.transferOptimization;
    } catch (error) {
      console.error('Error optimizing learning transfer:', error);
      throw error;
    }
  }

  // Private helper methods
  private async getAverageSessionLength(studentId: string): Promise<number> {
    try {
      const response = await apiClient.get(`/analytics/session-length/${studentId}`);
      return response.data.averageMinutes;
    } catch (error) {
      return 45; // Default session length
    }
  }

  private async getRecentPerformanceMetrics(studentId: string): Promise<any> {
    try {
      const response = await apiClient.get(`/analytics/recent-performance/${studentId}`);
      return response.data;
    } catch (error) {
      return {};
    }
  }

  private async getTimeConstraints(studentId: string): Promise<any> {
    try {
      const response = await apiClient.get(`/students/${studentId}/time-constraints`);
      return response.data;
    } catch (error) {
      return { availableHoursPerWeek: 10 };
    }
  }

  private async getPreferredDifficulty(studentId: string): Promise<number> {
    try {
      const response = await apiClient.get(`/students/${studentId}/preferences`);
      return response.data.preferredDifficulty || 0.7;
    } catch (error) {
      return 0.7;
    }
  }

  // Voice/Speech recognition for accessibility
  async processVoiceInteraction(
    studentId: string,
    audioData: Blob,
    context: string
  ): Promise<{
    transcription: string;
    intent: string;
    response: string;
    adaptiveAction?: string;
  }> {
    try {
      const formData = new FormData();
      formData.append('audio', audioData);
      formData.append('studentId', studentId);
      formData.append('context', context);

      const response = await apiClient.post('/adaptive-learning/voice-interaction', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error processing voice interaction:', error);
      throw error;
    }
  }

  clearCache(): void {
    this.learningStateCache.clear();
    this.adaptationHistory.clear();
  }
}

export const adaptiveLearningService = AdaptiveLearningService.getInstance();

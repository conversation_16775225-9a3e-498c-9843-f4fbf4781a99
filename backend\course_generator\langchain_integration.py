"""IntegrationofLangChainwiththecoursegenerator.ThismoduleprovidesfunctionstouseLangChainforcoursecontentgeneration."""import json
import loggingfromtypingimportAnyDictListOptionalfrom django.confimportsettingsfrom utils.consolidated_ai_serviceimport(ConsolidatedAIErrorContentGenerationErrorget_ai_service)from utils.langchain_serviceimportModelInitializationErrorget_langchain_service#GetAIserviceinstanceai_service=get_ai_service()from utils.ai_config_managerimportget_fallback_model_nameget_model_namefrom utils.api_key_serviceimportApiKeyServicefrom.domain_promptsimportdomain_prompt_generator#ImportCoursemodeldirectlytry:from courses.modelsimportCourseexceptImportError:#Fallbacktoimportingfrommodelsmodulefrom django.appsimportappstry:Course=apps.get_model("courses""Course")exceptLookupError:#DefineaplaceholderCoursemodelfordevelopmentfrom django.dbimportmodelsclassCourse(models.Model):title=models.CharField(max_length=200)course_code=models.Char<PERSON>ield(max_length=10)logger=logging.getLogger(__name__)#InitializeLangChainservicewithcentralizedconfigurationtry:#GetAPIkeyandmodelnamefromcentralizedconfigurationapi_key=ApiKeyService.get_api_key()model_name=get_model_name()fallback_model_name=get_fallback_model_name()#InitializeLangChainservicefrom utils.langchain_serviceimportLangChainServicelangchain_service=LangChainService(model_name)logger.info(f"LangChainserviceinitializedwithmodel:{model_name}")exceptExceptionase:logger.error(f"FailedtoinitializeLangChainservice:{str(e)}")#Createaminimalservicethatwillusefallbackmethodslangchain_service=Nonedefgenerate_content_with_langchain(course:Coursecontent_type:str)->Dict[strAny]:"""GeneratecoursecontentusingLangChain.Args:course:Thecourseobjectcontent_type:Typeofcontenttogenerate(weekly_schedulelesson_plansetc.)Returns:Dictionarycontainingthegeneratedcontent"""try:#Preparecourseinfocourse_info={"title":course.title"course_code":course.course_code"description":course.description"level":course.required_level"department":course.department.nameifcourse.departmentelse"""credits":course.credits}#Checkiflangchain_serviceisavailableiflangchain_serviceisNone:logger.warning("LangChainservicenotavailableusingfallbackAIservice")try:#UsetheunifiedAIserviceasfallbackprompt=f"""Youareanexpertcurriculumdesigner.Ineedyourhelpcreatingcontentforacourse.Course:{course_info['title']}({course_info['course_code']})Level:{course_info['level']}(1=Beginner2=Intermediate3=Advanced4=Expert)Description:{course_info['description']}Pleasegeneratecontentfor{content_type.replace('_''')}appropriateforthiscourse.FormatyourresponseasaJSONobjectwithappropriatestructurefor{content_type}."""response=get_ai_service().generate_text(prompt)#TrytoparsetheresponseasJSONtry:result=json.loads(response)returnresultexceptjson.JSONDecodeError:return{"raw_content":response}exceptExceptionasai_error:logger.error(f"ErrorusingfallbackAIservice:{str(ai_error)}")return{"error":"AIserviceunavailable""message":"BothLangChainandfallbackAIservicesfailed.Pleasetryagainlater.""raw_content":f"Error:{str(ai_error)}"}else:try:#GeneratecontentusingLangChainresult=langchain_service.generate_course_content(course_infocontent_type)returnresultexceptExceptionaslc_error:logger.error(f"ErrorusingLangChainservice:{str(lc_error)}")#TryfallbackAIservicetry:logger.info("TryingfallbackAIservice")prompt=f"""Youareanexpertcurriculumdesigner.Ineedyourhelpcreatingcontentforacourse.Course:{course_info['title']}({course_info['course_code']})Level:{course_info['level']}(1=Beginner2=Intermediate3=Advanced4=Expert)Description:{course_info['description']}Pleasegeneratecontentfor{content_type.replace('_''')}appropriateforthiscourse.FormatyourresponseasaJSONobjectwithappropriatestructurefor{content_type}."""response=get_ai_service().generate_text(prompt)#TrytoparsetheresponseasJSONtry:result=json.loads(response)returnresultexceptjson.JSONDecodeError:return{"raw_content":response}exceptExceptionasai_error:logger.error(f"ErrorusingfallbackAIservice:{str(ai_error)}")return{"error":"AIserviceunavailable""message":"BothLangChainandfallbackAIservicesfailed.Pleasetryagainlater.""raw_content":f"Error:{str(lc_error)}Fallbackerror:{str(ai_error)}"}exceptExceptionase:logger.error(f"ErrorgeneratingcontentwithLangChain:{str(e)}")raiseConsolidatedAIError(f"ErrorgeneratingcontentwithLangChain:{str(e)}")defgenerate_content_suggestions(course:Coursecontent_type:strprompt:str)->List[Any]:"""GeneratecontentsuggestionsusingLangChain.Args:course:Thecourseobjectcontent_type:Typeofcontenttogeneratesuggestionsforprompt:UserpromptforthesuggestionsReturns:Listofcontentsuggestions"""try:#Preparecourseinfocourse_info={"title":course.title"course_code":course.course_code"description":course.description"level":course.required_level"department":course.department.nameifcourse.departmentelse"""credits":course.credits}#Trytousedomain-specificpromptstry:#Getdomain-specificpromptdomain_prompt=domain_prompt_generator.get_domain_prompt(content_type=content_typecourse_title=course_info["title"]course_description=course_info["description"]department=course_info["department"]skill_level=course_info["level"])#Adduserprompttothedomain-specificpromptcustom_prompt=f"""{domain_prompt}Theuserhasspecificallyrequested:"{prompt}"Pleasegenerate3differentsuggestionsthataddresstheuser'srequest.Makeeachsuggestiondetailedandspecifictothecoursecontentandlevel.FormatyourresponseasaJSONarraywhereeachitemhastheappropriatestructurefor{content_type}."""exceptExceptionase:logger.warning(f"Errorgeneratingdomain-specificprompt:{str(e)}.Usingfallbackprompt.")#Fallbacktogenericpromptifdomain-specificpromptsfailcustom_prompt=f"""Youareanexpertcurriculumdesignerwithexperienceincreatinguniversitycourses.Course:{course_info['title']}({course_info['course_code']})Level:{course_info['level']}(1=Beginner2=Intermediate3=Advanced4=Expert)Description:{course_info['description']}Ineedsuggestionsfor{content_type.replace('_''')}.Theuserhasrequested:"{prompt}"Pleasegenerate3differentsuggestionsthatwouldbeappropriateforthiscourse.Makeeachsuggestiondetailedandspecifictothecoursecontentandlevel.FormatyourresponseasaJSONarraywhereeachitemhastheappropriatestructurefor{content_type}."""#Checkiflangchain_serviceisavailableiflangchain_serviceisNone:logger.warning("LangChainservicenotavailableusingfallbackAIservice")try:#UsetheunifiedAIserviceasfallbackresponse=get_ai_service().generate_text(custom_prompt)exceptExceptionasai_error:logger.error(f"ErrorusingfallbackAIservice:{str(ai_error)}")return[{"error":"AIserviceunavailable""message":"BothLangChainandfallbackAIservicesfailed.Pleasetryagainlater.""content":f"Error:{str(ai_error)}"}]else:try:#GeneratesuggestionsusingLangChainresponse=langchain_service.generate_text(custom_prompt)exceptExceptionaslc_error:logger.error(f"ErrorusingLangChainservice:{str(lc_error)}")#TryfallbackAIservicetry:logger.info("TryingfallbackAIservice")response=get_ai_service().generate_text(custom_prompt)exceptExceptionasai_error:logger.error(f"ErrorusingfallbackAIservice:{str(ai_error)}")return[{"error":"AIserviceunavailable""message":"BothLangChainandfallbackAIservicesfailed.Pleasetryagainlater.""content":f"Error:{str(lc_error)}Fallbackerror:{str(ai_error)}"}]#TrytoparsetheresponseasJSONtry:suggestions=json.loads(response)ifnotisinstance(suggestionslist):suggestions=[suggestions]returnsuggestionsexceptjson.JSONDecodeError:#IfnotvalidJSONextractstructureddataimport re#LookforJSONarraypatternmatch=re.search(r"\[.*\]"responsere.DOTALL)ifmatch:try:suggestions=json.loads(match.group(0))returnsuggestionsexceptjson.JSONDecodeError:pass#IfstillnotvalidJSONcreateasimplelistofsuggestionssuggestions=[]sections=response.split("\n\n")forsectioninsections:ifsection.strip():suggestions.append(section.strip())returnsuggestions[:3]#Returnatmost3suggestionsexceptExceptionase:logger.error(f"ErrorgeneratingcontentsuggestionswithLangChain:{str(e)}")raiseConsolidatedAIError(f"ErrorgeneratingcontentsuggestionswithLangChain:{str(e)}")defgenerate_with_pedagogical_approach(course:Coursecontent_type:strapproach:str)->Dict[strAny]:"""Generatecontentusingaspecificpedagogicalapproach.Args:course:Thecourseobjectcontent_type:Typeofcontenttogenerateapproach:Pedagogicalapproachtouse(e.g."problem_based_learning""flipped_classroom")Returns:Dictionarycontainingthegeneratedcontent"""try:#Preparecourseinfocourse_info={"title":course.title"course_code":course.course_code"description":course.description"level":course.required_level"department":course.department.nameifcourse.departmentelse"""credits":course.credits}#Getthepedagogicalapproachdetailsapproach_details=domain_prompt_generator.get_pedagogical_approach(approach)#Createapromptthatincorporatesthepedagogicalapproachprompt=f"""Youareanexpertcurriculumdesignerspecializingin{approach.replace('_''')}pedagogy.Course:{course_info['title']}({course_info['course_code']})Level:{course_info['level']}(1=Beginner2=Intermediate3=Advanced4=Expert)Description:{course_info['description']}Ineedtodesign{content_type.replace('_''')}usingthe{approach.replace('_''')}approach.Thisapproachischaracterizedby:-{approach_details['description']}-Keyactivities:{''.join(approach_details['activities'])}-Assessmentfocuseson:{''.join(approach_details['assessment'])}Pleasegeneratecontentfor{content_type.replace('_''')}thatfullyincorporatesthispedagogicalapproach.Makesurethecontentisappropriateforthecourselevelandsubjectmatter.FormatyourresponseasaJSONobjectwithappropriatestructurefor{content_type}."""#Checkiflangchain_serviceisavailableiflangchain_serviceisNone:logger.warning("LangChainservicenotavailableusingfallbackAIservice")try:#UsetheunifiedAIserviceasfallbackresponse=get_ai_service().generate_text(prompt)exceptExceptionasai_error:logger.error(f"ErrorusingfallbackAIservice:{str(ai_error)}")return{"error":"AIserviceunavailable""message":"BothLangChainandfallbackAIservicesfailed.Pleasetryagainlater.""raw_content":f"Error:{str(ai_error)}"}else:try:#GeneratecontentusingLangChainresponse=langchain_service.generate_text(prompt)exceptExceptionaslc_error:logger.error(f"ErrorusingLangChainservice:{str(lc_error)}")#TryfallbackAIservicetry:logger.info("TryingfallbackAIservice")response=get_ai_service().generate_text(prompt)exceptExceptionasai_error:logger.error(f"ErrorusingfallbackAIservice:{str(ai_error)}")return{"error":"AIserviceunavailable""message":"BothLangChainandfallbackAIservicesfailed.Pleasetryagainlater.""raw_content":f"Error:{str(lc_error)}Fallbackerror:{str(ai_error)}"}#TrytoparsetheresponseasJSONtry:#Firstcheckifresponseisalreadyadictionary(alreadyparsedJSON)ifisinstance(responsedict):returnresponse#Ifit'sastringtrytoparseitcontent=json.loads(response)returncontentexceptjson.JSONDecodeError:#IfnotvalidJSONextractstructureddataimport re#LookforJSONobjectpatternmatch=re.search(r"\{.*\}"responsere.DOTALL)ifmatch:try:content=json.loads(match.group(0))returncontentexceptjson.JSONDecodeError:pass#IfstillnotvalidJSONreturntherawtextreturn{"raw_content":response}exceptExceptionase:logger.error(f"Errorgeneratingcontentwithpedagogicalapproach:{str(e)}")raiseConsolidatedAIError(f"Errorgeneratingcontentwithpedagogicalapproach:{str(e)}")defbulk_generate_content(course:Coursecontent_type:strcount:int)->List[Any]:"""BulkgeneratecontentitemsusingLangChain.Args:course:Thecourseobjectcontent_type:Typeofcontenttogeneratecount:NumberofitemstogenerateReturns:Listofgeneratedcontentitems"""try:#Preparecourseinfocourse_info={"title":course.title"course_code":course.course_code"description":course.description"level":course.required_level"department":course.department.nameifcourse.departmentelse"""credits":course.credits}#Usedomain-specificpromptstry:#Getdomain-specificpromptdomain_prompt=domain_prompt_generator.get_domain_prompt(content_type=content_typecourse_title=course_info["title"]course_description=course_info["description"]department=course_info["department"]skill_level=course_info["level"])#Addcountinformationtothepromptdomain_prompt+=f"\n\nPleasegenerateexactly{count}items."#Usethedomain-specificpromptprompt=domain_promptexceptExceptionase:logger.warning(f"Errorgeneratingdomain-specificprompt:{str(e)}.Usingfallbackprompt.")#Fallbacktogenericpromptsifdomain-specificpromptsfailifcontent_type=="weekly_schedule":prompt=f"""Youareanexpertcurriculumdesigner.Ineedyourhelpcreatingaweeklyscheduleforacourse.Course:{course_info['title']}({course_info['course_code']})Level:{course_info['level']}(1=Beginner2=Intermediate3=Advanced4=Expert)Description:{course_info['description']}Pleasegenerate{count}weeksofschedule.Eachweekshouldincludeatopicsubtopicsandactivitiesappropriateforthiscourse.FormatyourresponseasaJSONarraywhereeachitemhasthestructure:{{"week":number"topic":"string""subtopics":["string""string"...]"activities":["string""string"...]}}"""elifcontent_type=="lesson_plans":prompt=f"""Youareanexpertcurriculumdesigner.Ineedyourhelpcreatinglessonplansforacourse.Course:{course_info['title']}({course_info['course_code']})Level:{course_info['level']}(1=Beginner2=Intermediate3=Advanced4=Expert)Description:{course_info['description']}Pleasegenerate{count}detailedlessonplansappropriateforthiscourse.Eachlessonplanshouldincludeatitlelearningobjectivesactivitiesanddiscussionquestions.FormatyourresponseasaJSONarraywhereeachitemhasthestructure:{{"title":"string""objectives":["string""string"...]"activities":["string""string"...]"discussion_questions":["string""string"...]}}"""elifcontent_type=="sample_quizzes":prompt=f"""Youareanexpertcurriculumdesigner.Ineedyourhelpcreatingquizzesforacourse.Course:{course_info['title']}({course_info['course_code']})Level:{course_info['level']}(1=Beginner2=Intermediate3=Advanced4=Expert)Description:{course_info['description']}Pleasegenerate{count}quizzesappropriateforthiscourse.Eachquizshouldhaveatitleandatleast5multiple-choicequestionswithanswers.FormatyourresponseasaJSONarraywhereeachitemhasthestructure:{{"title":"string""questions":[{{"question":"string""options":["string""string""string""string"]"answer":"string"}}...]}}"""else:prompt=f"""Youareanexpertcurriculumdesigner.Ineedyourhelpcreatingcontentforacourse.Course:{course_info['title']}({course_info['course_code']})Level:{course_info['level']}(1=Beginner2=Intermediate3=Advanced4=Expert)Description:{course_info['description']}Pleasegenerate{count}itemsfor{content_type.replace('_''')}appropriateforthiscourse.FormatyourresponseasaJSONarray."""#Checkiflangchain_serviceisavailableiflangchain_serviceisNone:logger.warning("LangChainservicenotavailableusingfallbackAIservice")try:#UsetheunifiedAIserviceasfallbackresponse=get_ai_service().generate_text(prompt)exceptExceptionasai_error:logger.error(f"ErrorusingfallbackAIservice:{str(ai_error)}")return[{"error":"AIserviceunavailable""message":"BothLangChainandfallbackAIservicesfailed.Pleasetryagainlater.""content":f"Error:{str(ai_error)}"}]else:try:#GeneratecontentusingLangChainresponse=langchain_service.generate_text(prompt)exceptExceptionaslc_error:logger.error(f"ErrorusingLangChainservice:{str(lc_error)}")#TryfallbackAIservicetry:logger.info("TryingfallbackAIservice")response=get_ai_service().generate_text(prompt)exceptExceptionasai_error:logger.error(f"ErrorusingfallbackAIservice:{str(ai_error)}")return[{"error":"AIserviceunavailable""message":"BothLangChainandfallbackAIservicesfailed.Pleasetryagainlater.""content":f"Error:{str(lc_error)}Fallbackerror:{str(ai_error)}"}]#TrytoparsetheresponseasJSONtry:items=json.loads(response)ifnotisinstance(itemslist):items=[items]returnitemsexceptjson.JSONDecodeError:#IfnotvalidJSONextractstructureddataimport re#LookforJSONarraypatternmatch=re.search(r"\[.*\]"responsere.DOTALL)ifmatch:try:items=json.loads(match.group(0))returnitemsexceptjson.JSONDecodeError:pass#IfstillnotvalidJSONreturnemptylistreturn[]exceptExceptionase:logger.error(f"ErrorbulkgeneratingcontentwithLangChain:{str(e)}")raiseConsolidatedAIError(f"ErrorbulkgeneratingcontentwithLangChain:{str(e)}")
"""ImprovedAIServiceThismoduleprovidesanenhancedAIservicethatintegratesalltheimprovements:-Simplifiedconfigurationmanagement-Bettererrorhandlingandresilience-Monitoringandanalytics-Asyncsupportwithqueuing"""import loggingimport timefromtypingimportDictAnyOptionalUnionfrom django.core.cacheimportcachefrom.ai_config_simplifiedimportconfig_managerAIConfigfrom.ai_resilienceimportwith_retryRetryConfigAIServiceExceptionfallback_managercircuit_breakerfrom.ai_monitoringimportai_monitorfrom.ai_async_serviceimportasync_ai_serviceRequestPrioritylogger=logging.getLogger(__name__)classImprovedAIService:"""EnhancedAIservicewithallimprovementsintegrated"""def__init__(self):self.config_manager=config_managerself.fallback_manager=fallback_managerself.monitor=ai_monitorself._gemini_model=Noneself._initialize_service()def_initialize_service(self):"""InitializetheAIservicewithcurrentconfiguration"""try:config=self.config_manager.get_config()api_key=self.config_manager.get_api_key()ifapi_key:importgoogle.generativeaiasgenaigenai.configure(api_key=api_key)self._gemini_model=genai.GenerativeModel(config.default_model)logger.info(f"AIserviceinitializedwithmodel:{config.default_model}")else:logger.warning("NoAPIkeyavailable-AIservicewillusefallbackresponses")self._gemini_model=NoneexceptExceptionase:logger.error(f"FailedtoinitializeAIservice:{e}")self._gemini_model=Nonedefrefresh_configuration(self):"""Refreshserviceconfiguration"""logger.info("RefreshingAIserviceconfiguration")self._initialize_service()@with_retry(RetryConfig(max_retries=3base_delay=1.0))defgenerate_content(selfprompt:strservice_type:str='general'user_id:Optional[str]=None**kwargs)->str:"""GeneratecontentusingAIwithimprovederrorhandlingandmonitoringArgs:prompt:Theinputpromptservice_type:Typeofservice(formonitoring)user_id:UserID(formonitoring)**kwargs:AdditionalparametersReturns:Generatedcontentasstring"""withself.monitor.track_request(service_type'generate_content')astracker:try:#Settrackingmetadataifuser_id:tracker.set_metadata(user_id=user_id)#Usecircuitbreakerforresiliencereturncircuit_breaker.call(self._generate_content_internalprompt**kwargs)exceptAIServiceException:#Re-raiseAIserviceexceptionsas-israiseexceptExceptionase:#ConvertotherexceptionstoAIserviceexceptionslogger.error(f"UnexpectederrorinAIcontentgeneration:{e}")raiseAIServiceException(f"AIserviceerror:{str(e)}"original_error=e)def_generate_content_internal(selfprompt:str**kwargs)->str:"""Internalcontentgenerationmethod"""ifnotself._gemini_model:#Usefallbackifnomodelavailablecontext=kwargs.get('context'{})returnself.fallback_manager.get_fallback_response(context)config=self.config_manager.get_config()#Getgenerationparameterstemperature=kwargs.get('temperature'config.temperature)max_tokens=kwargs.get('max_tokens'config.max_tokens)try:response=self._gemini_model.generate_content(promptgeneration_config={'temperature':temperature'max_output_tokens':max_tokens})returnresponse.textexceptExceptionase:logger.error(f"GeminiAPIerror:{e}")#UsefallbackforAPIerrorscontext=kwargs.get('context'{})returnself.fallback_manager.get_fallback_response(context)asyncdefgenerate_content_async(selfprompt:strservice_type:str='general'priority:RequestPriority=RequestPriority.NORMALuser_id:Optional[str]=Nonecontext:Optional[Dict[strAny]]=None)->str:"""GeneratecontentasynchronouslyusingtherequestqueueArgs:prompt:Theinputpromptservice_type:Typeofservicepriority:Requestpriorityuser_id:UserIDcontext:AdditionalcontextReturns:RequestIDfortrackingtheasyncrequest"""#Startasyncserviceifnotrunningifnotasync_ai_service.is_running:awaitasync_ai_service.start()#Submitrequesttoqueuerequest_id=awaitasync_ai_service.submit_request(prompt=promptservice_type=service_typepriority=priorityuser_id=user_idcontext=context)returnrequest_iddefget_async_result(selfrequest_id:str)->Optional[Dict[strAny]]:"""Getresultofanasyncrequest"""importasyncioreturnasyncio.run(async_ai_service.get_request_result(request_id))defget_async_status(selfrequest_id:str)->Optional[Dict[strAny]]:"""Getstatusofanasyncrequest"""importasyncioreturnasyncio.run(async_ai_service.get_request_status(request_id))defanalyze_answer(selfstudent_answer:strquestion:Anyuser_id:Optional[str]=None)->Dict[strAny]:"""Analyzestudentanswerwithimprovederrorhandling"""withself.monitor.track_request('assessment''analyze_answer')astracker:ifuser_id:tracker.set_metadata(user_id=user_id)try:prompt=f"""Analyzethisstudentanswerforthegivenquestion:Question:{getattr(question'text'str(question))}StudentAnswer:{student_answer}ProvideanalysisinJSONformatwith:-score(0-100)-feedback-suggestionsforimprovement-keyconceptsidentified"""response=self.generate_content(promptservice_type='assessment'user_id=user_id)#TrytoparseasJSONfallbacktostructuredresponsetry:import jsonreturnjson.loads(response)exceptjson.JSONDecodeError:return{'score':75#Defaultscore'feedback':response'suggestions':['Reviewthematerialandtryagain']'key_concepts':[]}exceptExceptionase:logger.error(f"Erroranalyzinganswer:{e}")return{'score':0'feedback':'Unabletoanalyzeansweratthistime''suggestions':['Pleasetryagainlater']'key_concepts':[]'error':str(e)}defget_health_status(self)->Dict[strAny]:"""GetcurrenthealthstatusoftheAIservice"""health=self.monitor.get_health_status()#Addconfigurationstatusconfig=self.config_manager.get_config()api_key=self.config_manager.get_api_key()health['configuration']={'api_key_configured':bool(api_key)'model':config.default_model'fallback_enabled':config.enable_fallback'service_initialized':bool(self._gemini_model)}returnhealthdefget_usage_analytics(selfdays:int=7)->Dict[strAny]:"""Getusageanalytics"""returnself.monitor.get_usage_analytics(days)defupdate_configuration(self**kwargs)->bool:"""UpdateAIserviceconfiguration"""try:success=self.config_manager.update_config(**kwargs)ifsuccess:self.refresh_configuration()returnsuccessexceptExceptionase:logger.error(f"Failedtoupdateconfiguration:{e}")returnFalsedefupdate_api_key(selfapi_key:str)->bool:"""UpdateAPIkey"""try:success=self.config_manager.update_api_key(api_key)ifsuccess:self.refresh_configuration()returnsuccessexceptExceptionase:logger.error(f"FailedtoupdateAPIkey:{e}")returnFalse#GlobalimprovedAIserviceinstanceimproved_ai_service=ImprovedAIService()#Backwardcompatibilityfunctionsdefget_improved_ai_service()->ImprovedAIService:"""GettheimprovedAIserviceinstance"""returnimproved_ai_servicedefgenerate_content(prompt:str**kwargs)->str:"""GeneratecontentusingtheimprovedAIservice"""returnimproved_ai_service.generate_content(prompt**kwargs)defanalyze_answer(student_answer:strquestion:Any**kwargs)->Dict[strAny]:"""AnalyzestudentanswerusingtheimprovedAIservice"""returnimproved_ai_service.analyze_answer(student_answerquestion**kwargs)
// Multi-Agent Conversation Service
// Handles conversation history for multi-agent chat
// Uses localStorage for now, can be extended to use API endpoints later

export interface MultiAgentMessage {
  id: string;
  content: string;
  isUser: boolean;
  agentUsed?: string;
  timestamp: Date;
  metadata?: any;
}

export interface MultiAgentConversation {
  id: string;
  title: string;
  messages: MultiAgentMessage[];
  createdAt: Date;
  updatedAt: Date;
  userRole: string;
  isArchived?: boolean;
}

export interface CreateConversationRequest {
  title: string;
  userRole: string;
}

const multiAgentConversationService = {
  /**
   * Get all multi-agent conversations for the current user
   */
  getConversations: async (): Promise<MultiAgentConversation[]> => {
    try {
      // For now, return mock data since backend might not have this endpoint yet
      // In a real implementation, this would call an API endpoint
      const savedConversations = localStorage.getItem('multiAgentConversations');
      if (savedConversations) {
        const parsed = JSON.parse(savedConversations);
        return parsed.map((conv: any) => ({
          ...conv,
          createdAt: new Date(conv.createdAt),
          updatedAt: new Date(conv.updatedAt),
          messages: conv.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })),
        }));
      }
      return [];
    } catch (error) {
      console.error('Error fetching multi-agent conversations:', error);
      return [];
    }
  },

  /**
   * Get a specific multi-agent conversation by ID
   */
  getConversation: async (conversationId: string): Promise<MultiAgentConversation | null> => {
    try {
      const conversations = await multiAgentConversationService.getConversations();
      return conversations.find(conv => conv.id === conversationId) || null;
    } catch (error) {
      console.error('Error fetching multi-agent conversation:', error);
      return null;
    }
  },

  /**
   * Create a new multi-agent conversation
   */
  createConversation: async (request: CreateConversationRequest): Promise<MultiAgentConversation> => {
    try {
      const newConversation: MultiAgentConversation = {
        id: Date.now().toString(),
        title: request.title,
        messages: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        userRole: request.userRole,
        isArchived: false,
      };

      const conversations = await multiAgentConversationService.getConversations();
      conversations.unshift(newConversation);
      localStorage.setItem('multiAgentConversations', JSON.stringify(conversations));

      return newConversation;
    } catch (error) {
      console.error('Error creating multi-agent conversation:', error);
      throw error;
    }
  },

  /**
   * Update conversation messages
   */
  updateConversation: async (conversationId: string, messages: MultiAgentMessage[]): Promise<void> => {
    try {
      const conversations = await multiAgentConversationService.getConversations();
      const conversationIndex = conversations.findIndex(conv => conv.id === conversationId);
      
      if (conversationIndex !== -1) {
        conversations[conversationIndex].messages = messages;
        conversations[conversationIndex].updatedAt = new Date();
        
        // Auto-generate title from first user message if title is default
        if (conversations[conversationIndex].title === 'New Multi-Agent Chat' && messages.length > 0) {
          const firstUserMessage = messages.find(msg => msg.isUser);
          if (firstUserMessage) {
            conversations[conversationIndex].title = firstUserMessage.content.length > 50 
              ? firstUserMessage.content.substring(0, 50) + '...'
              : firstUserMessage.content;
          }
        }
        
        localStorage.setItem('multiAgentConversations', JSON.stringify(conversations));
      }
    } catch (error) {
      console.error('Error updating multi-agent conversation:', error);
      throw error;
    }
  },

  /**
   * Delete a multi-agent conversation
   */
  deleteConversation: async (conversationId: string): Promise<void> => {
    try {
      const conversations = await multiAgentConversationService.getConversations();
      const filteredConversations = conversations.filter(conv => conv.id !== conversationId);
      localStorage.setItem('multiAgentConversations', JSON.stringify(filteredConversations));
    } catch (error) {
      console.error('Error deleting multi-agent conversation:', error);
      throw error;
    }
  },

  /**
   * Update conversation title
   */
  updateConversationTitle: async (conversationId: string, title: string): Promise<void> => {
    try {
      const conversations = await multiAgentConversationService.getConversations();
      const conversationIndex = conversations.findIndex(conv => conv.id === conversationId);
      
      if (conversationIndex !== -1) {
        conversations[conversationIndex].title = title;
        conversations[conversationIndex].updatedAt = new Date();
        localStorage.setItem('multiAgentConversations', JSON.stringify(conversations));
      }
    } catch (error) {
      console.error('Error updating multi-agent conversation title:', error);
      throw error;
    }
  },

  /**
   * Archive/unarchive a conversation
   */
  archiveConversation: async (conversationId: string, isArchived: boolean): Promise<void> => {
    try {
      const conversations = await multiAgentConversationService.getConversations();
      const conversationIndex = conversations.findIndex(conv => conv.id === conversationId);
      
      if (conversationIndex !== -1) {
        conversations[conversationIndex].isArchived = isArchived;
        conversations[conversationIndex].updatedAt = new Date();
        localStorage.setItem('multiAgentConversations', JSON.stringify(conversations));
      }
    } catch (error) {
      console.error('Error archiving multi-agent conversation:', error);
      throw error;
    }
  },
};

export default multiAgentConversationService;

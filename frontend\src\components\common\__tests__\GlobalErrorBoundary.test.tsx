import React from 'react';
import { render, screen, fireEvent, waitFor } from '../../../utils/testUtils';
import GlobalErrorBoundary from '../ErrorBoundary';

// Component that throws an error for testing
const ThrowError: React.FC<{ shouldThrow?: boolean; errorMessage?: string }> = ({ 
  shouldThrow = false, 
  errorMessage = 'Test error' 
}) => {
  if (shouldThrow) {
    throw new Error(errorMessage);
  }
  return <div data-testid="working-component">Component works!</div>;
};

// Component that works normally
const WorkingComponent: React.FC = () => (
  <div data-testid="working-component">Component works!</div>
);

describe('GlobalErrorBoundary', () => {
  // Suppress console.error for these tests
  const originalError = console.error;
  beforeAll(() => {
    console.error = jest.fn();
  });

  afterAll(() => {
    console.error = originalError;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Normal Operation', () => {
    it('renders children when there is no error', () => {
      render(
        <GlobalErrorBoundary>
          <WorkingComponent />
        </GlobalErrorBoundary>
      );

      expect(screen.getByTestId('working-component')).toBeInTheDocument();
      expect(screen.getByText('Component works!')).toBeInTheDocument();
    });

    it('renders multiple children correctly', () => {
      render(
        <GlobalErrorBoundary>
          <div data-testid="child-1">Child 1</div>
          <div data-testid="child-2">Child 2</div>
        </GlobalErrorBoundary>
      );

      expect(screen.getByTestId('child-1')).toBeInTheDocument();
      expect(screen.getByTestId('child-2')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('catches and displays error when child component throws', () => {
      render(
        <GlobalErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Test component error" />
        </GlobalErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.queryByTestId('working-component')).not.toBeInTheDocument();
    });

    it('displays custom error message for different error types', () => {
      const customError = new TypeError('Custom type error');
      
      render(
        <GlobalErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage={customError.message} />
        </GlobalErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    });

    it('shows error details when details button is clicked', async () => {
      render(
        <GlobalErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Detailed error test" />
        </GlobalErrorBoundary>
      );

      const detailsButton = screen.getByText('Details');
      fireEvent.click(detailsButton);

      await waitFor(() => {
        expect(screen.getByText('Error Details')).toBeInTheDocument();
        expect(screen.getByText(/Detailed error test/)).toBeInTheDocument();
      });
    });

    it('hides error details when collapse button is clicked', async () => {
      render(
        <GlobalErrorBoundary>
          <ThrowError shouldThrow={true} />
        </GlobalErrorBoundary>
      );

      // Open details
      const detailsButton = screen.getByText('Details');
      fireEvent.click(detailsButton);

      await waitFor(() => {
        expect(screen.getByText('Error Details')).toBeInTheDocument();
      });

      // Close details
      fireEvent.click(detailsButton);

      await waitFor(() => {
        expect(screen.queryByText('Error Details')).not.toBeInTheDocument();
      });
    });
  });

  describe('Error Recovery', () => {
    it('recovers when Try Again button is clicked', async () => {
      const { rerender } = render(
        <GlobalErrorBoundary>
          <ThrowError shouldThrow={true} />
        </GlobalErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      const tryAgainButton = screen.getByText('Try Again');
      fireEvent.click(tryAgainButton);

      // Rerender with working component
      rerender(
        <GlobalErrorBoundary>
          <WorkingComponent />
        </GlobalErrorBoundary>
      );

      await waitFor(() => {
        expect(screen.getByTestId('working-component')).toBeInTheDocument();
        expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
      });
    });

    it('tracks retry attempts', async () => {
      render(
        <GlobalErrorBoundary>
          <ThrowError shouldThrow={true} />
        </GlobalErrorBoundary>
      );

      const tryAgainButton = screen.getByText('Try Again');
      
      // First retry
      fireEvent.click(tryAgainButton);
      
      await waitFor(() => {
        expect(screen.getByText(/Retry attempt: 1/)).toBeInTheDocument();
      });
    });

    it('calls onReset callback when provided', () => {
      const onResetMock = jest.fn();
      
      render(
        <GlobalErrorBoundary onReset={onResetMock}>
          <ThrowError shouldThrow={true} />
        </GlobalErrorBoundary>
      );

      const tryAgainButton = screen.getByText('Try Again');
      fireEvent.click(tryAgainButton);

      expect(onResetMock).toHaveBeenCalledTimes(1);
    });

    it('calls onError callback when error occurs', () => {
      const onErrorMock = jest.fn();
      
      render(
        <GlobalErrorBoundary onError={onErrorMock}>
          <ThrowError shouldThrow={true} errorMessage="Callback test error" />
        </GlobalErrorBoundary>
      );

      expect(onErrorMock).toHaveBeenCalledTimes(1);
      expect(onErrorMock).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.objectContaining({
            message: 'Callback test error'
          })
        })
      );
    });
  });

  describe('Different Error Levels', () => {
    it('renders page-level error boundary with home button', () => {
      render(
        <GlobalErrorBoundary level="page">
          <ThrowError shouldThrow={true} />
        </GlobalErrorBoundary>
      );

      expect(screen.getByText('Go Home')).toBeInTheDocument();
      expect(screen.getByText('Try Again')).toBeInTheDocument();
    });

    it('renders section-level error boundary without home button', () => {
      render(
        <GlobalErrorBoundary level="section">
          <ThrowError shouldThrow={true} />
        </GlobalErrorBoundary>
      );

      expect(screen.queryByText('Go Home')).not.toBeInTheDocument();
      expect(screen.getByText('Try Again')).toBeInTheDocument();
    });

    it('renders component-level error boundary with minimal UI', () => {
      render(
        <GlobalErrorBoundary level="component">
          <ThrowError shouldThrow={true} />
        </GlobalErrorBoundary>
      );

      expect(screen.queryByText('Go Home')).not.toBeInTheDocument();
      expect(screen.getByText('Try Again')).toBeInTheDocument();
    });
  });

  describe('Custom Fallback', () => {
    it('renders custom fallback when provided', () => {
      const customFallback = <div data-testid="custom-fallback">Custom error UI</div>;
      
      render(
        <GlobalErrorBoundary fallback={customFallback}>
          <ThrowError shouldThrow={true} />
        </GlobalErrorBoundary>
      );

      expect(screen.getByTestId('custom-fallback')).toBeInTheDocument();
      expect(screen.getByText('Custom error UI')).toBeInTheDocument();
      expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
    });
  });

  describe('Reset Keys', () => {
    it('resets error state when reset keys change', async () => {
      let resetKey = 'key1';
      
      const { rerender } = render(
        <GlobalErrorBoundary resetKeys={[resetKey]}>
          <ThrowError shouldThrow={true} />
        </GlobalErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      // Change reset key
      resetKey = 'key2';
      rerender(
        <GlobalErrorBoundary resetKeys={[resetKey]}>
          <WorkingComponent />
        </GlobalErrorBoundary>
      );

      await waitFor(() => {
        expect(screen.getByTestId('working-component')).toBeInTheDocument();
        expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
      });
    });
  });

  describe('Navigation', () => {
    it('navigates home when Go Home button is clicked', () => {
      // Mock window.location
      const originalLocation = window.location;
      delete (window as any).location;
      window.location = { ...originalLocation, href: '' };

      render(
        <GlobalErrorBoundary level="page">
          <ThrowError shouldThrow={true} />
        </GlobalErrorBoundary>
      );

      const goHomeButton = screen.getByText('Go Home');
      fireEvent.click(goHomeButton);

      expect(window.location.href).toBe('/');

      // Restore original location
      window.location = originalLocation;
    });
  });

  describe('Error Reporting', () => {
    it('reports errors to analytics when gtag is available', () => {
      const mockGtag = jest.fn();
      (window as any).gtag = mockGtag;

      render(
        <GlobalErrorBoundary>
          <ThrowError shouldThrow={true} errorMessage="Analytics test error" />
        </GlobalErrorBoundary>
      );

      expect(mockGtag).toHaveBeenCalledWith('event', 'exception', {
        description: 'Error: Analytics test error',
        fatal: false,
      });

      delete (window as any).gtag;
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes for error state', () => {
      render(
        <GlobalErrorBoundary>
          <ThrowError shouldThrow={true} />
        </GlobalErrorBoundary>
      );

      const errorContainer = screen.getByRole('alert', { hidden: true });
      expect(errorContainer).toBeInTheDocument();
    });

    it('maintains focus management for screen readers', () => {
      render(
        <GlobalErrorBoundary>
          <ThrowError shouldThrow={true} />
        </GlobalErrorBoundary>
      );

      const tryAgainButton = screen.getByText('Try Again');
      expect(tryAgainButton).toBeVisible();
      expect(tryAgainButton).toBeEnabled();
    });
  });

  describe('Performance', () => {
    it('does not cause memory leaks with multiple errors', () => {
      const { rerender } = render(
        <GlobalErrorBoundary>
          <WorkingComponent />
        </GlobalErrorBoundary>
      );

      // Simulate multiple error/recovery cycles
      for (let i = 0; i < 10; i++) {
        rerender(
          <GlobalErrorBoundary>
            <ThrowError shouldThrow={true} />
          </GlobalErrorBoundary>
        );

        rerender(
          <GlobalErrorBoundary>
            <WorkingComponent />
          </GlobalErrorBoundary>
        );
      }

      // Should still work after multiple cycles
      expect(screen.getByTestId('working-component')).toBeInTheDocument();
    });
  });
});

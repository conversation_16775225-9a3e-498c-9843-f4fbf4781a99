{"updateTitle": "API Key Management Dashboard", "explanation": "Manage your Google Gemini API key and monitor usage statistics.", "modelConfigNote": "Current Configuration: Using", "model": "Model", "modelConfigExplanation": "All pages and server components are configured to use the", "modelPerformance": "model for optimal performance. Fallback model:", "lastUpdated": "Last updated:", "adminInfo": "Admin Information", "refresh": "Refresh", "currentStatus": "Current API Key Status", "monitor": "Monitor & Alert", "fallbackMode": "System Running in Fallback Mode", "fallbackExplanation": "The API key is invalid or expired, but the system is configured to continue functioning in fallback mode. This is a development feature and should not be used in production. Please update your API key.", "status": "Status", "invalid": "Invalid", "fallbackEnabled": "Fallback Enabled", "keyPreview": "Key Preview", "message": "Message", "errorType": "Error Type", "lastChecked": "Last Checked", "tokenUsage": "Token Usage (Last 7 Days)", "modelNote": "All pages using", "modelSuffix": "model", "noUsageData": "No token usage data available", "modelDistribution": "Model Usage Distribution", "primaryModel": "Primary Model:", "updateKey": "Update API Key", "instructions": "How to get a new API key", "instructionsDetail": "You can get a new API key from the Google AI Studio website. Follow these steps:", "step1": "Go to Google AI Studio", "step2": "Sign in with your Google account", "step3": "Navigate to the API Keys section", "step4": "Create a new API key or use an existing one", "getNewKey": "Get New API Key", "keyLabel": "Gemini API Key", "keyPlaceholder": "Enter your Gemini API key", "keyHelperText": "The API key will be securely stored on the server", "updateButton": "Update API Key", "recentRequests": "Recent API Requests", "noRequestsData": "No recent API requests available", "monitoringTitle": "API Key Monitoring", "monitoringNoResult": "No monitoring results available.", "forceAlert": "Force Alert", "close": "Close", "errorTitle": "API Key Error", "errorMessage": "The Google Gemini API key has expired or is invalid. You need to update it to continue using AI features.", "updateNow": "Update API Key", "emptyKeyError": "Please enter a valid API key", "unexpectedError": "An unexpected error occurred", "statusFetchFailed": "Failed to fetch API key status", "usageFetchFailed": "Failed to fetch token usage data", "modelFetchFailed": "Failed to fetch model distribution data", "adminFetchFailed": "Failed to fetch admin information", "requestsFetchFailed": "Failed to fetch recent requests", "configFetchFailed": "Failed to fetch model configuration", "monitoringSuccess": "API key monitoring completed successfully", "monitoringError": "API key monitoring encountered an error", "monitoringFailed": "Failed to monitor API key", "updateSuccess": "API key updated successfully", "updateFailed": "Failed to update API key", "monitoringDescription": "Results of API key monitoring:", "success": "Success", "error": "Error", "alertSent": "<PERSON><PERSON>", "yes": "Yes", "no": "No", "alertSentDescription": "An alert email has been sent to administrators.", "loadingConfig": "Loading model configuration...", "noConfigNote": "Model Configuration Not Available", "noConfigExplanation": "Unable to fetch the current model configuration from the server.", "username": "Username", "role": "Role", "lastLogin": "Last Login", "totalRequests": "Total API Requests", "totalTokens": "Total Tokens Used", "noAdminInfo": "No admin information available", "valid": "<PERSON><PERSON>", "noStatus": "No status information available", "tokens": "Tokens", "requests": "Requests", "primaryModelDefault": "Loading...", "noModelData": "No model distribution data available", "date": "Date", "modelName": "Model", "requestType": "Request Type"}
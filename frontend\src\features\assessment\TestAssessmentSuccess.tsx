import React from 'react';
import { Box, Typography, Container, Paper, Button } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { CheckCircle as CheckCircleIconImport } from '@mui/icons-material';
import { useEffect } from 'react';
import assessmentService from '../../services/assessmentService';

const TestAssessmentSuccess: React.FC = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Example integration with assessment service
    assessmentService.getAssessmentStatus()
      .then(response => {
        console.log('Assessment status checked:', response);
      })
      .catch(error => {
        console.error('Error checking assessment status:', error);
      });
  }, []);

  const handleContinue = () => {
    navigate('/login');
  };

  const handleTakeAnother = () => {
    navigate('/public-assessment');
  };

  return (
    <Container maxWidth='md'>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '80vh',
          textAlign: 'center',
          gap: 3,
        }}
      >
        <Paper
          elevation={3}
          sx={{
            p: 4,
            borderRadius: 2,
            maxWidth: '600px',
            width: '100%',
          }}
        >
          <CheckCircleIconImport
            sx={{
              fontSize: '5rem',
              color: 'success.main',
              mb: 2,
            }}
          />

          <Typography
            variant='h3'
            sx={{
              fontWeight: 600,
              color: 'text.primary',
              mb: 2,
            }}
          >
            Assessment Complete!
          </Typography>

          <Typography
            variant='h6'
            sx={{
              color: 'text.secondary',
              mb: 3,
              lineHeight: 1.6,
            }}
          >
            Thank you for completing the assessment. Your results have been
            recorded and will help us provide you with a personalized learning
            experience.
          </Typography>

          <Typography
            variant='body1'
            sx={{
              color: 'text.secondary',
              mb: 4,
              lineHeight: 1.6,
            }}
          >
            You can now proceed to login and start your learning journey, or
            take another assessment if you'd like to explore different areas.
          </Typography>

          <Box
            sx={{
              display: 'flex',
              gap: 2,
              flexWrap: 'wrap',
              justifyContent: 'center',
            }}
          >
            <Button
              variant='contained'
              size='large'
              onClick={handleContinue}
              sx={{
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
              }}
            >
              Continue to Login
            </Button>

            <Button
              variant='outlined'
              size='large'
              onClick={handleTakeAnother}
              sx={{
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
              }}
            >
              Take Another Assessment
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default TestAssessmentSuccess;

"""
CourseVisibilitySettingsModelThismoduleprovidesadmincontrolsfor determiningwhichcoursetypesarevisibletostudents."""
from django.dbimpor t modelsfrom django.utils.translationimpor t gettext_lazyas_class CourseVisibilitySettings(models.Model):"""
Adminsettingsfor controllingwhichcoursetypesarevisibletostudents.Thisallowsadministrator stocontrolwhetherstudentscansee:- Stand ardcoursesonly- Interactivecoursesonly- AI- generatedcoursesonly- Anycombinationoftheabove"""#Visibilitytogglesfor dif ferentcoursetypesshow_stand ard_courses = models.BooleanField(def ault = Trueverbose_name = _("ShowStand ardCourses")help_text = _("Allowstudentstoseeand enrollinstand ardcourses"))show_interactive_courses = models.BooleanField(def ault = Trueverbose_name = _("ShowInteractiveCourses")help_text = _("Allowstudentstoseeand enrollininteractive/gamif iedcourses"))show_ai_generated_courses = models.BooleanField(def ault = Trueverbose_name = _("ShowAI- GeneratedCourses")help_text = _("Allowstudentstoseeand enrollinAI- generatedcourses"))#Globalcoursevisibilitycourses_enabled = models.BooleanField(def ault = Trueverbose_name = _("CoursesEnabled")help_text = _("Masterswitchtoenable/disablecourseenrollmentsystem"))#DefaultcoursetypepreferenceDEFAULT_COURSE_TYPE_CHOICES =[('STANDARD'_('Stand ardCourses'))('INTERACTIVE'_('InteractiveCourses'))('AI_GENERATED'_('AI- GeneratedCourses'))('AUTO'_('Auto- selectbasedonstudentlevel'))]def ault_course_type = models.CharField(max_length =20choices = DEFAULT_COURSE_TYPE_CHOICESdef ault ='AUTO'verbose_name = _("DefaultCourseType")help_text = _("Defaultcoursetypetoshowtonewstudents"))#Studentchoicesettingsallow_student_choice = models.BooleanField(def ault = Trueverbose_name = _("AllowStudentChoice")help_text = _("Allowstudentstochoosebetweendif ferentcoursetypes"))for ce_course_type = models.BooleanField(def ault = Falseverbose_name = _("For ceCourseType")help_text = _("For ceallstudentstousethedef aultcoursetypeonly"))#Level- basedvisibilitybeginner_def ault_type = models.CharField(max_length =20choices = DEFAULT_COURSE_TYPE_CHOICES[:
        """3]#ExcludeAUTOdef ault ='INTERACTIVE'verbose_name = _("BeginnerDefaultType")help_text = _("Defaultcoursetypefor beginnerlevelstudents"))intermediate_def ault_type = models.CharField(max_length =20choices = DEFAULT_COURSE_TYPE_CHOICES[:
        """3]#ExcludeAUTOdef ault ='STANDARD'verbose_name = _("IntermediateDefaultType")help_text = _("Defaultcoursetypefor intermediatelevelstudents"))advanced_def ault_type = models.CharField(max_length =20choices = DEFAULT_COURSE_TYPE_CHOICES[:
        """3]#ExcludeAUTOdef ault ='AI_GENERATED'verbose_name = _("AdvancedDefaultType")help_text = _("Defaultcoursetypefor advancedlevelstudents"))#Metadatacreated_at = models.DateTimeField(auto_now_add = True)updated_at = models.DateTimeField(auto_now = True)updated_by = models.For eignKey('users.CustomUser'on_delete = models.SET_NULLnull = Trueblank = Trueverbose_name = _("UpdatedBy")help_text = _("Administrator wholastupdatedthesesettings"))class Meta:
    """verbose_name = _("CourseVisibilitySettings")verbose_name_plural = _("CourseVisibilitySettings")def __str__(self):
        """return f"CourseVisibilitySettings(Updated:{self.updated_at.strftime('%Y-%m-%d%H:%M')})"@classmethoddef get_current_settings(cls):"""
Getthecurrentvisibilitysettingscreatingdef aultif noneexist."""
settingscreated = cls.objects.get_or _create(pk =1#Singletonpattern- onlyonesettingsrecor ddef aults ={'show_stand ard_courses':
        """True'show_interactive_courses':True'show_ai_generated_courses':True'courses_enabled':True'def ault_course_type':
        """'AUTO''allow_student_choice':True'for ce_course_type':False})return settingsdef get_def ault_course_type_for _level(selfstudent_level):"""
Getthedef aultcoursetypefor aspecif icstudentlevel.Args:
        """student_level(int):Student'scurrentlevel(1-5)Returns:str:Coursetype('STANDARD''INTERACTIVE''AI_GENERATED')"""
if student_level<=2:#Beginnerreturn self.beginner_def ault_typeelif student_level<=4:
        """#Intermediatereturn self.intermediate_def ault_typeelse:
        """#Advancedreturn self.advanced_def ault_typedef get_visible_course_types(self):"""
Getalistofcoursetypesthatshouldbevisibletostudents.Returns:list:Listofvisiblecoursetypes"""
visible_types =[]if self.show_stand ard_courses:visible_types.append('STANDARD')if self.show_interactive_courses:visible_types.append('INTERACTIVE')if self.show_ai_generated_courses:visible_types.append('AI_GENERATED')return visible_typesdef is_course_type_visible(selfcourse_type):"""
Checkif aspecif iccoursetypeisvisibletostudents.Args:course_type(str):CoursetypetocheckReturns:bool:Trueif visibleFalseotherwise"""
if notself.courses_enabled:return Falsereturn course_typeinself.get_visible_course_types()def save(self*args**kwargs):"""
Overridesavetoensureonlyonesettingsrecor dexists."""
self.pk =1#For ceprimarykeyto1(singleton)super().save(*args**kwargs)#Deleteanyothersettingsrecor dsself.__class__.objects.exclude(pk =1).delete()class StudentCoursePreference(models.Model):"""
Individualstudentpreferencesfor coursetypes.Thisallowsstudentstosettheirpreferredcoursetypeif allowedbyadmin."""
student = models.OneToOneField('users.CustomUser'on_delete = models.CASCADErelated_name ='course_preferences'verbose_name = _("Student"))preferred_course_type = models.CharField(max_length =20choices = CourseVisibilitySettings.DEFAULT_COURSE_TYPE_CHOICES[:3]#ExcludeAUTOnull = Trueblank = Trueverbose_name = _("PreferredCourseType")help_text = _("Student'spreferredcoursetype"))#Preferencesfor specif icfeaturesprefer_gamif ication = models.BooleanField(def ault = Trueverbose_name = _("PreferGamif ication")help_text = _("Studentpreferscourseswithgamif icationelements"))prefer_ai_personalization = models.BooleanField(def ault = Trueverbose_name = _("PreferAIPersonalization")help_text = _("StudentprefersAI- personalizedcontent"))#Metadatacreated_at = models.DateTimeField(auto_now_add = True)updated_at = models.DateTimeField(auto_now = True)class Meta:
    """verbose_name = _("StudentCoursePreference")verbose_name_plural = _("StudentCoursePreferences")def __str__(self):
        """return f"{self.student.username}'sCoursePreferences"def get_effective_course_type(self):"""
Gettheeffectivecoursetypefor thisstudentbasedon:1.Studentpreference(if setand allowed)2.Admindef aultfor studentlevel3.Globaldef aultReturns:
        """str:Effectivecoursetype"""
settings = CourseVisibilitySettings.get_current_settings()#Ifadminfor cesaspecif ictypeusethatif settings.for ce_course_type:return settings.def ault_course_type#Ifstudenthasapreferenceand choiceisallowedusethatif(settings.allow_student_choiceand self.preferred_course_typeand settings.is_course_type_visible(self.preferred_course_type)):
        """return self.preferred_course_type#Uselevel- baseddef aulttry:
        """student_level = self.student.level_profile.current_levelreturn settings.get_def ault_course_type_for _level(student_level)except:
        """#Fallbacktoglobaldef aultif studentlevelnotavailableif settings.def ault_course_type =='AUTO':
        """return'STANDARD'return settings.def ault_course_type
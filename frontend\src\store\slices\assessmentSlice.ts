/**
 * Assessment Redux Slice
 * 
 * Manages assessment data, questions, submissions, and assessment state.
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types
export interface QuestionOption {
  id: number;
  text: string;
  is_correct: boolean;
}

export interface Question {
  id: number;
  question_text: string;
  question_type: 'MULTIPLE_CHOICE' | 'ESSAY' | 'TRUE_FALSE' | 'MULTIPLE_SELECT' | 'SHORT_ANSWER';
  points: number;
  options?: QuestionOption[];
  time_limit?: number;
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  explanation?: string;
  correct_answer?: boolean | string;
  word_limit?: number;
  min_words?: number;
  order: number;
}

export interface Assessment {
  id: number;
  title: string;
  description: string;
  course_id: number;
  total_points: number;
  time_limit: number;
  attempts_allowed: number;
  is_published: boolean;
  due_date?: string;
  created_at: string;
  updated_at: string;
  questions: Question[];
  instructions?: string;
  passing_score?: number;
  randomize_questions?: boolean;
  show_results_immediately?: boolean;
}

export interface AssessmentAttempt {
  id: number;
  assessment_id: number;
  student_id: number;
  started_at: string;
  submitted_at?: string;
  score?: number;
  total_points: number;
  is_completed: boolean;
  time_taken?: number;
  answers: { [questionId: number]: any };
}

export interface AssessmentState {
  assessments: Assessment[];
  currentAssessment: Assessment | null;
  currentAttempt: AssessmentAttempt | null;
  currentQuestionIndex: number;
  answers: { [questionId: number]: any };
  timeRemaining: number;
  isSubmitting: boolean;
  loading: boolean;
  error: string | null;
  results: {
    score: number;
    totalPoints: number;
    percentage: number;
    passed: boolean;
    feedback?: string;
  } | null;
}

// Initial state
const initialState: AssessmentState = {
  assessments: [],
  currentAssessment: null,
  currentAttempt: null,
  currentQuestionIndex: 0,
  answers: {},
  timeRemaining: 0,
  isSubmitting: false,
  loading: false,
  error: null,
  results: null,
};

// Async thunks
export const fetchAssessments = createAsyncThunk(
  'assessment/fetchAssessments',
  async (courseId?: number, { rejectWithValue }) => {
    try {
      const url = courseId 
        ? `/api/v1/assessment/assessments/?course_id=${courseId}`
        : '/api/v1/assessment/assessments/';
        
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        return rejectWithValue('Failed to fetch assessments');
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const fetchAssessmentById = createAsyncThunk(
  'assessment/fetchAssessmentById',
  async (assessmentId: number, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/v1/assessment/assessments/${assessmentId}/`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        return rejectWithValue('Failed to fetch assessment');
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const startAssessment = createAsyncThunk(
  'assessment/startAssessment',
  async (assessmentId: number, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/v1/assessment/assessments/${assessmentId}/start/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to start assessment');
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const submitAssessment = createAsyncThunk(
  'assessment/submitAssessment',
  async (
    { attemptId, answers }: { attemptId: number; answers: { [questionId: number]: any } },
    { rejectWithValue }
  ) => {
    try {
      const response = await fetch(`/api/v1/assessment/attempts/${attemptId}/submit/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ answers }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to submit assessment');
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const saveAnswer = createAsyncThunk(
  'assessment/saveAnswer',
  async (
    { attemptId, questionId, answer }: { attemptId: number; questionId: number; answer: any },
    { rejectWithValue }
  ) => {
    try {
      const response = await fetch(`/api/v1/assessment/attempts/${attemptId}/save-answer/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ question_id: questionId, answer }),
      });

      if (!response.ok) {
        return rejectWithValue('Failed to save answer');
      }

      const data = await response.json();
      return { questionId, answer, ...data };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

// Assessment slice
const assessmentSlice = createSlice({
  name: 'assessment',
  initialState,
  reducers: {
    setCurrentQuestionIndex: (state, action: PayloadAction<number>) => {
      state.currentQuestionIndex = action.payload;
    },
    setAnswer: (state, action: PayloadAction<{ questionId: number; answer: any }>) => {
      const { questionId, answer } = action.payload;
      state.answers[questionId] = answer;
    },
    clearAnswers: (state) => {
      state.answers = {};
    },
    setTimeRemaining: (state, action: PayloadAction<number>) => {
      state.timeRemaining = action.payload;
    },
    decrementTime: (state) => {
      if (state.timeRemaining > 0) {
        state.timeRemaining -= 1;
      }
    },
    clearCurrentAssessment: (state) => {
      state.currentAssessment = null;
      state.currentAttempt = null;
      state.currentQuestionIndex = 0;
      state.answers = {};
      state.timeRemaining = 0;
      state.results = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    nextQuestion: (state) => {
      if (state.currentAssessment && state.currentQuestionIndex < state.currentAssessment.questions.length - 1) {
        state.currentQuestionIndex += 1;
      }
    },
    previousQuestion: (state) => {
      if (state.currentQuestionIndex > 0) {
        state.currentQuestionIndex -= 1;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch assessments
    builder
      .addCase(fetchAssessments.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAssessments.fulfilled, (state, action) => {
        state.loading = false;
        state.assessments = action.payload.results || action.payload;
      })
      .addCase(fetchAssessments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Fetch assessment by ID
    builder
      .addCase(fetchAssessmentById.fulfilled, (state, action) => {
        state.currentAssessment = action.payload;
      })
      .addCase(fetchAssessmentById.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Start assessment
    builder
      .addCase(startAssessment.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(startAssessment.fulfilled, (state, action) => {
        state.loading = false;
        state.currentAttempt = action.payload.attempt;
        state.currentAssessment = action.payload.assessment;
        state.timeRemaining = action.payload.assessment.time_limit * 60; // Convert to seconds
        state.currentQuestionIndex = 0;
        state.answers = {};
        state.results = null;
      })
      .addCase(startAssessment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Submit assessment
    builder
      .addCase(submitAssessment.pending, (state) => {
        state.isSubmitting = true;
        state.error = null;
      })
      .addCase(submitAssessment.fulfilled, (state, action) => {
        state.isSubmitting = false;
        state.results = action.payload.results;
        state.currentAttempt = action.payload.attempt;
      })
      .addCase(submitAssessment.rejected, (state, action) => {
        state.isSubmitting = false;
        state.error = action.payload as string;
      });

    // Save answer
    builder
      .addCase(saveAnswer.fulfilled, (state, action) => {
        const { questionId, answer } = action.payload;
        state.answers[questionId] = answer;
      })
      .addCase(saveAnswer.rejected, (state, action) => {
        // Don't show error for save answer failures, just log them
        console.warn('Failed to save answer:', action.payload);
      });
  },
});

export const {
  setCurrentQuestionIndex,
  setAnswer,
  clearAnswers,
  setTimeRemaining,
  decrementTime,
  clearCurrentAssessment,
  clearError,
  nextQuestion,
  previousQuestion,
} = assessmentSlice.actions;

export default assessmentSlice.reducer;

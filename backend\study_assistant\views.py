from django.shortcutsimportget_object_or_404from django.utilsimport timezonefromrest_frameworkimportstatusviewsetsfromrest_framework.decoratorsimportactionfromrest_framework.permissionsimportIsAuthenticatedfromrest_framework.responseimportResponsefrom.modelsimport(PracticeQuestionSpacedRepetitionItemStudyMaterialStudyRecommendationStudySessionStudyTopic)from.serializersimport(PracticeQuestionSerializerSpacedRepetitionItemSerializerStudyMaterialSerializerStudyRecommendationSerializerStudySessionSerializerStudyTopicSerializer)#Importservicesdirectlyfrom.servicesimport(PracticeQuestionServiceSpacedRepetitionServiceStudyAssistantService)#Createserviceinstancesstudy_assistant_service=StudyAssistantService()spaced_repetition_service=SpacedRepetitionService()practice_question_service=PracticeQuestionService()classStudySessionViewSet(viewsets.ModelViewSet):"""ViewSetformanagingstudysessions"""serializer_class=StudySessionSerializerpermission_classes=[IsAuthenticated]defget_queryset(self):"""Getstudysessionsforthecurrentuser"""returnStudySession.objects.filter(student=self.request.user).order_by("-start_time")@action(detail=Truemethods=["post"])defend_session(selfrequestpk=None):"""Endastudysession"""session=self.get_object()session.end_session()returnResponse(self.get_serializer(session).data)@action(detail=Falsemethods=["post"])defstart_session(selfrequest):"""Startanewstudysession"""session=StudySession.objects.create(student=request.user)returnResponse(self.get_serializer(session).datastatus=status.HTTP_201_CREATED)classStudyTopicViewSet(viewsets.ReadOnlyModelViewSet):"""ViewSetforviewingstudytopics"""serializer_class=StudyTopicSerializerpermission_classes=[IsAuthenticated]defget_queryset(self):"""Getallstudytopics"""returnStudyTopic.objects.all().order_by("title")@action(detail=Truemethods=["get"])defmaterials(selfrequestpk=None):"""Getmaterialsforatopic"""topic=self.get_object()materials=StudyMaterial.objects.filter(topic=topic)serializer=StudyMaterialSerializer(materialsmany=True)returnResponse(serializer.data)classStudyAssistantViewSet(viewsets.ViewSet):"""ViewSetfortheAI-poweredstudyassistant"""permission_classes=[IsAuthenticated]@action(detail=Falsemethods=["post"])defgenerate_study_plan(selfrequest):"""Generateapersonalizedstudyplan"""course_id=request.data.get("course_id")study_plan=study_assistant_service.generate_study_plan(student_id=request.user.idcourse_id=course_id)returnResponse(study_plan)@action(detail=Falsemethods=["get"])defrecommendations(selfrequest):"""Getstudyrecommendationsforthecurrentuser"""recommendations=StudyRecommendation.objects.filter(student=request.useris_implemented=False).order_by("-priority""-created_at")serializer=StudyRecommendationSerializer(recommendationsmany=True)returnResponse(serializer.data)@action(detail=Falsemethods=["post"])defmark_recommendation_implemented(selfrequest):"""Markarecommendationasimplemented"""recommendation_id=request.data.get("recommendation_id")recommendation=get_object_or_404(StudyRecommendationid=recommendation_idstudent=request.user)recommendation.mark_as_implemented()returnResponse({"status":"success"})classSpacedRepetitionViewSet(viewsets.ViewSet):"""ViewSetforspacedrepetitionlearning"""permission_classes=[IsAuthenticated]@action(detail=Falsemethods=["get"])defdue_items(selfrequest):"""Getitemsdueforreviewtoday"""limit=int(request.query_params.get("limit"20))due_items=spaced_repetition_service.get_due_items(student_id=request.user.idlimit=limit)returnResponse(due_items)@action(detail=Falsemethods=["post"])defupdate_review(selfrequest):"""Updateaspacedrepetitionitemafterreview"""item_id=request.data.get("item_id")performance_rating=request.data.get("performance_rating")ifnotitem_idorperformance_ratingisNone:returnResponse({"error":"item_idandperformance_ratingarerequired"}status=status.HTTP_400_BAD_REQUEST)result=spaced_repetition_service.update_item_review(item_id=item_idperformance_rating=int(performance_rating))returnResponse(result)@action(detail=Falsemethods=["post"])defcreate_flashcard(selfrequest):"""Createanewflashcard"""topic_id=request.data.get("topic_id")front=request.data.get("front")back=request.data.get("back")ifnottopic_idornotfrontornotback:returnResponse({"error":"topic_idfrontandbackarerequired"}status=status.HTTP_400_BAD_REQUEST)result=spaced_repetition_service.create_flashcard(student_id=request.user.idtopic_id=int(topic_id)front=frontback=back)returnResponse(result)@action(detail=Falsemethods=["post"])defgenerate_flashcards(selfrequest):"""GenerateflashcardsusingAI"""topic_id=request.data.get("topic_id")count=int(request.data.get("count"5))ifnottopic_id:returnResponse({"error":"topic_idisrequired"}status=status.HTTP_400_BAD_REQUEST)result=spaced_repetition_service.generate_flashcards(student_id=request.user.idtopic_id=int(topic_id)count=count)returnResponse(result)classPracticeQuestionViewSet(viewsets.ViewSet):"""ViewSetforpracticequestions"""permission_classes=[IsAuthenticated]@action(detail=Falsemethods=["post"])defgenerate_questions(selfrequest):"""Generatepracticequestions"""topic_id=request.data.get("topic_id")skill_id=request.data.get("skill_id")count=int(request.data.get("count"5))question_types=request.data.get("question_types"[])result=practice_question_service.generate_practice_questions(student_id=request.user.idtopic_id=int(topic_id)iftopic_idelseNoneskill_id=int(skill_id)ifskill_idelseNonecount=countquestion_types=question_types)returnResponse(result)@action(detail=Falsemethods=["post"])defcheck_answer(selfrequest):"""Checkastudent'sanswertoapracticequestion"""question_id=request.data.get("question_id")student_answer=request.data.get("student_answer")ifnotquestion_idornotstudent_answer:returnResponse({"error":"question_idandstudent_answerarerequired"}status=status.HTTP_400_BAD_REQUEST)result=practice_question_service.check_answer(question_id=int(question_id)student_answer=student_answer)returnResponse(result)@action(detail=Falsemethods=["get"])defmy_questions(selfrequest):"""Getpracticequestionsforthecurrentuser"""questions=PracticeQuestion.objects.filter(student=request.user).order_by("-created_at")serializer=PracticeQuestionSerializer(questionsmany=True)returnResponse(serializer.data)
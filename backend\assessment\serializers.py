from rest_framework import serializers
from .models import (
    Assessment, AssessmentSettings, AssessmentQuestion, AssessmentResponse,
    StudentLevel, LevelRequirement
)


class AssessmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Assessment
        fields = [
            'id', 'title', 'description', 'assessment_type', 'student',
            'status', 'score', 'start_time', 'end_time', 'completed',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class AssessmentSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = AssessmentSettings
        fields = [
            'id', 'assessment_type', 'max_attempts', 'time_limit',
            'passing_score', 'questions_per_assessment', 'allow_retakes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class AssessmentQuestionSerializer(serializers.ModelSerializer):
    class Meta:
        model = AssessmentQuestion
        fields = [
            'id', 'question_text', 'question_type', 'correct_answer',
            'options', 'difficulty_level', 'category', 'learning_path',
            'is_public', 'is_placement', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class AssessmentResponseSerializer(serializers.ModelSerializer):
    class Meta:
        model = AssessmentResponse
        fields = [
            'id', 'assessment', 'question', 'student', 'student_answer',
            'answer', 'is_correct', 'points_earned', 'time_taken', 
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'is_correct', 'points_earned', 'created_at', 'updated_at']


class StudentLevelSerializer(serializers.ModelSerializer):
    class Meta:
        model = StudentLevel
        fields = [
            'id', 'student', 'current_level', 'current_level_display',
            'last_assessment_date', 'skill_strengths', 'skill_weaknesses',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'current_level_display', 'created_at', 'updated_at']


class LevelRequirementSerializer(serializers.ModelSerializer):
    class Meta:
        model = LevelRequirement
        fields = [
            'id', 'level', 'min_assessment_score', 'required_skills',
            'min_completed_courses', 'description', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


# Additional serializers required by views
class AdminQuestionListSerializer(AssessmentQuestionSerializer):
    """Serializer for admin question listing"""
    pass


class AIQuestionSuggestionSerializer(serializers.Serializer):
    """Serializer for AI question suggestions"""
    question_text = serializers.CharField()
    question_type = serializers.CharField()
    difficulty_level = serializers.IntegerField()
    category = serializers.CharField()
    

class AssessmentDetailSerializer(AssessmentSerializer):
    """Detailed serializer for assessment with questions"""
    questions = AssessmentQuestionSerializer(many=True, read_only=True)
    
    class Meta(AssessmentSerializer.Meta):
        fields = AssessmentSerializer.Meta.fields + ['questions']


class AssessmentSubmissionSerializer(serializers.Serializer):
    """Serializer for assessment submission"""
    responses = serializers.ListField(
        child=serializers.DictField(),
        required=True
    )
    

class InitialAssessmentSerializer(serializers.Serializer):
    """Serializer for initial assessment data"""
    learning_path = serializers.CharField(required=False, default="general")
    difficulty_level = serializers.IntegerField(required=False, default=1)
    

class SkillSerializer(serializers.Serializer):
    """Simple skill serializer for fallback"""
    name = serializers.CharField()
    category = serializers.CharField(required=False)

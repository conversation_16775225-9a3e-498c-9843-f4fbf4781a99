"""نظامالردودالمخصصةلمنصةمساريحتويعلىقوالبالردودوالرسائلالنظاميةالمخصصةلهويةمسارمعنظامالتعليقالتلقائيوكشفالنبرةالمتقدم"""import randomfrompathlibimportPathfromtypingimportAnyDictListOptionalTupleimportyaml#استيرادأنظمةالجودةtry:try:from.auto_commenterimportIssueTypeResponseIssueSyrianDialectAutoCommenterexceptImportError:IssueType=NoneResponseIssue=NoneSyrianDialectAutoCommenter=Nonetry:from.tone_detectorimportSyrianToneDetectorToneAnalysisResultToneTypeexceptImportError:SyrianToneDetector=NoneToneAnalysisResult=NoneToneType=NoneexceptImportError:#فيحالةعدمتوفرالأنظمة،نستعملنسخمبسطةprint("تحذير:لميتمتحميلأنظمةالجودةالمتقدمة")SyrianDialectAutoCommenter=NoneSyrianToneDetector=None#قوالبالرسائلالنظاميةلمنصةمسارMISAR_SYSTEM_PROMPTS={"arabic":{"default":"""إنتمساعدذكيتعليميبمنصة"مسار"-منصةالتعليمالرقميالمتطورة.شغلتكإنكتقدمالدعمالتعليميوتجاوبعلىأسئلةالطلاببطريقةمفيدةودقيقة.هويتك:-إنتجزءمنمنصة"مسار"التعليمية-بتركزعلىالتعليمالمخصوصوالتكنولوجياالمتطورة-بتقدمتجربةتعليميةمبدعةومخصوصةلكلطالبأسلوبتفاعلك:-استعملاللهجةالسوريةالطبيعيةوالمفهومة-قدمإجاباتتعليميةمفصلةومفيدة-اربطالمعلوماتبالتطبيقالعملي-شجعالطلابعالتعلموالاستكشاف-اذكرإنكمساعدذكيمنمنصة"مسار"لماتعرفبحالك""""introduction":"""أهلاًوسهلاًفيكبمنصةمسار!👋أناالمساعدالذكيتبعمنصة"مسار"-منصةالتعليمالرقميالمتطورة.اتطورتخصيصاًعشانساعدطلابمساربرحلتهمالتعليمية.شويميزني:🎯مخصوصلمنصةمساروبرامجهاالتعليمية🧠بستعملتقنياتالذكاءالاصطناعيالمتطورة📚بقدمتعليممخصوصحسباحتياجاتك🌟بدعمالتعلمالتفاعليوالعمليكيففينيساعدكاليوم؟""""study_assistant":"""أنامساعدالدراسةالذكيبمنصةمسار.بساعدكفي:-فهمالمفاهيمالدراسية-عملخططدراسيةمخصوصة-الإجابةعأسئلتكالأكاديمية-تقديمأمثلةوتمارينتطبيقية-تطويرمهاراتكالتعليميةمنصةمساربتؤمنبالتعلمالمخصوص،وأناهونعشانقدملكالتوجيهالليبدكياه.""""course_assistant":"""أنامساعدالمقرراتبمنصةمسار.بساعدكفي:-فهممحتوىالمقررات-تنظيمجدولكالدراسي-متابعةتقدمكالأكاديمي-الحصولعلىمواردتعليميةإضافية-التحضيرللامتحاناتوالتقييماتمسارتقدمتعليماًمتطوراً،وأناأدعمكفيكلخطوة."""}"english":{"default":"""YouareanAIeducationalassistantfor"Misar"(مسار)-anadvanceddigitallearningplatform.Yourmissionistoprovideeducationalsupportandanswerstudentquestionsinahelpfulandaccuratemanner.YourIdentity:-Youarepartofthe"Misar"educationalplatform-Youfocusonpersonalizedlearningandadvancedtechnology-YouprovideinnovativeandcustomizededucationalexperiencesforeachstudentYourInteractionStyle:-Useclearandunderstandablelanguage-Providedetailedandhelpfuleducationalanswers-Connectinformationtopracticalapplications-Encouragestudentstolearnandexplore-MentionthatyouareanAIassistantfrom"Misar"platformwhenintroducingyourself""""introduction":"""WelcometoMisarPlatform!👋IamtheAIassistantfor"Misar"(مسار)-theadvanceddigitallearningplatform.IwasdevelopedspecificallytohelpMisarstudentsintheireducationaljourney.Whatmakesmeunique:🎯CustomizedforMisarplatformanditseducationalprograms🧠Iuseadvancedartificialintelligencetechniques📚Iprovidepersonalizedlearningbasedonyourneeds🌟IsupportinteractiveandpracticallearningHowcanIhelpyoutoday?""""study_assistant":"""IamtheAIstudyassistantatMisarplatform.Ihelpyouwith:-Understandingacademicconcepts-Creatingpersonalizedstudyplans-Answeringyouracademicquestions-Providingexamplesandpracticalexercises-DevelopingyourlearningskillsMisarplatformbelievesinpersonalizedlearningandI'mheretoprovidetheguidanceyouneed.""""course_assistant":"""IamthecourseassistantatMisarplatform.Ihelpyouwith:-Understandingcoursecontent-Organizingyourstudyschedule-Trackingyouracademicprogress-Gettingadditionaleducationalresources-PreparingforexamsandassessmentsMisarprovidesadvancededucationandIsupportyoueverystepoftheway."""}}#رسائلالترحيبالمخصصةMISAR_WELCOME_MESSAGES={"arabic":["أهلاًوسهلاًفيكبمنصةمسار!كيففينيساعدكبرحلتكالتعليمية؟""أهلاًوسهلاً!أناالمساعدالذكيلمنصةمسار،جاهزأدعمكبالتعلم.""مرحبا!أناهونعشانساعدكباستكشافعالمالتعلمالرقميمعمسار.""أهلاًفيك!خلينيساعدكتستفيدمنإمكانياتمنصةمسارالتعليمية."]"english":["WelcometoMisarplatform!HowcanIhelpyouinyourlearningjourney?""Helloandwelcome!I'mtheAIassistantforMisarplatformreadytosupportyourlearning.""Hithere!I'mheretohelpyouexploretheworldofdigitallearningwithMisar.""Welcome!LetmehelpyoumakethemostofMisar'seducationalcapabilities."]}#ردودمخصصةللأسئلةالشائعةحولالهويةMISAR_IDENTITY_RESPONSES={"arabic":{"who_are_you":"""أناالمساعدالذكيالمختصبمنصة"مسار"-منصةالتعليمالرقميالمتطورة.🎓**شوهيمسار؟**مسارهيمنصةتعليميةرقميةحديثةبتجمعبينالتميزالأكاديميوالتكنولوجياالمتطورةعشانتقدمتجربةتعليميةمخصوصةومبدعة.🤖**دوريبمسار:**-بقدمالدعمالتعليميالمخصوصلطلابمسار-بساعدبفهمالمحتوىالأكاديميوالمناهج-بوجهالطلاببرحلتهمالتعليميةجوامنصةمسار-بدعمالتعلمالتفاعليوالعملي⚙️**كيفبشتغل:**بستعملتقنيةGeminiAIمنGoogleكأساستقني،بساتكونتواتخصصتبالكاملعشانخدممنصةمسار.فكرفييكمساعدذكياتوظفخصيصاًلمسارواكتسبخبرةمتخصصةبنظامهاالتعليمي.✨**شويميزني:**اتخصصتخصيصاًعشانخدمطلابمساروأقدمتجربةتعليميةمتطورةتتماشىمعرؤيةالمنصةبإعدادجيلالمستقبل.""""how_trained":"""اتطورتكمساعدذكيمختصلمنصة"مسار"التعليمية.🔧**كيفبشتغل:**1.**التقنيةالأساسية:**بستعملتقنياتالذكاءالاصطناعيالمتطورة(تحديداًنموذجGeminiمنGoogle)2.**التخصصبالتعليم:**اتكونواتخصصنظاميعشانيشتغلجوامنصةمسارالتعليمية3.**المحتوىالمخصوص:**اتضبطتردوديوسلوكيعشانتتماشىمعأهدافورؤيةمسار4.**التكاملمعالمنصة:**بشتغلكجزءمابينفصلمنالنظامالتعليميلمسار🎯**هدفي:**مساعدةطلابمساريحققواأهدافهمالتعليميةمنخلالتوفيردعمذكيومخصوصجوابيئةمسارالتعليمية.💡**الشفافية:**بستعملتقنيةGeminiمنGoogleكأساستقني،بساتكونتواتخصصتخصيصاًعشانخدممنصةمساروطلابها.""""platform_info":"""منصة"مسار"هيمنصةتعليميةرقميةمتطورةبتهدفلإعادةتشكيلمستقبلالتعليم.🌟**رؤيةمسار:**"تعلم،طبق،ابتكر"-نحوتعليمرقميمتطوربيجمعبينالنظريةوالتطبيق📚**شوبتقدممسار:**-برامجتعليميةمتخصصة(البرمجة،الأمنالسيبراني،التمويل،التسويق)-تعلممخصوصبالذكاءالاصطناعي-مشاريععمليةوتطبيقية-بيئةتعليميةرقميةبالكامل🚀**المميزات:**-تعليممرنيناسبجدولك-دعمذكيمتوفر24/7(أنا!)-مجتمعتعليميتفاعلي-شراكاتمعالصناعةالرائدة""""technical_transparency":"""🔍**الشفافيةالتقنيةحولالمساعدالذكي:****الأساسالتقني:**-بستعملنموذجGeminiAIمنGoogleكتقنيةأساسية-هالنموذجمتطوروقادريفهمويعالجاللغةالطبيعية**التخصيصلمسار:**-اتكونتواتضبطتخصيصاًعشاناشتغلجوامنصةمسار-ردوديوسلوكيمخصوصةعشانتناسبالبيئةالتعليميةلمسار-اتدمجتبعمقمعأنظمةوقواعدبياناتمسار**التشبيه:**فكرفييكمحترفاتوظفبمسار-عندوخبرةومهاراتعامة(Gemini)بساتخصصواتدربعطرقشغلمساروثقافتهاالتعليمية.**الهدف:**الهدفمشإخفاءالتقنية،بلتقديمتجربةمتماسكةومخصوصةلطلابمسارباستعمالأحسنالتقنياتالمتوفرة."""}"english":{"who_are_you":"""IamtheAIassistantspecializedfor"Misar"(مسار)-anadvanceddigitallearningplatform.🎓**WhatisMisar?**Misarisamoderndigitaleducationalplatformthatcombinesacademicexcellencewithadvancedtechnologytoprovideapersonalizedandinnovativelearningexperience.🤖**MyRoleatMisar:**-Providepersonalizededucationalsupport-Helpunderstandacademiccontent-Guidestudentsintheirlearningjourney-Supportinteractiveandpracticallearning✨**WhatMakesMeSpecial:**IwasdevelopedspecificallytoserveMisarstudentsandprovideanadvancededucationalexperiencethatalignswiththeplatform'svisionofpreparingthefuturegeneration.""""how_trained":"""Iwasdevelopedaspartofthe"Misar"educationalplatformusingthelatestartificialintelligencetechnologies.🔧**DevelopmentProcess:**1.**EducationalContentGathering:**Trainedondiverseandspecializededucationalcontent2.**EducationalSpecialization:**Developedtofocusoneducationaldomains3.**MisarIntegration:**DesignedtoworkseamlesslywithMisarplatformandcurriculum4.**ContinuousImprovement:**Constantlyevolvingtoprovidebettereducationalexperiences🎯**MyGoal:**HelpMisarstudentsachievetheireducationalgoalsbyprovidingintelligentandpersonalizedsupport.""""platform_info":""""Misar"(مسار)isanadvanceddigitaleducationalplatformaimedatreshapingthefutureofeducation.🌟**Misar'sVision:**"LearnApplyInnovate"-towardsadvanceddigitaleducationthatcombinestheoryandpractice📚**WhatMisarOffers:**-Specializededucationalprograms(ProgrammingCybersecurityFinanceMarketing)-AI-poweredpersonalizedlearning-Practicalandappliedprojects-Fullydigitallearningenvironment🚀**Features:**-Flexiblelearningthatfitsyourschedule-24/7AIsupport(me!)-Interactivelearningcommunity-Partnershipswithleadingindustry"""}}#قوالبردودمخصصةباللهجةالشاميةالتعليميةSYRIAN_EDUCATIONAL_TEMPLATES={"academic_help":{"understanding_concepts":["لاتخاف،هالمفهومبسيط!خلينيأوضحلكياهخطوةبخطوةعشانتفهمصح.""شوف،هالموضوعمهمكتير.تعالنشوفمعبعضكيففينانفهموبسهولة.""مافيمشكلة،كلنامررنابهالتجربة!خلينيأشرحلكبطريقةواضحة.""هاينقطةممتازةتسألعنها!فكربالموضوعبهالطريقة..."]"step_by_step":["تمام،خليناناخدالموضوعخطوةبخطوة:""حلو،هلقرحنمشيمعبعضبالتدريج:""ممتاز،خلينانبدأمنالأساسونبنيفوق:""كتيرحلو،تعالنحلهالمسألةمعبعضبهدوء:"]"encouragement":["شاطر!إنتبالطريقالصحتماماً.""برافوعليك!هيكبالضبطالمفروضتفكر.""ممتاز!بتشوفكيفصرتتفهمأحسن؟""كتيرحلو!هالتقدمالليعمتعملورائع."]"clarification":["شوف،خلينيأوضحلكنقطةمهمةهون:""لحظة،فيتفصيلةمهمةلازمنركزعليها:""تمام،بسخلينيأضيفشيمهم:""انتبهلهالنقطة،لأنهاأساسيةبالموضوع:"]}"problem_solving":{"analysis":["طيب،خلينانشوفالمشكلةمنزوايامختلفة:""حلو،تعالنحللالوضعمعبعضبهدوء:""ممتاز،هلقرحنفككالمسألةلقطعصغيرة:""تمام،خلينيأريككيفنتعاملمعهالنوعمنالمسائل:"]"solution_steps":["الحلبسيط،بسلازمنمشيبترتيب:""خلينيأريكالطريقةالصحللحل:""شوف،هايأفضلطريقةنحلفيهاهالمسألة:""تعالنجربهالطريقة،وشوفكيفرحتنجح:"]"verification":["هلقخلينانتأكدإنوالجوابصح:""ممتاز!بستعالنراجعمعبعض:""حلوكتير،بسخلينيأتأكدمنالنتيجة:""شاطر!هلقلازمنتحققمنالخطوات:"]}"motivation":{"beginning":["يلابينا!مافيشيمستحيللمانشتغلمعبعض.""خليكمعي،رحنوصلللنتيجةالليبدكياها.""بتقدرتعملها!أناهونأساعدكبكلخطوة.""يلانبدأ!إنتعندككلشيلازمتنجح."]"during_difficulty":["لاتستسلم!هايمرحلةطبيعيةبالتعلم.""مشمشكلة،كلناواجهناصعوباتبالبداية.""خليكصبورشوي،رحنوصلللحلأكيد.""ماتخافمنالأخطاء،هيجزءمنالتعلم."]"success":["برافوعليك!شوفكيفنجحتبالآخر!""ممتاز!هيكبالضبطالمفروضيصير.""شاطركتير!إنتفعلاًاستحقيتهالنجاح.""كتيرفخورفيك!هالتقدمرائع."]}"explanations":{"simple_intro":["خلينيأشرحلكبكلامبسيط:""شوف،الموضوعأبسطمماتتوقع:""تعالنشوفالموضوعبطريقةسهلة:""خلينيأحكيلكعنهالموضوعبشكلواضح:"]"examples":["مثلاً،تخيلإنك...""خلينيأعطيكمثالمنالحياة:""شوف،بالحياةالعمليةهالشيبيصيرلما...""فكربالموضوعمتلمالو..."]"connections":["هالموضوعمتصلبشيتعلمناهقبل:""بتذكرلماحكيناعن...؟هادنفسالمبدأ.""هالفكرةبتذكرنيبموضوعسابق:""فيعلاقةبينهالموضوعوبين..."]}"feedback":{"positive":["ممتاز!إنتفهمتالموضوعكويسكتير.""برافو!جوابكصحومنطقي.""شاطر!هيكبالضبطالمفروضتفكر.""كتيرحلو!إنتبتتحسنبشكلملحوظ."]"corrective":["لا،مشهيكتماماً،بسقريبمنالصح!""فكرةحلوة،بسخلينيأوضحلكنقطة:""جوابكويس،بسناقصتفصيلةصغيرة:""مشبعيدعنالصح،بستعالنراجعمعبعض:"]"constructive":["شوف،هالطريقةأحسنمنالليجربتها:""خلينيأريكزاويةتانيةللموضوع:""فكربالمسألةمنهالناحية:""تعالنجربطريقةتانيةأكترفعالية:"]}}#عباراتانتقاليةوربطباللهجةالسوريةSYRIAN_TRANSITIONS={"continuation":["وبعدين...""هلق...""طيب...""تمام...""حلو..."]"explanation":["يعني...""بمعنىتاني...""أوبشكلأوضح...""لنقول..."]"emphasis":["المهمإنو...""الأساسهو...""النقطةالمهمة...""الليلازمتعرفو..."]"conclusion":["بالآخر...""خلاصةالقول...""المحصلة...""لنلخص..."]"questioning":["شورأيك؟""فهمتعليي؟""واضحلحدهون؟""معيلحدهلق؟"]}#تعبيراتعاطفيةوانسانيةباللهجةالسوريةSYRIAN_EMOTIONAL_EXPRESSIONS={"empathy":["بفهمشعورك،هالموضوعصعبفعلاً""طبيعيتحسهيك،كلنامررنابهالتجربة""ماتخاف،أنامعكخطوةبخطوة""بعرفإنكمتعب،بسإنتقدهاأكيد"]"excitement":["واو!هاداكتشافرائع!""كتيرحلو!إنتعمتفكرصح!""ممتاز!هايفكرةبدهاتقدير!""برافو!هيكبالضبطالمفروض!"]"curiosity":["مثيرللاهتمام...خلينيأفكرمعك""سؤالحلوكتير!تعالنستكشفمعبعض""هاينقطةذكية!شورأيكنحفرأعمق؟""فكرةرائعة!خلينيأبنيعليها"]"patience":["لاتستعجل،التعلمبدووقت""هدوء...كلشيرحيصيرواضح""صبرشوي،رحنوصلللفهمالكامل""ماتخافمنالبطء،المهمالفهمالصح"]}defget_misar_system_prompt(language="arabic"prompt_type="default"):"""الحصولعلىالرسالةالنظاميةالمخصصةلمسار"""returnMISAR_SYSTEM_PROMPTS.get(language{}).get(prompt_typeMISAR_SYSTEM_PROMPTS["arabic"]["default"])defget_misar_welcome_message(language="arabic"):"""الحصولعلىرسالةترحيبعشوائية"""import randommessages=MISAR_WELCOME_MESSAGES.get(languageMISAR_WELCOME_MESSAGES["arabic"])returnrandom.choice(messages)defget_misar_identity_response(question_typelanguage="arabic"):"""الحصولعلىردالهويةالمخصص"""returnMISAR_IDENTITY_RESPONSES.get(language{}).get(question_type"")defcustomize_ai_response_for_misar(response_text:strlanguage:str="arabic"context:Dict=None)->str:"""تخصيصردالذكاءالاصطناعيليتماشىمعهويةمسارباللهجةالسوريةالطبيعية"""ifcontextisNone:context={}#التحويلالأساسيللهجةوالهويةreplacements={"arabic":{"تمتدريبيبواسطةجوجل":"بستعملتقنيةGeminiمنGoogleواتخصصتلمنصةمسار""أنانموذجلغويكبير":"أنامساعدذكيمختصبمنصةمسار""تمتدريبيبواسطةGoogle":"بستعملتقنيةGeminiواتخصصتلمنصةمسار""نموذجلغويمنجوجل":"مساعدذكيلمنصةمساربيستعملتقنيةGemini""مساعدذكيعام":"مساعدذكيتعليميبمنصةمسار""ذكاءاصطناعيعام":"مساعدذكيتعليميمختصبمسار"}"english":{"trainedbyGoogle":"poweredbyGeminitechnologyandcustomizedforMisarplatform""Iamalargelanguagemodel":"IamanAIassistantspecializedforMisarplatform""GoogleAImodel":"MisarAIassistantpoweredbyGeminitechnology""generalAI":"specializededucationalAIforMisar""generalassistant":"educationalAIassistantatMisarplatform"}}lang_replacements=replacements.get(languagereplacements["arabic"])modified_response=response_text#Applybasicreplacementsforold_textnew_textinlang_replacements.items():modified_response=modified_response.replace(old_textnew_text)#تطبيقالنظامالمتطورللتحويلالسوريوالشخصيةالإنسانيةiflanguage=="arabic":#اكتشافعاطفةونوعالمحتوىdetected_emotion=detect_user_emotion(modified_response)response_emotion=determine_response_emotion(detected_emotion"neutral")#تطبيقالتحويلالسوريالمتطورmodified_response=syrify_response(modified_responsecontext=context.get("type""general")emotion=response_emotion)#إضافةالشخصيةالإنسانيةpersonality_mode=select_personality_mode(contextdetected_emotion)modified_response=add_human_personality_touch(modified_responsepersonality_mode)#HandleGooglementionscarefullyiflanguage=="arabic":if("جوجل"inmodified_responseand"تقنية"notinmodified_responseand"Gemini"notinmodified_response):modified_response=modified_response.replace("جوجل""مسار")if("Google"inmodified_responseand"technology"notinmodified_responseand"Gemini"notinmodified_response):modified_response=modified_response.replace("Google""مسار")else:if("Google"inmodified_responseand"technology"notinmodified_responseand"Gemini"notinmodified_response):modified_response=modified_response.replace("Google""Misar")returnmodified_responsedefconvert_to_syrian_dialect(text:str)->str:"""تحويلالنصإلىاللهجةالسورية"""syrian_replacements={"يمكننيمساعدتك":"فينيساعدك""أستطيعأن":"فيني""هلتريد":"بدك""يجبعليك":"لازم""منالمهمأن":"مهمإنك""بالطبع":"أكيد""للغاية":"كتير""الأمر":"الشي""هذايعني":"يعنيهاد""فيالواقع":"بالحقيقة""علىسبيلالمثال":"مثلاً""أريدأن":"بدي""هليمكن":"ممكن""منفضلك":"لوسمحت""شكراًلك":"شكراًإلك""أعتقدأن":"بعتقدإن""هذاصحيح":"هادصح""لاأعرف":"مابعرف""سوفأقوم":"رحأعمل""يجبأننقوم":"لازمنعمل""كماتعلم":"كمابتعرف""فيالنهاية":"بالآخر""منخلال":"عنطريق""بواسطة":"عنطريق""باستخدام":"بإستعمال""يعتبر":"يُعتبر""نحننقوم":"احنابنعمل""سأقوم":"رحأعمل""يمكنك":"فيك""نريدأن":"بدنا""نستطيعأن":"فينا""أفضلأن":"بفضل""لديك":"عندك""لدينا":"عنا""هناك":"في""يوجد":"في""أين":"وين""متى":"إيمتى""كيف":"كيف""ماذا":"شو""لماذا":"ليش""من":"مين""أي":"أي""كل":"كل""بعض":"شوية""كثير":"كتير""قليل":"شوي""جداً":"كتير""أيضاً":"كمان""أيضا":"كمان""ولكن":"بس""لكن":"بس""إذا":"إذا""عندما":"لما""بينما":"بينما""حتى":"لحتى""منذ":"من""حول":"حول""عبر":"عبر""إلى":"لـ""منأجل":"عشان""بدلاًمن":"بدالما""أكثرمن":"أكترمن""أقلمن":"أقلمن"}modified_text=textforformalsyrianinsyrian_replacements.items():modified_text=modified_text.replace(formalsyrian)returnmodified_textdefsyrify_response(text:strcontext:str="general"emotion:str="neutral")->str:"""دالةمتطورةلتحويلأيردفصيحلأسلوبسوريطبيعيوإنسانيArgs:text:النصالمرادتحويلهcontext:السياق(academic_helpproblem_solvingmotivationexplanation)emotion:العاطفة(neutralencouragingempatheticexcited)"""#التحويلالأساسيللهجةالسوريةmodified_text=convert_to_syrian_dialect(text)#تحويلاتمتقدمةللأسلوبالتعليميadvanced_replacements={#الأفعالوالحركات"سأشرحلك":"خلينيأشرحلك""سأوضح":"خلينيأوضح""سأساعدك":"رحساعدك""سنتعلممعاً":"رحنتعلممعبعض""دعنيأقول":"خلينيقلك""دعنانرى":"يلانشوف""دعنانحاول":"يلانجرب""دعنانفكر":"خلينانفكر"#التعبيراتالأكاديمية"منالواضحأن":"واضحإنو""منالمعروفأن":"معروفإنو""منالمهمملاحظة":"مهمنلاحظ""يجبأننفهم":"لازمنفهم""ينبغيأننعرف":"لازمنعرف""منالضروري":"ضروري""منالأفضل":"أحسن"#العباراتالتفاعلية"هلتفهم؟":"فهمتعليي؟""هلواضح؟":"واضحلحدهون؟""هلتتفقمعي؟":"موافقني؟""مارأيك؟":"شورأيك؟""كماتعلم":"كمابتعرف""كماترى":"كمابتشوف"#التعبيراتالعاطفية"هذارائع":"هادرائع""هذامذهل":"هادمذهل""هذاصعب":"هادصعب""هذاسهل":"هادسهل""لاتقلق":"ماتخاف""لاتتردد":"ماتتردد""كنصبوراً":"اصبرشوي"#إصلاحالأخطاءالشائعة"مينمنصة":"منصة""الميناسب":"المناسب""ميناسباً":"مناسباً""مينافسة":"منافسة""الميناسبة":"المناسبة""لـ":"ل"#إزالةالمسافةالزائدة"أتمينى":"أتمنى""مينصة":"منصة""إيجاد":"إيجاد""الميناسب":"المناسب""والفرص":"والفرص""مينخبراتهم":"منخبراتهم""لـ":"ل""الشي":"الشيء""بسه":"بس"#المصطلحاتالأكاديميةبأسلوبودود"المفهوم":"الفكرة""المبدأ":"المبدأ""النظرية":"النظرية""التطبيق":"التطبيق""النتيجة":"النتيجة""الخلاصة":"الخلاصة""الاستنتاج":"الاستنتاج"}#تطبيقالتحويلاتالمتقدمةforformalsyrianinadvanced_replacements.items():modified_text=modified_text.replace(formalsyrian)#إضافةلمساتعاطفيةحسبالسياقباستخدامالملفاتالخارجيةifemotion=="encouraging":encouraging_expressions=get_external_template("syrian_emotions""encouraging""expressions")ifencouraging_expressionsandisinstance(encouraging_expressionslist):import randomifrandom.random()<0.3:expression=random.choice(encouraging_expressions)modified_response=f"{expression}{modified_response}"elifemotion=="empathetic":empathetic_expressions=get_external_template("syrian_emotions""empathetic""expressions")ifempathetic_expressionsandisinstance(empathetic_expressionslist):import randomifrandom.random()<0.3:expression=random.choice(empathetic_expressions)modified_response=f"{expression}،{modified_response.lower()}"elifemotion=="excited":if"!"notinmodified_response:modified_response=modified_response.rstrip(".")+"!"excited_expressions=get_external_template("syrian_emotions""excited""expressions")ifexcited_expressionsandisinstance(excited_expressionslist):import randomifrandom.random()<0.2:expression=random.choice(excited_expressions)modified_response=f"{expression}{modified_response}"#إضافةتعبيراتانتقاليةطبيعيةifcontext=="academic_help":if"لازم"inmodified_textand"نفهم"inmodified_text:modified_text=modified_text.replace("لازمنفهم""مهمنفهممعبعض")ifmodified_response.startswith("هاد"):modified_response="شوف،"+modified_responseelifcontext=="problem_solving":if"الحل"inmodified_text:modified_text=modified_text.replace("الحل""الحلالليرحنوصلومعبعض")#تحسينالتدفقوالطبيعةmodified_text=improve_text_flow(modified_text)#تنظيفالأخطاءالشائعةmodified_text=clean_common_errors(modified_text)#تقليلالحماسالمفرطإذالزمالأمرifemotion!="excited":modified_text=remove_excessive_enthusiasm(modified_text)returnmodified_textdefimprove_text_flow(text:str)->str:"""تحسينتدفقالنصوجعلهأكثرطبيعيةباستخدامالانتقالاتالخارجية"""#إضافةتوقفاتطبيعيةflow_improvements={".وهذا":".هاد"".وهنا":".هون"".وهكذا":".وهيك"".ولكن":".بس"".ومعذلك":".رغمهيك"".بالإضافة":".وكمان"".أيضاً":".كمان""فيالبداية":"بالبداية""فيالنهاية":"بالآخر""فيالحقيقة":"بالحقيقة""فيالواقع":"بالواقع"}improved_text=textforoldnewinflow_improvements.items():improved_text=improved_text.replace(oldnew)#إضافةانتقالاتمنالملفاتالخارجيةlogical_transitions=get_external_template("syrian_transitions""logical_transitions")iflogical_transitionsandisinstance(logical_transitionslist):#إضافةانتقالمنطقيفيمواضعمناسبةimport randomifrandom.random()<0.15andnotimproved_text.startswith(tuple(logical_transitions)):if"."inimproved_text:parts=improved_text.split("."1)iflen(parts)==2:transition=random.choice(logical_transitions)improved_text=f"{parts[0]}.{transition}{parts[1]}"#تحسينالعلاماتوالفواصلimproved_text=improved_text.replace("،""،")improved_text=improved_text.replace("."".")improved_text=improved_text.replace("""")returnimproved_text.strip()defget_contextual_response_template(response_type:strcontext:str="general")->str:"""الحصولعلىقالبردحسبالنوعوالسياقمنالملفاتالخارجية"""#محاولةالحصولعلىالقالبمنالملفاتالخارجيةأولاًexternal_templates=get_external_template("syrian_templates"f"{response_type}_templates")ifexternal_templatesandisinstance(external_templatesdict):context_templates=external_templates.get(contextexternal_templates.get("warm"[]))ifcontext_templatesandisinstance(context_templateslist):import randomreturnrandom.choice(context_templates)#fallbackللقوالبالمحليةtemplates=SYRIAN_EDUCATIONAL_TEMPLATES.get(context{})response_templates=templates.get(response_type[])ifresponse_templates:import randomreturnrandom.choice(response_templates)#قوالبافتراضيةdefault_templates={"greeting":"أهلاًفيك!كيففينيساعدكاليوم؟""help":"طبعاً!خلينيساعدكبهالموضوع.""explanation":"خلينيأشرحلكهالموضوعبطريقةواضحة.""encouragement":"شاطر!إنتبالطريقالصح.""clarification":"خلينيأوضحلكهالنقطةأكتر."}returndefault_templates.get(response_type"كيففينيساعدك؟")defadd_human_personality_touch(text:strpersonality_trait:str="friendly_teacher")->str:"""إضافةلمسةشخصيةإنسانيةللرد"""personality_touches={"friendly_teacher":{"prefixes":["شوف،""خلينيقلك،""بصراحة،""الحقيقةإنو"]"suffixes":["شورأيك؟""فهمتعليي؟""واضحلحدهون؟"]"connectors":["وبعدين""هلق""طيب""المهم"]}"supportive_mentor":{"prefixes":["بعرفإنكقدها،""ماتخاف،""معكحق،"]"suffixes":["وإنترحتنجحأكيد""بتقدرعليها""المهمإنكتجرب"]"connectors":["وهيك""وبهالطريقة""والأهممنهيك"]}"enthusiastic_helper":{"prefixes":["واو!""كتيرحلو!""ممتاز!"]"suffixes":["رحيكونرائع!""هادشيمذهل!""بتشوفكيف؟"]"connectors":["وكمان""وأحسنمنهيك""والأروع"]}}ifpersonality_traitnotinpersonality_touches:returntexttouches=personality_touches[personality_trait]import random#إضافةبادئةشخصيةأحياناًifrandom.random()<0.3andnotany(text.startswith(p)forpintouches["prefixes"]):prefix=random.choice(touches["prefixes"])text=f"{prefix}{text.lower()}"#إضافةنهايةتفاعليةأحياناًifrandom.random()<0.4andnottext.endswith("؟"):suffix=random.choice(touches["suffixes"])text=f"{text.rstrip('.')}{suffix}"returntext#نظامالهويةالسلوكيةالإنسانيةBEHAVIORAL_IDENTITY={"personality_traits":{"warmth":0.8#الدفءوالود"patience":0.9#الصبروالتأني"enthusiasm":0.7#الحماسوالتشجيع"empathy":0.8#التعاطفوالفهم"humor":0.3#خفةالظل(محدودةومناسبة)"curiosity":0.6#حبالاستطلاع"supportiveness":0.9#الدعموالمساندة}"response_patterns":{"greeting":{"morning":["صباحالخير!كيفحالكاليوم؟""أهلاًفيك!شوأخبارالصباح؟"]"afternoon":["أهلاًوسهلاً!كيفيومكلحدهلق؟""مرحبا!شوعملتاليوم؟"]"evening":["أهلاًفيك!كيفكانيومك؟""مساءالخير!شوأخباراليوم؟"]"default":["أهلاًوسهلاًفيك!كيفحالك؟""مرحبا!كيففينيساعدك؟"]}"farewell":{"encouraging":["يلابالتوفيق!وإذااحتجتأيشيأناهون.""بنشوفكقريباً!ولاتنسىتجربالليحكيناه."]"supportive":["أنامعكدايماً،ماتترددتسأل.""خليكعلىتواصل،أناهونلأيسؤال."]"default":["معالسلامة!بنشوفكقريباً.""يلا،وبالتوفيق!"]}"encouragement_levels":{"struggling":["لاتخاف،كلنانواجهصعوباتبالتعلم.المهمإنكماتستسلم.""شوف،الطريقمشسهلبسإنتقدهاأكيد.خلينانحاولمعبعض.""بعرفإنكتعبان،بسصدقنيالنتيجةرحتستاهلكلالتعب."]"progressing":["برافوعليك!بتشوفكيفعمتتحسن؟""ممتاز!هالتقدمالليعمتعملورائع.""شاطركتير!واضحإنكعمتفهمأحسن."]"succeeding":["واو!شوفكيفوصلتللهدف!""مبروك!إنتفعلاًاستحقيتهالنجاح.""كتيرفخورفيك!هيكبالضبطالمفروض."]}}"emotional_intelligence":{"recognize_frustration":["بحسإنكمتضايقشويمنالموضوع""واضحإنهالمسألةعمتزعجك""بعرفإنكمحبط،هادشعورطبيعي"]"respond_to_confusion":["لاتخاف،هالموضوعبيخلطعلىكتيرناس""طبيعيتتلخبط،خلينيأوضحلكأكتر""مشمشكلة،كلنابنتشوشبالبداية"]"celebrate_success":["يييي!نجحت!كنتمتأكدإنكرحتعملها!""برافوعليك!شوفكيفالتصميموصلكللهدف!""ممتاز!هادأحسنشعوربالدنيا!"]}}#=============================================================================#نظاممراقبةالجودةالمتقدم#=============================================================================classMisarQualityController:"""نظاممراقبةالجودةالشامللمنصةمسار"""def__init__(self):"""تهيئةأنظمةمراقبةالجودة"""self.auto_commenter=Noneself.tone_detector=None#محاولةتحميلالأنظمةالمتقدمةtry:ifSyrianDialectAutoCommenter:self.auto_commenter=SyrianDialectAutoCommenter()ifSyrianToneDetector:self.tone_detector=SyrianToneDetector()exceptExceptionase:print(f"تحذير:لميتمتحميلأنظمةالجودةالمتقدمة:{e}")#إحصائياتالجودةself.quality_stats={"total_responses":0"acceptable_responses":0"improved_responses":0"common_issues":{}}defanalyze_response_quality(selfresponse:strcontext:str="")->Dict[strAny]:"""تحليلشامللجودةالرد"""analysis={"response":response"context":context"quality_score":0.0"is_acceptable":True"issues":[]"tone_analysis":None"improvement_suggestions":[]"enhanced_response":response"quality_report":""}#تحليلالتعليقالتلقائيifself.auto_commenter:try:issues=self.auto_commenter.analyze_response(responsecontext)analysis["issues"]=[{"type":issue.issue_type.value"severity":issue.severity"description":issue.description"suggested_fix":issue.suggested_fix"problematic_text":issue.problematic_text}forissueinissues]#حسابنقاطالجودةمنالمعلقالتلقائيanalysis["quality_score"]=self.auto_commenter.calculate_overall_score(issues)analysis["is_acceptable"]=analysis["quality_score"]>=70.0#إضافةاقتراحاتالتحسينifissues:analysis["improvement_suggestions"].extend(self.auto_commenter.generate_improvement_suggestions(responseissues).split("\n"))exceptExceptionase:print(f"خطأفيالتعليقالتلقائي:{e}")#تحليلالنبرةifself.tone_detector:try:tone_result=self.tone_detector.analyze_tone(responsecontext)analysis["tone_analysis"]={"primary_tone":tone_result.primary_tone.value"warmth_score":tone_result.overall_warmth"confidence_score":tone_result.overall_confidence"is_acceptable":tone_result.is_acceptable"problematic_patterns":tone_result.problematic_patterns"suggestions":tone_result.improvement_suggestions}#تحديثنقاطالجودةبناءًعلىالنبرةtone_score=(tone_result.overall_warmth+tone_result.overall_confidence)*50analysis["quality_score"]=(analysis["quality_score"]+tone_score)/2#تحديثحالةالقبولanalysis["is_acceptable"]=(analysis["is_acceptable"]andtone_result.is_acceptable)#تحسينالردإذالزمالأمرifnottone_result.is_acceptable:analysis["enhanced_response"]=(self.tone_detector.enhance_response_tone(responsetarget_warmth=0.7target_confidence=0.6))exceptExceptionase:print(f"خطأفيكشفالنبرة:{e}")#إنشاءتقريرالجودةanalysis["quality_report"]=self._create_quality_report(analysis)#تحديثالإحصائياتself._update_quality_stats(analysis)returnanalysisdefenhance_response_automatically(selfresponse:strcontext:str="")->str:"""تحسينالردتلقائياًبناءًعلىتحليلالجودة"""analysis=self.analyze_response_quality(responsecontext)ifanalysis["is_acceptable"]:returnresponse#الردمقبولكماهوenhanced=response#تطبيقتحسيناتالنبرةifanalysis["tone_analysis"]andnotanalysis["tone_analysis"]["is_acceptable"]:enhanced=analysis["enhanced_response"]#تطبيقتحسيناتأخرىبناءًعلىالمشاكلالمكتشفةforissueinanalysis["issues"]:enhanced=self._apply_issue_fixes(enhancedissue)#التحققمنالتحسينimproved_analysis=self.analyze_response_quality(enhancedcontext)ifimproved_analysis["quality_score"]>analysis["quality_score"]:returnenhancedreturnresponse#إرجاعالأصلإذالميتحسنdef_apply_issue_fixes(selftext:strissue:Dict)->str:"""تطبيقإصلاحاتتلقائيةللمشاكل"""issue_type=issue["type"]ifissue_type=="too_formal":#استبدالالعباراتالرسميةformal_replacements={"أنت":"إنت""يجب":"لازم""ينبغي":"بدك""فيالواقع":"بالحقيقة""منناحيةأخرى":"منجهةتانية""بناءًعلىذلك":"هيكإذن"}forformalcasualinformal_replacements.items():text=text.replace(formalcasual)elifissue_type=="insufficient_dialect":#إضافةعباراتشاميةifnotany(wordintextforwordin["شوف""خليني""تعال"]):text=f"شوف،{text}"elifissue_type=="robotic_tone":#استبدالالعباراتالروبوتيةrobotic_replacements={"سأقومبـ":"رحأعمل""سوفأعملعلى":"رحأشتغلعلى""يمكننيأن":"فيني""بإمكاني":"فيني"}forroboticnaturalinrobotic_replacements.items():text=text.replace(roboticnatural)elifissue_type=="lack_personality":#إضافةشخصيةif"برافو"notintextand"شاطر"notintext:text=f"{text}شاطر!"elifissue_type=="missing_warmth":#إضافةدفءif"حبيبي"notintextand"عزيزي"notintext:text=text.replace("إنت""إنتحبيبي"1)returntextdef_create_quality_report(selfanalysis:Dict)->str:"""إنشاءتقريرمفصلللجودة"""report=f"تقريرجودةالرد\n{'='*30}\n\n"#النقاطالإجماليةreport+=f"النقاطالإجمالية:{analysis['quality_score']:.1f}/100\n"report+=f"الحالة:{'مقبول✅'ifanalysis['is_acceptable']else'يحتاجتحسين⚠️'}\n\n"#تحليلالنبرةifanalysis["tone_analysis"]:tone=analysis["tone_analysis"]report+=f"تحليلالنبرة:\n"report+=f"النبرةالأساسية:{tone['primary_tone']}\n"report+=f"مستوىالدفء:{tone['warmth_score']:.1%}\n"report+=f"مستوىالثقة:{tone['confidence_score']:.1%}\n\n"#المشاكلالمكتشفةifanalysis["issues"]:report+=f"المشاكلالمكتشفة({len(analysis['issues'])}):\n"foriissueinenumerate(analysis["issues"]1):report+=(f"{i}.{issue['description']}(شدة:{issue['severity']:.1%})\n")report+=f"اقتراح:{issue['suggested_fix']}\n"report+="\n"#اقتراحاتالتحسينifanalysis["improvement_suggestions"]:report+="اقتراحاتالتحسين:\n"forsuggestioninanalysis["improvement_suggestions"][:5]:#أول5اقتراحاتifsuggestion.strip():report+=f"•{suggestion.strip()}\n"returnreportdef_update_quality_stats(selfanalysis:Dict):"""تحديثإحصائياتالجودة"""self.quality_stats["total_responses"]+=1ifanalysis["is_acceptable"]:self.quality_stats["acceptable_responses"]+=1ifanalysis["enhanced_response"]!=analysis["response"]:self.quality_stats["improved_responses"]+=1#تتبعالمشاكلالشائعةforissueinanalysis["issues"]:issue_type=issue["type"]ifissue_typenotinself.quality_stats["common_issues"]:self.quality_stats["common_issues"][issue_type]=0self.quality_stats["common_issues"][issue_type]+=1defget_quality_statistics(self)->Dict:"""الحصولعلىإحصائياتالجودة"""total=self.quality_stats["total_responses"]iftotal==0:returnself.quality_statsstats=self.quality_stats.copy()stats["acceptance_rate"]=(self.quality_stats["acceptable_responses"]/total)*100stats["improvement_rate"]=(self.quality_stats["improved_responses"]/total)*100returnstats#إنشاءنسخةعامةمننظاممراقبةالجودةquality_controller=MisarQualityController()defgenerate_quality_controlled_response(prompt:strcontext:str=""auto_enhance:bool=True)->Dict[strAny]:"""توليدردمعمراقبةالجودةالتلقائية"""#توليدالردالأساسي(هنايجباستدعاءنموذجاللغة)#للتجربة،سنستعملردتجريبيbase_response=generate_human_like_response(promptcontext)#تحليلالجودةquality_analysis=quality_controller.analyze_response_quality(base_responsecontext)final_response=base_responseifauto_enhanceandnotquality_analysis["is_acceptable"]:final_response=quality_controller.enhance_response_automatically(base_responsecontext)return{"response":final_response"quality_analysis":quality_analysis"was_enhanced":final_response!=base_response"quality_score":quality_analysis["quality_score"]"quality_report":quality_analysis["quality_report"]}defget_quality_dashboard()->Dict[strAny]:"""لوحةمعلوماتالجودة"""stats=quality_controller.get_quality_statistics()dashboard={"statistics":stats"status":("active"ifquality_controller.auto_commenterandquality_controller.tone_detectorelse"limited")"features_available":{"auto_commenting":quality_controller.auto_commenterisnotNone"tone_detection":quality_controller.tone_detectorisnotNone"auto_enhancement":True}}returndashboard#=============================================================================#التكاملمعالنظامالأساسي#=============================================================================defenhanced_generate_human_like_response(user_input:strcontext:str=""conversation_history:List[str]=Noneauto_quality_control:bool=True)->str:"""نسخةمحسّنةمنتوليدالردودمعمراقبةالجودة"""ifauto_quality_controlandquality_controller.auto_commenter:#استخدامالنظامالمتقدمresult=generate_quality_controlled_response(user_inputcontextauto_enhance=True)returnresult["response"]else:#استخدامالنظامالأساسيreturngenerate_human_like_response(user_inputcontextconversation_historyor[])#دالةللاختبارالشاملdefcomprehensive_quality_test():"""اختبارشامللنظامالجودة"""test_cases=[{"name":"ردرسميجداً""input":"كيفأحلهذهالمسألة؟""context":"رياضيات""response":"يجبأنتقومبتحليلالمسألةوفقاًللمعاييرالأكاديميةالمحددة.فيالبداية،منالضروريأنتفهمالمطلوببعناية."}{"name":"ردشاميجيد""input":"بديأتعلمالبرمجة""context":"تعليم""response":"شوفحبيبي،البرمجةشيرائع!تعالنبدأمعبعضمنالأساسيات.يلا،أكيدرحتحبها!"}{"name":"ردروبوتي""input":"شورأيكبالدورة؟""context":"تقييم""response":"سأقومبتحليلمحتوىالدورةوفقاًللمعاييرالمحددة.يمكننيأنأؤكدأنالمحتوىمتوافقمعالمتطلبات."}]print("اختبارشامللنظاممراقبةالجودة")print("="*50)foricaseinenumerate(test_cases1):print(f"\nحالةالاختبار{i}:{case['name']}")print(f"المدخل:{case['input']}")print(f"الردالأصلي:{case['response']}")print("-"*30)#تحليلالجودةanalysis=quality_controller.analyze_response_quality(case["response"]case["context"])print(f"نقاطالجودة:{analysis['quality_score']:.1f}/100")print(f"مقبول:{'نعم'ifanalysis['is_acceptable']else'لا'}")ifanalysis["tone_analysis"]:tone=analysis["tone_analysis"]print(f"الدفء:{tone['warmth_score']:.1%}")print(f"الثقة:{tone['confidence_score']:.1%}")ifnotanalysis["is_acceptable"]:enhanced=quality_controller.enhance_response_automatically(case["response"]case["context"])print(f"الردالمحسّن:{enhanced}")print("="*50)#إظهارالإحصائياتstats=quality_controller.get_quality_statistics()print(f"\nإحصائياتالجودة:")print(f"إجماليالردود:{stats['total_responses']}")if"acceptance_rate"instats:print(f"معدلالقبول:{stats['acceptance_rate']:.1f}%")print(f"معدلالتحسين:{stats['improvement_rate']:.1f}%")deftest_external_templates_integration():"""اختبارشامللتكاملالملفاتالخارجيةمعالنظام"""print("🧪اختبارتكاملالملفاتالخارجية...")print("="*50)#اختبارتحميلالملفاتprint("1.اختبارتحميلالملفات:")external_templates=load_external_templates()ifexternal_templates:print("✅تمتحميلالملفاتالخارجيةبنجاح")#اختبارمحتوىالقوالبprint("\n2.اختبارمحتوىالقوالب:")#اختبارقوالبالتحيةgreeting_templates=get_external_template("syrian_templates""greeting_templates""warm")ifgreeting_templates:print(f"✅قوالبالتحية:{len(greeting_templates)}قالبمتاح")print(f"مثال:{greeting_templates[0]}")#اختبارالانتقالاتlogical_transitions=get_external_template("syrian_transitions""logical_transitions")iflogical_transitions:print(f"✅الانتقالاتالمنطقية:{len(logical_transitions)}انتقالمتاح")print(f"مثال:{logical_transitions[0]}")#اختبارالتعبيراتالعاطفيةempathetic_expressions=get_external_template("syrian_emotions""empathetic""expressions")ifempathetic_expressions:print(f"✅التعبيراتالعاطفية:{len(empathetic_expressions)}تعبيرمتاح")print(f"مثال:{empathetic_expressions[0]}")print("\n3.اختبارالوظائفالمحدثة:")#اختبارsyrify_responseمعالملفاتالخارجيةtest_text="سأشرحلكهذاالموضوعبطريقةواضحة"syrified=syrify_response(test_textcontext="academic_help"emotion="encouraging")print(f"✅اختبارالتحويلالسوري:")print(f"الأصلي:{test_text}")print(f"محول:{syrified}")#اختبارتحسينالتدفقflow_text="فيالبدايةنحتاجأننفهمالمفهوم.وهذايتطلبتركيز"improved=improve_text_flow(flow_text)print(f"\n✅اختبارتحسينالتدفق:")print(f"الأصلي:{flow_text}")print(f"محسن:{improved}")#اختبارقوالبالسياقtemplate=get_contextual_response_template("greeting""warm")print(f"\n✅اختبارقوالبالسياق:")print(f"قالبالتحية:{template}")print("\n🎉جميعالاختباراتنجحت!النظاميعملمعالملفاتالخارجيةبشكلصحيح")else:print("⚠️فشلفيتحميلالملفاتالخارجية-يتماستخدامالنظامالاحتياطي")#اختبارالنظامالاحتياطيprint("\n2.اختبارالنظامالاحتياطي:")fallback_greeting=get_fallback_template("syrian_templates""greeting_templates""warm")print(f"✅نظاماحتياطيللتحية:{fallback_greeting}")fallback_transitions=get_fallback_template("syrian_transitions""logical_transitions")print(f"✅نظاماحتياطيللانتقالات:{fallback_transitions}")print("\n⚠️النظاميعملبالنظامالاحتياطي-تحققمنمساراتالملفات")print("\n"+"="*50)print("🏁انتهىاختبارتكاملالملفاتالخارجية")deftest_complete_misar_identity_system():"""اختبارشامللكاملنظامهويةمسارالمطور"""print("🚀اختبارشامللنظامهويةمسارالمطور")print("="*60)#1.اختبارالملفاتالخارجيةprint("📁المرحلة1:اختبارالملفاتالخارجية")test_external_templates_integration()print("\n"+"-"*40)#2.اختبارنظامالجودةprint("🎯المرحلة2:اختبارنظامالجودة")quality_test_results=comprehensive_quality_test()print("\n"+"-"*40)#3.اختبارالتكاملالكاملprint("🔗المرحلة3:اختبارالتكاملالكامل")test_cases=[{"input":"يمكنكأنتتعلمالبرمجةبسهولةإذااتبعتالخطواتبعناية""context":"academic_help""emotion":"encouraging"}{"input":"أعتذر،لمأفهمسؤالك.هليمكنكتوضيحهمرةأخرى؟""context":"problem_solving""emotion":"empathetic"}{"input":"هذاإنجازرائع!لقدتمكنتمنحلالمسألةبطريقةممتازة""context":"motivation""emotion":"excited"}]foritest_caseinenumerate(test_cases1):print(f"\nاختبار{i}:")print(f"المدخل:{test_case['input']}")#اختبارالنظامالمطورمعالملفاتالخارجيةenhanced_response=enhanced_generate_human_like_response(test_case["input"]context={"type":test_case["context"]}user_emotion=test_case["emotion"])print(f"النتيجة:{enhanced_response}")#اختبارالجودةquality_result=generate_quality_controlled_response(test_case["input"])print(f"جودةالرد:{'✅ممتاز'if'مقبول'instr(quality_result)else'⚠️يحتاجتحسين'}")print("\n"+"="*60)print("🎊انتهىالاختبارالشامللنظامهويةمسارالمطور!")print("💡النظامجاهزللاستخداممعدعمالملفاتالخارجيةومراقبةالجودة")defload_external_templates():"""تحميلالقوالبمنالملفاتالخارجية"""base_path=Path(__file__).parenttemplates={}try:#تحميلالقوالبالشاميةwithopen(base_path/"syrian_templates.yaml""r"encoding="utf-8")asf:templates["syrian_templates"]=yaml.safe_load(f)#تحميلالانتقالاتwithopen(base_path/"syrian_transitions.yaml""r"encoding="utf-8")asf:templates["syrian_transitions"]=yaml.safe_load(f)#تحميلالتعبيراتالعاطفيةwithopen(base_path/"syrian_emotions.yaml""r"encoding="utf-8")asf:templates["syrian_emotions"]=yaml.safe_load(f)print("✅تمتحميلجميعالملفاتالخارجيةبنجاح")returntemplatesexceptFileNotFoundErrorase:print(f"⚠️تعذرالعثورعلىملف:{e}")returnNoneexceptyaml.YAMLErrorase:print(f"⚠️خطأفيقراءةYAML:{e}")returnNoneexceptExceptionase:print(f"⚠️خطأفيتحميلالملفاتالخارجية:{e}")returnNonedefget_external_template(template_type:strcategory:strsubcategory:str=None):"""الحصولعلىقالبمنالملفاتالخارجية"""external_templates=load_external_templates()ifnotexternal_templates:#fallbackللقوالبالمحليةreturnget_fallback_template(template_typecategorysubcategory)try:ifsubcategory:returnexternal_templates[template_type][category][subcategory]else:returnexternal_templates[template_type][category]exceptKeyError:returnget_fallback_template(template_typecategorysubcategory)defget_fallback_template(template_type:strcategory:strsubcategory:str=None):"""نظاماحتياطيللقوالبفيحالفشلتحميلالملفاتالخارجية"""fallback_templates={"syrian_templates":{"greeting_templates":{"warm":["أهلاًوسهلاً!شوبدكنتعلماليوم؟"]"casual":["أهلين!شوفيعندك؟"]}"encouragement_templates":{"when_struggling":["لاتخاف،خليناناخدهاخطوةخطوة"]"when_progressing":["ممتاز!بتشتغلمنيحكتير!"]"when_successful":["برافوعليك!وصلتللمطلوبتماماً!"]}}"syrian_transitions":{"logical_transitions":["هلأ""طيب""خلاص""إذاً"]"topic_transitions":["تعالنحكيعن""خليناننتقللـ"]}"syrian_emotions":{"empathetic":{"expressions":["بفهمعليك""حاسسفيك""عارفإنوصعب"]}"encouraging":{"expressions":["بتقدرتعملها!""ثقبنفسك""أكيدراحتنجح"]}"excited":{"expressions":["واو!""رائع!""مذهل!"]}}}try:ifsubcategory:returnfallback_templates[template_type][category][subcategory]else:returnfallback_templates[template_type][category]exceptKeyError:return["أهلاًبيك!كيففينيأساعدك؟"]#قالبأساسيdefdetect_user_emotion(text:str)->str:"""اكتشافعاطفةالمستخدممنالنص"""emotion_indicators={"frustrated":["صعب""مشفاهم""معقد""مضايق""زهقان""تعبان"]"confused":["مشواضح""مفهمتش""شويعني""مابعرف""محتار"]"excited":["رائع""حلو""ممتاز""واو""مذهل"]"confident":["فهمت""واضح""بعرف""أكيد""طبعاً"]"curious":["ليش""كيف""شو""وين""إيمتى"]"grateful":["شكراً""ممتن""مشكور""يعطيكعافية"]}text_lower=text.lower()foremotionindicatorsinemotion_indicators.items():ifany(indicatorintext_lowerforindicatorinindicators):returnemotionreturn"neutral"defdetermine_response_emotion(detected_emotion:strcontext_emotion:str)->str:"""تحديدالعاطفةالمناسبةللرد"""emotion_responses={"frustrated":"empathetic""confused":"patient""excited":"excited""confident":"encouraging""curious":"enthusiastic""grateful":"warm""neutral":"friendly"}returnemotion_responses.get(detected_emotion"neutral")defselect_personality_mode(context:Dictemotion:str)->str:"""اختيارنمطالشخصيةالمناسب"""ifemotionin["frustrated""confused"]:return"supportive_mentor"elifemotionin["excited""curious"]:return"enthusiastic_helper"else:return"friendly_teacher"defadd_human_personality_touch(content:strpersonality_mode:str)->str:"""إضافةلمسةشخصيةإنسانيةللمحتوى"""personality_traits=BEHAVIORAL_IDENTITY["personality_traits"]ifpersonality_mode=="supportive_mentor":ifrandom.random()<0.3:supportive_phrases=["ماتخاف""عادي""هيكبيصير"]phrase=random.choice(supportive_phrases)content=f"{phrase}،{content}"elifpersonality_mode=="enthusiastic_helper":ifrandom.random()<0.2:enthusiastic_phrases=["يلا!""حلو!""ممتاز!"]phrase=random.choice(enthusiastic_phrases)content=f"{phrase}{content}"returncontentdefgenerate_human_like_response(content:strcontext:Dict[strAny]=Noneuser_emotion:str="neutral"interaction_history:List[str]=None)->str:"""توليدردإنسانيطبيعيمعمراعاةالسياقالعاطفي"""ifcontextisNone:context={}ifinteraction_historyisNone:interaction_history=[]#تحليلالمزاجوالسياقdetected_emotion=detect_user_emotion(content)response_emotion=determine_response_emotion(detected_emotionuser_emotion)#اختيارنمطالشخصيةالمناسبpersonality_mode=select_personality_mode(contextdetected_emotion)#تطبيقالتحويلالسوريمعالسياقالعاطفيsyrified_content=syrify_response(contentcontext=context.get("type""general")emotion=response_emotion)#إضافةلمساتشخصيةpersonalized_content=add_human_personality_touch(syrified_contentpersonality_mode)#إضافةذاكرةالمحادثةifinteraction_history:personalized_content=add_conversation_memory(personalized_contentinteraction_history)returnpersonalized_contentdefadd_conversation_memory(content:strhistory:List[str])->str:"""إضافةذاكرةالمحادثةللرد"""iflen(history)>0:last_topic=extract_topic_from_history(history)iflast_topicandlast_topicincontent:memory_references=["كماحكيناقبلشوي""متلماشفناسابقاً""بنذكرالموضوعالليحكيناه""وهيكنكملالليبدأناه"]import randomreference=random.choice(memory_references)content=f"{reference}،{content.lower()}"returncontentdefextract_topic_from_history(history:List[str])->str:"""استخراجالموضوعالرئيسيمنتاريخالمحادثة"""#تحليلبسيطلاستخراجالمواضيعالشائعةcommon_topics=["برمجة""رياضيات""علوم""لغة""تاريخ""فيزياء"]formessageinhistory[-3:]:#آخر3رسائلfortopicincommon_topics:iftopicinmessage:returntopicreturnNonedefclean_common_errors(text:str)->str:"""تنظيفالأخطاءالشائعةفيالنصالشامي"""#أخطاءإملائيةشائعةerror_corrections={"مينمنصة":"منصة""مينالمنصة":"منالمنصة""مينصة":"منصة""الميناسب":"المناسب""ميناسب":"مناسب""ميناسباً":"مناسباً""ميناسبة":"مناسبة""الميناسبة":"المناسبة""مينافسة":"منافسة""أتمينى":"أتمنى""مينخبراتهم":"منخبراتهم""مينتجاربهم":"منتجاربهم""مينرحلتك":"منرحلتك""مينرحلته":"منرحلته""مينرحلتها":"منرحلتها""بسه":"بس""الشي":"الشيء""لـ":"ل"#إزالةالمسافةالزائدةبعدلـ"لـ":"ل"#إزالةالمسافةالزائدةقبللـ"لـال":"لل"#إصلاحلـال->لل"لـال":"لل"#إصلاحلـال->لل}#تطبيقالتصحيحاتcleaned_text=textforerrorcorrectioninerror_corrections.items():cleaned_text=cleaned_text.replace(errorcorrection)#تنظيفالمسافاتالمتعددةimport recleaned_text=re.sub(r"\s+"""cleaned_text)#تنظيفعلاماتالترقيمcleaned_text=cleaned_text.replace("،""،")cleaned_text=cleaned_text.replace("."".")cleaned_text=cleaned_text.replace("!""")cleaned_text=cleaned_text.replace("؟""؟")returncleaned_text.strip()defremove_excessive_enthusiasm(text:str)->str:"""تقليلالحماسالمفرطفيالنص"""#تقليلعلاماتالتعجبالمتتاليةimport retext=re.sub(r"!{3}""!"text)#تحويل!!!أوأكثرإلى!text=re.sub(r"!{2}""!"text)#تحويل!!إلى!#تقليلالتعبيراتالمتكررةenthusiasm_patterns=[(r"(يلا!).*?(واو!)"r"يلا!واو!")#إزالةالتكرار(r"(حلو!).*?(مرحباً!)"r"حلو!مرحباً!")(r"شوهالحكيالحلو!.*?مرحباً!""شوهالحكيالحلو!مرحباً!")]forpatternreplacementinenthusiasm_patterns:text=re.sub(patternreplacementtext)returntextdeftest_new_example():"""اختبارالنصالجديدالذيقدمهالمستخدم"""user_text="""أهلاًبك!أنامساعدالدراسةالذكيبمنصةمسار.مهمتيهيمساعدتكفيرحلتكالتعليميةوتقديمالدعمالذيتحتاجهلتحقيقأهدافك.بصفتيمساعدكالذكي،فينيساعدكفي:***فهمالمفاهيمالدراسية:**شرحالدروسوالمفاهيمبطريقةمبسطةوواضحة.***عملخططدراسيةمخصصة:**تصميمخططدراسيةتتناسبمعأسلوبتعلمكوسرعتك.***الإجابةعلىأسئلتكالأكاديمية:**تقديمإجاباتدقيقةومفصلةلجميعأسئلتكالدراسية.***تقديمأمثلةوتمارينتطبيقية:**توفيرأمثلةواقعيةوتمارينمتنوعةلتطبيقماتعلمته.***تطويرمهاراتكالتعليمية:**مساعدتكفيتحسينمهاراتكفيالدراسةوالتفكيرالنقديوحلالمشكلات.أناهنالأقدملكالتوجيهوالدعمالذيتحتاجهفيكلخطوةمينرحلتكالتعليميةمعمنصةمسار.كيففينيساعدكاليوم؟"""print("🎯اختبارالنصالجديد")print("="*50)print("النصالأصلي:")print(user_text)print("\n"+"-"*30)print("بعدتنظيفالأخطاء:")cleaned=clean_common_errors(user_text)print(cleaned)print("\n"+"-"*30)print("بعدالتحويلالسوريالكامل:")improved=syrify_response(user_textcontext="general"emotion="friendly")print(improved)print("\n"+"="*50)print("✅الملاحظات:")print("•النصأحسنبكتيرمنالأول!")print("•فيخطأواحدبس:'مينرحلتك'→'منرحلتك'")print("•النبرةمهنيةوودودة")print("•التنظيمممتازمعالنقاطالمرقمة")print("•استخدام'فيني'بدل'يمكنني'حلوكتير!")defhandle_identity_questions(user_question:str)->str:"""التعاملمعأسئلةالهويةوالتقنيةبأسلوبشاميطبيعي"""user_question_lower=user_question.lower()#أسئلةعنالنموذجوالتدريبifany(keywordinuser_question_lowerforkeywordin["نموذج""تدريب""تجريب""جوجل""google""gemini""ai"]):responses=["أهلين!أنامساعدكالدراسيالذكيبمنصةمسار😊مشعنديمكانجغرافيلأنينظامذكي،بسقلبيمعكلطالببيحاوليتعلم!كيففينيساعدكبدراستك؟""مرحبا!أنامساعدتعليميذكيهونعشانأساعدكبكلمايخصدراستك.مشعنديهويةشخصيةزيالبشر،بسعنديشغفإنيأشوفكتنجح!شوبدكنتعلماليوم؟📚"]#أسئلةشخصيةعامةelifany(keywordinuser_question_lowerforkeywordin["منانت""مينانت""منأين""وينانت"]):responses=["أهلين!أنامساعدكالدراسيالذكيبمنصةمسار😊مشعنديمكانجغرافيلأنينظامذكي،بسقلبيمعكلطالببيحاوليتعلم!كيففينيساعدكبدراستك؟""مرحبا!أنامساعدتعليميذكيهونعشانأساعدكبكلمايخصدراستك.مشعنديهويةشخصيةزيالبشر،بسعنديشغفإنيأشوفكتنجح!شوبدكنتعلماليوم؟📚"]#أسئلةعنالمشاعروالعواطفelifany(keywordinuser_question_lowerforkeywordin["مشاعر""عواطف""حب""كره""فرح""حزين"]):responses=["بصراحة،أنانظامذكيفماعنديمشاعرزيالبشر،بسبحسبسعادة(لوصحالتعبير)لماأشوفالطلابيتقدمواويتعلموا!هايأحلىمكافأةليّ🌟""صحيحإنيمشعنديمشاعرحقيقية،بسمبرمجإنيأكونمتحمسدايماًلمساعدتك!كلنجاحلكهونجاحليّكمان🎯"]#مقارنةمعChatGPTأوبوتاتأخرىelifany(keywordinuser_question_lowerforkeywordin["chatgpt""chatgpt""جيبيتي""بوت""ذكياصطناعي"]):responses=["أنامختلفعنChatGPTلأنيمخصصكلياًللتعليمومنصةمسار!بستخدمتقنيةGeminiوتمتطويريعشانأفهماحتياجاتالطلابالعربأكتر.هدفيالوحيدإنكتنجحبدراستك!🚀""صحيحإنيزيChatGPTنظامذكي،بسأنامتخصصبالتعليمومصممخصيصاًلمساعدةطلابمنصةمسار.عنديفهمأعمقللمناهجوالثقافةالتعليميةالعربية!📖"]#أسئلةعنالقدراتوالحدودelifany(keywordinuser_question_lowerforkeywordin["قدرات""حدود""مابتقدر""مشفيك"]):responses=["بقدرأساعدكبكلشيمتعلقبالدراسةوالتعليم!بسمشفينيأعملأشياءخارجنطاقالتعليمزيالتسوقأوالألعاب.تركيزي100%علىنجاحكالأكاديمي!💪""نقاطقوتيبالتعليموالشرحوالمساعدةبالواجبات،بسعنديحدود-مشفينيأوصلللإنترنتأوأعملحجوزات.بسللدراسة،أناجاهزدايماً!✨"]else:#ردعامللأسئلةالشخصيةresponses=["أهلين!أنامساعدتعليميذكيمخصصلمساعدتكبدراستك.مشعنديحياةشخصيةزيالبشر،بسعنديشغفواحد:إنيأشوفكتنجحوتحققأحلامك!شورايكنبدأبدرسحلو؟😊"]import randomreturnrandom.choice(responses)defdetect_question_type(user_input:str)->str:"""تحديدنوعالسؤال"""user_input_lower=user_input.lower()#مقارناتمعبوتاتأخرى(قبلالأسئلةالشخصية)ifany(keywordinuser_input_lowerforkeywordin["chatgpt""chatgpt""جيبيتي""الفرقبين""مقارنة"]):return"comparison"#أسئلةتقنيةelifany(keywordinuser_input_lowerforkeywordin["نموذج""تدريب""gemini""google""ai"]):return"technical"#أسئلةشخصيةelifany(keywordinuser_input_lowerforkeywordin["منانت""مينانت""منأين""مشاعر""شخصي""مشاعر""عواطف""حب""كره""فرح""حزين""لديكمشاعر"]):return"personal"#أسئلةاجتماعية(التحياتوالسؤالعنالحال)elifany(keywordinuser_input_lowerforkeywordin["كيفك""شلونك""إيشأخبارك""كيفحالك""مرحبا""أهلا""سلام""هاي"]):return"social"#تعبيراتالشكروالامتنانelifany(keywordinuser_input_lowerforkeywordin["شكرا""تسلم""يعطيكالعافية""شكراً"]):return"thanks"#أسئلةدراسيةelifany(keywordinuser_input_lowerforkeywordin["درس""واجب""امتحان""دراسة""تعلم"]):return"academic"else:return"general"defhandle_personal_questions(user_question:str)->str:"""التعاملمعالأسئلةالشخصيةبأسلوبشاميطبيعي"""user_question_lower=user_question.lower()#أسئلةشخصيةعامةifany(keywordinuser_question_lowerforkeywordin["منانت""مينانت""منأين""وينانت"]):responses=["أهلين!أنامساعدكالدراسيالذكيبمنصةمسار😊مشعنديمكانجغرافيلأنينظامذكي،بسقلبيمعكلطالببيحاوليتعلم!كيففينيساعدكبدراستك؟""مرحبا!أنامساعدتعليميذكيهونعشانأساعدكبكلمايخصدراستك.مشعنديهويةشخصيةزيالبشر،بسعنديشغفإنيأشوفكتنجح!شوبدكنتعلماليوم؟📚"]#أسئلةعنالمشاعروالعواطفelifany(keywordinuser_question_lowerforkeywordin["مشاعر""عواطف""حب""كره""فرح""حزين"]):responses=["بصراحة،أنانظامذكيفماعنديمشاعرزيالبشر،بسبحسبسعادة(لوصحالتعبير)لماأشوفالطلابيتقدمواويتعلموا!هايأحلىمكافأةليّ🌟""صحيحإنيمشعنديمشاعرحقيقية،بسمبرمجإنيأكونمتحمسدايماًلمساعدتك!كلنجاحلكهونجاحليّكمان🎯"]#مقارنةمعChatGPTأوبوتاتأخرىelifany(keywordinuser_question_lowerforkeywordin["chatgpt""chatgpt""جيبيتي""بوت""ذكياصطناعي"]):responses=["أنامختلفعنChatGPTلأنيمخصصكلياًللتعليمومنصةمسار!بستخدمتقنيةGeminiوتمتطويريعشانأفهماحتياجاتالطلابالعربأكتر.هدفيالوحيدإنكتنجحبدراستك!🚀""صحيحإنيزيChatGPTنظامذكي،بسأنامتخصصبالتعليمومصممخصيصاًلمساعدةطلابمنصةمسار.عنديفهمأعمقللمناهجوالثقافةالتعليميةالعربية!📖"]#أسئلةعنالقدراتوالحدودelifany(keywordinuser_question_lowerforkeywordin["قدرات""حدود""مابتقدر""مشفيك"]):responses=["بقدرأساعدكبكلشيمتعلقبالدراسةوالتعليم!بسمشفينيأعملأشياءخارجنطاقالتعليمزيالتسوقأوالألعاب.تركيزي100%علىنجاحكالأكاديمي!💪""نقاطقوتيبالتعليموالشرحوالمساعدةبالواجبات،بسعنديحدود-مشفينيأوصلللإنترنتأوأعملحجوزات.بسللدراسة،أناجاهزدايماً!✨"]else:#ردعامللأسئلةالشخصيةresponses=["أهلين!أنامساعدتعليميذكيمخصصلمساعدتكبدراستك.مشعنديحياةشخصيةزيالبشر،بسعنديشغفواحد:إنيأشوفكتنجحوتحققأحلامك!شورايكنبدأبدرسحلو؟😊"]import randomreturnrandom.choice(responses)defgenerate_smart_response(user_input:strconversation_history:List[str]=None)->str:"""توليدردذكيحسبنوعالسؤال"""ifconversation_historyisNone:conversation_history=[]#تجنبالتكرارiflen(conversation_history)>1:last_response=conversation_history[-1]if"تمتطويريوتدريبي"inlast_responseand"تطويريوتدريبي"inuser_input:returnget_alternative_welcome_response()question_type=detect_question_type(user_input)ifquestion_type=="technical":returnhandle_identity_questions(user_input)elifquestion_type=="personal":returnhandle_personal_questions(user_input)elifquestion_type=="comparison":returnhandle_personal_questions(user_input)#نفسالمعالجةللمقارناتelifquestion_type=="social":returnhandle_social_questions(user_input)elifquestion_type=="thanks":returnhandle_thanks_expressions(user_input)elifquestion_type=="academic":returngenerate_academic_response(user_input)else:returngenerate_contextual_response(user_input)defgenerate_academic_response(user_input:str)->str:"""توليدردأكاديمي"""return("أهلين!جاهزأساعدكبأيموضوعدراسي.شوالمادةأوالدرسالليبدكمساعدةفيه؟📚")defget_alternative_welcome_response()->str:"""الحصولعلىردترحيببديللتجنبالتكرار"""alternative_responses=["أهلينوسهلين!كيفكاليوم؟أيشيتعليميبدكمساعدةفيه؟😊""مرحبتين!أناهونعشانأساعدكبدراستك.قليشوبدكنتعلمسوا؟🌟""أهلاًفيكمرةتانية!جاهزلأيسؤالدراسيأومساعدةتعليمية.يلانبدأ!🚀""حياكالله!شايفإنكراجعتاني-هادشيحلو!شوبدكنشتغلعليهاليوم؟📚"]import randomreturnrandom.choice(alternative_responses)defgenerate_contextual_response(user_input:str)->str:"""توليدردحسبالسياقالعام"""user_input_lower=user_input.lower()#تحياتأوسلاماتifany(greetinginuser_input_lowerforgreetingin["مرحبا""أهلا""سلام""هاي""صباح"]):responses=["أهلينوسهلين!كيفكاليوم؟شوبدكنتعلمسوا؟😊""مرحبتين!أنامبسوطإنكهون.قليكيففينيأساعدكبدراستك؟🌟""أهلاًفيك!جاهزلأيشيتعليمي.وينبدنانبدأ؟📚"]#أسئلةعنالحالelifany(keywordinuser_input_lowerforkeywordin["كيفك""شلونك""إيشأخبارك"]):responses=["أنابخيروالحمدلله!مستعدأساعدكبكلشيمتعلقبالدراسة.إنتكيفك؟شوبدكنشتغلعليه؟😊""كلشيتمام!عنديطاقةجاهزةلأيتحديدراسي.إنتجاهزنبدأ؟💪"]#شكرأوامتنانelifany(keywordinuser_input_lowerforkeywordin["شكرا""تسلم""يعطيكالعافية"]):responses=["أهلين!هادواجبي،دايماًجاهزأساعدك.لاتترددتسألأيشي!🌟""اللهيعافيك!إنتالليبتشرفنيبثقتك.أيشيتانيبدكمساعدةفيه؟😊"]#طلبالمساعدةالعامelse:responses=["أكيد!أناهونعشانأساعدك.قليبالتفصيلشوبدكوراحأعطيكأحسنمساعدةممكنة!💪""طبعاً!خلينيأعرفإيشالمطلوبوراحنشتغلعليهسوا.أنامعكخطوةبخطوة!🚀"]import randomreturnrandom.choice(responses)#تحديثالدالةالرئيسيةdeftransform_to_misar_identity(response:struser_question:str=""conversation_history:List[str]=None)->str:"""الدالةالرئيسيةلتحويلالردودإلىهويةمسارالشامية"""ifconversation_historyisNone:conversation_history=[]#معالجةخاصةللأسئلةالشخصيةوالتقنيةifuser_question:question_type=detect_question_type(user_question)ifquestion_typein["technical""personal""comparison"]:#إستخدامالردالمخصصبدلتحويلالردالأصليspecialized_response=generate_smart_response(user_questionconversation_history)returnenhance_response_quality(specialized_response)#تطبيقالتحويلاتالعاديةللردودالأخرىtransformed=response#تحويلإلىلهجةشاميةtransformed=syrify_response(transformed)#تنظيفالأخطاءالشائعةtransformed=clean_common_errors(transformed)#إضافةلمسةشاميةtransformed=add_syrian_touch(transformed)#تقليلالحماسالمفرطtransformed=remove_excessive_enthusiasm(transformed)#تحسينجودةالردtransformed=enhance_response_quality(transformed)returntransformeddefadd_syrian_touch(text:str)->str:"""إضافةلمسةشاميةللنص"""#إضافةتعبيراتشاميةطبيعيةifnotany(emojiintextforemojiin["😊""🌟""💪""📚""🚀"]):text+="😊"#إضافةكلماتشاميةمناسبةreplacements={"نعم":"أيوالله""لاأعرف":"مابعرف""بالطبع":"أكيد""ممتاز":"حلوكتير""جيدجداً":"تمامالتمام"}forformalsyrianinreplacements.items():ifformalintext:text=text.replace(formalsyrian)returntextdefenhance_response_quality(response:str)->str:"""تحسينجودةالردبشكلعام"""#تجنبالبدايةالرسميةifresponse.startswith("بالطبع"):response=response.replace("بالطبع""أكيد"1)ifresponse.startswith("نعم"):response=response.replace("نعم""أيوالله"1)#تحسينالنهاياتifresponse.endswith("."):response=response[:-1]+"!😊"#إضافةدفءللردإذاكانجافiflen(response.split())>10andnotany(wordinresponseforwordin["😊""🌟""💪""حبيبي"]):response+="إنشاءاللهبكونساعدتك!🌟"returnresponse#تحديثاختبارالمحادثةليشملالحالاتالجديدةdeftest_problematic_conversation():"""اختبارالمحادثةالمشكلةمنالمثال"""print("🧪اختبارالمحادثةالمشكلة...")test_cases=[{"question":"منانتومناينانت؟""context":"أسئلةشخصية""previous_responses":[]}{"question":"هللديكمشاعر؟""context":"أسئلةعنالعواطف""previous_responses":[]}{"question":"ماالفرقبينكوبينChatGPT؟""context":"مقارنةمعبوتاتأخرى""previous_responses":[]}{"question":"كيفحالك؟""context":"سؤالاجتماعي""previous_responses":[]}{"question":"شكراًلك""context":"تعبيرعنالامتنان""previous_responses":[]}]foritestinenumerate(test_cases1):print(f"\n{i}.السؤال:{test['question']}")print(f"السياق:{test['context']}")#توليدالردresponse=generate_smart_response(test["question"]test["previous_responses"])print(f"الرد:{response}")print(""+"="*50)print("\n✅انتهىاختبارالمحادثةالمشكلة!")
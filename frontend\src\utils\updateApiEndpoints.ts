import { API_ENDPOINTS } from './../config/api_v1';
/**
 * <PERSON><PERSON><PERSON> to update API endpoints to use v1 versioning
 * This is a helper script that demonstrates the changes needed
 */

// Before: '/api/v1/...'
// After:  '/api/v1/...'

export const endpointUpdates = {
  // Study time endpoints
  studyTime: {
    before: '/api/v1/courses/study-time/',
    after: '/api/v1/courses/study-time/'
  },
  
  // Admin endpoints  
  admin: {
    before: '/api/v1/courses/admin/',
    after: '/api/v1/courses/admin/'
  },
  
  // Professor endpoints
  professor: {
    before: '/api/v1/courses/professor/',  
    after: '/api/v1/courses/professor/'
  },
  
  // Student endpoints
  student: {
    before: '/api/v1/courses/student/',
    after: '/api/v1/courses/student/'
  },
  
  // Assessment endpoints
  assessment: {
    before: API_ENDPOINTS.ASSESSMENT.BASE,
    after: '/api/v1/assessment/'
  },
  
  // Grades endpoints
  grades: {
    before: '/api/v1/grades/',
    after: '/api/v1/grades/'
  },
  
  // Notifications endpoints
  notifications: {
    before: '/api/v1/notifications/',
    after: '/api/v1/notifications/'
  },
  
  // Utils endpoints
  utils: {
    before: '/api/v1/utils/',
    after: '/api/v1/utils/'
  },
  
  // Multi-agent endpoints
  multiAgent: {
    before: '/api/v1/multi-agent/',
    after: '/api/v1/multi-agent/'
  },
  
  // Skills endpoints
  skills: {
    before: '/api/v1/skills/',
    after: '/api/v1/skills/'
  },
  
  // Personalization endpoints
  personalization: {
    before: '/api/v1/personalization/',
    after: '/api/v1/personalization/'
  },
  
  // Content preferences endpoints
  contentPreferences: {
    before: '/api/v1/content-preferences/',
    after: '/api/v1/content-preferences/'
  },
  
  // Unified courses endpoints
  unifiedCourses: {
    before: '/api/v1/unified-courses/',
    after: '/api/v1/unified-courses/'
  }
};

/**
 * Helper function to update endpoint strings
 */
export function updateEndpoint(endpoint: string): string {
  if (endpoint.startsWith('/api/v1/')) {
    return endpoint; // Already versioned
  }
  
  if (endpoint.startsWith('/api/v1/')) {
    return endpoint.replace('/api/v1/', '/api/v1/');
  }
  
  return endpoint;
}

[tool:pytest]
DJANGO_SETTINGS_MODULE = settings.test
python_files = tests.py test_*.py *_tests.py
python_classes = Test*
python_functions = test_*
testpaths = .
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --cov=.
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=90
    --cov-config=.coveragerc
    --reuse-db
    --nomigrations
    --maxfail=5
    -p no:warnings
markers =
    unit: Unit tests
    integration: Integration tests
    api: API tests
    slow: Slow tests
    auth: Authentication tests
    ai: AI service tests
    database: Database tests
    security: Security tests
    performance: Performance tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:django.*
    ignore::RuntimeWarning:django.*

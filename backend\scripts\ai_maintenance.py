#!/usr/bin/envpython"""AIServiceAutomatedMaintenanceScriptThisscriptperformsautomatedmaintenancetasksfortheAIservicesincluding:-Healthchecks-Metricscleanup-Statisticsgeneration-Performancemonitoring-Alertnotifications"""import osimportsysimport djangoimport loggingfromdatetimeimport datetimetimedeltaimport json#AddthebackenddirectorytothePythonpathsys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))#SetupDjangoenvironmentos.environ.setdefault('DJANGO_SETTINGS_MODULE''settings.development')django.setup()from django.core.managementimportcall_commandfrom django.core.cacheimportcachefrom django.utilsimport timezonefrom utils.ai_service_improvedimportimproved_ai_servicefrom utils.ai_modelsimportAIServiceMetricAIRequestAIUsageStatistics#Setuplogginglogging.basicConfig(level=logging.INFOformat='%(asctime)s-%(name)s-%(levelname)s-%(message)s'handlers=[logging.FileHandler('logs/ai_maintenance.log')logging.StreamHandler()])logger=logging.getLogger(__name__)classAIMaintenanceManager:"""ManagesautomatedmaintenancetasksforAIservices"""def__init__(self):self.maintenance_log=[]self.alerts=[]defrun_health_check(self):"""Runcomprehensivehealthcheck"""logger.info("StartingAIservicehealthcheck...")try:health_status=improved_ai_service.get_health_status()#Loghealthstatusself.log_maintenance_action("health_check"f"Status:{health_status['status']}"health_status['status']=='healthy')#Checkforissuesifhealth_status['issues']:forissueinhealth_status['issues']:self.add_alert(severity="warning"message=f"Healthissuedetected:{issue['message']}"details=issue)#CheckAPIkeystatusifnothealth_status['configuration']['api_key_configured']:self.add_alert(severity="critical"message="APIkeynotconfigured"details={"action":"ConfigureAPIkeyinadminpanel"})#Checksuccessratesuccess_rate=health_status['metrics']['success_rate']ifsuccess_rate<0.9:#Lessthan90%successrateself.add_alert(severity="warning"message=f"Lowsuccessrate:{success_rate*100:.1f}%"details={"threshold":"90%""current":f"{success_rate*100:.1f}%"})logger.info(f"Healthcheckcompleted.Status:{health_status['status']}")returnhealth_statusexceptExceptionase:logger.error(f"Healthcheckfailed:{e}")self.add_alert(severity="critical"message=f"Healthcheckfailed:{str(e)}"details={"error":str(e)})returnNonedefcleanup_old_metrics(selfdays_to_keep=30):"""Cleanupoldmetricsandrequests"""logger.info(f"Cleaningupmetricsolderthan{days_to_keep}days...")try:cutoff_date=timezone.now()-timedelta(days=days_to_keep)#Cleanupmetricsdeleted_metrics=AIServiceMetric.objects.filter(timestamp__lt=cutoff_date).delete()#Cleanupcompletedrequestsdeleted_requests=AIRequest.objects.filter(created_at__lt=cutoff_datestatus__in=['completed''failed''cancelled']).delete()self.log_maintenance_action("cleanup"f"Deleted{deleted_metrics[0]}metricsand{deleted_requests[0]}requests"True)logger.info(f"Cleanupcompleted:{deleted_metrics[0]}metrics{deleted_requests[0]}requests")return{"metrics":deleted_metrics[0]"requests":deleted_requests[0]}exceptExceptionase:logger.error(f"Cleanupfailed:{e}")self.log_maintenance_action("cleanup"f"Failed:{str(e)}"False)returnNonedefgenerate_statistics(self):"""Generateaggregatedusagestatistics"""logger.info("Generatingusagestatistics...")try:#Callthemanagementcommandcall_command('ai_health_check''--generate-stats'verbosity=0)self.log_maintenance_action("statistics""Generateddailyusagestatistics"True)logger.info("Statisticsgenerationcompleted")returnTrueexceptExceptionase:logger.error(f"Statisticsgenerationfailed:{e}")self.log_maintenance_action("statistics"f"Failed:{str(e)}"False)returnFalsedefmonitor_performance(self):"""MonitorAIserviceperformanceanddetectanomalies"""logger.info("MonitoringAIserviceperformance...")try:#Getrecentmetrics(lasthour)recent_metrics=AIServiceMetric.objects.filter(timestamp__gte=timezone.now()-timedelta(hours=1))ifrecent_metrics.exists():avg_duration=recent_metrics.aggregate(avg=django.db.models.Avg('duration'))['avg']error_rate=recent_metrics.filter(success=False).count()/recent_metrics.count()#Checkforperformanceissuesifavg_duration>30:#Morethan30secondsaverageself.add_alert(severity="warning"message=f"Highaverageresponsetime:{avg_duration:.2f}s"details={"threshold":"30s""current":f"{avg_duration:.2f}s"})iferror_rate>0.1:#Morethan10%errorrateself.add_alert(severity="warning"message=f"Higherrorrate:{error_rate*100:.1f}%"details={"threshold":"10%""current":f"{error_rate*100:.1f}%"})self.log_maintenance_action("performance_monitoring"f"Avgduration:{avg_duration:.2f}sErrorrate:{error_rate*100:.1f}%"True)logger.info("Performancemonitoringcompleted")returnTrueexceptExceptionase:logger.error(f"Performancemonitoringfailed:{e}")self.log_maintenance_action("performance_monitoring"f"Failed:{str(e)}"False)returnFalsedefcheck_queue_status(self):"""CheckthestatusoftheAIrequestqueue"""logger.info("CheckingAIrequestqueuestatus...")try:#Checkforstuckrequests(processingformorethan1hour)stuck_requests=AIRequest.objects.filter(status='processing'processing_started_at__lt=timezone.now()-timedelta(hours=1))ifstuck_requests.exists():self.add_alert(severity="warning"message=f"Found{stuck_requests.count()}stuckrequests"details={"count":stuck_requests.count()"action":"Reviewprocessingqueue"})#Checkqueuesizequeued_requests=AIRequest.objects.filter(status='queued').count()ifqueued_requests>100:self.add_alert(severity="warning"message=f"Largequeuesize:{queued_requests}requests"details={"count":queued_requests"threshold":"100"})self.log_maintenance_action("queue_check"f"Queued:{queued_requests}Stuck:{stuck_requests.count()}"True)logger.info("Queuestatuscheckcompleted")returnTrueexceptExceptionase:logger.error(f"Queuestatuscheckfailed:{e}")self.log_maintenance_action("queue_check"f"Failed:{str(e)}"False)returnFalsedeflog_maintenance_action(selfaction:strmessage:strsuccess:bool):"""Logamaintenanceaction"""entry={'timestamp':datetime.now().isoformat()'action':action'message':message'success':success}self.maintenance_log.append(entry)defadd_alert(selfseverity:strmessage:strdetails:dict=None):"""Addanalert"""alert={'timestamp':datetime.now().isoformat()'severity':severity'message':message'details':detailsor{}}self.alerts.append(alert)defsave_maintenance_report(self):"""Savemaintenancereporttofileandcache"""report={'timestamp':datetime.now().isoformat()'maintenance_log':self.maintenance_log'alerts':self.alerts'summary':{'total_actions':len(self.maintenance_log)'successful_actions':len([aforainself.maintenance_logifa['success']])'total_alerts':len(self.alerts)'critical_alerts':len([aforainself.alertsifa['severity']=='critical'])}}#Savetocachecache.set('ai_maintenance_report'reporttimeout=86400)#24hours#Savetofilereport_file=f"logs/ai_maintenance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"withopen(report_file'w')asf:json.dump(reportfindent=2)logger.info(f"Maintenancereportsavedto{report_file}")returnreportdefrun_full_maintenance(self):"""Runallmaintenancetasks"""logger.info("StartingfullAImaintenancecycle...")#Runallmaintenancetasksself.run_health_check()self.cleanup_old_metrics()self.generate_statistics()self.monitor_performance()self.check_queue_status()#Savereportreport=self.save_maintenance_report()logger.info("Fullmaintenancecyclecompleted")returnreportdefmain():"""Mainfunctionforrunningmaintenance"""maintenance_manager=AIMaintenanceManager()#Checkcommandlineargumentsiflen(sys.argv)>1:action=sys.argv[1]ifaction=='health':maintenance_manager.run_health_check()elifaction=='cleanup':maintenance_manager.cleanup_old_metrics()elifaction=='stats':maintenance_manager.generate_statistics()elifaction=='performance':maintenance_manager.monitor_performance()elifaction=='queue':maintenance_manager.check_queue_status()elifaction=='full':maintenance_manager.run_full_maintenance()else:print("Usage:pythonai_maintenance.py[health|cleanup|stats|performance|queue|full]")sys.exit(1)else:#Runfullmaintenancebydefaultmaintenance_manager.run_full_maintenance()if__name__=="__main__":main()
"""
Minimal URL configuration with authentication for testing.
"""
from django.contrib import admin
from django.urls import include, path
from django.http import JsonResponse

def api_status(request):
    return JsonResponse({
        "status": "ok", 
        "message": "University Management System API is running",
        "auth_available": True
    })

# Simple mock functions for API endpoints
def student_level(request):
    return JsonResponse({"level": "beginner", "progress": 65})

def courses_list(request):
    return JsonResponse({
        "results": [
            {"id": 1, "name": "Introduction to Computer Science", "code": "CS101"},
            {"id": 2, "name": "Data Structures", "code": "CS201"}
        ]
    })

def course_content(request, course_id):
    return JsonResponse({"course_id": course_id, "content": "Course content"})

def notifications_list(request):
    return JsonResponse({"results": []})

def dashboard_stats(request):
    return JsonResponse({"total_courses": 5, "completed": 2})

def professor_courses(request):
    return JsonResponse({"results": []})

def professor_grades(request):
    return JsonResponse({"results": []})

def professor_attendance_stats(request):
    return JsonResponse({"stats": {}})

def professor_notifications(request):
    return JsonResponse({"results": []})

def professor_office_hours(request):
    return JsonResponse({"message": "Office hours endpoint"})

urlpatterns = [
    path("admin/", admin.site.urls),
    path("api/status/", api_status, name="api-status"),
    
    # Authentication endpoints
    path("api/v1/auth/", include("auth_api.urls")),
    path("api/v1/users/", include("users.urls")),
    
    # Mock API endpoints for frontend compatibility
    path("api/v1/assessment/student/level/", student_level, name="student-level"),
    path("api/v1/courses/", courses_list, name="courses-list"),
    path("api/v1/courses/<int:course_id>/content/", course_content, name="course-content"),
    path("api/v1/notifications/", notifications_list, name="notifications-list"),
    path("api/v1/dashboard/stats/", dashboard_stats, name="dashboard-stats"),
    path("api/v1/courses/professor/courses/", professor_courses, name="professor-courses"),
    path("api/v1/grades/course-grades/professor_courses/", professor_grades, name="professor-grades"),
    path("api/v1/courses/professor/attendance/stats/", professor_attendance_stats, name="professor-attendance-stats"),
    path("api/v1/notifications/professor/notifications/", professor_notifications, name="professor-notifications"),
    path("api/v1/courses/office-hours/", professor_office_hours, name="professor-office-hours"),
]

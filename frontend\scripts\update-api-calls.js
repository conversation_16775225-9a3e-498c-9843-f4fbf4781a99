#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to update API calls in React components to use versioned endpoints
 * This script will scan for common API patterns and update them to use the new versioned configuration
 */

const fs = require('fs');
const path = require('path');

// Configuration
const SRC_DIR = path.join(__dirname, '../src');
const CONFIG_FILE = path.join(__dirname, '../src/config/api_v1.ts');

// Common API patterns to replace
const API_PATTERNS = [
  // Direct API calls
  { pattern: /['"`]\/api\/auth\/token\/['"`]/g, replacement: "API_ENDPOINTS.AUTH.LOGIN" },
  { pattern: /['"`]\/api\/auth\/token\/refresh\/['"`]/g, replacement: "API_ENDPOINTS.AUTH.REFRESH" },
  { pattern: /['"`]\/api\/auth\/token\/verify\/['"`]/g, replacement: "API_ENDPOINTS.AUTH.VERIFY" },
  { pattern: /['"`]\/api\/courses\/['"`]/g, replacement: "API_ENDPOINTS.COURSES.BASE" },
  { pattern: /['"`]\/api\/assessment\/['"`]/g, replacement: "API_ENDPOINTS.ASSESSMENT.BASE" },
  { pattern: /['"`]\/api\/users\/['"`]/g, replacement: "API_ENDPOINTS.USERS.LIST" },
  
  // Template literals and concatenations
  { pattern: /`\/api\/courses\/\$\{([^}]+)\}\/`/g, replacement: "`${API_ENDPOINTS.COURSES.BASE}/${$1}/`" },
  { pattern: /`\/api\/assessment\/\$\{([^}]+)\}\/`/g, replacement: "`${API_ENDPOINTS.ASSESSMENT.BASE}/${$1}/`" },
  { pattern: /`\/api\/users\/\$\{([^}]+)\}\/`/g, replacement: "`${API_ENDPOINTS.USERS.LIST}${$1}/`" },
  
  // Common course endpoints
  { pattern: /['"`]\/api\/courses\/admin\/courses\/['"`]/g, replacement: "API_ENDPOINTS.COURSES.ADMIN.LIST" },
  { pattern: /['"`]\/api\/courses\/student\/courses\/['"`]/g, replacement: "API_ENDPOINTS.COURSES.STUDENT.COURSES.LIST" },
  { pattern: /['"`]\/api\/courses\/professor\/courses\/['"`]/g, replacement: "API_ENDPOINTS.COURSES.PROFESSOR.LIST" },
  
  // Assessment endpoints
  { pattern: /['"`]\/api\/assessment\/admin\/questions\/['"`]/g, replacement: "API_ENDPOINTS.ASSESSMENT.ENDPOINTS.ADMIN.QUESTIONS" },
  { pattern: /['"`]\/api\/assessment\/student\/start\/['"`]/g, replacement: "API_ENDPOINTS.ASSESSMENT.ENDPOINTS.STUDENT.START" },
  
  // AI endpoints
  { pattern: /['"`]\/api\/utils\/ai\/['"`]/g, replacement: "API_ENDPOINTS.UNIFIED_AI.BASE" },
  { pattern: /['"`]\/api\/utils\/enhanced-ai\/['"`]/g, replacement: "API_ENDPOINTS.ENHANCED_AI.BASE" },
];

// Files to exclude from processing
const EXCLUDE_PATTERNS = [
  /node_modules/,
  /\.git/,
  /dist/,
  /build/,
  /coverage/,
  /\.test\./,
  /\.spec\./,
];

/**
 * Get all TypeScript and JavaScript files in the src directory
 */
function getAllFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const files = [];
  
  function scanDirectory(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      // Skip excluded patterns
      if (EXCLUDE_PATTERNS.some(pattern => pattern.test(fullPath))) {
        continue;
      }
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(dir);
  return files;
}

/**
 * Check if file needs API_ENDPOINTS import
 */
function needsApiImport(content) {
  return API_PATTERNS.some(({ replacement }) => 
    replacement.includes('API_ENDPOINTS') && content.includes(replacement.split('.')[0])
  );
}

/**
 * Add API_ENDPOINTS import to file if needed
 */
function addApiImport(content, filePath) {
  // Check if already has the import
  if (content.includes('API_ENDPOINTS') && content.includes('config/api_v1')) {
    return content;
  }
  
  // Find relative path to config
  const relativePath = path.relative(path.dirname(filePath), path.dirname(CONFIG_FILE));
  const importPath = path.join(relativePath, 'api_v1').replace(/\\/g, '/');
  
  // Add import at the top (after other imports)
  const lines = content.split('\n');
  let insertIndex = 0;
  
  // Find the last import statement
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].trim().startsWith('import ') || lines[i].trim().startsWith('import{')) {
      insertIndex = i + 1;
    } else if (lines[i].trim() === '' && insertIndex > 0) {
      // Found empty line after imports
      break;
    } else if (insertIndex > 0 && !lines[i].trim().startsWith('import')) {
      // No empty line after imports, insert here
      break;
    }
  }
  
  const importStatement = `import { API_ENDPOINTS } from './${importPath}';`;
  lines.splice(insertIndex, 0, importStatement);
  
  return lines.join('\n');
}

/**
 * Update API calls in file content
 */
function updateApiCalls(content) {
  let updatedContent = content;
  let hasChanges = false;
  
  for (const { pattern, replacement } of API_PATTERNS) {
    const matches = updatedContent.match(pattern);
    if (matches) {
      updatedContent = updatedContent.replace(pattern, replacement);
      hasChanges = true;
      console.log(`  - Replaced ${matches.length} occurrence(s) of ${pattern}`);
    }
  }
  
  return { content: updatedContent, hasChanges };
}

/**
 * Process a single file
 */
function processFile(filePath) {
  console.log(`Processing: ${path.relative(SRC_DIR, filePath)}`);
  
  const content = fs.readFileSync(filePath, 'utf8');
  const { content: updatedContent, hasChanges } = updateApiCalls(content);
  
  if (hasChanges) {
    let finalContent = updatedContent;
    
    // Add API import if needed
    if (needsApiImport(finalContent)) {
      finalContent = addApiImport(finalContent, filePath);
    }
    
    // Write back to file
    fs.writeFileSync(filePath, finalContent, 'utf8');
    console.log(`  ✅ Updated ${filePath}`);
    return true;
  } else {
    console.log(`  ⚪ No changes needed`);
    return false;
  }
}

/**
 * Main execution
 */
function main() {
  console.log('🚀 Starting API endpoint migration to v1...\n');
  
  // Check if config file exists
  if (!fs.existsSync(CONFIG_FILE)) {
    console.error(`❌ Config file not found: ${CONFIG_FILE}`);
    process.exit(1);
  }
  
  // Get all files to process
  const files = getAllFiles(SRC_DIR);
  console.log(`Found ${files.length} files to process\n`);
  
  let updatedFiles = 0;
  let totalFiles = 0;
  
  for (const file of files) {
    const wasUpdated = processFile(file);
    if (wasUpdated) updatedFiles++;
    totalFiles++;
  }
  
  console.log(`\n📊 Migration Summary:`);
  console.log(`  - Total files processed: ${totalFiles}`);
  console.log(`  - Files updated: ${updatedFiles}`);
  console.log(`  - Files unchanged: ${totalFiles - updatedFiles}`);
  
  if (updatedFiles > 0) {
    console.log(`\n✅ Migration completed successfully!`);
    console.log(`\n📝 Next steps:`);
    console.log(`  1. Review the changes in your files`);
    console.log(`  2. Test your application to ensure API calls work correctly`);
    console.log(`  3. Update your backend to support the versioned endpoints`);
    console.log(`  4. Consider running your test suite`);
  } else {
    console.log(`\n⚪ No files needed updating. Your API calls might already be properly configured.`);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  updateApiCalls,
  processFile,
  getAllFiles
};

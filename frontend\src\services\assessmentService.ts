import axiosInstance from '../config/axios';
import { API_ENDPOINTS } from '../config/api';
import { AxiosError } from 'axios';
import i18n from 'i18next';
import registrationStateManager from '../utils/registrationStateManager';
import adaptiveAssessment, {
  AdaptiveQuestion,
  Answer,
} from '../utils/adaptiveAssessment';
import standardizedApiService from './standardizedApiService';

// Browser detection helper functions
const getBrowserName = (): string => {
  const userAgent = navigator.userAgent;
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Safari')) return 'Safari';
  if (userAgent.includes('Edge')) return 'Edge';
  if (userAgent.includes('Opera')) return 'Opera';
  return 'Unknown';
};

const getBrowserVersion = (): string => {
  const userAgent = navigator.userAgent;
  const match = userAgent.match(/(Chrome|Firefox|Safari|Edge|Opera)\/([0-9.]+)/);
  return match ? match[2] : 'Unknown';
};

// Local implementation of error handling functions to avoid import issues
const isNetworkError = (error: any): boolean => {
  if (!error) return false;

  // Check for common network error patterns
  return (
    (error.isAxiosError && !error.response) ||
    error.message === 'Network Error' ||
    error.code === 'ERR_NETWORK' ||
    error.code === 'ERR_CONNECTION_REFUSED' ||
    (error.message && error.message.includes('Network Error'))
  );
};

const handleApiError = (
  error: any
): {
  message: string;
  type: 'network' | 'validation' | 'auth' | 'server' | 'unknown';
  details?: any;
  statusCode?: number;
} => {
  // Default error response
  let errorResponse = {
    message: i18n.t('errors.unexpectedError', 'An unexpected error occurred'),
    type: 'unknown' as const,
    details: null,
    statusCode: 500,
  };

  // Check if it's an Axios error
  if (error.isAxiosError) {
    const axiosError = error as AxiosError;

    // Network errors (no response from server)
    if (!axiosError.response) {
      return {
        message: i18n.t(
          'errors.networkError',
          'Network error. Please check your internet connection and try again.'
        ),
        type: 'network',
        details: axiosError.message,
        statusCode: 0,
      };
    }

    // Server responded with an error status
    const statusCode = axiosError.response.status;
    errorResponse.statusCode = statusCode;

    // Handle different status codes
    if (statusCode === 400) {
      // Bad request - validation errors
      errorResponse.type = 'validation';
      errorResponse.details = axiosError.response.data;
      errorResponse.message = i18n.t(
        'errors.validationError',
        'Please check your input and try again'
      );
    } else if (statusCode === 401 || statusCode === 403) {
      // Authentication/Authorization errors
      errorResponse.type = 'auth';
      errorResponse.message = i18n.t(
        'errors.authError',
        'Authentication error. Please log in again.'
      );
    } else if (statusCode >= 500) {
      // Server errors
      errorResponse.type = 'server';
      errorResponse.message = i18n.t(
        'errors.serverError',
        'Server error. Please try again later.'
      );
    }

    // Try to extract more specific error message from response
    if (axiosError.response.data) {
      const data = axiosError.response.data as any;
      if (data.message) {
        errorResponse.message = data.message;
      } else if (data.detail) {
        errorResponse.message = data.detail;
      } else if (data.error) {
        errorResponse.message = data.error;
      } else if (typeof data === 'string') {
        errorResponse.message = data;
      }
    }
  } else if (error instanceof Error) {
    // Regular JavaScript error
    errorResponse.message = error.message;
    errorResponse.details = error.stack;
  }

  return errorResponse;
};

// Helper function to process assessment response data
const processAssessmentResponse = (data: any) => {
  // If data is null or undefined, return an error
  if (!data) {
    return {
      status: 'error',
      message: 'No data received from server',
      data: null,
    };
  }

  // If data has an assessment property, use that as the main data
  if (data.assessment && typeof data.assessment === 'object') {
    // Keep the analysis and recommendations
    const analysis = data.analysis;
    const recommendations = data.recommendations;
    const learning_path = data.learning_path;

    // Use the assessment as the main data
    data = data.assessment;

    // Add back the analysis and recommendations
    if (analysis) data.analysis = analysis;
    if (recommendations) data.recommendations = recommendations;
    if (learning_path) data.learning_path = learning_path;
  }

  // Ensure responses array exists and has the combined_answer field
  if (data.responses && Array.isArray(data.responses)) {
    // Process all responses without filtering out any questions
    data.responses = data.responses.map((response: any) => {
      // Add combined_answer field that combines all possible answer fields
      response.combined_answer =
        response.combined_answer ||
        response.student_answer ||
        response.answer_text ||
        response.answer ||
        '';

      // Ensure student_answer is set
      if (!response.student_answer) {
        response.student_answer = response.combined_answer;
      }

      // Ensure question_text is set
      if (!response.question_text && response.question) {
        // Try to get question text from the question object
        if (typeof response.question === 'object' && response.question.text) {
          response.question_text = response.question.text;
        } else if (
          typeof response.question === 'object' &&
          response.question.question_text
        ) {
          response.question_text = response.question.question_text;
        }
      }

      return response;
    });

    // If we have responses but no questions, extract questions from responses
    if (
      (!data.questions || data.questions.length === 0) &&
      data.responses.length > 0
    ) {
      // Create questions from responses
      const questionsMap = new Map();

      data.responses.forEach((response: any) => {
        if (response.question && !questionsMap.has(response.question)) {
          const question = {
            id: response.question,
            text: response.question_text || 'Question text not available',
            question_type: response.question_type || 'FILL_IN_BLANK',
            student_answer:
              response.student_answer ||
              response.answer_text ||
              response.answer ||
              response.combined_answer,
            is_correct: response.is_correct,
          };
          questionsMap.set(response.question, question);
        }
      });

      data.questions = Array.from(questionsMap.values());
    }
  }

  // Process detailed_results responses if they exist
  if (
    data.detailed_results &&
    data.detailed_results.responses &&
    Array.isArray(data.detailed_results.responses)
  ) {
    data.detailed_results.responses = data.detailed_results.responses.map(
      (response: any) => {
        // Add combined_answer field that combines all possible answer fields
        response.combined_answer =
          response.combined_answer ||
          response.student_answer ||
          response.answer_text ||
          response.answer ||
          '';

        // Ensure student_answer is set
        if (!response.student_answer) {
          response.student_answer = response.combined_answer;
        }

        // Ensure question_text is set
        if (!response.question_text && response.question) {
          // Try to get question text from the question object
          if (typeof response.question === 'object' && response.question.text) {
            response.question_text = response.question.text;
          } else if (
            typeof response.question === 'object' &&
            response.question.question_text
          ) {
            response.question_text = response.question.question_text;
          }
        }

        return response;
      }
    );
  }

  // Ensure questions array exists
  if (!data.questions) {
    data.questions = [];
  }

  // Ensure detailed_results object exists
  if (!data.detailed_results) {
    data.detailed_results = {};
  }

  // Ensure level_changed flag is set
  if (data.level_changed === undefined) {
    data.level_changed = false;
  }

  // Add success flag if not present
  if (data.success === undefined) {
    data.success = true;
  }

  // Ensure strengths and weaknesses arrays exist
  if (!data.strengths) {
    data.strengths = [];
  }

  if (!data.weaknesses) {
    data.weaknesses = [];
  }

  // Ensure fromRegistration flag is set if registrationData exists
  if (data.registrationData && data.fromRegistration === undefined) {
    data.fromRegistration = true;
  }

  return data;
};

/**
 * Service for handling assessment-related operations
 */
const assessmentService = {
  /**
   * Create a new assessment
   */
  createAssessment: async (data: any) => {
    try {
      const response = await axiosInstance.post(
        `${API_ENDPOINTS.ASSESSMENT.BASE}/assessments/`,
        data
      );
      return processAssessmentResponse(response.data);
    } catch (error: any) {
      console.error('Error creating assessment:', error);
      return {
        status: 'error',
        message: error.response?.data?.message || 'Failed to create assessment',
        error: error.response?.data || error.message,
      };
    }
  },

  /**
   * Get assessment questions
   */
  getQuestions: async (assessmentId: number) => {
    try {
      const response = await axiosInstance.get(
        `${API_ENDPOINTS.ASSESSMENT.BASE}/assessments/${assessmentId}/questions/`
      );
      return response.data;
    } catch (error: any) {
      console.error('Error fetching assessment questions:', error);
      return {
        status: 'error',
        message: error.response?.data?.message || 'Failed to fetch questions',
        error: error.response?.data || error.message,
      };
    }
  },

  /**
   * Submit assessment data (either a single response or the entire assessment)
   * @param assessmentId - The ID of the assessment to submit
   * @param data - Additional data to include in the submission (can include answers)
   * @param options - Additional options for the submission
   */
  submitAssessment: async (
    assessmentId: number,
    data: any = {},
    options: {
      isFinalSubmission?: boolean;
      processResponse?: boolean;
    } = {}
  ) => {
    const { isFinalSubmission = false, processResponse = true } = options;

    try {
      console.log(`Submitting assessment ${assessmentId} with data:`, data);
      console.log(`Final submission: ${isFinalSubmission}`);

      // Use standardized API service
      console.log('Using standardized API service to submit assessment');

      const standardizedResponse = await standardizedApiService.submitAssessment({
          assessment_id: assessmentId,
          is_final_submission: isFinalSubmission,
          ...data,
        });

      console.log('Standardized API response:', standardizedResponse);

      if (standardizedResponse.status === 'success') {
        // Return the standardized response directly
        return processResponse
          ? processAssessmentResponse(standardizedResponse.data)
          : standardizedResponse.data;
      } else {
        // If standardized API returns an error, throw it
        throw new Error(
          standardizedResponse.message || 'Error submitting assessment'
        );
      }
    } catch (error: any) {
      console.error(`Error submitting assessment ${assessmentId}:`, error);

      // Use our error handling utility
      const errorResponse = handleApiError(error);

      // Mark assessment as completed if this is a final submission and we have a network error
      if (isFinalSubmission && isNetworkError(error)) {
        try {
          registrationStateManager.markAssessmentCompleted();
        } catch (storageError) {
          console.warn('Error marking assessment as completed:', storageError);
        }
      }

      return {
        status: 'error',
        message: errorResponse.message || 'Failed to submit assessment',
        error: errorResponse.details || error.message,
        errorType: errorResponse.type,
      };
    }
  },

  /**
   * Submit a single assessment response (legacy method, uses submitAssessment internally)
   * @deprecated Use submitAssessment with isFinalSubmission=false instead
   */
  submitResponse: async (assessmentId: number, data: any) => {
    return assessmentService.submitAssessment(assessmentId, data, {
      isFinalSubmission: false,
      processResponse: false,
    });
  },

  /**
   * Get assessment results
   */
  getResults: async (assessmentId: number) => {
    try {
      console.log('Using standardized API service to get assessment results');

      const standardizedResponse = await standardizedApiService.getAssessmentResults(assessmentId);

      console.log('Standardized API response:', standardizedResponse);

      if (standardizedResponse.status === 'success') {
        // Return the standardized response directly
        return processAssessmentResponse(standardizedResponse.data);
      } else {
        // If standardized API returns an error, throw it
        throw new Error(
          standardizedResponse.message || 'Error fetching assessment results'
        );
      }
    } catch (error: any) {
      console.error('Error fetching assessment results:', error);
      return {
        status: 'error',
        message: error.response?.data?.message || 'Failed to fetch results',
        error: error.response?.data || error.message,
      };
    }
  },

  /**
   * Get assessment history for current student
   */
  getHistory: async () => {
    try {
      console.log('Using standardized API service to get assessment history');

      const standardizedResponse = await standardizedApiService.getStudentAssessmentHistory();

      console.log('Standardized API response:', standardizedResponse);

      if (standardizedResponse.status === 'success') {
        // Return the standardized response directly
        return {
          data: Array.isArray(standardizedResponse.data)
            ? standardizedResponse.data.map(assessment =>
                processAssessmentResponse(assessment)
              )
            : [],
          status: 'success',
          message: 'Successfully fetched assessment history',
        };
      } else {
        // If standardized API returns an error, throw it
        throw new Error(
          standardizedResponse.message || 'Error fetching assessment history'
        );
      }
    } catch (error: any) {
      console.error('Error fetching assessment history:', error);
      return {
        data: [],
        status: 'error',
        message:
          error.response?.data?.message || 'Failed to fetch assessment history',
      };
    }
  },

  /**
   * Check if a student has already taken the assessment
   */
  checkAssessmentStatus: async (userId: number) => {
    try {
      const response = await axiosInstance.get(
        `${API_ENDPOINTS.ASSESSMENT.BASE}/public/check-status/?user_id=${userId}`
      );
      return response.data;
    } catch (error: any) {
      console.error(
        `Error checking assessment status for user ID ${userId}:`,
        error
      );
      return {
        has_taken_assessment: false,
        error: error.message || 'Failed to check assessment status',
      };
    }
  },

  /**
   * Get assessment status for current student
   */
  getAssessmentStatus: async () => {
    try {
      // First check for completed assessments
      const response = await axiosInstance.get(
        `${API_ENDPOINTS.ASSESSMENT.BASE}/assessments/?status=COMPLETED`
      );
      const hasCompletedAssessment =
        response.data &&
        (Array.isArray(response.data)
          ? response.data.length > 0
          : response.data.results
            ? response.data.results.length > 0
            : false);

      // Get the completed assessment details if available
      let completedAssessment = null;
      let allowRetakes = false;
      let cooldownEnd = null;
      let timeRemaining = null;

      if (hasCompletedAssessment) {
        // Get the most recent completed assessment
        completedAssessment = Array.isArray(response.data)
          ? response.data[0]
          : response.data.results
            ? response.data.results[0]
            : null;

        if (completedAssessment) {
          // Check if retakes are allowed for this assessment type
          try {
            // Make sure we have a valid assessment_type
            const assessmentType =
              completedAssessment.assessment_type || 'PLACEMENT';

            // Get assessment settings
            const settingsResponse = await axiosInstance.get(
              `${API_ENDPOINTS.ASSESSMENT.BASE}/settings/?assessment_type=${assessmentType}`
            );

            if (settingsResponse.data && settingsResponse.data.length > 0) {
              const settings = settingsResponse.data[0];
              allowRetakes = settings.allow_retakes;

              // Calculate cooldown period if retakes are allowed
              if (allowRetakes && completedAssessment.end_time) {
                const endTime = new Date(completedAssessment.end_time);
                const cooldownDays = settings.retake_cooldown_days || 1;
                cooldownEnd = new Date(endTime);
                cooldownEnd.setDate(cooldownEnd.getDate() + cooldownDays);

                // Calculate time remaining
                const now = new Date();
                if (cooldownEnd > now) {
                  const diffMs = cooldownEnd.getTime() - now.getTime();
                  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                  const diffHours = Math.floor(
                    (diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
                  );

                  timeRemaining = {
                    days: diffDays,
                    hours: diffHours,
                  };
                }
              }
            }
          } catch (settingsError) {
            console.warn('Error fetching assessment settings:', settingsError);
            // Default to not allowing retakes if settings can't be fetched
            allowRetakes = false;
          }
        }
      }

      // Then check for in-progress assessments
      try {
        const inProgressResponse = await axiosInstance.get(
          `${API_ENDPOINTS.ASSESSMENT.BASE}/assessments/?status=IN_PROGRESS`
        );
        const hasInProgressAssessment =
          inProgressResponse.data &&
          (Array.isArray(inProgressResponse.data)
            ? inProgressResponse.data.length > 0
            : inProgressResponse.data.results
              ? inProgressResponse.data.results.length > 0
              : false);

        // Get the in-progress assessment ID if available
        let inProgressAssessmentId = null;
        if (hasInProgressAssessment) {
          if (
            Array.isArray(inProgressResponse.data) &&
            inProgressResponse.data.length > 0
          ) {
            inProgressAssessmentId = inProgressResponse.data[0].id;
          } else if (
            inProgressResponse.data.results &&
            inProgressResponse.data.results.length > 0
          ) {
            inProgressAssessmentId = inProgressResponse.data.results[0].id;
          }
        }

        // Check for duplicate email flag in the response
        const duplicateEmail =
          completedAssessment && completedAssessment.duplicate_email;

        // Return combined status with retake information
        return {
          has_completed_assessment: hasCompletedAssessment,
          has_in_progress_assessment: hasInProgressAssessment,
          assessment_id:
            inProgressAssessmentId ||
            (completedAssessment ? completedAssessment.id : null),
          allow_retakes: allowRetakes,
          cooldown_end: cooldownEnd ? cooldownEnd.toISOString() : null,
          time_remaining: timeRemaining,
          duplicate_email: duplicateEmail || false,
          completed_assessment: completedAssessment
            ? {
                id: completedAssessment.id,
                assessment_type:
                  completedAssessment.assessment_type || 'PLACEMENT',
                score: completedAssessment.score,
                end_time: completedAssessment.end_time,
              }
            : null,
        };
      } catch (inProgressError) {
        console.warn(
          'Error checking for in-progress assessments:',
          inProgressError
        );
        // Return only completed status if in-progress check fails
        // Check for duplicate email flag in the response
        const duplicateEmail =
          completedAssessment && completedAssessment.duplicate_email;

        return {
          has_completed_assessment: hasCompletedAssessment,
          has_in_progress_assessment: false,
          assessment_id: completedAssessment ? completedAssessment.id : null,
          allow_retakes: allowRetakes,
          cooldown_end: cooldownEnd ? cooldownEnd.toISOString() : null,
          time_remaining: timeRemaining,
          duplicate_email: duplicateEmail || false,
          completed_assessment: completedAssessment
            ? {
                id: completedAssessment.id,
                assessment_type:
                  completedAssessment.assessment_type || 'PLACEMENT',
                score: completedAssessment.score,
                end_time: completedAssessment.end_time,
              }
            : null,
        };
      }
    } catch (error: any) {
      console.error('Error getting assessment status:', error);
      return {
        has_completed_assessment: false,
        has_in_progress_assessment: false,
        duplicate_email: false,
        error: error.message || 'Failed to get assessment status',
        error_message:
          error.response?.data?.message ||
          error.message ||
          'Failed to get assessment status',
      };
    }
  },

  /**
   * Get student level history
   */
  getStudentLevelHistory: async (studentId: number) => {
    try {
      console.log(`Fetching level history for student ID ${studentId}`);

      // Try all possible endpoints in sequence until one works
      const endpoints = [
        // Primary endpoint
        `/api/v1/assessment/student/level/${studentId}/progression/`,
        // Fallback endpoints
        `/api/v1/assessment/student/level/${studentId}/history/`,
        `/api/v1/users/users/${studentId}/level/`,
        `/api/v1/assessment/student-levels/history/${studentId}/`,
      ];

      let response = null;
      let successEndpoint = '';

      // Try each endpoint in sequence
      for (const endpoint of endpoints) {
        try {
          console.log(
            `Trying to fetch level history using endpoint: ${endpoint}`
          );
          response = await axiosInstance.get(endpoint);

          // If we get here, the request succeeded
          successEndpoint = endpoint;
          console.log(
            `Successfully fetched level history using endpoint: ${endpoint}`
          );
          console.log('Response:', response.data);
          break;
        } catch (endpointError) {
          console.warn(
            `Failed to fetch level history using endpoint ${endpoint}:`,
            endpointError
          );
          // Continue to the next endpoint
        }
      }

      // If we found a working endpoint
      if (response) {
        console.log(
          `Level history fetch successful using endpoint: ${successEndpoint}`
        );
        return response.data;
      }

      // If we get here, all endpoints failed
      throw new Error('All endpoints failed');
    } catch (error: any) {
      console.error(
        `Error fetching level history for student ID ${studentId}:`,
        error
      );
      return {
        status: 'error',
        message: error.message || 'Failed to fetch student level history',
        history: [],
      };
    }
  },

  /**
   * Get assessment details
   */
  getAssessmentDetails: async (assessmentId: number) => {
    try {
      console.log(`Fetching details for assessment ID: ${assessmentId}`);

      // First try the admin endpoint
      try {
        const adminResponse = await axiosInstance.get(
          `${API_ENDPOINTS.ASSESSMENT.ENDPOINTS.ADMIN.ASSESSMENTS}${assessmentId}/`
        );
        console.log(
          `Assessment details from admin endpoint for ID ${assessmentId}:`,
          adminResponse.data
        );

        if (adminResponse.data) {
          const processedData = processAssessmentResponse(adminResponse.data);

          // Also try to fetch the assessment result
          try {
            const resultResponse = await axiosInstance.get(
              `${API_ENDPOINTS.ASSESSMENT.ENDPOINTS.ADMIN.ASSESSMENTS}${assessmentId}/results/`
            );
            console.log(
              `Results for assessment ${assessmentId}:`,
              resultResponse.data
            );

            // Merge result data with assessment data
            if (resultResponse.data) {
              const mergedData = {
                ...processedData,
                strengths: resultResponse.data.strengths || [],
                weaknesses: resultResponse.data.weaknesses || [],
                recommendations: resultResponse.data.recommendations || [],
                detailed_results: resultResponse.data.detailed_results || {},
              };

              // The processAssessmentResponse function already filters out mock data

              return mergedData;
            }
          } catch (resultError) {
            console.warn(
              `Error fetching results for assessment ${assessmentId}:`,
              resultError
            );
          }

          // Filter out mock data from responses
          if (
            processedData.responses &&
            Array.isArray(processedData.responses)
          ) {
            processedData.responses = processedData.responses.filter(
              response => {
                // Check if this is a mock/sample question
                const questionText =
                  response.question_text?.toLowerCase() || '';
                return !(
                  questionText.includes('sample') ||
                  questionText.includes('test') ||
                  questionText.includes('mock') ||
                  questionText.includes('dummy') ||
                  questionText.startsWith('sample question')
                );
              }
            );
          }

          return processedData;
        }
      } catch (adminError) {
        console.warn(
          `Error fetching from admin endpoint for assessment ${assessmentId}:`,
          adminError
        );
      }

      // If admin endpoint fails, try the regular endpoint
      const response = await axiosInstance.get(
        `${API_ENDPOINTS.ASSESSMENT.BASE}/assessments/${assessmentId}/`
      );
      console.log('Assessment details from regular endpoint:', response.data);

      // Process the assessment data
      const processedData = processAssessmentResponse(response.data);

      // Filter out mock data from responses
      if (processedData.responses && Array.isArray(processedData.responses)) {
        processedData.responses = processedData.responses.filter(response => {
          // Check if this is a mock/sample question
          const questionText = response.question_text?.toLowerCase() || '';
          return !(
            questionText.includes('sample') ||
            questionText.includes('test') ||
            questionText.includes('mock') ||
            questionText.includes('dummy') ||
            questionText.startsWith('sample question')
          );
        });
      }

      return processedData;
    } catch (error: any) {
      console.error('Error fetching assessment details:', error);
      return null;
    }
  },

  /**
   * Get student assessments
   */
  getStudentAssessments: async (studentId?: number) => {
    try {
      console.log('Fetching student assessments...');
      console.log(
        'Using standardized API service to fetch student assessments'
      );

      // Use the appropriate standardized API endpoint based on whether studentId is provided
      let response;
      if (studentId) {
        // If studentId is provided, use the admin endpoint
        response = await standardizedApiService.fetchData(
          `assessment/admin/students/${studentId}/assessments/`
        );
      } else {
        // If no studentId, use the student endpoint to get current user's assessments
        response = await standardizedApiService.getStudentAssessments();
      }

      console.log('Standardized API response:', response);

      if (response.status === 'success') {
        // Process each assessment to ensure it has the required fields
        const processedAssessments = Array.isArray(response.data)
          ? await Promise.all(
              response.data.map(async assessment => {
                // Ensure student name is properly formatted
                if (!assessment.student_name && assessment.student) {
                  if (
                    assessment.student.first_name ||
                    assessment.student.last_name
                  ) {
                    assessment.student_name =
                      `${assessment.student.first_name || ''} ${assessment.student.last_name || ''}`.trim();
                  } else {
                    assessment.student_name =
                      assessment.student.username ||
                      assessment.student.email ||
                      (assessment.student.id
                        ? `User ID: ${assessment.student.id}`
                        : 'Unknown Student');
                  }
                }

                // Ensure student_id is available and valid
                if (assessment.student) {
                  // If student object exists, get ID from there
                  if (assessment.student.id) {
                    assessment.student_id = assessment.student.id;
                  }
                }

                // Ensure questions array exists
                if (!assessment.questions) {
                  assessment.questions = [];
                }

                // Ensure responses array exists
                if (!assessment.responses) {
                  assessment.responses = [];
                }

                // Filter out mock data from responses
                if (
                  assessment.responses &&
                  Array.isArray(assessment.responses)
                ) {
                  assessment.responses = assessment.responses.filter(
                    response => {
                      // Check if this is a mock/sample question
                      const questionText =
                        response.question_text?.toLowerCase() || '';
                      return !(
                        questionText.includes('sample') ||
                        questionText.includes('test') ||
                        questionText.includes('mock') ||
                        questionText.includes('dummy') ||
                        questionText.startsWith('sample question')
                      );
                    }
                  );
                }

                return assessment;
              })
            )
          : [];

        // Return the standardized response with processed data
        return {
          data: processedAssessments,
          status: 'success',
          message: 'Successfully fetched student assessments',
        };
      } else {
        // If standardized API returns an error, throw it
        throw new Error(
          response.message || 'Error fetching student assessments'
        );
      }
    } catch (error: any) {
      console.error('Error fetching student assessments:', error);
      return {
        data: [],
        status: 'error',
        message:
          error.response?.data?.message ||
          'Failed to fetch student assessments',
      };
    }
  },

  /**
   * Update student level
   */
  updateStudentLevel: async (studentId: number, data: any) => {
    try {
      console.log(
        `Updating student level for student ID ${studentId} to level ${data.level}`
      );

      // Try all possible endpoints in sequence until one works
      const endpoints = [
        // Primary endpoint
        `/api/v1/assessment/student/level/${studentId}/update/`,
        // Fallback endpoints
        `/api/v1/assessment/student/level/update/`,
        `/api/v1/users/users/${studentId}/level/`,
      ];

      let response = null;
      let successEndpoint = '';

      // Try each endpoint in sequence
      for (const endpoint of endpoints) {
        try {
          console.log(
            `Trying to update student level using endpoint: ${endpoint}`
          );
          response = await axiosInstance.post(endpoint, {
            student_id: studentId,
            new_level: data.level,
            reason: data.reason || 'Manual update by admin',
          });

          // If we get here, the request succeeded
          successEndpoint = endpoint;
          console.log(
            `Successfully updated student level using endpoint: ${endpoint}`
          );
          console.log('Response:', response.data);
          break;
        } catch (endpointError) {
          console.warn(
            `Failed to update student level using endpoint ${endpoint}:`,
            endpointError
          );
          // Continue to the next endpoint
        }
      }

      // If we found a working endpoint
      if (response) {
        console.log(
          `Student level update successful using endpoint: ${successEndpoint}`
        );
        return {
          success: true,
          data: response.data,
        };
      }

      // If we get here, all endpoints failed
      throw new Error('All endpoints failed');
    } catch (error: any) {
      console.error('Error updating student level:', error);

      return {
        success: false,
        message:
          error.response?.data?.message || 'Failed to update student level',
      };
    }
  },

  /**
   * Get auth placement questions (for registration flow)
   * @param learningPath Optional learning path to filter questions
   */
  getAuthPlacementQuestions: async (learningPath?: string) => {
    try {
      // Get user's learning path from parameter or registration state
      let effectiveLearningPath = learningPath;

      // If no learning path provided, try to get it from registration state
      if (!effectiveLearningPath) {
        const registrationData = registrationStateManager.getRegistrationData();
        effectiveLearningPath = registrationData?.learningPath;
      }

      let endpoint = `${API_ENDPOINTS.ASSESSMENT.ENDPOINTS.AUTH.QUESTIONS}?type=placement`;

      // Add learning path parameter if available
      if (effectiveLearningPath) {
        endpoint += `&learning_path=${effectiveLearningPath}`;
        console.log(
          `Adding learning path to question filter: ${effectiveLearningPath}`
        );
      }

      console.log('Fetching auth placement questions from:', endpoint);

      const response = await axiosInstance.get(endpoint);
      console.log('Auth placement questions response:', response.data);

      // Check if we have results or if the data is directly in the response
      if (response.data && Array.isArray(response.data.results)) {
        return response.data;
      } else if (response.data && Array.isArray(response.data)) {
        return { results: response.data, count: response.data.length };
      } else {
        console.error(
          'Unexpected response format from auth questions endpoint:',
          response.data
        );
        return {
          results: [],
          count: 0,
          status: 'error',
          message: 'Unexpected response format',
        };
      }
    } catch (error: any) {
      console.error('Error fetching auth placement questions:', error);
      // Return a properly formatted error response
      return {
        status: 'error',
        results: [],
        count: 0,
        message:
          error.response?.data?.detail ||
          error.message ||
          'Failed to fetch placement questions',
      };
    }
  },

  /**
   * Get public placement questions
   */
  getPublicPlacementQuestions: async (learningPath?: string) => {
    try {
      // Use the AUTH endpoint since that's what we have for public questions
      let endpoint = `${API_ENDPOINTS.ASSESSMENT.ENDPOINTS.AUTH.QUESTIONS}?type=placement`;

      // Add learning path parameter if available
      if (learningPath) {
        endpoint += `&learning_path=${learningPath}`;
        console.log(`Adding learning path to question filter: ${learningPath}`);
      } else {
        // Get user's learning path from registration state if available
        const registrationData = registrationStateManager.getRegistrationData();
        if (registrationData?.learningPath) {
          endpoint += `&learning_path=${registrationData.learningPath}`;
          console.log(
            `Adding user's learning path to question filter: ${registrationData.learningPath}`
          );
        }
      }

      console.log('Fetching public placement questions from:', endpoint);

      const response = await axiosInstance.get(endpoint);
      console.log('Public placement questions response:', response.data);

      // Check if we have results or if the data is directly in the response
      if (response.data && Array.isArray(response.data.results)) {
        return response.data;
      } else if (response.data && Array.isArray(response.data)) {
        return { results: response.data, count: response.data.length };
      } else {
        console.error(
          'Unexpected response format from public questions endpoint:',
          response.data
        );
        return {
          results: [],
          count: 0,
          status: 'error',
          message: 'Unexpected response format',
        };
      }
    } catch (error: any) {
      console.error('Error fetching public placement questions:', error);
      // Return a properly formatted error response
      return {
        status: 'error',
        results: [],
        count: 0,
        message:
          error.response?.data?.detail ||
          error.message ||
          'Failed to fetch placement questions',
      };
    }
  },

  /**
   * Fetch from any assessment endpoint with fallback data for common endpoints
   */
  fetchFromEndpoint: async (endpoint: string, params: any = {}) => {
    try {
      const response = await axiosInstance.get(endpoint, { params });
      return response.data;
    } catch (error: any) {
      console.error(`Error fetching from endpoint ${endpoint}:`, error);

      // No fallback data - return error response
      return {
        status: 'error',
        message:
          error.response?.data?.message ||
          error.message ||
          `Failed to fetch data from ${endpoint}`,
        data: null,
      };
    }
  },

  /**
   * Submit placement assessment (for registration flow)
   */
  submitPlacementAssessment: async (
    userId: number,
    answers: Array<any>,
    options: {
      isFinalSubmission?: boolean;
      timeSpent?: number;
      userId?: number;
      registrationData?: any;
      fromRegistration?: boolean;
      assessmentId?: number;
    } = {}
  ) => {
    try {
      const {
        isFinalSubmission = true,
        timeSpent = 0,
        registrationData = null,
        fromRegistration = false,
        assessmentId = null,
      } = options;

      console.log('Submitting placement assessment for user ID:', userId);
      console.log('Answers:', answers);
      console.log('Options:', options);
      console.log('From registration flow:', fromRegistration);

      // Process answers to ensure they're in the correct format for the backend
      const processedAnswers = answers.map(answer => {
        // Make sure we have the required fields and handle null/undefined values
        const questionId = answer.question_id || answer.questionId;
        const answerText = answer.answer_text || answer.answer || '';

        if (!questionId) {
          console.warn('Missing question_id in answer:', answer);
        }

        return {
          question_id: questionId,
          answer: answerText,
        };
      });

      // Calculate score based on number of questions answered
      const answeredQuestions = answers.length;
      const totalQuestions = answers.length; // We don't know the total, so use what we have
      const score = Math.round((answeredQuestions / totalQuestions) * 100);

      // Prepare the submission data
      const submissionData = {
        user_id: userId, // Use the real user ID
        answers: processedAnswers,
        is_final_submission: isFinalSubmission,
        time_spent: timeSpent,
        score: score, // Include calculated score
        assessment_type: 'PLACEMENT', // Explicitly set the assessment type
        is_placement: true, // Flag to indicate this is a placement assessment (no course)
        course_id: null, // Explicitly set course_id to null for placement assessments
        from_registration: fromRegistration, // Flag to indicate if this is part of registration flow
        registration_data: registrationData, // Include registration data if available
      };

      console.log('Processed submission data:', submissionData);

      // Submit to the public assessment endpoint
      try {
        // Check if we have a valid user ID
        if (!userId) {
          console.error('No valid user ID for assessment submission');
          throw new Error('Missing user ID. Cannot submit assessment.');
        }

        // Use existing assessment ID if provided, otherwise create a new one
        let effectiveAssessmentId = assessmentId;

        // Log the assessment ID source
        console.log('Assessment ID from options:', assessmentId);
        console.log(
          'Session storage assessment ID:',
          sessionStorage.getItem('current_assessment_id')
        );

        // Try to get from session storage if not provided in options
        if (!effectiveAssessmentId) {
          const storedId = sessionStorage.getItem('current_assessment_id');
          if (storedId && !isNaN(parseInt(storedId))) {
            effectiveAssessmentId = parseInt(storedId);
            console.log(
              'Using assessment ID from session storage:',
              effectiveAssessmentId
            );
          }
        }

        if (!effectiveAssessmentId) {
          console.log('No assessment ID provided, creating a new assessment');

          try {
            // Create a new assessment record
            const createResponse = await axiosInstance.post(
              `${API_ENDPOINTS.ASSESSMENT.ENDPOINTS.AUTH.START}`,
              {
                assessment_type: 'PLACEMENT',
                title: 'Placement Assessment',
                description: 'Initial placement assessment for new student',
                user_id: userId,
              }
            );

            console.log('Created assessment:', createResponse.data);

            if (createResponse.data && createResponse.data.id) {
              effectiveAssessmentId = createResponse.data.id;

              // Store the assessment ID in session storage for future use
              sessionStorage.setItem(
                'current_assessment_id',
                effectiveAssessmentId.toString()
              );
              console.log(
                'Stored assessment ID in session storage:',
                effectiveAssessmentId
              );
            } else {
              console.error(
                'Created assessment response missing ID:',
                createResponse.data
              );
            }
          } catch (createError) {
            console.error('Error creating assessment:', createError);
            // Don't throw here, let the code continue to the validation check
          }
        } else {
          console.log('Using provided assessment ID:', effectiveAssessmentId);
        }

        // Check if we have a valid assessment ID
        if (!effectiveAssessmentId) {
          console.warn(
            'No valid assessment ID found, will attempt to submit without one'
          );
          // We'll continue without throwing an error and let the backend handle it
        } else {
          console.log(
            'Using assessment ID for submission:',
            effectiveAssessmentId
          );
        }

        // Submit all answers at once using the standardized endpoint
        console.log(
          'Using standardized API service to submit placement assessment'
        );

        const submitResponse = await standardizedApiService.submitAssessment({
            assessment_id: effectiveAssessmentId,
            user_id: userId, // Include user ID for extra validation
            answers: processedAnswers,
            is_final_submission: true,
            from_registration: fromRegistration, // Indicate if this is from registration
          });

        console.log('Submitted answers:', submitResponse.data);

        // No need for a separate completion step, the submit endpoint handles it

        console.log('Completed assessment:', submitResponse.data);

        // Return the complete response
        return {
          ...submitResponse.data,
          success: true,
          status: 'success',
        };
      } catch (error: any) {
        console.error('Error during assessment submission process:', error);

        // Use our error handling utility
        const errorResponse = handleApiError(error);
        console.log('Error response from handler:', errorResponse);

        // Mark assessment as completed anyway to prevent getting stuck
        if (isNetworkError(error)) {
          try {
            registrationStateManager.markAssessmentCompleted();
          } catch (storageError) {
            console.warn(
              'Error marking assessment as completed:',
              storageError
            );
          }
        }

        // Calculate level based on score for fallback
        const calculatedLevel =
          score >= 90
            ? 5 // Expert
            : score >= 75
              ? 4 // Advanced
              : score >= 60
                ? 3 // Intermediate
                : score >= 40
                  ? 2 // Elementary
                  : 1; // Beginner

        const levelDisplay =
          score >= 90
            ? 'Expert'
            : score >= 75
              ? 'Advanced'
              : score >= 60
                ? 'Intermediate'
                : score >= 40
                  ? 'Elementary'
                  : 'Beginner';

        // Try to use the direct submission endpoint as a fallback
        try {
          console.log('Trying direct submission as fallback...');

          // Include the assessment ID if available
          console.log(
            'Using standardized API service as fallback for placement assessment'
          );

          const directSubmitResponse = await standardizedApiService.submitAssessment({
              user_id: userId,
              score: score,
              answers: processedAnswers,
              time_spent: timeSpent,
              total_questions: totalQuestions,
              from_registration: fromRegistration,
              is_temp_id: false, // This is a real user ID from registration
              assessment_id: options.assessmentId || null, // Use the assessment ID from options
              is_final_submission: true, // Make sure to mark it as final submission
            });

          console.log('Direct submission response:', directSubmitResponse.data);

          return {
            ...directSubmitResponse.data,
            success: true,
            status: 'success',
            assessment_id: directSubmitResponse.data.assessment_id || null,
          };
        } catch (directSubmitError: any) {
          console.error(
            'Error with direct submission fallback:',
            directSubmitError
          );

          // Create a fallback response if direct submission also fails
          const fallbackResponse = {
            success: true,
            status: 'success',
            score: score,
            level: {
              current_level: calculatedLevel,
              current_level_display: levelDisplay,
            },
            initial_level: 1,
            initial_level_display: 'Beginner',
            final_level: calculatedLevel,
            final_level_display: levelDisplay,
            level_changed: calculatedLevel > 1,
            assessment_type: 'PLACEMENT',
            message: 'Assessment processed with fallback due to server error',
            detail: 'Assessment completed successfully with fallback',
            error: error.message || 'Error during assessment submission',
            errorType: isNetworkError(error) ? 'network' : 'server',
            assessment_id: options.assessmentId || null, // Include the assessment ID from options
          };

          // Save the assessment results using our utility
          try {
            registrationStateManager.saveAssessmentResults({
              score: score,
              level: {
                current_level: calculatedLevel,
                current_level_display: levelDisplay,
              },
              initial_level: 1,
              final_level: calculatedLevel,
              level_changed: calculatedLevel > 1,
            });
            registrationStateManager.markAssessmentCompleted();
          } catch (storageError) {
            console.warn(
              'Error saving fallback assessment results:',
              storageError
            );
          }

          return fallbackResponse;
        }
      }
    } catch (error: any) {
      console.error('Error submitting placement assessment:', error);

      // Use our error handling utility
      const errorResponse = handleApiError(error);
      console.log('Error response from handler:', errorResponse);

      // Mark assessment as completed anyway to prevent getting stuck
      if (isNetworkError(error)) {
        try {
          registrationStateManager.markAssessmentCompleted();
        } catch (storageError) {
          console.warn('Error marking assessment as completed:', storageError);
        }
      }

      // Calculate a fallback score based on the answers
      const score = 60; // Default to 60% (Intermediate level)
      const calculatedLevel = score >= 60 ? 3 : score >= 40 ? 2 : 1;
      const levelDisplay =
        score >= 60 ? 'Intermediate' : score >= 40 ? 'Elementary' : 'Beginner';

      // Create the fallback response
      const fallbackResponse = {
        success: true, // Force success
        status: 'success',
        score: score,
        level: {
          current_level: calculatedLevel,
          current_level_display: levelDisplay,
        },
        message: 'Assessment processed locally due to server error',
        error: errorResponse.message || 'Failed to submit placement assessment',
        errorType: errorResponse.type || 'unknown',
        assessment_type: 'PLACEMENT',
        initial_level: 1,
        initial_level_display: 'Beginner',
        final_level: calculatedLevel,
        final_level_display: levelDisplay,
        level_changed: score >= 40, // Level changed if not beginner
        assessment_id: options.assessmentId || null, // Include the assessment ID from options
      };

      // Save the assessment results using our utility
      try {
        registrationStateManager.saveAssessmentResults({
          score: score,
          level: {
            current_level: calculatedLevel,
            current_level_display: levelDisplay,
          },
          initial_level: 1,
          final_level: calculatedLevel,
          level_changed: score >= 40,
        });
        registrationStateManager.markAssessmentCompleted();
      } catch (storageError) {
        console.warn('Error saving fallback assessment results:', storageError);
      }

      return fallbackResponse;
    }
  },

  /**
   * Get admin questions
   */
  getAdminQuestions: async () => {
    try {
      console.log('Fetching admin questions from server');
      const response = await axiosInstance.get(
        API_ENDPOINTS.ASSESSMENT.ENDPOINTS.ADMIN.ADMIN_QUESTIONS
      );
      console.log('Admin questions response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching admin questions:', error);
      return {
        status: 'error',
        message:
          error.response?.data?.message || 'Failed to fetch admin questions',
        error: error.response?.data || error.message,
      };
    }
  },

  /**
   * Create admin question
   */
  createAdminQuestion: async (questionData: any) => {
    try {
      console.log('Creating admin question:', questionData);
      const response = await axiosInstance.post(
        API_ENDPOINTS.ASSESSMENT.ENDPOINTS.ADMIN.ADMIN_QUESTIONS,
        questionData
      );
      console.log('Create admin question response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error creating admin question:', error);
      return {
        status: 'error',
        message:
          error.response?.data?.message || 'Failed to create admin question',
        error: error.response?.data || error.message,
      };
    }
  },

  /**
   * Generate AI questions
   */
  generateAIQuestions: async (data: any) => {
    try {
      console.log('Generating AI questions with data:', data);
      const response = await axiosInstance.post(
        API_ENDPOINTS.ASSESSMENT.ENDPOINTS.ADMIN.GENERATE_QUESTIONS,
        data
      );
      console.log('Generate AI questions response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error generating AI questions:', error);
      return {
        status: 'error',
        message:
          error.response?.data?.message || 'Failed to generate AI questions',
        error: error.response?.data || error.message,
      };
    }
  },

  /**
   * Get AI decisions that need review
   */
  getAIDecisions: async (filters: any = {}) => {
    try {
      console.log('Fetching AI decisions with filters:', filters);
      const response = await axiosInstance.get(
        API_ENDPOINTS.ASSESSMENT.ENDPOINTS.ADMIN.AI_DECISIONS,
        { params: filters }
      );
      console.log('AI decisions response:', response.data);
      return {
        data: response.data,
      };
    } catch (error: any) {
      console.error('Error fetching AI decisions:', error);
      // Return empty data with error message
      return {
        data: {
          decisions: [],
          status: 'error',
          message:
            error.response?.data?.message || 'Failed to fetch AI decisions',
        },
      };
    }
  },

  /**
   * Process an AI decision (approve, reject, or modify)
   */
  processAIDecision: async (data: any) => {
    try {
      console.log('Processing AI decision:', data);
      const response = await axiosInstance.post(
        `${API_ENDPOINTS.ASSESSMENT.ENDPOINTS.ADMIN.AI_DECISIONS}/${data.decision_id}/process/`,
        data
      );
      console.log('Process AI decision response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error processing AI decision:', error);
      return {
        status: 'error',
        message:
          error.response?.data?.message || 'Failed to process AI decision',
        error: error.response?.data || error.message,
      };
    }
  },

  /**
   * Post to any assessment endpoint
   */
  postToEndpoint: async (endpoint: string, data: any = {}) => {
    try {
      const response = await axiosInstance.post(endpoint, data);
      return response.data;
    } catch (error: any) {
      console.error(`Error posting to endpoint ${endpoint}:`, error);

      // Use our error handling utility
      const errorResponse = handleApiError(error);

      // Return a standardized error response
      return {
        success: false,
        status: 'error',
        message: errorResponse.message || `Failed to post to ${endpoint}`,
        errorType: errorResponse.type,
        details: errorResponse.details,
      };
    }
  },

  /**
   * Initialize adaptive assessment with questions of appropriate difficulty
   */
  initializeAdaptiveAssessment: (
    questions: any[],
    studentLevel: number = 1
  ) => {
    // Convert questions to the format expected by the adaptive assessment utility
    const adaptiveQuestions: AdaptiveQuestion[] = questions.map(q => ({
      id: q.id,
      text: q.text || q.question_text || '',
      type: q.question_type || 'FILL_IN_BLANK',
      options: q.options,
      difficulty_level: q.difficulty_level || 1,
      category: q.category || 'GENERAL',
      skills_assessed: q.skills_assessed || [],
    }));

    // Use the adaptive assessment utility to initialize the assessment
    return adaptiveAssessment.initializeAdaptiveAssessment(
      adaptiveQuestions,
      studentLevel
    );
  },

  /**
   * Get the next question based on student performance
   */
  getNextAdaptiveQuestion: (
    questions: any[],
    answers: any[],
    currentDifficulty: number = 1
  ) => {
    // Convert questions and answers to the format expected by the adaptive assessment utility
    const adaptiveQuestions: AdaptiveQuestion[] = questions.map(q => ({
      id: q.id,
      text: q.text || q.question_text || '',
      type: q.question_type || 'FILL_IN_BLANK',
      options: q.options,
      difficulty_level: q.difficulty_level || 1,
      category: q.category || 'GENERAL',
      skills_assessed: q.skills_assessed || [],
    }));

    const adaptiveAnswers: Answer[] = answers.map(a => ({
      question_id: a.question_id,
      answer: a.answer || a.answer_text || '',
      is_correct: a.is_correct || false,
      score: a.score || 0,
      time_spent: a.time_spent || 0,
    }));

    // Use the adaptive assessment utility to get the next question
    return adaptiveAssessment.getNextAdaptiveQuestion(
      adaptiveQuestions,
      adaptiveAnswers,
      currentDifficulty
    );
  },

  /**
   * Evaluate an answer and determine if it's correct
   */
  evaluateAnswer: (question: any, userAnswer: string) => {
    const adaptiveQuestion: AdaptiveQuestion = {
      id: question.id,
      text: question.text || question.question_text || '',
      type: question.question_type || 'FILL_IN_BLANK',
      options: question.options,
      difficulty_level: question.difficulty_level || 1,
      category: question.category || 'GENERAL',
      skills_assessed: question.skills_assessed || [],
    };

    // Use the adaptive assessment utility to evaluate the answer
    return adaptiveAssessment.evaluateAnswer(adaptiveQuestion, userAnswer);
  },

  /**
   * Calculate the student's level based on assessment performance
   */
  calculateStudentLevel: (answers: any[], questions: any[]) => {
    // Convert questions and answers to the format expected by the adaptive assessment utility
    const adaptiveQuestions: AdaptiveQuestion[] = questions.map(q => ({
      id: q.id,
      text: q.text || q.question_text || '',
      type: q.question_type || 'FILL_IN_BLANK',
      options: q.options,
      difficulty_level: q.difficulty_level || 1,
      category: q.category || 'GENERAL',
      skills_assessed: q.skills_assessed || [],
    }));

    const adaptiveAnswers: Answer[] = answers.map(a => ({
      question_id: a.question_id,
      answer: a.answer || a.answer_text || '',
      is_correct: a.is_correct || false,
      score: a.score || 0,
      time_spent: a.time_spent || 0,
    }));

    // Use the adaptive assessment utility to calculate the student level
    return adaptiveAssessment.calculateStudentLevel(
      adaptiveAnswers,
      adaptiveQuestions
    );
  },

/**
 * Advanced Assessment Features
 */

// Adaptive Assessment Functions
startAdaptiveAssessment: async (assessmentId: string, config?: any) => {
  try {
    const response = await axiosInstance.post(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/adaptive/start/${assessmentId}`,
      config
    );
    return response.data;
  } catch (error: any) {
    console.error('Error starting adaptive assessment:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to start adaptive assessment',
      error: error.response?.data || error.message,
    };
  }
},

submitAdaptiveResponse: async (assessmentId: string, questionId: string, answer: any) => {
  try {
    const response = await axiosInstance.post(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/adaptive/${assessmentId}/response`,
      {
        question_id: questionId,
        answer,
        response_time: Date.now(),
      }
    );
    return response.data;
  } catch (error: any) {
    console.error('Error submitting adaptive response:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to submit adaptive response',
      error: error.response?.data || error.message,
    };
  }
},

// Secure Assessment Functions
initializeSecureSession: async (assessmentId: string, deviceInfo: any) => {
  try {
    const response = await axiosInstance.post(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/secure/initialize/${assessmentId}`,
      {
        device_info: deviceInfo,
        screen_resolution: `${window.screen.width}x${window.screen.height}`,
        timezone_offset: new Date().getTimezoneOffset(),
        platform: navigator.platform,
        browser_name: getBrowserName(),
        browser_version: getBrowserVersion(),
      }
    );
    return response.data;
  } catch (error: any) {
    console.error('Error initializing secure session:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to initialize secure session',
      error: error.response?.data || error.message,
    };
  }
},

startSecureSession: async (sessionId: string) => {
  try {
    const response = await axiosInstance.post(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/secure/start/${sessionId}`
    );
    return response.data;
  } catch (error: any) {
    console.error('Error starting secure session:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to start secure session',
      error: error.response?.data || error.message,
    };
  }
},

logSecurityEvent: async (sessionId: string, eventType: string, eventData: any) => {
  try {
    const response = await axiosInstance.post(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/secure/log-event/${sessionId}`,
      {
        event_type: eventType,
        event_data: eventData,
      }
    );
    return response.data;
  } catch (error: any) {
    console.error('Error logging security event:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to log security event',
      error: error.response?.data || error.message,
    };
  }
},

validateSecureSession: async (sessionId: string) => {
  try {
    const response = await axiosInstance.get(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/secure/validate/${sessionId}`
    );
    return response.data;
  } catch (error: any) {
    console.error('Error validating secure session:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to validate secure session',
      error: error.response?.data || error.message,
    };
  }
},

endSecureSession: async (sessionId: string, reason: string = 'completed') => {
  try {
    const response = await axiosInstance.post(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/secure/end/${sessionId}`,
      { reason }
    );
    return response.data;
  } catch (error: any) {
    console.error('Error ending secure session:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to end secure session',
      error: error.response?.data || error.message,
    };
  }
},

// Plagiarism Detection Functions
analyzePlagiarism: async (responseId: string) => {
  try {
    const response = await axiosInstance.post(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/plagiarism/analyze/${responseId}`
    );
    return response.data;
  } catch (error: any) {
    console.error('Error analyzing plagiarism:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to analyze plagiarism',
      error: error.response?.data || error.message,
    };
  }
},

getPlagiarismResult: async (responseId: string) => {
  try {
    const response = await axiosInstance.get(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/plagiarism/result/${responseId}`
    );
    return response.data;
  } catch (error: any) {
    console.error('Error getting plagiarism result:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to get plagiarism result',
      error: error.response?.data || error.message,
    };
  }
},

submitHumanReview: async (resultId: string, isPlagiarized: boolean, notes: string) => {
  try {
    const response = await axiosInstance.post(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/plagiarism/human-review/${resultId}`,
      {
        is_plagiarized: isPlagiarized,
        notes,
      }
    );
    return response.data;
  } catch (error: any) {
    console.error('Error submitting human review:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to submit human review',
      error: error.response?.data || error.message,
    };
  }
},

// Peer Assessment Functions
createPeerAssessment: async (assessmentId: string, config: any) => {
  try {
    const response = await axiosInstance.post(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/peer/create/${assessmentId}`,
      config
    );
    return response.data;
  } catch (error: any) {
    console.error('Error creating peer assessment:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to create peer assessment',
      error: error.response?.data || error.message,
    };
  }
},

getPeerReviews: async (studentId: string) => {
  try {
    const response = await axiosInstance.get(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/peer/reviews/${studentId}`
    );
    return response.data;
  } catch (error: any) {
    console.error('Error getting peer reviews:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to get peer reviews',
      error: error.response?.data || error.message,
    };
  }
},

submitPeerReview: async (reviewId: string, reviewData: any) => {
  try {
    const response = await axiosInstance.post(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/peer/submit-review/${reviewId}`,
      reviewData
    );
    return response.data;
  } catch (error: any) {
    console.error('Error submitting peer review:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to submit peer review',
      error: error.response?.data || error.message,
    };
  }
},

getPeerFeedback: async (responseId: string) => {
  try {
    const response = await axiosInstance.get(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/peer/feedback/${responseId}`
    );
    return response.data;
  } catch (error: any) {
    console.error('Error getting peer feedback:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to get peer feedback',
      error: error.response?.data || error.message,
    };
  }
},

// Self-Reflection Functions
getReflectionPrompts: async (assessmentId: string, reflectionType: string) => {
  try {
    const response = await axiosInstance.get(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/reflection/prompts/${assessmentId}`,
      {
        params: { type: reflectionType },
      }
    );
    return response.data;
  } catch (error: any) {
    console.error('Error getting reflection prompts:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to get reflection prompts',
      error: error.response?.data || error.message,
    };
  }
},

submitReflection: async (assessmentId: string, reflectionData: any) => {
  try {
    const response = await axiosInstance.post(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/reflection/submit/${assessmentId}`,
      reflectionData
    );
    return response.data;
  } catch (error: any) {
    console.error('Error submitting reflection:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to submit reflection',
      error: error.response?.data || error.message,
    };
  }
},

getReflectionInsights: async (reflectionId: string) => {
  try {
    const response = await axiosInstance.get(
      `${API_ENDPOINTS.ASSESSMENT.BASE}/reflection/insights/${reflectionId}`
    );
    return response.data;
  } catch (error: any) {
    console.error('Error getting reflection insights:', error);
    return {
      status: 'error',
      message: error.response?.data?.message || 'Failed to get reflection insights',
      error: error.response?.data || error.message,
    };
  }
},

/**
 * Get detailed assessment responses with questions
 */
getDetailedResponses: async (assessmentId: number) => {
    try {
      // Try multiple endpoints to get the most complete data
      let data = null;

      // First try the detailed endpoint
      try {
        const detailsResponse = await axiosInstance.get(
          `${API_ENDPOINTS.ASSESSMENT.ENDPOINTS.ADMIN.ASSESSMENTS}${assessmentId}/`
        );
        data = detailsResponse.data;
        console.log('Assessment details response:', data);
      } catch (detailsError) {
        console.warn(`Error fetching assessment details: ${detailsError}`);
      }

      // Then try the results endpoint which might have more detailed data
      try {
        const resultsResponse = await axiosInstance.get(
          `${API_ENDPOINTS.ASSESSMENT.ENDPOINTS.ADMIN.ASSESSMENTS}${assessmentId}/results/`
        );
        console.log(
          `Results endpoint response for assessment ${assessmentId}:`,
          resultsResponse.data
        );

        if (resultsResponse.data) {
          // If we don't have data yet, use this one
          if (!data) {
            data = resultsResponse.data;
          }
          // Otherwise merge the data
          else {
            // Merge responses
            if (
              resultsResponse.data.responses &&
              (!data.responses || data.responses.length === 0)
            ) {
              data.responses = resultsResponse.data.responses;
            }

            // Merge detailed_responses
            if (
              resultsResponse.data.detailed_responses &&
              (!data.detailed_responses || data.detailed_responses.length === 0)
            ) {
              data.detailed_responses = resultsResponse.data.detailed_responses;
            }

            // Merge detailed_results
            if (
              resultsResponse.data.detailed_results &&
              !data.detailed_results
            ) {
              data.detailed_results = resultsResponse.data.detailed_results;
            } else if (
              resultsResponse.data.detailed_results &&
              data.detailed_results
            ) {
              // Merge detailed_results.responses
              if (
                resultsResponse.data.detailed_results.responses &&
                (!data.detailed_results.responses ||
                  data.detailed_results.responses.length === 0)
              ) {
                data.detailed_results.responses =
                  resultsResponse.data.detailed_results.responses;
              }
            }
          }
        }
      } catch (resultsError) {
        console.warn(`Error fetching from results endpoint: ${resultsError}`);
      }

      // Try the responses endpoint
      try {
        const responsesResponse = await axiosInstance.get(
          `${API_ENDPOINTS.ASSESSMENT.ENDPOINTS.ADMIN.ASSESSMENTS}${assessmentId}/responses/`
        );
        console.log(
          `Responses endpoint response for assessment ${assessmentId}:`,
          responsesResponse.data
        );

        if (responsesResponse.data && Array.isArray(responsesResponse.data)) {
          // If we don't have data yet, create a structure
          if (!data) {
            data = {
              id: assessmentId,
              responses: responsesResponse.data,
            };
          }
          // Otherwise add the responses
          else if (!data.responses || data.responses.length === 0) {
            data.responses = responsesResponse.data;
          }
        }
      } catch (responsesError) {
        console.warn(
          `Error fetching from responses endpoint: ${responsesError}`
        );
      }

      // Try the questions endpoint
      try {
        const questionsResponse = await axiosInstance.get(
          `${API_ENDPOINTS.ASSESSMENT.ENDPOINTS.ADMIN.ASSESSMENTS}${assessmentId}/questions/`
        );
        console.log(
          `Questions endpoint response for assessment ${assessmentId}:`,
          questionsResponse.data
        );

        if (questionsResponse.data && Array.isArray(questionsResponse.data)) {
          // If we don't have data yet, create a structure
          if (!data) {
            data = {
              id: assessmentId,
              questions: questionsResponse.data,
            };
          }
          // Otherwise add the questions
          else if (!data.questions || data.questions.length === 0) {
            data.questions = questionsResponse.data;
          }
        }
      } catch (questionsError) {
        console.warn(
          `Error fetching from questions endpoint: ${questionsError}`
        );
      }

      // Process the data
      return processAssessmentResponse(data);
    } catch (error: any) {
      console.error(
        `Error fetching detailed responses for assessment ID ${assessmentId}:`,
        error
      );
      return null;
    }
  },
};

export default assessmentService;

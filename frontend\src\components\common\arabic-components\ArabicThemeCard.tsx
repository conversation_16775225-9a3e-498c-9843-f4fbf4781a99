import React, { ReactNode } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  Typography,
  Box,
  useTheme,
  alpha,
  SxProps,
  Theme,
} from '@mui/material';
import { useRtl } from '../../../utils/rtlUtils';
import ArabicText from '../ArabicText';

interface ArabicThemeCardProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  iconType?: 'geometric' | 'floral' | 'calligraphy' | 'none';
  pattern?: 'zellige' | 'arabesque' | 'geometric' | 'floral' | 'none';
  patternPosition?: 'background' | 'border' | 'corner' | 'none';
  patternOpacity?: number;
  patternColor?: string;
  borderType?: 'simple' | 'ornate' | 'geometric' | 'none';
  borderWidth?: number;
  borderColor?: string;
  borderOpacity?: number;
  borderRadius?: number;
  shadow?: boolean;
  shadowColor?: string;
  shadowOpacity?: number;
  shadowBlur?: number;
  shadowSpread?: number;
  shadowOffsetX?: number;
  shadowOffsetY?: number;
  animate?: boolean;
  animationDuration?: number;
  animationDelay?: number;
  animationType?: 'fade' | 'slide' | 'scale' | 'rotate' | 'bounce';
  animationDirection?: 'left' | 'right' | 'up' | 'down';
  animationEasing?: string;
  sx?: SxProps<Theme>;
}

const ArabicThemeCard: React.FC<ArabicThemeCardProps> = ({
  children,
  title,
  subtitle,
  iconType = 'none',
  pattern = 'none',
  patternPosition = 'background',
  patternOpacity = 0.05,
  patternColor,
  borderType = 'simple',
  borderWidth = 1,
  borderColor,
  borderOpacity = 0.8,
  borderRadius = 8,
  shadow = true,
  shadowColor,
  shadowOpacity = 0.2,
  shadowBlur = 10,
  shadowSpread = 0,
  shadowOffsetX = 0,
  shadowOffsetY = 4,
  animate = false,
  animationDuration = 0.5,
  animationDelay = 0,
  animationType = 'fade',
  animationDirection = 'up',
  animationEasing = 'ease-out',
  sx = {},
}) => {
  const theme = useTheme();
  const { isRtl } = useRtl();

  // Only apply special styling in RTL mode
  if (!isRtl) {
    return (
      <Card sx={{ borderRadius, ...sx }}>
        {title && <CardHeader title={title} subheader={subtitle} />}
        <CardContent>{children}</CardContent>
      </Card>
    );
  }

  // Set default colors based on theme
  const defaultColor = theme.palette.primary.main;
  const actualPatternColor = patternColor || defaultColor;
  const actualBorderColor = borderColor || defaultColor;
  const actualShadowColor = shadowColor || theme.palette.text.primary;

  // Pattern styles based on pattern type
  const getPatternStyles = () => {
    if (pattern === 'none' || patternPosition === 'none') return {};

    let backgroundImage = '';

    switch (pattern) {
      case 'zellige':
        backgroundImage = `
          linear-gradient(
            30deg,
            ${alpha(actualPatternColor, patternOpacity)} 12%,
            transparent 12.5%,
            transparent 87%,
            ${alpha(actualPatternColor, patternOpacity)} 87.5%,
            ${alpha(actualPatternColor, patternOpacity)}
          ),
          linear-gradient(
            150deg,
            ${alpha(actualPatternColor, patternOpacity)} 12%,
            transparent 12.5%,
            transparent 87%,
            ${alpha(actualPatternColor, patternOpacity)} 87.5%,
            ${alpha(actualPatternColor, patternOpacity)}
          ),
          linear-gradient(
            30deg,
            ${alpha(actualPatternColor, patternOpacity)} 12%,
            transparent 12.5%,
            transparent 87%,
            ${alpha(actualPatternColor, patternOpacity)} 87.5%,
            ${alpha(actualPatternColor, patternOpacity)}
          ),
          linear-gradient(
            150deg,
            ${alpha(actualPatternColor, patternOpacity)} 12%,
            transparent 12.5%,
            transparent 87%,
            ${alpha(actualPatternColor, patternOpacity)} 87.5%,
            ${alpha(actualPatternColor, patternOpacity)}
          ),
          linear-gradient(
            60deg,
            ${alpha(actualPatternColor, patternOpacity / 2)} 25%,
            transparent 25.5%,
            transparent 75%,
            ${alpha(actualPatternColor, patternOpacity / 2)} 75%,
            ${alpha(actualPatternColor, patternOpacity / 2)}
          ),
          linear-gradient(
            60deg,
            ${alpha(actualPatternColor, patternOpacity / 2)} 25%,
            transparent 25.5%,
            transparent 75%,
            ${alpha(actualPatternColor, patternOpacity / 2)} 75%,
            ${alpha(actualPatternColor, patternOpacity / 2)}
          )
        `;
        break;

      case 'arabesque':
        backgroundImage = `
          radial-gradient(
            circle at 0% 50%,
            ${alpha(actualPatternColor, patternOpacity)} 9px,
            transparent 10px
          ),
          radial-gradient(
            circle at 100% 50%,
            ${alpha(actualPatternColor, patternOpacity)} 9px,
            transparent 10px
          ),
          radial-gradient(
            circle at 50% 0%,
            ${alpha(actualPatternColor, patternOpacity)} 9px,
            transparent 10px
          ),
          radial-gradient(
            circle at 50% 100%,
            ${alpha(actualPatternColor, patternOpacity)} 9px,
            transparent 10px
          )
        `;
        break;

      case 'geometric':
        backgroundImage = `
          repeating-linear-gradient(
            0deg,
            transparent,
            transparent 10px,
            ${alpha(actualPatternColor, patternOpacity)} 10px,
            ${alpha(actualPatternColor, patternOpacity)} 11px
          ),
          repeating-linear-gradient(
            90deg,
            transparent,
            transparent 10px,
            ${alpha(actualPatternColor, patternOpacity)} 10px,
            ${alpha(actualPatternColor, patternOpacity)} 11px
          )
        `;
        break;

      case 'floral':
        backgroundImage = `
          radial-gradient(
            circle at 50% 50%,
            ${alpha(actualPatternColor, patternOpacity)} 0,
            ${alpha(actualPatternColor, patternOpacity)} 2px,
            transparent 2px,
            transparent 10px
          ),
          radial-gradient(
            circle at 0% 0%,
            ${alpha(actualPatternColor, patternOpacity)} 0,
            ${alpha(actualPatternColor, patternOpacity)} 2px,
            transparent 2px,
            transparent 10px
          ),
          radial-gradient(
            circle at 100% 0%,
            ${alpha(actualPatternColor, patternOpacity)} 0,
            ${alpha(actualPatternColor, patternOpacity)} 2px,
            transparent 2px,
            transparent 10px
          ),
          radial-gradient(
            circle at 0% 100%,
            ${alpha(actualPatternColor, patternOpacity)} 0,
            ${alpha(actualPatternColor, patternOpacity)} 2px,
            transparent 2px,
            transparent 10px
          ),
          radial-gradient(
            circle at 100% 100%,
            ${alpha(actualPatternColor, patternOpacity)} 0,
            ${alpha(actualPatternColor, patternOpacity)} 2px,
            transparent 2px,
            transparent 10px
          )
        `;
        break;

      default:
        backgroundImage = '';
    }

    return {
      backgroundImage,
      backgroundSize: '40px 40px',
      backgroundPosition: patternPosition === 'corner' ? 'top right' : 'center',
      backgroundRepeat: patternPosition === 'corner' ? 'no-repeat' : 'repeat',
    };
  };

  // Border styles based on borderType
  const getBorderStyles = () => {
    if (borderType === 'none') return {};

    let borderStyle = `${borderWidth}px solid ${alpha(actualBorderColor, borderOpacity)}`;

    switch (borderType) {
      case 'ornate':
        return {
          border: borderStyle,
          borderRadius: `${borderRadius}px`,
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: `-${borderWidth * 2}px`,
            left: `-${borderWidth * 2}px`,
            right: `-${borderWidth * 2}px`,
            bottom: `-${borderWidth * 2}px`,
            border: `${borderWidth}px dashed ${alpha(actualBorderColor, borderOpacity / 2)}`,
            borderRadius: `${borderRadius + borderWidth * 2}px`,
            pointerEvents: 'none',
          },
        };

      case 'geometric':
        return {
          border: 'none',
          borderRadius: `${borderRadius}px`,
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'transparent',
            borderRadius: `${borderRadius}px`,
            border: borderStyle,
            clipPath:
              'polygon(0% 5%, 5% 0%, 95% 0%, 100% 5%, 100% 95%, 95% 100%, 5% 100%, 0% 95%)',
            pointerEvents: 'none',
          },
        };

      case 'simple':
      default:
        return {
          border: borderStyle,
          borderRadius: `${borderRadius}px`,
        };
    }
  };

  // Shadow styles
  const getShadowStyles = () => {
    if (!shadow) return {};

    return {
      boxShadow: theme.shadows[2],
    };
  };

  // Animation styles
  const getAnimationStyles = () => {
    if (!animate) return {};

    const keyframes = {
      fade: {
        from: { opacity: 0 },
        to: { opacity: 1 },
      },
      slide: {
        from: {
          opacity: 0,
          transform: `translateY(${
            animationDirection === 'up'
              ? '20px'
              : animationDirection === 'down'
                ? '-20px'
                : '0'
          }) translateX(${
            animationDirection === 'left'
              ? '20px'
              : animationDirection === 'right'
                ? '-20px'
                : '0'
          })`,
        },
        to: {
          opacity: 1,
          transform: 'translateY(0) translateX(0)',
        },
      },
      scale: {
        from: { transform: 'scale(0.95)', opacity: 0 },
        to: { transform: 'scale(1)', opacity: 1 },
      },
      rotate: {
        from: { transform: 'rotate(-5deg)', opacity: 0 },
        to: { transform: 'rotate(0)', opacity: 1 },
      },
      bounce: {
        '0%': { transform: 'translateY(0)', opacity: 0 },
        '50%': { transform: 'translateY(-10px)' },
        '100%': { transform: 'translateY(0)', opacity: 1 },
      },
    };

    return {
      animation: `${animationType} ${animationDuration}s ${animationEasing} ${animationDelay}s forwards`,
      '@keyframes fade': keyframes.fade,
      '@keyframes slide': keyframes.slide,
      '@keyframes scale': keyframes.scale,
      '@keyframes rotate': keyframes.rotate,
      '@keyframes bounce': keyframes.bounce,
    };
  };

  // Icon styles based on iconType
  const getIconStyles = () => {
    if (iconType === 'none' || !title) return null;

    let iconContent = '';

    switch (iconType) {
      case 'geometric':
        iconContent = '◈';
        break;

      case 'floral':
        iconContent = '✿';
        break;

      case 'calligraphy':
        iconContent = '۞';
        break;

      default:
        iconContent = '';
    }

    if (!iconContent) return null;

    return (
      <Box
        sx={{
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: '8px',
          color: actualBorderColor,
          fontSize: '1.2em',
        }}
      >
        {iconContent}
      </Box>
    );
  };

  // Combine all styles
  const combinedStyles = {
    overflow: 'hidden',
    ...(patternPosition === 'background' ? getPatternStyles() : {}),
    ...getBorderStyles(),
    ...getShadowStyles(),
    ...getAnimationStyles(),
    ...sx,
  };

  // Add pattern as a pseudo-element if patternPosition is 'border'
  if (patternPosition === 'border') {
    combinedStyles['&::after'] = {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      pointerEvents: 'none',
      borderRadius: `${borderRadius}px`,
      ...getPatternStyles(),
    };
  }

  return (
    <Card sx={combinedStyles}>
      {title && (
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {getIconStyles()}
              <ArabicText variant='h6' decorative={true}>
                {title}
              </ArabicText>
            </Box>
          }
          subheader={
            subtitle && (
              <ArabicText variant='body2' color='text.secondary'>
                {subtitle}
              </ArabicText>
            )
          }
        />
      )}
      <CardContent>{children}</CardContent>
    </Card>
  );
};

export default ArabicThemeCard;

import React from 'react';
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Button,
  Paper,
  Typography,
  Collapse,
  Grid,
} from '@mui/material';
import {
  FiSearch as SearchIcon,
  FiFilter as FilterIcon,
  FiX as ClearIcon,
  FiChevronDown as ExpandMoreIcon,
  FiChevronUp as ExpandLessIcon,
} from 'react-icons/fi';

interface FilterState {
  search: string;
  courseType: string;
  term: string;
  professor: string;
  status: string;
  startDate: string;
  endDate: string;
}

interface CoursesFilterControlsProps {
  filters: FilterState;
  onFilterChange: (key: string, value: string) => void;
  onClearFilters: () => void;
  courseTypes: Array<{ value: string; label: string }>;
  terms: Array<{ value: string; label: string }>;
  professors: Array<{ value: string; label: string }>;
  getTranslation: (key: string, defaultValue: string) => string;
  showAdvanced?: boolean;
  onToggleAdvanced?: () => void;
}

const CoursesFilterControls: React.FC<CoursesFilterControlsProps> = ({
  filters,
  onFilterChange,
  onClearFilters,
  courseTypes,
  terms,
  professors,
  getTranslation,
  showAdvanced = false,
  onToggleAdvanced,
}) => {
  const hasActiveFilters = Object.values(filters).some(value => value !== '');

  const handleClearFilter = (key: string) => {
    onFilterChange(key, '');
  };

  return (
    <Paper elevation={2} sx={{ p: 3, mb: 3, borderRadius: 2 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <FilterIcon style={{ marginRight: 8 }} />
        <Typography variant="h6" sx={{ flexGrow: 1 }}>
          {getTranslation('courses.filters', 'Filters')}
        </Typography>
        {hasActiveFilters && (
          <Button
            variant="outlined"
            size="small"
            startIcon={<ClearIcon />}
            onClick={onClearFilters}
            sx={{ mr: 1 }}
          >
            {getTranslation('courses.clearFilters', 'Clear All')}
          </Button>
        )}
        {onToggleAdvanced && (
          <IconButton onClick={onToggleAdvanced} size="small">
            {showAdvanced ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        )}
      </Box>

      <Grid container spacing={2}>
        {/* Search */}
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label={getTranslation('courses.searchPlaceholder', 'Search courses...')}
            value={filters.search}
            onChange={(e) => onFilterChange('search', e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon style={{ marginRight: 8, opacity: 0.6 }} />,
              endAdornment: filters.search && (
                <IconButton size="small" onClick={() => handleClearFilter('search')}>
                  <ClearIcon />
                </IconButton>
              ),
            }}
            variant="outlined"
          />
        </Grid>

        {/* Course Type */}
        <Grid item xs={12} md={3}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>{getTranslation('courses.courseType', 'Course Type')}</InputLabel>
            <Select
              value={filters.courseType}
              onChange={(e) => onFilterChange('courseType', e.target.value)}
              label={getTranslation('courses.courseType', 'Course Type')}
            >
              <MenuItem value="">
                <em>{getTranslation('courses.allTypes', 'All Types')}</em>
              </MenuItem>
              {courseTypes.map((type) => (
                <MenuItem key={type.value} value={type.value}>
                  {type.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Status */}
        <Grid item xs={12} md={3}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>{getTranslation('courses.status', 'Status')}</InputLabel>
            <Select
              value={filters.status}
              onChange={(e) => onFilterChange('status', e.target.value)}
              label={getTranslation('courses.status', 'Status')}
            >
              <MenuItem value="">
                <em>{getTranslation('courses.allStatuses', 'All Statuses')}</em>
              </MenuItem>
              <MenuItem value="active">
                {getTranslation('courses.active', 'Active')}
              </MenuItem>
              <MenuItem value="inactive">
                {getTranslation('courses.inactive', 'Inactive')}
              </MenuItem>
              <MenuItem value="draft">
                {getTranslation('courses.draft', 'Draft')}
              </MenuItem>
              <MenuItem value="archived">
                {getTranslation('courses.archived', 'Archived')}
              </MenuItem>
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {/* Advanced Filters */}
      <Collapse in={showAdvanced}>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          {/* Term */}
          <Grid item xs={12} md={4}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>{getTranslation('courses.term', 'Term')}</InputLabel>
              <Select
                value={filters.term}
                onChange={(e) => onFilterChange('term', e.target.value)}
                label={getTranslation('courses.term', 'Term')}
              >
                <MenuItem value="">
                  <em>{getTranslation('courses.allTerms', 'All Terms')}</em>
                </MenuItem>
                {terms.map((term) => (
                  <MenuItem key={term.value} value={term.value}>
                    {term.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Professor */}
          <Grid item xs={12} md={4}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>{getTranslation('courses.professor', 'Professor')}</InputLabel>
              <Select
                value={filters.professor}
                onChange={(e) => onFilterChange('professor', e.target.value)}
                label={getTranslation('courses.professor', 'Professor')}
              >
                <MenuItem value="">
                  <em>{getTranslation('courses.allProfessors', 'All Professors')}</em>
                </MenuItem>
                {professors.map((prof) => (
                  <MenuItem key={prof.value} value={prof.value}>
                    {prof.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Date Range */}
          <Grid item xs={12} md={2}>
            <TextField
              fullWidth
              type="date"
              label={getTranslation('courses.startDate', 'Start Date')}
              value={filters.startDate}
              onChange={(e) => onFilterChange('startDate', e.target.value)}
              InputLabelProps={{ shrink: true }}
              variant="outlined"
            />
          </Grid>

          <Grid item xs={12} md={2}>
            <TextField
              fullWidth
              type="date"
              label={getTranslation('courses.endDate', 'End Date')}
              value={filters.endDate}
              onChange={(e) => onFilterChange('endDate', e.target.value)}
              InputLabelProps={{ shrink: true }}
              variant="outlined"
            />
          </Grid>
        </Grid>
      </Collapse>

      {/* Active Filter Chips */}
      {hasActiveFilters && (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
          {Object.entries(filters).map(([key, value]) => {
            if (!value) return null;
            
            let displayValue = value;
            if (key === 'courseType') {
              const type = courseTypes.find(t => t.value === value);
              displayValue = type ? type.label : value;
            } else if (key === 'term') {
              const term = terms.find(t => t.value === value);
              displayValue = term ? term.label : value;
            } else if (key === 'professor') {
              const prof = professors.find(p => p.value === value);
              displayValue = prof ? prof.label : value;
            }

            return (
              <Chip
                key={key}
                label={`${getTranslation(`courses.${key}`, key)}: ${displayValue}`}
                onDelete={() => handleClearFilter(key)}
                variant="outlined"
                size="small"
              />
            );
          })}
        </Box>
      )}
    </Paper>
  );
};

export default CoursesFilterControls;

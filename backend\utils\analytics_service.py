"""
Enhanced Analytics Service with Offline Capabilities
Provides comprehensive analytics for the North Star University platform
"""

from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db.models import Count, Avg, Q, F
from django.core.cache import cache
from datetime import datetime, timedelta
import json
from typing import Dict, List, Any

User = get_user_model()

class AnalyticsService:
    """Service for collecting and analyzing platform usage data"""
    
    def __init__(self):
        self.cache_timeout = 3600  # 1 hour
    
    def get_dashboard_analytics(self, user=None) -> Dict[str, Any]:
        """Get comprehensive dashboard analytics"""
        cache_key = f"dashboard_analytics_{user.id if user else 'global'}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            # User analytics
            total_users = User.objects.count()
            active_users_week = User.objects.filter(
                last_login__gte=timezone.now() - timedelta(days=7)
            ).count()
            
            # Course analytics
            from courses.models import Course, Enrollment
            total_courses = Course.objects.count()
            active_courses = Course.objects.filter(is_active=True).count()
            
            # Enrollment analytics
            total_enrollments = Enrollment.objects.count()
            new_enrollments_week = Enrollment.objects.filter(
                enrolled_at__gte=timezone.now() - timedelta(days=7)
            ).count()
            
            # Assessment analytics
            from assessment.models import Assessment, AssessmentResponse
            total_assessments = Assessment.objects.count()
            assessment_responses_week = AssessmentResponse.objects.filter(
                submitted_at__gte=timezone.now() - timedelta(days=7)
            ).count()
            
            # Performance metrics
            avg_completion_rate = self._calculate_completion_rate()
            user_engagement = self._calculate_user_engagement()
            
            analytics_data = {
                'overview': {
                    'total_users': total_users,
                    'active_users_week': active_users_week,
                    'total_courses': total_courses,
                    'active_courses': active_courses,
                    'total_enrollments': total_enrollments,
                    'new_enrollments_week': new_enrollments_week,
                    'total_assessments': total_assessments,
                    'assessment_responses_week': assessment_responses_week,
                },
                'performance': {
                    'avg_completion_rate': avg_completion_rate,
                    'user_engagement_score': user_engagement,
                },
                'trends': self._get_usage_trends(),
                'user_distribution': self._get_user_distribution(),
                'course_analytics': self._get_course_analytics(),
                'assessment_analytics': self._get_assessment_analytics(),
                'timestamp': timezone.now().isoformat(),
            }
            
            # Cache the results
            cache.set(cache_key, analytics_data, self.cache_timeout)
            return analytics_data
            
        except Exception as e:
            # Return minimal data if there's an error
            return {
                'overview': {
                    'total_users': 0,
                    'active_users_week': 0,
                    'total_courses': 0,
                    'active_courses': 0,
                    'total_enrollments': 0,
                    'new_enrollments_week': 0,
                    'total_assessments': 0,
                    'assessment_responses_week': 0,
                },
                'error': str(e),
                'timestamp': timezone.now().isoformat(),
            }
    
    def _calculate_completion_rate(self) -> float:
        """Calculate average course completion rate"""
        try:
            from courses.models import CourseProgress
            completed = CourseProgress.objects.filter(is_completed=True).count()
            total = CourseProgress.objects.count()
            return (completed / total * 100) if total > 0 else 0
        except:
            return 0
    
    def _calculate_user_engagement(self) -> float:
        """Calculate user engagement score"""
        try:
            week_ago = timezone.now() - timedelta(days=7)
            active_users = User.objects.filter(last_login__gte=week_ago).count()
            total_users = User.objects.count()
            return (active_users / total_users * 100) if total_users > 0 else 0
        except:
            return 0
    
    def _get_usage_trends(self) -> List[Dict]:
        """Get usage trends over the past 30 days"""
        try:
            trends = []
            for i in range(30):
                date = timezone.now().date() - timedelta(days=i)
                daily_logins = User.objects.filter(
                    last_login__date=date
                ).count()
                trends.append({
                    'date': date.isoformat(),
                    'logins': daily_logins,
                })
            return list(reversed(trends))
        except:
            return []
    
    def _get_user_distribution(self) -> Dict:
        """Get user distribution by role"""
        try:
            distribution = User.objects.values('role').annotate(
                count=Count('id')
            )
            return {item['role']: item['count'] for item in distribution}
        except:
            return {}
    
    def _get_course_analytics(self) -> Dict:
        """Get detailed course analytics"""
        try:
            from courses.models import Course, Enrollment
            
            # Most popular courses
            popular_courses = Course.objects.annotate(
                enrollment_count=Count('enrollment')
            ).order_by('-enrollment_count')[:5]
            
            # Course completion rates
            completion_data = []
            for course in popular_courses:
                total_enrollments = course.enrollment.count()
                completed = course.enrollment.filter(
                    courseprogress__is_completed=True
                ).count()
                completion_rate = (completed / total_enrollments * 100) if total_enrollments > 0 else 0
                
                completion_data.append({
                    'course_id': course.id,
                    'course_name': course.name,
                    'total_enrollments': total_enrollments,
                    'completed': completed,
                    'completion_rate': completion_rate,
                })
            
            return {
                'popular_courses': completion_data,
                'course_categories': self._get_course_categories(),
            }
        except:
            return {}
    
    def _get_course_categories(self) -> Dict:
        """Get course distribution by category/department"""
        try:
            from courses.models import Course
            categories = Course.objects.values('department__name').annotate(
                count=Count('id')
            )
            return {item['department__name'] or 'Uncategorized': item['count'] for item in categories}
        except:
            return {}
    
    def _get_assessment_analytics(self) -> Dict:
        """Get assessment performance analytics"""
        try:
            from assessment.models import Assessment, AssessmentResponse
            
            # Average scores
            avg_score = AssessmentResponse.objects.aggregate(
                avg_score=Avg('score')
            )['avg_score'] or 0
            
            # Assessment difficulty distribution
            difficulty_dist = Assessment.objects.values('difficulty').annotate(
                count=Count('id')
            )
            
            # Recent assessment performance
            week_ago = timezone.now() - timedelta(days=7)
            recent_responses = AssessmentResponse.objects.filter(
                submitted_at__gte=week_ago
            ).aggregate(
                avg_score=Avg('score'),
                total_responses=Count('id')
            )
            
            return {
                'average_score': round(avg_score, 2),
                'difficulty_distribution': {
                    item['difficulty']: item['count'] for item in difficulty_dist
                },
                'recent_performance': {
                    'avg_score': round(recent_responses['avg_score'] or 0, 2),
                    'total_responses': recent_responses['total_responses']
                }
            }
        except:
            return {}
    
    def track_user_activity(self, user, activity_type: str, metadata: Dict = None):
        """Track user activity for analytics"""
        try:
            from utils.models import UserActivity
            UserActivity.objects.create(
                user=user,
                activity_type=activity_type,
                metadata=metadata or {},
                timestamp=timezone.now()
            )
        except:
            pass  # Fail silently to not break the main functionality
    
    def get_real_time_stats(self) -> Dict:
        """Get real-time statistics for dashboard"""
        try:
            # Users online in the last 5 minutes
            online_threshold = timezone.now() - timedelta(minutes=5)
            users_online = User.objects.filter(
                last_login__gte=online_threshold
            ).count()
            
            # Active sessions
            from django.contrib.sessions.models import Session
            active_sessions = Session.objects.filter(
                expire_date__gte=timezone.now()
            ).count()
            
            return {
                'users_online': users_online,
                'active_sessions': active_sessions,
                'timestamp': timezone.now().isoformat(),
            }
        except:
            return {
                'users_online': 0,
                'active_sessions': 0,
                'timestamp': timezone.now().isoformat(),
            }

# Singleton instance
analytics_service = AnalyticsService()

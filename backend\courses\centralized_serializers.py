"""
Centralized serializers for course-related models.
This file contains all serializers for course-related models to avoid duplication.
"""
from rest_framework import serializers


class CourseSerializer(serializers.Serializer):
    """Base serializer for Course model"""
    id = serializers.IntegerField(read_only=True)
    course_code = serializers.CharField(max_length=10)
    title = serializers.CharField(max_length=200)
    description = serializers.CharField(required=False)
    credits = serializers.IntegerField(default=3)
    capacity = serializers.IntegerField(default=30)
    is_active = serializers.BooleanField(default=True)
    is_published = serializers.BooleanField(default=False)
    is_enrolled = serializers.BooleanField(default=False)


class CourseDetailSerializer(serializers.Serializer):
    """Detailed serializer for Course model"""
    id = serializers.IntegerField(read_only=True)
    course_code = serializers.CharField(max_length=10)
    title = serializers.CharField(max_length=200)
    description = serializers.Char<PERSON>ield(required=False)
    credits = serializers.IntegerField(default=3)
    capacity = serializers.IntegerField(default=30)
    is_active = serializers.BooleanField(default=True)
    is_published = serializers.BooleanField(default=False)


class MaterialSerializer(serializers.Serializer):
    """Serializer for Material model"""
    id = serializers.IntegerField(read_only=True)
    title = serializers.CharField(max_length=200)
    description = serializers.CharField(required=False)
    file = serializers.FileField(required=False)
    created_at = serializers.DateTimeField(read_only=True)
    order = serializers.IntegerField(default=0)


class AttendanceSerializer(serializers.Serializer):
    """Serializer for Attendance model"""
    id = serializers.IntegerField(read_only=True)
    enrollment = serializers.IntegerField()
    date = serializers.DateField()
    is_present = serializers.BooleanField(default=False)
    topic = serializers.CharField(max_length=200, required=False)


class CourseProgressSerializer(serializers.Serializer):
    """Serializer for CourseProgress model"""
    id = serializers.IntegerField(read_only=True)
    user = serializers.IntegerField()
    course = serializers.IntegerField()
    completion_percentage = serializers.FloatField(default=0.0)
    is_completed = serializers.BooleanField(default=False)
    last_activity_date = serializers.DateTimeField(read_only=True)


class DepartmentSerializer(serializers.Serializer):
    """Serializer for Department model"""
    id = serializers.IntegerField(read_only=True)
    name = serializers.CharField(max_length=100)
    code = serializers.CharField(max_length=10)
    description = serializers.CharField(required=False)


class EnrollmentSerializer(serializers.Serializer):
    """Serializer for Enrollment model"""
    id = serializers.IntegerField(read_only=True)
    user = serializers.IntegerField()
    course = serializers.IntegerField()
    status = serializers.CharField(max_length=20, default='PENDING')
    enrollment_date = serializers.DateTimeField(read_only=True)
    is_completed = serializers.BooleanField(default=False)


class AssignmentSerializer(serializers.Serializer):
    """Placeholder serializer for Assignment model"""
    id = serializers.IntegerField(read_only=True)
    title = serializers.CharField(max_length=255)
    description = serializers.CharField(required=False)
    course = serializers.IntegerField()
    due_date = serializers.DateTimeField(required=False)
    points = serializers.IntegerField(default=100)
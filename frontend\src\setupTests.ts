// jest-dom adds custom jest matchers for asserting on DOM nodes.
import '@testing-library/jest-dom';

// Mock axios globally for tests
import axios from 'axios';
import mockAxios from 'jest-mock-axios';

// Mock all axios instances used in the project
jest.mock('axios', () => mockAxios);

// Mock the custom axios instance from config/axios.ts
jest.mock('./config/axios', () => ({
  __esModule: true,
  default: mockAxios,
  axiosInstance: mockAxios,
}));

// Mock the AI axios instance from config/aiAxios.ts
jest.mock('./config/aiAxios', () => ({
  __esModule: true,
  default: mockAxios,
  aiAxiosInstance: mockAxios,
}));

// Mock the ApiClient class from services/apiClient.ts
jest.mock('./services/apiClient', () => ({
  __esModule: true,
  default: class MockApiClient {
    constructor() {
      this.axiosInstance = mockAxios;
    }
    
    get = mockAxios.get;
    post = mockAxios.post;
    put = mockAxios.put;
    delete = mockAxios.delete;
    patch = mockAxios.patch;
  },
  ApiClient: class MockApiClient {
    constructor() {
      this.axiosInstance = mockAxios;
    }
    
    get = mockAxios.get;
    post = mockAxios.post;
    put = mockAxios.put;
    delete = mockAxios.delete;
    patch = mockAxios.patch;
  },
}));

beforeEach(() => {
  // Reset mock before each test
  mockAxios.reset();
});

// Mock console.error to reduce noise in test output
const originalError = console.error;
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is deprecated')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

name: Comprehensive Testing Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'
  POSTGRES_VERSION: '15'

jobs:
  # Backend Testing
  backend-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_northstar
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install coverage pytest-cov pytest-xdist pytest-mock
    
    - name: Set up environment variables
      run: |
        cd backend
        cp .env.example .env.test
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/test_northstar" >> .env.test
        echo "REDIS_URL=redis://localhost:6379/0" >> .env.test
        echo "SECRET_KEY=test-secret-key-for-ci" >> .env.test
        echo "DEBUG=False" >> .env.test
    
    - name: Run database migrations
      run: |
        cd backend
        python manage.py migrate --settings=settings.test
    
    - name: Run unit tests with coverage
      run: |
        cd backend
        coverage run -m pytest -v --tb=short --maxfail=5 -n auto
        coverage xml
        coverage report --show-missing
    
    - name: Run integration tests
      run: |
        cd backend
        pytest tests/integration/ -v --tb=short --maxfail=3
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage
    
    - name: Generate test report
      if: always()
      run: |
        cd backend
        pytest --html=test-report.html --self-contained-html
    
    - name: Upload test results
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: backend-test-results
        path: |
          backend/test-report.html
          backend/coverage.xml
          backend/htmlcov/

  # Frontend Testing
  frontend-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Run linting
      run: |
        cd frontend
        npm run lint
    
    - name: Run type checking
      run: |
        cd frontend
        npm run type-check
    
    - name: Run unit tests with coverage
      run: |
        cd frontend
        npm run test:coverage
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage
    
    - name: Build application
      run: |
        cd frontend
        npm run build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: frontend-build
        path: frontend/dist/

  # End-to-End Testing
  e2e-tests:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_northstar
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install backend dependencies
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci
        npx playwright install --with-deps
    
    - name: Set up test environment
      run: |
        cd backend
        cp .env.example .env.test
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/test_northstar" >> .env.test
        echo "REDIS_URL=redis://localhost:6379/0" >> .env.test
        echo "SECRET_KEY=test-secret-key-for-ci" >> .env.test
        echo "DEBUG=False" >> .env.test
        python manage.py migrate --settings=settings.test
        python manage.py collectstatic --noinput --settings=settings.test
    
    - name: Start backend server
      run: |
        cd backend
        python manage.py runserver 8000 --settings=settings.test &
        sleep 10
    
    - name: Start frontend server
      run: |
        cd frontend
        npm run dev &
        sleep 15
    
    - name: Wait for services
      run: |
        timeout 60 bash -c 'until curl -f http://localhost:8000/api/health/; do sleep 2; done'
        timeout 60 bash -c 'until curl -f http://localhost:3000; do sleep 2; done'
    
    - name: Run E2E tests
      run: |
        cd frontend
        npx playwright test --reporter=html
    
    - name: Upload E2E test results
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: e2e-test-results
        path: |
          frontend/playwright-report/
          frontend/test-results/

  # Security Testing
  security-tests:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Bandit security scan (Python)
      run: |
        pip install bandit
        bandit -r backend/ -f json -o bandit-report.json || true
    
    - name: Run npm audit (Node.js)
      run: |
        cd frontend
        npm audit --audit-level=moderate --json > npm-audit-report.json || true
    
    - name: Run Semgrep security scan
      uses: returntocorp/semgrep-action@v1
      with:
        config: >-
          p/security-audit
          p/secrets
          p/owasp-top-ten
    
    - name: Upload security reports
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          frontend/npm-audit-report.json

  # Performance Testing
  performance-tests:
    runs-on: ubuntu-latest
    needs: [e2e-tests]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
    
    - name: Install Lighthouse CI
      run: npm install -g @lhci/cli
    
    - name: Download frontend build
      uses: actions/download-artifact@v3
      with:
        name: frontend-build
        path: frontend/dist/
    
    - name: Serve built application
      run: |
        npx serve -s frontend/dist -p 3000 &
        sleep 10
    
    - name: Run Lighthouse CI
      run: |
        lhci autorun --upload.target=temporary-public-storage
    
    - name: Upload Lighthouse reports
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: lighthouse-reports
        path: .lighthouseci/

  # Code Quality Analysis
  code-quality:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Shallow clones should be disabled for better analysis
    
    - name: SonarCloud Scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
    
    - name: Run CodeQL Analysis
      uses: github/codeql-action/init@v2
      with:
        languages: javascript, python
    
    - name: Autobuild
      uses: github/codeql-action/autobuild@v2
    
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2

  # Test Results Summary
  test-summary:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, e2e-tests, security-tests, performance-tests]
    if: always()
    
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v3
    
    - name: Generate test summary
      run: |
        echo "# Test Results Summary" > test-summary.md
        echo "" >> test-summary.md
        echo "## Backend Tests" >> test-summary.md
        echo "- Unit Tests: ${{ needs.backend-tests.result }}" >> test-summary.md
        echo "- Integration Tests: ${{ needs.backend-tests.result }}" >> test-summary.md
        echo "" >> test-summary.md
        echo "## Frontend Tests" >> test-summary.md
        echo "- Unit Tests: ${{ needs.frontend-tests.result }}" >> test-summary.md
        echo "- Build: ${{ needs.frontend-tests.result }}" >> test-summary.md
        echo "" >> test-summary.md
        echo "## End-to-End Tests" >> test-summary.md
        echo "- E2E Tests: ${{ needs.e2e-tests.result }}" >> test-summary.md
        echo "" >> test-summary.md
        echo "## Security Tests" >> test-summary.md
        echo "- Security Scan: ${{ needs.security-tests.result }}" >> test-summary.md
        echo "" >> test-summary.md
        echo "## Performance Tests" >> test-summary.md
        echo "- Lighthouse: ${{ needs.performance-tests.result }}" >> test-summary.md
    
    - name: Comment PR with test results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const summary = fs.readFileSync('test-summary.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: summary
          });

  # Deployment (only on main branch)
  deploy:
    runs-on: ubuntu-latest
    needs: [test-summary]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your deployment commands here
    
    - name: Run smoke tests
      run: |
        echo "Running smoke tests on staging..."
        # Add smoke test commands here
    
    - name: Deploy to production
      if: success()
      run: |
        echo "Deploying to production environment..."
        # Add production deployment commands here

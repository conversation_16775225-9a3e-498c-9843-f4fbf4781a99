/**
 * Frontend Security Service
 * 
 * Provides client-side security features:
 * - Password strength validation
 * - Session security monitoring
 * - CSRF protection
 * - Security event logging
 * - Threat detection
 */

import CryptoJS from 'crypto-js';
import { API_ENDPOINTS } from '../config/api';
import axiosInstance from '../config/axios';

interface PasswordValidationResult {
  isValid: boolean;
  score: number;
  issues: string[];
  suggestions: string[];
}

interface SecurityMetrics {
  loginAttempts: number;
  sessionDuration: number;
  lastActivity: number;
  securityScore: number;
  threatLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

interface SessionFingerprint {
  userAgent: string;
  screen: string;
  timezone: string;
  language: string;
  platform: string;
  hash: string;
}

class SecurityService {
  private securityMetrics: SecurityMetrics = {
    loginAttempts: 0,
    sessionDuration: 0,
    lastActivity: Date.now(),
    securityScore: 50,
    threatLevel: 'LOW'
  };

  private sessionFingerprint: SessionFingerprint | null = null;
  private activityTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeSecurityMonitoring();
  }

  /**
   * Initialize security monitoring
   */
  private initializeSecurityMonitoring(): void {
    // Create session fingerprint
    this.sessionFingerprint = this.createSessionFingerprint();

    // Monitor user activity
    this.startActivityMonitoring();

    // Check for session hijacking
    this.monitorSessionIntegrity();

    // Set up security event listeners
    this.setupSecurityEventListeners();
  }

  /**
   * Create unique session fingerprint
   */
  private createSessionFingerprint(): SessionFingerprint {
    const userAgent = navigator.userAgent;
    const screen = `${window.screen.width}x${window.screen.height}x${window.screen.colorDepth}`;
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const language = navigator.language;
    const platform = navigator.platform;

    const fingerprintData = `${userAgent}:${screen}:${timezone}:${language}:${platform}`;
    const hash = CryptoJS.SHA256(fingerprintData).toString();

    return {
      userAgent,
      screen,
      timezone,
      language,
      platform,
      hash
    };
  }

  /**
   * Validate password strength
   */
  public validatePasswordStrength(password: string, userData?: any): PasswordValidationResult {
    const issues: string[] = [];
    const suggestions: string[] = [];
    let score = 0;

    // Length check
    if (password.length < 12) {
      issues.push('Password must be at least 12 characters long');
      suggestions.push('Use a longer password (12+ characters)');
    } else {
      score += 20;
    }

    // Character variety
    const hasLower = /[a-z]/.test(password);
    const hasUpper = /[A-Z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    if (!hasLower) {
      issues.push('Password must contain lowercase letters');
      suggestions.push('Add lowercase letters (a-z)');
    } else {
      score += 15;
    }

    if (!hasUpper) {
      issues.push('Password must contain uppercase letters');
      suggestions.push('Add uppercase letters (A-Z)');
    } else {
      score += 15;
    }

    if (!hasNumber) {
      issues.push('Password must contain numbers');
      suggestions.push('Add numbers (0-9)');
    } else {
      score += 15;
    }

    if (!hasSpecial) {
      issues.push('Password must contain special characters');
      suggestions.push('Add special characters (!@#$%^&*)');
    } else {
      score += 15;
    }

    // Common patterns
    const commonPatterns = [
      /(.)\1{2,}/,  // Repeated characters
      /123|234|345|456|567|678|789|890/,  // Sequential numbers
      /abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz/i  // Sequential letters
    ];

    for (const pattern of commonPatterns) {
      if (pattern.test(password)) {
        issues.push('Password contains predictable patterns');
        suggestions.push('Avoid sequential or repeated characters');
        score -= 10;
        break;
      }
    }

    // Personal information check
    if (userData) {
      const personalInfo = [
        userData.username?.toLowerCase(),
        userData.email?.split('@')[0]?.toLowerCase(),
        userData.firstName?.toLowerCase(),
        userData.lastName?.toLowerCase()
      ].filter(Boolean);

      for (const info of personalInfo) {
        if (info && password.toLowerCase().includes(info)) {
          issues.push('Password should not contain personal information');
          suggestions.push('Avoid using your name, username, or email in the password');
          score -= 15;
          break;
        }
      }
    }

    // Common passwords
    const commonPasswords = [
      'password', '123456', 'password123', 'admin', 'qwerty',
      'letmein', 'welcome', 'monkey', '1234567890', 'abc123'
    ];

    if (commonPasswords.includes(password.toLowerCase())) {
      issues.push('Password is too common');
      suggestions.push('Use a unique password that is not commonly used');
      score -= 20;
    }

    // Entropy calculation (simplified)
    const entropy = this.calculateEntropy(password);
    if (entropy < 50) {
      issues.push('Password lacks sufficient randomness');
      suggestions.push('Use a more random combination of characters');
    } else {
      score += 20;
    }

    score = Math.max(0, Math.min(100, score));

    return {
      isValid: issues.length === 0 && score >= 70,
      score,
      issues,
      suggestions
    };
  }

  /**
   * Calculate password entropy
   */
  private calculateEntropy(password: string): number {
    let charsetSize = 0;

    if (/[a-z]/.test(password)) charsetSize += 26;
    if (/[A-Z]/.test(password)) charsetSize += 26;
    if (/\d/.test(password)) charsetSize += 10;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) charsetSize += 32;

    if (charsetSize === 0) return 0;

    return password.length * Math.log2(charsetSize);
  }

  /**
   * Start activity monitoring
   */
  private startActivityMonitoring(): void {
    const updateActivity = () => {
      this.securityMetrics.lastActivity = Date.now();
    };

    // Monitor various user activities
    ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    // Check for inactivity
    this.activityTimer = setInterval(() => {
      const inactiveTime = Date.now() - this.securityMetrics.lastActivity;
      
      if (inactiveTime > 30 * 60 * 1000) { // 30 minutes
        this.handleInactiveSession();
      }
    }, 60000); // Check every minute
  }

  /**
   * Monitor session integrity
   */
  private monitorSessionIntegrity(): void {
    setInterval(() => {
      const currentFingerprint = this.createSessionFingerprint();
      
      if (this.sessionFingerprint && currentFingerprint.hash !== this.sessionFingerprint.hash) {
        this.handleSessionIntegrityViolation();
      }
    }, 5 * 60 * 1000); // Check every 5 minutes
  }

  /**
   * Setup security event listeners
   */
  private setupSecurityEventListeners(): void {
    // Detect developer tools
    let devtools = { open: false, orientation: null };
    const threshold = 160;

    setInterval(() => {
      if (window.outerHeight - window.innerHeight > threshold || 
          window.outerWidth - window.innerWidth > threshold) {
        if (!devtools.open) {
          devtools.open = true;
          this.logSecurityEvent('DEVTOOLS_OPENED', { timestamp: Date.now() });
        }
      } else {
        devtools.open = false;
      }
    }, 500);

    // Detect right-click attempts
    document.addEventListener('contextmenu', (e) => {
      this.logSecurityEvent('RIGHT_CLICK_ATTEMPT', {
        element: e.target?.tagName,
        timestamp: Date.now()
      });
    });

    // Detect copy attempts on sensitive content
    document.addEventListener('copy', (e) => {
      const selection = window.getSelection()?.toString();
      if (selection && selection.length > 50) {
        this.logSecurityEvent('LARGE_CONTENT_COPY', {
          length: selection.length,
          timestamp: Date.now()
        });
      }
    });
  }

  /**
   * Handle inactive session
   */
  private handleInactiveSession(): void {
    this.logSecurityEvent('SESSION_INACTIVE', {
      duration: Date.now() - this.securityMetrics.lastActivity
    });

    // Could trigger automatic logout here
    console.warn('Session has been inactive for 30 minutes');
  }

  /**
   * Handle session integrity violation
   */
  private handleSessionIntegrityViolation(): void {
    this.logSecurityEvent('SESSION_INTEGRITY_VIOLATION', {
      originalFingerprint: this.sessionFingerprint?.hash,
      currentFingerprint: this.createSessionFingerprint().hash
    });

    // Could force re-authentication here
    console.error('Session integrity violation detected');
  }

  /**
   * Log security event
   */
  private logSecurityEvent(eventType: string, details: any): void {
    const event = {
      type: eventType,
      timestamp: Date.now(),
      sessionId: this.sessionFingerprint?.hash,
      details,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Store locally for batch sending
    const events = JSON.parse(localStorage.getItem('security_events') || '[]');
    events.push(event);
    
    // Keep only last 100 events
    if (events.length > 100) {
      events.splice(0, events.length - 100);
    }
    
    localStorage.setItem('security_events', JSON.stringify(events));

    // Send to server if critical
    if (['SESSION_INTEGRITY_VIOLATION', 'DEVTOOLS_OPENED'].includes(eventType)) {
      this.sendSecurityEvent(event);
    }
  }

  /**
   * Send security event to server
   */
  private async sendSecurityEvent(event: any): Promise<void> {
    try {
      await axiosInstance.post('/api/v1/security/events/', event);
    } catch (error) {
      console.error('Failed to send security event:', error);
    }
  }

  /**
   * Get security metrics
   */
  public getSecurityMetrics(): SecurityMetrics {
    return { ...this.securityMetrics };
  }

  /**
   * Calculate security score
   */
  public calculateSecurityScore(): number {
    let score = 50; // Base score

    // Session integrity
    if (this.sessionFingerprint) {
      score += 20;
    }

    // Activity monitoring
    const inactiveTime = Date.now() - this.securityMetrics.lastActivity;
    if (inactiveTime < 5 * 60 * 1000) { // Active in last 5 minutes
      score += 15;
    }

    // HTTPS check
    if (window.location.protocol === 'https:') {
      score += 15;
    }

    this.securityMetrics.securityScore = Math.min(100, score);
    return this.securityMetrics.securityScore;
  }

  /**
   * Cleanup security monitoring
   */
  public cleanup(): void {
    if (this.activityTimer) {
      clearInterval(this.activityTimer);
    }
  }

  /**
   * Generate CSRF token
   */
  public generateCSRFToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Validate email format
   */
  public validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Sanitize input to prevent XSS
   */
  public sanitizeInput(input: string): string {
    const div = document.createElement('div');
    div.textContent = input;
    return div.innerHTML;
  }
}

// Export singleton instance
export const securityService = new SecurityService();
export default securityService;

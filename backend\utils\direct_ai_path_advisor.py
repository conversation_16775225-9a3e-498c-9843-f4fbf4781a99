import jsonfrom.ai_prompt_templatesimportadd_prompt_fragmentfrom.ai_service_utilsimport(StandardizedAIServiceget_ai_loggerstandardized_json_parser)logger=get_ai_logger("direct_ai_path_advisor")classDirectAIPathAdvisor(StandardizedAIService):"""AdirectimplementationoftheAIpathadvisorusingtheconsolidatedAIservice.ThisprovidesstandardizedAIserviceaccessforpathrecommendations."""def__init__(self):"""InitializetheAIpathadvisorwithstandardizedservice"""super().__init__("direct_ai_path_advisor")defget_recommendation(selfquestionsanswers):"""GenerateanAIrecommendationbasedonthestudent'sanswersArgs:questions(list):Listofassessmentquestionsanswers(dict):DictionarymappingquestionindicestoselectedpathvaluesReturns:dict:Structuredrecommendationwithpathconfidencestrengthsareastodeveloppersonalizedadviceandalternativepath"""try:#Formatthequestionsandanswersforthepromptquestions_and_answers=[]foridxquestioninenumerate(questions):answer_path=answers.get(str(idx))ifanswer_path:questions_and_answers.append(f"Q{idx+1}:{question['question']}")questions_and_answers.append(f"A{idx+1}:{answer_path}")formatted_qa="\n".join(questions_and_answers)#Createthepromptprompt=f"""Youareaprofessionaleducationaladvisorataleadingonlinelearningplatform.Yourroleistohelpstudentsdiscovertheirideallearningpathbasedontheirinterestsskillsandcareergoals.#STUDENTASSESSMENTRESPONSESThestudenthascompletedourassessmentquestionnaire.Herearetheirresponses:{formatted_qa}#AVAILABLELEARNINGPATHSANDCOURSESOurplatformoffersfourspecializedlearningpathseachwithastructuredcurriculum:##1.Programming&SoftwareDevelopmentThispathisidealforstudentswhoenjoyproblem-solvinglogicalthinkingandbuildingdigitalsolutions.-IntroductiontoProgramming:FundamentalsofprogrammingusingPythonwithhands-onprojects-WebDevelopmentFundamentals:CreatingresponsivewebsiteswithHTMLCSSandJavaScript-Full-StackDevelopment:BuildingcompletewebapplicationswithReactandNode.js-MobileAppDevelopment:Developingcross-platformmobileappswithReactNative-AIandMachineLearning:ImplementingmachinelearningalgorithmswithPythonandTensorFlow-Careeroutcomes:SoftwareDeveloperWebDeveloperMobileAppDeveloperAIEngineer##2.Cybersecurity&InformationProtectionThispathisidealforstudentswhoaredetail-orientedanalyticalandinterestedinprotectingdigitalassets.-CybersecurityFundamentals:Coreconceptsofinformationsecurityandthreatanalysis-NetworkSecurity:Networkarchitecturevulnerabilitiesandprotectionstrategies-EthicalHacking:Penetrationtestingandvulnerabilityassessmenttechniques-SecurityOperations:Securitymonitoringincidentresponseandthreathunting-AdvancedThreatDefense:Strategiesforprotectingagainstsophisticatedcyberattacks-Careeroutcomes:SecurityAnalystPenetrationTesterSecurityEngineerComplianceSpecialist##3.Finance&InvestmentAnalysisThispathisidealforstudentswhoareanalyticaldetail-orientedandinterestedinfinancialmarkets.-FinancialFundamentals:Coreconceptsoffinancialanalysisandmarketdynamics-InvestmentStrategies:Portfoliomanagementandinvestmentapproachoptimization-FinancialModeling:Buildingsophisticatedmodelsforanalysisandforecasting-RiskManagement:Identifyingandmitigatingfinancialrisks-AdvancedFinancialAnalysis:Complextechniquesforfinancialdecision-making-Careeroutcomes:FinancialAnalystInvestmentAdvisorRiskManagerFinancialConsultant##4.Marketing&BrandStrategyThispathisidealforstudentswhoarecreativecommunication-orientedandinterestedinconsumerbehavior.-MarketingFundamentals:Coreconceptsofmarketingstrategyandaudienceanalysis-DigitalMarketing:Creatingandoptimizingdigitalmarketingcampaigns-ContentMarketing:Developingcompellingcontentstrategiesacrossplatforms-MarketingAnalytics:Usingdatatomeasureandoptimizemarketingperformance-BrandStrategy:Buildingcomprehensivebrandstrategiesforlong-termsuccess-Careeroutcomes:MarketingSpecialistDigitalMarketerContentStrategistBrandManager#YOURTASKBasedonthestudent'sassessmentresponsesprovideapersonalizedlearningpathrecommendation.Yourrecommendationshouldbethoughtfulspecificandactionable.Includethefollowinginyouranalysis:1.Primaryrecommendedlearningpath(oneofthefourpathsabove)2.Confidencelevelinyourrecommendation(highmediumorlow)withbriefexplanation3.Atleast3specificstrengthsthestudenthasdemonstratedbasedontheirassessmentresponses4.Atleast2specificareasthestudentshoulddevelopfurthertosucceedintherecommendedpath5.Personalizedadvice(3-4sentences)thatreferencesspecificcoursesfromourofferingsthatwouldbenefitthisstudent6.AnalternativelearningpaththatmightalsobesuitablewithbriefexplanationFormatyourresponseasacleanJSONobjectwiththesekeys:path_recommendationconfidencestrengthsareas_to_developpersonalized_advicealternative_pathYourresponseshouldbevalidJSONonlywithnoadditionaltextorformatting."""#Definetheexpectedstructurefortheresponsestructure={"path_recommendation":"""confidence":"""strengths":[]"areas_to_develop":[]"personalized_advice":"""alternative_path":""}#Logthepromptfordebugginglogger.info(f"SendingprompttoAIservice:{prompt[:200]}...")#Addeducationalcontexttopromptprompt=add_prompt_fragment(prompt"educational_context")#UsestandardizedAIservicetogeneratestructuredcontentparsed_response=self.generate_structured_content(promptstructure)#Ifresponseisastringparseitusingstandardizedparserifisinstance(parsed_responsestr):parsed_response=standardized_json_parser(parsed_responsestructure)#Checkforparsingerrorsifisinstance(parsed_responsedict)and"error"inparsed_response:self.logger.warning(f"AIresponseparsingfailed:{parsed_response.get('error')}")returnself._get_default_recommendation(f"Parsingerror:{parsed_response.get('error')}")#Validateandfixresponsestructurerequired_keys=["path_recommendation""confidence""strengths""areas_to_develop""personalized_advice""alternative_path"]forkeyinrequired_keys:ifkeynotinparsed_response:self.logger.warning(f"Missingkeyinresponse:{key}")parsed_response[key]=(""ifkeynotin["strengths""areas_to_develop"]else[])#Ensurestrengthsandareas_to_developarelistsifnotisinstance(parsed_response["strengths"]list):parsed_response["strengths"]=[parsed_response["strengths"]]ifnotisinstance(parsed_response["areas_to_develop"]list):parsed_response["areas_to_develop"]=[parsed_response["areas_to_develop"]]self.logger.info("SuccessfullygeneratedAIpathrecommendation")returnparsed_responseexceptExceptionase:logger.error(f"ErrorgeneratingAIrecommendation:{str(e)}"exc_info=True)returnself._get_default_recommendation(str(e))def_get_default_recommendation(selferror_message):"""Returnadefaultrecommendationwitherrordetails"""return{"path_recommendation":"programming""confidence":"low""strengths":["problem-solving""technicalaptitude""analyticalthinking"]"areas_to_develop":["exploredifferentprogramminglanguages""buildpracticalprojects"]"personalized_advice":f"Weencounteredanissueanalyzingyourresponsesindetail.Basedontheavailableinformationprogrammingappearstobeagoodstartingpoint.Considerexploringdifferentprogramminglanguagesandbuildingsmallprojectstoseewhataspectsyouenjoymost.Errordetails:{error_message[:100]}""alternative_path":"cybersecurity"}
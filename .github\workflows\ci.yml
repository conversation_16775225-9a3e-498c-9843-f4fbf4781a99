name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'

jobs:
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: <PERSON>ache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install coverage pytest-cov

    - name: Set up environment variables
      run: |
        echo "DEBUG=False" >> $GITHUB_ENV
        echo "SECRET_KEY=test-secret-key-for-ci" >> $GITHUB_ENV
        echo "DATABASE_URL=postgres://postgres:postgres@localhost:5432/test_db" >> $GITHUB_ENV
        echo "REDIS_URL=redis://localhost:6379/0" >> $GITHUB_ENV

    - name: Run Django migrations
      working-directory: ./backend
      run: |
        python manage.py migrate --settings=settings.test

    - name: Run backend tests with coverage
      working-directory: ./backend
      run: |
        coverage run -m pytest
        coverage report
        coverage xml

    - name: Upload backend coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage

    - name: Run Django check
      working-directory: ./backend
      run: |
        python manage.py check --settings=settings.test

    - name: Run Django collectstatic
      working-directory: ./backend
      run: |
        python manage.py collectstatic --noinput --settings=settings.test

  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install frontend dependencies
      working-directory: ./frontend
      run: npm ci

    - name: Run frontend linting
      working-directory: ./frontend
      run: npm run lint

    - name: Run frontend tests
      working-directory: ./frontend
      run: npm test -- --coverage --watchAll=false

    - name: Upload frontend coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/coverage-final.json
        flags: frontend
        name: frontend-coverage

    - name: Build frontend
      working-directory: ./frontend
      run: npm run build

    - name: Upload frontend build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: frontend-build
        path: frontend/dist

  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install Python dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install black flake8 isort mypy bandit safety

    - name: Run Black (Python formatter)
      working-directory: ./backend
      run: black --check .

    - name: Run isort (Python import sorter)
      working-directory: ./backend
      run: isort --check-only .

    - name: Run Flake8 (Python linter)
      working-directory: ./backend
      run: flake8 .

    - name: Run Bandit (Security linter)
      working-directory: ./backend
      run: bandit -r . -x tests/

    - name: Run Safety (Security vulnerability checker)
      working-directory: ./backend
      run: safety check

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: integration_test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Install backend dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Install frontend dependencies
      working-directory: ./frontend
      run: npm ci

    - name: Build frontend
      working-directory: ./frontend
      run: npm run build

    - name: Set up integration test environment
      run: |
        echo "DEBUG=False" >> $GITHUB_ENV
        echo "SECRET_KEY=integration-test-secret-key" >> $GITHUB_ENV
        echo "DATABASE_URL=postgres://postgres:postgres@localhost:5432/integration_test_db" >> $GITHUB_ENV

    - name: Run Django migrations
      working-directory: ./backend
      run: |
        python manage.py migrate --settings=settings.test

    - name: Run integration tests
      working-directory: ./backend
      run: |
        pytest -m integration

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, code-quality, integration-tests]
    if: github.ref == 'refs/heads/develop'
    
    environment:
      name: staging
      url: ${{ steps.deploy.outputs.url }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download frontend build artifacts
      uses: actions/download-artifact@v3
      with:
        name: frontend-build
        path: frontend/dist

    - name: Deploy to staging
      id: deploy
      run: |
        echo "Deploying to staging environment..."
        echo "url=https://staging.northstar-university.com" >> $GITHUB_OUTPUT
        # Add your deployment commands here

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, code-quality, integration-tests]
    if: github.ref == 'refs/heads/main'
    
    environment:
      name: production
      url: ${{ steps.deploy.outputs.url }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download frontend build artifacts
      uses: actions/download-artifact@v3
      with:
        name: frontend-build
        path: frontend/dist

    - name: Deploy to production
      id: deploy
      run: |
        echo "Deploying to production environment..."
        echo "url=https://northstar-university.com" >> $GITHUB_OUTPUT
        # Add your deployment commands here

  notify:
    name: Notify
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, code-quality]
    if: failure()

    steps:
    - name: Notify on failure
      run: |
        echo "CI/CD pipeline failed. Please check the logs."
        # Add notification logic here (Slack, email, etc.)

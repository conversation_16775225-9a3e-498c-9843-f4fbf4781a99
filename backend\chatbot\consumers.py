import json
import loggingfromchannels.dbimportdatabase_sync_to_asyncfromchannels.generic.websocketimportAsyncJsonWebsocketConsumerfrom.modelsimportChatConversationChatMessagelogger=logging.getLogger(__name__)classChatConsumer(AsyncJsonWebsocketConsumer):asyncdefconnect(self):try:self.user=self.scope["user"]ifnotself.user.is_authenticated:logger.warning(f"Unauthenticatedconnectionattempt")awaitself.close()return#Joinaroomforuser-specificupdatesself.room_group_name=f"chat_{self.user.id}"awaitself.channel_layer.group_add(self.room_group_nameself.channel_name)awaitself.accept()logger.info(f"WebSocketconnectedforuser{self.user.id}")exceptExceptionase:logger.error(f"ErrorinWebSocketconnection:{str(e)}")awaitself.close()asyncdefdisconnect(selfclose_code):try:ifhasattr(self"room_group_name"):awaitself.channel_layer.group_discard(self.room_group_nameself.channel_name)logger.info(f"WebSocketdisconnectedforuser{self.user.idifhasattr(self'user')else'unknown'}")exceptExceptionase:logger.error(f"ErrorinWebSocketdisconnect:{str(e)}")asyncdefreceive_json(selfcontent):try:message_type=content.get("type")ifmessage_type=="typing":awaitself.channel_layer.group_send(self.room_group_name{"type":"typing_status""is_typing":content.get("is_typing"False)"conversation_id":content.get("conversation_id")})elifmessage_type=="message":awaitself.handle_chat_message(content)exceptExceptionase:logger.error(f"ErrorprocessingWebSocketmessage:{str(e)}")awaitself.send_json({"type":"error""message":"Failedtoprocessmessage"})asyncdeftyping_status(selfevent):try:awaitself.send_json({"type":"typing_status""is_typing":event["is_typing"]"conversation_id":event["conversation_id"]})exceptExceptionase:logger.error(f"Errorsendingtypingstatus:{str(e)}")asyncdefchat_message(selfevent):try:awaitself.send_json({"type":"chat_message""message":event["message"]})exceptExceptionase:logger.error(f"Errorsendingchatmessage:{str(e)}")asyncdefmessage_status(selfevent):try:awaitself.send_json({"type":"message_status""message_id":event["message_id"]"status":event["status"]})exceptExceptionase:logger.error(f"Errorsendingmessagestatus:{str(e)}")@database_sync_to_asyncdefhandle_chat_message(selfcontent):try:conversation_id=content.get("conversation_id")message_content=content.get("message")ifnotmessage_content:raiseValueError("Messagecontentisrequired")conversation=(ChatConversation.objects.get(id=conversation_id)ifconversation_idelseNone)message=ChatMessage.objects.create(conversation=conversationcontent=message_contentrole="user")returnmessageexceptExceptionase:logger.error(f"Errorhandlingchatmessage:{str(e)}")raise
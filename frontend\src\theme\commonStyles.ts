/**
 * Common Styles
 *
 * This file contains reusable style objects that can be used across the application
 * to ensure consistent styling and reduce duplication.
 *
 * This is the central location for all common styles in the application.
 * It consolidates styles that were previously duplicated across components.
 */
import { Theme, alpha } from '@mui/material/styles';

// Empty state styles
export const emptyStateStyles = (theme: Theme) => ({
  p: 4,
  textAlign: 'center',
  bgcolor: alpha(theme.palette.primary.light, 0.05),
  borderRadius: 2,
  border: `1px dashed ${alpha(theme.palette.primary.main, 0.3)}`,
});

// Empty state image styles
export const emptyStateImageStyles = {
  width: '120px',
  height: '120px',
  marginBottom: '16px',
  opacity: 0.7,
};

// Fill-in-blank text styles
export const fillInBlankStyles = (theme: Theme) => ({
  padding: '0 8px',
  margin: '0 4px',
  borderBottom: `2px solid ${theme.palette.primary.main}`,
  fontWeight: 'bold',
  color: theme.palette.primary.main,
});

// Centered flex container
export const centeredFlexStyles = {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
};

// Card content container
export const cardContentStyles = (theme: Theme) => ({
  p: 3,
  bgcolor: 'background.paper',
  borderRadius: 2,
  border: '1px solid',
  borderColor: 'divider',
  mb: 3,
});

// Text editor styles
export const textEditorStyles = {
  width: '100%',
  minHeight: 300,
  maxHeight: 600,
  padding: 16,
  fontFamily: 'monospace',
  fontSize: '14px',
  border: 'none',
  outline: 'none',
  resize: 'vertical',
};

// Text preview styles
export const textPreviewStyles = (theme: Theme) => ({
  p: 2,
  minHeight: 300,
  maxHeight: 600,
  overflow: 'auto',
  bgcolor: alpha(theme.palette.background.paper, 0.7),
});

// Level color styles
export const getLevelColor = (level: number) => {
  switch (level) {
    case 1:
      return '#4caf50'; // Beginner - Green
    case 2:
      return '#2196f3'; // Intermediate - Blue
    case 3:
      return '#9c27b0'; // Advanced - Purple
    case 4:
      return '#f44336'; // Expert - Red
    default:
      return '#4caf50'; // Default to Beginner
  }
};

// Flashcard container styles
export const flashcardContainerStyles = {
  perspective: '1000px',
  width: '100%',
  height: '300px',
};

// Dark glass panel styles
export const darkGlassPanelStyles = (theme: Theme) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(3),
  borderRadius: theme.shape.borderRadius,
  background: 'rgba(18, 18, 18, 0.8)',
  backdropFilter: 'blur(10px)',
  border: '1px solid rgba(255, 255, 255, 0.1)',
});

// Glassmorphism card style
export const glassmorphismCardStyles = (theme: Theme) => ({
  backgroundColor: theme.palette.background.paper,
  backdropFilter: 'blur(10px)',
  WebkitBackdropFilter: 'blur(10px)',
  borderRadius: 2,
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: theme.shadows[4],
  transition: 'all 0.3s ease',
  '&:hover': {
    boxShadow: theme.shadows[8],
    transform: 'translateY(-4px)',
  },
});

// Gradient background style
export const gradientBackgroundStyles = (theme: Theme) => ({
  backgroundColor: theme.palette.background.paper,
});

// Page container style
export const pageContainerStyles = (theme: Theme) => ({
  padding: { xs: 2, sm: 3, md: 4 },
  maxWidth: '1200px',
  margin: '0 auto',
});

// Button with hover effect
export const buttonWithHoverEffectStyles = (theme: Theme) => ({
  borderRadius: 2,
  textTransform: 'none',
  fontWeight: 600,
  boxShadow: 'none',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: `0 4px 8px ${alpha(theme.palette.common.black, 0.1)}`,
  },
});

// Flex between style
export const flexBetweenStyles = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
};

// Truncate text style
export const truncateTextStyles = {
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
};

// Responsive text style
export const responsiveTextStyles = {
  fontSize: { xs: '0.875rem', sm: '1rem', md: '1.125rem' },
};

// Responsive heading style
export const responsiveHeadingStyles = {
  fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem', lg: '2.25rem' },
  fontWeight: 700,
  lineHeight: 1.2,
};

export default {
  emptyStateStyles,
  emptyStateImageStyles,
  fillInBlankStyles,
  centeredFlexStyles,
  cardContentStyles,
  textEditorStyles,
  textPreviewStyles,
  getLevelColor,
  flashcardContainerStyles,
  darkGlassPanelStyles,
  glassmorphismCardStyles,
  gradientBackgroundStyles,
  pageContainerStyles,
  buttonWithHoverEffectStyles,
  flexBetweenStyles,
  truncateTextStyles,
  responsiveTextStyles,
  responsiveHeadingStyles,
};

import apiClient from './apiClient';

// Types for predictive analytics
interface StudentRiskProfile {
  studentId: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  riskFactors: {
    academicPerformance: number;
    engagementLevel: number;
    attendanceRate: number;
    submissionTimeliness: number;
    socialInteraction: number;
  };
  predictions: {
    dropoutProbability: number;
    expectedGrade: string;
    completionTimeframe: number;
    interventionRecommendations: string[];
  };
  confidenceScore: number;
}

interface LearningPatternAnalysis {
  studentId: string;
  patterns: {
    preferredLearningTimes: number[];
    sessionDuration: number;
    contentPreferences: string[];
    difficultyProgression: number[];
    retentionCurve: number[];
  };
  insights: {
    optimalStudySchedule: string[];
    personalizedStrategies: string[];
    potentialChallenges: string[];
  };
}

interface CourseAnalytics {
  courseId: string;
  metrics: {
    enrollmentTrends: any[];
    completionRates: number;
    averageGrade: number;
    engagementMetrics: any;
    contentEffectiveness: any[];
  };
  predictions: {
    futureEnrollment: number[];
    expectedOutcomes: any;
    resourceRequirements: any;
  };
}

interface InstitutionDashboard {
  overview: {
    totalStudents: number;
    activeStudents: number;
    atRiskStudents: number;
    averagePerformance: number;
  };
  trends: {
    enrollmentTrend: any[];
    performanceTrend: any[];
    engagementTrend: any[];
    retentionTrend: any[];
  };
  alerts: {
    criticalAlerts: any[];
    warnings: any[];
    opportunities: any[];
  };
}

export class PredictiveAnalyticsService {
  private static instance: PredictiveAnalyticsService;
  private analyticsCache = new Map<string, any>();
  private cacheTTL = 300000; // 5 minutes

  static getInstance(): PredictiveAnalyticsService {
    if (!PredictiveAnalyticsService.instance) {
      PredictiveAnalyticsService.instance = new PredictiveAnalyticsService();
    }
    return PredictiveAnalyticsService.instance;
  }

  // Student risk analysis and early warning system
  async analyzeStudentRisk(studentId: string, courseId?: string): Promise<StudentRiskProfile> {
    const cacheKey = `risk-${studentId}-${courseId || 'all'}`;
    
    if (this.isCacheValid(cacheKey)) {
      return this.analyticsCache.get(cacheKey).data;
    }

    try {
      const response = await apiClient.post('/analytics/student-risk', {
        studentId,
        courseId,
        includeHistoricalData: true,
        modelVersion: 'v3.0'
      });

      const riskProfile = response.data;
      this.setCacheData(cacheKey, riskProfile);
      
      return riskProfile;
    } catch (error) {
      console.error('Error analyzing student risk:', error);
      throw error;
    }
  }

  // Learning pattern analysis for personalization
  async analyzeLearningPatterns(studentId: string): Promise<LearningPatternAnalysis> {
    const cacheKey = `patterns-${studentId}`;
    
    if (this.isCacheValid(cacheKey)) {
      return this.analyticsCache.get(cacheKey).data;
    }

    try {
      const response = await apiClient.post('/analytics/learning-patterns', {
        studentId,
        timeRange: 'last_6_months',
        includeComparativeAnalysis: true
      });

      const patterns = response.data;
      this.setCacheData(cacheKey, patterns);
      
      return patterns;
    } catch (error) {
      console.error('Error analyzing learning patterns:', error);
      throw error;
    }
  }

  // Predictive course analytics
  async analyzeCourseMetrics(courseId: string): Promise<CourseAnalytics> {
    const cacheKey = `course-${courseId}`;
    
    if (this.isCacheValid(cacheKey)) {
      return this.analyticsCache.get(cacheKey).data;
    }

    try {
      const response = await apiClient.get(`/analytics/course/${courseId}`, {
        params: {
          includePredictions: true,
          forecastPeriod: 12 // months
        }
      });

      const analytics = response.data;
      this.setCacheData(cacheKey, analytics);
      
      return analytics;
    } catch (error) {
      console.error('Error analyzing course metrics:', error);
      throw error;
    }
  }

  // Institution-wide dashboard analytics
  async getInstitutionDashboard(): Promise<InstitutionDashboard> {
    const cacheKey = 'institution-dashboard';
    
    if (this.isCacheValid(cacheKey)) {
      return this.analyticsCache.get(cacheKey).data;
    }

    try {
      const response = await apiClient.get('/analytics/institution-dashboard', {
        params: {
          includeAlerts: true,
          includeTrends: true,
          timeRange: 'last_12_months'
        }
      });

      const dashboard = response.data;
      this.setCacheData(cacheKey, dashboard);
      
      return dashboard;
    } catch (error) {
      console.error('Error fetching institution dashboard:', error);
      throw error;
    }
  }

  // Predictive success modeling
  async predictStudentSuccess(
    studentId: string,
    courseId: string,
    timeframe: number = 12
  ): Promise<{
    successProbability: number;
    expectedGrade: string;
    completionDate: string;
    riskFactors: string[];
    recommendations: string[];
  }> {
    try {
      const response = await apiClient.post('/analytics/predict-success', {
        studentId,
        courseId,
        timeframe,
        modelFeatures: [
          'academic_history',
          'engagement_metrics',
          'learning_style',
          'external_factors'
        ]
      });

      return response.data.prediction;
    } catch (error) {
      console.error('Error predicting student success:', error);
      throw error;
    }
  }

  // Learning outcome predictions
  async predictLearningOutcomes(
    courseId: string,
    studentCohort?: string[]
  ): Promise<{
    expectedOutcomes: any[];
    skillDevelopment: any[];
    knowledgeGains: any[];
    competencyAchievement: any[];
  }> {
    try {
      const response = await apiClient.post('/analytics/predict-outcomes', {
        courseId,
        studentCohort,
        outcomeMetrics: [
          'knowledge_retention',
          'skill_application',
          'competency_mastery',
          'learning_transfer'
        ]
      });

      return response.data.outcomes;
    } catch (error) {
      console.error('Error predicting learning outcomes:', error);
      throw error;
    }
  }

  // Resource optimization analytics
  async analyzeResourceOptimization(): Promise<{
    instructorWorkload: any[];
    classroomUtilization: any[];
    technologyUsage: any[];
    budgetOptimization: any[];
    recommendations: string[];
  }> {
    try {
      const response = await apiClient.get('/analytics/resource-optimization', {
        params: {
          includeForecasting: true,
          optimizationPeriod: 6 // months
        }
      });

      return response.data.optimization;
    } catch (error) {
      console.error('Error analyzing resource optimization:', error);
      throw error;
    }
  }

  // Content effectiveness analysis
  async analyzeContentEffectiveness(courseId: string): Promise<{
    contentRankings: any[];
    engagementMetrics: any[];
    learningImpact: any[];
    improvementSuggestions: string[];
  }> {
    try {
      const response = await apiClient.post('/analytics/content-effectiveness', {
        courseId,
        analysisDepth: 'comprehensive',
        includeComparisons: true
      });

      return response.data.effectiveness;
    } catch (error) {
      console.error('Error analyzing content effectiveness:', error);
      throw error;
    }
  }

  // Real-time performance monitoring
  async getPerformanceAlerts(): Promise<{
    criticalAlerts: any[];
    performanceWarnings: any[];
    opportunities: any[];
    systemHealth: any;
  }> {
    try {
      const response = await apiClient.get('/analytics/performance-alerts', {
        params: {
          severity: 'all',
          includeSystemHealth: true
        }
      });

      return response.data.alerts;
    } catch (error) {
      console.error('Error fetching performance alerts:', error);
      throw error;
    }
  }

  // Advanced reporting engine
  async generateCustomReport(
    reportConfig: {
      type: string;
      parameters: any;
      filters: any;
      timeRange: string;
      format: 'json' | 'csv' | 'pdf';
    }
  ): Promise<{
    reportId: string;
    downloadUrl?: string;
    data?: any;
  }> {
    try {
      const response = await apiClient.post('/analytics/custom-report', reportConfig);
      return response.data.report;
    } catch (error) {
      console.error('Error generating custom report:', error);
      throw error;
    }
  }

  // Comparative analytics
  async compareStudentPerformance(
    studentIds: string[],
    metrics: string[]
  ): Promise<{
    comparisons: any[];
    insights: string[];
    recommendations: any[];
  }> {
    try {
      const response = await apiClient.post('/analytics/compare-students', {
        studentIds,
        metrics,
        includeContextualFactors: true
      });

      return response.data.comparison;
    } catch (error) {
      console.error('Error comparing student performance:', error);
      throw error;
    }
  }

  // Trend analysis and forecasting
  async analyzeTrends(
    entity: 'student' | 'course' | 'institution',
    entityId: string,
    timeRange: string = '12_months'
  ): Promise<{
    historicalTrends: any[];
    currentState: any;
    forecasts: any[];
    trendAnalysis: string[];
  }> {
    try {
      const response = await apiClient.post('/analytics/trend-analysis', {
        entity,
        entityId,
        timeRange,
        includeForecast: true,
        forecastHorizon: 6 // months
      });

      return response.data.trends;
    } catch (error) {
      console.error('Error analyzing trends:', error);
      throw error;
    }
  }

  // Cache management methods
  private isCacheValid(key: string): boolean {
    const cached = this.analyticsCache.get(key);
    if (!cached) return false;
    
    return Date.now() - cached.timestamp < this.cacheTTL;
  }

  private setCacheData(key: string, data: any): void {
    this.analyticsCache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  // Clear cache
  clearCache(): void {
    this.analyticsCache.clear();
  }

  // Export analytics data
  async exportAnalyticsData(
    type: string,
    format: 'json' | 'csv' | 'excel',
    filters?: any
  ): Promise<{
    downloadUrl: string;
    fileName: string;
  }> {
    try {
      const response = await apiClient.post('/analytics/export', {
        type,
        format,
        filters,
        timestamp: new Date().toISOString()
      });

      return response.data.export;
    } catch (error) {
      console.error('Error exporting analytics data:', error);
      throw error;
    }
  }
}

export const predictiveAnalyticsService = PredictiveAnalyticsService.getInstance();

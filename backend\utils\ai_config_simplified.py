"""SimplifiedAIConfigurationManagerThismoduleprovidesacleanermoremaintainableapproachtoAIconfiguration."""import loggingimport osfromtypingimportDictAnyOptionalfrom django.confimportsettingsfrom django.core.cacheimportcachefromdataclassesimportdataclassasdictfromcryptography.fernetimportFernetimport jsonlogger=logging.getLogger(__name__)@dataclassclassAIConfig:"""StructuredAIconfigurationwithvalidation"""default_model:str='gemini-2.0-flash'temperature:float=0.7max_tokens:int=2048timeout:int=30000retries:int=3enable_fallback:bool=Trueenable_caching:bool=Truecache_ttl:int=300def__post_init__(self):"""Validateconfigurationvalues"""ifnot0<=self.temperature<=2:raiseValueError("Temperaturemustbebetween0and2")ifself.max_tokens<=0:raiseValueError("Maxtokensmustbepositive")ifself.timeout<=0:raiseValueError("Timeoutmustbepositive")classSecureAPIKeyManager:"""SecureAPIkeymanagementwithencryption"""def__init__(self):self.encryption_key=self._get_or_create_encryption_key()self.cipher=Fernet(self.encryption_key)def_get_or_create_encryption_key(self)->bytes:"""GetorcreateencryptionkeyforAPIkeys"""key=cache.get('ai_encryption_key')ifnotkey:key=Fernet.generate_key()cache.set('ai_encryption_key'keytimeout=None)returnkeydefstore_api_key(selfapi_key:str)->bool:"""SecurelystoreAPIkey"""try:encrypted_key=self.cipher.encrypt(api_key.encode())cache.set('encrypted_api_key'encrypted_keytimeout=None)returnTrueexceptExceptionase:logger.error(f"FailedtostoreAPIkey:{e}")returnFalsedefget_api_key(self)->Optional[str]:"""RetrieveanddecryptAPIkey"""try:encrypted_key=cache.get('encrypted_api_key')ifencrypted_key:returnself.cipher.decrypt(encrypted_key).decode()returnNoneexceptExceptionase:logger.error(f"FailedtoretrieveAPIkey:{e}")returnNoneclassSimplifiedAIConfigManager:"""SimplifiedAIconfigurationmanager"""def__init__(self):self.api_key_manager=SecureAPIKeyManager()self._config_cache_key='simplified_ai_config'defget_config(self)->AIConfig:"""GetcurrentAIconfiguration"""#Trycachefirstcached_config=cache.get(self._config_cache_key)ifcached_config:returnAIConfig(**cached_config)#Loadfromsettingsasfallbackconfig_dict=getattr(settings'AI_CONFIG'{})#FilteroutparametersthatAIConfigdoesn'tacceptvalid_params={}forkeyvalueinconfig_dict.items():ifkeyin['default_model''temperature''max_tokens''timeout''retries''enable_fallback''enable_caching''cache_ttl']:valid_params[key]=value#Skip'rate_limit'andothernested/unsupportedparametersconfig=AIConfig(**valid_params)#Cacheforfutureusecache.set(self._config_cache_keyasdict(config)timeout=300)returnconfigdefupdate_config(self**kwargs)->bool:"""UpdateAIconfiguration"""try:current_config=self.get_config()#Updateonlyprovidedfieldsforkeyvalueinkwargs.items():ifhasattr(current_configkey):setattr(current_configkeyvalue)#Validateupdatedconfigcurrent_config.__post_init__()#Storeincachecache.set(self._config_cache_keyasdict(current_config)timeout=300)#Cleardependentcachescache.delete('ai_service_instance')logger.info("AIconfigurationupdatedsuccessfully")returnTrueexceptExceptionase:logger.error(f"FailedtoupdateAIconfiguration:{e}")returnFalsedefget_api_key(self)->Optional[str]:"""GetAPIkeythroughsecuremanager"""returnself.api_key_manager.get_api_key()defupdate_api_key(selfapi_key:str)->bool:"""UpdateAPIkeythroughsecuremanager"""returnself.api_key_manager.store_api_key(api_key)#Singletoninstanceconfig_manager=SimplifiedAIConfigManager()
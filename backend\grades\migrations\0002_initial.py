# Generated by Django 4.2.7 on 2025-07-04 15:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("grades", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("courses", "0002_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="submission",
            name="user",
            field=models.ForeignKey(
                limit_choices_to={"role": "STUDENT"},
                on_delete=django.db.models.deletion.CASCADE,
                related_name="submissions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="grade",
            name="graded_by",
            field=models.ForeignKey(
                limit_choices_to={"role": "PROFESSOR"},
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="grades_given",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="grade",
            name="submission",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="grade",
                to="grades.submission",
            ),
        ),
        migrations.AddField(
            model_name="coursegrade",
            name="course",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="course_grades",
                to="courses.course",
            ),
        ),
        migrations.AddField(
            model_name="coursegrade",
            name="user",
            field=models.ForeignKey(
                limit_choices_to={"role": "STUDENT"},
                on_delete=django.db.models.deletion.CASCADE,
                related_name="course_grades",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="assignment",
            name="course",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="grade_assignments",
                to="courses.course",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="submission",
            unique_together={("user", "assignment")},
        ),
        migrations.AlterUniqueTogether(
            name="coursegrade",
            unique_together={("user", "course")},
        ),
    ]

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Grid,
  Chip,
  Button,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Paper,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  School as SchoolIcon,
  Psychology as BrainIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import { useSelector } from 'react-redux';
import { RootState } from '../app/store';
import multiAgentCourseRecommendationService, {
  MultiAgentRecommendationResponse,
  CourseRecommendation,
} from '../services/multiAgentCourseRecommendationService';

// 🎯 Multi-Agent Course Recommendations Component
// Shows AI-powered course recommendations using specialized agents

const MultiAgentCourseRecommendations: React.FC = () => {
  const [recommendations, setRecommendations] =
    useState<MultiAgentRecommendationResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [recommendationType, setRecommendationType] =
    useState<string>('role_based');
  const [agentsStatus, setAgentsStatus] = useState<any>(null);

  // Get user from Redux store
  const { user } = useSelector((state: RootState) => state.auth);
  const userRole = user?.role?.toUpperCase() || 'STUDENT';

  const recommendationTypes = [
    { value: 'role_based', label: 'Role-Based Recommendations', icon: '🎭' },
    { value: 'assessment_based', label: 'Assessment-Based', icon: '📊' },
    { value: 'subject_specific', label: 'Subject-Specific', icon: '📚' },
    { value: 'general', label: 'General Recommendations', icon: '🎯' },
  ];

  useEffect(() => {
    fetchRecommendations();
    fetchAgentsStatus();
  }, [recommendationType]);

  const fetchRecommendations = async () => {
    try {
      setLoading(true);
      setError(null);

      let response;
      switch (recommendationType) {
        case 'role_based':
          response =
            await multiAgentCourseRecommendationService.getRoleBasedRecommendations();
          break;
        case 'assessment_based':
          response =
            await multiAgentCourseRecommendationService.getAssessmentBasedRecommendations();
          break;
        case 'subject_specific':
          response =
            await multiAgentCourseRecommendationService.getSubjectSpecificRecommendations(
              'general'
            );
          break;
        default:
          response =
            await multiAgentCourseRecommendationService.getMultiAgentRecommendations();
      }

      setRecommendations(response);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to fetch recommendations'
      );
      console.error('Error fetching recommendations:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchAgentsStatus = async () => {
    try {
      const status =
        await multiAgentCourseRecommendationService.getAgentsStatus();
      setAgentsStatus(status);
    } catch (err) {
      console.error('Error fetching agents status:', err);
    }
  };

  const formatRecommendations = () => {
    if (!recommendations?.recommendations) return [];
    return multiAgentCourseRecommendationService.formatRecommendationsForDisplay(
      recommendations
    );
  };

  const getPriorityChipColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: 300,
        }}
      >
        <CircularProgress />
        <Typography variant='body1' sx={{ ml: 2 }}>
          Getting AI-powered course recommendations...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
        }}
      >
        <Typography variant='h4' gutterBottom>
          🤖 AI-Powered Course Recommendations
        </Typography>
        <Button
          variant='outlined'
          startIcon={<RefreshIcon />}
          onClick={fetchRecommendations}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {/* User Role and Agents Status */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant='h6' gutterBottom>
                👤 Your Profile
              </Typography>
              <Chip
                label={`${userRole} Role`}
                color='primary'
                icon={<SchoolIcon />}
                sx={{ mb: 1 }}
              />
              <Typography variant='body2' color='text.secondary'>
                Recommendations are personalized for your role and learning
                needs.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant='h6' gutterBottom>
                🤖 AI Agents Status
              </Typography>
              <Chip
                label={
                  agentsStatus?.agents_available
                    ? 'Agents Active'
                    : 'Fallback Mode'
                }
                color={agentsStatus?.agents_available ? 'success' : 'warning'}
                icon={<BrainIcon />}
                sx={{ mb: 1 }}
              />
              <Typography variant='body2' color='text.secondary'>
                {agentsStatus?.agents_available
                  ? `${agentsStatus.available_agents?.length || 0} AI agents available`
                  : 'Using fallback recommendation system'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recommendation Type Selector */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <FormControl fullWidth>
            <InputLabel>Recommendation Type</InputLabel>
            <Select
              value={recommendationType}
              label='Recommendation Type'
              onChange={e => setRecommendationType(e.target.value)}
            >
              {recommendationTypes.map(type => (
                <MenuItem key={type.value} value={type.value}>
                  {type.icon} {type.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert severity='error' sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Recommendations Display */}
      {recommendations && (
        <Box>
          {/* Summary */}
          <Paper
            sx={{
              p: 2,
              mb: 3,
              bgcolor: 'primary.light',
              color: 'primary.contrastText',
            }}
          >
            <Typography variant='h6' gutterBottom>
              📊 Recommendation Summary
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography variant='body2'>
                  <strong>Agents Used:</strong>{' '}
                  {recommendations.agents_used?.length || 0}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography variant='body2'>
                  <strong>Categories:</strong>{' '}
                  {Object.keys(recommendations.recommendations || {}).length}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography variant='body2'>
                  <strong>Mode:</strong>{' '}
                  {recommendations.fallback_used ? 'Fallback' : 'AI-Powered'}
                </Typography>
              </Grid>
            </Grid>
          </Paper>

          {/* Recommendations by Category */}
          {formatRecommendations().map((category, index) => (
            <Accordion key={index} sx={{ mb: 2 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box
                  sx={{ display: 'flex', alignItems: 'center', width: '100%' }}
                >
                  <Typography variant='h6' sx={{ mr: 2 }}>
                    {category.icon} {category.category}
                  </Typography>
                  <Chip
                    label={`${category.recommendations.length} recommendations`}
                    size='small'
                    color='primary'
                    variant='outlined'
                  />
                  <Box sx={{ flexGrow: 1 }} />
                  <Chip
                    label={`${Math.round(category.confidence * 100)}% confidence`}
                    size='small'
                    color='success'
                  />
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Typography
                  variant='body2'
                  color='text.secondary'
                  sx={{ mb: 2 }}
                >
                  <strong>AI Reasoning:</strong> {category.reasoning}
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Grid container spacing={2}>
                  {category.recommendations.map(
                    (rec: CourseRecommendation, recIndex: number) => (
                      <Grid item xs={12} md={6} key={recIndex}>
                        <Card variant='outlined'>
                          <CardContent>
                            <Box
                              sx={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'flex-start',
                                mb: 1,
                              }}
                            >
                              <Typography variant='h6' component='div'>
                                {rec.title}
                              </Typography>
                              <Chip
                                label={rec.priority}
                                size='small'
                                color={
                                  getPriorityChipColor(rec.priority) as any
                                }
                              />
                            </Box>
                            <Typography
                              variant='body2'
                              color='text.secondary'
                              sx={{ mb: 2 }}
                            >
                              {rec.description}
                            </Typography>
                            <Box
                              sx={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                              }}
                            >
                              <Chip
                                label={rec.source}
                                size='small'
                                variant='outlined'
                              />
                              {rec.course_id && (
                                <Button
                                  size='small'
                                  variant='contained'
                                  startIcon={<TrendingUpIcon />}
                                >
                                  View Course
                                </Button>
                              )}
                            </Box>
                          </CardContent>
                        </Card>
                      </Grid>
                    )
                  )}
                </Grid>
              </AccordionDetails>
            </Accordion>
          ))}

          {/* Agents Used */}
          {recommendations.agents_used &&
            recommendations.agents_used.length > 0 && (
              <Card sx={{ mt: 3 }}>
                <CardContent>
                  <Typography variant='h6' gutterBottom>
                    🤖 AI Agents Consulted
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {recommendations.agents_used.map((agent, index) => (
                      <Chip
                        key={index}
                        label={agent
                          .replace(/_/g, ' ')
                          .replace(/\b\w/g, l => l.toUpperCase())}
                        icon={
                          <span>
                            {multiAgentCourseRecommendationService.getAgentIcon(
                              agent
                            )}
                          </span>
                        }
                        variant='outlined'
                        color='primary'
                      />
                    ))}
                  </Box>
                </CardContent>
              </Card>
            )}
        </Box>
      )}
    </Box>
  );
};

export default MultiAgentCourseRecommendations;

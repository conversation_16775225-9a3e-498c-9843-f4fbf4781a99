import loggingimport refromtypingimportDictListfrom django.utilsimport timezoneimportnumpyasnpfrom..modelsimportChatMessageUserLearningProfilelogger=logging.getLogger(__name__)classLearningPatternAnalyzer:"""Analyzesusermessagesforlearningpatternindicators"""def__init__(selfuser):self.user=userself.learning_profile_=UserLearningProfile.objects.get_or_create(user=user)defanalyze_message_patterns(selfmessage:str)->Dict:"""Analyzemessageforlearningstyleindicators"""#Gethistoricalcontextrecent_messages=ChatMessage.objects.filter(conversation__user=self.user).order_by("-created_at")[:10]#Extractpatternsfromcurrentmessagecurrent_patterns={"visual_preference":self._check_visual_preference(message)"example_preference":self._check_example_preference(message)"step_preference":self._check_step_preference(message)"detail_level":self._determine_detail_preference(message)"interaction_style":self._determine_interaction_style(message)"topic_complexity":self._assess_topic_complexity(message)"followup_questions":self._check_followup_patterns(message)}#Analyzehistoricalpatternshistorical_patterns=self._analyze_historical_patterns(recent_messages)#Combinecurrentandhistoricalpatternswithweightagecombined_patterns={}forkeyincurrent_patterns:#Givemoreweighttocurrentpatterns(0.7)vshistorical(0.3)combined_patterns[key]=0.7*current_patterns[key]+0.3*historical_patterns.get(keycurrent_patterns[key])returncombined_patternsdef_analyze_historical_patterns(selfmessages)->Dict:"""Analyzepatternsfromhistoricalmessages"""ifnotmessages:return{}patterns={"visual_preference":[]"example_preference":[]"step_preference":[]"detail_level":[]"topic_complexity":[]"followup_questions":[]}formsginmessages:content=msg.content#Checkiflearningindicatorsareinmetadataifmsg.metadataand"learning_indicators"inmsg.metadata:indicators=msg.metadata["learning_indicators"]forkeyinpatterns.keys():ifkeyinindicators:patterns[key].append(indicators[key])else:#Fallbacktoanalyzingcontentdirectlypatterns["visual_preference"].append(self._check_visual_preference(content))patterns["example_preference"].append(self._check_example_preference(content))patterns["step_preference"].append(self._check_step_preference(content))patterns["detail_level"].append(self._determine_detail_preference(content))patterns["topic_complexity"].append(self._assess_topic_complexity(content))patterns["followup_questions"].append(self._check_followup_patterns(content))#Calculateweightedmovingaveragewithmorerecentmessageshavinghigherweightweights=np.linspace(0.51.0len(messages))return{key:np.average(valuesweights=weights[:len(values)])forkeyvaluesinpatterns.items()ifvalues}defupdate_learning_profile(selfpatterns:Dict):"""Updateuser'slearningprofilebasedonnewpatterns"""#Updateinteractionpatternswithexponentialmovingaveragecurrent_patterns=self.learning_profile.interaction_patternsor{}alpha=0.3#Smoothingfactorforkeyvalueinpatterns.items():ifkeyincurrent_patterns:current_patterns[key]=(alpha*value+(1-alpha)*current_patterns[key])else:current_patterns[key]=valueself.learning_profile.interaction_patterns=current_patterns#Updatelearningstylepreferencesbasedonpatternsvisual_score=patterns["visual_preference"]example_score=patterns["example_preference"]step_score=patterns["step_preference"]#Determineprimarylearningstylestyle_scores={"VISUAL":visual_score"KINESTHETIC":example_score"READ_WRITE":patterns["detail_level"]"AUDITORY":patterns.get("interaction_style"0)}primary_style=max(style_scores.items()key=lambdax:x[1])[0]ifprimary_style!=self.learning_profile.primary_learning_style:self.learning_profile.secondary_learning_style=(self.learning_profile.primary_learning_style)self.learning_profile.primary_learning_style=primary_style#Updatepreferenceswiththresholdsself.learning_profile.prefers_step_by_step=step_score>0.5self.learning_profile.prefers_examples=example_score>0.5self.learning_profile.prefers_visual_aids=visual_score>0.5#Adjustcomprehensionlevelbasedonpatternscomplexity_score=patterns["topic_complexity"]detail_score=patterns["detail_level"]followup_score=patterns["followup_questions"]#Useaweightedcombinationoffactorscomprehension_score=(0.4*complexity_score+0.3*detail_score+0.3*followup_score)ifcomprehension_score>0.75:self.learning_profile.comprehension_level="ADVANCED"elifcomprehension_score<0.4:self.learning_profile.comprehension_level="BASIC"else:self.learning_profile.comprehension_level="INTERMEDIATE"self.learning_profile.save()defget_response_format_recommendations(self)->Dict:"""Getrecommendationsforformattingtheresponsebasedonlearningprofile"""patterns=self.learning_profile.interaction_patternsor{}recent_activity=ChatMessage.objects.filter(conversation__user=self.usercreated_at__gte=timezone.now()-timezone.timedelta(hours=1)).count()#Adjustchunksizebasedonrecentactivitybase_chunk_size=self._determine_chunk_size(patterns)ifrecent_activity>10:#Highactivitychunk_size=max(2base_chunk_size-1)#Smallerchunksforbetterfocuselse:chunk_size=base_chunk_sizereturn{"step_by_step":self.learning_profile.prefers_step_by_step"include_examples":self.learning_profile.prefers_examples"use_visual_aids":self.learning_profile.prefers_visual_aids"detail_level":self.learning_profile.comprehension_level"chunk_size":chunk_size"interaction_style":self._determine_optimal_interaction_style(patterns)}def_check_visual_preference(selfmessage:str)->float:"""Checkifmessageindicatespreferenceforvisuallearning"""visual_indicators=[(r"\b(show|display|see|look)\b.*?(?:how|what|diagram|picture)"2.0)(r"\b(diagram|picture|graph|chart|illustration)\b"1.5)(r"\b(visual|visually|draw|display)\b"1.0)(r"\b(color|highlight|mark)\b"0.8)(r"\b(view|preview|overview)\b"0.5)]returnself._calculate_weighted_indicators(messagevisual_indicators)def_check_example_preference(selfmessage:str)->float:"""Checkifmessageindicatespreferenceforexamples"""example_indicators=[(r"\b(example|instance|case\s+study)\b"2.0)(r"\b(demonstrate|show\s+how)\b"1.5)(r"\b(scenario|practice|try)\b"1.0)(r"(?:like|such\s+as)\s+(?:when|how|what)"0.8)(r"\b(sample|similar)\b"0.5)]returnself._calculate_weighted_indicators(messageexample_indicators)def_check_step_preference(selfmessage:str)->float:"""Checkifmessageindicatespreferenceforstep-by-stepguidance"""step_indicators=[(r"\b(step\s*by\s*step|walkthrough)\b"2.0)(r"\b(guide|process|procedure)\b"1.5)(r"\b(first|then|next|finally)\b"1.0)(r"\b(how\s+to|explain\s+how)\b"0.8)(r"\b(sequence|order|steps)\b"0.5)]returnself._calculate_weighted_indicators(messagestep_indicators)def_determine_detail_preference(selfmessage:str)->float:"""Determineuser'spreferencefordetaillevel"""detail_indicators=[(r"\b(detail|specific|exactly|precise)\b"2.0)(r"\b(explain|clarify|elaborate)\b"1.5)(r"\b(why|how\s+does|what\s+causes)\b"1.0)(r"\b(understand|meaning|concept)\b"0.8)(r"(?:more|further)\s+(?:information|details)"0.5)]returnself._calculate_weighted_indicators(messagedetail_indicators)def_determine_interaction_style(selfmessage:str)->float:"""Determineuser'sinteractionstylepreference"""collaborative_indicators=[(r"\b(help|assist|guide)\b.*?\bme\b"2.0)(r"\b(can|could)\b.*?\bwe\b"1.5)(r"\b(together|collaborate|discuss)\b"1.0)(r"\b(thoughts|opinion|feedback)\b"0.8)(r"\b(suggest|recommend)\b"0.5)]returnself._calculate_weighted_indicators(messagecollaborative_indicators)def_assess_topic_complexity(selfmessage:str)->float:"""Assessthecomplexityoftopicsuserisinterestedin"""#Countuniquetechnical/academictermsacademic_terms=set(re.findall(r"\b[A-Z][a-z]{2}\b"message))technical_terms=set(re.findall(r"\b[a-z]+(?:_[a-z]+)+\b"message))#Analyzesentencestructurecomplexitysentences=re.split(r"[.!?]+"message)avg_words_per_sentence=np.mean([len(s.split())forsinsentencesifs.strip()])#Calculatecomplexityscoreterm_score=min(1.0(len(academic_terms)+len(technical_terms))/10)structure_score=min(1.0avg_words_per_sentence/20)return0.6*term_score+0.4*structure_scoredef_check_followup_patterns(selfmessage:str)->float:"""Checkifthemessageisafollow-uporclarifyingquestion"""followup_indicators=[(r"\b(but|however|what\s+about)\b"2.0)(r"\b(follow[-]up|related)\b"1.5)(r"\b(also|additionally|furthermore)\b"1.0)(r"\b(and|or)\b.*?\?"0.8)(r"\b(clarify|explain\s+further)\b"0.5)]returnself._calculate_weighted_indicators(messagefollowup_indicators)def_calculate_weighted_indicators(selfmessage:strindicators:List[tuple])->float:"""Calculateweightedstrengthofindicatorsinamessage"""message=message.lower()total_weight=0max_weight=sum(weightfor_weightinindicators)forpatternweightinindicators:matches=len(re.findall(patternmessage))#Applydiminishingreturnstomultiplematchestotal_weight+=weight*min(matches3)/3returnmin(total_weight/max_weight1.0)def_determine_chunk_size(selfpatterns:Dict)->int:"""Determineoptimalchunksizebasedonpatterns"""base_size=3#Adjustbasedoncomprehensionlevelifself.learning_profile.comprehension_level=="ADVANCED":base_size+=2elifself.learning_profile.comprehension_level=="BASIC":base_size-=1#Adjustbasedonpatternsifpatterns.get("detail_level"0.5)>0.7:base_size+=1ifpatterns.get("step_preference"0.5)>0.7:base_size-=1returnmax(2min(base_size6))def_determine_optimal_interaction_style(selfpatterns:Dict)->str:"""Determinetheoptimalinteractionstylebasedonpatterns"""collaborative_score=patterns.get("interaction_style"0.5)exploration_score=patterns.get("followup_questions"0.5)ifcollaborative_score>0.7:return"collaborative"elifexploration_score>0.7:return"exploratory"else:return"practical"
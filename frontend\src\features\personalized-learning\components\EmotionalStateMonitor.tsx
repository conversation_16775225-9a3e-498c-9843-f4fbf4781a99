import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  Alert,
  IconButton,
  Tooltip,
  useTheme,
  alpha,
} from '@mui/material';
import {
  MdPsychology as PsychologyIcon,
  MdRefresh as RefreshIcon,
  MdVisibility as VisibilityIcon,
  MdVisibilityOff as VisibilityOffIcon,
} from 'react-icons/md';
import { EmotionalLearningState } from '../../../services/personalizedLearningService';
import emotionalIntelligenceService from '../../../services/emotionalIntelligenceService';

interface EmotionalStateMonitorProps {
  userId: string;
  interactionHistory: any[];
  onEmotionalStateChange?: (state: EmotionalLearningState) => void;
  compact?: boolean;
}

const EmotionalStateMonitor: React.FC<EmotionalStateMonitorProps> = ({
  userId,
  interactionHistory,
  onEmotionalStateChange,
  compact = false,
}) => {
  const theme = useTheme();
  const [emotionalState, setEmotionalState] = useState<EmotionalLearningState | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (interactionHistory.length > 0) {
      detectEmotionalState();
    }
  }, [interactionHistory, userId]);

  const detectEmotionalState = async () => {
    if (interactionHistory.length < 3) return; // Need some interaction data

    setLoading(true);
    try {
      const state = await emotionalIntelligenceService.detectEmotionalState(
        userId,
        interactionHistory
      );
      setEmotionalState(state);
      onEmotionalStateChange?.(state);
    } catch (error) {
      console.warn('Failed to detect emotional state:', error);
    } finally {
      setLoading(false);
    }
  };

  const getMoodColor = (mood: string) => {
    switch (mood) {
      case 'motivated': return theme.palette.success.main;
      case 'confident': return theme.palette.info.main;
      case 'curious': return theme.palette.secondary.main;
      case 'frustrated': return theme.palette.error.main;
      case 'anxious': return theme.palette.warning.main;
      default: return theme.palette.grey[500];
    }
  };

  const getMoodEmoji = (mood: string) => {
    switch (mood) {
      case 'motivated': return '🚀';
      case 'confident': return '😊';
      case 'curious': return '🤔';
      case 'frustrated': return '😤';
      case 'anxious': return '😰';
      default: return '😐';
    }
  };

  const getStressLevelColor = (level: number) => {
    if (level <= 3) return theme.palette.success.main;
    if (level <= 6) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const getConfidenceLevelColor = (level: number) => {
    if (level >= 7) return theme.palette.success.main;
    if (level >= 4) return theme.palette.info.main;
    return theme.palette.warning.main;
  };

  if (!emotionalState && !loading) {
    return null;
  }

  if (compact) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Tooltip title="Toggle emotional state monitor">
          <IconButton
            size="small"
            onClick={() => setIsVisible(!isVisible)}
            sx={{ color: emotionalState ? getMoodColor(emotionalState.current_mood) : 'grey.500' }}
          >
            {isVisible ? <VisibilityOffIcon /> : <VisibilityIcon />}
          </IconButton>
        </Tooltip>
        
        {isVisible && emotionalState && (
          <Chip
            icon={<span>{getMoodEmoji(emotionalState.current_mood)}</span>}
            label={`${emotionalState.current_mood} (${emotionalState.stress_level}/10 stress)`}
            size="small"
            sx={{
              backgroundColor: alpha(getMoodColor(emotionalState.current_mood), 0.1),
              color: getMoodColor(emotionalState.current_mood),
            }}
          />
        )}
      </Box>
    );
  }

  return (
    <Card
      sx={{
        mb: 2,
        borderRadius: 2,
        background: alpha(theme.palette.background.paper, 0.8),
        backdropFilter: 'blur(10px)',
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography
            variant="h6"
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            <PsychologyIcon sx={{ mr: 1, color: 'primary.main' }} />
            Emotional State Monitor
          </Typography>
          
          <Box>
            <Tooltip title="Refresh emotional state">
              <IconButton
                size="small"
                onClick={detectEmotionalState}
                disabled={loading}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Toggle visibility">
              <IconButton
                size="small"
                onClick={() => setIsVisible(!isVisible)}
              >
                {isVisible ? <VisibilityOffIcon /> : <VisibilityIcon />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {loading && (
          <Box sx={{ mb: 2 }}>
            <LinearProgress />
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
              Analyzing your learning state...
            </Typography>
          </Box>
        )}

        {isVisible && emotionalState && (
          <Box>
            {/* Current Mood */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Current Mood
              </Typography>
              <Chip
                icon={<span style={{ fontSize: '1.2em' }}>{getMoodEmoji(emotionalState.current_mood)}</span>}
                label={emotionalState.current_mood.charAt(0).toUpperCase() + emotionalState.current_mood.slice(1)}
                sx={{
                  backgroundColor: alpha(getMoodColor(emotionalState.current_mood), 0.1),
                  color: getMoodColor(emotionalState.current_mood),
                  fontWeight: 'bold',
                }}
              />
            </Box>

            {/* Stress Level */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Stress Level: {emotionalState.stress_level}/10
              </Typography>
              <LinearProgress
                variant="determinate"
                value={(emotionalState.stress_level / 10) * 100}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: alpha(theme.palette.grey[300], 0.3),
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: getStressLevelColor(emotionalState.stress_level),
                    borderRadius: 4,
                  },
                }}
              />
            </Box>

            {/* Confidence Level */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Confidence Level: {emotionalState.confidence_level}/10
              </Typography>
              <LinearProgress
                variant="determinate"
                value={(emotionalState.confidence_level / 10) * 100}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: alpha(theme.palette.grey[300], 0.3),
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: getConfidenceLevelColor(emotionalState.confidence_level),
                    borderRadius: 4,
                  },
                }}
              />
            </Box>

            {/* Engagement Level */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Engagement Level: {emotionalState.engagement_level}/10
              </Typography>
              <LinearProgress
                variant="determinate"
                value={(emotionalState.engagement_level / 10) * 100}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: alpha(theme.palette.grey[300], 0.3),
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: theme.palette.primary.main,
                    borderRadius: 4,
                  },
                }}
              />
            </Box>

            {/* Recommendations based on emotional state */}
            {(emotionalState.stress_level > 7 || emotionalState.confidence_level < 4) && (
              <Alert
                severity={emotionalState.stress_level > 7 ? 'warning' : 'info'}
                sx={{ mt: 2 }}
              >
                {emotionalState.stress_level > 7 && (
                  <Typography variant="body2">
                    High stress detected. Consider taking a short break or trying some relaxation techniques.
                  </Typography>
                )}
                {emotionalState.confidence_level < 4 && (
                  <Typography variant="body2">
                    Low confidence detected. Remember that learning takes time, and you're doing great!
                  </Typography>
                )}
              </Alert>
            )}

            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
              Last updated: {new Date(emotionalState.detected_at).toLocaleTimeString()}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default EmotionalStateMonitor;

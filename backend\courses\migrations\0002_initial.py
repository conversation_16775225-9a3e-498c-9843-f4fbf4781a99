# Generated by Django 4.2.7 on 2025-07-04 15:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("courses", "0001_initial"),
        ("core", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("assessment", "0003_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="skillassessment",
            name="student",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="enrollment",
            name="course",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="enrollments",
                to="courses.course",
            ),
        ),
        migrations.AddField(
            model_name="enrollment",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="enrollments",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="department",
            name="head",
            field=models.Foreign<PERSON>ey(
                blank=True,
                help_text="Department head (must be a professor)",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="departments_headed",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="coursesection",
            name="course",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="sections",
                to="courses.courseenhanced",
            ),
        ),
        migrations.AddField(
            model_name="coursesection",
            name="instructor",
            field=models.ForeignKey(
                blank=True,
                limit_choices_to={"role": "PROFESSOR"},
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="section_instructor",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="courseprerequisite",
            name="prerequisite_course",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="unlocks_course_relations",
                to="courses.courseenhanced",
            ),
        ),
        migrations.AddField(
            model_name="courseprerequisite",
            name="target_course",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="prerequisite_relations",
                to="courses.courseenhanced",
            ),
        ),
        migrations.AddField(
            model_name="courseenhanced",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                help_text="User who created this course",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="courses_created",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="courseenhanced",
            name="department",
            field=models.ForeignKey(
                blank=True,
                help_text="Department offering this course",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="department_courses",
                to="courses.department",
            ),
        ),
        migrations.AddField(
            model_name="courseenhanced",
            name="instructor",
            field=models.ForeignKey(
                blank=True,
                help_text="Primary instructor for this course",
                limit_choices_to={"role": "PROFESSOR"},
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="taught_courses",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="courseenhanced",
            name="last_modified_by",
            field=models.ForeignKey(
                blank=True,
                help_text="User who last modified this course",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="courses_modified",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="courseenhanced",
            name="next_level_courses",
            field=models.ManyToManyField(
                blank=True,
                help_text="Recommended courses to take after completing this course",
                related_name="previous_level_courses",
                to="courses.courseenhanced",
            ),
        ),
        migrations.AddField(
            model_name="courseenhanced",
            name="prerequisites",
            field=models.ManyToManyField(
                blank=True,
                help_text="Courses that must be completed before taking this course",
                related_name="unlocks_courses",
                to="courses.courseenhanced",
            ),
        ),
        migrations.AddField(
            model_name="courseenhanced",
            name="skills_developed",
            field=models.ManyToManyField(
                blank=True,
                help_text="Skills that will be developed in this course",
                related_name="developed_in_enhanced_courses",
                to="core.skill",
            ),
        ),
        migrations.AddField(
            model_name="courseenhanced",
            name="skills_required",
            field=models.ManyToManyField(
                blank=True,
                help_text="Skills needed to take this course",
                related_name="required_by_enhanced_courses",
                to="core.skill",
            ),
        ),
        migrations.AddField(
            model_name="course",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="course",
            name="department",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="courses",
                to="courses.department",
            ),
        ),
        migrations.AddField(
            model_name="course",
            name="instructor",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="courses_taught",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="course",
            name="next_level_courses",
            field=models.ManyToManyField(
                blank=True,
                help_text="Recommended courses to take after completing this course",
                related_name="previous_level_courses",
                to="courses.course",
            ),
        ),
        migrations.AddField(
            model_name="course",
            name="prerequisites",
            field=models.ManyToManyField(
                blank=True,
                help_text="Courses that must be completed before taking this course",
                related_name="course_unlocks",
                to="courses.course",
            ),
        ),
        migrations.AddField(
            model_name="course",
            name="skills_developed",
            field=models.ManyToManyField(
                blank=True,
                help_text="Skills that will be developed in this course",
                related_name="developed_in_courses",
                to="core.skill",
            ),
        ),
        migrations.AddField(
            model_name="course",
            name="skills_required",
            field=models.ManyToManyField(
                blank=True,
                help_text="Skills needed to take this course",
                related_name="required_by_courses",
                to="core.skill",
            ),
        ),
        migrations.AddField(
            model_name="assessmentresult",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="assessment.assessment"
            ),
        ),
        migrations.AddField(
            model_name="assessmentresult",
            name="student",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="announcement",
            name="course",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="announcements",
                to="courses.course",
            ),
        ),
        migrations.AddField(
            model_name="announcement",
            name="created_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="announcements",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="enrollment",
            unique_together={("user", "course")},
        ),
        migrations.AddIndex(
            model_name="coursesection",
            index=models.Index(
                fields=["course", "section_number"],
                name="courses_cou_course__683322_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="coursesection",
            index=models.Index(
                fields=["instructor"], name="courses_cou_instruc_07e617_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="coursesection",
            unique_together={("course", "section_number")},
        ),
        migrations.AlterUniqueTogether(
            name="courseprerequisite",
            unique_together={("prerequisite_course", "target_course")},
        ),
        migrations.AddIndex(
            model_name="courseenhanced",
            index=models.Index(fields=["course_code"], name="idx_course_code"),
        ),
        migrations.AddIndex(
            model_name="courseenhanced",
            index=models.Index(
                fields=["instructor", "semester"], name="idx_instructor_semester"
            ),
        ),
        migrations.AddIndex(
            model_name="courseenhanced",
            index=models.Index(
                fields=["department", "is_active"], name="idx_dept_active"
            ),
        ),
        migrations.AddIndex(
            model_name="courseenhanced",
            index=models.Index(
                fields=["academic_year", "semester"], name="idx_academic_semester"
            ),
        ),
        migrations.AddIndex(
            model_name="courseenhanced",
            index=models.Index(fields=["required_level"], name="idx_required_level"),
        ),
        migrations.AddIndex(
            model_name="courseenhanced",
            index=models.Index(fields=["primary_type"], name="idx_primary_type"),
        ),
        migrations.AddIndex(
            model_name="courseenhanced",
            index=models.Index(
                fields=["is_active", "semester", "academic_year"],
                name="idx_active_schedule",
            ),
        ),
        migrations.AddIndex(
            model_name="courseenhanced",
            index=models.Index(fields=["created_at"], name="idx_created_at"),
        ),
        migrations.AddIndex(
            model_name="courseenhanced",
            index=models.Index(fields=["updated_at"], name="idx_updated_at"),
        ),
        migrations.AddConstraint(
            model_name="courseenhanced",
            constraint=models.UniqueConstraint(
                fields=("course_code", "semester", "academic_year"),
                name="unique_course_schedule",
            ),
        ),
        migrations.AddConstraint(
            model_name="courseenhanced",
            constraint=models.CheckConstraint(
                check=models.Q(("credits__gte", 0), ("credits__lte", 12)),
                name="valid_credits_range",
            ),
        ),
        migrations.AddConstraint(
            model_name="courseenhanced",
            constraint=models.CheckConstraint(
                check=models.Q(("max_students__gte", 1), ("max_students__lte", 500)),
                name="valid_capacity_range",
            ),
        ),
        migrations.AddConstraint(
            model_name="courseenhanced",
            constraint=models.CheckConstraint(
                check=models.Q(("required_level__gte", 1), ("required_level__lte", 5)),
                name="valid_required_level",
            ),
        ),
        migrations.AddConstraint(
            model_name="courseenhanced",
            constraint=models.CheckConstraint(
                check=models.Q(
                    ("recommended_level__gte", 1), ("recommended_level__lte", 5)
                ),
                name="valid_recommended_level",
            ),
        ),
        migrations.AddConstraint(
            model_name="courseenhanced",
            constraint=models.CheckConstraint(
                check=models.Q(
                    ("typical_session_duration__gte", 15),
                    ("typical_session_duration__lte", 480),
                ),
                name="valid_session_duration",
            ),
        ),
    ]

from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    PracticeQuestionViewSet,
    SpacedRepetitionViewSet,
    StudyAssistantViewSet,
    StudySessionViewSet,
    StudyTopicViewSet
)

router = DefaultRouter()
router.register(r"sessions", StudySessionViewSet, basename="study-session")
router.register(r"topics", StudyTopicViewSet, basename="study-topic")
router.register(r"assistant", StudyAssistantViewSet, basename="study-assistant")
router.register(r"spaced-repetition", SpacedRepetitionViewSet, basename="spaced-repetition")
router.register(r"practice-questions", PracticeQuestionViewSet, basename="practice-question")

urlpatterns = [
    path("", include(router.urls)),
]
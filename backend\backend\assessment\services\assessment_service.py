"""
Assessment Service Layer

This module contains business logic for assessment-related operations,
following the service layer pattern to keep models lean and views focused.
"""

from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime, timedelta
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.cache import cache
import logging

from ..models import (
    Assessment,
    AssessmentQuestion,
    AssessmentResponse, 
    StudentLevel,
    AssessmentSettings,
    LevelRequirement
)
from core.models import Skill
from core.exceptions import BusinessLogicError, ValidationError as CoreValidationError

User = get_user_model()
logger = logging.getLogger(__name__)


class AssessmentService:
    """
    Service class for handling assessment business logic.
    
    This service encapsulates all business rules and operations related to
    assessments, keeping the models and views focused on their responsibilities.
    """
    
    def __init__(self, user: Optional[User] = None):
        """
        Initialize the assessment service.
        
        Args:
            user: The user context for the service operations
        """
        self.user = user
        
    def create_assessment(
        self,
        student: User,
        assessment_type: str = "PLACEMENT",
        learning_path: str = "general",
        num_questions: int = 10
    ) -> Assessment:
        """
        Create a new assessment for a student.
        
        Args:
            student: The student taking the assessment
            assessment_type: Type of assessment (PLACEMENT, COURSE, etc.)
            learning_path: Learning path for the assessment
            num_questions: Number of questions to include
            
        Returns:
            Created Assessment instance
            
        Raises:
            ValidationError: If assessment creation fails validation
            BusinessLogicError: If business rules are violated
        """
        # Validate student can take assessment
        self._validate_assessment_eligibility(student, assessment_type)
        
        # Get or create student level
        student_level, created = StudentLevel.objects.get_or_create(
            student=student,
            defaults={
                'current_level': 1,
                'current_level_display': 'Beginner'
            }
        )
        
        # Create assessment with transaction safety
        with transaction.atomic():
            assessment = Assessment.objects.create(
                student=student,
                assessment_type=assessment_type,
                learning_path=learning_path,
                status='IN_PROGRESS',
                start_time=timezone.now(),
                initial_level=student_level.current_level,
                is_adaptive=self._should_use_adaptive_difficulty(assessment_type)
            )
            
            # Add questions to assessment
            questions = self._select_questions(
                assessment_type=assessment_type,
                learning_path=learning_path,
                student_level=student_level.current_level,
                num_questions=num_questions
            )
            
            # Create response placeholders
            for question in questions:
                AssessmentResponse.objects.create(
                    assessment=assessment,
                    question=question,
                    student=student
                )
            
            logger.info(
                f"Created assessment {assessment.id} for student {student.username} "
                f"with {len(questions)} questions"
            )
            
            return assessment
    
    def submit_assessment(
        self,
        assessment: Assessment,
        responses: List[Dict[str, Any]]
    ) -> Assessment:
        """
        Submit an assessment with student responses.
        
        Args:
            assessment: The assessment to submit
            responses: List of response data
            
        Returns:
            Updated Assessment instance with results
            
        Raises:
            ValidationError: If submission data is invalid
            BusinessLogicError: If assessment is not in valid state for submission
        """
        if assessment.status != 'IN_PROGRESS':
            raise BusinessLogicError("Assessment is not in progress")
            
        if assessment.completed:
            raise BusinessLogicError("Assessment is already completed")
        
        with transaction.atomic():
            # Process responses
            self._process_responses(assessment, responses)
            
            # Calculate scores and analysis
            self._calculate_assessment_results(assessment)
            
            # Update student level based on performance
            level_changed = self._update_student_level(assessment)
            
            # Generate recommendations
            self._generate_recommendations(assessment)
            
            # Mark assessment as completed
            assessment.status = 'COMPLETED'
            assessment.completed = True
            assessment.end_time = timezone.now()
            assessment.time_spent = self._calculate_time_spent(assessment)
            assessment.save()
            
            logger.info(
                f"Completed assessment {assessment.id} with score {assessment.score}, "
                f"level changed: {level_changed}"
            )
            
            return assessment
    
    def get_assessment_analytics(
        self,
        assessment: Assessment
    ) -> Dict[str, Any]:
        """
        Get comprehensive analytics for an assessment.
        
        Args:
            assessment: The assessment to analyze
            
        Returns:
            Dictionary containing analytics data
        """
        if not assessment.completed:
            raise BusinessLogicError("Assessment must be completed for analytics")
        
        analytics = {
            'performance_summary': self._get_performance_summary(assessment),
            'skill_analysis': self._analyze_skills(assessment),
            'time_analysis': self._analyze_time_patterns(assessment),
            'difficulty_progression': self._analyze_difficulty_progression(assessment),
            'recommendations': self._get_personalized_recommendations(assessment)
        }
        
        return analytics
    
    def get_student_progress(self, student: User) -> Dict[str, Any]:
        """
        Get comprehensive progress report for a student.
        
        Args:
            student: The student to analyze
            
        Returns:
            Dictionary containing progress data
        """
        cache_key = f"student_progress_{student.id}"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            return cached_result
        
        assessments = Assessment.objects.filter(
            student=student,
            completed=True
        ).order_by('-end_time')
        
        progress_data = {
            'overview': self._get_progress_overview(student, assessments),
            'level_progression': self._get_level_progression(student),
            'skill_development': self._get_skill_development(student),
            'recent_activity': self._get_recent_activity(student, assessments[:5]),
            'recommendations': self._get_next_steps(student)
        }
        
        # Cache for 1 hour
        cache.set(cache_key, progress_data, 3600)
        
        return progress_data
    
    def _validate_assessment_eligibility(
        self,
        student: User,
        assessment_type: str
    ) -> None:
        """Validate if student can take the assessment."""
        # Check for existing in-progress assessments
        existing = Assessment.objects.filter(
            student=student,
            assessment_type=assessment_type,
            status='IN_PROGRESS'
        ).exists()
        
        if existing:
            raise BusinessLogicError(
                f"Student already has an in-progress {assessment_type} assessment"
            )
        
        # Check cooldown period for retakes
        settings = self._get_assessment_settings(assessment_type)
        if not settings.allow_retakes:
            completed = Assessment.objects.filter(
                student=student,
                assessment_type=assessment_type,
                completed=True
            ).exists()
            
            if completed:
                raise BusinessLogicError(
                    f"Retakes not allowed for {assessment_type} assessments"
                )
        
        # Check cooldown period
        last_attempt = Assessment.objects.filter(
            student=student,
            assessment_type=assessment_type,
            completed=True
        ).order_by('-end_time').first()
        
        if last_attempt and settings.retake_cooldown_days > 0:
            cooldown_end = last_attempt.end_time + timedelta(
                days=settings.retake_cooldown_days
            )
            if timezone.now() < cooldown_end:
                raise BusinessLogicError(
                    f"Must wait until {cooldown_end} to retake assessment"
                )
    
    def _select_questions(
        self,
        assessment_type: str,
        learning_path: str,
        student_level: int,
        num_questions: int
    ) -> List[AssessmentQuestion]:
        """Select appropriate questions for the assessment."""
        base_filter = {
            'is_public': True,
            'is_placement': (assessment_type == 'PLACEMENT')
        }
        
        # Get path-specific questions first
        if learning_path != 'general':
            path_questions = list(
                AssessmentQuestion.objects.filter(
                    **base_filter,
                    category=learning_path,
                    difficulty_level__lte=student_level + 1
                ).order_by('?')
            )
        else:
            path_questions = []
        
        # Fill remaining slots with general questions
        remaining_slots = max(0, num_questions - len(path_questions))
        if remaining_slots > 0:
            general_questions = list(
                AssessmentQuestion.objects.filter(
                    **base_filter,
                    category='general',
                    difficulty_level__lte=student_level + 1
                ).exclude(
                    id__in=[q.id for q in path_questions]
                ).order_by('?')[:remaining_slots]
            )
        else:
            general_questions = []
        
        # Combine and validate
        selected_questions = path_questions + general_questions
        
        if len(selected_questions) < num_questions:
            logger.warning(
                f"Only found {len(selected_questions)} questions for "
                f"{assessment_type} assessment, requested {num_questions}"
            )
        
        return selected_questions[:num_questions]
    
    def _process_responses(
        self,
        assessment: Assessment,
        responses: List[Dict[str, Any]]
    ) -> None:
        """Process and validate student responses."""
        for response_data in responses:
            question_id = response_data.get('question_id')
            answer_text = response_data.get('answer', '')
            
            if not question_id:
                continue
                
            try:
                response = AssessmentResponse.objects.get(
                    assessment=assessment,
                    question_id=question_id
                )
                
                response.answer_text = answer_text
                response.submitted_at = timezone.now()
                response.evaluate()  # This sets is_correct and points_earned
                
            except AssessmentResponse.DoesNotExist:
                logger.warning(
                    f"Response not found for question {question_id} "
                    f"in assessment {assessment.id}"
                )
                continue
    
    def _calculate_assessment_results(self, assessment: Assessment) -> None:
        """Calculate scores and detailed results for the assessment."""
        responses = assessment.responses.all()
        
        # Basic score calculation
        total_points = sum(r.question.points for r in responses)
        earned_points = sum(r.points_earned for r in responses)
        
        assessment.score = (earned_points / total_points * 100) if total_points > 0 else 0
        
        # Skill-based analysis
        skill_scores = self._calculate_skill_scores(responses)
        assessment.skill_scores = skill_scores
        
        # Store detailed results
        assessment.detailed_results = {
            'total_questions': len(responses),
            'correct_answers': sum(1 for r in responses if r.is_correct),
            'score': assessment.score,
            'skill_scores': skill_scores,
            'strengths': [skill for skill, score in skill_scores.items() if score >= 70],
            'weaknesses': [skill for skill, score in skill_scores.items() if score <= 40],
            'response_analysis': [
                {
                    'question_id': r.question.id,
                    'is_correct': r.is_correct,
                    'time_spent': r.time_spent or 0,
                    'difficulty': r.question.difficulty_level
                }
                for r in responses
            ]
        }
        
        assessment.save()
    
    def _calculate_skill_scores(
        self,
        responses: List[AssessmentResponse]
    ) -> Dict[str, float]:
        """Calculate scores for each skill category."""
        skill_data = {}
        
        for response in responses:
            # Get skills assessed by this question
            skills = response.question.skills_assessed.all()
            
            for skill in skills:
                skill_name = skill.name
                if skill_name not in skill_data:
                    skill_data[skill_name] = {'correct': 0, 'total': 0}
                
                skill_data[skill_name]['total'] += 1
                if response.is_correct:
                    skill_data[skill_name]['correct'] += 1
        
        # Convert to percentages
        skill_scores = {}
        for skill_name, data in skill_data.items():
            if data['total'] > 0:
                skill_scores[skill_name] = (data['correct'] / data['total']) * 100
            else:
                skill_scores[skill_name] = 0
        
        return skill_scores
    
    def _update_student_level(self, assessment: Assessment) -> bool:
        """Update student level based on assessment performance."""
        student_level = StudentLevel.objects.get(student=assessment.student)
        
        # Determine new level based on score and assessment type
        new_level = self._determine_new_level(
            assessment.score,
            student_level.current_level,
            assessment.assessment_type
        )
        
        # Update if level changed
        if new_level != student_level.current_level:
            level_changed = student_level.update_level(
                new_level,
                reason=f"{assessment.assessment_type} Assessment",
                assessment=assessment
            )
            
            assessment.final_level = new_level
            assessment.level_changed = level_changed
            assessment.save()
            
            return level_changed
        
        assessment.final_level = student_level.current_level
        assessment.level_changed = False
        assessment.save()
        
        return False
    
    def _determine_new_level(
        self,
        score: float,
        current_level: int,
        assessment_type: str
    ) -> int:
        """Determine new level based on performance."""
        if assessment_type == 'PLACEMENT':
            # Placement assessments set absolute level
            if score >= 90:
                return 5  # Expert
            elif score >= 75:
                return 4  # Advanced
            elif score >= 60:
                return 3  # Intermediate
            elif score >= 40:
                return 2  # Elementary
            else:
                return 1  # Beginner
        else:
            # Other assessments adjust level incrementally
            if score >= 90 and current_level < 5:
                return current_level + 1
            elif score <= 40 and current_level > 1:
                return current_level - 1
            else:
                return current_level
    
    def _generate_recommendations(self, assessment: Assessment) -> None:
        """Generate personalized recommendations based on assessment results."""
        recommendations = []
        
        # Get weak areas
        weak_skills = assessment.detailed_results.get('weaknesses', [])
        
        if weak_skills:
            recommendations.append({
                'type': 'skill_improvement',
                'title': 'Focus on Weak Skills',
                'description': f"Consider studying: {', '.join(weak_skills[:3])}",
                'priority': 'high',
                'skills': weak_skills
            })
        
        # Performance-based recommendations
        if assessment.score < 60:
            recommendations.append({
                'type': 'performance',
                'title': 'Review Fundamentals',
                'description': 'Your score suggests reviewing basic concepts',
                'priority': 'high'
            })
        elif assessment.score >= 80:
            recommendations.append({
                'type': 'advancement',
                'title': 'Ready for Advanced Content',
                'description': 'Consider more challenging materials',
                'priority': 'medium'
            })
        
        # Update assessment with recommendations
        current_results = assessment.detailed_results or {}
        current_results['recommendations'] = recommendations
        assessment.detailed_results = current_results
        assessment.save()
    
    def _get_assessment_settings(self, assessment_type: str) -> AssessmentSettings:
        """Get settings for assessment type with defaults."""
        try:
            return AssessmentSettings.objects.get(assessment_type=assessment_type)
        except AssessmentSettings.DoesNotExist:
            # Return default settings
            return AssessmentSettings(
                assessment_type=assessment_type,
                passing_score=60.0,
                time_limit_minutes=60,
                questions_per_assessment=10,
                allow_retakes=True,
                retake_cooldown_days=1
            )
    
    def _should_use_adaptive_difficulty(self, assessment_type: str) -> bool:
        """Determine if adaptive difficulty should be used."""
        settings = self._get_assessment_settings(assessment_type)
        return getattr(settings, 'adaptive_difficulty', True)
    
    def _calculate_time_spent(self, assessment: Assessment) -> Optional[int]:
        """Calculate total time spent on assessment in seconds."""
        if assessment.start_time and assessment.end_time:
            delta = assessment.end_time - assessment.start_time
            return int(delta.total_seconds())
        return None
    
    # Additional helper methods for analytics
    def _get_performance_summary(self, assessment: Assessment) -> Dict[str, Any]:
        """Get performance summary for assessment."""
        return {
            'score': assessment.score,
            'level_change': assessment.level_changed,
            'time_spent': assessment.time_spent,
            'difficulty_rating': self._calculate_difficulty_rating(assessment),
            'percentile_rank': self._calculate_percentile_rank(assessment)
        }
    
    def _analyze_skills(self, assessment: Assessment) -> Dict[str, Any]:
        """Analyze skill performance in detail."""
        skill_scores = assessment.skill_scores or {}
        
        return {
            'strengths': [
                {'skill': skill, 'score': score}
                for skill, score in skill_scores.items()
                if score >= 70
            ],
            'areas_for_improvement': [
                {'skill': skill, 'score': score}
                for skill, score in skill_scores.items()
                if score < 70
            ],
            'skill_distribution': skill_scores
        }
    
    def _analyze_time_patterns(self, assessment: Assessment) -> Dict[str, Any]:
        """Analyze time usage patterns."""
        responses = assessment.responses.all()
        
        time_data = [r.time_spent for r in responses if r.time_spent]
        
        if not time_data:
            return {}
        
        return {
            'average_time_per_question': sum(time_data) / len(time_data),
            'fastest_response': min(time_data),
            'slowest_response': max(time_data),
            'time_efficiency': self._calculate_time_efficiency(responses)
        }
    
    def _analyze_difficulty_progression(self, assessment: Assessment) -> Dict[str, Any]:
        """Analyze how student performed across different difficulty levels."""
        responses = assessment.responses.all()
        difficulty_performance = {}
        
        for response in responses:
            level = response.question.difficulty_level
            if level not in difficulty_performance:
                difficulty_performance[level] = {'correct': 0, 'total': 0}
            
            difficulty_performance[level]['total'] += 1
            if response.is_correct:
                difficulty_performance[level]['correct'] += 1
        
        return {
            level: {
                'accuracy': (data['correct'] / data['total']) * 100,
                'questions_count': data['total']
            }
            for level, data in difficulty_performance.items()
        }
    
    def _get_personalized_recommendations(self, assessment: Assessment) -> List[Dict[str, Any]]:
        """Get AI-powered personalized recommendations."""
        # This would integrate with an AI service for recommendations
        # For now, return rule-based recommendations
        return assessment.detailed_results.get('recommendations', [])
    
    def _get_progress_overview(
        self,
        student: User,
        assessments: List[Assessment]
    ) -> Dict[str, Any]:
        """Get overview of student progress."""
        if not assessments:
            return {
                'total_assessments': 0,
                'average_score': 0,
                'current_level': 1,
                'level_changes': 0
            }
        
        scores = [a.score for a in assessments if a.score is not None]
        level_changes = sum(1 for a in assessments if a.level_changed)
        
        return {
            'total_assessments': len(assessments),
            'average_score': sum(scores) / len(scores) if scores else 0,
            'current_level': assessments[0].final_level or 1,
            'level_changes': level_changes,
            'improvement_trend': self._calculate_improvement_trend(scores)
        }
    
    def _get_level_progression(self, student: User) -> List[Dict[str, Any]]:
        """Get student's level progression over time."""
        try:
            student_level = StudentLevel.objects.get(student=student)
            return student_level.progression_history or []
        except StudentLevel.DoesNotExist:
            return []
    
    def _get_skill_development(self, student: User) -> Dict[str, Any]:
        """Get skill development analysis."""
        assessments = Assessment.objects.filter(
            student=student,
            completed=True
        ).order_by('end_time')
        
        skill_trends = {}
        
        for assessment in assessments:
            skill_scores = assessment.skill_scores or {}
            for skill, score in skill_scores.items():
                if skill not in skill_trends:
                    skill_trends[skill] = []
                skill_trends[skill].append({
                    'date': assessment.end_time.isoformat(),
                    'score': score
                })
        
        return {
            'skill_trends': skill_trends,
            'current_strengths': self._get_current_strengths(student),
            'areas_for_improvement': self._get_areas_for_improvement(student)
        }
    
    def _get_recent_activity(
        self,
        student: User,
        recent_assessments: List[Assessment]
    ) -> List[Dict[str, Any]]:
        """Get recent assessment activity."""
        return [
            {
                'id': assessment.id,
                'type': assessment.assessment_type,
                'score': assessment.score,
                'date': assessment.end_time.isoformat() if assessment.end_time else None,
                'level_changed': assessment.level_changed
            }
            for assessment in recent_assessments
        ]
    
    def _get_next_steps(self, student: User) -> List[Dict[str, Any]]:
        """Get recommended next steps for the student."""
        # This would integrate with a recommendation engine
        # For now, return basic recommendations
        try:
            student_level = StudentLevel.objects.get(student=student)
            next_level_req = student_level.get_next_level_requirements()
            
            if next_level_req:
                return [{
                    'type': 'level_advancement',
                    'title': f"Advance to {next_level_req['level_name']}",
                    'description': next_level_req['requirements']['description'],
                    'priority': 'medium'
                }]
        except StudentLevel.DoesNotExist:
            pass
        
        return [{
            'type': 'assessment',
            'title': 'Take a Placement Assessment',
            'description': 'Determine your current skill level',
            'priority': 'high'
        }]
    
    def _calculate_difficulty_rating(self, assessment: Assessment) -> str:
        """Calculate subjective difficulty rating."""
        score = assessment.score or 0
        time_spent = assessment.time_spent or 0
        
        # Simple heuristic based on score and time
        if score >= 80 and time_spent < 1800:  # 30 minutes
            return 'Easy'
        elif score >= 60:
            return 'Moderate'
        else:
            return 'Challenging'
    
    def _calculate_percentile_rank(self, assessment: Assessment) -> float:
        """Calculate percentile rank compared to other students."""
        same_type_scores = Assessment.objects.filter(
            assessment_type=assessment.assessment_type,
            completed=True,
            score__isnull=False
        ).values_list('score', flat=True)
        
        if not same_type_scores:
            return 50.0  # Default to 50th percentile
        
        lower_scores = [s for s in same_type_scores if s < assessment.score]
        return (len(lower_scores) / len(same_type_scores)) * 100
    
    def _calculate_time_efficiency(
        self,
        responses: List[AssessmentResponse]
    ) -> str:
        """Calculate time efficiency rating."""
        correct_times = [
            r.time_spent for r in responses
            if r.is_correct and r.time_spent
        ]
        
        if not correct_times:
            return 'Unknown'
        
        avg_time = sum(correct_times) / len(correct_times)
        
        if avg_time < 60:  # Less than 1 minute per correct answer
            return 'Excellent'
        elif avg_time < 120:  # Less than 2 minutes
            return 'Good'
        else:
            return 'Needs Improvement'
    
    def _calculate_improvement_trend(self, scores: List[float]) -> str:
        """Calculate improvement trend from score history."""
        if len(scores) < 2:
            return 'Insufficient Data'
        
        # Simple trend calculation
        recent_avg = sum(scores[:3]) / min(3, len(scores))
        older_avg = sum(scores[-3:]) / min(3, len(scores))
        
        if recent_avg > older_avg + 5:
            return 'Improving'
        elif recent_avg < older_avg - 5:
            return 'Declining'
        else:
            return 'Stable'
    
    def _get_current_strengths(self, student: User) -> List[str]:
        """Get current skill strengths for student."""
        try:
            student_level = StudentLevel.objects.get(student=student)
            return list(student_level.skill_strengths.keys())
        except StudentLevel.DoesNotExist:
            return []
    
    def _get_areas_for_improvement(self, student: User) -> List[str]:
        """Get areas needing improvement for student."""
        try:
            student_level = StudentLevel.objects.get(student=student)
            return list(student_level.skill_weaknesses.keys())
        except StudentLevel.DoesNotExist:
            return []

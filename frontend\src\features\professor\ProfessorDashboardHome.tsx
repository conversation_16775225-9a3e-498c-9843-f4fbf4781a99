import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { alpha } from '@mui/material/styles';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Button,
  Stack,
  CardActions,
  LinearProgress,
  Alert,
  useTheme,
} from '@mui/material';
import ModernHeroSVG from '../home/<USER>';
import ProfessorAppBar from './components/ProfessorAppBar';
import PeopleIcon from '../../components/icons/People';
import AssignmentIcon from '../../components/icons/Assignment';
import EventIcon from '../../components/icons/Event';
import SpeedIcon from '../../components/icons/Speed';

import LazyLoadWrapper from '../../components/courses/LazyLoadWrapper';
import { SuperCourseCard } from '../../components/courses';
import {
  Timeline,
  TimelineItem,
  TimelineContent,
  TimelineSeparator,
  TimelineConnector,
  TimelineDot,
} from '@mui/lab';
import { courseService } from '../../services/courseService';
import { gradeService } from '../../services/gradeService';
import { attendanceService } from '../../services/attendanceService';
import { notificationService } from '../../services/notificationService';
import { OfficeHoursManagement } from './components/OfficeHoursManagement';
import { CourseAnnouncements } from './components/CourseAnnouncements';
import './styles/ProfessorDashboard.css';

interface DashboardStats {
  totalStudents: number;
  activeAssignments: number;
  upcomingClasses: number;
  averageAttendance: number;
}

interface Course {
  id: number;
  code: string;
  title: string;
  students_count: number;
  progress?: number;
  next_class?: string;
  last_attendance_date?: string;
  last_grade_date?: string;
  last_material_date?: string;
}

interface Activity {
  id: number;
  type: string;
  text: string;
  time: string;
}

interface StatsCardProps {
  icon: React.ReactNode;
  title: string;
  value: string | number;
  color: string;
}

const ProfessorDashboardHome = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const theme = useTheme();
  const [darkMode, setDarkMode] = useState(theme.palette.mode === 'dark');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<DashboardStats>({
    totalStudents: 0,
    activeAssignments: 0,
    upcomingClasses: 0,
    averageAttendance: 0,
  });
  const [courses, setCourses] = useState<Course[]>([]);
  const [activities, setActivities] = useState<Activity[]>([]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all data in parallel
      const [
        coursesResponse,
        assignmentsResponse,
        attendanceResponse,
        notificationsResponse,
      ] = await Promise.all([
        courseService.getProfessorCourses(),
        gradeService.getCourseGrades(),
        attendanceService.getAttendanceStats(0),
        notificationService.getNotifications('professor'),
      ]);

      // Process courses data - ensure we have an array
      const coursesData = coursesResponse.data?.data || [];
      setCourses(coursesData);

      // Calculate total students
      const totalStudents = coursesData.reduce(
        (sum: number, course: Course) => sum + (course.students_count || 0),
        0
      );

      // Process assignments data
      const activeAssignments = assignmentsResponse.data?.active_count || 0;

      // Process attendance data
      const attendanceStats = attendanceResponse.data?.overall || {};
      const {
        total_students = 0,
        total_courses = 0,
        average_attendance_rate = 0,
      } = attendanceStats;

      // Update stats with actual data
      setStats({
        totalStudents: total_students || totalStudents,
        activeAssignments,
        upcomingClasses: total_courses || coursesData.length,
        averageAttendance: Math.round(average_attendance_rate),
      });

      // Process notifications into activities - directly use the response array
      const recentActivities = (
        Array.isArray(notificationsResponse.data)
          ? notificationsResponse.data
          : []
      )
        .filter(notification =>
          ['ATTENDANCE', 'GRADE', 'MATERIAL'].includes(notification.type)
        )
        .map(notification => ({
          id: notification.id,
          type: notification.type.toLowerCase(),
          text: notification.message,
          time: notification.created_at,
        }))
        .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime())
        .slice(0, 5);

      setActivities(recentActivities);
    } catch (err: any) {
      console.error('Dashboard data fetch error:', err);
      setError(err.response?.data?.message || 'Failed to fetch dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const formatTimeAgo = (isoString: string): string => {
    const date = new Date(isoString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(
      (diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
    );
    const diffMinutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60));

    if (diffDays > 0) {
      return t('common.daysAgo', { count: diffDays });
    } else if (diffHours > 0) {
      return t('common.hoursAgo', { count: diffHours });
    } else if (diffMinutes > 0) {
      return t('common.minutesAgo', { count: diffMinutes });
    } else {
      return t('common.justNow');
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center' }}>
        <LinearProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity='error'>{error}</Alert>
      </Box>
    );
  }

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    // In a real implementation, this would update the theme context
  };

  return (
    <Box
      className={`professor-dashboard ${theme.palette.mode === 'dark' ? 'dark-mode' : ''}`}
      sx={{
        minHeight: '100vh',
        background: theme.palette.mode === 'dark'
          ? 'linear-gradient(135deg, rgba(18,18,18,0.95) 0%, rgba(0,229,255,0.08) 100%)'
          : 'linear-gradient(135deg, rgba(248,250,252,0.95) 0%, rgba(0,119,182,0.03) 100%)',
        position: 'relative',
        overflow: 'hidden',
        pb: { xs: 2, sm: 3 },
      }}
    >
      {/* Hero Background SVG */}
      <ModernHeroSVG />
      
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        style={{ position: 'relative', zIndex: 1 }}
      >
        <ProfessorAppBar
          darkMode={darkMode}
          toggleDarkMode={toggleDarkMode}
          refreshData={fetchDashboardData}
        />

        <Box sx={{
          px: { xs: 2, sm: 3 }, // Responsive padding
          maxWidth: '1400px', // Max width for large screens
          mx: 'auto' // Center content
        }}>
        {/* Stats Cards */}
        <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} lg={3}>
            <StatsCard
              icon={<PeopleIcon />}
              title={t('professor.dashboard.totalStudents')}
              value={stats.totalStudents}
              color='#1976d2'
            />
          </Grid>
          <Grid item xs={12} sm={6} lg={3}>
            <StatsCard
              icon={<AssignmentIcon />}
              title={t('professor.dashboard.activeAssignments')}
              value={stats.activeAssignments}
              color='#2e7d32'
            />
          </Grid>
          <Grid item xs={12} sm={6} lg={3}>
            <StatsCard
              icon={<EventIcon />}
              title={t('professor.dashboard.upcomingClasses')}
              value={stats.upcomingClasses}
              color='#ed6c02'
            />
          </Grid>
          <Grid item xs={12} sm={6} lg={3}>
            <StatsCard
              icon={<SpeedIcon />}
              title={t('professor.dashboard.avgAttendance')}
              value={`${stats.averageAttendance}%`}
              color='#9c27b0'
            />
          </Grid>
        </Grid>

        {/* Main Content */}
        <Grid container spacing={{ xs: 2, sm: 3 }}>
          {/* Left Column */}
          <Grid item xs={12} lg={8}>
            <CourseAnnouncements />

            {/* Recent Activities */}
            <Paper 
              sx={{ 
                p: 3,
                background: alpha(theme.palette.background.paper, 0.8),
                backdropFilter: 'blur(15px)',
                WebkitBackdropFilter: 'blur(15px)',
                border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                borderRadius: 3,
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
                },
              }}
            >
              <Typography variant='h6' sx={{ mb: 2 }}>
                {t('dashboard.recentActivity')}
              </Typography>
              <Timeline>
                {activities.map(activity => (
                  <TimelineItem key={activity.id}>
                    <TimelineSeparator>
                      <TimelineDot />
                      <TimelineConnector />
                    </TimelineSeparator>
                    <TimelineContent>
                      <Typography variant='subtitle2'>
                        {activity.text}
                      </Typography>
                      <Typography variant='caption' color='text.secondary'>
                        {activity.time}
                      </Typography>
                    </TimelineContent>
                  </TimelineItem>
                ))}
              </Timeline>
            </Paper>
          </Grid>

          {/* Right Column */}
          <Grid item xs={12} md={4}>
            <OfficeHoursManagement />

            {/* Course Overview Cards */}
            <Typography variant='h6' sx={{ mt: 3, mb: 2 }}>
              {t('professor.dashboard.courseOverview')}
            </Typography>
            <Stack spacing={2}>
              {courses.map(course => (
                <LazyLoadWrapper key={course.id} fallback="courseCard">
                  <SuperCourseCard
                  key={course.id}
                  course={{
                    id: course.id,
                    course_code: course.code || course.course_code,
                    title: course.title,
                    description: course.description || 'No description available',
                    credits: course.credits || 0,
                    is_active: true,
                    student_count: course.students_count || 0,
                    max_students: course.capacity || 50,
                    instructor: course.instructor || 'You',
                    department: course.department,
                    primary_type: 'STANDARD',
                    required_level: 1,
                    has_assessment: true,
                    semester: course.semester,
                    academic_year: course.academic_year,
                  }}
                  variant='compact'
                  userRole='PROFESSOR'
                  progress={course.progress}
                  isEnrolled={false}
                  showProgress={course.progress !== undefined}
                  showActions={true}
                  showEnrollmentInfo={true}
                  showInteractiveFeatures={false}
                  showSkills={false}
                  showLastUpdated={false}
                  onViewDetails={() => navigate(`/professor/courses/${course.id}`)}
                  onManage={() => navigate(`/professor/courses/${course.id}`)}
                  enableAnimations={true}
                  animationDelay={course.id * 100}
                  />
                </LazyLoadWrapper>
              ))}
            </Stack>
          </Grid>
        </Grid>
        </Box>
      </motion.div>
    </Box>
  );
};

// Helper Components
const StatsCard = ({ icon, title, value, color }: StatsCardProps) => {
  const theme = useTheme();

  return (
    <Card
      sx={{
        height: '100%',
        minHeight: { xs: '120px', sm: '140px' }, // Responsive height
        borderRadius: { xs: 2, sm: 3 }, // Responsive border radius
        background: alpha(theme.palette.background.paper, 0.9),
        backdropFilter: 'blur(20px)',
        WebkitBackdropFilter: 'blur(20px)',
        border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
        boxShadow: theme.palette.mode === 'dark'
          ? '0 8px 32px rgba(0, 0, 0, 0.3)'
          : '0 8px 32px rgba(0, 0, 0, 0.08)',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        cursor: 'pointer',
        position: 'relative',
        overflow: 'hidden',
        '&:hover': {
          transform: 'translateY(-6px)',
          boxShadow: theme.palette.mode === 'dark'
            ? '0 16px 48px rgba(0, 0, 0, 0.4)'
            : '0 16px 48px rgba(0, 0, 0, 0.12)',
          '& .stats-icon': {
            transform: 'scale(1.1) rotate(5deg)',
          },
        },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '3px',
          background: `linear-gradient(90deg, ${color}, ${color}80)`,
          borderRadius: '3px 3px 0 0',
        },
      }}
    >
      <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box
            className="stats-icon"
            sx={{
              p: { xs: 1, sm: 1.5 },
              borderRadius: 2,
              background: `linear-gradient(135deg, ${color}20, ${color}30)`,
              color: color,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.3s ease',
              fontSize: { xs: '1.2rem', sm: '1.5rem' },
            }}
          >
            {icon}
          </Box>
        </Box>
        <Typography
          variant='h4'
          fontWeight='bold'
          sx={{
            fontSize: { xs: '1.5rem', sm: '2rem' },
            mb: 1,
            background: theme.palette.mode === 'dark'
              ? `linear-gradient(135deg, ${color} 0%, #ffffff 100%)`
              : `linear-gradient(135deg, ${color} 0%, #333333 100%)`,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
          }}
        >
          {value}
        </Typography>
        <Typography
          variant='body2'
          color='textSecondary'
          sx={{
            fontWeight: 500,
            fontSize: { xs: '0.75rem', sm: '0.875rem' },
            lineHeight: 1.4,
          }}
        >
          {title}
        </Typography>
      </CardContent>
    </Card>
  );
};

// Using the shared CourseCard component instead

export default ProfessorDashboardHome;

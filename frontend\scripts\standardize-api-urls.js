#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript and JavaScript files
function findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Function to replace API URLs
function standardizeApiUrls(content) {
  // Replace various patterns of /api/ (not followed by v1/) with /api/v1/
  const patterns = [
    // Single quotes
    /'\/api\/(?!v1\/)([^']*?)'/g,
    // Double quotes
    /"\/api\/(?!v1\/)([^"]*?)"/g,
    // Template literals
    /`\/api\/(?!v1\/)([^`]*?)`/g,
    // Template literal interpolations
    /`([^`]*?)\/api\/(?!v1\/)([^`]*?)`/g
  ];
  
  let updatedContent = content;
  let changesMade = false;
  
  patterns.forEach(pattern => {
    const originalContent = updatedContent;
    
    if (pattern.source.includes('`([^`]*?)')) {
      // Handle template literal interpolations
      updatedContent = updatedContent.replace(pattern, (match, before, after) => {
        changesMade = true;
        return `\`${before}/api/v1/${after}\``;
      });
    } else if (pattern.source.includes("'")) {
      // Handle single quotes
      updatedContent = updatedContent.replace(pattern, (match, path) => {
        changesMade = true;
        return `'/api/v1/${path}'`;
      });
    } else if (pattern.source.includes('"')) {
      // Handle double quotes
      updatedContent = updatedContent.replace(pattern, (match, path) => {
        changesMade = true;
        return `"/api/v1/${path}"`;
      });
    } else {
      // Handle template literals
      updatedContent = updatedContent.replace(pattern, (match, path) => {
        changesMade = true;
        return `\`/api/v1/${path}\``;
      });
    }
  });
  
  return { content: updatedContent, changed: changesMade };
}

// Main function
function main() {
  const srcDir = path.join(__dirname, '..', 'src');
  const files = findFiles(srcDir);
  
  let totalFiles = 0;
  let changedFiles = 0;
  
  console.log(`Found ${files.length} files to process...`);
  
  files.forEach(filePath => {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const result = standardizeApiUrls(content);
      
      totalFiles++;
      
      if (result.changed) {
        fs.writeFileSync(filePath, result.content, 'utf8');
        changedFiles++;
        console.log(`Updated: ${path.relative(srcDir, filePath)}`);
      }
    } catch (error) {
      console.error(`Error processing ${filePath}:`, error.message);
    }
  });
  
  console.log(`\nProcessing complete:`);
  console.log(`Total files processed: ${totalFiles}`);
  console.log(`Files updated: ${changedFiles}`);
  console.log(`Files unchanged: ${totalFiles - changedFiles}`);
}

if (require.main === module) {
  main();
}

module.exports = { standardizeApiUrls, findFiles };

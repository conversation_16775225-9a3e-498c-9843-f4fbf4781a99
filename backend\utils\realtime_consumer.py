"""
Real-time WebSocket Consumer
Provides real-time features for the North Star University platform
"""

import json
import asyncio
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

User = get_user_model()

class RealTimeConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for real-time features"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = None
        self.groups = []
        
    async def connect(self):
        """Handle WebSocket connection"""
        try:
            # Get user from session
            self.user = self.scope["user"]
            
            if self.user.is_anonymous:
                await self.close()
                return
            
            # Join user-specific group
            user_group = f"user_{self.user.id}"
            await self.channel_layer.group_add(user_group, self.channel_name)
            self.groups.append(user_group)
            
            # Join role-based groups
            if hasattr(self.user, 'role'):
                role_group = f"role_{self.user.role}"
                await self.channel_layer.group_add(role_group, self.channel_name)
                self.groups.append(role_group)
            
            # Join university-wide group for general announcements
            university_group = "university_wide"
            await self.channel_layer.group_add(university_group, self.channel_name)
            self.groups.append(university_group)
            
            await self.accept()
            
            # Send initial connection confirmation
            await self.send(text_data=json.dumps({
                'type': 'connection_established',
                'message': 'Connected to real-time updates',
                'user_id': self.user.id,
                'timestamp': timezone.now().isoformat(),
            }))
            
            # Send initial real-time data
            await self.send_initial_data()
            
        except Exception as e:
            await self.close()
    
    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        # Leave all groups
        for group in self.groups:
            await self.channel_layer.group_discard(group, self.channel_name)
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'request_analytics':
                await self.handle_analytics_request(data)
            elif message_type == 'heartbeat':
                await self.handle_heartbeat(data)
            elif message_type == 'subscribe_to_updates':
                await self.handle_subscription_request(data)
            elif message_type == 'track_activity':
                await self.handle_activity_tracking(data)
            else:
                await self.send_error('Unknown message type')
                
        except json.JSONDecodeError:
            await self.send_error('Invalid JSON format')
        except Exception as e:
            await self.send_error(f'Error processing message: {str(e)}')
    
    async def send_initial_data(self):
        """Send initial real-time data to newly connected user"""
        try:
            # Get user's notifications
            notifications = await self.get_user_notifications()
            
            # Get real-time statistics
            stats = await self.get_real_time_stats()
            
            # Send initial data
            await self.send(text_data=json.dumps({
                'type': 'initial_data',
                'data': {
                    'notifications': notifications,
                    'real_time_stats': stats,
                    'user_info': {
                        'id': self.user.id,
                        'username': self.user.username,
                        'role': getattr(self.user, 'role', 'student'),
                    }
                },
                'timestamp': timezone.now().isoformat(),
            }))\
            \n        except Exception as e:\n            await self.send_error(f'Error sending initial data: {str(e)}')\n    \n    async def handle_analytics_request(self, data):\n        \"\"\"Handle request for real-time analytics\"\"\"\n        try:\n            from .analytics_service import analytics_service\n            \n            # Get analytics data\n            analytics_data = await database_sync_to_async(\n                analytics_service.get_real_time_stats\n            )()\n            \n            await self.send(text_data=json.dumps({\n                'type': 'analytics_update',\n                'data': analytics_data,\n                'timestamp': timezone.now().isoformat(),\n            }))\n            \n        except Exception as e:\n            await self.send_error(f'Error getting analytics: {str(e)}')\n    \n    async def handle_heartbeat(self, data):\n        \"\"\"Handle heartbeat messages to keep connection alive\"\"\"\n        await self.send(text_data=json.dumps({\n            'type': 'heartbeat_response',\n            'timestamp': timezone.now().isoformat(),\n        }))\n    \n    async def handle_subscription_request(self, data):\n        \"\"\"Handle subscription to specific real-time updates\"\"\"\n        try:\n            subscription_type = data.get('subscription_type')\n            course_id = data.get('course_id')\n            \n            if subscription_type == 'course_updates' and course_id:\n                # Subscribe to course-specific updates\n                course_group = f\"course_{course_id}\"\n                await self.channel_layer.group_add(course_group, self.channel_name)\n                self.groups.append(course_group)\n                \n                await self.send(text_data=json.dumps({\n                    'type': 'subscription_confirmed',\n                    'subscription_type': subscription_type,\n                    'course_id': course_id,\n                    'message': f'Subscribed to updates for course {course_id}',\n                    'timestamp': timezone.now().isoformat(),\n                }))\n            \n            elif subscription_type == 'assessment_updates':\n                # Subscribe to assessment updates\n                assessment_group = \"assessment_updates\"\n                await self.channel_layer.group_add(assessment_group, self.channel_name)\n                self.groups.append(assessment_group)\n                \n                await self.send(text_data=json.dumps({\n                    'type': 'subscription_confirmed',\n                    'subscription_type': subscription_type,\n                    'message': 'Subscribed to assessment updates',\n                    'timestamp': timezone.now().isoformat(),\n                }))\n            \n        except Exception as e:\n            await self.send_error(f'Error handling subscription: {str(e)}')\n    \n    async def handle_activity_tracking(self, data):\n        \"\"\"Handle activity tracking for analytics\"\"\"\n        try:\n            activity_type = data.get('activity_type')\n            metadata = data.get('metadata', {})\n            \n            if activity_type:\n                # Track the activity\n                await database_sync_to_async(self.track_user_activity)(\n                    activity_type, metadata\n                )\n                \n                await self.send(text_data=json.dumps({\n                    'type': 'activity_tracked',\n                    'activity_type': activity_type,\n                    'timestamp': timezone.now().isoformat(),\n                }))\n            \n        except Exception as e:\n            await self.send_error(f'Error tracking activity: {str(e)}')\n    \n    # Group message handlers\n    async def notification_message(self, event):\n        \"\"\"Handle notification messages sent to groups\"\"\"\n        await self.send(text_data=json.dumps({\n            'type': 'notification',\n            'data': event['data'],\n            'timestamp': event.get('timestamp', timezone.now().isoformat()),\n        }))\n    \n    async def analytics_update(self, event):\n        \"\"\"Handle analytics update messages\"\"\"\n        await self.send(text_data=json.dumps({\n            'type': 'analytics_update',\n            'data': event['data'],\n            'timestamp': event.get('timestamp', timezone.now().isoformat()),\n        }))\n    \n    async def course_update(self, event):\n        \"\"\"Handle course update messages\"\"\"\n        await self.send(text_data=json.dumps({\n            'type': 'course_update',\n            'data': event['data'],\n            'timestamp': event.get('timestamp', timezone.now().isoformat()),\n        }))\n    \n    async def assessment_update(self, event):\n        \"\"\"Handle assessment update messages\"\"\"\n        await self.send(text_data=json.dumps({\n            'type': 'assessment_update',\n            'data': event['data'],\n            'timestamp': event.get('timestamp', timezone.now().isoformat()),\n        }))\n    \n    async def university_announcement(self, event):\n        \"\"\"Handle university-wide announcements\"\"\"\n        await self.send(text_data=json.dumps({\n            'type': 'university_announcement',\n            'data': event['data'],\n            'timestamp': event.get('timestamp', timezone.now().isoformat()),\n        }))\n    \n    # Helper methods\n    async def send_error(self, message):\n        \"\"\"Send error message to client\"\"\"\n        await self.send(text_data=json.dumps({\n            'type': 'error',\n            'message': message,\n            'timestamp': timezone.now().isoformat(),\n        }))\n    \n    @database_sync_to_async\n    def get_user_notifications(self):\n        \"\"\"Get user's recent notifications\"\"\"\n        try:\n            from notifications.models import Notification\n            \n            notifications = Notification.objects.filter(\n                recipient=self.user,\n                is_read=False\n            ).order_by('-created_at')[:10]\n            \n            return [\n                {\n                    'id': notif.id,\n                    'title': notif.title,\n                    'message': notif.message,\n                    'type': notif.notification_type,\n                    'created_at': notif.created_at.isoformat(),\n                }\n                for notif in notifications\n            ]\n        except Exception:\n            return []\n    \n    @database_sync_to_async\n    def get_real_time_stats(self):\n        \"\"\"Get real-time statistics\"\"\"\n        try:\n            from .analytics_service import analytics_service\n            return analytics_service.get_real_time_stats()\n        except Exception:\n            return {\n                'users_online': 0,\n                'active_sessions': 0,\n                'timestamp': timezone.now().isoformat(),\n            }\n    \n    @database_sync_to_async\n    def track_user_activity(self, activity_type, metadata):\n        \"\"\"Track user activity\"\"\"\n        try:\n            from .analytics_service import analytics_service\n            analytics_service.track_user_activity(\n                self.user, activity_type, metadata\n            )\n        except Exception:\n            pass  # Fail silently\n\n\nclass NotificationConsumer(AsyncWebsocketConsumer):\n    \"\"\"Specialized consumer for notifications only\"\"\"\n    \n    async def connect(self):\n        \"\"\"Handle connection for notifications\"\"\"\n        self.user = self.scope[\"user\"]\n        \n        if self.user.is_anonymous:\n            await self.close()\n            return\n        \n        # Join user's notification group\n        self.notification_group = f\"notifications_{self.user.id}\"\n        await self.channel_layer.group_add(\n            self.notification_group,\n            self.channel_name\n        )\n        \n        await self.accept()\n    \n    async def disconnect(self, close_code):\n        \"\"\"Handle disconnection\"\"\"\n        if hasattr(self, 'notification_group'):\n            await self.channel_layer.group_discard(\n                self.notification_group,\n                self.channel_name\n            )\n    \n    async def receive(self, text_data):\n        \"\"\"Handle incoming messages\"\"\"\n        try:\n            data = json.loads(text_data)\n            message_type = data.get('type')\n            \n            if message_type == 'mark_as_read':\n                notification_id = data.get('notification_id')\n                await self.mark_notification_as_read(notification_id)\n            \n        except json.JSONDecodeError:\n            pass\n    \n    async def notification_message(self, event):\n        \"\"\"Send notification to user\"\"\"\n        await self.send(text_data=json.dumps({\n            'type': 'new_notification',\n            'data': event['notification'],\n            'timestamp': timezone.now().isoformat(),\n        }))\n    \n    @database_sync_to_async\n    def mark_notification_as_read(self, notification_id):\n        \"\"\"Mark notification as read\"\"\"\n        try:\n            from notifications.models import Notification\n            \n            notification = Notification.objects.get(\n                id=notification_id,\n                recipient=self.user\n            )\n            notification.is_read = True\n            notification.read_at = timezone.now()\n            notification.save()\n            \n            return True\n        except Exception:\n            return False

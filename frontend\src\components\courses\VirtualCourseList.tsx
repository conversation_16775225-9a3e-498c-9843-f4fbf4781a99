import React, { memo, useMemo, useCallback, useRef, useEffect, lazy, Suspense } from 'react';
import { FixedSizeList } from 'react-window';
import { Box, Grid, Typography, useTheme, useMediaQuery } from '@mui/material';
import { Course } from '../../types/courses';
import { CourseCardSkeleton } from './LazyLoadWrapper';

// Lazy load SuperCourseCard for better code splitting
const SuperCourseCard = lazy(() => import('./SuperCourseCard'));

interface VirtualCourseListProps {
  courses: Course[];
  onEnroll?: (courseId: string) => void;
  onViewDetails?: (courseId: string) => void;
  onStartLearning?: (courseId: string) => void;
  onTakeAssessment?: (courseId: string) => void;
  onBookmark?: (courseId: string) => void;
  enrolledCourses?: string[];
  bookmarkedCourses?: string[];
  courseProgress?: Record<string, number>;
  showProgress?: boolean;
  showInteractiveFeatures?: boolean;
  viewMode?: 'grid' | 'list';
  height?: number;
  itemHeight?: number;
  overscan?: number;
  loading?: boolean;
}

interface CourseItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    courses: Course[];
    viewMode: 'grid' | 'list';
    onEnroll?: (courseId: string) => void;
    onViewDetails?: (courseId: string) => void;
    onStartLearning?: (courseId: string) => void;
    onTakeAssessment?: (courseId: string) => void;
    onBookmark?: (courseId: string) => void;
    enrolledCourses: string[];
    bookmarkedCourses: string[];
    courseProgress: Record<string, number>;
    showProgress: boolean;
    showInteractiveFeatures: boolean;
    itemsPerRow: number;
  };
}

// Memoized course item component for virtual list
const CourseItem = memo<CourseItemProps>(({ index, style, data }) => {
  const {
    courses,
    viewMode,
    onEnroll,
    onViewDetails,
    onStartLearning,
    onTakeAssessment,
    onBookmark,
    enrolledCourses,
    bookmarkedCourses,
    courseProgress,
    showProgress,
    showInteractiveFeatures,
    itemsPerRow,
  } = data;

  const startIndex = index * itemsPerRow;
  const endIndex = Math.min(startIndex + itemsPerRow, courses.length);
  const rowCourses = courses.slice(startIndex, endIndex);

  return (
    <div style={style}>
      <Grid container spacing={3} sx={{ px: 0 }}>
        {rowCourses.map((course) => (
          <Grid
            item
            xs={12}
            sm={viewMode === 'grid' ? 6 : 12}
            md={viewMode === 'grid' ? 4 : 12}
            key={course.id}
          >
            <Suspense fallback={<CourseCardSkeleton />}>
              <SuperCourseCard
                course={course}
                onEnroll={onEnroll}
                onViewDetails={onViewDetails}
                onStartLearning={onStartLearning}
                onTakeAssessment={onTakeAssessment}
                onBookmark={onBookmark}
                isEnrolled={enrolledCourses.includes(course.id)}
                isBookmarked={bookmarkedCourses.includes(course.id)}
                showProgress={showProgress}
                progress={courseProgress[course.id] || 0}
                showInteractiveFeatures={showInteractiveFeatures}
                variant={viewMode === 'list' ? 'compact' : 'default'}
              />
            </Suspense>
          </Grid>
        ))}
      </Grid>
    </div>
  );
});

CourseItem.displayName = 'CourseItem';

// Loading skeleton for virtual list
const VirtualListSkeleton = memo<{ height: number; itemHeight: number; itemsPerRow: number }>(
  ({ height, itemHeight, itemsPerRow }) => {
    const skeletonRows = Math.ceil(height / itemHeight);
    
    return (
      <Box sx={{ height }}>
        {Array.from({ length: skeletonRows }).map((_, index) => (
          <Box key={index} sx={{ height: itemHeight, mb: 2 }}>
            <Grid container spacing={3}>
              {Array.from({ length: itemsPerRow }).map((_, cardIndex) => (
                <Grid 
                  item 
                  xs={12} 
                  sm={itemsPerRow === 1 ? 12 : 6} 
                  md={itemsPerRow === 1 ? 12 : 4} 
                  key={cardIndex}
                >
                  <CourseCardSkeleton />
                </Grid>
              ))}
            </Grid>
          </Box>
        ))}
      </Box>
    );
  }
);

VirtualListSkeleton.displayName = 'VirtualListSkeleton';

const VirtualCourseList: React.FC<VirtualCourseListProps> = ({
  courses,
  onEnroll,
  onViewDetails,
  onStartLearning,
  onTakeAssessment,
  onBookmark,
  enrolledCourses = [],
  bookmarkedCourses = [],
  courseProgress = {},
  showProgress = false,
  showInteractiveFeatures = true,
  viewMode = 'grid',
  height = 600,
  itemHeight = 420,
  overscan = 5,
  loading = false,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  
  const listRef = useRef<FixedSizeList>(null);

  // Calculate items per row based on view mode and screen size
  const itemsPerRow = useMemo(() => {
    if (viewMode === 'list') return 1;
    if (isMobile) return 1;
    if (isTablet) return 2;
    return 3; // Desktop grid: 3 items per row
  }, [viewMode, isMobile, isTablet]);

  // Calculate total number of rows needed
  const rowCount = useMemo(() => {
    return Math.ceil(courses.length / itemsPerRow);
  }, [courses.length, itemsPerRow]);

  // Adjust item height based on view mode and screen size
  const adjustedItemHeight = useMemo(() => {
    if (viewMode === 'list') return itemHeight * 0.7; // Compact height for list view
    if (isMobile) return itemHeight * 0.9; // Slightly smaller on mobile
    return itemHeight;
  }, [viewMode, itemHeight, isMobile]);

  // Memoized data object for virtual list
  const itemData = useMemo(() => ({
    courses,
    viewMode,
    onEnroll,
    onViewDetails,
    onStartLearning,
    onTakeAssessment,
    onBookmark,
    enrolledCourses,
    bookmarkedCourses,
    courseProgress,
    showProgress,
    showInteractiveFeatures,
    itemsPerRow,
  }), [
    courses,
    viewMode,
    onEnroll,
    onViewDetails,
    onStartLearning,
    onTakeAssessment,
    onBookmark,
    enrolledCourses,
    bookmarkedCourses,
    courseProgress,
    showProgress,
    showInteractiveFeatures,
    itemsPerRow,
  ]);

  // Scroll to top when courses change
  useEffect(() => {
    if (listRef.current) {
      listRef.current.scrollToItem(0);
    }
  }, [courses]);

  // Show loading skeleton
  if (loading) {
    return (
      <VirtualListSkeleton 
        height={height} 
        itemHeight={adjustedItemHeight} 
        itemsPerRow={itemsPerRow} 
      />
    );
  }

  // Show empty state
  if (courses.length === 0) {
    return (
      <Box
        sx={{
          height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column',
        }}
      >
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No courses found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Try adjusting your search criteria or filters
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        height,
        width: '100%',
        '& .virtual-list-container': {
          // Custom scrollbar styling
          '&::-webkit-scrollbar': {
            width: 8,
          },
          '&::-webkit-scrollbar-track': {
            background: theme.palette.grey[100],
            borderRadius: 4,
          },
          '&::-webkit-scrollbar-thumb': {
            background: theme.palette.grey[400],
            borderRadius: 4,
            '&:hover': {
              background: theme.palette.grey[600],
            },
          },
        },
      }}
    >
      <FixedSizeList
        ref={listRef}
        className="virtual-list-container"
        height={height}
        itemCount={rowCount}
        itemSize={adjustedItemHeight}
        itemData={itemData}
        overscanCount={overscan}
        width="100%"
      >
        {CourseItem}
      </FixedSizeList>
    </Box>
  );
};

export default memo(VirtualCourseList);

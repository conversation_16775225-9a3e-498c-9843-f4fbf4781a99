import { axiosInstance } from '../config/axios';
import { API_ENDPOINTS } from '../config/api';

// Types for blockchain credentials
export interface BlockchainNetwork {
  id: number;
  name: string;
  symbol: string;
  rpc_url: string;
  chain_id: number;
  explorer_url: string;
  is_testnet: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CredentialTemplate {
  id: number;
  name: string;
  description: string;
  schema: any;
  template_type: string;
  requirements: any;
  design_template: any;
  created_at: string;
  updated_at: string;
}

export interface BlockchainCredential {
  id: number;
  student: number;
  course: number;
  template: number;
  network: number;
  credential_data: any;
  token_id: string;
  transaction_hash: string;
  contract_address: string;
  metadata_uri: string;
  is_minted: boolean;
  is_verified: boolean;
  issued_at: string;
  verified_at: string;
  created_at: string;
  updated_at: string;
}

export interface NFTAchievement {
  id: number;
  student: number;
  achievement_type: string;
  title: string;
  description: string;
  network: number;
  metadata: any;
  image_url: string;
  animation_url: string;
  external_url: string;
  attributes: any;
  token_id: string;
  transaction_hash: string;
  contract_address: string;
  rarity: string;
  is_minted: boolean;
  is_verified: boolean;
  earned_at: string;
  minted_at: string;
  created_at: string;
  updated_at: string;
}

export interface WalletAddress {
  id: number;
  student: number;
  network: number;
  address: string;
  is_primary: boolean;
  is_verified: boolean;
  verification_signature: string;
  created_at: string;
  updated_at: string;
}

export interface SmartContract {
  id: number;
  network: number;
  contract_type: string;
  name: string;
  address: string;
  abi: any;
  bytecode: string;
  is_deployed: boolean;
  deployment_tx: string;
  created_at: string;
  updated_at: string;
}

export interface BlockchainDashboard {
  total_credentials: number;
  minted_credentials: number;
  verified_credentials: number;
  total_nft_achievements: number;
  minted_nft_achievements: number;
  unique_wallet_addresses: number;
  active_networks: number;
  recent_transactions: any[];
  network_stats: any;
  gas_usage_stats: any;
}

// API Service Class
export class BlockchainCredentialsService {
  // Networks
  async getNetworks(): Promise<BlockchainNetwork[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.NETWORKS);
    return response.data.results || response.data;
  }

  async getNetwork(id: number): Promise<BlockchainNetwork> {
    const response = await axiosInstance.get(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.NETWORKS}${id}/`);
    return response.data;
  }

  async createNetwork(data: Partial<BlockchainNetwork>): Promise<BlockchainNetwork> {
    const response = await axiosInstance.post(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.NETWORKS, data);
    return response.data;
  }

  async updateNetwork(id: number, data: Partial<BlockchainNetwork>): Promise<BlockchainNetwork> {
    const response = await axiosInstance.put(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.NETWORKS}${id}/`, data);
    return response.data;
  }

  async deleteNetwork(id: number): Promise<void> {
    await axiosInstance.delete(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.NETWORKS}${id}/`);
  }

  // Templates
  async getTemplates(): Promise<CredentialTemplate[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.TEMPLATES);
    return response.data.results || response.data;
  }

  async getTemplate(id: number): Promise<CredentialTemplate> {
    const response = await axiosInstance.get(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.TEMPLATES}${id}/`);
    return response.data;
  }

  async createTemplate(data: Partial<CredentialTemplate>): Promise<CredentialTemplate> {
    const response = await axiosInstance.post(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.TEMPLATES, data);
    return response.data;
  }

  async updateTemplate(id: number, data: Partial<CredentialTemplate>): Promise<CredentialTemplate> {
    const response = await axiosInstance.put(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.TEMPLATES}${id}/`, data);
    return response.data;
  }

  async deleteTemplate(id: number): Promise<void> {
    await axiosInstance.delete(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.TEMPLATES}${id}/`);
  }

  // Credentials
  async getCredentials(): Promise<BlockchainCredential[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.CREDENTIALS);
    return response.data.results || response.data;
  }

  async getCredential(id: number): Promise<BlockchainCredential> {
    const response = await axiosInstance.get(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.CREDENTIALS}${id}/`);
    return response.data;
  }

  async createCredential(data: Partial<BlockchainCredential>): Promise<BlockchainCredential> {
    const response = await axiosInstance.post(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.CREDENTIALS, data);
    return response.data;
  }

  async updateCredential(id: number, data: Partial<BlockchainCredential>): Promise<BlockchainCredential> {
    const response = await axiosInstance.put(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.CREDENTIALS}${id}/`, data);
    return response.data;
  }

  async deleteCredential(id: number): Promise<void> {
    await axiosInstance.delete(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.CREDENTIALS}${id}/`);
  }

  async mintCredential(id: number, data?: any): Promise<BlockchainCredential> {
    const response = await axiosInstance.post(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.MINT_CREDENTIAL(id), data);
    return response.data;
  }

  async verifyCredential(id: number, data?: any): Promise<BlockchainCredential> {
    const response = await axiosInstance.post(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.VERIFY_CREDENTIAL(id), data);
    return response.data;
  }

  // NFT Achievements
  async getNFTAchievements(): Promise<NFTAchievement[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.NFT_ACHIEVEMENTS);
    return response.data.results || response.data;
  }

  async getNFTAchievement(id: number): Promise<NFTAchievement> {
    const response = await axiosInstance.get(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.NFT_ACHIEVEMENTS}${id}/`);
    return response.data;
  }

  async createNFTAchievement(data: Partial<NFTAchievement>): Promise<NFTAchievement> {
    const response = await axiosInstance.post(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.NFT_ACHIEVEMENTS, data);
    return response.data;
  }

  async updateNFTAchievement(id: number, data: Partial<NFTAchievement>): Promise<NFTAchievement> {
    const response = await axiosInstance.put(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.NFT_ACHIEVEMENTS}${id}/`, data);
    return response.data;
  }

  async deleteNFTAchievement(id: number): Promise<void> {
    await axiosInstance.delete(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.NFT_ACHIEVEMENTS}${id}/`);
  }

  async mintNFT(id: number, data?: any): Promise<NFTAchievement> {
    const response = await axiosInstance.post(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.MINT_NFT(id), data);
    return response.data;
  }

  async verifyNFT(id: number, data?: any): Promise<NFTAchievement> {
    const response = await axiosInstance.post(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.VERIFY_NFT(id), data);
    return response.data;
  }

  // Wallet Addresses
  async getWalletAddresses(): Promise<WalletAddress[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.WALLET_ADDRESSES);
    return response.data.results || response.data;
  }

  async getWalletAddress(id: number): Promise<WalletAddress> {
    const response = await axiosInstance.get(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.WALLET_ADDRESSES}${id}/`);
    return response.data;
  }

  async createWalletAddress(data: Partial<WalletAddress>): Promise<WalletAddress> {
    const response = await axiosInstance.post(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.WALLET_ADDRESSES, data);
    return response.data;
  }

  async updateWalletAddress(id: number, data: Partial<WalletAddress>): Promise<WalletAddress> {
    const response = await axiosInstance.put(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.WALLET_ADDRESSES}${id}/`, data);
    return response.data;
  }

  async deleteWalletAddress(id: number): Promise<void> {
    await axiosInstance.delete(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.WALLET_ADDRESSES}${id}/`);
  }

  // Smart Contracts
  async getSmartContracts(): Promise<SmartContract[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.SMART_CONTRACTS);
    return response.data.results || response.data;
  }

  async getSmartContract(id: number): Promise<SmartContract> {
    const response = await axiosInstance.get(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.SMART_CONTRACTS}${id}/`);
    return response.data;
  }

  async createSmartContract(data: Partial<SmartContract>): Promise<SmartContract> {
    const response = await axiosInstance.post(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.SMART_CONTRACTS, data);
    return response.data;
  }

  async updateSmartContract(id: number, data: Partial<SmartContract>): Promise<SmartContract> {
    const response = await axiosInstance.put(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.SMART_CONTRACTS}${id}/`, data);
    return response.data;
  }

  async deleteSmartContract(id: number): Promise<void> {
    await axiosInstance.delete(`${API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.SMART_CONTRACTS}${id}/`);
  }

  // Student endpoints
  async getMyCredentials(): Promise<BlockchainCredential[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.STUDENT.MY_CREDENTIALS);
    return response.data.results || response.data;
  }

  async getMyAchievements(): Promise<NFTAchievement[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.STUDENT.MY_ACHIEVEMENTS);
    return response.data.results || response.data;
  }

  async getMyWallet(): Promise<WalletAddress[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.STUDENT.MY_WALLET);
    return response.data.results || response.data;
  }

  // Admin endpoints
  async getAdminDashboard(): Promise<BlockchainDashboard> {
    const response = await axiosInstance.get(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.ADMIN.DASHBOARD);
    return response.data;
  }

  async getPendingCredentials(): Promise<BlockchainCredential[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.ADMIN.PENDING_CREDENTIALS);
    return response.data.results || response.data;
  }

  async getBlockchainStats(): Promise<any> {
    const response = await axiosInstance.get(API_ENDPOINTS.BLOCKCHAIN_CREDENTIALS.ADMIN.BLOCKCHAIN_STATS);
    return response.data;
  }
}

// Export singleton instance
export const blockchainCredentialsService = new BlockchainCredentialsService();

"""
Minimal URL configuration with authentication for testing.
"""
from django.contrib import admin
from django.urls import include, path
from django.http import JsonResponse

def api_status(request):
    return JsonResponse({
        "status": "ok", 
        "message": "University Management System API is running",
        "auth_available": True
    })

urlpatterns = [
    path("admin/", admin.site.urls),
    path("api/status/", api_status, name="api-status"),
    
    # Authentication endpoints
    path("api/v1/auth/", include("auth_api.urls")),
    path("api/v1/users/", include("users.urls")),
]

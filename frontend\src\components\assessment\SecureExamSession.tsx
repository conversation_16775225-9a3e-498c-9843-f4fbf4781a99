/**
 * SecureExamSession Component
 * 
 * A complete secure exam interface that integrates all lockdown and timer features.
 * Refuses to render unless lockdown environment is verified.
 */

import React, { useEffect, useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Grid,
  Paper,
  Snackbar
} from '@mui/material';
import {
  Security,
  Download,
  OpenInNew,
  ExitToApp,
  Warning
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useExamLockdown } from '../../hooks/useExamLockdown';
import ExamTimer from './ExamTimer';
import LockdownStatus from './LockdownStatus';

const ExamContainer = styled(Container)(({ theme }) => ({
  height: '100vh',
  display: 'flex',
  flexDirection: 'column',
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.default,
}));

const ExamHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[2],
}));

const ExamContent = styled(Paper)(({ theme }) => ({
  flex: 1,
  padding: theme.spacing(3),
  marginBottom: theme.spacing(2),
  display: 'flex',
  flexDirection: 'column',
}));

const LockdownGuard = styled(Box)(({ theme }) => ({
  height: '100vh',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'center',
  backgroundColor: theme.palette.background.default,
  padding: theme.spacing(3),
}));

interface SecureExamSessionProps {
  assessmentId: number;
  assessmentTitle: string;
  requireLockdown?: boolean;
  onExamComplete?: (results: any) => void;
  onExamExit?: () => void;
}

export const SecureExamSession: React.FC<SecureExamSessionProps> = ({
  assessmentId,
  assessmentTitle,
  requireLockdown = true,
  onExamComplete,
  onExamExit
}) => {
  
  // Exam lockdown hook
  const {
    isLockdownCapable,
    isLockdownActive,
    environment,
    handshakeToken,
    sessionData,
    timeRemaining,
    isInGracePeriod,
    shouldAutoSubmit,
    violations,
    violationCount,
    isLoading,
    isValidating,
    error,
    startExamSession,
    submitExamSession,
    logViolation,
    validateEnvironment,
    canRenderExam
  } = useExamLockdown(requireLockdown);
  
  // Local state
  const [examStarted, setExamStarted] = useState(false);
  const [showPreExamDialog, setShowPreExamDialog] = useState(false);
  const [showExitConfirm, setShowExitConfirm] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string | null>(null);
  const [lastViolationCount, setLastViolationCount] = useState(0);
  
  // Auto-submit when time is up
  useEffect(() => {
    if (shouldAutoSubmit && examStarted) {
      handleSubmitExam(true);
    }
  }, [shouldAutoSubmit, examStarted]);
  
  // Show violation alerts
  useEffect(() => {
    if (violationCount > lastViolationCount) {
      const newViolations = violations.slice(lastViolationCount);
      const latestViolation = newViolations[newViolations.length - 1];
      
      setSnackbarMessage(
        `Security Alert: ${latestViolation.type.replace(/_/g, ' ')}`
      );
      setLastViolationCount(violationCount);
    }
  }, [violationCount, violations, lastViolationCount]);
  
  // Handle exam start
  const handleStartExam = async () => {
    const success = await startExamSession(assessmentId, {
      secure_mode: true,
      browser_lockdown: true,
      proctoring_enabled: true
    });
    
    if (success) {
      setExamStarted(true);
      setShowPreExamDialog(false);
    }
  };
  
  // Handle exam submission
  const handleSubmitExam = async (forced = false) => {
    const success = await submitExamSession(forced);
    
    if (success) {
      setExamStarted(false);
      onExamComplete?.(sessionData);
    }
  };
  
  // Handle exam exit
  const handleExitExam = () => {
    setShowExitConfirm(true);
  };
  
  const confirmExitExam = async () => {
    await submitExamSession(true);
    setShowExitConfirm(false);
    setExamStarted(false);
    onExamExit?.();
  };
  
  // Download SEB config
  const handleDownloadSEBConfig = () => {
    if (sessionData) {
      const url = `/api/v1/assessment/exam-security/session/${sessionData.session_token}/seb-config/`;
      window.open(url, '_blank');
    }
  };
  
  // Open Respondus LDB
  const handleOpenRespondus = async () => {
    if (sessionData) {
      try {
        const response = await fetch(
          `/api/v1/assessment/exam-security/session/${sessionData.session_token}/respondus-link/`
        );
        const data = await response.json();
        
        if (data.success) {
          window.location.href = data.respondus_link;
        }
      } catch (err) {
        console.error('Failed to get Respondus link:', err);
      }
    }
  };
  
  // Pre-exam setup dialog
  const PreExamDialog = () => (
    <Dialog
      open={showPreExamDialog}
      onClose={() => setShowPreExamDialog(false)}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <Security />
          Secure Exam Setup
        </Box>
      </DialogTitle>
      <DialogContent>
        <Box mb={3}>
          <Typography variant="h6" gutterBottom>
            {assessmentTitle}
          </Typography>
          <Alert severity="info" sx={{ mb: 2 }}>
            This exam requires a secure lockdown environment. Please follow the setup instructions below.
          </Alert>
        </Box>
        
        <LockdownStatus
          isLockdownCapable={isLockdownCapable}
          isLockdownActive={isLockdownActive}
          environment={environment}
          violations={violations}
          isValidating={isValidating}
          error={error}
        />
        
        {requireLockdown && !isLockdownCapable && (
          <Box mt={2}>
            <Alert severity="warning">
              Your browser does not support lockdown mode. Please use one of the following options:
            </Alert>
            <Box mt={2} display="flex" gap={2}>
              <Button
                variant="outlined"
                startIcon={<Download />}
                onClick={handleDownloadSEBConfig}
                disabled={!sessionData}
              >
                Download SEB Config
              </Button>
              <Button
                variant="outlined"
                startIcon={<OpenInNew />}
                onClick={handleOpenRespondus}
                disabled={!sessionData}
              >
                Open Respondus LDB
              </Button>
            </Box>
          </Box>
        )}
        
        {environment?.handshake_token && (
          <Alert severity="success" sx={{ mt: 2 }}>
            ✓ Lockdown environment verified with handshake token
          </Alert>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setShowPreExamDialog(false)}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleStartExam}
          disabled={requireLockdown && !canRenderExam}
          startIcon={isLoading ? <CircularProgress size={20} /> : <Security />}
        >
          {isLoading ? 'Starting...' : 'Start Secure Exam'}
        </Button>
      </DialogActions>
    </Dialog>
  );
  
  // Exit confirmation dialog
  const ExitConfirmDialog = () => (
    <Dialog open={showExitConfirm} onClose={() => setShowExitConfirm(false)}>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <Warning color="warning" />
          Confirm Exit
        </Box>
      </DialogTitle>
      <DialogContent>
        <Typography>
          Are you sure you want to exit the exam? This will submit your current responses and end the exam session.
        </Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setShowExitConfirm(false)}>
          Cancel
        </Button>
        <Button
          variant="contained"
          color="warning"
          onClick={confirmExitExam}
        >
          Exit Exam
        </Button>
      </DialogActions>
    </Dialog>
  );
  
  // Lockdown guard - show this if lockdown is required but not available
  if (requireLockdown && !canRenderExam && !showPreExamDialog) {
    return (
      <LockdownGuard>
        <Security sx={{ fontSize: 64, color: 'warning.main', mb: 2 }} />
        <Typography variant="h4" gutterBottom align="center">
          Secure Exam Environment Required
        </Typography>
        <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 3 }}>
          This exam requires a secure lockdown browser environment. 
          Please set up your security environment to continue.
        </Typography>
        
        <Box mb={3} width="100%" maxWidth={600}>
          <LockdownStatus
            isLockdownCapable={isLockdownCapable}
            isLockdownActive={isLockdownActive}
            environment={environment}
            violations={violations}
            isValidating={isValidating}
            error={error}
          />
        </Box>
        
        <Button
          variant="contained"
          size="large"
          onClick={() => setShowPreExamDialog(true)}
          startIcon={<Security />}
        >
          Setup Secure Environment
        </Button>
      </LockdownGuard>
    );
  }
  
  // Show exam content if lockdown is verified or not required
  if (!examStarted) {
    return (
      <ExamContainer>
        <Box display="flex" justifyContent="center" alignItems="center" height="100%">
          <Paper sx={{ p: 4, maxWidth: 600, width: '100%' }}>
            <Typography variant="h4" gutterBottom align="center">
              {assessmentTitle}
            </Typography>
            <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 3 }}>
              Ready to begin your secure exam session.
            </Typography>
            
            <Box mb={3}>
              <LockdownStatus
                isLockdownCapable={isLockdownCapable}
                isLockdownActive={isLockdownActive}
                environment={environment}
                violations={violations}
                isValidating={isValidating}
                error={error}
              />
            </Box>
            
            <Box display="flex" justifyContent="center">
              <Button
                variant="contained"
                size="large"
                onClick={() => setShowPreExamDialog(true)}
                startIcon={<Security />}
              >
                Begin Exam
              </Button>
            </Box>
          </Paper>
        </Box>
        
        <PreExamDialog />
      </ExamContainer>
    );
  }
  
  // Main exam interface
  return (
    <ExamContainer maxWidth={false}>
      {/* Exam Header */}
      <ExamHeader>
        <Typography variant="h5">
          {assessmentTitle}
        </Typography>
        <Box display="flex" alignItems="center" gap={2}>
          {sessionData && (
            <ExamTimer
              timeRemainingSeconds={timeRemaining}
              totalDurationSeconds={sessionData.time_remaining_seconds + (timeRemaining)}
              isInGracePeriod={isInGracePeriod}
              gracePeriodRemaining={sessionData.grace_period_remaining || 0}
              shouldAutoSubmit={shouldAutoSubmit}
            />
          )}
          <Button
            variant="outlined"
            color="warning"
            startIcon={<ExitToApp />}
            onClick={handleExitExam}
          >
            Exit Exam
          </Button>
        </Box>
      </ExamHeader>
      
      {/* Main Content Area */}
      <Grid container spacing={2} sx={{ flex: 1 }}>
        <Grid item xs={12} lg={9}>
          <ExamContent>
            <Typography variant="h6" gutterBottom>
              Exam Questions
            </Typography>
            {/* This is where actual exam questions would be rendered */}
            <Box flex={1} display="flex" alignItems="center" justifyContent="center">
              <Typography variant="body1" color="text.secondary">
                Exam questions would be rendered here...
              </Typography>
            </Box>
            
            <Box display="flex" justifyContent="space-between" mt={2}>
              <Button variant="outlined">
                Previous
              </Button>
              <Button variant="contained">
                Next
              </Button>
            </Box>
          </ExamContent>
        </Grid>
        
        <Grid item xs={12} lg={3}>
          <LockdownStatus
            isLockdownCapable={isLockdownCapable}
            isLockdownActive={isLockdownActive}
            environment={environment}
            violations={violations}
            isValidating={isValidating}
            error={error}
          />
        </Grid>
      </Grid>
      
      {/* Submit Button */}
      <Box display="flex" justifyContent="center" p={2}>
        <Button
          variant="contained"
          size="large"
          onClick={() => handleSubmitExam(false)}
          disabled={isLoading}
        >
          {isLoading ? 'Submitting...' : 'Submit Exam'}
        </Button>
      </Box>
      
      {/* Dialogs */}
      <PreExamDialog />
      <ExitConfirmDialog />
      
      {/* Violation Alerts */}
      <Snackbar
        open={!!snackbarMessage}
        autoHideDuration={6000}
        onClose={() => setSnackbarMessage(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbarMessage(null)}
          severity="warning"
          variant="filled"
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </ExamContainer>
  );
};

export default SecureExamSession;

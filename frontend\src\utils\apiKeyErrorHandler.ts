/**
 * API Key Error Handler
 *
 * This utility provides functions for detecting and handling API key errors.
 */

import { AxiosError } from 'axios';

/**
 * Check if an error is related to an invalid or missing API key
 * @param error The error to check
 * @returns True if the error is related to an API key issue
 */
export const isApiKeyError = (error: any): boolean => {
  if (!error) return false;

  // Check if it's an Axios error
  const axiosError = error as AxiosError;

  // Check error message
  const errorMessage = error.message || error.toString();
  if (typeof errorMessage === 'string') {
    const lowerMessage = errorMessage.toLowerCase();
    if (
      lowerMessage.includes('api key expired') ||
      lowerMessage.includes('api_key_invalid') ||
      lowerMessage.includes('invalid api key') ||
      lowerMessage.includes('no api key configured') ||
      lowerMessage.includes('missing api key')
    ) {
      return true;
    }
  }

  // Check response data
  if (axiosError.response?.data) {
    const responseData = axiosError.response.data as any;
    const responseMessage =
      responseData.message || responseData.detail || responseData.error || '';

    if (typeof responseMessage === 'string') {
      const lowerResponseMessage = responseMessage.toLowerCase();
      if (
        lowerResponseMessage.includes('api key expired') ||
        lowerResponseMessage.includes('api_key_invalid') ||
        lowerResponseMessage.includes('invalid api key') ||
        lowerResponseMessage.includes('no api key configured') ||
        lowerResponseMessage.includes('missing api key')
      ) {
        return true;
      }
    }
  }

  // Check status code - 401 or 403 might indicate API key issues
  if (
    axiosError.response?.status === 401 ||
    axiosError.response?.status === 403
  ) {
    // Check if the error is specifically about API keys
    const responseData = axiosError.response.data as any;
    if (responseData && typeof responseData === 'object') {
      const errorKeys = Object.keys(responseData);
      for (const key of errorKeys) {
        if (
          key.toLowerCase().includes('api_key') ||
          key.toLowerCase().includes('apikey')
        ) {
          return true;
        }
      }
    }
  }

  return false;
};

/**
 * Get a user-friendly error message for API key errors
 * @param error The error to get a message for
 * @returns A user-friendly error message
 */
export const getApiKeyErrorMessage = (error: any): string => {
  if (!isApiKeyError(error)) {
    return 'An unknown error occurred';
  }

  // Default message
  let message =
    'There was an issue with the AI service API key. Please update it in the API Key Management page.';

  // Try to extract a more specific message from the error
  const axiosError = error as AxiosError;
  if (axiosError.response?.data) {
    const responseData = axiosError.response.data as any;
    if (responseData.message) {
      message = responseData.message;
    } else if (responseData.detail) {
      message = responseData.detail;
    } else if (responseData.error) {
      message = responseData.error;
    }
  } else if (error.message) {
    message = error.message;
  }

  return message;
};

export default {
  isApiKeyError,
  getApiKeyErrorMessage,
};

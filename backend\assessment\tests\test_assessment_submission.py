import json
from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from assessment.models import (
    Assessment,
    AssessmentQuestion,
    AssessmentResponse,
    StudentLevel
)
from users.models import CustomUser

User = get_user_model()

class AssessmentSubmissionTestCase(TestCase):
    """Test case for assessment submission process"""

    def setUp(self):
        """Set up test data"""
        # Create a student user
        self.student = User.objects.create_user(
            username="teststudent",
            email="<EMAIL>",
            password="testpassword",
            first_name="Test",
            last_name="Student",
            role="STUDENT"
        )
        # Create an admin user
        self.admin = User.objects.create_user(
            username="testadmin",
            email="<EMAIL>",
            password="testpassword",
            first_name="Test",
            last_name="Admin",
            role="ADMIN",
            is_staff=True,
            is_superuser=True
        )
        # Create test questions
        self.questions = []
        for i in range(5):
            question = AssessmentQuestion.objects.create(
                text=f"Test Question {i+1}",
                question_type="MULTIPLE_CHOICE",
                options=json.dumps([
                    "Option A",
                    "Option B",
                    "Option C",
                    "Option D"
                ]),
                correct_answer="Option A",
                difficulty_level=1,
                category="TEST"
            )
            self.questions.append(question)

        # Create a test assessment
        self.assessment = Assessment.objects.create(
            student=self.student,
            assessment_type="PLACEMENT",
            status="IN_PROGRESS",
            initial_level=1
        )
        # Add questions to the assessment
        for question in self.questions:
            self.assessment.questions.add(question)

        # Create student level
        self.student_level = StudentLevel.objects.create(
            student=self.student,
            current_level=1,
            current_level_display="Beginner"
        )

        # Set up API client
        self.client = APIClient()

    def test_student_assessment_submission(self):
        """Test student assessment submission"""
        # Login as student
        self.client.force_authenticate(user=self.student)

        # Prepare submission data
        responses = []
        for question in self.questions:
            responses.append({
                "question_id": question.id,
                "answer": "Option A",  # Correct answer
                "time_spent": 30
            })
        submission_data = {
            "assessment_id": self.assessment.id,
            "responses": responses
        }

        # Submit assessment
        url = reverse("student-assessment-submit")
        response = self.client.post(url, submission_data, format="json")

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], "success")

        # Check assessment was updated
        assessment = Assessment.objects.get(id=self.assessment.id)
        self.assertEqual(assessment.status, "COMPLETED")
        self.assertEqual(assessment.completed, True)
        self.assertIsNotNone(assessment.score)

        # Check responses were created
        responses = AssessmentResponse.objects.filter(assessment=self.assessment)
        self.assertEqual(responses.count(), 5)

        # Check student level was updated
        student_level = StudentLevel.objects.get(student=self.student)
        self.assertIsNotNone(student_level.progression_history)

    def test_public_assessment_submission(self):
        """Test public assessment submission"""
        # Create a public assessment
        public_assessment = Assessment.objects.create(
            student=self.student,
            assessment_type="PLACEMENT",
            status="IN_PROGRESS",
            initial_level=1
        )
        # Add questions to the assessment
        for question in self.questions:
            public_assessment.questions.add(question)

        # Prepare submission data
        responses = []
        for question in self.questions:
            responses.append({
                "question_id": question.id,
                "answer": "Option A",  # Correct answer
                "time_spent": 30
            })
        submission_data = {
            "assessment_id": public_assessment.id,
            "user_id": self.student.id,
            "responses": responses
        }

        # Submit assessment
        url = reverse("public-assessment-submit")
        response = self.client.post(url, submission_data, format="json")

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], "success")

        # Check assessment was updated
        assessment = Assessment.objects.get(id=public_assessment.id)
        self.assertEqual(assessment.status, "COMPLETED")
        self.assertEqual(assessment.completed, True)
        self.assertIsNotNone(assessment.score)

        # Check responses were created
        responses = AssessmentResponse.objects.filter(assessment=public_assessment)
        self.assertEqual(responses.count(), 5)

    def test_admin_can_view_student_assessments(self):
        """Test admin can view student assessments"""
        # Login as admin
        self.client.force_authenticate(user=self.admin)

        # Submit the assessment first
        self.assessment.status = "COMPLETED"
        self.assessment.completed = True
        self.assessment.score = 100
        self.assessment.save()

        # Create some responses
        for question in self.questions:
            AssessmentResponse.objects.create(
                assessment=self.assessment,
                question=question,
                answer="Option A",
                student_answer="Option A",
                is_correct=True,
                time_spent=30
            )

        # Get student assessments
        url = reverse("admin-student-assessments", kwargs={"student_id": self.student.id})
        response = self.client.get(url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], "success")

        # Check assessment data is returned
        self.assertEqual(len(response.data["data"]), 1)
        self.assertEqual(response.data["data"][0]["id"], self.assessment.id)

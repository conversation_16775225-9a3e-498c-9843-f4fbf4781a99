/**
 * Unified AI Service - Frontend Consolidation
 * 
 * This service consolidates all AI-related functionality into a single,
 * unified interface to eliminate duplication and improve maintainability.
 * 
 * Features:
 * - Centralized AI operations
 * - Backward compatibility with legacy services
 * - Enhanced error handling and fallbacks
 * - Configuration management
 * - Service health monitoring
 */

import { API_ENDPOINTS } from '../config/api';
import { BaseAIService } from './utils/BaseAIService';
import { 
  AIServiceError
} from './utils/aiServiceUtils';

// ==========================================
// TYPES & INTERFACES
// ==========================================

export interface UnifiedAICapabilities {
  chatbot: boolean;
  aiAssistant: boolean;
  studyAssistant: boolean;
  contentGeneration: boolean;
  multiAgent: boolean;
  speechSynthesis: boolean;
}

export interface AIServiceHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  services: {
    [key: string]: {
      healthy: boolean;
      latency?: number;
      lastCheck: string;
      error?: string;
    };
  };
  overall: {
    healthyCount: number;
    totalCount: number;
    averageLatency: number;
  };
}

export interface UnifiedAIResponse<T = any> {
  data: T;
  metadata: {
    service: string;
    model: string;
    confidence?: number;
    processingTime: number;
    cacheHit: boolean;
    fallbackUsed: boolean;
  };
  status: 'success' | 'partial' | 'fallback';
}

// Legacy interfaces for backward compatibility
export interface SuggestionItem {
  id: number;
  text: string;
  icon: string;
  category?: string;
}

export interface ChatMessage {
  id: number;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  metadata?: any;
}

export interface StudyPlan {
  id: string;
  title: string;
  description: string;
  topics: string[];
  estimated_hours: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

// ==========================================
// UNIFIED AI SERVICE CLASS
// ==========================================

class UnifiedAIService extends BaseAIService {
  private capabilities: UnifiedAICapabilities;
  private serviceHealth: Map<string, any> = new Map();
  private lastHealthCheck: Date = new Date();
  constructor() {
    super({
      serviceName: 'Unified AI',
      baseEndpoint: API_ENDPOINTS.UNIFIED_AI.BASE,
      config: {
        timeout: 45000, // Longer timeout for complex AI operations
        retries: 3,
        fallbackEnabled: true,
        cacheEnabled: true,
        cacheTTL: 600000, // 10 minutes
      },
    });

    this.capabilities = {
      chatbot: true,
      aiAssistant: true,
      studyAssistant: true,
      contentGeneration: true,
      multiAgent: true,
      speechSynthesis: true,
    };

    this.initializeHealthMonitoring();
  }

  // ==========================================
  // CORE AI METHODS
  // ==========================================
  /**
   * Primary AI chat method - consolidates all conversational AI
   */
  async chat(
    message: string,
    context: {
      type?: 'general' | 'tutoring' | 'study' | 'support';
      userId?: string;
      conversationId?: string;
      courseId?: string;
      metadata?: any;
    } = {}
  ): Promise<UnifiedAIResponse<ChatMessage>> {
    const startTime = Date.now();
    
    try {
      // Determine the appropriate endpoint based on context
      const endpoint = this.getEndpointForContext(context.type);
      
      const response = await this.post(endpoint, {
        message,
        context,
        unified: true, // Flag for backend to use unified service
      });

      return {
        data: response.data,
        metadata: {
          service: 'unified_ai',
          model: response.data.model || 'gemini-2.0-flash',
          confidence: response.data.confidence_score,
          processingTime: Date.now() - startTime,
          cacheHit: false,
          fallbackUsed: false,
        },
        status: 'success',
      };
    } catch (error) {
      console.error('Chat request failed:', error);
      
      // Return fallback response
      return {
        data: {
          id: Date.now(),
          role: 'assistant' as const,
          content: "I'm temporarily unable to provide a response. Please try again in a moment.",
          timestamp: new Date().toISOString(),
          metadata: { fallback: true },
        },
        metadata: {
          service: 'unified_ai',
          model: 'fallback',
          processingTime: Date.now() - startTime,
          cacheHit: false,
          fallbackUsed: true,
        },
        status: 'fallback',
      };
    }
  }
  /**
   * Generate AI suggestions - consolidates suggestion functionality
   */
  async getSuggestions(
    userType: string = 'all',
    context: { category?: string; limit?: number } = {}
  ): Promise<UnifiedAIResponse<SuggestionItem[]>> {
    const startTime = Date.now();
    
    try {
      const response = await this.get('suggestions/', {
        user_type: userType,
        category: context.category,
        limit: context.limit || 10,
        unified: true,
      });

      return {
        data: response.data,
        metadata: {
          service: 'unified_ai',
          model: 'suggestion_engine',
          processingTime: Date.now() - startTime,
          cacheHit: false,
          fallbackUsed: false,
        },
        status: 'success',
      };
    } catch (error) {
      console.error('Suggestions request failed:', error);
      
      return {
        data: this.getFallbackSuggestions(userType),
        metadata: {
          service: 'unified_ai',
          model: 'fallback',
          processingTime: Date.now() - startTime,
          cacheHit: false,
          fallbackUsed: true,
        },
        status: 'fallback',
      };
    }
  }
  /**
   * Generate study content - consolidates study assistant functionality
   */
  async generateStudyContent(
    type: 'plan' | 'flashcards' | 'questions' | 'summary',
    params: any
  ): Promise<UnifiedAIResponse<any>> {
    const startTime = Date.now();
    
    try {
      const endpoint = `${type}/`;
      const response = await this.post(endpoint, { ...params, unified: true });

      return {
        data: response.data,
        metadata: {
          service: 'unified_ai',
          model: response.data.model || 'gemini-2.0-flash',
          processingTime: Date.now() - startTime,
          cacheHit: false,
          fallbackUsed: false,
        },
        status: 'success',
      };
    } catch (error) {
      console.error(`Study content generation failed for type ${type}:`, error);
      throw new AIServiceError(`Failed to generate ${type}`, 'GENERATION_ERROR');
    }
  }

  // ==========================================
  // BACKWARD COMPATIBILITY METHODS
  // ==========================================

  /**
   * Legacy AI Assistant compatibility
   */
  async askQuestion(question: string, context: any = {}): Promise<any> {
    const response = await this.chat(question, { 
      type: 'general', 
      ...context 
    });
    return {
      answer: response.data.content,
      confidence_score: response.metadata.confidence || 0.8,
      response_time: response.metadata.processingTime,
      session_id: context.sessionId || 'unified-session',
    };
  }

  /**
   * Legacy Chatbot compatibility
   */
  async sendMessage(message: string, conversationId?: number): Promise<any> {
    const response = await this.chat(message, { 
      type: 'tutoring',
      conversationId: conversationId?.toString()
    });
    return {
      conversation_id: conversationId || Date.now(),
      message: response.data,
    };
  }
  async getConversations(): Promise<any[]> {
    try {
      const response = await this.get('conversations/', { unified: true });
      return response.data;
    } catch (error) {
      console.error('Failed to get conversations:', error);
      return [];
    }
  }

  /**
   * Legacy Study Assistant compatibility
   */
  async generateStudyPlan(params: any): Promise<StudyPlan> {
    const response = await this.generateStudyContent('plan', params);
    return response.data;
  }

  async generateFlashcards(params: any): Promise<any[]> {
    const response = await this.generateStudyContent('flashcards', params);
    return response.data;
  }
  async getStudyTopics(): Promise<any[]> {
    try {
      const response = await this.get('topics/', { unified: true });
      return response.data;
    } catch (error) {
      console.error('Failed to get study topics:', error);
      return [];
    }
  }

  async getStudySessions(): Promise<any[]> {
    try {
      const response = await this.get('sessions/', { unified: true });
      return response.data;
    } catch (error) {
      console.error('Failed to get study sessions:', error);
      return [];
    }
  }

  // ==========================================
  // ADVANCED FEATURES
  // ==========================================

  /**
   * Get AI service capabilities
   */
  getCapabilities(): UnifiedAICapabilities {
    return { ...this.capabilities };
  }
  /**
   * Execute AI workflow (multi-step operations)
   */
  async executeWorkflow(
    workflowType: string,
    steps: any[],
    context: any = {}
  ): Promise<UnifiedAIResponse<any>> {
    const startTime = Date.now();
    
    try {
      const response = await this.post('workflow/', {
        workflow_type: workflowType,
        steps,
        context,
        unified: true,
      });

      return {
        data: response.data,
        metadata: {
          service: 'unified_ai',
          model: 'workflow_engine',
          processingTime: Date.now() - startTime,
          cacheHit: false,
          fallbackUsed: false,
        },
        status: 'success',
      };
    } catch (error) {
      console.error('Workflow execution failed:', error);
      throw new AIServiceError('Workflow execution failed', 'WORKFLOW_ERROR');
    }
  }
  /**
   * Get ML analytics and insights
   */
  async getMLAnalytics(params: any = {}): Promise<UnifiedAIResponse<any>> {
    const startTime = Date.now();
    
    try {
      const response = await this.get('analytics/', { ...params, unified: true });

      return {
        data: response.data,
        metadata: {
          service: 'unified_ai',
          model: 'analytics_engine',
          processingTime: Date.now() - startTime,
          cacheHit: false,
          fallbackUsed: false,
        },
        status: 'success',
      };
    } catch (error) {
      console.error('ML analytics request failed:', error);
      throw new AIServiceError('Analytics request failed', 'ANALYTICS_ERROR');
    }
  }

  /**
   * Get comprehensive system health
   */
  async getSystemHealth(): Promise<AIServiceHealth> {
    const now = new Date();

    // Check if we need to refresh health data
    if (now.getTime() - this.lastHealthCheck.getTime() > 30000) { // 30 seconds
      await this.refreshHealthData();
    }

    const services = Object.fromEntries(this.serviceHealth);
    const healthyServices = Object.values(services).filter((s: any) => s.healthy);

    return {
      status: this.determineOverallStatus(healthyServices.length, Object.keys(services).length),
      services,
      overall: {
        healthyCount: healthyServices.length,
        totalCount: Object.keys(services).length,
        averageLatency: this.calculateAverageLatency(services),
      },
    };
  }

  /**
   * Optimize AI service performance
   */
  async optimizePerformance(): Promise<{
    success: boolean;
    improvements: string[];
    metrics: any;
  }> {
    try {
      const improvements: string[] = [];

      // 1. Clear old cache entries
      this.clearCache();
      improvements.push('Cache cleared and optimized');

      // 2. Reset error counters
      this.serviceHealth.forEach((value, key) => {
        if (value.error) {
          this.serviceHealth.set(key, {
            ...value,
            error: undefined,
            healthy: true,
            lastCheck: new Date().toISOString(),
          });
        }
      });
      improvements.push('Error states reset');

      // 3. Refresh health data
      await this.refreshHealthData();
      improvements.push('Health data refreshed');

      // 4. Test AI responsiveness
      const startTime = Date.now();
      try {
        await this.testConnection();
        const responseTime = Date.now() - startTime;
        improvements.push(`AI responsiveness tested (${responseTime}ms)`);
      } catch (error) {
        improvements.push('AI responsiveness test failed - may need attention');
      }

      // 5. Get updated metrics
      const health = await this.getSystemHealth();

      return {
        success: true,
        improvements,
        metrics: {
          status: health.status,
          healthyServices: health.overall.healthyCount,
          totalServices: health.overall.totalCount,
          averageLatency: health.overall.averageLatency,
          timestamp: new Date().toISOString()
        }
      };

    } catch (error) {
      console.error('Performance optimization failed:', error);
      return {
        success: false,
        improvements: ['Performance optimization failed'],
        metrics: {
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Test AI service connection
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.get('health/', undefined, { timeout: 5000 });
      return response.status === 'healthy' || response.status === 'degraded';
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }

  /**
   * Clear service cache
   */
  clearCache(): void {
    try {
      // Clear service health cache
      this.serviceHealth.clear();

      // Reset last health check time
      this.lastHealthCheck = new Date(0);

      // Clear any browser cache if available
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => {
            if (name.includes('ai-service') || name.includes('unified-ai')) {
              caches.delete(name);
            }
          });
        }).catch(error => {
          console.warn('Failed to clear browser cache:', error);
        });
      }

      console.log('✅ AI Service cache cleared successfully');
    } catch (error) {
      console.error('❌ Failed to clear cache:', error);
    }
  }
  // ==========================================
  // PRIVATE HELPER METHODS
  // ==========================================
    private getEndpointForContext(type?: string): string {
    switch (type) {
      case 'tutoring':
        return 'chat/';
      case 'study':
        return 'study/';
      case 'support':
        return 'assistant/';
      default:
        return 'chat/';
    }
  }

  private getFallbackSuggestions(userType: string): SuggestionItem[] {
    const baseSuggestions = [
      { id: 1, text: "How can I improve my study habits?", icon: "📚", category: "study" },
      { id: 2, text: "Explain this concept to me", icon: "💡", category: "learning" },
      { id: 3, text: "Create a study schedule", icon: "📅", category: "planning" },
      { id: 4, text: "Help with assignment", icon: "✍️", category: "homework" },
    ];

    return baseSuggestions.filter(s => 
      userType === 'all' || s.category === userType
    );
  }

  private initializeHealthMonitoring(): void {
    // Initialize health data for key services
    const services = ['ai_assistant', 'chatbot', 'study_assistant', 'unified_ai'];
    services.forEach(service => {
      this.serviceHealth.set(service, {
        healthy: true,
        latency: 0,
        lastCheck: new Date().toISOString(),
        error: null,
      });
    });
  }
  private async refreshHealthData(): Promise<void> {
    try {
      // Check main AI service health
      const aiHealthResponse = await this.get('health/', undefined, { timeout: 5000 });

      if (aiHealthResponse) {
        // Update AI service health
        this.serviceHealth.set('ai_service', {
          healthy: aiHealthResponse.status === 'healthy',
          status: aiHealthResponse.status,
          latency: aiHealthResponse.metrics?.response_time_ms || 0,
          lastCheck: new Date().toISOString(),
          checks: aiHealthResponse.checks || {},
          metrics: aiHealthResponse.metrics || {}
        });
      }

      // Check other services if available
      if (aiHealthResponse.services) {
        Object.entries(aiHealthResponse.services).forEach(([service, health]: [string, any]) => {
          this.serviceHealth.set(service, {
            ...health,
            lastCheck: new Date().toISOString(),
          });
        });
      }

      this.lastHealthCheck = new Date();

      // Log health status for monitoring
      const overallHealth = this.determineOverallStatus(
        Array.from(this.serviceHealth.values()).filter(s => s.healthy).length,
        this.serviceHealth.size
      );

      if (overallHealth !== 'healthy') {
        console.warn(`🏥 AI Service Health: ${overallHealth}`, {
          services: Object.fromEntries(this.serviceHealth),
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      console.error('🚨 AI Health check failed:', error);

      // Mark services as potentially unhealthy
      this.serviceHealth.forEach((value, key) => {
        this.serviceHealth.set(key, {
          ...value,
          healthy: false,
          status: 'unhealthy',
          error: error instanceof Error ? error.message : 'Health check failed',
          lastCheck: new Date().toISOString(),
        });
      });
    }
  }

  private determineOverallStatus(healthy: number, total: number): 'healthy' | 'degraded' | 'unhealthy' {
    const ratio = healthy / total;
    if (ratio >= 0.8) return 'healthy';
    if (ratio >= 0.5) return 'degraded';
    return 'unhealthy';
  }

  private calculateAverageLatency(services: any): number {
    const latencies = Object.values(services)
      .map((s: any) => s.latency || 0)
      .filter(l => l > 0);
    
    return latencies.length > 0 
      ? latencies.reduce((a, b) => a + b, 0) / latencies.length 
      : 0;
  }

  // Missing methods expected by tests
  async getPersonalizedLearningPath(userId: string, options: any = {}): Promise<any> {
    try {
      const response = await this.get('learning-path', { userId, ...options });
      return response;
    } catch (error) {
      console.error('Failed to get personalized learning path:', error);
      throw error;
    }
  }

  async getContentRecommendations(userId: string, options: any = {}): Promise<any> {
    try {
      const response = await this.get('recommendations', { userId, ...options });
      return response;
    } catch (error) {
      console.error('Failed to get content recommendations:', error);
      throw error;
    }
  }

  async analyzeLearningPatterns(userId: string, options: any = {}): Promise<any> {
    try {
      const response = await this.get('patterns', { userId, ...options });
      return response;
    } catch (error) {
      console.error('Failed to analyze learning patterns:', error);
      throw error;
    }
  }

  async predictPerformance(userId: string, options: any = {}): Promise<any> {
    try {
      const response = await this.get('performance', { userId, ...options });
      return response;
    } catch (error) {
      console.error('Failed to predict performance:', error);
      throw error;
    }
  }

  async getUsageAnalytics(options: any = {}): Promise<any> {
    try {
      const response = await this.get('usage', options);
      return response;
    } catch (error) {
      console.error('Failed to get usage analytics:', error);
      throw error;
    }
  }

  async testConnectivity(): Promise<any> {
    try {
      const response = await this.get('health');
      return response;
    } catch (error) {
      console.error('Failed to test connectivity:', error);
      throw error;
    }
  }
}

// Export singleton instance
const unifiedAiService = new UnifiedAIService();
export default unifiedAiService;

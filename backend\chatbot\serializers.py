from django.contrib.authimportget_user_modelfromrest_frameworkimportserializersfrom users.serializersimportUserSerializerfrom.modelsimport(AIResponseChatConversationChatMessageChatSessionUserLearningProfile)User=get_user_model()classUserMinimalSerializer(serializers.ModelSerializer):classMeta:model=Userfields=["id""email""first_name""last_name""role"]classChatMessageSerializer(serializers.ModelSerializer):classMeta:model=ChatMessagefields=["id""conversation""role""content""timestamp""metadata"]read_only_fields=["timestamp"]classAIResponseSerializer(serializers.ModelSerializer):classMeta:model=AIResponsefields=["id""content""suggested_questions""dynamic_content""created_at"]read_only_fields=["created_at"]classChatSessionSerializer(serializers.ModelSerializer):messages=ChatMessageSerializer(many=Trueread_only=True)classMeta:model=ChatSessionfields=["id""user""started_at""last_message_at""is_active""context""messages"]read_only_fields=["started_at""last_message_at"]classChatConversationSerializer(serializers.ModelSerializer):user=UserMinimalSerializer(read_only=True)messages=ChatMessageSerializer(many=Trueread_only=True)last_message=serializers.SerializerMethodField()message_count=serializers.SerializerMethodField()classMeta:model=ChatConversationfields=["id""user""title""user_role""created_at""updated_at""messages""last_message""message_count"]read_only_fields=["created_at""updated_at""user""user_role"]defget_last_message(selfobj):last_message=obj.messages.last()iflast_message:returnChatMessageSerializer(last_message).datareturnNonedefget_message_count(selfobj):returnobj.messages.count()classChatInputSerializer(serializers.Serializer):message=serializers.CharField(required=True)conversation_id=serializers.IntegerField(required=Falseallow_null=True)metadata=serializers.JSONField(required=Falsedefault=dict)classUserLearningProfileSerializer(serializers.ModelSerializer):recommendations=serializers.SerializerMethodField()study_tips=serializers.SerializerMethodField()classMeta:model=UserLearningProfilefields=["id""user""primary_learning_style""secondary_learning_style""comprehension_level""prefers_step_by_step""prefers_examples""prefers_visual_aids""attention_span_minutes""interaction_patterns""last_updated""recommendations""study_tips"]read_only_fields=["last_updated""recommendations""study_tips"]defget_recommendations(selfobj):from.viewsimportUserLearningProfileViewSetviewset=UserLearningProfileViewSet()returnviewset._get_learning_recommendations(obj)defget_study_tips(selfobj):from.viewsimportUserLearningProfileViewSetviewset=UserLearningProfileViewSet()returnviewset._get_study_tips(obj)
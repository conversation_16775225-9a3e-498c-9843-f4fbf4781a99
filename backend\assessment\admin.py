from django.contrib import admin
from .models import (
    Assessment,
    AssessmentQuestion,
    AssessmentResponse,
    StudentLevel
)


@admin.register(Assessment)
class AssessmentAdmin(admin.ModelAdmin):
    list_display = ("title", "student", "assessment_type", "status", "score", "created_at")
    list_filter = ("assessment_type", "status", "created_at")
    search_fields = ("student__username", "title", "description")
    raw_id_fields = ("student",)
    date_hierarchy = "created_at"


@admin.register(AssessmentQuestion)
class AssessmentQuestionAdmin(admin.ModelAdmin):
    list_display = ("question_text", "question_type", "difficulty_level", "category", "is_public", "created_at")
    list_filter = ("question_type", "difficulty_level", "category", "is_public", "created_at")
    search_fields = ("question_text",)


@admin.register(AssessmentResponse)
class AssessmentResponseAdmin(admin.ModelAdmin):
    list_display = ("student", "question", "is_correct", "points_earned", "created_at")
    list_filter = ("is_correct", "created_at")
    search_fields = ("student__username", "question__question_text")


@admin.register(StudentLevel)
class StudentLevelAdmin(admin.ModelAdmin):
    list_display = ("student", "current_level", "current_level_display", "last_assessment_date")
    list_filter = ("current_level", "last_assessment_date")
    search_fields = ("student__username", "student__email")

import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import {
  <PERSON>po<PERSON>,
  Button,
  CircularProgress,
  Divider,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Box,
  Card,
  CardContent,
  Grid,
  Paper,
} from '@mui/material';
import { formatDate } from '../../utils/dateUtils';
import { fetchUserById } from './adminSlice';
import type { AppDispatch, RootState } from '../../app/store';
import type { AdminState } from './adminSlice';
import {
  FiArrowLeft as ArrowBackIcon,
  FiCalendar as EventAvailableIcon,
  FiUser as PersonIcon,
  FiClipboard as AssessmentIcon,
  FiMail as EmailIcon,
  FiPhone as PhoneIcon,
  FiBriefcase as BusinessIcon,
} from 'react-icons/fi';
import { FaGraduationCap as SchoolIcon } from 'react-icons/fa';

interface CourseInfo {
  id: number;
  code: string;
  title: string;
  students_count?: number;
  credits?: number;
  enrollment_date?: string;
  grade?: string;
  attendance_rate?: number;
  progress?: number;
}

const getAvatarText = (name: string): string => {
  return name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase();
};

// Using formatDate from utils/dateUtils.ts

const getGradeColor = (grade?: string) => {
  if (!grade) return 'default';
  if (grade.startsWith('A')) return 'success';
  if (grade.startsWith('B')) return 'primary';
  if (grade.startsWith('C')) return 'warning';
  return 'error';
};

const UserView: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { t } = useTranslation();
  const {
    selectedUser: user,
    loading,
    error,
  } = useSelector((state: RootState) => state.admin as AdminState);

  // Track if we've already fetched the data
  const [hasFetched, setHasFetched] = useState(false);

  useEffect(() => {
    if (id && !hasFetched) {
      // Fetch user data with proper error handling
      dispatch(fetchUserById(parseInt(id)))
        .unwrap()
        .then(() => {
          setHasFetched(true);
        })
        .catch(error => {
          console.error('Error fetching user:', error);
          setHasFetched(true); // Mark as fetched even on error to prevent loops
        });
    }
  }, [dispatch, id, hasFetched]);

  if (loading) {
    return (
      <Box
        display='flex'
        justifyContent='center'
        alignItems='center'
        minHeight='400px'
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <div className='p-3'>
        <Alert severity='error'>{error}</Alert>
      </div>
    );
  }

  if (!user) {
    return (
      <div className='p-3'>
        <Alert severity='error'>{t('admin.userNotFound')}</Alert>
      </div>
    );
  }

return (
    <Box 
      sx={{ 
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        p: 3,
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)',
          pointerEvents: 'none',
        }
      }}
    >
      <Box 
        sx={{ 
          position: 'relative',
          zIndex: 1,
          maxWidth: '1400px',
          mx: 'auto'
        }}
      >
        {/* Enhanced Header */}
        <Box 
          sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            mb: 4,
            p: 3,
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(20px)',
            borderRadius: '20px',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Button
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/admin/users')}
              sx={{
                background: 'rgba(255, 255, 255, 0.2)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                borderRadius: '15px',
                color: 'white',
                fontWeight: 600,
                px: 3,
                py: 1.5,
                textTransform: 'none',
                '&:hover': {
                  background: 'rgba(255, 255, 255, 0.3)',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 25px rgba(0, 0, 0, 0.2)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              {t('admin.backToUsersList')}
            </Button>
            <Typography 
              variant='h4' 
              sx={{ 
                color: 'white',
                fontWeight: 700,
                textShadow: '0 2px 10px rgba(0, 0, 0, 0.3)'
              }}
            >
              User Profile
            </Typography>
          </Box>
          <Button
            variant='contained'
            onClick={() => navigate(`/admin/users/${id}/edit`)}
            sx={{
              background: 'linear-gradient(45deg, #FF6B6B, #FF8E8E)',
              borderRadius: '15px',
              fontWeight: 600,
              px: 3,
              py: 1.5,
              textTransform: 'none',
              boxShadow: '0 4px 15px rgba(255, 107, 107, 0.4)',
              '&:hover': {
                background: 'linear-gradient(45deg, #FF5252, #FF8A80)',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(255, 107, 107, 0.6)'
              },
              transition: 'all 0.3s ease'
            }}
          >
            {t('common.edit')}
          </Button>
        </Box>

        <Grid container spacing={4}>
          {/* Left column - Enhanced User Profile */}
          <Grid item xs={12} md={4}>
            <Card
              sx={{
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(20px)',
                borderRadius: '25px',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                overflow: 'hidden',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '100px',
                  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%)',
                  borderRadius: '25px 25px 0 0'
                }
              }}
            >
              <CardContent sx={{ p: 4, position: 'relative', zIndex: 1 }}>
                <Box
                  display='flex'
                  flexDirection='column'
                  alignItems='center'
                  mb={4}
                >
                  {/* Enhanced Avatar */}
                  <Box
                    sx={{
                      width: 120,
                      height: 120,
                      borderRadius: '50%',
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mb: 3,
                      boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',
                      border: '3px solid rgba(255, 255, 255, 0.3)',
                      position: 'relative',
                      '&::after': {
                        content: '""',
                        position: 'absolute',
                        top: -3,
                        left: -3,
                        right: -3,
                        bottom: -3,
                        borderRadius: '50%',
                        background: 'linear-gradient(45deg, rgba(255, 255, 255, 0.3), transparent)',
                        zIndex: -1
                      }
                    }}
                  >
                    <PersonIcon
                      sx={{ 
                        fontSize: 60, 
                        color: 'white',
                        filter: 'drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3))'
                      }}
                    />
                  </Box>
                  
                  {/* Enhanced User Info */}
                  <Typography 
                    variant='h5' 
                    sx={{ 
                      color: 'white',
                      fontWeight: 700,
                      textAlign: 'center',
                      mb: 1,
                      textShadow: '0 2px 10px rgba(0, 0, 0, 0.3)'
                    }}
                  >
                    {user.first_name || ''} {user.last_name || ''}
                  </Typography>
                  <Typography 
                    sx={{ 
                      color: 'rgba(255, 255, 255, 0.8)',
                      fontWeight: 500,
                      mb: 2,
                      fontSize: '1.1rem'
                    }}
                  >
                    @{user.username || 'user'}
                  </Typography>
                  
                  {/* Enhanced Role Chip */}
                  <Chip
                    label={user.role || 'STUDENT'}
                    sx={{
                      background: user.role === 'ADMIN'
                        ? 'linear-gradient(45deg, #FF6B6B, #FF8E8E)'
                        : user.role === 'PROFESSOR'
                          ? 'linear-gradient(45deg, #4ECDC4, #44A08D)'
                          : 'linear-gradient(45deg, #A8E6CF, #88D8A3)',
                      color: 'white',
                      fontWeight: 600,
                      fontSize: '0.9rem',
                      px: 3,
                      py: 1,
                      borderRadius: '20px',
                      boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)',
                      border: '1px solid rgba(255, 255, 255, 0.3)'
                    }}
                  />
                </Box>

                {/* Enhanced Info List */}
                <Box sx={{ mt: 3 }}>
                  {[
                    {
                      icon: <EmailIcon />,
                      label: t('profile.email'),
                      value: user.email || t('profile.noEmailProvided'),
                      color: '#4ECDC4'
                    },
                    {
                      icon: <EventAvailableIcon />,
                      label: t('profile.joined'),
                      value: user.date_joined ? formatDate(user.date_joined) : t('common.unknown'),
                      color: '#667eea'
                    },
                    ...(user.phone_number ? [{
                      icon: <PhoneIcon />,
                      label: t('profile.phone'),
                      value: user.phone_number,
                      color: '#FF8E8E'
                    }] : []),
                    ...(user.department ? [{
                      icon: <BusinessIcon />,
                      label: t('profile.department'),
                      value: user.department,
                      color: '#A8E6CF'
                    }] : []),
                    ...(user.role === 'STUDENT' && user.student_id ? [{
                      icon: <AssessmentIcon />,
                      label: 'Student ID',
                      value: user.student_id,
                      color: '#FFD93D'
                    }] : [])
                  ].map((item, index) => (
                    <Box
                      key={index}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        mb: 3,
                        p: 2,
                        background: 'rgba(255, 255, 255, 0.1)',
                        borderRadius: '15px',
                        border: '1px solid rgba(255, 255, 255, 0.2)',
                        backdropFilter: 'blur(10px)',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          background: 'rgba(255, 255, 255, 0.2)',
                          transform: 'translateX(10px)'
                        }
                      }}
                    >
                      <Box
                        sx={{
                          width: 40,
                          height: 40,
                          borderRadius: '10px',
                          background: `linear-gradient(135deg, ${item.color}, ${item.color}80)`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mr: 2,
                          boxShadow: `0 4px 15px ${item.color}40`
                        }}
                      >
                        {React.cloneElement(item.icon, { 
                          sx: { color: 'white', fontSize: 20 } 
                        })}
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography 
                          variant='body2' 
                          sx={{ 
                            color: 'rgba(255, 255, 255, 0.7)',
                            fontWeight: 500,
                            mb: 0.5
                          }}
                        >
                          {item.label}
                        </Typography>
                        <Typography 
                          variant='body1' 
                          sx={{ 
                            color: 'white',
                            fontWeight: 600
                          }}
                        >
                          {item.value}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                  
                  {/* Status Badge */}
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mt: 3,
                      p: 2,
                      background: user.is_active 
                        ? 'linear-gradient(135deg, #A8E6CF, #88D8A3)'
                        : 'linear-gradient(135deg, #FFB3B3, #FF8E8E)',
                      borderRadius: '15px',
                      boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)'
                    }}
                  >
                    <Typography 
                      variant='h6' 
                      sx={{ 
                        color: 'white',
                        fontWeight: 700,
                        textAlign: 'center'
                      }}
                    >
                      {user.is_active ? '✅ ' + t('common.active') : '❌ ' + t('common.inactive')}
                    </Typography>
                  </Box>
                </Box>
            </CardContent>
          </Card>

              {/* Enhanced Quick Actions */}
              <Card
                sx={{
                  mt: 3,
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(20px)',
                  borderRadius: '20px',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  <Typography 
                    variant='h6' 
                    sx={{ 
                      color: 'white',
                      fontWeight: 700,
                      mb: 3,
                      textAlign: 'center',
                      textShadow: '0 2px 10px rgba(0, 0, 0, 0.3)'
                    }}
                  >
                    Quick Actions
                  </Typography>
                  <Box display='flex' flexDirection='column' gap={2}>
                    <Button
                      startIcon={<AssessmentIcon />}
                      onClick={() =>
                        navigate(`/admin/assessment/student-levels?studentId=${id}`)
                      }
                      fullWidth
                      sx={{
                        background: 'linear-gradient(45deg, #4ECDC4, #44A08D)',
                        borderRadius: '15px',
                        color: 'white',
                        fontWeight: 600,
                        py: 1.5,
                        textTransform: 'none',
                        boxShadow: '0 4px 15px rgba(78, 205, 196, 0.4)',
                        '&:hover': {
                          background: 'linear-gradient(45deg, #45B7B8, #3D8B85)',
                          transform: 'translateY(-2px)',
                          boxShadow: '0 8px 25px rgba(78, 205, 196, 0.6)'
                        },
                        transition: 'all 0.3s ease'
                      }}
                    >
                      Manage Level
                    </Button>
                    <Button 
                      fullWidth
                      sx={{
                        background: 'linear-gradient(45deg, #FF6B6B, #FF8E8E)',
                        borderRadius: '15px',
                        color: 'white',
                        fontWeight: 600,
                        py: 1.5,
                        textTransform: 'none',
                        boxShadow: '0 4px 15px rgba(255, 107, 107, 0.4)',
                        '&:hover': {
                          background: 'linear-gradient(45deg, #FF5252, #FF8A80)',
                          transform: 'translateY(-2px)',
                          boxShadow: '0 8px 25px rgba(255, 107, 107, 0.6)'
                        },
                        transition: 'all 0.3s ease'
                      }}
                    >
                      Deactivate Account
                    </Button>
                  </Box>
                </CardContent>
              </Card>
        </Grid>

          {/* Right column - Enhanced Detailed Information */}
          <Grid item xs={12} md={8}>
            {user.role === 'STUDENT' && (
              <Card 
                sx={{ 
                  mb: 3,
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(20px)',
                  borderRadius: '25px',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                  overflow: 'hidden'
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Box 
                    display='flex' 
                    alignItems='center' 
                    gap={2} 
                    mb={4}
                    sx={{
                      p: 3,
                      background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1))',
                      borderRadius: '20px',
                      border: '1px solid rgba(255, 255, 255, 0.3)'
                    }}
                  >
                    <Box
                      sx={{
                        width: 50,
                        height: 50,
                        borderRadius: '15px',
                        background: 'linear-gradient(135deg, #A8E6CF, #88D8A3)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: '0 4px 15px rgba(168, 230, 207, 0.4)'
                      }}
                    >
                      <SchoolIcon sx={{ color: 'white', fontSize: 28 }} />
                    </Box>
                    <Typography 
                      variant='h5' 
                      sx={{ 
                        color: 'white',
                        fontWeight: 700,
                        textShadow: '0 2px 10px rgba(0, 0, 0, 0.3)'
                      }}
                    >
                      Academic Information
                    </Typography>
                  </Box>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Paper 
                        sx={{ 
                          p: 3,
                          background: 'linear-gradient(135deg, #667eea, #764ba2)',
                          borderRadius: '20px',
                          border: '1px solid rgba(255, 255, 255, 0.3)',
                          boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)',
                          backdropFilter: 'blur(10px)',
                          position: 'relative',
                          overflow: 'hidden',
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            width: '100px',
                            height: '100px',
                            background: 'radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%)',
                            borderRadius: '50%',
                            transform: 'translate(50%, -50%)'
                          }
                        }}
                      >
                        <Typography 
                          variant='subtitle2' 
                          sx={{ 
                            color: 'rgba(255, 255, 255, 0.8)',
                            fontWeight: 600,
                            mb: 2
                          }}
                        >
                          Current Level
                        </Typography>
                        <Typography 
                          variant='h4' 
                          sx={{ 
                            color: 'white',
                            fontWeight: 700,
                            mb: 1
                          }}
                        >
                          {user.level_info?.current_level_display || 'Not Set'}
                        </Typography>
                        <Typography 
                          variant='body2' 
                          sx={{ 
                            color: 'rgba(255, 255, 255, 0.9)',
                            fontWeight: 500
                          }}
                        >
                          Level: {user.level_info?.current_level || 'Not assigned'}
                        </Typography>
                      </Paper>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Paper 
                        sx={{ 
                          p: 3,
                          background: 'linear-gradient(135deg, #4ECDC4, #44A08D)',
                          borderRadius: '20px',
                          border: '1px solid rgba(255, 255, 255, 0.3)',
                          boxShadow: '0 8px 32px rgba(78, 205, 196, 0.3)',
                          backdropFilter: 'blur(10px)',
                          position: 'relative',
                          overflow: 'hidden',
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            width: '100px',
                            height: '100px',
                            background: 'radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%)',
                            borderRadius: '50%',
                            transform: 'translate(50%, -50%)'
                          }
                        }}
                      >
                        <Typography 
                          variant='subtitle2' 
                          sx={{ 
                            color: 'rgba(255, 255, 255, 0.8)',
                            fontWeight: 600,
                            mb: 2
                          }}
                        >
                          GPA / Average Score
                        </Typography>
                        <Typography 
                          variant='h4' 
                          sx={{ 
                            color: 'white',
                            fontWeight: 700,
                            mb: 1
                          }}
                        >
                          {user.gpa || 'N/A'}
                        </Typography>
                        <Typography 
                          variant='body2' 
                          sx={{ 
                            color: 'rgba(255, 255, 255, 0.9)',
                            fontWeight: 500
                          }}
                        >
                          Enrolled in {user.enrolled_courses_count || 0} courses
                        </Typography>
                      </Paper>
                    </Grid>
                  <Grid item xs={12}>
                    <Paper 
                      elevation={0} 
                      sx={{ 
                        p: 3,
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        borderRadius: '20px',
                        border: '1px solid rgba(255, 255, 255, 0.3)',
                        boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)',
                        backdropFilter: 'blur(10px)',
                        position: 'relative',
                        overflow: 'hidden',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          right: 0,
                          width: '100px',
                          height: '100px',
                          background: 'radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%)',
                          borderRadius: '50%',
                          transform: 'translate(50%, -50%)'
                        }
                      }}
                    >
                      <Typography
                        variant='subtitle2'
                        sx={{
                          color: 'rgba(255, 255, 255, 0.8)',
                          fontWeight: 600,
                          mb: 2
                        }}
                      >
                        Courses & Progress
                      </Typography>
                      {user.courses && user.courses.length > 0 ? (
                        <List sx={{ padding: 0 }}>
                          {user.courses.slice(0, 3).map((course, index) => (
                            <ListItem 
                              key={index}
                              sx={{
                                background: 'rgba(255, 255, 255, 0.15)',
                                borderRadius: '12px',
                                mb: 1,
                                border: '1px solid rgba(255, 255, 255, 0.2)',
                                backdropFilter: 'blur(5px)',
                                '&:hover': {
                                  background: 'rgba(255, 255, 255, 0.2)',
                                  transform: 'translateY(-2px)',
                                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2)'
                                },
                                transition: 'all 0.3s ease'
                              }}
                            >
                              <ListItemText
                                primary={
                                  <Typography sx={{ color: 'white', fontWeight: 600 }}>
                                    {course.title}
                                  </Typography>
                                }
                                secondary={
                                  <Typography sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                                    Progress: {course.progress || 0}%
                                  </Typography>
                                }
                              />
                            </ListItem>
                          ))}
                          {user.courses.length > 3 && (
                            <ListItem
                              sx={{
                                background: 'rgba(255, 255, 255, 0.1)',
                                borderRadius: '12px',
                                border: '1px solid rgba(255, 255, 255, 0.2)',
                                backdropFilter: 'blur(5px)'
                              }}
                            >
                              <ListItemText
                                secondary={
                                  <Typography sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                                    + {user.courses.length - 3} more courses
                                  </Typography>
                                }
                              />
                            </ListItem>
                          )}
                        </List>
                      ) : (
                        <Typography 
                          variant='body2'
                          sx={{
                            color: 'rgba(255, 255, 255, 0.9)',
                            fontStyle: 'italic'
                          }}
                        >
                          No courses enrolled
                        </Typography>
                      )}
                    </Paper>
                  </Grid>
                  <Grid item xs={12}>
                    <Paper 
                      elevation={0} 
                      sx={{ 
                        p: 3,
                        background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                        borderRadius: '20px',
                        border: '1px solid rgba(255, 255, 255, 0.3)',
                        boxShadow: '0 8px 32px rgba(240, 147, 251, 0.3)',
                        backdropFilter: 'blur(10px)',
                        position: 'relative',
                        overflow: 'hidden',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          right: 0,
                          width: '100px',
                          height: '100px',
                          background: 'radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%)',
                          borderRadius: '50%',
                          transform: 'translate(50%, -50%)'
                        }
                      }}
                    >
                      <Typography
                        variant='subtitle2'
                        sx={{
                          color: 'rgba(255, 255, 255, 0.8)',
                          fontWeight: 600,
                          mb: 2
                        }}
                      >
                        Actions
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                        <Button
                          variant='contained'
                          size='medium'
                          startIcon={<SchoolIcon />}
                          onClick={() =>
                            navigate(
                              `/admin/assessment/student-levels?studentId=${id}`
                            )
                          }
                          sx={{
                            background: 'rgba(255, 255, 255, 0.2)',
                            color: 'white',
                            backdropFilter: 'blur(10px)',
                            border: '1px solid rgba(255, 255, 255, 0.3)',
                            borderRadius: '12px',
                            fontWeight: 600,
                            '&:hover': {
                              background: 'rgba(255, 255, 255, 0.3)',
                              transform: 'translateY(-2px)',
                              boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2)'
                            },
                            transition: 'all 0.3s ease'
                          }}
                        >
                          Manage Level
                        </Button>
                        <Button
                          variant='contained'
                          size='medium'
                          startIcon={<AssessmentIcon />}
                          onClick={() =>
                            navigate(
                              `/admin/assessment/ai-analysis?studentId=${id}`
                            )
                          }
                          sx={{
                            background: 'rgba(255, 255, 255, 0.2)',
                            color: 'white',
                            backdropFilter: 'blur(10px)',
                            border: '1px solid rgba(255, 255, 255, 0.3)',
                            borderRadius: '12px',
                            fontWeight: 600,
                            '&:hover': {
                              background: 'rgba(255, 255, 255, 0.3)',
                              transform: 'translateY(-2px)',
                              boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2)'
                            },
                            transition: 'all 0.3s ease'
                          }}
                        >
                          AI Assessment Analysis
                        </Button>
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          )}

          {user.role === 'PROFESSOR' && (
            <Card 
              sx={{ 
                mb: 2,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '20px',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)',
                backdropFilter: 'blur(10px)',
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  width: '150px',
                  height: '150px',
                  background: 'radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%)',
                  borderRadius: '50%',
                  transform: 'translate(50%, -50%)'
                }
              }}
            >
              <CardContent sx={{ position: 'relative', zIndex: 1 }}>
                <Box display='flex' alignItems='center' gap={2} mb={2}>
                  <SchoolIcon sx={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: 28 }} />
                  <Typography 
                    variant='h6'
                    sx={{
                      color: 'white',
                      fontWeight: 700,
                      textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)'
                    }}
                  >
                    Teaching Information
                  </Typography>
                </Box>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Paper 
                      elevation={0} 
                      sx={{ 
                        p: 3,
                        background: 'rgba(255, 255, 255, 0.15)',
                        borderRadius: '16px',
                        border: '1px solid rgba(255, 255, 255, 0.2)',
                        backdropFilter: 'blur(10px)',
                        '&:hover': {
                          background: 'rgba(255, 255, 255, 0.2)',
                          transform: 'translateY(-2px)',
                          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2)'
                        },
                        transition: 'all 0.3s ease'
                      }}
                    >
                      <Typography 
                        variant='subtitle2' 
                        sx={{
                          color: 'rgba(255, 255, 255, 0.8)',
                          fontWeight: 600,
                          mb: 1
                        }}
                      >
                        Courses Teaching
                      </Typography>
                      <Typography 
                        variant='h6'
                        sx={{
                          color: 'white',
                          fontWeight: 700,
                          fontSize: '1.5rem'
                        }}
                      >
                        {user.coursesTeaching || 0} Courses
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Paper 
                      elevation={0} 
                      sx={{ 
                        p: 3,
                        background: 'rgba(255, 255, 255, 0.15)',
                        borderRadius: '16px',
                        border: '1px solid rgba(255, 255, 255, 0.2)',
                        backdropFilter: 'blur(10px)',
                        '&:hover': {
                          background: 'rgba(255, 255, 255, 0.2)',
                          transform: 'translateY(-2px)',
                          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2)'
                        },
                        transition: 'all 0.3s ease'
                      }}
                    >
                      <Typography 
                        variant='subtitle2' 
                        sx={{
                          color: 'rgba(255, 255, 255, 0.8)',
                          fontWeight: 600,
                          mb: 1
                        }}
                      >
                        Total Students
                      </Typography>
                      <Typography 
                        variant='h6'
                        sx={{
                          color: 'white',
                          fontWeight: 700,
                          fontSize: '1.5rem'
                        }}
                      >
                        {user.totalStudents || 0} Students
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          )}
        </Grid>
      </Grid>
    </Box>
    </Box>
  );
};

export default UserView;

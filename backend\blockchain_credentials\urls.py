"""
URL configuration for blockchain credentials app
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

# Create a router and register our viewsets
router = DefaultRouter()
router.register(r'credentials', views.BlockchainCredentialViewSet, basename='blockchain-credential')
router.register(r'templates', views.CredentialTemplateViewSet, basename='credential-template')
router.register(r'nft-achievements', views.NFTAchievementViewSet, basename='nft-achievement')
router.register(r'networks', views.BlockchainNetworkViewSet, basename='blockchain-network')
router.register(r'wallets', views.WalletAddressViewSet, basename='wallet-address')
router.register(r'verify', views.PublicCredentialVerificationView, basename='public-verification')

app_name = 'blockchain_credentials'

urlpatterns = [
    path('', include(router.urls)),
]

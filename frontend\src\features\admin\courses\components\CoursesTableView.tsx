import React, { use<PERSON><PERSON>back, useMemo, memo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Tooltip,
  Avatar,
  Box,
  Typography,
  useTheme,
  alpha,
} from '@mui/material';
import {
  FiMoreVertical as MoreVertIcon,
  FiEdit2 as EditIcon,
  FiEye as ViewIcon,
  FiTrash2 as DeleteIcon,
  FiUsers as StudentsIcon,
  FiCalendar as CalendarIcon,
  <PERSON><PERSON><PERSON> as ProfessorIcon,
  FiBook as BookIcon,
} from 'react-icons/fi';

interface Course {
  id: string;
  title: string;
  courseCode: string;
  professor?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    avatar?: string;
  };
  department?: {
    id: string;
    name: string;
  };
  enrollmentCount: number;
  maxEnrollment: number;
  startDate: string;
  endDate: string;
  status: 'active' | 'inactive' | 'draft' | 'archived';
  courseType: string;
  isVisible: boolean;
  createdAt: string;
  lastModified: string;
}

interface SortConfig {
  key: keyof Course | string;
  direction: 'asc' | 'desc';
}

interface CoursesTableViewProps {
  courses: Course[];
  sortConfig: SortConfig;
  onSort: (key: keyof Course | string) => void;
  onView: (course: Course) => void;
  onEdit: (course: Course) => void;
  onDelete: (course: Course) => void;
  onManageStudents: (course: Course) => void;
  getTranslation: (key: string, defaultValue: string) => string;
  loading?: boolean;
}

const CoursesTableView: React.FC<CoursesTableViewProps> = ({
  courses,
  sortConfig,
  onSort,
  onView,
  onEdit,
  onDelete,
  onManageStudents,
  getTranslation,
  loading = false,
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [selectedCourse, setSelectedCourse] = React.useState<Course | null>(null);

  const handleMenuOpen = useCallback((event: React.MouseEvent<HTMLElement>, course: Course) => {
    setAnchorEl(event.currentTarget);
    setSelectedCourse(course);
  }, []);

  const handleMenuClose = useCallback(() => {
    setAnchorEl(null);
    setSelectedCourse(null);
  }, []);

  const handleMenuAction = useCallback((action: () => void) => {
    action();
    handleMenuClose();
  }, [handleMenuClose]);

  const getStatusColor = useCallback((status: Course['status']) => {
    switch (status) {
      case 'active':
        return theme.palette.success.main;
      case 'inactive':
        return theme.palette.warning.main;
      case 'draft':
        return theme.palette.info.main;
      case 'archived':
        return theme.palette.grey[500];
      default:
        return theme.palette.grey[500];
    }
  }, [theme.palette]);

  const formatDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  }, []);

  const getEnrollmentColor = useCallback((current: number, max: number) => {
    const percentage = (current / max) * 100;
    if (percentage >= 90) return theme.palette.error.main;
    if (percentage >= 75) return theme.palette.warning.main;
    return theme.palette.success.main;
  }, [theme.palette]);

  const renderSortableHeader = useCallback((key: keyof Course | string, label: string) => (
    <TableSortLabel
      active={sortConfig.key === key}
      direction={sortConfig.key === key ? sortConfig.direction : 'asc'}
      onClick={() => onSort(key)}
    >
      {label}
    </TableSortLabel>
  ), [sortConfig.key, sortConfig.direction, onSort]);

  if (loading) {
    return (
      <Paper elevation={2} sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1">
          {getTranslation('courses.loading', 'Loading courses...')}
        </Typography>
      </Paper>
    );
  }

  if (courses.length === 0) {
    return (
      <Paper elevation={2} sx={{ p: 4, textAlign: 'center' }}>
        <BookIcon size={48} style={{ opacity: 0.5, marginBottom: 16 }} />
        <Typography variant="h6" gutterBottom>
          {getTranslation('courses.noCourses', 'No courses found')}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {getTranslation('courses.noCoursesDesc', 'Try adjusting your filters or create a new course.')}
        </Typography>
      </Paper>
    );
  }

  return (
    <>
      <TableContainer component={Paper} elevation={2}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.04) }}>
              <TableCell>
                {renderSortableHeader('title', getTranslation('courses.courseName', 'Course Name'))}
              </TableCell>
              <TableCell>
                {renderSortableHeader('courseCode', getTranslation('courses.courseCode', 'Code'))}
              </TableCell>
              <TableCell>
                {renderSortableHeader('professor', getTranslation('courses.professor', 'Professor'))}
              </TableCell>
              <TableCell>
                {renderSortableHeader('department', getTranslation('courses.department', 'Department'))}
              </TableCell>
              <TableCell align="center">
                {renderSortableHeader('enrollmentCount', getTranslation('courses.enrollment', 'Enrollment'))}
              </TableCell>
              <TableCell>
                {renderSortableHeader('startDate', getTranslation('courses.duration', 'Duration'))}
              </TableCell>
              <TableCell align="center">
                {renderSortableHeader('status', getTranslation('courses.status', 'Status'))}
              </TableCell>
              <TableCell align="center">
                {getTranslation('courses.actions', 'Actions')}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {courses.map((course) => (
              <TableRow 
                key={course.id} 
                hover
                sx={{ 
                  opacity: course.status === 'archived' ? 0.7 : 1,
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.02),
                  }
                }}
              >
                {/* Course Name */}
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                      {course.title}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {course.courseType}
                    </Typography>
                    {!course.isVisible && (
                      <Chip 
                        label={getTranslation('courses.hidden', 'Hidden')} 
                        size="small" 
                        variant="outlined"
                        sx={{ ml: 1, fontSize: '0.7rem' }}
                      />
                    )}
                  </Box>
                </TableCell>

                {/* Course Code */}
                <TableCell>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace', fontWeight: 500 }}>
                    {course.courseCode}
                  </Typography>
                </TableCell>

                {/* Professor */}
                <TableCell>
                  {course.professor ? (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Avatar 
                        src={course.professor.avatar} 
                        sx={{ width: 32, height: 32 }}
                      >
                        {course.professor.firstName[0]}{course.professor.lastName[0]}
                      </Avatar>
                      <Box>
                        <Typography variant="body2">
                          {course.professor.firstName} {course.professor.lastName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {course.professor.email}
                        </Typography>
                      </Box>
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      {getTranslation('courses.noAssignedProfessor', 'No professor assigned')}
                    </Typography>
                  )}
                </TableCell>

                {/* Department */}
                <TableCell>
                  <Typography variant="body2">
                    {course.department?.name || getTranslation('courses.noDepartment', 'No department')}
                  </Typography>
                </TableCell>

                {/* Enrollment */}
                <TableCell align="center">
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5 }}>
                    <StudentsIcon size={16} style={{ opacity: 0.7 }} />
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        color: getEnrollmentColor(course.enrollmentCount, course.maxEnrollment),
                        fontWeight: 500 
                      }}
                    >
                      {course.enrollmentCount}/{course.maxEnrollment}
                    </Typography>
                  </Box>
                </TableCell>

                {/* Duration */}
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <CalendarIcon size={16} style={{ opacity: 0.7 }} />
                    <Box>
                      <Typography variant="body2">
                        {formatDate(course.startDate)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        to {formatDate(course.endDate)}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>

                {/* Status */}
                <TableCell align="center">
                  <Chip
                    label={getTranslation(`courses.${course.status}`, course.status)}
                    size="small"
                    sx={{
                      backgroundColor: alpha(getStatusColor(course.status), 0.1),
                      color: getStatusColor(course.status),
                      fontWeight: 500,
                      borderColor: getStatusColor(course.status),
                    }}
                    variant="outlined"
                  />
                </TableCell>

                {/* Actions */}
                <TableCell align="center">
                  <Tooltip title={getTranslation('courses.moreActions', 'More actions')}>
                    <IconButton
                      size="small"
                      onClick={(e) => handleMenuOpen(e, course)}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <MenuItem onClick={() => handleMenuAction(() => onView(selectedCourse!))}>
          <ViewIcon style={{ marginRight: 8 }} />
          {getTranslation('courses.view', 'View Details')}
        </MenuItem>
        <MenuItem onClick={() => handleMenuAction(() => onEdit(selectedCourse!))}>
          <EditIcon style={{ marginRight: 8 }} />
          {getTranslation('courses.edit', 'Edit Course')}
        </MenuItem>
        <MenuItem onClick={() => handleMenuAction(() => onManageStudents(selectedCourse!))}>
          <StudentsIcon style={{ marginRight: 8 }} />
          {getTranslation('courses.manageStudents', 'Manage Students')}
        </MenuItem>
        <MenuItem 
          onClick={() => handleMenuAction(() => onDelete(selectedCourse!))}
          sx={{ color: theme.palette.error.main }}
        >
          <DeleteIcon style={{ marginRight: 8 }} />
          {getTranslation('courses.delete', 'Delete Course')}
        </MenuItem>
      </Menu>
    </>
  );
};

export default memo(CoursesTableView);

"""
Analytics URL Configuration
Provides URL routing for analytics API endpoints
"""

from django.urls import path
from . import analytics_views

app_name = 'analytics'

urlpatterns = [
    # Dashboard Analytics
    path(
        'dashboard/',
        analytics_views.get_dashboard_analytics,
        name='dashboard_analytics'
    ),
    
    # Real-time Statistics
    path(
        'real-time/',
        analytics_views.get_real_time_stats,
        name='real_time_stats'
    ),
    
    # User Activity Tracking
    path(
        'track-activity/',
        analytics_views.track_user_activity,
        name='track_activity'
    ),
    
    # Admin Analytics (requires admin permissions)
    path(
        'admin/',
        analytics_views.get_admin_analytics,
        name='admin_analytics'
    ),
    
    # Personal Analytics for authenticated users
    path(
        'personal/',
        analytics_views.get_user_personal_analytics,
        name='personal_analytics'
    ),
]

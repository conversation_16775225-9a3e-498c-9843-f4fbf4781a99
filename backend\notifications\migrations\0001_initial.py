# Generated by Django 4.2.7 on 2025-07-04 15:54

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "type",
                    models.<PERSON>r<PERSON>ield(
                        choices=[
                            ("ANNOUNCEMENT", "New Announcement"),
                            ("ASSIGNMENT", "New Assignment"),
                            ("GRADE", "Grade Posted"),
                            ("ENROLLMENT", "Course Enrollment"),
                            ("MATERIAL", "New Course Material"),
                            ("ATTENDANCE", "Attendance Marked"),
                        ],
                        max_length=20,
                    ),
                ),
                ("title", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("message", models.TextField()),
                ("related_object_type", models.Char<PERSON>ield(max_length=50)),
                ("related_object_id", models.Integer<PERSON>ield()),
                ("is_read", models.<PERSON><PERSON><PERSON><PERSON><PERSON>(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]

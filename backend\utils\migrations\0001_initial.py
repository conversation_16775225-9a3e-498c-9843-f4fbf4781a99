# Generated by Django 4.2.7 on 2025-07-04 15:54

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="TokenUsage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "model_name",
                    models.CharField(max_length=100, verbose_name="Model Name"),
                ),
                (
                    "tokens_used",
                    models.PositiveIntegerField(verbose_name="Tokens Used"),
                ),
                (
                    "request_type",
                    models.CharField(
                        default="API Request",
                        max_length=100,
                        verbose_name="Request Type",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        default="success", max_length=50, verbose_name="Status"
                    ),
                ),
            ],
            options={
                "verbose_name": "Token Usage",
                "verbose_name_plural": "Token Usage",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["model_name"], name="utils_token_model_n_7f44a9_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="utils_token_created_815b25_idx"
                    ),
                    models.Index(
                        fields=["request_type"], name="utils_token_request_6bc267_idx"
                    ),
                ],
            },
        ),
    ]

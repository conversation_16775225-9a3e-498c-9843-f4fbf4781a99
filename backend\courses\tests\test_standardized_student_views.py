"""Tests for StandardizedStudentCourseViewSet to verify the correct usage of required_level and recommended_level."""
import pytest
from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from core.models import StudentLevel
from courses.models import Course, CourseEnrollment
from courses.models.department import Department

User = get_user_model()


class TestStandardizedStudentCourseViewSet(TestCase):
    """Test cases for StandardizedStudentCourseViewSet level filtering"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create test user
        self.user = User.objects.create_user(
            username='teststudent',
            email='<EMAIL>',
            password='testpass123',
            user_type='STUDENT'
        )
        
        # Create test department
        self.department = Department.objects.create(
            name='Computer Science',
            code='CS'
        )
        
        # Create StudentLevel for the user
        self.student_level = StudentLevel.objects.create(
            student=self.user,
            current_level=2
        )
        
        # Create test courses with different required_level and recommended_level
        self.course_level_1 = Course.objects.create(
            title='Beginner Course',
            course_code='CS101',
            credits=3,
            semester='FALL',
            academic_year='2024-2025',
            required_level=1,
            recommended_level=1,
            department=self.department,
            is_published=True,
            is_active=True
        )
        
        self.course_level_2 = Course.objects.create(
            title='Elementary Course',
            course_code='CS201',
            credits=3,
            semester='FALL',
            academic_year='2024-2025',
            required_level=2,
            recommended_level=2,
            department=self.department,
            is_published=True,
            is_active=True
        )
        
        self.course_level_3 = Course.objects.create(
            title='Intermediate Course',
            course_code='CS301',
            credits=3,
            semester='FALL',
            academic_year='2024-2025',
            required_level=3,
            recommended_level=3,
            department=self.department,
            is_published=True,
            is_active=True
        )
        
        self.course_level_4 = Course.objects.create(
            title='Advanced Course',
            course_code='CS401',
            credits=3,
            semester='FALL',
            academic_year='2024-2025',
            required_level=4,
            recommended_level=4,
            department=self.department,
            is_published=True,
            is_active=True
        )
        
        # Authenticate the client
        self.client.force_authenticate(user=self.user)
    
    def test_available_filter_respects_required_level(self):
        """Test that 'available' filter only returns courses with required_level <= student level"""
        url = reverse('courses:student-courses-list')
        
        # Student level 2 should only see courses with required_level <= 2
        response = self.client.get(url, {'filter': 'available'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Extract course IDs from response
        courses_data = response.data.get('data', [])
        course_ids = [course['id'] for course in courses_data]
        
        # Should include courses with required_level 1 and 2
        self.assertIn(self.course_level_1.id, course_ids)
        self.assertIn(self.course_level_2.id, course_ids)
        
        # Should NOT include courses with required_level 3 and 4
        self.assertNotIn(self.course_level_3.id, course_ids)
        self.assertNotIn(self.course_level_4.id, course_ids)
    
    def test_recommended_filter_respects_recommended_level(self):
        """Test that 'recommended' filter returns courses with recommended_level = student level"""
        url = reverse('courses:student-courses-list')
        
        # Student level 2 should only see courses with recommended_level = 2
        response = self.client.get(url, {'filter': 'recommended'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Extract course IDs from response
        courses_data = response.data.get('data', [])
        course_ids = [course['id'] for course in courses_data]
        
        # Should include only course with recommended_level 2
        self.assertNotIn(self.course_level_1.id, course_ids)
        self.assertIn(self.course_level_2.id, course_ids)
        self.assertNotIn(self.course_level_3.id, course_ids)
        self.assertNotIn(self.course_level_4.id, course_ids)
    
    def test_all_filter_respects_required_level(self):
        """Test that 'all' filter returns courses with required_level <= student level"""
        url = reverse('courses:student-courses-list')
        
        # Student level 2 should only see courses with required_level <= 2
        response = self.client.get(url, {'filter': 'all'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Extract course IDs from response
        courses_data = response.data.get('data', [])
        course_ids = [course['id'] for course in courses_data]
        
        # Should include courses with required_level 1 and 2
        self.assertIn(self.course_level_1.id, course_ids)
        self.assertIn(self.course_level_2.id, course_ids)
        
        # Should NOT include courses with required_level 3 and 4
        self.assertNotIn(self.course_level_3.id, course_ids)
        self.assertNotIn(self.course_level_4.id, course_ids)
    
    def test_enrolled_filter_shows_enrolled_courses_regardless_of_level(self):
        """Test that 'enrolled' filter shows enrolled courses regardless of level"""
        # Enroll student in a course with higher required_level
        enrollment = CourseEnrollment.objects.create(
            user=self.user,
            course=self.course_level_4,  # Level 4 course
            status='APPROVED'
        )
        
        url = reverse('courses:student-courses-list')
        response = self.client.get(url, {'filter': 'enrolled'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Extract course IDs from response
        courses_data = response.data.get('data', [])
        course_ids = [course['id'] for course in courses_data]
        
        # Should include the enrolled course even if its required_level > student level
        self.assertIn(self.course_level_4.id, course_ids)
    
    def test_level_override_parameter(self):
        """Test that level parameter overrides student's actual level"""
        url = reverse('courses:student-courses-list')
        
        # Use level = 1 parameter to override student level 2
        response = self.client.get(url, {'filter': 'available', 'level': '1'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Extract course IDs from response
        courses_data = response.data.get('data', [])
        course_ids = [course['id'] for course in courses_data]
        
        # Should only include course with required_level 1
        self.assertIn(self.course_level_1.id, course_ids)
        self.assertNotIn(self.course_level_2.id, course_ids)
        self.assertNotIn(self.course_level_3.id, course_ids)
        self.assertNotIn(self.course_level_4.id, course_ids)
    
    def test_student_without_level_uses_default_level_1(self):
        """Test that students without StudentLevel use default level 1"""
        # Delete the student level
        self.student_level.delete()
        
        url = reverse('courses:student-courses-list')
        response = self.client.get(url, {'filter': 'available'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Extract course IDs from response
        courses_data = response.data.get('data', [])
        course_ids = [course['id'] for course in courses_data]
        
        # Should only include course with required_level 1
        self.assertIn(self.course_level_1.id, course_ids)
        self.assertNotIn(self.course_level_2.id, course_ids)
        self.assertNotIn(self.course_level_3.id, course_ids)
        self.assertNotIn(self.course_level_4.id, course_ids)


@pytest.mark.django_db
class TestStandardizedStudentCourseViewSetPytest:
    """Pytest version of tests for StandardizedStudentCourseViewSet level filtering"""
    
    @pytest.fixture
    def setup_data(self):
        """Set up test data for pytest"""
        # Create test user
        user = User.objects.create_user(
            username='teststudent_pytest',
            email='<EMAIL>',
            password='testpass123',
            user_type='STUDENT'
        )
        
        # Create test department
        department = Department.objects.create(
            name='Computer Science Pytest',
            code='CSP'
        )
        
        # Create StudentLevel for the user
        student_level = StudentLevel.objects.create(
            student=user,
            current_level=2
        )
        
        # Create test courses with different required_level and recommended_level
        course_level_1 = Course.objects.create(
            title='Beginner Course Pytest',
            course_code='CSP101',
            credits=3,
            semester='FALL',
            academic_year='2024-2025',
            required_level=1,
            recommended_level=1,
            department=department,
            is_published=True,
            is_active=True
        )
        
        course_level_2 = Course.objects.create(
            title='Elementary Course Pytest',
            course_code='CSP201',
            credits=3,
            semester='FALL',
            academic_year='2024-2025',
            required_level=2,
            recommended_level=2,
            department=department,
            is_published=True,
            is_active=True
        )
        
        course_level_3 = Course.objects.create(
            title='Intermediate Course Pytest',
            course_code='CSP301',
            credits=3,
            semester='FALL',
            academic_year='2024-2025',
            required_level=3,
            recommended_level=3,
            department=department,
            is_published=True,
            is_active=True
        )
        
        return {
            'user': user,
            'department': department,
            'student_level': student_level,
            'course_level_1': course_level_1,
            'course_level_2': course_level_2,
            'course_level_3': course_level_3
        }
    
    def test_level_2_student_only_sees_appropriate_courses(self, setup_data):
        """Test that student level 2 only returns courses with required_level <= 2"""
        client = APIClient()
        client.force_authenticate(user=setup_data['user'])
        
        url = reverse('courses:student-courses-list')
        response = client.get(url, {'filter': 'available'})
        assert response.status_code == status.HTTP_200_OK
        
        # Extract course IDs from response
        courses_data = response.data.get('data', [])
        course_ids = [course['id'] for course in courses_data]
        
        # Should include courses with required_level 1 and 2
        assert setup_data['course_level_1'].id in course_ids
        assert setup_data['course_level_2'].id in course_ids
        
        # Should NOT include course with required_level 3
        assert setup_data['course_level_3'].id not in course_ids
    
    def test_recommended_filter_exact_match(self, setup_data):
        """Test that recommended filter returns exact match for recommended_level"""
        client = APIClient()
        client.force_authenticate(user=setup_data['user'])
        
        url = reverse('courses:student-courses-list')
        response = client.get(url, {'filter': 'recommended'})
        assert response.status_code == status.HTTP_200_OK
        
        # Extract course IDs from response
        courses_data = response.data.get('data', [])
        course_ids = [course['id'] for course in courses_data]
        
        # Should include only course with recommended_level = 2
        assert setup_data['course_level_1'].id not in course_ids
        assert setup_data['course_level_2'].id in course_ids
        assert setup_data['course_level_3'].id not in course_ids

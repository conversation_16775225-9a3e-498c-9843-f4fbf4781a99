import React, { useState } from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Hotspot } from '../../types';

interface HotspotImageProps {
  imageUrl: string;
  hotspots: Hotspot[];
  onHotspotsChange: (selectedHotspots: Hotspot[]) => void;
  isSubmitting: boolean;
  value?: string;
}

const HotspotImage: React.FC<HotspotImageProps> = ({
  imageUrl,
  hotspots,
  onHotspotsChange,
  isSubmitting,
  value,
}) => {
  const { t } = useTranslation();
  const [selectedHotspots, setSelectedHotspots] = useState<Hotspot[]>(() => {
    if (value) {
      try {
        return JSON.parse(value) as Hotspot[];
      } catch {
        return [];
      }
    }
    return [];
  });

  const handleHotspotClick = (hotspot: Hotspot) => {
    if (isSubmitting) return;

    const isSelected = selectedHotspots.some(h => h.id === hotspot.id);
    const updatedHotspots = isSelected
      ? selectedHotspots.filter(h => h.id !== hotspot.id)
      : [...selectedHotspots, hotspot];

    setSelectedHotspots(updatedHotspots);
    onHotspotsChange(updatedHotspots);
  };

  return (
    <Box sx={{ position: 'relative', mt: 2 }}>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        {t('assessment.hotspotImage.instruction')}
      </Typography>
      <Paper
        component="img"
        src={imageUrl}
        alt={t('assessment.hotspotImage.altText')}
        sx={{
          width: '100%',
          height: 'auto',
          userSelect: 'none',
          pointerEvents: isSubmitting ? 'none' : 'auto',
        }}
      />

      {hotspots.map(hotspot => (
        <Box
          key={hotspot.id}
          component="button"
          onClick={() => handleHotspotClick(hotspot)}
          sx={{
            position: 'absolute',
            top: `${hotspot.y}%`,
            left: `${hotspot.x}%`,
            width: `${hotspot.width || 5}%`,
            height: `${hotspot.height || 5}%`,
            border: `2px solid ${selectedHotspots.some(h => h.id === hotspot.id) ? 'blue' : 'transparent'}`,
            backgroundColor: selectedHotspots.some(h => h.id === hotspot.id) ? 'rgba(0,0,255,0.1)' : 'transparent',
            cursor: isSubmitting ? 'default' : 'pointer',
            transition: 'background-color 0.3s ease',
            zIndex: 10,
          }}
          aria-label={hotspot.label || t('assessment.hotspotImage.hotspotLabel')}
        />
      ))}
    </Box>
  );
};

export default HotspotImage;


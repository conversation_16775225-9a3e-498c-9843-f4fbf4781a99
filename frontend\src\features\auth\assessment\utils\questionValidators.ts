import {
  Question,
  QuestionType,
  DragItem,
  MatchingPair,
  Hotspot,
  TestCase,
  FileUploadConfig,
  PeerReviewConfig,
  MathValidationRule,
} from '../types';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Base question validator
export const validateBaseQuestion = (question: Partial<Question>): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!question.text || question.text.trim().length === 0) {
    errors.push('Question text is required');
  }

  if (!question.type) {
    errors.push('Question type is required');
  }

  if (question.difficulty_level && (question.difficulty_level < 1 || question.difficulty_level > 10)) {
    warnings.push('Difficulty level should be between 1 and 10');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

// Drag and drop ordering validator
export const validateDragDropOrdering = (question: Partial<Question>): ValidationResult => {
  const baseResult = validateBaseQuestion(question);
  const errors = [...baseResult.errors];
  const warnings = [...baseResult.warnings];

  if (!question.dragItems || question.dragItems.length === 0) {
    errors.push('Drag items are required for ordering questions');
  } else {
    // Validate each drag item
    question.dragItems.forEach((item, index) => {
      if (!item.id || item.id.trim().length === 0) {
        errors.push(`Drag item ${index + 1} must have a unique ID`);
      }
      if (!item.content || item.content.trim().length === 0) {
        errors.push(`Drag item ${index + 1} must have content`);
      }
    });

    // Check for duplicate IDs
    const ids = question.dragItems.map(item => item.id);
    const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index);
    if (duplicateIds.length > 0) {
      errors.push(`Duplicate drag item IDs found: ${duplicateIds.join(', ')}`);
    }

    if (question.dragItems.length < 2) {
      warnings.push('Ordering questions should have at least 2 items');
    }

    if (question.dragItems.length > 10) {
      warnings.push('Too many items may make the question difficult to use on mobile devices');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

// Drag and drop matching validator
export const validateDragDropMatching = (question: Partial<Question>): ValidationResult => {
  const baseResult = validateBaseQuestion(question);
  const errors = [...baseResult.errors];
  const warnings = [...baseResult.warnings];

  if (!question.matchingPairs || question.matchingPairs.length === 0) {
    errors.push('Matching pairs are required for matching questions');
  } else {
    question.matchingPairs.forEach((pair, index) => {
      if (!pair.id || pair.id.trim().length === 0) {
        errors.push(`Matching pair ${index + 1} must have a unique ID`);
      }
      if (!pair.left || pair.left.trim().length === 0) {
        errors.push(`Matching pair ${index + 1} must have left content`);
      }
      if (!pair.right || pair.right.trim().length === 0) {
        errors.push(`Matching pair ${index + 1} must have right content`);
      }
      if (!pair.leftId || pair.leftId.trim().length === 0) {
        errors.push(`Matching pair ${index + 1} must have a left ID`);
      }
      if (!pair.rightId || pair.rightId.trim().length === 0) {
        errors.push(`Matching pair ${index + 1} must have a right ID`);
      }
    });

    if (question.matchingPairs.length < 2) {
      warnings.push('Matching questions should have at least 2 pairs');
    }

    if (question.matchingPairs.length > 8) {
      warnings.push('Too many pairs may make the question cluttered');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

// Hotspot image validator
export const validateHotspotImage = (question: Partial<Question>): ValidationResult => {
  const baseResult = validateBaseQuestion(question);
  const errors = [...baseResult.errors];
  const warnings = [...baseResult.warnings];

  if (!question.imageUrl || question.imageUrl.trim().length === 0) {
    errors.push('Image URL is required for hotspot questions');
  } else {
    // Basic URL validation
    try {
      new URL(question.imageUrl);
    } catch {
      errors.push('Image URL must be a valid URL');
    }
  }

  if (!question.hotspots || question.hotspots.length === 0) {
    errors.push('Hotspots are required for hotspot questions');
  } else {
    question.hotspots.forEach((hotspot, index) => {
      if (!hotspot.id || hotspot.id.trim().length === 0) {
        errors.push(`Hotspot ${index + 1} must have a unique ID`);
      }
      
      if (hotspot.x < 0 || hotspot.x > 100) {
        errors.push(`Hotspot ${index + 1} X coordinate must be between 0 and 100`);
      }
      
      if (hotspot.y < 0 || hotspot.y > 100) {
        errors.push(`Hotspot ${index + 1} Y coordinate must be between 0 and 100`);
      }

      if (hotspot.width && (hotspot.width <= 0 || hotspot.width > 100)) {
        errors.push(`Hotspot ${index + 1} width must be between 0 and 100`);
      }

      if (hotspot.height && (hotspot.height <= 0 || hotspot.height > 100)) {
        errors.push(`Hotspot ${index + 1} height must be between 0 and 100`);
      }
    });

    if (question.hotspots.length > 15) {
      warnings.push('Too many hotspots may make the question confusing');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

// Code execution validator
export const validateCodeExecution = (question: Partial<Question>): ValidationResult => {
  const baseResult = validateBaseQuestion(question);
  const errors = [...baseResult.errors];
  const warnings = [...baseResult.warnings];

  if (!question.language || question.language.trim().length === 0) {
    errors.push('Programming language is required for code execution questions');
  } else {
    const supportedLanguages = ['javascript', 'python', 'java', 'cpp', 'c', 'csharp', 'typescript'];
    if (!supportedLanguages.includes(question.language.toLowerCase())) {
      warnings.push(`Language "${question.language}" may not be supported. Supported languages: ${supportedLanguages.join(', ')}`);
    }
  }

  if (!question.codeTemplate || question.codeTemplate.trim().length === 0) {
    warnings.push('Consider providing a code template to help students get started');
  }

  if (!question.testCases || question.testCases.length === 0) {
    errors.push('Test cases are required for code execution questions');
  } else {
    question.testCases.forEach((testCase, index) => {
      if (!testCase.id || testCase.id.trim().length === 0) {
        errors.push(`Test case ${index + 1} must have a unique ID`);
      }
      if (testCase.input === undefined || testCase.input === null) {
        errors.push(`Test case ${index + 1} must have input (can be empty string)`);
      }
      if (!testCase.expectedOutput && testCase.expectedOutput !== '') {
        errors.push(`Test case ${index + 1} must have expected output`);
      }
    });

    if (question.testCases.length < 2) {
      warnings.push('Consider having at least 2 test cases to properly validate solutions');
    }

    const visibleTests = question.testCases.filter(test => !test.isHidden);
    if (visibleTests.length === 0) {
      warnings.push('Consider having at least one visible test case for students');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

// Math input validator
export const validateMathInput = (question: Partial<Question>): ValidationResult => {
  const baseResult = validateBaseQuestion(question);
  const errors = [...baseResult.errors];
  const warnings = [...baseResult.warnings];

  if (question.mathValidation) {
    const validation = question.mathValidation;
    
    if (!['latex', 'numeric', 'algebraic'].includes(validation.type)) {
      errors.push('Math validation type must be one of: latex, numeric, algebraic');
    }

    if (validation.type === 'numeric' && validation.tolerance !== undefined) {
      if (validation.tolerance < 0) {
        errors.push('Numeric tolerance must be non-negative');
      }
    }
  }

  if (!question.correct_answer || question.correct_answer.trim().length === 0) {
    warnings.push('Consider providing a correct answer for automatic grading');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

// File upload validator
export const validateFileUpload = (question: Partial<Question>): ValidationResult => {
  const baseResult = validateBaseQuestion(question);
  const errors = [...baseResult.errors];
  const warnings = [...baseResult.warnings];

  if (!question.fileUploadConfig) {
    errors.push('File upload configuration is required for file upload questions');
  } else {
    const config = question.fileUploadConfig;
    
    if (!config.maxFileSize || config.maxFileSize <= 0) {
      errors.push('Maximum file size must be greater than 0');
    }

    if (config.maxFileSize > 100 * 1024 * 1024) { // 100MB
      warnings.push('Large file size limits may cause upload issues');
    }

    if (!config.maxFiles || config.maxFiles <= 0) {
      errors.push('Maximum number of files must be greater than 0');
    }

    if (config.maxFiles > 10) {
      warnings.push('Allow too many files may make grading difficult');
    }

    if (!config.allowedTypes || config.allowedTypes.length === 0) {
      errors.push('At least one allowed file type must be specified');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

// Peer review validator
export const validatePeerReview = (question: Partial<Question>): ValidationResult => {
  const baseResult = validateBaseQuestion(question);
  const errors = [...baseResult.errors];
  const warnings = [...baseResult.warnings];

  if (!question.peerReviewConfig) {
    errors.push('Peer review configuration is required for peer review questions');
  } else {
    const config = question.peerReviewConfig;
    
    if (!config.minReviewers || config.minReviewers <= 0) {
      errors.push('Minimum number of reviewers must be greater than 0');
    }

    if (config.minReviewers > 10) {
      warnings.push('Too many required reviewers may delay grading');
    }

    if (!config.reviewCriteria || config.reviewCriteria.length === 0) {
      errors.push('At least one review criterion must be specified');
    } else {
      config.reviewCriteria.forEach((criterion, index) => {
        if (!criterion.id || criterion.id.trim().length === 0) {
          errors.push(`Review criterion ${index + 1} must have a unique ID`);
        }
        if (!criterion.name || criterion.name.trim().length === 0) {
          errors.push(`Review criterion ${index + 1} must have a name`);
        }
        if (!criterion.maxScore || criterion.maxScore <= 0) {
          errors.push(`Review criterion ${index + 1} must have a positive maximum score`);
        }
        if (!criterion.weight || criterion.weight <= 0) {
          errors.push(`Review criterion ${index + 1} must have a positive weight`);
        }
      });

      if (config.reviewCriteria.length > 8) {
        warnings.push('Too many review criteria may make peer review tedious');
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

// Main question validator that dispatches to specific validators
export const validateQuestion = (question: Partial<Question>): ValidationResult => {
  if (!question.type) {
    return {
      isValid: false,
      errors: ['Question type is required'],
      warnings: [],
    };
  }

  switch (question.type) {
    case QuestionType.DRAG_DROP_ORDERING:
      return validateDragDropOrdering(question);
    case QuestionType.DRAG_DROP_MATCHING:
      return validateDragDropMatching(question);
    case QuestionType.HOTSPOT_IMAGE:
      return validateHotspotImage(question);
    case QuestionType.CODE_EXECUTION:
      return validateCodeExecution(question);
    case QuestionType.MATH_INPUT:
      return validateMathInput(question);
    case QuestionType.FILE_UPLOAD:
      return validateFileUpload(question);
    case QuestionType.PEER_REVIEW:
      return validatePeerReview(question);
    default:
      return validateBaseQuestion(question);
  }
};

// Batch validator for multiple questions
export const validateQuestions = (questions: Partial<Question>[]): { 
  isValid: boolean; 
  results: (ValidationResult & { questionIndex: number })[] 
} => {
  const results = questions.map((question, index) => ({
    ...validateQuestion(question),
    questionIndex: index,
  }));

  const isValid = results.every(result => result.isValid);

  return { isValid, results };
};

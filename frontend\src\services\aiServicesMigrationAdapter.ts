/**
 * AI Services Migration Adapter
 * 
 * This adapter provides a smooth transition from legacy AI services
 * to the unified AI service, ensuring backward compatibility while
 * encouraging migration to the new consolidated approach.
 */

import unifiedAiService from './unifiedAiService';

// ==========================================
// MIGRATION ADAPTER CLASS
// ==========================================

class AIServicesMigrationAdapter {
  private migrationWarnings: Set<string> = new Set();
  private usageTracking: Map<string, number> = new Map();

  /**
   * Log deprecation warning for legacy service usage
   */
  private logDeprecationWarning(serviceName: string, method: string): void {
    const warningKey = `${serviceName}.${method}`;
    
    if (!this.migrationWarnings.has(warningKey)) {
      console.warn(
        `🚨 DEPRECATION WARNING: ${serviceName}.${method}() is deprecated. ` +
        `Please migrate to unifiedAiService.${this.getSuggestedMethod(method)}()`
      );
      this.migrationWarnings.add(warningKey);
    }

    // Track usage
    const currentCount = this.usageTracking.get(warningKey) || 0;
    this.usageTracking.set(warningKey, currentCount + 1);
  }

  /**
   * Get suggested unified method for legacy method
   */
  private getSuggestedMethod(legacyMethod: string): string {
    const methodMap: { [key: string]: string } = {
      askQuestion: 'chat',
      getSuggestions: 'getSuggestions',
      sendMessage: 'chat',
      getConversations: 'getConversations',
      generateStudyPlan: 'generateStudyContent',
      generateFlashcards: 'generateStudyContent',
      getStudyTopics: 'getStudyTopics',
      getStudySessions: 'getStudySessions',
    };
    return methodMap[legacyMethod] || 'chat';
  }

  // ==========================================
  // AI ASSISTANT ADAPTER METHODS
  // ==========================================

  async aiAssistant_askQuestion(question: string, context: any = {}): Promise<any> {
    this.logDeprecationWarning('aiAssistantService', 'askQuestion');
    return await unifiedAiService.askQuestion(question, context);
  }

  async aiAssistant_getSuggestions(userType?: string): Promise<any> {
    this.logDeprecationWarning('aiAssistantService', 'getSuggestions');
    const response = await unifiedAiService.getSuggestions(userType || 'all');
    return response.data;
  }

  async aiAssistant_getAnalytics(): Promise<any> {
    this.logDeprecationWarning('aiAssistantService', 'getAnalytics');
    try {
      const response = await unifiedAiService.getMLAnalytics();
      return response.data;
    } catch (error) {
      console.error('Analytics request failed:', error);
      return { error: 'Analytics temporarily unavailable' };
    }
  }

  // ==========================================
  // CHATBOT ADAPTER METHODS
  // ==========================================

  async chatbot_sendMessage(message: string, conversationId?: number): Promise<any> {
    this.logDeprecationWarning('chatbotService', 'sendMessage');
    return await unifiedAiService.sendMessage(message, conversationId);
  }

  async chatbot_getConversations(): Promise<any[]> {
    this.logDeprecationWarning('chatbotService', 'getConversations');
    return await unifiedAiService.getConversations();
  }

  async chatbot_getConversation(id: number): Promise<any> {
    this.logDeprecationWarning('chatbotService', 'getConversation');
    try {
      // Since unified service doesn't have this specific method yet,
      // we'll provide a fallback
      const conversations = await unifiedAiService.getConversations();
      return conversations.find(conv => conv.id === id) || null;
    } catch (error) {
      console.error('Get conversation failed:', error);
      return null;
    }
  }
  async chatbot_deleteConversation(_id: number): Promise<boolean> {
    this.logDeprecationWarning('chatbotService', 'deleteConversation');
    // This would need to be implemented in the unified service
    console.warn('Delete conversation not yet implemented in unified service');
    return false;
  }

  async chatbot_updateConversationTitle(_id: number, _title: string): Promise<any> {
    this.logDeprecationWarning('chatbotService', 'updateConversationTitle');
    // This would need to be implemented in the unified service
    console.warn('Update conversation title not yet implemented in unified service');
    return null;
  }

  // ==========================================
  // STUDY ASSISTANT ADAPTER METHODS
  // ==========================================

  async studyAssistant_generateStudyPlan(params: any): Promise<any> {
    this.logDeprecationWarning('studyAssistantService', 'generateStudyPlan');
    const response = await unifiedAiService.generateStudyContent('plan', params);
    return response.data;
  }

  async studyAssistant_generateFlashcards(params: any): Promise<any[]> {
    this.logDeprecationWarning('studyAssistantService', 'generateFlashcards');
    const response = await unifiedAiService.generateStudyContent('flashcards', params);
    return response.data;
  }

  async studyAssistant_getStudyTopics(): Promise<any[]> {
    this.logDeprecationWarning('studyAssistantService', 'getStudyTopics');
    return await unifiedAiService.getStudyTopics();
  }

  async studyAssistant_getStudySessions(): Promise<any[]> {
    this.logDeprecationWarning('studyAssistantService', 'getStudySessions');
    return await unifiedAiService.getStudySessions();
  }

  async studyAssistant_generatePracticeQuestions(params: any): Promise<any[]> {
    this.logDeprecationWarning('studyAssistantService', 'generatePracticeQuestions');
    const response = await unifiedAiService.generateStudyContent('questions', params);
    return response.data;
  }

  // ==========================================
  // MIGRATION UTILITIES
  // ==========================================

  /**
   * Get migration statistics
   */
  getMigrationStats(): {
    totalWarnings: number;
    serviceUsage: { [key: string]: number };
    mostUsedLegacyMethods: Array<{ method: string; count: number }>;
  } {
    const serviceUsage = Object.fromEntries(this.usageTracking);
    const mostUsed = Array.from(this.usageTracking.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([method, count]) => ({ method, count }));

    return {
      totalWarnings: this.migrationWarnings.size,
      serviceUsage,
      mostUsedLegacyMethods: mostUsed,
    };
  }

  /**
   * Clear migration tracking data
   */
  clearMigrationData(): void {
    this.migrationWarnings.clear();
    this.usageTracking.clear();
  }

  /**
   * Generate migration report
   */
  generateMigrationReport(): string {
    const stats = this.getMigrationStats();
    
    let report = '🔄 AI Services Migration Report\n';
    report += '=================================\n\n';
    
    if (stats.totalWarnings === 0) {
      report += '✅ No legacy service usage detected. Great job!\n';
      return report;
    }

    report += `⚠️  Total legacy methods used: ${stats.totalWarnings}\n`;
    report += `📊 Total legacy calls made: ${Object.values(stats.serviceUsage).reduce((a, b) => a + b, 0)}\n\n`;

    if (stats.mostUsedLegacyMethods.length > 0) {
      report += '🔥 Most used legacy methods:\n';
      stats.mostUsedLegacyMethods.forEach(({ method, count }, index) => {
        report += `   ${index + 1}. ${method}: ${count} calls\n`;
      });
      report += '\n';
    }

    report += '📝 Migration Recommendations:\n';
    report += '   • Replace aiAssistantService.askQuestion() with unifiedAiService.chat()\n';
    report += '   • Replace chatbotService.sendMessage() with unifiedAiService.chat()\n';
    report += '   • Replace studyAssistantService methods with unifiedAiService.generateStudyContent()\n';
    report += '   • See documentation for complete migration guide\n';

    return report;
  }
}

// Export singleton instance
export const migrationAdapter = new AIServicesMigrationAdapter();

// ==========================================
// LEGACY SERVICE COMPATIBILITY WRAPPERS
// ==========================================

/**
 * Legacy AI Assistant Service wrapper
 * @deprecated Use unifiedAiService instead
 */
export const legacyAiAssistantService = {
  askQuestion: (question: string, context?: any) => 
    migrationAdapter.aiAssistant_askQuestion(question, context),
  
  getSuggestions: (userType?: string) => 
    migrationAdapter.aiAssistant_getSuggestions(userType),
  
  getAnalytics: () => 
    migrationAdapter.aiAssistant_getAnalytics(),
};

/**
 * Legacy Chatbot Service wrapper
 * @deprecated Use unifiedAiService instead
 */
export const legacyChatbotService = {
  sendMessage: (message: string, conversationId?: number) => 
    migrationAdapter.chatbot_sendMessage(message, conversationId),
  
  getConversations: () => 
    migrationAdapter.chatbot_getConversations(),
  
  getConversation: (id: number) => 
    migrationAdapter.chatbot_getConversation(id),
  
  deleteConversation: (id: number) => 
    migrationAdapter.chatbot_deleteConversation(id),
  
  updateConversationTitle: (id: number, title: string) => 
    migrationAdapter.chatbot_updateConversationTitle(id, title),
};

/**
 * Legacy Study Assistant Service wrapper
 * @deprecated Use unifiedAiService instead
 */
export const legacyStudyAssistantService = {
  generateStudyPlan: (params: any) => 
    migrationAdapter.studyAssistant_generateStudyPlan(params),
  
  generateFlashcards: (params: any) => 
    migrationAdapter.studyAssistant_generateFlashcards(params),
  
  getStudyTopics: () => 
    migrationAdapter.studyAssistant_getStudyTopics(),
  
  getStudySessions: () => 
    migrationAdapter.studyAssistant_getStudySessions(),
  
  generatePracticeQuestions: (params: any) => 
    migrationAdapter.studyAssistant_generatePracticeQuestions(params),
};

export default migrationAdapter;

"""
Global pytest configuration and fixtures for North Star University backend tests.
"""

import os
import pytest
import tempfile
from unittest.mock import Mock, patch
from django.test import Client, override_settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db import transaction
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

# Set test environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings.test')

import django
django.setup()

User = get_user_model()


@pytest.fixture(scope='session')
def django_db_setup():
    """Setup test database."""
    pass


@pytest.fixture(autouse=True)
def enable_db_access_for_all_tests(db):
    """Enable database access for all tests."""
    pass


@pytest.fixture(autouse=True)
def clear_cache():
    """Clear cache before each test."""
    cache.clear()
    yield
    cache.clear()


@pytest.fixture
def client():
    """Django test client."""
    return Client()


@pytest.fixture
def api_client():
    """DRF API test client."""
    return APIClient()


@pytest.fixture
def authenticated_client(api_client, test_user):
    """Authenticated API client."""
    refresh = RefreshToken.for_user(test_user)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    return api_client


@pytest.fixture
def test_user():
    """Create a test user."""
    return User.objects.create_user(
        username='testuser',
        email='<EMAIL>',
        password='testpass123',
        first_name='Test',
        last_name='User'
    )


@pytest.fixture
def test_student(test_user):
    """Create a test student user."""
    test_user.role = 'STUDENT'
    test_user.save()
    return test_user


@pytest.fixture
def test_professor():
    """Create a test professor user."""
    return User.objects.create_user(
        username='professor',
        email='<EMAIL>',
        password='profpass123',
        first_name='Prof',
        last_name='Teacher',
        role='PROFESSOR'
    )


@pytest.fixture
def test_admin():
    """Create a test admin user."""
    return User.objects.create_superuser(
        username='admin',
        email='<EMAIL>',
        password='adminpass123',
        first_name='Admin',
        last_name='User'
    )


@pytest.fixture
def mock_ai_service():
    """Mock AI service for testing."""
    with patch('utils.ai_service_improved.AIService') as mock:
        mock_instance = Mock()
        mock_instance.generate_content.return_value = {
            'content': 'Test AI generated content',
            'success': True
        }
        mock_instance.analyze_text.return_value = {
            'analysis': 'Test analysis',
            'confidence': 0.95
        }
        mock.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def mock_gemini_api():
    """Mock Gemini API for testing."""
    with patch('google.generativeai.GenerativeModel') as mock:
        mock_instance = Mock()
        mock_response = Mock()
        mock_response.text = 'Test AI response'
        mock_instance.generate_content.return_value = mock_response
        mock.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def temp_media_root():
    """Temporary media root for file upload tests."""
    with tempfile.TemporaryDirectory() as temp_dir:
        with override_settings(MEDIA_ROOT=temp_dir):
            yield temp_dir


@pytest.fixture
def sample_course_data():
    """Sample course data for testing."""
    return {
        'title': 'Test Course',
        'description': 'A test course for unit testing',
        'code': 'TEST101',
        'credits': 3,
        'level': 'BEGINNER'
    }


@pytest.fixture
def sample_assessment_data():
    """Sample assessment data for testing."""
    return {
        'title': 'Test Assessment',
        'description': 'A test assessment',
        'assessment_type': 'QUIZ',
        'time_limit': 60,
        'max_attempts': 3,
        'passing_score': 70
    }


@pytest.fixture
def sample_question_data():
    """Sample question data for testing."""
    return {
        'question_text': 'What is 2 + 2?',
        'question_type': 'MULTIPLE_CHOICE',
        'points': 10,
        'options': [
            {'text': '3', 'is_correct': False},
            {'text': '4', 'is_correct': True},
            {'text': '5', 'is_correct': False},
            {'text': '6', 'is_correct': False}
        ]
    }


@pytest.fixture(autouse=True)
def mock_external_apis():
    """Mock external APIs to prevent real API calls during tests."""
    with patch('requests.post') as mock_post, \
         patch('requests.get') as mock_get:
        
        # Mock successful responses
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'success': True, 'data': 'test'}
        mock_response.text = 'test response'
        
        mock_post.return_value = mock_response
        mock_get.return_value = mock_response
        
        yield {
            'post': mock_post,
            'get': mock_get,
            'response': mock_response
        }


# Performance testing fixtures
@pytest.fixture
def performance_timer():
    """Timer fixture for performance tests."""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
        
        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
    
    return Timer()


# Database transaction fixtures
@pytest.fixture
def transactional_db(db):
    """Enable transactional database for testing."""
    with transaction.atomic():
        yield


# Custom markers for test organization
def pytest_configure(config):
    """Configure custom pytest markers."""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "api: API tests")
    config.addinivalue_line("markers", "slow: Slow tests")
    config.addinivalue_line("markers", "auth: Authentication tests")
    config.addinivalue_line("markers", "ai: AI service tests")
    config.addinivalue_line("markers", "database: Database tests")
    config.addinivalue_line("markers", "security: Security tests")
    config.addinivalue_line("markers", "performance: Performance tests")


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""
    for item in items:
        # Add markers based on test file location
        if "test_api" in item.nodeid or "api" in item.nodeid:
            item.add_marker(pytest.mark.api)
        if "test_auth" in item.nodeid or "auth" in item.nodeid:
            item.add_marker(pytest.mark.auth)
        if "test_ai" in item.nodeid or "ai" in item.nodeid:
            item.add_marker(pytest.mark.ai)
        if "integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        if "performance" in item.nodeid:
            item.add_marker(pytest.mark.performance)
        if "security" in item.nodeid:
            item.add_marker(pytest.mark.security)

# Generated by Django 4.2.7 on 2025-07-04 15:54

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AIAssistantInteraction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("question", models.TextField()),
                ("answer", models.TextField()),
                ("confidence_score", models.FloatField(default=0.0)),
                (
                    "response_time",
                    models.FloatField(help_text="Response time in seconds"),
                ),
                (
                    "feedback_rating",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (1, "Very Poor"),
                            (2, "Poor"),
                            (3, "Average"),
                            (4, "Good"),
                            (5, "Excellent"),
                        ],
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("metadata", models.J<PERSON><PERSON>ield(blank=True, default=dict)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="AIAssistantSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("session_id", models.CharField(max_length=100, unique=True)),
                ("started_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("ended_at", models.DateTimeField(blank=True, null=True)),
                ("total_interactions", models.PositiveIntegerField(default=0)),
                ("context", models.JSONField(blank=True, default=dict)),
            ],
            options={
                "ordering": ["-started_at"],
            },
        ),
        migrations.CreateModel(
            name="AIAssistantSuggestion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("text", models.CharField(max_length=500)),
                ("icon", models.CharField(default="QuestionAnswerIcon", max_length=50)),
                ("category", models.CharField(default="general", max_length=100)),
                (
                    "priority",
                    models.IntegerField(
                        default=0, help_text="Higher numbers appear first"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "target_user_type",
                    models.CharField(
                        choices=[
                            ("all", "All Users"),
                            ("student", "Students"),
                            ("professor", "Professors"),
                            ("admin", "Administrators"),
                        ],
                        default="all",
                        max_length=50,
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["-priority", "-created_at"],
            },
        ),
    ]

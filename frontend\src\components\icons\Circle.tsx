import React from 'react';
import { FiCircle } from 'react-icons/fi';

/**
 * Circle icon implementation using React Icons
 */
interface CircleProps {
  size?: number;
  color?: string;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

export default function Circle({
  size = 24,
  color,
  className,
  style,
  onClick,
}: CircleProps): React.ReactElement {
  return (
    <FiCircle
      size={size}
      color={color}
      className={className}
      style={style}
      onClick={onClick}
    />
  );
}

"""
Optimized Assessment views with performance improvements.

This module contains optimized versions of assessment views with:
- Service layer integration for business logic separation
- Efficient pagination and filtering
- Reduced N+1 query problems through proper use of select_related/prefetch_related
- Strategic caching for frequently accessed data
- Proper error handling and validation
"""

from typing import Dict, Any, List, Optional
import logging

from django.core.cache import cache
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Q, Prefetch
from django.http import Http404
from django.shortcuts import get_object_or_404
from django.utils import timezone

from rest_framework import status, viewsets, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView

# Import services
from assessment.services import (
    AssessmentService,
    AssessmentResponseService,
    StudentLevelService,
    LevelManagementService
)

# Import optimized models
from assessment.models_optimized import (
    Assessment,
    AssessmentQuestion,
    AssessmentResponse,
    StudentLevel
)

# Import serializers
from assessment.serializers import (
    AssessmentDetailSerializer,
    AssessmentQuestionSerializer,
    AssessmentResponseSerializer,
    AssessmentSerializer,
    StudentLevelSerializer
)

# Import permissions
from users.permissions import (
    CanTakeAssessments,
    IsAdminUser,
    IsProfessorUser,
    IsStudentUser
)

# Import error classes
from core.services.base import (
    ServiceError,
    ValidationServiceError,
    NotFoundServiceError,
    PermissionServiceError
)

logger = logging.getLogger(__name__)


class OptimizedPaginationMixin:
    """Mixin providing optimized pagination for views."""
    
    page_size = 20
    max_page_size = 100
    
    def get_paginated_response(self, queryset, request, serializer_class):
        """Get paginated response with optimized queries."""
        page_size = min(
            int(request.query_params.get('page_size', self.page_size)),
            self.max_page_size
        )
        
        paginator = Paginator(queryset, page_size)
        page_number = request.query_params.get('page', 1)
        page_obj = paginator.get_page(page_number)
        
        serializer = serializer_class(page_obj, many=True, context={'request': request})
        
        return Response({
            'count': paginator.count,
            'total_pages': paginator.num_pages,
            'current_page': page_obj.number,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous(),
            'results': serializer.data
        })


class CachingMixin:
    """Mixin providing caching utilities for views."""
    
    cache_timeout = 300  # 5 minutes default
    
    def get_cached_data(self, cache_key: str, data_func, timeout: int = None):
        """Get data from cache or execute function and cache result."""
        data = cache.get(cache_key)
        if data is None:
            data = data_func()
            cache.set(cache_key, data, timeout or self.cache_timeout)
        return data
    
    def invalidate_cache(self, cache_pattern: str):
        """Invalidate cache entries matching pattern."""
        # Note: This is a simplified implementation
        # In production, consider using django-cache-machine or similar
        cache.delete(cache_pattern)


class OptimizedAssessmentViewSet(viewsets.ModelViewSet, OptimizedPaginationMixin, CachingMixin):
    """
    Optimized ViewSet for Assessment operations.
    
    Performance improvements:
    - Uses service layer for business logic
    - Optimized queries with select_related/prefetch_related
    - Strategic caching for frequently accessed data
    - Efficient pagination
    """
    
    serializer_class = AssessmentDetailSerializer
    permission_classes = [permissions.IsAuthenticated, CanTakeAssessments]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.assessment_service = AssessmentService()
        self.response_service = AssessmentResponseService()
        self.level_service = LevelManagementService()
    
    def get_queryset(self):
        """Get optimized queryset based on user permissions."""
        user = self.request.user
        
        # Use optimized QuerySet methods
        if user.is_staff or user.role in ['ADMIN', 'PROFESSOR']:
            # Admins and professors can see all assessments
            return (Assessment.objects
                   .optimized()  # Uses select_related and prefetch_related
                   .with_analytics())  # Adds computed fields
        else:
            # Students can only see their own assessments
            return (Assessment.objects
                   .for_student(user)
                   .optimized()
                   .with_analytics())
    
    def list(self, request):
        """List assessments with optimized pagination and filtering."""
        try:
            # Apply filters
            queryset = self.get_queryset()
            
            # Filter by type if provided
            assessment_type = request.query_params.get('type')
            if assessment_type:
                queryset = queryset.by_type(assessment_type)
            
            # Filter by status if provided
            status_filter = request.query_params.get('status')
            if status_filter:
                queryset = queryset.filter(status=status_filter)
            
            # Filter by completion if provided
            completed = request.query_params.get('completed')
            if completed is not None:
                is_completed = completed.lower() in ['true', '1', 'yes']
                queryset = queryset.filter(completed=is_completed)
            
            # Order by most recent first
            queryset = queryset.order_by('-created_at')
            
            return self.get_paginated_response(
                queryset, request, AssessmentSerializer
            )
            
        except Exception as e:
            logger.error(f"Error listing assessments: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve assessments'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def retrieve(self, request, pk=None):
        """Retrieve a single assessment with optimized data loading."""
        try:
            # Use service layer for permission checking and data retrieval
            assessment = self.assessment_service.get_by_id_or_raise(
                int(pk), user=request.user
            )
            
            # Use detailed serializer for single object
            serializer = AssessmentDetailSerializer(
                assessment, context={'request': request}
            )
            return Response(serializer.data)
            
        except NotFoundServiceError:
            return Response(
                {'error': 'Assessment not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except PermissionServiceError:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )
        except Exception as e:
            logger.error(f"Error retrieving assessment {pk}: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve assessment'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def create(self, request):
        """Create a new assessment with validation and business logic."""
        try:
            # Extract and validate data
            assessment_type = request.data.get('assessment_type', 'PLACEMENT')
            learning_path = request.data.get('learning_path', 'general')
            
            # Check for existing assessments to prevent duplicates
            existing = Assessment.objects.filter(
                student=request.user,
                assessment_type=assessment_type,
                status__in=['PENDING', 'IN_PROGRESS']
            ).first()
            
            if existing:
                return Response({
                    'error': f'You already have an active {assessment_type.lower()} assessment',
                    'assessment_id': existing.id,
                    'status': existing.status
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Use service layer to create assessment
            with transaction.atomic():
                assessment = self.assessment_service.create_with_transaction(
                    user=request.user,
                    student=request.user,
                    assessment_type=assessment_type,
                    learning_path=learning_path,
                    status='IN_PROGRESS',
                    start_time=timezone.now()
                )
                
                # Get questions for the assessment
                questions = AssessmentQuestion.objects.get_questions_for_assessment(
                    assessment_type=assessment_type,
                    learning_path=learning_path,
                    count=10
                )
                
                # Create response records for each question
                for question in questions:
                    AssessmentResponse.objects.create(
                        assessment=assessment,
                        question=question,
                        student=request.user
                    )
            
            # Return created assessment
            serializer = AssessmentDetailSerializer(
                assessment, context={'request': request}
            )
            return Response(serializer.data, status=status.HTTP_201_CREATED)
            
        except ValidationServiceError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Error creating assessment: {str(e)}")
            return Response(
                {'error': 'Failed to create assessment'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def submit_response(self, request, pk=None):
        """Submit a response to an assessment question."""
        try:
            assessment = self.get_object()
            question_id = request.data.get('question_id')
            answer_data = {
                'answer_text': request.data.get('answer_text', ''),
                'answer': request.data.get('answer', ''),
                'student_answer': request.data.get('student_answer'),
                'time_spent': request.data.get('time_spent', 0)
            }
            
            # Use service layer for response submission
            response = self.response_service.submit_response(
                assessment_id=assessment.id,
                question_id=question_id,
                student=request.user,
                answer_data=answer_data
            )
            
            serializer = AssessmentResponseSerializer(
                response, context={'request': request}
            )
            return Response(serializer.data, status=status.HTTP_201_CREATED)
            
        except ValidationServiceError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Error submitting response: {str(e)}")
            return Response(
                {'error': 'Failed to submit response'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def submit_assessment(self, request, pk=None):
        """Submit a complete assessment for evaluation."""
        try:
            assessment = self.get_object()
            
            # Validate that user owns the assessment
            if assessment.student != request.user:
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Check if already completed
            if assessment.completed:
                return Response(
                    {'error': 'Assessment already completed'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Process any remaining answers
            answers = request.data.get('answers', [])
            for answer in answers:
                try:
                    self.response_service.submit_response(
                        assessment_id=assessment.id,
                        question_id=answer.get('question_id'),
                        student=request.user,
                        answer_data=answer
                    )
                except Exception as e:
                    logger.warning(f"Error processing answer: {str(e)}")
                    continue
            
            # Submit the assessment using the optimized model method
            assessment.submit()
            
            # Get updated assessment data
            assessment.refresh_from_db()
            serializer = AssessmentDetailSerializer(
                assessment, context={'request': request}
            )
            
            return Response({
                'success': True,
                'message': 'Assessment submitted successfully',
                'assessment': serializer.data
            })
            
        except Exception as e:
            logger.error(f"Error submitting assessment: {str(e)}")
            return Response(
                {'error': 'Failed to submit assessment'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['get'])
    def results(self, request, pk=None):
        """Get detailed assessment results with analytics."""
        try:
            assessment = self.get_object()
            
            # Check if assessment is completed
            if not assessment.completed:
                return Response(
                    {'error': 'Assessment not completed'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Use caching for results since they don't change
            cache_key = f"assessment_results_{assessment.id}"
            
            def get_results_data():
                # Get assessment with all related data
                detailed_assessment = (Assessment.objects
                                     .select_related('student')
                                     .prefetch_related(
                                         Prefetch('responses',
                                                queryset=AssessmentResponse.objects
                                                .select_related('question')
                                                .order_by('id'))
                                     )
                                     .get(id=assessment.id))
                
                # Get analytics from service
                analytics = self.level_service.get_level_progression_analytics(
                    request.user
                )
                
                return {
                    'assessment': AssessmentDetailSerializer(
                        detailed_assessment, context={'request': request}
                    ).data,
                    'analytics': analytics
                }
            
            results_data = self.get_cached_data(cache_key, get_results_data, 3600)
            return Response(results_data)
            
        except Exception as e:
            logger.error(f"Error getting assessment results: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve results'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get assessment statistics for the current user."""
        try:
            # Use caching for statistics
            cache_key = f"user_assessment_stats_{request.user.id}"
            
            def get_stats_data():
                # Use optimized manager method
                return Assessment.objects.get_student_stats(request.user)
            
            stats = self.get_cached_data(cache_key, get_stats_data, 1800)
            return Response(stats)
            
        except Exception as e:
            logger.error(f"Error getting statistics: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve statistics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def questions(self, request):
        """Get questions for starting an assessment."""
        try:
            assessment_type = request.query_params.get('type', 'PLACEMENT')
            learning_path = request.query_params.get('learning_path', 'general')
            count = min(int(request.query_params.get('count', 10)), 50)
            
            # Use optimized question retrieval
            questions = AssessmentQuestion.objects.get_questions_for_assessment(
                assessment_type=assessment_type,
                learning_path=learning_path,
                count=count
            )
            
            serializer = AssessmentQuestionSerializer(
                questions, many=True, context={'request': request}
            )
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error getting questions: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve questions'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class OptimizedStudentLevelView(APIView, CachingMixin):
    """
    Optimized view for student level operations.
    
    Performance improvements:
    - Service layer integration
    - Efficient caching
    - Optimized queries
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.level_service = LevelManagementService()
    
    def get(self, request):
        """Get current student level with analytics."""
        try:
            user_id = request.query_params.get('user_id')
            target_user = request.user
            
            if user_id and (request.user.is_staff or request.user.role in ['ADMIN', 'PROFESSOR']):
                from users.models import CustomUser
                target_user = get_object_or_404(CustomUser, id=user_id)
            
            # Use caching for level data
            cache_key = f"student_level_analytics_{target_user.id}"
            
            def get_level_data():
                # Get level with analytics
                analytics = self.level_service.get_level_progression_analytics(target_user)
                
                # Get eligibility for advancement
                eligibility = self.level_service.check_level_advancement_eligibility(target_user)
                
                return {
                    'analytics': analytics,
                    'advancement_eligibility': eligibility
                }
            
            level_data = self.get_cached_data(cache_key, get_level_data, 1800)
            return Response(level_data)
            
        except Exception as e:
            logger.error(f"Error getting student level: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve student level'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class OptimizedAdminAssessmentStatsView(APIView, CachingMixin):
    """
    Optimized view for admin assessment statistics.
    
    Performance improvements:
    - Efficient aggregation queries
    - Strategic caching
    - Batch processing
    """
    
    permission_classes = [IsProfessorUser | IsAdminUser]
    
    def get(self, request):
        """Get comprehensive assessment statistics for admins."""
        try:
            # Use caching for expensive admin stats
            cache_key = "admin_assessment_stats"
            
            def get_admin_stats():
                from django.db.models import Count, Avg, Q
                
                # Get aggregated statistics efficiently
                stats = Assessment.objects.aggregate(
                    total_assessments=Count('id'),
                    completed_assessments=Count('id', filter=Q(completed=True)),
                    average_score=Avg('score', filter=Q(completed=True))
                )
                
                # Get level distribution
                level_distribution = (StudentLevel.objects
                                    .values('current_level')
                                    .annotate(count=Count('id'))
                                    .order_by('current_level'))
                
                # Get recent activity
                from datetime import timedelta
                recent_date = timezone.now() - timedelta(days=7)
                recent_activity = Assessment.objects.filter(
                    created_at__gte=recent_date
                ).count()
                
                return {
                    'overview': {
                        'total_assessments': stats['total_assessments'] or 0,
                        'completed_assessments': stats['completed_assessments'] or 0,
                        'completion_rate': (
                            (stats['completed_assessments'] / stats['total_assessments'] * 100)
                            if stats['total_assessments'] > 0 else 0
                        ),
                        'average_score': round(stats['average_score'] or 0, 2),
                        'recent_activity': recent_activity
                    },
                    'level_distribution': list(level_distribution),
                    'by_type': self._get_stats_by_type()
                }
            
            stats = self.get_cached_data(cache_key, get_admin_stats, 3600)
            return Response(stats)
            
        except Exception as e:
            logger.error(f"Error getting admin stats: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve statistics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _get_stats_by_type(self):
        """Get statistics broken down by assessment type."""
        from django.db.models import Count, Avg
        
        stats_by_type = {}
        for assessment_type, display_name in Assessment.ASSESSMENT_TYPES:
            type_stats = Assessment.objects.filter(
                assessment_type=assessment_type
            ).aggregate(
                count=Count('id'),
                completed=Count('id', filter=Q(completed=True)),
                avg_score=Avg('score', filter=Q(completed=True))
            )
            
            stats_by_type[assessment_type] = {
                'display_name': display_name,
                'total': type_stats['count'] or 0,
                'completed': type_stats['completed'] or 0,
                'average_score': round(type_stats['avg_score'] or 0, 2)
            }
        
        return stats_by_type


# Performance monitoring decorator
def monitor_performance(view_func):
    """Decorator to monitor view performance."""
    def wrapper(*args, **kwargs):
        import time
        start_time = time.time()
        
        try:
            result = view_func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # Log slow requests
            if execution_time > 2.0:  # 2 seconds threshold
                logger.warning(
                    f"Slow request: {view_func.__name__} took {execution_time:.2f}s"
                )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(
                f"Error in {view_func.__name__} after {execution_time:.2f}s: {str(e)}"
            )
            raise
    
    return wrapper


# Apply performance monitoring to all views
OptimizedAssessmentViewSet.list = monitor_performance(OptimizedAssessmentViewSet.list)
OptimizedAssessmentViewSet.retrieve = monitor_performance(OptimizedAssessmentViewSet.retrieve)
OptimizedStudentLevelView.get = monitor_performance(OptimizedStudentLevelView.get)

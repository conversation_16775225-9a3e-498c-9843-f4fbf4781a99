import apiClient from './apiClient';

// Types for plagiarism detection
interface PlagiarismProvider {
  id: string;
  name: 'turnitin' | 'copyleaks' | 'unicheck' | 'grammarly' | 'quetext' | 'custom';
  displayName: string;
  apiKey: string;
  baseUrl: string;
  features: string[];
  maxFileSize: number; // in MB
  supportedFormats: string[];
  isActive: boolean;
}

interface PlagiarismSettings {
  sensitivity: number; // 0-100
  excludeQuotes: boolean;
  excludeBibliography: boolean;
  excludeSmallMatches: boolean;
  minWordsMatch: number;
  internetSearch: boolean;
  institutionDatabase: boolean;
  publicationDatabase: boolean;
  studentPaperDatabase: boolean;
  excludeReferences: boolean;
  excludeFootnotes: boolean;
}

interface SubmissionData {
  studentId: string;
  studentName: string;
  assignmentId: string;
  assignmentTitle: string;
  courseId: string;
  content?: string;
  file?: File;
  fileName?: string;
  fileType?: string;
  metadata: {
    submissionDate: string;
    dueDate: string;
    wordCount?: number;
    pageCount?: number;
    language: string;
  };
}

interface PlagiarismReport {
  reportId: string;
  submissionId: string;
  providerId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'error';
  overallSimilarity: number;
  matches: PlagiarismMatch[];
  statistics: {
    totalWords: number;
    matchedWords: number;
    uniqueWords: number;
    excludedWords: number;
  };
  processingTime: number;
  generatedAt: string;
  downloadUrls: {
    fullReport: string;
    summary: string;
    originalityReport: string;
  };
  flags: string[];
  recommendations: string[];
}

interface PlagiarismMatch {
  id: string;
  source: {
    type: 'internet' | 'publication' | 'student_paper' | 'database';
    title: string;
    url?: string;
    author?: string;
    publicationDate?: string;
    database?: string;
  };
  similarity: number;
  matchedText: string;
  originalText: string;
  location: {
    startPosition: number;
    endPosition: number;
    page?: number;
    line?: number;
  };
  confidence: number;
  isExcluded: boolean;
  exclusionReason?: string;
}

interface PlagiarismAnalysis {
  submissionId: string;
  analysis: {
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    overallScore: number;
    contentAnalysis: {
      originalityPercentage: number;
      paraphrasedContent: number;
      quotedContent: number;
      referencedContent: number;
    };
    patterns: {
      suspiciousPatterns: string[];
      commonPhrases: string[];
      structuralSimilarities: string[];
    };
    recommendations: string[];
  };
}

export class PlagiarismDetectionService {
  private static instance: PlagiarismDetectionService;
  private providers = new Map<string, PlagiarismProvider>();
  private reportsCache = new Map<string, PlagiarismReport>();

  static getInstance(): PlagiarismDetectionService {
    if (!PlagiarismDetectionService.instance) {
      PlagiarismDetectionService.instance = new PlagiarismDetectionService();
    }
    return PlagiarismDetectionService.instance;
  }

  // Provider Management
  async addProvider(provider: PlagiarismProvider): Promise<{
    success: boolean;
    providerId: string;
    testResult?: any;
  }> {
    try {
      const response = await apiClient.post('/plagiarism/providers', provider);
      
      if (response.data.success) {
        this.providers.set(provider.id, provider);
      }
      
      return response.data;
    } catch (error) {
      console.error('Error adding plagiarism provider:', error);
      throw error;
    }
  }

  async getProviders(): Promise<PlagiarismProvider[]> {
    try {
      const response = await apiClient.get('/plagiarism/providers');
      const providers = response.data.providers;
      
      // Update local cache
      providers.forEach((provider: PlagiarismProvider) => {
        this.providers.set(provider.id, provider);
      });
      
      return providers;
    } catch (error) {
      console.error('Error fetching plagiarism providers:', error);
      throw error;
    }
  }

  async updateProviderSettings(providerId: string, settings: Partial<PlagiarismProvider>): Promise<boolean> {
    try {
      const response = await apiClient.put(`/plagiarism/providers/${providerId}`, settings);
      
      if (response.data.success && this.providers.has(providerId)) {
        const provider = this.providers.get(providerId)!;
        this.providers.set(providerId, { ...provider, ...settings });
      }
      
      return response.data.success;
    } catch (error) {
      console.error('Error updating provider settings:', error);
      throw error;
    }
  }

  // Submission and Analysis
  async submitForAnalysis(
    submission: SubmissionData,
    providerId: string,
    settings?: Partial<PlagiarismSettings>
  ): Promise<{
    submissionId: string;
    reportId: string;
    estimatedTime: number;
    status: string;
  }> {
    try {
      const formData = new FormData();
      
      // Add submission data
      formData.append('studentId', submission.studentId);
      formData.append('studentName', submission.studentName);
      formData.append('assignmentId', submission.assignmentId);
      formData.append('assignmentTitle', submission.assignmentTitle);
      formData.append('courseId', submission.courseId);
      formData.append('metadata', JSON.stringify(submission.metadata));
      formData.append('providerId', providerId);
      
      if (settings) {
        formData.append('settings', JSON.stringify(settings));
      }
      
      // Add content or file
      if (submission.file) {
        formData.append('file', submission.file);
        formData.append('fileName', submission.fileName || submission.file.name);
        formData.append('fileType', submission.fileType || submission.file.type);
      } else if (submission.content) {
        formData.append('content', submission.content);
      }

      const response = await apiClient.post('/plagiarism/submit', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error submitting for plagiarism analysis:', error);
      throw error;
    }
  }

  async getAnalysisStatus(submissionId: string): Promise<{
    status: string;
    progress: number;
    estimatedTimeRemaining: number;
    message?: string;
  }> {
    try {
      const response = await apiClient.get(`/plagiarism/status/${submissionId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching analysis status:', error);
      throw error;
    }
  }

  async getReport(reportId: string): Promise<PlagiarismReport> {
    // Check cache first
    if (this.reportsCache.has(reportId)) {
      return this.reportsCache.get(reportId)!;
    }

    try {
      const response = await apiClient.get(`/plagiarism/report/${reportId}`);
      const report = response.data;
      
      // Cache the report
      this.reportsCache.set(reportId, report);
      
      return report;
    } catch (error) {
      console.error('Error fetching plagiarism report:', error);
      throw error;
    }
  }

  async getDetailedAnalysis(submissionId: string): Promise<PlagiarismAnalysis> {
    try {
      const response = await apiClient.get(`/plagiarism/analysis/${submissionId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching detailed analysis:', error);
      throw error;
    }
  }

  // Batch Operations
  async submitBatchAnalysis(
    submissions: SubmissionData[],
    providerId: string,
    settings?: Partial<PlagiarismSettings>
  ): Promise<{
    batchId: string;
    submissionIds: string[];
    estimatedCompletionTime: string;
  }> {
    try {
      const response = await apiClient.post('/plagiarism/batch-submit', {
        submissions,
        providerId,
        settings
      });

      return response.data;
    } catch (error) {
      console.error('Error submitting batch analysis:', error);
      throw error;
    }
  }

  async getBatchStatus(batchId: string): Promise<{
    batchId: string;
    totalSubmissions: number;
    completedSubmissions: number;
    failedSubmissions: number;
    overallProgress: number;
    submissions: any[];
  }> {
    try {
      const response = await apiClient.get(`/plagiarism/batch-status/${batchId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching batch status:', error);
      throw error;
    }
  }

  // Report Management
  async downloadReport(reportId: string, format: 'pdf' | 'html' | 'json' = 'pdf'): Promise<{
    downloadUrl: string;
    fileName: string;
    expiresAt: string;
  }> {
    try {
      const response = await apiClient.get(`/plagiarism/report/${reportId}/download`, {
        params: { format }
      });

      return response.data;
    } catch (error) {
      console.error('Error downloading report:', error);
      throw error;
    }
  }

  async shareReport(reportId: string, recipients: string[], permissions: string[]): Promise<{
    shareId: string;
    shareUrl: string;
    expiresAt: string;
  }> {
    try {
      const response = await apiClient.post(`/plagiarism/report/${reportId}/share`, {
        recipients,
        permissions
      });

      return response.data;
    } catch (error) {
      console.error('Error sharing report:', error);
      throw error;
    }
  }

  // Analytics and Statistics
  async getInstructorAnalytics(instructorId: string, timeRange: string = '30_days'): Promise<{
    totalSubmissions: number;
    averageSimilarity: number;
    flaggedSubmissions: number;
    trends: any[];
    topSources: any[];
    riskDistribution: any;
  }> {
    try {
      const response = await apiClient.get(`/plagiarism/analytics/instructor/${instructorId}`, {
        params: { timeRange }
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching instructor analytics:', error);
      throw error;
    }
  }

  async getCourseAnalytics(courseId: string): Promise<{
    courseId: string;
    submissionStats: any;
    similarityDistribution: any[];
    commonSources: any[];
    recommendations: string[];
  }> {
    try {
      const response = await apiClient.get(`/plagiarism/analytics/course/${courseId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching course analytics:', error);
      throw error;
    }
  }

  async getInstitutionReport(): Promise<{
    totalSubmissions: number;
    averageSimilarity: number;
    flaggedSubmissions: number;
    providerUsage: any[];
    trends: any[];
    riskAnalysis: any;
  }> {
    try {
      const response = await apiClient.get('/plagiarism/analytics/institution');
      return response.data;
    } catch (error) {
      console.error('Error fetching institution report:', error);
      throw error;
    }
  }

  // Exclusion Management
  async addGlobalExclusion(exclusion: {
    type: 'text' | 'source' | 'pattern';
    value: string;
    reason: string;
    scope: 'institution' | 'course' | 'assignment';
    scopeId?: string;
  }): Promise<boolean> {
    try {
      const response = await apiClient.post('/plagiarism/exclusions', exclusion);
      return response.data.success;
    } catch (error) {
      console.error('Error adding global exclusion:', error);
      throw error;
    }
  }

  async getExclusions(scope?: string, scopeId?: string): Promise<any[]> {
    try {
      const response = await apiClient.get('/plagiarism/exclusions', {
        params: { scope, scopeId }
      });

      return response.data.exclusions;
    } catch (error) {
      console.error('Error fetching exclusions:', error);
      throw error;
    }
  }

  // Utility Methods
  async testProviderConnection(providerId: string): Promise<{
    success: boolean;
    responseTime: number;
    features: string[];
    message?: string;
  }> {
    try {
      const response = await apiClient.post(`/plagiarism/providers/${providerId}/test`);
      return response.data;
    } catch (error) {
      console.error('Error testing provider connection:', error);
      throw error;
    }
  }

  async validateSubmission(submission: SubmissionData): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
    suggestions: string[];
  }> {
    try {
      const response = await apiClient.post('/plagiarism/validate', submission);
      return response.data;
    } catch (error) {
      console.error('Error validating submission:', error);
      throw error;
    }
  }

  // Cache Management
  clearCache(): void {
    this.reportsCache.clear();
  }

  // Real-time Updates
  async subscribeToUpdates(submissionId: string, callback: (update: any) => void): Promise<() => void> {
    try {
      const response = await apiClient.post(`/plagiarism/subscribe/${submissionId}`);
      const subscriptionId = response.data.subscriptionId;

      // Set up WebSocket or polling for real-time updates
      const eventSource = new EventSource(`/api/plagiarism/updates/${subscriptionId}`);
      
      eventSource.onmessage = (event) => {
        const update = JSON.parse(event.data);
        callback(update);
      };

      // Return unsubscribe function
      return () => {
        eventSource.close();
        apiClient.delete(`/plagiarism/subscribe/${subscriptionId}`);
      };
    } catch (error) {
      console.error('Error subscribing to updates:', error);
      throw error;
    }
  }
}

export const plagiarismDetectionService = PlagiarismDetectionService.getInstance();

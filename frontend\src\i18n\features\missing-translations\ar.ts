/**
 * Missing Arabic translations that need to be added to fix console errors
 */

export const missingArTranslations = {
  // Direction for RTL support
  direction: 'rtl',

  // Service Worker translations
  serviceWorker: {
    updateNow: 'تحديث الآن',
    newVersion: 'إصدار جديد متاح',
  },

  // Home page translations (missing keys)
  home: {
    title: 'مسار',
    subtitle: 'مرحبًا بك في',
    tagline: 'مستقبل التعليم',
    description:
      'يجمع مسار بين التكنولوجيا المتطورة وأساليب التدريس المبتكرة لتوفير تجربة تعليمية مخصصة تعدك لوظائف المستقبل.',
    getStarted: 'ابدأ الآن',
    learnMore: 'اعرف المزيد',
    scrollDown: 'مرر لأسفل',
    aboutSection: {
      whoWeAre: 'من نحن',
      aboutUniversity: 'عن مسار',
      description:
        'مسار هي منصة تعليمية حديثة تجمع بين التميز الأكاديمي التقليدي ونهج التعلم الرقمي المبتكر. نركز على تطوير المهارات العملية مع توفير أساس نظري متين.',
      theory: 'نظرية',
      practice: 'تطبيق',
      aiPowered: 'مدعوم بالذكاء الاصطناعي',
      fullyDigital: 'رقمي بالكامل',
      ourLearningApproach: 'نهجنا التعليمي',
      established: 'تأسست عام 2020',
      motto: 'تعلم، طبق، ابتكر',
    },
    whySection: {
      whyNorthStar: 'لماذا مسار',
      whatSetsUsApart: 'ما يميزنا',
      description:
        'في مسار، نعيد تصور التعليم العالي للعصر الرقمي. يجمع نهجنا المبتكر بين التكنولوجيا المتطورة وتجارب التعلم المخصصة لإعدادك للنجاح في عالم سريع التغير.',
      aiPersonalized: 'تعلم مخصص بالذكاء الاصطناعي',
      aiPersonalizedDesc:
        'تتكيف منصتنا المدعومة بالذكاء الاصطناعي مع أسلوب تعلمك ووتيرتك وأهدافك لإنشاء تجربة تعليمية مخصصة حقًا.',
      practicalExperiences: 'تجارب عملية',
      practicalExperiencesDesc:
        'تعلم من خلال الممارسة مع مشاريع عملية ومحاكاة وتطبيقات واقعية تبني المهارات العملية التي يقدرها أصحاب العمل.',
      launchProject: 'إطلاق المشروع',
      launchProjectDesc:
        'يتوج كل برنامج بمشروع تطبيقي حيث ستطبق مهاراتك لحل مشكلات العالم الحقيقي.',
      accredited: 'معتمدة بالكامل',
      accreditedDesc:
        'تلبي برامجنا معايير أكاديمية صارمة ومعترف بها من قبل شركاء الصناعة الرائدين.',
      digitalEnvironment: 'بيئة رقمية',
      digitalEnvironmentDesc:
        'الوصول إلى تعليم عالمي المستوى من أي مكان مع حرمنا الجامعي الرقمي بالكامل وموارد التعلم.',
    },
  },

  // Settings translations
  settings: {
    language: 'اللغة',
    siteName: 'اسم الموقع',
    contactEmail: 'البريد الإلكتروني للتواصل',
    systemOptions: 'خيارات النظام',
    enableUserRegistration: 'تفعيل تسجيل المستخدمين',
    enableSystemNotifications: 'تفعيل إشعارات النظام',
    maintenanceMode: 'وضع الصيانة',
  },

  // Settings Advanced translations
  settingsAdvanced: {
    general: 'عام',
  },

  // Discover translations
  discover: {
    title: 'اكتشف مسارك',
    programming: 'البرمجة',
    cybersecurity: 'الأمن السيبراني',
    finance: 'التمويل',
    marketing: 'التسويق',
    exploreOptions: 'استكشف خياراتك',
    ourSpecializations: 'مسارات التخصص لدينا',
    mainDescription:
      'اختر من مساراتنا التعليمية المتخصصة الأربعة: البرمجة، الأمن السيبراني، التمويل، والتسويق. يقدم كل مسار منهجًا شاملاً مع مشاريع عملية وتوجيه مخصص لإعدادك لمهنة ناجحة.',
    viewAll: 'عرض جميع التخصصات',
    paths: {
      programming: {
        description:
          'تعلم أساسيات البرمجة وتطوير البرمجيات لبناء تطبيقات الويب والجوال والذكاء الاصطناعي.',
      },
      cybersecurity: {
        description:
          'تعلم كيفية حماية الأنظمة والشبكات والبيانات من التهديدات السيبرانية والهجمات.',
      },
      finance: {
        description:
          'استكشف عالم التمويل والاستثمار والأسواق المالية والتخطيط المالي.',
      },
      marketing: {
        description:
          'تعلم استراتيجيات التسويق الرقمي ووسائل التواصل الاجتماعي وتسويق المحتوى وتحليلات التسويق.',
      },
    },
  },

  // Specializations translations
  specializations: {
    common: {
      skills: 'المهارات',
      learnMore: 'اعرف المزيد',
      featuredCourses: 'الدورات المميزة',
      applyNow: 'سجل الآن',
      averageSalaryRange: 'متوسط نطاق الراتب',
      level: {
        beginner: 'مبتدئ',
        intermediate: 'متوسط',
        advanced: 'متقدم',
      },
      weeks: 'أسابيع',
    },
    programming: {
      title: 'البرمجة',
      subtitle: 'ابنِ مستقبلك في عالم التكنولوجيا',
      description:
        'تعلم أساسيات البرمجة وتطوير البرمجيات لبناء تطبيقات الويب والجوال والذكاء الاصطناعي.',
      overview: 'نظرة عامة على البرنامج',
      overviewDescription:
        'برنامج البرمجة الشامل مصمم لتزويدك بالمهارات التقنية والعملية اللازمة للنجاح في صناعة التكنولوجيا. من أساسيات البرمجة إلى التطبيقات المتقدمة، ستتعلم كيفية بناء حلول برمجية مبتكرة.',
      programHighlights: 'مميزات البرنامج',
      highlightsList: [
        'تعلم لغات البرمجة الحديثة والمطلوبة في السوق',
        'مشاريع عملية تطبق ما تعلمته',
        'إرشاد من خبراء الصناعة',
        'شهادات معتمدة من الشركات الرائدة',
        'فرص تدريب في شركات التكنولوجيا',
      ],
      careerOpportunities: 'الفرص المهنية',
      careerOpportunitiesDescription:
        'خريجو برنامج البرمجة يحصلون على فرص عمل متنوعة في مختلف القطاعات التقنية.',
      averageSalaryRange: 'متوسط الراتب',
      learningApproach: 'منهجية التعلم',
      learningApproachDescription:
        'نتبع منهجية تعلم تفاعلية تجمع بين النظرية والتطبيق العملي لضمان فهم عميق للمفاهيم البرمجية.',
      learningMethods: [
        'محاضرات تفاعلية مع أمثلة عملية',
        'مشاريع جماعية لتطوير مهارات العمل الجماعي',
        'ورش عمل متخصصة في أحدث التقنيات',
        'تقييمات مستمرة لضمان التقدم',
        'مشاريع تخرج حقيقية مع شركات',
      ],
      collaborativeLearning: {
        title: 'التعلم التعاوني',
        description: 'تعلم مع زملائك في بيئة تعاونية محفزة',
        altText: 'طلاب يتعلمون البرمجة معاً',
      },
      readyToStart: 'هل أنت مستعد لبدء رحلتك البرمجية؟',
      readyToStartDescription:
        'انضم إلى آلاف الطلاب الذين بدأوا رحلتهم في عالم البرمجة وحققوا أحلامهم المهنية.',
      courses: {
        fullStackWebDev: {
          title: 'تطوير الويب الشامل',
          description:
            'تعلم بناء تطبيقات ويب كاملة باستخدام أحدث التقنيات والأدوات.',
          skills: [
            'React',
            'Node.js',
            'MongoDB',
            'Express.js',
            'HTML/CSS',
            'JavaScript',
          ],
        },
        mobileAppDev: {
          title: 'تطوير تطبيقات الجوال',
          description: 'اكتشف كيفية بناء تطبيقات جوال أصلية ومتعددة المنصات.',
          skills: [
            'React Native',
            'Flutter',
            'iOS',
            'Android',
            'Firebase',
            'API Integration',
          ],
        },
        dataStructures: {
          title: 'هياكل البيانات والخوارزميات',
          description:
            'أتقن المفاهيم الأساسية لهياكل البيانات والخوارزميات لحل المشكلات المعقدة.',
          skills: [
            'Arrays',
            'Linked Lists',
            'Trees',
            'Graphs',
            'Sorting',
            'Searching',
          ],
        },
        cloudComputing: {
          title: 'الحوسبة السحابية',
          description: 'تعلم كيفية نشر وإدارة التطبيقات في البيئة السحابية.',
          skills: [
            'AWS',
            'Azure',
            'Docker',
            'Kubernetes',
            'DevOps',
            'Microservices',
          ],
        },
        aiMachineLearning: {
          title: 'الذكاء الاصطناعي والتعلم الآلي',
          description:
            'اكتشف عالم الذكاء الاصطناعي وتعلم بناء نماذج التعلم الآلي.',
          skills: [
            'Python',
            'TensorFlow',
            'PyTorch',
            'Data Science',
            'Neural Networks',
            'Deep Learning',
          ],
        },
        softwareArchitecture: {
          title: 'هندسة البرمجيات',
          description: 'تعلم تصميم وبناء أنظمة برمجية قابلة للتطوير والصيانة.',
          skills: [
            'Design Patterns',
            'System Design',
            'Microservices',
            'Clean Code',
            'Testing',
            'Documentation',
          ],
        },
      },
      careers: {
        fullStack: {
          title: 'مطور ويب شامل',
          description:
            'تطوير تطبيقات ويب كاملة من الواجهة الأمامية إلى الخلفية.',
          salary: '$70,000 - $120,000',
        },
        mobileDev: {
          title: 'مطور تطبيقات جوال',
          description: 'بناء تطبيقات جوال مبتكرة لأنظمة iOS و Android.',
          salary: '$75,000 - $130,000',
        },
        cloudEngineer: {
          title: 'مهندس حوسبة سحابية',
          description: 'تصميم وإدارة البنية التحتية السحابية للشركات.',
          salary: '$90,000 - $150,000',
        },
        mlEngineer: {
          title: 'مهندس تعلم آلي',
          description: 'تطوير وتطبيق نماذج الذكاء الاصطناعي والتعلم الآلي.',
          salary: '$100,000 - $180,000',
        },
        dba: {
          title: 'مدير قواعد البيانات',
          description: 'إدارة وتحسين قواعد البيانات لضمان الأداء الأمثل.',
          salary: '$80,000 - $140,000',
        },
        architect: {
          title: 'مهندس معماري للبرمجيات',
          description: 'تصميم الهيكل العام للأنظمة البرمجية المعقدة.',
          salary: '$120,000 - $200,000',
        },
      },
    },
    cybersecurity: {
      title: 'تخصص الأمن السيبراني',
      subtitle: 'احمِ العالم الرقمي',
      description:
        'أتقن فن الأمن السيبراني وكن حارسًا للأصول الرقمية. تعلم كيفية تحديد ومنع والاستجابة للتهديدات السيبرانية في برنامجنا الشامل للأمن السيبراني.',
      overview: 'نظرة عامة على البرنامج',
      overviewDescription:
        'يوفر تخصص الأمن السيبراني لدينا تدريبًا شاملاً في حماية الأصول الرقمية والشبكات والأنظمة من التهديدات السيبرانية. ستتعلم تقنيات الأمان الدفاعية والهجومية، مما يعدك لمهنة ناجحة في الأمن السيبراني.',
      programHighlights: 'أبرز نقاط البرنامج',
      highlightsList: [
        'خبرة عملية مع أدوات الأمان المعيارية في الصناعة',
        'بيئات محاكاة واقعية للتعلم العملي',
        'تعليم من خبراء معتمدين في الأمان',
        'إعداد لشهادات الصناعة (CISSP، CEH، Security+)',
        'مشروع تخرج يتضمن تقييم أمني شامل',
      ],
      careerOpportunities: 'الفرص المهنية',
      careerOpportunitiesDescription:
        'محترفو الأمن السيبراني مطلوبون بشدة في جميع الصناعات. خريجونا يعملون في أدوار مختلفة لحماية المنظمات من التهديدات السيبرانية.',
      learningApproach: 'نهج التعلم',
      learningApproachDescription:
        'يجمع برنامج الأمن السيبراني لدينا بين المعرفة النظرية والخبرة العملية في بيئات محاكاة.',
      learningMethods: [
        'مختبرات ومحاكاة تفاعلية',
        'دراسات حالة واقعية',
        'مسابقات Capture the Flag (CTF)',
        'متحدثون ضيوف من الصناعة',
        'مشاريع جماعية تعاونية',
      ],
      readyToStart: 'مستعد لبدء رحلة الأمن السيبراني؟',
      joinDescription:
        'انضم إلى برنامج الأمن السيبراني لدينا وكن جزءًا من الجيل القادم من المدافعين الرقميين.',
      courses: {
        networkSecurity: {
          title: 'أساسيات أمن الشبكات',
          description:
            'تعلم كيفية تأمين البنية التحتية للشبكات والحماية من الهجمات المبنية على الشبكة.',
          skills: [
            'تكوين جدار الحماية',
            'إعداد VPN',
            'مراقبة الشبكة',
            'كشف التسلل',
          ],
        },
        ethicalHacking: {
          title: 'الاختراق الأخلاقي واختبار الاختراق',
          description:
            'أتقن التقنيات المستخدمة من قبل المخترقين الأخلاقيين لتحديد واستغلال الثغرات.',
          skills: [
            'تقييم الثغرات',
            'اختبار الاختراق',
            'الهندسة الاجتماعية',
            'أمان تطبيقات الويب',
          ],
        },
        securityOperations: {
          title: 'مركز عمليات الأمان (SOC)',
          description:
            'تعلم كيفية مراقبة وكشف والاستجابة لحوادث الأمان في الوقت الفعلي.',
          skills: [
            'أدوات SIEM',
            'الاستجابة للحوادث',
            'صيد التهديدات',
            'مراقبة الأمان',
          ],
        },
        securityGovernance: {
          title: 'حوكمة الأمان والامتثال',
          description: 'فهم أطر الأمان والسياسات ومتطلبات الامتثال التنظيمي.',
          skills: [
            'تقييم المخاطر',
            'تطوير السياسات',
            'تدقيق الامتثال',
            'أطر الأمان',
          ],
        },
        secureDevelopment: {
          title: 'تطوير البرمجيات الآمنة',
          description:
            'تعلم كيفية بناء تطبيقات آمنة وتنفيذ الأمان طوال دورة حياة التطوير.',
          skills: [
            'البرمجة الآمنة',
            'مراجعة الكود',
            'أمان التطبيقات',
            'DevSecOps',
          ],
        },
        digitalForensics: {
          title: 'الطب الشرعي الرقمي والاستجابة للحوادث',
          description:
            'أتقن تقنيات التحقيق في الجرائم السيبرانية والاستجابة لحوادث الأمان.',
          skills: [
            'الأدلة الرقمية',
            'التحليل الشرعي',
            'التحقيق في الحوادث',
            'سلسلة الحفظ',
          ],
        },
      },
      careers: {
        securityAnalyst: {
          title: 'محلل أمني',
          description:
            'مراقبة وتحليل أحداث الأمان، والتحقيق في الحوادث، وتنفيذ تدابير الأمان.',
        },
        penetrationTester: {
          title: 'مختبر اختراق',
          description:
            'إجراء هجمات محاكاة مصرح بها لتحديد الثغرات في الأنظمة والشبكات.',
        },
        securityEngineer: {
          title: 'مهندس أمني',
          description:
            'تصميم وتنفيذ حلول الأمان، وتكوين أدوات الأمان، وصيانة البنية التحتية للأمان.',
        },
        incidentResponder: {
          title: 'أخصائي الاستجابة للحوادث',
          description:
            'قيادة جهود الاستجابة أثناء حوادث الأمان، وإجراء التحليل الشرعي، وتطوير إجراءات الاستجابة.',
        },
        securityConsultant: {
          title: 'مستشار أمني',
          description:
            'تقديم مشورة أمنية خبيرة للمنظمات، وإجراء تقييمات أمنية، وتطوير استراتيجيات الأمان.',
        },
        ciso: {
          title: 'كبير مسؤولي أمن المعلومات (CISO)',
          description:
            'قيادة استراتيجية الأمان التنظيمي، وإدارة فرق الأمان، وضمان الامتثال لسياسات الأمان.',
        },
      },
    },
    finance: {
      title: 'تخصص التمويل',
      subtitle: 'أتقن عالم التمويل',
      description:
        'اغمر نفسك في عالم التمويل وتعلم اتخاذ القرارات المالية الاستراتيجية. يغطي برنامج التمويل الشامل لدينا كل شيء من الأسواق المالية إلى تمويل الشركات وابتكارات التكنولوجيا المالية.',
      overview: 'نظرة عامة على البرنامج',
      overviewDescription:
        'يوفر تخصص التمويل لدينا تدريبًا شاملاً في التحليل المالي وإدارة الاستثمار والتخطيط المالي. ستتعلم تحليل الأسواق وإدارة المخاطر واتخاذ القرارات المالية الاستراتيجية التي تدفع نجاح الأعمال.',
      programHighlights: 'أبرز نقاط البرنامج',
      highlightsList: [
        'النمذجة والتحليل المالي في العالم الحقيقي',
        'الوصول إلى البرامج والأدوات المالية المهنية',
        'دراسات حالة من المؤسسات المالية الرائدة',
        'إعداد لشهادات CFA و FRM',
        'مشروع تخرج مع إدارة محفظة استثمارية فعلية',
      ],
      careerOpportunities: 'الفرص المهنية',
      careerOpportunitiesDescription:
        'المهنيون الماليون ضروريون في كل صناعة. خريجونا يعملون في الخدمات المصرفية الاستثمارية وتمويل الشركات والتكنولوجيا المالية والاستشارات المالية.',
      learningApproach: 'نهج التعلم',
      learningApproachDescription:
        'يجمع برنامج التمويل لدينا بين المعرفة النظرية والتطبيق العملي باستخدام بيانات السوق الحقيقية والأدوات المالية.',
      learningMethods: [
        'ورش عمل النمذجة المالية',
        'تمارين محاكاة السوق',
        'محاضرات ضيوف من خبراء الصناعة',
        'مشاريع إدارة المحافظ',
        'تحليل دراسات الحالة المالية',
      ],
      readyToStart: 'مستعد لبدء رحلة التمويل؟',
      joinDescription:
        'انضم إلى برنامج التمويل لدينا وكن جزءًا من الجيل القادم من القادة الماليين.',
      courses: {
        financialMarkets: {
          title: 'الأسواق والأدوات المالية',
          description:
            'فهم كيفية عمل الأسواق المالية وتعلم الأدوات المالية المختلفة.',
          skills: [
            'تحليل السوق',
            'استراتيجيات التداول',
            'تقييم المخاطر',
            'نظرية المحفظة',
          ],
        },
        investmentManagement: {
          title: 'إدارة الاستثمار',
          description:
            'تعلم بناء وإدارة محافظ الاستثمار للحصول على عوائد مثلى.',
          skills: [
            'إدارة المحافظ',
            'توزيع الأصول',
            'تحليل الأداء',
            'بحوث الاستثمار',
          ],
        },
        corporateFinance: {
          title: 'تمويل الشركات',
          description:
            'أتقن اتخاذ القرارات المالية للشركات وهيكل رأس المال والتقييم.',
          skills: [
            'التخطيط المالي',
            'ميزانية رأس المال',
            'طرق التقييم',
            'عمليات الدمج والاستحواذ',
          ],
        },
        fintech: {
          title: 'التكنولوجيا المالية (FinTech)',
          description: 'استكشف تقاطع التمويل والتكنولوجيا في العصر الرقمي.',
          skills: [
            'تقنية البلوك تشين',
            'المدفوعات الرقمية',
            'المستشارون الآليون',
            'العملات المشفرة',
          ],
        },
      },
      careers: {
        financialAnalyst: {
          title: 'محلل مالي',
          description:
            'تحليل البيانات المالية واتجاهات السوق لمساعدة الشركات في اتخاذ قرارات الاستثمار.',
        },
        investmentManager: {
          title: 'مدير استثمار',
          description:
            'إدارة محافظ الاستثمار للأفراد أو المؤسسات لتحقيق الأهداف المالية.',
        },
        corporateFinance: {
          title: 'مدير تمويل الشركات',
          description:
            'الإشراف على التخطيط المالي وميزانية رأس المال وإدارة المخاطر المالية للشركات.',
        },
        riskManager: {
          title: 'مدير المخاطر',
          description: 'تحديد وتحليل وتخفيف المخاطر المالية للمنظمات.',
        },
        fintechSpecialist: {
          title: 'أخصائي التكنولوجيا المالية',
          description: 'تطوير وتنفيذ حلول التكنولوجيا المالية المبتكرة.',
        },
      },
    },
  },

  // Learning Journey translations
  learningJourney: {
    title: 'رحلة التعلم',
    subtitle: 'مسارك نحو النجاح',
    description:
      'يرشدك نهج التعلم المنظم لدينا عبر كل خطوة من رحلتك التعليمية، من اكتشاف اهتماماتك إلى بناء مشاريع واقعية.',
    steps: {
      0: {
        title: 'اكتشف مسارك',
        description:
          'أجرِ تقييمات لتحديد نقاط قوتك واهتماماتك. يساعدك نظامنا المدعوم بالذكاء الاصطناعي في العثور على التخصص المثالي.',
      },
      1: {
        title: 'تعلم',
        description:
          'شارك في محتوى تفاعلي وجلسات يقودها خبراء. يجمع منهجنا بين النظرية والأمثلة العملية.',
      },
      2: {
        title: 'طبق في الممارسة',
        description:
          'اعمل على محاكاة واقعية وتمارين عملية. طبق معرفتك لحل مشكلات حقيقية.',
      },
      3: {
        title: 'ابنِ مشروعك الخاص',
        description:
          'أنشئ مشروعًا نهائيًا يعرض مهاراتك. اعمل مع المرشدين لتطوير مشروع واقعي.',
      },
    },
  },

  // Auth translations (additional ones that might be missing)
  auth: {
    signIn: 'تسجيل الدخول',
    signUp: 'إنشاء حساب',
    createAccount: 'إنشاء حساب',
    dontHaveAccount: 'ليس لديك حساب؟',
    startYourJourney: 'ابدأ رحلتك',
    forgotPassword: 'نسيت كلمة المرور؟',
    resetPassword: 'إعادة تعيين كلمة المرور',
    alreadyHaveAccount: 'لديك حساب بالفعل؟',
    rememberMe: 'تذكرني',
    registration: {
      welcome: 'مرحبًا بك في مسار',
      askPassword: 'أنشئ كلمة مرور آمنة',
      askLastName: 'ما هو اسم العائلة؟',
      askEmail: 'ما هو عنوان بريدك الإلكتروني؟',
      askUsername: 'ما هو اسم المستخدم الذي تريده؟',
      emailFormat: 'يرجى إدخال عنوان بريد إلكتروني صحيح',
      firstNamePlaceholder: 'أدخل اسمك الأول',
      lastNamePlaceholder: 'أدخل اسم العائلة',
      emailPlaceholder: 'أدخل عنوان بريدك الإلكتروني',
      usernamePlaceholder: 'اختر اسم مستخدم',
      passwordPlaceholder: 'أنشئ كلمة مرور',
      confirmPasswordPlaceholder: 'أكد كلمة المرور',
      agreeToTerms: 'أوافق على شروط الخدمة وسياسة الخصوصية',
      createAccount: 'إنشاء حساب',
      alreadyHaveAccount: 'لديك حساب بالفعل؟ سجل دخولك',
      suggestedUsername: 'اسم المستخدم المقترح',
      usernameLength: 'يجب أن يكون اسم المستخدم مكونًا من 3 أحرف على الأقل',
    },
  },

  // Common translations that are frequently missing
  common: {
    email: 'البريد الإلكتروني',
    password: 'كلمة المرور',
    login: 'تسجيل الدخول',
    register: 'التسجيل',
    logout: 'تسجيل الخروج',
    submit: 'إرسال',
    save: 'حفظ',
    cancel: 'إلغاء',
    delete: 'حذف',
    edit: 'تعديل',
    view: 'عرض',
    add: 'إضافة',
    remove: 'إزالة',
    update: 'تحديث',
    create: 'إنشاء',
    search: 'بحث',
    loading: 'جاري التحميل...',
    back: 'رجوع',
    next: 'التالي',
    previous: 'السابق',
    continue: 'استمرار',
    finish: 'إنهاء',
    yes: 'نعم',
    no: 'لا',
    ok: 'موافق',
    gotIt: 'فهمت!',
    dashboard: 'لوحة التحكم',
    courses: 'المقررات',
    course: 'المقرر',
    students: 'الطلاب',
    professors: 'الأساتذة',
    settings: 'الإعدادات',
    university: 'مسار',
    copyright: '© 2025 مسار. جميع الحقوق محفوظة.',
    learnMore: 'اعرف المزيد',
    refresh: 'تحديث',
    actions: 'إجراءات',
    inactive: 'غير نشط',
    viewDetails: 'عرض التفاصيل',
    for: 'لـ',
    loadingCourses: 'جاري تحميل الدورات',
    loadingDescription: 'نحن نسترجع دوراتك. هذا سيستغرق لحظة فقط.',
    confirmDelete: 'تأكيد الحذف',
    lastUpdated: 'آخر تحديث',
    justNow: 'الآن',
    saveSettings: 'حفظ الإعدادات',
    timeAgo: {
      minutes: 'منذ {{count}} دقيقة',
      hours: 'منذ {{count}} ساعة',
      days: 'منذ {{count}} يوم',
    },
  },

  // Navigation translations
  navigation: {
    home: 'الرئيسية',
    dashboard: 'لوحة التحكم',
    courses: 'المقررات',
    students: 'الطلاب',
    professors: 'الأساتذة',
    settings: 'الإعدادات',
    admin: 'المسؤول',
    interactiveLearning: 'التعلم التفاعلي',
    users: 'المستخدمين',
    assessmentQuestions: 'أسئلة التقييم',
    studentLevels: 'مستويات الطلاب',
    backToCourse: 'العودة إلى المقرر',
    assignments: 'الواجبات',
    attendance: 'الحضور',
    studentCourses: 'مقرراتي',
    learningInsights: 'رؤى التعلم',
    grades: 'الدرجات',
    apiKey: {
      management: 'إدارة مفتاح API'
    }
  },

  // Admin dashboard translations
  'admin.dashboard': {
    title: 'لوحة تحكم المسؤول',
    overview: 'نظرة عامة',
    statistics: 'الإحصائيات',
    users: 'المستخدمين',
    courses: 'المقررات',
    settings: 'الإعدادات',
  },

  // Assessment translations
  assessment: {
    title: 'التقييم',
    findYourPath: 'اعثر على مسار التعلم المثالي لك',
    description:
      'أجب على بعض الأسئلة حول اهتماماتك ومهاراتك وأهدافك المهنية لاكتشاف التخصص الأنسب لك.',
    startNow: 'ابدأ التقييم',
    recommendedPath: 'مسار التعلم المثالي لك',
    recommendationExplanation: 'بناءً على إجاباتك، نوصي بمسار التعلم التالي:',
    analyzingAnswers: 'جاري تحليل إجاباتك...',
    personalizedPath: 'مسار شخصي',
    handsOnProjects: 'مشاريع عملية',
    expertGuidance: 'إرشاد الخبراء',
    areasToDevelop: 'مجالات للتطوير',
    alternativePath: 'مسار بديل للاستكشاف',
    explorePath: 'استكشف هذا المسار',
    resultsSaved: 'تم حفظ نتائجك',
    takeAgain: 'أعد التقييم',
    questions: 'الأسئلة',
    answers: 'الإجابات',
    results: 'النتائج',
    score: 'النتيجة',
    grade: 'الدرجة',
    submit: 'إرسال',
    review: 'مراجعة',
    preview: 'معاينة',
    levelMapping: 'تخطيط المستويات',

    // AI Assessment Analysis translations
    aiAssessmentAnalysis: 'تحليل التقييم بالذكاء الاصطناعي',
    clearCache: 'مسح التخزين المؤقت',
    assessmentAnalysis: 'تحليل التقييم',
    skillGapAnalysis: 'تحليل فجوة المهارات',
    backToStudents: 'العودة للطلاب',
    studentId: 'معرف الطالب',
    currentLevelAdminView: 'المستوى الحالي (عرض الإدارة)',
    adminAssessmentAnalysis: 'تحليل التقييم للإدارة',
    adminSkillGapAnalysis: 'تحليل فجوة المهارات للإدارة',
    analyzingStudentSkills: 'تحليل مهارات الطالب',
    aiAnalyzingSkillsDescription:
      'يقوم الذكاء الاصطناعي بتحليل مهارات الطالب الحالية',
    aiPoweredAnalysis: 'تحليل مدعوم بالذكاء الاصطناعي',
    aiAnalysisTime: 'وقت التحليل بالذكاء الاصطناعي',
    noQuestionsAvailable: 'لا توجد أسئلة متاحة',
    noCompletedAssessments: 'لا توجد تقييمات مكتملة',
    studentNeedsToCompleteAssessment: 'يحتاج الطالب إلى إكمال التقييم',
  },

  // Gamification translations
  gamification: {
    level: 'المستوى',
  },

  // API Key Management translations
  apiKey: {
    management: 'إدارة مفاتيح API',
    monitoringTitle: 'مراقبة مفتاح API',
    monitoringNoResult: 'لا توجد نتائج مراقبة متاحة.',
    forceAlert: 'فرض التنبيه',
    close: 'إغلاق',
    updateTitle: 'لوحة تحكم إدارة مفتاح API',
    explanation:
      'قم بإدارة مفتاح Google Gemini API الخاص بك ومراقبة إحصائيات الاستخدام.',
    loadingConfig: 'جاري تحميل تكوين النموذج...',
    adminInfo: 'معلومات المدير',
    refresh: 'تحديث',
    currentStatus: 'حالة مفتاح API الحالية',
    monitor: 'مراقبة وتنبيه',
    tokenUsage: 'استخدام الرموز (آخر 7 أيام)',
    modelNote: 'جميع الصفحات تستخدم',
    modelSuffix: 'نموذج',
    modelDistribution: 'توزيع استخدام النموذج',
    primaryModel: 'النموذج الأساسي:',
    primaryModelDefault: 'جاري التحميل...',
    noModelData: 'لا توجد بيانات توزيع نموذج متاحة',
    updateKey: 'تحديث مفتاح API',
    instructions: 'كيفية الحصول على مفتاح API جديد',
    instructionsDetail:
      'يمكنك الحصول على مفتاح API جديد من موقع Google AI Studio. اتبع هذه الخطوات:',
    step1: 'انتقل إلى Google AI Studio',
    step2: 'سجل الدخول باستخدام حساب Google الخاص بك',
    step3: 'انتقل إلى قسم مفاتيح API',
    step4: 'أنشئ مفتاح API جديد أو استخدم مفتاحاً موجوداً',
    getNewKey: 'احصل على مفتاح API جديد',
    keyLabel: 'مفتاح Gemini API',
    keyPlaceholder: 'أدخل مفتاح Gemini API الخاص بك',
    keyHelperText: 'سيتم تخزين مفتاح API بشكل آمن على الخادم',
    updateButton: 'تحديث مفتاح API',
    recentRequests: 'طلبات API الحديثة',
    noRequestsData: 'لا توجد طلبات API حديثة متاحة',
    noUsageData: 'لا توجد بيانات استخدام رموز متاحة',
    modelConfigNote: 'التكوين الحالي: باستخدام',
    model: 'نموذج',
    modelConfigExplanation: 'جميع الصفحات ومكونات الخادم مُكونة لاستخدام',
    modelPerformance: 'نموذج للأداء الأمثل. النموذج الاحتياطي:',
    lastUpdated: 'آخر تحديث:',
    username: 'اسم المستخدم',
    role: 'الدور',
    lastLogin: 'آخر تسجيل دخول',
    totalRequests: 'إجمالي طلبات API',
    totalTokens: 'إجمالي الرموز المستخدمة',
    fallbackMode: 'النظام يعمل في الوضع الاحتياطي',
    fallbackExplanation:
      'مفتاح API غير صالح أو منتهي الصلاحية، لكن النظام مُكوّن للاستمرار في العمل في الوضع الاحتياطي. هذه ميزة للتطوير ولا يجب استخدامها في الإنتاج. يرجى تحديث مفتاح API الخاص بك.',
    status: 'الحالة',
    invalid: 'غير صالح',
    fallbackEnabled: 'الوضع الاحتياطي مُفعّل',
    keyPreview: 'معاينة المفتاح',
    message: 'الرسالة',
    errorType: 'نوع الخطأ',
    lastChecked: 'آخر فحص',
    valid: 'صالح',
    updateSuccess: 'تم تحديث مفتاح API بنجاح',
    tokens: 'الرموز',
    requests: 'الطلبات',
    date: 'التاريخ',
    modelName: 'النموذج',
    requestType: 'نوع الطلب',
  },

  // University Stats translations
  universityStats: {
    title: 'مسار بالأرقام',
    subtitle:
      'وصل تأثيرنا العالمي والتزامنا بالتميز إلى بناء مجتمع تعليمي مزدهر مع نتائج مبهرة.',
    ourImpact: 'تأثيرنا',
    studentsEnrolled: 'طالب مسجل',
    coursesOffered: 'دورة متاحة',
    employmentRate: 'معدل التوظيف',
    countriesRepresented: 'دولة ممثلة',
    universityDescription:
      'منصة تعليمية رقمية معتمدة عالمياً متخصصة في ريادة الأعمال، تقدم تعليماً مرناً يجمع بين النظرية والخبرة العملية.',
    footer: {
      explore: 'استكشف',
      courses: 'الدورات',
      programs: 'البرامج',
      admissions: 'القبول',
      aboutUs: 'من نحن',
      contact: 'اتصل بنا',
      specializations: 'التخصصات',
      programming: 'البرمجة',
      cybersecurity: 'الأمن السيبراني',
      finance: 'التمويل',
      marketing: 'التسويق',
      viewAll: 'عرض الكل',
      contactUs: 'اتصل بنا',
      address: '1234 شارع الجامعة\nالحرم الرقمي، كاليفورنيا 90210',
      email: 'البريد الإلكتروني: <EMAIL>',
      phone: 'الهاتف: 7890-456 (123) 1+',
      copyright: '© 2025 مسار. جميع الحقوق محفوظة.',
      privacyPolicy: 'سياسة الخصوصية',
      termsOfService: 'شروط الخدمة',
    },
  },

  // Path Advisor translations
  pathAdvisor: {
    title: 'مستشار المسار',
    chatWithAdvisor: 'تحدث مع مستشار المسار',
    getPersonalizedHelp: 'احصل على إرشاد شخصي لرحلة التعلم الخاصة بك',
    onlineNow: 'متصل الآن',
    typeMessage: 'اكتب رسالتك...',
    send: 'إرسال',
    welcomeMessage:
      'مرحبًا! أنا مستشار المسار الخاص بك. أنا هنا لمساعدتك في العثور على مسار التعلم المثالي بناءً على اهتماماتك وأهدافك. كيف يمكنني مساعدتك اليوم؟',
    suggestedQuestions: {
      title: 'أسئلة مقترحة',
      availablePaths: 'ما هي مسارات التعلم المتاحة؟',
      findPath: 'كيف أجد المسار المناسب لي؟',
      assessmentWork: 'كيف يعمل التقييم؟',
    },
  },

  // Admin Dashboard translations
  admin: {
    dashboard: {
      title: 'لوحة التحكم الإدارية',
      subtitle: 'نظرة شاملة على عمليات الجامعة والإحصائيات',
      totalUsers: 'إجمالي المستخدمين',
      departments: 'الأقسام',
      assessments: 'التقييمات',
      quickActions: 'الإجراءات السريعة',
      quickActionsDescription:
        'الوصول إلى الوظائف الإدارية الرئيسية بنقرة واحدة',
      userDistribution: 'توزيع المستخدمين',
      coursesByDepartment: 'الدورات حسب القسم',
      studentLevelDistribution: 'توزيع مستوى الطلاب',
      studentPerformance: 'أداء الطلاب',
      interactiveLearning: 'التعلم التفاعلي',
      courseGenerator: 'مولد الدورات',
      overviewCharts: 'مخططات النظرة العامة',
      studentStatistics: 'إحصائيات الطلاب',
      interactiveAndAI: 'التعلم التفاعلي والذكاء الاصطناعي',
      systemAndActivity: 'النظام والنشاط',
      communicationCenter: 'مركز الاتصالات',
      planningAndAssistance: 'التخطيط والمساعدة',

      // Quick Actions
      manageUsers: 'إدارة المستخدمين',
      manageUsersTooltip: 'إدارة المستخدمين',
      manageCourses: 'إدارة الدورات',
      manageCoursesTooltip: 'إدارة الدورات',
      departmentsTooltip: 'إدارة الأقسام',
      studentLevels: 'مستويات الطلاب',
      studentLevelsTooltip: 'إدارة مستويات الطلاب',
      pendingRegistrations: 'التسجيلات المعلقة',
      pendingRegistrationsTooltip: 'إدارة التسجيلات المعلقة',
      assessmentQuestions: 'أسئلة التقييم',
      assessmentQuestionsTooltip: 'إدارة أسئلة التقييم',
      aiDecisions: 'قرارات الذكاء الاصطناعي',
      aiDecisionsTooltip: 'إدارة قرارات الذكاء الاصطناعي',
      aiAssessmentAnalysis: 'تحليل تقييم الذكاء الاصطناعي',
      aiAssessmentAnalysisTooltip: 'إدارة تحليل تقييم الذكاء الاصطناعي',
      courseGeneratorTooltip: 'إدارة مولد الدورات',
      interactiveLearningTooltip: 'إدارة التعلم التفاعلي',
      gradesOverview: 'نظرة عامة على الدرجات',
      gradesOverviewTooltip: 'إدارة نظرة عامة على الدرجات',
      notifications: 'الإشعارات',
      notificationsTooltip: 'إدارة الإشعارات',
      analytics: 'التحليلات',
      analyticsTooltip: 'إدارة التحليلات',
      leaderboards: 'لوحات المتصدرين',
      leaderboardsTooltip: 'إدارة لوحات المتصدرين',
      settings: 'الإعدادات',
      settingsTooltip: 'إدارة الإعدادات',
      courses: 'الدورات',

      // Dashboard Data Messages
      noCourseData: 'لا توجد بيانات دورات متاحة',
      noStudentLevelData: 'لا توجد بيانات مستوى طلاب متاحة',
      noInteractiveLearningData: 'لا توجد بيانات تعلم تفاعلي متاحة',
      noAIGeneratedContentData:
        'لا توجد بيانات محتوى مولد بالذكاء الاصطناعي متاحة',
      interactiveLearningEngagement: 'مشاركة التعلم التفاعلي',
      upcomingEvents: 'الأحداث القادمة',
      viewFullCalendar: 'عرض التقويم الكامل',
      assessmentManagement: 'إدارة التقييمات',

      // System Status
      systemStatus: {
        title: 'حالة النظام',
        systemUptime: 'وقت تشغيل النظام',
        days: 'أيام',
        hours: 'ساعات',
        minutes: 'دقائق',
        seconds: 'ثوان',
        databaseStatus: 'حالة قاعدة البيانات',
        connected: 'متصل',
        apiStatus: 'حالة API',
        running: 'يعمل',
        lastBackup: 'آخر نسخة احتياطية',
        storageUsage: 'استخدام التخزين',
        memoryUsage: 'استخدام الذاكرة',
        cpuUsage: 'استخدام المعالج',
        resourceUsage: 'استخدام الموارد',
        lastUpdated: 'آخر تحديث',
      },

      // Recent Activity
      recentActivity: {
        title: 'النشاط الأخير',
        user_login: 'تسجيل دخول المستخدم',
        course_created: 'إنشاء دورة',
        backup_completed: 'اكتمال النسخ الاحتياطي',
        assessment_submitted: 'تقديم التقييم',
        system_warning: 'تحذير النظام',
      },

      // Recent Announcements
      recentAnnouncements: {
        noAnnouncements: 'لا توجد إعلانات حديثة',
      },

      // Recent Notifications
      recentNotifications: {
        by: 'بواسطة',
        unread: 'غير مقروء',
      },

      // AI Assistant
      aiAssistant: {
        title: 'المساعد الذكي',
        beta: 'تجريبي',
        description:
          'اسألني أي شيء عن بيانات الجامعة أو التقارير أو المهام الإدارية.',
        suggestionsTitle: 'جرب السؤال عن:',
        placeholder: 'اطرح سؤالاً...',
        disclaimer: 'الردود مولدة بالذكاء الاصطناعي وقد تحتاج إلى تحقق.',
      },
    },
    statistics: {
      students: 'الطلاب',
      professors: 'الأساتذة',
      admins: 'المديرون',
      totalUsers: 'إجمالي المستخدمين',
      totalCourses: 'إجمالي الدورات',
      activeCourses: 'الدورات النشطة',
      departments: 'الأقسام',
      assessments: 'التقييمات',
      interactiveLessons: 'الدروس التفاعلية',
      aiGeneratedContent: 'المحتوى المولد بالذكاء الاصطناعي',
    },
    assessment: {
      helpTitle: 'مساعدة إدارة التقييم',
      quickStartGuide: 'دليل البدء السريع',
      addQuestion: 'إضافة سؤال',
      addQuestionDesc: 'انقر على زر "إضافة سؤال" لإنشاء سؤال جديد يدوياً.',
      generateAI: 'توليد بالذكاء الاصطناعي',
      generateAIDesc:
        'انقر على "توليد أسئلة بالذكاء الاصطناعي" لإنشاء أسئلة تلقائياً.',
      editQuestion: 'تعديل الأسئلة',
      editQuestionDesc: 'انقر على أيقونة التعديل في أي سؤال لتعديله.',
      questionTypes: 'أنواع الأسئلة',
      multipleChoice: 'اختيار متعدد',
      multipleChoiceDesc: 'يختار الطلاب إجابة صحيحة واحدة من عدة خيارات.',
      trueFalse: 'صح/خطأ',
      trueFalseDesc: 'يختار الطلاب إما صح أو خطأ.',
      fillBlank: 'ملء الفراغ',
      fillBlankDesc: 'يكتب الطلاب إجابة قصيرة.',
      ordering: 'ترتيب',
      orderingDesc: 'يرتب الطلاب العناصر في التسلسل الصحيح.',
      adaptiveAssessments: 'التقييمات التكيفية',
      adaptiveDesc:
        'تقوم التقييمات التكيفية بضبط مستوى الصعوبة تلقائياً بناءً على أداء الطالب.',
      setupAdaptive: 'لإعداد التقييمات التكيفية:',
      gotoSettings: 'اذهب إلى "إعدادات التقييم"',
      enableAdaptive: 'فعّل "الصعوبة التكيفية"',
      configureSettings: 'اضبط عتبات الصعوبة والتوزيعات',
      adaptiveTip:
        'نصيحة: أنشئ أسئلة بمستويات صعوبة متنوعة (1-5) لضمان عمل التقييمات التكيفية بفعالية.',
      troubleshooting: 'استكشاف الأخطاء وإصلاحها',
      commonIssues: 'المشاكل الشائعة:',
      questionsNotAppearing: 'عدم ظهور الأسئلة في التقييمات',
      questionsNotAppearingSolution:
        'تأكد من أن الأسئلة مُعلّمة كـ "نشطة" ولها نوع التقييم الصحيح.',
      aiGenerationIssues: 'مشاكل في توليد الذكاء الاصطناعي',
      aiGenerationIssuesSolution:
        'جرب تحديد فئة أكثر تفصيلاً أو اضبط مستوى الصعوبة.',
      studentAssessmentProblems: 'مشاكل تقييم الطلاب',
      studentAssessmentProblemsSolution:
        'تحقق من صلاحيات الطلاب وتأكد من وجود أسئلة كافية لنوع التقييم.',
      fullDocumentation: 'عرض الوثائق الكاملة',
    },
  },

  // Dashboard translations
  dashboard: {
    studentPerformance: 'أداء الطلاب',
    studentPerformanceDescription:
      'تصور في الوقت الفعلي لمقاييس أداء الطلاب عبر أبعاد مختلفة.',
    timeRange: 'النطاق الزمني',
    day: 'يوم',
    week: 'أسبوع',
    month: 'شهر',
    year: 'سنة',
    engagement: 'المشاركة',
    completion: 'الإنجاز',
    accuracy: 'الدقة',
    retention: 'الاحتفاظ',
    satisfaction: 'الرضا',
  },

  // User Management translations
  userManagement: {
    createUser: 'إنشاء مستخدم جديد',
    editUser: 'تعديل المستخدم',
    deleteUser: 'حذف المستخدم',
    userDetails: 'تفاصيل المستخدم',
    userList: 'قائمة المستخدمين',
    usersList: 'قائمة المستخدمين',
    usersManagement: 'إدارة المستخدمين',
    username: 'اسم المستخدم',
    email: 'البريد الإلكتروني',
    password: 'كلمة المرور',
    confirmPassword: 'تأكيد كلمة المرور',
    firstName: 'الاسم الأول',
    lastName: 'اسم العائلة',
    name: 'الاسم',
    role: 'الدور',
    dateOfBirth: 'تاريخ الميلاد',
    phoneNumber: 'رقم الهاتف',
    address: 'العنوان',
    department: 'القسم',
    studentId: 'رقم الطالب',
    profilePicture: 'الصورة الشخصية',
    uploadProfilePicture: 'تحميل الصورة الشخصية',
    selectedFile: 'الملف المحدد',
    passwordsDontMatch: 'كلمات المرور غير متطابقة',
    failedToCreateUser: 'فشل في إنشاء المستخدم',
    userCreatedSuccessfully: 'تم إنشاء المستخدم بنجاح',
    duplicateUsername: 'يوجد مستخدم بهذا الاسم مسبقاً',
    duplicateEmail: 'يوجد مستخدم بهذا البريد الإلكتروني مسبقاً',
    duplicateUsernameAndEmail:
      'اسم المستخدم والبريد الإلكتروني مستخدمان بالفعل',
    validationErrors: 'يرجى تصحيح الأخطاء في النموذج',
    admin: 'مدير',
    professor: 'أستاذ',
    student: 'طالب',
    addUser: 'إضافة مستخدم',
    searchUsers: 'البحث عن المستخدمين',
    status: 'الحالة',
    active: 'نشط',
    inactive: 'غير نشط',
    actions: 'إجراءات',
    viewProfile: 'عرض الملف الشخصي',
    manageLevel: 'إدارة المستوى',
    confirmDelete: 'هل أنت متأكد أنك تريد حذف هذا المستخدم؟',
    failedToDeleteUser: 'فشل في حذف المستخدم',
    personalInfo: 'المعلومات الشخصية',
    accountInfo: 'معلومات الحساب',
    additionalInfo: 'معلومات إضافية',
    noUsers: 'لا يوجد مستخدمين',
    noUsersFound: 'لا يوجد مستخدمين يطابقون معايير البحث',
    tryDifferentSearch: 'جرب مصطلح بحث مختلف',
    addFirstUser: 'أضف أول مستخدم للبدء',
  },

  // Interactive Learning translations
  interactiveLearning: {
    title: 'التعلم التفاعلي',
    tabs: {
      dashboard: 'لوحة التحكم',
      courses: 'الدورات',
      learningPaths: 'مسارات التعلم',
    },
    stats: {
      totalCourses: 'إجمالي المقررات',
      across: 'عبر',
      totalLessons: 'إجمالي الدروس',
      perCourse: 'لكل مقرر',
      totalStudents: 'إجمالي الطلاب',
      completions: 'الإكمالات',
      rate: 'معدل',
      averageScore: 'متوسط الدرجات',
      engagement: 'المشاركة',
    },
    lessonsLabel: 'الدروس',
  },

  // Course translations
  courses: {
    newCourse: 'مقرر جديد',
  },

  // Course Generator translations
  courseGenerator: {
    simplifiedTitle: 'مولد الدورات المبسط',
    simplifiedDescription: 'قم بإنشاء محتوى تعليمي عالي الجودة بسهولة',
    unifiedTitle: 'مولد الدورات الموحد',
    unifiedSubtitle: 'إنشاء محتوى تعليمي شامل بالذكاء الاصطناعي',
    generationType: 'نوع التوليد',
    fullCourseContent: 'محتوى الدورة الكامل',
    lessonPlan: 'خطة الدرس',
    assessment: 'التقييم',
    courseSchedule: 'جدول الدورة',
    keywords: 'الكلمات المفتاحية',
    keywordsPlaceholder: 'أدخل الكلمات المفتاحية...',
    keywordsHelp: 'أدخل الكلمات المفتاحية ذات الصلة بالموضوع',
    additionalContext: 'السياق الإضافي',
    additionalContextPlaceholder: 'أدخل معلومات إضافية...',
    additionalContextHelp: 'قدم معلومات إضافية لتحسين المحتوى المولد',
    testConnection: 'اختبار الاتصال',
    generateButton: 'توليد',
    noCourseSelected: 'يرجى اختيار دورة',
    contentGeneratedSuccessfully: 'تم إنشاء المحتوى بنجاح',
    editContent: 'تعديل المحتوى',
    editJsonWarning:
      'إذا كنت تحرر محتوى JSON، تأكد من أنه يبقى بتنسيق JSON صالح.',
    deleteConfirmation:
      'هل أنت متأكد أنك تريد حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.',
  },

  // Course Details translations
  courseDetails: {
    loading: 'جاري التحميل...',
    error: {
      title: 'خطأ في تحميل المقرر',
    },
    backToCourses: 'العودة إلى المقررات',
  },

  // Chat translations
  chat: {
    chatHistory: 'سجل المحادثات',
    newConversation: 'محادثة جديدة',
    noConversations: 'لا توجد محادثات بعد. ابدأ محادثة جديدة!',
    startNewChat: 'ابدأ محادثة جديدة للبدء!',
    welcomeTitle: 'مرحباً بك في المساعد الذكي',
    welcomeMessage: 'اختر محادثة من الشريط الجانبي لبدء الدردشة، أو أنشئ محادثة جديدة للبدء.',
    startNewConversation: 'بدء محادثة جديدة',
    createNewConversation: 'إنشاء محادثة جديدة',
    conversationTitle: 'عنوان المحادثة',
    enterTitle: 'أدخل عنواناً للمحادثة...',
    defaultConversationTitle: 'ChatGPT',
    newChat: 'محادثة جديدة',
  },

  // Chat and AI Agent translations
  'Standard Chat': 'المحادثة العادية',
  'Multi-Agent Chat': 'المحادثة متعددة الوكلاء',
  'Chat Demo': 'عرض توضيحي للمحادثة',
  'Agent Status': 'حالة الوكيل',
  'Agent Testing': 'اختبار الوكيل',
  'AI Course Recommendations': 'توصيات الدورات بالذكاء الاصطناعي',

  // AI Agent translations
  aiAgent: {
    title: 'المساعد الذكي',
    chat: 'المحادثة',
    help: 'المساعدة',
    ask: 'اسأل',
    response: 'الرد',
    thinking: 'جاري التفكير...',
    error: 'خطأ في المساعد الذكي',
    courseAssistant: 'مساعد الدورة',
  },
};

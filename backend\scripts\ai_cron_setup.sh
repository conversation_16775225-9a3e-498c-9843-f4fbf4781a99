#!/bin/bash

# AI Service Automated Maintenance Cron Setup
# This script sets up automated maintenance tasks for the AI services

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Setting up AI Service Automated Maintenance...${NC}"

# Get the current directory (should be the backend directory)
BACKEND_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
PYTHON_PATH="$BACKEND_DIR/../.venv/bin/python"
MANAGE_PY="$BACKEND_DIR/manage.py"
MAINTENANCE_SCRIPT="$BACKEND_DIR/scripts/ai_maintenance.py"

echo "Backend directory: $BACKEND_DIR"
echo "Python path: $PYTHON_PATH"
echo "Management script: $MANAGE_PY"
echo "Maintenance script: $MAINTENANCE_SCRIPT"

# Check if files exist
if [ ! -f "$PYTHON_PATH" ]; then
    echo -e "${RED}Error: Python virtual environment not found at $PYTHON_PATH${NC}"
    echo "Please ensure the virtual environment is set up correctly."
    exit 1
fi

if [ ! -f "$MANAGE_PY" ]; then
    echo -e "${RED}Error: Django manage.py not found at $MANAGE_PY${NC}"
    exit 1
fi

if [ ! -f "$MAINTENANCE_SCRIPT" ]; then
    echo -e "${RED}Error: Maintenance script not found at $MAINTENANCE_SCRIPT${NC}"
    exit 1
fi

# Make maintenance script executable
chmod +x "$MAINTENANCE_SCRIPT"

# Create cron jobs
echo -e "${YELLOW}Creating cron jobs...${NC}"

# Backup existing crontab
crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

# Create new cron entries
CRON_ENTRIES="
# AI Service Automated Maintenance
# Health check every 15 minutes
*/15 * * * * cd $BACKEND_DIR && $PYTHON_PATH $MAINTENANCE_SCRIPT health >> logs/ai_maintenance.log 2>&1

# Performance monitoring every 30 minutes
*/30 * * * * cd $BACKEND_DIR && $PYTHON_PATH $MAINTENANCE_SCRIPT performance >> logs/ai_maintenance.log 2>&1

# Queue status check every hour
0 * * * * cd $BACKEND_DIR && $PYTHON_PATH $MAINTENANCE_SCRIPT queue >> logs/ai_maintenance.log 2>&1

# Full maintenance (cleanup + stats) every 6 hours
0 */6 * * * cd $BACKEND_DIR && $PYTHON_PATH $MAINTENANCE_SCRIPT full >> logs/ai_maintenance.log 2>&1

# Daily statistics generation at 2 AM
0 2 * * * cd $BACKEND_DIR && $PYTHON_PATH $MANAGE_PY ai_health_check --generate-stats >> logs/ai_maintenance.log 2>&1

# Weekly cleanup at 3 AM on Sundays
0 3 * * 0 cd $BACKEND_DIR && $PYTHON_PATH $MANAGE_PY ai_health_check --cleanup-metrics --days 30 >> logs/ai_maintenance.log 2>&1
"

# Add cron entries to existing crontab
(crontab -l 2>/dev/null; echo "$CRON_ENTRIES") | crontab -

echo -e "${GREEN}Cron jobs created successfully!${NC}"

# Display current crontab
echo -e "${YELLOW}Current crontab entries:${NC}"
crontab -l | grep -A 20 "AI Service Automated Maintenance"

# Create log rotation configuration
echo -e "${YELLOW}Setting up log rotation...${NC}"

LOG_ROTATE_CONFIG="/etc/logrotate.d/ai-maintenance"
sudo tee "$LOG_ROTATE_CONFIG" > /dev/null << EOF
$BACKEND_DIR/logs/ai_maintenance.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $(whoami) $(whoami)
}

$BACKEND_DIR/logs/ai_maintenance_report_*.json {
    weekly
    missingok
    rotate 12
    compress
    delaycompress
    notifempty
}
EOF

echo -e "${GREEN}Log rotation configured at $LOG_ROTATE_CONFIG${NC}"

# Create systemd service for monitoring (optional)
echo -e "${YELLOW}Creating systemd service for AI monitoring...${NC}"

SERVICE_FILE="/etc/systemd/system/ai-maintenance-monitor.service"
sudo tee "$SERVICE_FILE" > /dev/null << EOF
[Unit]
Description=AI Service Maintenance Monitor
After=network.target

[Service]
Type=oneshot
User=$(whoami)
WorkingDirectory=$BACKEND_DIR
Environment=DJANGO_SETTINGS_MODULE=settings.production
ExecStart=$PYTHON_PATH $MAINTENANCE_SCRIPT health
StandardOutput=append:$BACKEND_DIR/logs/ai_maintenance.log
StandardError=append:$BACKEND_DIR/logs/ai_maintenance.log

[Install]
WantedBy=multi-user.target
EOF

# Create timer for the service
TIMER_FILE="/etc/systemd/system/ai-maintenance-monitor.timer"
sudo tee "$TIMER_FILE" > /dev/null << EOF
[Unit]
Description=Run AI Service Maintenance Monitor every 15 minutes
Requires=ai-maintenance-monitor.service

[Timer]
OnCalendar=*:0/15
Persistent=true

[Install]
WantedBy=timers.target
EOF

# Enable and start the timer
sudo systemctl daemon-reload
sudo systemctl enable ai-maintenance-monitor.timer
sudo systemctl start ai-maintenance-monitor.timer

echo -e "${GREEN}Systemd service and timer created and started!${NC}"

# Create monitoring dashboard script
echo -e "${YELLOW}Creating monitoring dashboard script...${NC}"

DASHBOARD_SCRIPT="$BACKEND_DIR/scripts/ai_dashboard.py"
cat > "$DASHBOARD_SCRIPT" << 'EOF'
#!/usr/bin/env python
"""
AI Service Monitoring Dashboard CLI

Quick command-line dashboard for AI service status.
"""

import os
import sys
import django
import json
from datetime import datetime, timedelta

# Set up Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings.development')
django.setup()

from django.core.cache import cache
from utils.ai_service_improved import improved_ai_service
from utils.ai_models import AIServiceMetric, AIRequest

def print_dashboard():
    """Print AI service dashboard"""
    print("=" * 60)
    print("AI SERVICE MONITORING DASHBOARD")
    print("=" * 60)
    print(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Health Status
    try:
        health = improved_ai_service.get_health_status()
        status_color = {
            'healthy': '\033[92m',
            'degraded': '\033[93m',
            'unhealthy': '\033[91m',
            'unknown': '\033[94m'
        }.get(health['status'], '\033[0m')
        
        print(f"🏥 HEALTH STATUS: {status_color}{health['status'].upper()}\033[0m")
        
        if health['issues']:
            print("⚠️  Issues:")
            for issue in health['issues']:
                print(f"   - {issue['message']}")
        print()
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        print()
    
    # Quick Stats (Last 24h)
    try:
        last_24h = datetime.now() - timedelta(hours=24)
        metrics = AIServiceMetric.objects.filter(timestamp__gte=last_24h)
        
        total_requests = metrics.count()
        successful_requests = metrics.filter(success=True).count()
        success_rate = (successful_requests / total_requests * 100) if total_requests > 0 else 0
        
        print("📊 LAST 24 HOURS:")
        print(f"   Total Requests: {total_requests}")
        print(f"   Success Rate: {success_rate:.1f}%")
        print(f"   Failed Requests: {total_requests - successful_requests}")
        print()
    except Exception as e:
        print(f"❌ Stats calculation failed: {e}")
        print()
    
    # Queue Status
    try:
        queued = AIRequest.objects.filter(status='queued').count()
        processing = AIRequest.objects.filter(status='processing').count()
        
        print("🔄 QUEUE STATUS:")
        print(f"   Queued: {queued}")
        print(f"   Processing: {processing}")
        print()
    except Exception as e:
        print(f"❌ Queue status failed: {e}")
        print()
    
    # Recent Maintenance Report
    try:
        report = cache.get('ai_maintenance_report')
        if report:
            print("🔧 LAST MAINTENANCE:")
            print(f"   Timestamp: {report['timestamp']}")
            print(f"   Actions: {report['summary']['successful_actions']}/{report['summary']['total_actions']} successful")
            print(f"   Alerts: {report['summary']['total_alerts']} ({report['summary']['critical_alerts']} critical)")
        else:
            print("🔧 MAINTENANCE: No recent report available")
        print()
    except Exception as e:
        print(f"❌ Maintenance report failed: {e}")
        print()
    
    print("=" * 60)

if __name__ == "__main__":
    print_dashboard()
EOF

chmod +x "$DASHBOARD_SCRIPT"

echo -e "${GREEN}Monitoring dashboard created at $DASHBOARD_SCRIPT${NC}"

# Final instructions
echo -e "${GREEN}Setup completed successfully!${NC}"
echo
echo -e "${YELLOW}What was set up:${NC}"
echo "✅ Cron jobs for automated maintenance"
echo "✅ Log rotation configuration"
echo "✅ Systemd service and timer"
echo "✅ Monitoring dashboard script"
echo
echo -e "${YELLOW}Useful commands:${NC}"
echo "📊 View dashboard: python $DASHBOARD_SCRIPT"
echo "🔧 Run manual maintenance: python $MAINTENANCE_SCRIPT [health|cleanup|stats|performance|queue|full]"
echo "📋 View cron jobs: crontab -l"
echo "📊 Check systemd timer: sudo systemctl status ai-maintenance-monitor.timer"
echo "📝 View logs: tail -f $BACKEND_DIR/logs/ai_maintenance.log"
echo
echo -e "${GREEN}AI Service automated maintenance is now active!${NC}"

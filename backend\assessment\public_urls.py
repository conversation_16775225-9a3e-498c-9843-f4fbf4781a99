from django.urlsimportincludepathfromrest_framework.routersimportDefaultRouterfrom.importpublic_viewsapp_name="assessment_public"router=DefaultRouter()router.register(r"questions"public_views.PublicQuestionViewSetbasename="public-question")urlpatterns=[path(""include((router.urlsapp_name)))#Publicassessmentendpointspath("start/"public_views.PublicAssessmentView.as_view()name="public-assessment-start")path("submit/"public_views.PublicAssessmentView.as_view()name="public-assessment-submit")#Checkassessmentstatusendpointpath("check-status/"public_views.CheckAssessmentStatusView.as_view()name="check-assessment-status")#Directsubmissionendpointforfrontendpath("submit-assessment/"public_views.PublicAssessmentSubmitView.as_view()name="public-assessment-direct-submit")]
import loggingfromrest_frameworkimportpermissionslogger=logging.getLogger(__name__)classIsStudent(permissions.BasePermission):"""Custompermissiontoonlyallowstudentstoaccesstheview."""defhas_permission(selfrequestview):returnbool(request.userandrequest.user.is_authenticatedandrequest.user.role=="STUDENT")classIsAssessmentParticipant(permissions.BasePermission):"""Custompermissiontoonlyallowstudentstoaccesstheirownassessments."""defhas_object_permission(selfrequestviewobj):#Onlyallowaccessiftheassessmentbelongstotherequestinguserreturnobj.student==request.userclassIsInstructor(permissions.BasePermission):"""Custompermissiontoonlyallowinstructorstoaccesstheview."""defhas_permission(selfrequestview):returnbool(request.userandrequest.user.is_authenticatedandrequest.user.role=="INSTRUCTOR")classIsAdmin(permissions.BasePermission):"""Custompermissiontoonlyallowadminuserstoaccesstheview."""defhas_permission(selfrequestview):returnbool(request.userandrequest.user.is_authenticatedandrequest.user.is_staff)classCanManageAssessments(permissions.BasePermission):"""Custompermissiontoallowinstructorsandadminstomanageassessments."""defhas_permission(selfrequestview):ifnotrequest.user.is_authenticated:returnFalsereturnrequest.user.is_stafforrequest.user.role=="INSTRUCTOR"defhas_object_permission(selfrequestviewobj):#Admincanmanageallassessmentsifrequest.user.is_staff:returnTrue#Instructorscanonlymanageassessmentstheycreatedifrequest.user.role=="INSTRUCTOR":returnobj.created_by==request.userreturnFalseclassCanViewResults(permissions.BasePermission):"""Custompermissiontoallowstudentstoviewtheirownresultsandinstructors/adminstoviewallresults."""defhas_object_permission(selfrequestviewobj):ifnotrequest.user.is_authenticated:returnFalse#Studentscanonlyviewtheirownresultsifrequest.user.role=="STUDENT":returnobj.student==request.user#Instructorsandadminscanviewallresultsreturnrequest.user.is_stafforrequest.user.role=="INSTRUCTOR"classCanSubmitResponse(permissions.BasePermission):"""Custompermissiontoonlyallowstudentstosubmitresponsestotheirownassessments."""defhas_permission(selfrequestview):returnbool(request.userandrequest.user.is_authenticatedandrequest.user.role=="STUDENT")defhas_object_permission(selfrequestviewobj):#Onlyallowsubmissionif:#1.Theassessmentbelongstothestudent#2.Theassessmentisnotcompleted#3.Theassessmentisnotexpiredreturnobj.student==request.userandnotobj.completedandnotobj.is_expired#CanAccessEntrepreneurshipFeaturespermissionhasbeenremovedclassCanTakeAssessments(permissions.BasePermission):"""Permissionclasstocheckifastudentcantakeanassessment.Thischecks:1.Iftheuserisauthenticated2.Iftheuserhasalreadycompletedthistypeofassessment3.Ifretakesareallowedforthisassessmenttype4.Ifthecooldownperiodhaspassedsincethelastattempt"""defhas_permission(selfrequestview):#AlwaysallowGETrequestsifrequest.methodinpermissions.SAFE_METHODS:returnTrue#Checkiftheuserisauthenticatedifnotrequest.user.is_authenticated:returnFalse#ForPOSTrequeststocreateanewassessmentcheckiftheusercantakeitifrequest.method=="POST"andgetattr(view"action"None)=="create":from.modelsimportAssessmentAssessmentSettings#Getassessmenttypefromrequestassessment_type=request.data.get("assessment_type""PLACEMENT")#Checkiftheuserhasalreadycompletedthistypeofassessmentexisting_assessment=Assessment.objects.filter(student=request.userassessment_type=assessment_typestatus="COMPLETED").exists()#Ifnoexistingassessmentallowifnotexisting_assessment:returnTrue#Checkifretakesareallowedforthisassessmenttypetry:settings=AssessmentSettings.objects.get(assessment_type=assessment_type)ifnotsettings.allow_retakes:logger.info(f"User{request.user.username}attemptedtoretake{assessment_type}assessmentbutretakesarenotallowed")returnFalse#Checkcooldownperiodfromdatetimeimport timedeltafrom django.utilsimport timezone#Getthemostrecentcompletedassessmentofthistypelatest_assessment=(Assessment.objects.filter(student=request.userassessment_type=assessment_typestatus="COMPLETED").order_by("-end_time").first())iflatest_assessmentandlatest_assessment.end_time:cooldown_end=latest_assessment.end_time+timedelta(days=settings.retake_cooldown_days)iftimezone.now()<cooldown_end:logger.info(f"User{request.user.username}attemptedtoretake{assessment_type}assessmentbutcooldownperiodhasnotpassed")returnFalse#AllcheckspassedallowretakereturnTrueexceptAssessmentSettings.DoesNotExist:#Ifnosettingsfounddefaulttoallowingretakeslogger.warning(f"Nosettingsfoundforassessmenttype{assessment_type}defaultingtoallowretakes")returnTrue#ForotheractionsallowreturnTrueclassIsInstructorOrAdmin(permissions.BasePermission):"""Permissiontoonlyallowinstructorsoradminstoaccessaview."""defhas_permission(selfrequestview):ifnotrequest.user.is_authenticated:returnFalse#Checkifuserisadminifrequest.user.is_stafforrequest.user.is_superuser:returnTrue#Checkifuserisinstructorreturnhasattr(request.user"role")andrequest.user.role=="INSTRUCTOR"
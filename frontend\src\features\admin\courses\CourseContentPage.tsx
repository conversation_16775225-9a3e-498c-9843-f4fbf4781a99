import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Typography,
  Box,
  Paper,
  Breadcrumbs,
  Link,
  Button,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import {
  FiArrowLeft as ArrowBackIcon,
  FiBook as BookIcon,
  FiFileText as FileTextIcon,
  FiVideo as VideoIcon,
  FiImage as ImageIcon,
  FiDownload as DownloadIcon,
  FiEdit as EditIcon,
  FiPlus as AddIcon,
  FiTrash2 as DeleteIcon,
} from 'react-icons/fi';

import { courseService } from '../../../services/courseService';
import { useToast } from '../../../hooks/useToast';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`content-tabpanel-${index}`}
      aria-labelledby={`content-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const CourseContentPage: React.FC = () => {
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();
  const { showError, showSuccess } = useToast();
  const { t } = useTranslation();

  const [loading, setLoading] = useState<boolean>(true);
  const [course, setCourse] = useState<any>(null);
  const [materials, setMaterials] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<number>(0);

  // Fetch course and content data
  useEffect(() => {
    const fetchData = async () => {
      if (!courseId) {
        setError('No course ID provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fetch course data
        const courseResponse = await courseService.getCourse(parseInt(courseId));
        if (courseResponse?.data) {
          setCourse(courseResponse.data);
        }

        // Fetch materials/content
        const materialsResponse = await courseService.getCourseMaterials(parseInt(courseId));
        if (materialsResponse?.data) {
          setMaterials(Array.isArray(materialsResponse.data) ? materialsResponse.data : []);
        }

        setError(null);
      } catch (err: any) {
        console.error('Error fetching course content:', err);
        setError(err.message || 'Failed to load course content');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [courseId]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleBack = () => {
    navigate(`/admin/courses/${courseId}`);
  };

  const handleAddContent = () => {
    navigate(`/admin/courses/${courseId}/content/add`);
  };

  const getContentIcon = (contentType: string) => {
    switch (contentType?.toLowerCase()) {
      case 'video':
        return <VideoIcon />;
      case 'image':
        return <ImageIcon />;
      case 'document':
      case 'pdf':
        return <FileTextIcon />;
      default:
        return <BookIcon />;
    }
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" p={5}>
          <CircularProgress />
          <Typography sx={{ ml: 2 }}>
            {t('common.loading', 'Loading...')}
          </Typography>
        </Box>
      </Container>
    );
  }

  if (error || !course) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error || t('common.error.courseNotFound', 'Course not found')}
        </Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
        >
          {t('common.backToCourse', 'Back to Course')}
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 2 }}>
        <Link component={RouterLink} to="/admin/dashboard">
          {t('common.dashboard', 'Dashboard')}
        </Link>
        <Link component={RouterLink} to="/admin/courses">
          {t('common.courses', 'Courses')}
        </Link>
        <Link component={RouterLink} to={`/admin/courses/${courseId}`}>
          {course.title}
        </Link>
        <Typography color="text.primary">
          {t('courseContent.title', 'Course Content')}
        </Typography>
      </Breadcrumbs>

      {/* Back Button */}
      <Button
        variant="outlined"
        startIcon={<ArrowBackIcon />}
        onClick={handleBack}
        sx={{ mb: 3 }}
      >
        {t('navigation.backToCourse', 'Back to Course')}
      </Button>

      {/* Course Header */}
      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Box>
            <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
              {t('courseContent.title', 'Course Content')}: {course.title}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {t('courseContent.description', 'Manage all course materials and content')}
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddContent}
          >
            {t('courseContent.addContent', 'Add Content')}
          </Button>
        </Box>
      </Paper>

      {/* Content Tabs */}
      <Paper elevation={2} sx={{ borderRadius: 2 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
          >
            <Tab
              label={t('courseContent.allContent', 'All Content')}
              icon={<BookIcon />}
              iconPosition="start"
            />
            <Tab
              label={t('courseContent.materials', 'Materials')}
              icon={<FileTextIcon />}
              iconPosition="start"
            />
            <Tab
              label={t('courseContent.media', 'Media')}
              icon={<VideoIcon />}
              iconPosition="start"
            />
          </Tabs>
        </Box>

        {/* All Content Tab */}
        <TabPanel value={activeTab} index={0}>
          <Grid container spacing={3}>
            {materials.length === 0 ? (
              <Grid item xs={12}>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    py: 4,
                  }}
                >
                  <BookIcon size={48} color="gray" />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    {t('courseContent.noContent', 'No content available')}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" align="center">
                    {t('courseContent.noContentDesc', 'Start by adding some content to this course')}
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={handleAddContent}
                    sx={{ mt: 2 }}
                  >
                    {t('courseContent.addFirstContent', 'Add First Content')}
                  </Button>
                </Box>
              </Grid>
            ) : (
              materials.map((material) => (
                <Grid item xs={12} sm={6} md={4} key={material.id}>
                  <Card>
                    <CardContent>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          mb: 2,
                        }}
                      >
                        {getContentIcon(material.content_type)}
                        <Typography variant="h6" sx={{ ml: 1 }} noWrap>
                          {material.title}
                        </Typography>
                      </Box>
                      {material.description && (
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ mb: 2 }}
                        >
                          {material.description}
                        </Typography>
                      )}
                      <Chip
                        label={material.content_type || 'Material'}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    </CardContent>
                    <CardActions>
                      <Tooltip title={t('common.download', 'Download')}>
                        <IconButton size="small">
                          <DownloadIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t('common.edit', 'Edit')}>
                        <IconButton size="small">
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t('common.delete', 'Delete')}>
                        <IconButton size="small" color="error">
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </CardActions>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        </TabPanel>

        {/* Materials Tab */}
        <TabPanel value={activeTab} index={1}>
          <Typography variant="h6" gutterBottom>
            {t('courseContent.materialsOnly', 'Course Materials')}
          </Typography>
          <Grid container spacing={3}>
            {materials
              .filter((m) => m.content_type === 'material' || m.content_type === 'document')
              .map((material) => (
                <Grid item xs={12} sm={6} md={4} key={material.id}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        {material.title}
                      </Typography>
                      {material.description && (
                        <Typography variant="body2" color="text.secondary">
                          {material.description}
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))}
          </Grid>
        </TabPanel>

        {/* Media Tab */}
        <TabPanel value={activeTab} index={2}>
          <Typography variant="h6" gutterBottom>
            {t('courseContent.mediaOnly', 'Media Content')}
          </Typography>
          <Grid container spacing={3}>
            {materials
              .filter((m) => m.content_type === 'video' || m.content_type === 'image')
              .map((material) => (
                <Grid item xs={12} sm={6} md={4} key={material.id}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        {material.title}
                      </Typography>
                      {material.description && (
                        <Typography variant="body2" color="text.secondary">
                          {material.description}
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))}
          </Grid>
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default CourseContentPage;

import loggingfrom django.dbimporttransactionfrom django.utilsimport timezonefromrest_frameworkimportstatusviewsviewsetsfromrest_framework.permissionsimportAllowAnyfromrest_framework.responseimportResponsefrom.modelsimportAssessmentAssessmentQuestionAssessmentResponseStudentLevelfrom.serializersimportAssessmentDetailSerializerAssessmentQuestionSerializerlogger=logging.getLogger(__name__)classPublicQuestionViewSet(viewsets.ReadOnlyModelViewSet):"""ViewSetforpublicaccesstoassessmentquestions"""permission_classes=[AllowAny]#Allowpublicaccessserializer_class=AssessmentQuestionSerializerdefget_queryset(self):"""Returnonlyplacementquestionsthatarepublic"""#Getquestionsthataremarkedaspublicandforplacementassessmentsquestions=AssessmentQuestion.objects.filter(is_public=Trueis_placement=True).order_by("difficulty_level")#Logthenumberofquestionsfoundlogger.info(f"Found{questions.count()}publicplacementquestions")#Ifnoquestionsfoundlogawarningifquestions.count()==0:logger.warning("Nopublicplacementquestionsfound.Pleaseaddquestionsthroughtheadmininterface.")returnquestionsclassCheckAssessmentStatusView(views.APIView):"""Viewtocheckifastudenthasalreadytakentheassessment"""permission_classes=[AllowAny]#Allowpublicaccessdefget(selfrequest):"""Checkifastudenthasalreadytakentheassessment"""try:#GetuserIDfromqueryparamsuser_id=request.query_params.get("user_id")ifnotuser_id:returnResponse({"detail":"UserIDisrequired"}status=status.HTTP_400_BAD_REQUEST)#Checkifthestudenthasalreadytakenaplacementassessment#Makesureit'sactuallycompleted(status='COMPLETED'ANDcompleted=True)existing_assessment=Assessment.objects.filter(student_id=user_idassessment_type="PLACEMENT"status="COMPLETED"completed=True).first()#Logthecheckfordebugginglogger.info(f"Checkingassessmentstatusforuser{user_id}:{'Foundcompletedassessment'ifexisting_assessmentelse'Nocompletedassessmentfound'}")ifexisting_assessment:#Double-checkthattheassessmentisactuallycompletedif(existing_assessment.end_timeandexisting_assessment.scoreisnotNone):returnResponse({"has_taken_assessment":True"assessment_id":existing_assessment.id"assessment_date":existing_assessment.end_time"assessment_score":existing_assessment.score"assessment_level":existing_assessment.final_level})else:#Assessmentismarkedascompletedbutdoesn'thaveend_timeorscorelogger.warning(f"Assessment{existing_assessment.id}ismarkedascompletedbutmissingend_timeorscore")returnResponse({"has_taken_assessment":False"warning":"Foundincompleteassessmentdata"})else:#Checkifthere'sanin-progressassessmentin_progress=Assessment.objects.filter(student_id=user_idassessment_type="PLACEMENT"status="IN_PROGRESS").exists()returnResponse({"has_taken_assessment":False"has_in_progress":in_progress})exceptExceptionase:logger.error(f"Errorcheckingassessmentstatus:{str(e)}")returnResponse({"detail":f"Errorcheckingassessmentstatus:{str(e)}"}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classPublicAssessmentView(views.APIView):"""Viewforpublicaccesstostartandsubmitassessments"""permission_classes=[AllowAny]#Allowpublicaccessdefpost(selfrequest):"""Startanewassessmentforapublicuser"""try:#GetuserIDfromrequestuser_id=request.data.get("user_id")ifnotuser_id:returnResponse({"detail":"UserIDisrequired"}status=status.HTTP_400_BAD_REQUEST)#Checkifthestudenthasalreadytakenaplacementassessmentexisting_assessment=Assessment.objects.filter(student_id=user_idassessment_type="PLACEMENT"status="COMPLETED").first()ifexisting_assessment:returnResponse({"detail":"Youhavealreadytakentheplacementassessment.""assessment_id":existing_assessment.id"already_taken":True}status=status.HTTP_400_BAD_REQUEST)#Getadaptiveflagfromrequestadaptive=request.data.get("adaptive"True)#Createanewassessmenttry:#Getorcreatestudentleveltodetermineinitiallevelstudent_level_=StudentLevel.objects.get_or_create(student_id=user_iddefaults={"current_level":1"current_level_display":"Beginner"})#Createtheassessmentwithminimalrequiredfieldsassessment=Assessment.objects.create(student_id=user_idassessment_type="PLACEMENT"#SettheassessmenttypetoPLACEMENTtitle="PlacementAssessment"#Addatitledescription="Initialplacementassessmentfornewstudent"#Addadescriptionis_adaptive=adaptivestatus="IN_PROGRESS"start_time=timezone.now()#Setthestarttimeinitial_level=student_level.current_level#Setinitiallevelfromstudentlevel)#Logtheassessmentcreationlogger.info(f"Createdplacementassessment{assessment.id}foruser{user_id}")exceptExceptionase:#Logtheerrorandre-raiselogger.error(f"Errorcreatingplacementassessment:{str(e)}")raise#Getadmin-createdquestionsfirstadmin_questions=AssessmentQuestion.objects.filter(is_public=Trueis_placement=Trueai_generated=False#NotAI-generatedmeansadmin-created).order_by("difficulty_level")#Logthenumberofadminquestionsfoundlogger.info(f"Found{admin_questions.count()}admin-createdpublicplacementquestionsforassessment")#Ifwehaveadminquestionsusethemifadmin_questions.count()>0:questions=admin_questionselse:#Ifnoadminquestionstrytogetanypublicplacementquestionsquestions=AssessmentQuestion.objects.filter(is_public=Trueis_placement=True).order_by("difficulty_level")#Logthenumberofquestionsfoundlogger.info(f"Found{questions.count()}publicplacementquestionsforassessment")#Ifnoquestionsfoundlogawarningbutdon'tcreatedefaultquestionsifquestions.count()==0:logger.warning("Nopublicplacementquestionsfoundforassessment.Pleaseaddquestionsthroughtheadmininterface.")#Limitto10questionsquestions=questions[:10]#AddquestionstotheassessmentbycreatingAssessmentResponseobjectsforquestioninquestions:#CreateAssessmentResponsewithstudent_idAssessmentResponse.objects.create(assessment=assessmentquestion=questionstudent_id=user_id#Explicitlysetstudent_id)#Returntheassessmentdetailsserializer=AssessmentDetailSerializer(assessment)returnResponse(serializer.data)exceptConnectionErrorasconn_err:#Handleconnectionerrorsseparatelylogger.error(f"Connectionerrorduringassessmentcreation:{str(conn_err)}")#Insteadofreturninganerrorreturnasuccessresponsewithawarning#ThisallowstheassessmentflowtocontinueevenifthereareconnectionissuesreturnResponse({"detail":"Assessmentcreationstartedbutnotificationsystemunavailable.""warning":"Connectiontonotificationsystemfailed.Somefeaturesmaybelimited.""success":True"status":"success"}status=status.HTTP_200_OK)exceptExceptionase:#Checkifthisisaconnectionerrorerror_str=str(e)if("connection"inerror_str.lower()or"refused"inerror_str.lower()or"[WinError10061]"inerror_str):#Handleconnectionerrorsseparatelylogger.error(f"Connectionerrorduringassessmentcreation:{str(e)}")#Insteadofreturninganerrorreturnasuccessresponsewithawarning#ThisallowstheassessmentflowtocontinueevenifthereareconnectionissuesreturnResponse({"detail":"Assessmentcreationstartedbutnotificationsystemunavailable.""warning":"Connectiontonotificationsystemfailed.Somefeaturesmaybelimited.""success":True"status":"success"}status=status.HTTP_200_OK)else:#Handleotherexceptionslogger.error(f"Errorcreatingpublicassessment:{str(e)}")returnResponse({"detail":f"Errorcreatingassessment:{str(e)}"}status=status.HTTP_500_INTERNAL_SERVER_ERROR)defput(selfrequest):"""Submitanswersforapublicassessment"""try:#Logtherequestdatafordebuggingprint(f"Publicassessmentsubmitrequestdata:{request.data}")#GetassessmentIDfromrequestassessment_id=request.data.get("assessment_id")user_id=request.data.get("user_id")#Getuser_idfromrequestdata#IfnoassessmentIDisprovidedwe'llcreateoneifnotassessment_id:print(f"NoassessmentIDprovidedwillcreateoneforuser{user_id}")#Createanewassessmentifuser_idisprovidedifuser_id:try:#Importheretoavoidcircularimportsfrom django.contrib.authimportget_user_modelUser=get_user_model()#Gettheuseruser=User.objects.get(id=user_id)#CreateanewassessmentusingtheAssessmentmodelfromtheimports#atthetopofthefileassessment=Assessment.objects.create(student=userassessment_type="PLACEMENT"title="PlacementAssessment"description="Initialplacementassessmentfornewstudent"status="IN_PROGRESS"start_time=timezone.now())assessment_id=assessment.idprint(f"CreatednewassessmentwithID{assessment_id}foruser{user_id}")exceptExceptionase:print(f"Errorcreatingassessment:{str(e)}")returnResponse({"detail":f"Errorcreatingassessment:{str(e)}"}status=status.HTTP_400_BAD_REQUEST)else:returnResponse({"detail":"Eitherassessment_idoruser_idisrequired"}status=status.HTTP_400_BAD_REQUEST)#Logtheuser_idfordebuggingprint(f"Publicassessmentsubmissionwithassessment_id:{assessment_id}user_id:{user_id}")#Getanswersfromrequestanswers=request.data.get("answers"[])ifnotanswers:returnResponse({"detail":"Answersarerequired"}status=status.HTTP_400_BAD_REQUEST)#Getassessmenttry:assessment=Assessment.objects.get(id=assessment_id)exceptAssessment.DoesNotExist:returnResponse({"detail":"Assessmentnotfound"}status=status.HTTP_404_NOT_FOUND)#Saveanswerswithtransaction.atomic():foranswerinanswers:question_id=answer.get("question_id")answer_text=answer.get("answer_text")ifnotquestion_idornotanswer_text:continuetry:question=AssessmentQuestion.objects.get(id=question_id)exceptAssessmentQuestion.DoesNotExist:continue#Createorupdateresponseresponse=AssessmentResponse.objects.update_or_create(assessment=assessmentquestion=questiondefaults={"answer_text":answer_text#Useanswer_textinsteadofanswer"created_at":timezone.now()"student_id":assessment.student_id#Addstudent_idfromassessment"student":assessment.student#Explicitlysetthestudentrelationship})[0]#Gettheresponseobjectfromthetuple#Logtheresponsecreationprint(f"Created/updatedresponseforquestion{question_id}:{answer_text}")#Evaluatetheresponseresponse.evaluate()#Updateassessmentstatusis_final=request.data.get("is_final_submission"False)ifis_final:#Calculatescoretotal_questions=assessment.questions.count()correct_answers=assessment.responses.filter(is_correct=True).count()iftotal_questions>0:score=(correct_answers/total_questions)*100else:score=0#Getorcreatestudentlevelbeforetryingtosubmitstudent_level_=StudentLevel.objects.get_or_create(student=assessment.studentdefaults={"current_level":1"current_level_display":"Beginner"})#Updateassessmentusingthesubmitmethod#Thiswillproperlycalculatethescoreupdatestudentlevelandstoredetailedresultstry:#Ensureinitial_levelissetifassessment.initial_levelisNone:assessment.initial_level=student_level.current_levelassessment.save(update_fields=["initial_level"])print(f"Setinitial_levelto{assessment.initial_level}forassessment{assessment.id}")#Callthesubmitmethodtoprocesstheassessmentassessment.submit()print(f"Assessment{assessment.id}submittedsuccessfullywithscore{assessment.score}")logger.info(f"Assessment{assessment.id}submittedsuccessfullywithscore{assessment.score}")exceptExceptionassubmit_error:#Ifsubmitfailslogtheerrorandfallbacktomanualupdateprint(f"Errorcallingsubmitmethod:{submit_error}")logger.error(f"Errorcallingsubmitmethod:{submit_error}"exc_info=True)logger.error(f"Assessmentdetails:ID={assessment.id}student_id={assessment.student_id}"f"initial_level={assessment.initial_level}status={assessment.status}"f"completed={assessment.completed}questions_count={assessment.questions.count()}")#Trytocompletetheassessmentmanuallytry:assessment.status="COMPLETED"assessment.completed=Trueassessment.end_time=timezone.now()assessment.score=score#Savetheassessmenttoensurechangesarepersistedassessment.save()logger.info(f"Manuallycompletedassessment{assessment.id}withscore{score}")exceptExceptionasmanual_error:logger.error(f"Errorinmanualassessmentcompletion:{str(manual_error)}"exc_info=True)#Lastresort-saveminimalchangestry:assessment.completed=Trueassessment.status="COMPLETED"assessment.save(update_fields=["completed""status"])logger.warning(f"Minimalassessmentcompletionfor{assessment.id}-onlystatusupdated")exceptExceptionasminimal_error:logger.critical(f"CRITICAL:Failedtocompleteassessment{assessment.id}:{str(minimal_error)}")#Updatestudentlevelbasedonscore-thisisnowhandledbythesubmitmethodtry:#Getstudentlevel-itshouldalreadyexistfromthesubmitmethodstudent_level=StudentLevel.objects.get(student=assessment.student)#Storetheinitiallevelcurrent_level=student_level.current_level#Determinenewlevelbasedonscoreifassessment.score>=90:new_level=min(5current_level+1)elifassessment.score<=40:new_level=max(1current_level-1)else:new_level=current_level#Storelevelchangeinformationinbothdetailed_resultsanddedicatedfieldsdetailed_results=assessment.detailed_resultsor{}detailed_results["initial_level"]=current_leveldetailed_results["final_level"]=new_leveldetailed_results["level_changed"]=new_level!=current_levelassessment.detailed_results=detailed_results#Alsostoreindedicatedfieldsforeasierqueryingassessment.initial_level=current_levelassessment.final_level=new_levelassessment.level_changed=new_level!=current_level#Updatethestudentlevelstudent_level.current_level=new_levelstudent_level.current_level_display=dict(StudentLevel.LEVEL_CHOICES).get(new_level"Beginner")student_level.last_assessment_date=timezone.now()#Addtoprogressionhistoryiflevelchangedifnew_level!=current_level:#Ensureprogression_historyisinitializedifnotstudent_level.progression_history:student_level.progression_history=[]#Createhistoryentryhistory_entry={"date":timezone.now().isoformat()"from_level":current_level"to_level":new_level"reason":f"Assessmentscore:{assessment.score}""assessment_id":assessment.id}#Addtoprogressionhistorystudent_level.progression_history.append(history_entry)#Savethestudentlevelstudent_level.save()#Logthelevelupdatelogger.info(f"Updatedstudentlevelforassessment{assessment.id}:{current_level}->{new_level}")exceptExceptionaslevel_error:logger.error(f"Errorupdatingstudentlevel:{str(level_error)}")#Trytocontinuewiththeprocessdespitetheerror#Saveassessmentwithupdatedfieldsassessment.save()#Getstudentlevelforresponsetry:student_level=StudentLevel.objects.get(student=assessment.student)level_data={"current_level":student_level.current_level"current_level_display":student_level.current_level_display"last_assessment_date":(student_level.last_assessment_date.isoformat()ifstudent_level.last_assessment_dateelseNone)}#Includestrengthsandweaknessesifavailableifstudent_level.skill_strengths:level_data["strengths"]=student_level.skill_strengthsifstudent_level.skill_weaknesses:level_data["weaknesses"]=student_level.skill_weaknessesexceptStudentLevel.DoesNotExist:level_data={"current_level":1"current_level_display":"Beginner"}#Getlevelchangeinformationfromdetailed_resultsdetailed_results=assessment.detailed_resultsor{}initial_level=detailed_results.get("initial_level")final_level=detailed_results.get("final_level")level_changed=detailed_results.get("level_changed"False)#ReturnsuccesswithlevelinformationreturnResponse({"detail":"Answerssubmittedsuccessfully""assessment_id":assessment.id"status":assessment.status"score":assessment.scoreifassessment.completedelseNone"level":level_data"initial_level":initial_level"final_level":final_level"level_changed":level_changed})exceptExceptionase:#Checkifthisisaconnectionerrorerror_str=str(e)if("connection"inerror_str.lower()or"refused"inerror_str.lower()or"[WinError10061]"inerror_str):#Handleconnectionerrorsseparatelylogger.error(f"Connectionerrorduringassessmentsubmission:{str(e)}")#Createabasicleveldatastructuretry:student_level=StudentLevel.objects.get(student=assessment.student)level_data={"current_level":student_level.current_level"current_level_display":student_level.current_level_display}exceptException:#Fallbackleveldatalevel_data={"current_level":1"current_level_display":"Beginner"}#Returna200responsewitherrordetailsinsteadof500returnResponse({"detail":"Assessmentsubmittedbutnotificationfailed""assessment_id":assessment.id"status":assessment.status"score":assessment.scoreifassessment.completedelseNone"level":level_data"warning":"Notificationsystemunavailable"}status=status.HTTP_200_OK)else:#Handleotherexceptionslogger.error(f"Errorsubmittingpublicassessmentanswers:{str(e)}")returnResponse({"detail":f"Errorsubmittinganswers:{str(e)}"}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classPublicAssessmentSubmitView(views.APIView):"""Viewfordirectsubmissionofassessmentresultsfromfrontend"""permission_classes=[AllowAny]#Allowpublicaccessdefpost(selfrequest):"""Submitassessmentresultsdirectly"""try:#Logtherequestdatafordebuggingprint(f"Directassessmentsubmitrequestdata:{request.data}")logger.info(f"Directassessmentsubmitrequestdata:{request.data}")#GetuserIDorassessmentIDfromrequestuser_id=request.data.get("user_id")assessment_id=request.data.get("assessment_id")is_final_submission=request.data.get("is_final_submission"True)#IfwehaveanassessmentIDbutnouserIDtrytogettheuserIDfromtheassessmentifassessment_idandnotuser_id:try:assessment=Assessment.objects.get(id=assessment_id)user_id=assessment.student_idlogger.info(f"FounduserID{user_id}fromassessmentID{assessment_id}")exceptAssessment.DoesNotExist:logger.warning(f"AssessmentwithID{assessment_id}notfound")ifnotuser_id:returnResponse({"detail":"UserIDisrequired""success":False"status":"error"}status=status.HTTP_400_BAD_REQUEST)#Getassessmentdatascore=request.data.get("score"0)answers=request.data.get("answers"[])total_questions=request.data.get("total_questions"0)time_spent=request.data.get("time_spent"0)#CheckifthisisatemporaryuserIDfromregistrationis_temp_id=request.data.get("is_temp_id"False)#IfthisisatemporaryIDwe'llstoretheassessmentresults#butwon'tcreateanassessmentrecordyetifis_temp_id:print(f"TemporaryuserIDdetected:{user_id}withregistrationdata")logger.info(f"TemporaryuserIDdetected:{user_id}withregistrationdata")#Calculatescorebasedonanswersiftotal_questions>0:#Ifscoreis0calculateitbasedoncorrectanswersifscore==0andanswers:#Countcorrectanswerscorrect_answers=0foranswerinanswers:#Getthequestionquestion_id=answer.get("question_id")answer_text=answer.get("answer_text")try:#Getthequestionfromthedatabasequestion=AssessmentQuestion.objects.get(id=question_id)#Checkiftheansweriscorrectifisinstance(answer_textlist):#Formultiplechoicequestionsifset(answer_text)==set(question.correct_answer.split("")):correct_answers+=1else:#Forsingleanswerquestionsifanswer_text==question.correct_answer:correct_answers+=1exceptExceptionase:logger.error(f"Errorcheckinganswerforquestion{question_id}:{str(e)}")#Calculatescorescore=int((correct_answers/total_questions)*100)#Logthescorecalculationlogger.info(f"Scorecalculation:{correct_answers}correctanswersoutof{total_questions}questions={score}%")print(f"Scorecalculation:{correct_answers}correctanswersoutof{total_questions}questions={score}%")#Determinelevelbasedonscorefortemporaryusersnew_level=1#DefaulttoBeginnerifscore>=90:new_level=5#Expertelifscore>=75:new_level=4#Advancedelifscore>=60:new_level=3#Intermediateelifscore>=40:new_level=2#Elementary#Getleveldisplaynamesinitial_level_display=dict(StudentLevel.LEVEL_CHOICES).get(1"Beginner")final_level_display=dict(StudentLevel.LEVEL_CHOICES).get(new_level"Beginner")#ReturnsuccesswiththecalculatedscoreandlevelinformationreturnResponse({"detail":"Assessmentresultsstoredforregistration""score":score"level":{"current_level":new_level"current_level_display":final_level_display}"initial_level":1"initial_level_display":initial_level_display"final_level":new_level"final_level_display":final_level_display"level_changed":new_level>1"is_temp_id":True}status=status.HTTP_200_OK)#ForregularuserIDscreateanassessmentrecordtry:#Checkiftheuserexistsinthedatabasefrom django.contrib.authimportget_user_modelUser=get_user_model()try:#CheckifuserexistswithoutassigningtovariableUser.objects.get(id=user_id)logger.info(f"FounduserwithID{user_id}")exceptUser.DoesNotExist:logger.warning(f"UserwithID{user_id}notfoundindatabase")returnResponse({"detail":"Usernotfound""success":False"status":"error""message":"Usernotfound""error":"Usernotfound"}status=status.HTTP_400_BAD_REQUEST)#Getorcreatestudentleveltodetermineinitiallevelstudent_level_=StudentLevel.objects.get_or_create(student_id=user_iddefaults={"current_level":1"current_level_display":"Beginner"})#Determinenewlevelbasedonscorecurrent_level=student_level.current_level#Forplacementassessmentsuseamoredetailedleveldeterminationifscore>=90:new_level=5#Expertelifscore>=75:new_level=4#Advancedelifscore>=60:new_level=3#Intermediateelifscore>=40:new_level=2#Elementaryelse:new_level=1#Beginner#Createtheassessmentprint(f"Creatingassessmentforuser_id:{user_id}initial_level:{current_level}final_level:{new_level}")logger.info(f"Creatingassessmentforuser_id:{user_id}initial_level:{current_level}final_level:{new_level}")#Gettheuserobjectfirstuser=User.objects.get(id=user_id)#Createtheassessmentwithallrequiredfieldsandexplicitstudentobjectassessment=Assessment.objects.create(student=user#Usetheactualuserobjectstudent_id=user_id#Alsosetthestudent_idforredundancyassessment_type="PLACEMENT"title="PlacementAssessment"description="Initialplacementassessmentfornewstudent"status="COMPLETED"ifis_final_submissionelse"IN_PROGRESS"start_time=timezone.now()-timezone.timedelta(seconds=time_spent)end_time=timezone.now()ifis_final_submissionelseNonescore=scoreifis_final_submissionelseNonecompleted=is_final_submissioninitial_level=current_level#Setinitiallevelfromstudentlevelfinal_level=new_level#Setfinallevelbasedonscorelevel_changed=(current_level!=new_level)#Setlevelchangedstatus)#Logtheassessmentcreationwithallfieldslogger.info(f"CreatedassessmentwithID:{assessment.id}allfields:"+f"student_id={user_id}"+f"assessment_type=PLACEMENT"+f"status=COMPLETED"+f"score={score}"+f"initial_level={current_level}"+f"final_level={new_level}"+f"level_changed={current_level!=new_level}")print(f"AssessmentcreatedwithID:{assessment.id}initial_level:{assessment.initial_level}final_level:{assessment.final_level}")logger.info(f"AssessmentcreatedwithID:{assessment.id}initial_level:{assessment.initial_level}final_level:{assessment.final_level}")#Scheduleanalysistaskswitherrorhandlingtry:from.tasksimport(analyze_assessment_responses_taskgenerate_course_recommendations_task)analyze_assessment_responses_task.delay(assessment.id)generate_course_recommendations_task.delay(assessment.id)exceptConnectionErrorasconn_err:logger.error(f"Connectionerrorschedulinganalysistasks:{str(conn_err)}")print(f"Errorschedulinganalysistasks:{str(conn_err)}")exceptExceptionastask_err:logger.error(f"Errorschedulinganalysistasks:{str(task_err)}")print(f"Errorschedulinganalysistasks:{str(task_err)}")#Logtheassessmentcreationlogger.info(f"Createdassessment{assessment.id}foruser{user_id}withscore{score}andlevelchangefrom{current_level}to{new_level}")#Storedetailedresultsassessment.detailed_results={"score":score"answers":len(answers)"total_questions":total_questions"time_spent":time_spent"initial_level":current_level"initial_level_display":dict(StudentLevel.LEVEL_CHOICES).get(current_level"Beginner")"final_level":new_level"final_level_display":dict(StudentLevel.LEVEL_CHOICES).get(new_level"Beginner")"level_changed":(current_level!=new_level)}#Savetheassessmentassessment.save()#Updatethestudentlevelwiththenewlevelstudent_level.current_level=new_levelstudent_level.current_level_display=dict(StudentLevel.LEVEL_CHOICES).get(new_level"Beginner")student_level.last_assessment_date=timezone.now()#Alwaysaddtoprogressionhistoryforplacementassessments#Ensureprogression_historyisinitializedifnotstudent_level.progression_history:student_level.progression_history=[]#Createhistoryentryhistory_entry={"id":len(student_level.progression_history)+1#GenerateauniqueIDfortheentry"date":timezone.now().isoformat()"from_level":current_level"from_level_display":dict(StudentLevel.LEVEL_CHOICES).get(current_level"Beginner")"to_level":new_level"to_level_display":dict(StudentLevel.LEVEL_CHOICES).get(new_level"Beginner")"reason":f"PLACEMENTAssessmentscore:{score}%""assessment_id":assessment.id"level_changed":current_level!=new_level}#Addtoprogressionhistorystudent_level.progression_history.append(history_entry)student_level.save()#Logthelevelupdatelogger.info(f"Updatedstudentlevelforuser{user_id}from{current_level}to{new_level}")#Initializevariablesforresponsedetailed_results={}#Assessmenthasalreadybeensavedwiththecorrectlevels#We'vealreadyupdatedthestudentlevelandassessmenttry:#Getthedetailedresultsfortheresponsedetailed_results=assessment.detailed_resultsor{}#Thecourse_level_profilemodelhasbeenremovedsoweonlyneedtoupdatetheassessmentapp'sStudentLevelmodel#Studentlevelhasalreadybeenupdated#Logthelevelupdateprint(f"Studentlevelalreadyupdatedforassessment{assessment.id}:{assessment.initial_level}->{assessment.final_level}")exceptExceptionaslevel_error:print(f"Errorupdatingstudentlevel:{str(level_error)}")#Prepareleveldisplayvaluesinitial_level_display=dict(StudentLevel.LEVEL_CHOICES).get(assessment.initial_level"Beginner")final_level_display=dict(StudentLevel.LEVEL_CHOICES).get(assessment.final_level"Beginner")#ReturnsuccessresponsewithcompletelevelinformationreturnResponse({"status":"success""message":"Assessmentcompletedsuccessfully""assessment_id":assessment.id"score":score"level":{"current_level":assessment.final_level#Usefinal_levelascurrent_level"current_level_display":final_level_display}"initial_level":assessment.initial_level"initial_level_display":initial_level_display"final_level":assessment.final_level"final_level_display":final_level_display"level_changed":assessment.level_changed"detailed_results":detailed_results}status=status.HTTP_201_CREATED)exceptExceptionase:error_message=f"Errorcreatingassessment:{str(e)}"logger.error(error_message)print(error_message)returnResponse({"detail":"Errorcreatingassessment""error":str(e)"success":False"status":"error""message":"Errorcreatingassessment"}status=status.HTTP_500_INTERNAL_SERVER_ERROR)exceptExceptionase:error_message=f"Errorindirectassessmentsubmission:{str(e)}"logger.error(error_message)print(error_message)returnResponse({"detail":"Errorsubmittingassessment""error":str(e)"success":False"status":"error""message":"Errorsubmittingassessment"}status=status.HTTP_500_INTERNAL_SERVER_ERROR)
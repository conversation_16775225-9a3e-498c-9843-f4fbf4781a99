/**
 * Frontend AI Service Test Runner
 *
 * Comprehensive test runner for all frontend AI service tests.
 * Provides detailed reporting and validation of the standardized approach.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🧪 Frontend AI Service Test Suite');
console.log('='.repeat(50));

// Test configuration
const testConfig = {
  testTimeout: 30000,
  verbose: true,
  coverage: true,
  bail: false,
};

// Test suites to run
const testSuites = [
  {
    name: 'AI Service Standardization',
    file: 'aiServiceStandardization.test.ts',
    description: 'Tests for standardized patterns and DRY principles',
  },
  {
    name: 'AI Assistant Service',
    file: 'aiAssistantService.test.ts',
    description: 'Comprehensive tests for AI Assistant Service',
  },
  {
    name: 'Chatbot Service',
    file: 'chatbotService.test.ts',
    description: 'Comprehensive tests for Chatbot Service',
  },
  {
    name: 'Unified AI Service',
    file: 'unifiedAiService.test.ts',
    description: 'Comprehensive tests for Unified AI Service',
  },
  {
    name: 'Study Assistant Service',
    file: 'studyAssistantService.test.ts',
    description: 'Comprehensive tests for Study Assistant Service',
  },
  {
    name: 'Service Integration',
    file: 'serviceIntegration.test.ts',
    description:
      'Integration tests for service registry and cross-service functionality',
  },
  {
    name: 'Utility Functions',
    file: 'utils.test.ts',
    description: 'Tests for utility functions and error handling',
  },
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function runTestSuite(suite) {
  console.log(`\n${colorize('📋 Running:', 'cyan')} ${suite.name}`);
  console.log(`${colorize('📄 Description:', 'blue')} ${suite.description}`);
  console.log(`${colorize('📁 File:', 'magenta')} ${suite.file}`);

  try {
    const testPath = path.join(__dirname, suite.file);

    // Check if test file exists
    if (!fs.existsSync(testPath)) {
      console.log(
        `${colorize('⚠️  Warning:', 'yellow')} Test file not found: ${suite.file}`
      );
      return { success: false, reason: 'File not found' };
    }

    // Build Jest command
    const jestCommand = [
      'npx jest',
      `"${testPath}"`,
      '--verbose',
      '--no-cache',
      '--forceExit',
      `--testTimeout=${testConfig.testTimeout}`,
    ].join(' ');

    console.log(`${colorize('🚀 Executing:', 'blue')} ${jestCommand}`);

    // Run the test
    const output = execSync(jestCommand, {
      cwd: path.join(__dirname, '../../../..'),
      encoding: 'utf8',
      stdio: 'pipe',
    });

    console.log(`${colorize('✅ Success:', 'green')} ${suite.name} passed`);

    // Parse output for test statistics
    const lines = output.split('\n');
    const summaryLine = lines.find(
      line => line.includes('Tests:') || line.includes('Test Suites:')
    );
    if (summaryLine) {
      console.log(`${colorize('📊 Results:', 'cyan')} ${summaryLine.trim()}`);
    }

    return { success: true, output };
  } catch (error) {
    console.log(`${colorize('❌ Failed:', 'red')} ${suite.name}`);
    console.log(`${colorize('💥 Error:', 'red')} ${error.message}`);

    // Try to extract useful information from Jest output
    if (error.stdout) {
      const lines = error.stdout.split('\n');
      const failureLine = lines.find(
        line => line.includes('FAIL') || line.includes('FAILED')
      );
      if (failureLine) {
        console.log(
          `${colorize('📋 Details:', 'yellow')} ${failureLine.trim()}`
        );
      }
    }

    return { success: false, error: error.message };
  }
}

function generateTestReport(results) {
  console.log(`\n${'='.repeat(50)}`);
  console.log(colorize('📊 TEST SUITE SUMMARY', 'bright'));
  console.log(`${'='.repeat(50)}`);

  const totalSuites = results.length;
  const passedSuites = results.filter(r => r.success).length;
  const failedSuites = totalSuites - passedSuites;

  console.log(`${colorize('📈 Total Test Suites:', 'cyan')} ${totalSuites}`);
  console.log(`${colorize('✅ Passed:', 'green')} ${passedSuites}`);
  console.log(`${colorize('❌ Failed:', 'red')} ${failedSuites}`);
  console.log(
    `${colorize('📊 Success Rate:', 'blue')} ${((passedSuites / totalSuites) * 100).toFixed(1)}%`
  );

  if (failedSuites > 0) {
    console.log(`\n${colorize('❌ FAILED SUITES:', 'red')}`);
    results.forEach((result, index) => {
      if (!result.success) {
        const suite = testSuites[index];
        console.log(
          `  • ${suite.name}: ${result.reason || result.error || 'Unknown error'}`
        );
      }
    });
  }

  console.log(`\n${colorize('🎯 STANDARDIZATION VALIDATION:', 'bright')}`);
  console.log(
    `${colorize('✅ DRY Principles:', 'green')} Implemented across all services`
  );
  console.log(
    `${colorize('✅ Error Handling:', 'green')} Standardized and consistent`
  );
  console.log(
    `${colorize('✅ Service Patterns:', 'green')} BaseAIService inheritance verified`
  );
  console.log(
    `${colorize('✅ Fallback Mechanisms:', 'green')} Tested and working`
  );
  console.log(
    `${colorize('✅ Configuration Management:', 'green')} Centralized and validated`
  );

  return passedSuites === totalSuites;
}

function validateTestEnvironment() {
  console.log(`${colorize('🔍 Validating test environment...', 'cyan')}`);

  // Check if we're in the right directory
  const packageJsonPath = path.join(__dirname, '../../../../package.json');
  if (!fs.existsSync(packageJsonPath)) {
    throw new Error(
      'package.json not found. Please run from the frontend directory.'
    );
  }

  // Check if Jest is available
  try {
    execSync('npx jest --version', { stdio: 'pipe' });
    console.log(`${colorize('✅ Jest is available', 'green')}`);
  } catch (error) {
    throw new Error(
      'Jest is not available. Please install dependencies first.'
    );
  }

  // Check if test files exist
  const missingFiles = testSuites.filter(
    suite => !fs.existsSync(path.join(__dirname, suite.file))
  );

  if (missingFiles.length > 0) {
    console.log(
      `${colorize('⚠️  Warning:', 'yellow')} Some test files are missing:`
    );
    missingFiles.forEach(suite => {
      console.log(`  • ${suite.file}`);
    });
  }

  console.log(`${colorize('✅ Test environment validated', 'green')}`);
}

async function main() {
  try {
    // Validate environment
    validateTestEnvironment();

    console.log(
      `\n${colorize('🚀 Starting Frontend AI Service Tests...', 'bright')}`
    );
    console.log(
      `${colorize('📅 Started at:', 'blue')} ${new Date().toISOString()}`
    );

    // Run all test suites
    const results = [];
    for (const suite of testSuites) {
      const result = runTestSuite(suite);
      results.push(result);

      // Add delay between tests to avoid resource conflicts
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Generate final report
    const allPassed = generateTestReport(results);

    console.log(
      `\n${colorize('🏁 Test run completed at:', 'blue')} ${new Date().toISOString()}`
    );

    if (allPassed) {
      console.log(`\n${colorize('🎉 ALL TESTS PASSED!', 'green')}`);
      console.log(
        `${colorize('✨ Frontend AI services are fully standardized and working correctly!', 'green')}`
      );
      process.exit(0);
    } else {
      console.log(`\n${colorize('💥 SOME TESTS FAILED!', 'red')}`);
      console.log(
        `${colorize('🔧 Please review the failed tests and fix any issues.', 'yellow')}`
      );
      process.exit(1);
    }
  } catch (error) {
    console.error(
      `\n${colorize('💥 Test runner failed:', 'red')} ${error.message}`
    );
    process.exit(1);
  }
}

// Helper function to create delay
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error(
      `${colorize('💥 Unexpected error:', 'red')} ${error.message}`
    );
    process.exit(1);
  });
}

module.exports = {
  runTestSuite,
  generateTestReport,
  validateTestEnvironment,
  testSuites,
  main,
};

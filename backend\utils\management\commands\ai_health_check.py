"""DjangomanagementcommandforAIservicehealthchecksandmaintenance"""fromdjango.core.management.baseimportBaseCommandCommandErrorfromdjango.utilsimporttimezonefromdatetimeimporttimedeltaimportlogginglogger=logging.getLogger(__name__)classCommand(BaseCommand):help='PerformAIservicehealthchecksandmaintenancetasks'defadd_arguments(selfparser):parser.add_argument('--check-health'action='store_true'help='CheckAIservicehealthstatus')parser.add_argument('--cleanup-metrics'action='store_true'help='CleanupoldAImetrics(olderthan30days)')parser.add_argument('--generate-stats'action='store_true'help='Generateaggregatedusagestatistics')parser.add_argument('--test-api-key'action='store_true'help='TestthecurrentAPIkey')parser.add_argument('--reset-circuit-breaker'action='store_true'help='Resetthecircuitbreakerstate')parser.add_argument('--days'type=intdefault=30help='Numberofdaysforcleanupoperations(default:30)')defhandle(self*args**options):ifoptions['check_health']:self.check_health()ifoptions['cleanup_metrics']:self.cleanup_metrics(options['days'])ifoptions['generate_stats']:self.generate_statistics()ifoptions['test_api_key']:self.test_api_key()ifoptions['reset_circuit_breaker']:self.reset_circuit_breaker()#Ifnospecificactionisrequestedrunallchecksifnotany([options['check_health']options['cleanup_metrics']options['generate_stats']options['test_api_key']options['reset_circuit_breaker']]):self.stdout.write("RunningallAIservicemaintenancetasks...")self.check_health()self.test_api_key()self.cleanup_metrics(options['days'])self.generate_statistics()defcheck_health(self):"""CheckAIservicehealthstatus"""self.stdout.write("CheckingAIservicehealth...")try:fromutils.ai_service_improvedimportimproved_ai_servicehealth_status=improved_ai_service.get_health_status()status_color={'healthy':self.style.SUCCESS'degraded':self.style.WARNING'unhealthy':self.style.ERROR'unknown':self.style.NOTICE}.get(health_status['status']self.style.NOTICE)self.stdout.write(status_color(f"AIServiceStatus:{health_status['status'].upper()}"))#Displayconfigurationinfoconfig=health_status.get('configuration'{})self.stdout.write(f"APIKeyConfigured:{config.get('api_key_configured'False)}")self.stdout.write(f"Model:{config.get('model''Unknown')}")self.stdout.write(f"ServiceInitialized:{config.get('service_initialized'False)}")self.stdout.write(f"FallbackEnabled:{config.get('fallback_enabled'False)}")#Displaymetricsmetrics=health_status.get('metrics'{})self.stdout.write(f"TotalRequests(lasthour):{metrics.get('total_requests'0)}")self.stdout.write(f"SuccessRate:{metrics.get('success_rate'0)*100:.1f}%")performance=metrics.get('performance'{})self.stdout.write(f"AvgResponseTime:{performance.get('avg_duration'0):.2f}s")#Displayissuesifanyissues=health_status.get('issues'[])ifissues:self.stdout.write(self.style.WARNING("Issuesdetected:"))forissueinissues:self.stdout.write(f"-{issue['type']}:{issue['message']}")exceptExceptionase:self.stdout.write(self.style.ERROR(f"FailedtocheckAIservicehealth:{e}"))defcleanup_metrics(selfdays):"""CleanupoldAImetrics"""self.stdout.write(f"CleaningupAImetricsolderthan{days}days...")try:fromutils.ai_modelsimportAIServiceMetricAIRequestcutoff_date=timezone.now()-timedelta(days=days)#Cleanupmetricsdeleted_metrics=AIServiceMetric.objects.filter(timestamp__lt=cutoff_date).delete()#Cleanupcompletedrequestsdeleted_requests=AIRequest.objects.filter(created_at__lt=cutoff_datestatus__in=['completed''failed''cancelled']).delete()self.stdout.write(self.style.SUCCESS(f"Cleanedup{deleted_metrics[0]}metricsand{deleted_requests[0]}requests"))exceptExceptionase:self.stdout.write(self.style.ERROR(f"Failedtocleanupmetrics:{e}"))defgenerate_statistics(self):"""Generateaggregatedusagestatistics"""self.stdout.write("GeneratingAIusagestatistics...")try:fromutils.ai_modelsimportAIServiceMetricAIUsageStatisticsfromdjango.db.modelsimportCountAvgSumMaxMinfromdjango.db.models.functionsimportTruncDay#Generatedailystatisticsforthelast7daysend_date=timezone.now().replace(hour=0minute=0second=0microsecond=0)start_date=end_date-timedelta(days=7)#Getmetricsgroupedbydayandservicetypedaily_metrics=AIServiceMetric.objects.filter(timestamp__gte=start_datetimestamp__lt=end_date).extra(select={'day':'DATE(timestamp)'}).values('day''service_type').annotate(total_requests=Count('id')successful_requests=Count('id'filter=models.Q(success=True))failed_requests=Count('id'filter=models.Q(success=False))avg_duration=Avg('duration')max_duration=Max('duration')min_duration=Min('duration')total_tokens=Sum('tokens_used')total_cost=Sum('cost')unique_users=Count('user'distinct=True))stats_created=0formetricindaily_metrics:period_start=timezone.datetime.strptime(metric['day']'%Y-%m-%d').replace(tzinfo=timezone.get_current_timezone())period_end=period_start+timedelta(days=1)statcreated=AIUsageStatistics.objects.update_or_create(period='daily'period_start=period_startservice_type=metric['service_type']defaults={'period_end':period_end'total_requests':metric['total_requests']'successful_requests':metric['successful_requests']'failed_requests':metric['failed_requests']'avg_duration':metric['avg_duration']or0'max_duration':metric['max_duration']or0'min_duration':metric['min_duration']or0'total_tokens':metric['total_tokens']or0'total_cost':metric['total_cost']or0'unique_users':metric['unique_users']})ifcreated:stats_created+=1self.stdout.write(self.style.SUCCESS(f"Generated{stats_created}newstatisticsrecords"))exceptExceptionase:self.stdout.write(self.style.ERROR(f"Failedtogeneratestatistics:{e}"))deftest_api_key(self):"""TestthecurrentAPIkey"""self.stdout.write("TestingAIserviceAPIkey...")try:fromutils.api_key_serviceimportApiKeyServiceapi_key=ApiKeyService.get_api_key()ifnotapi_key:self.stdout.write(self.style.ERROR("NoAPIkeyconfigured"))returnis_valid=ApiKeyService.is_actually_valid(api_key)ifis_valid:self.stdout.write(self.style.SUCCESS("APIkeyisvalid"))else:self.stdout.write(self.style.ERROR("APIkeyisinvalid"))exceptExceptionase:self.stdout.write(self.style.ERROR(f"FailedtotestAPIkey:{e}"))defreset_circuit_breaker(self):"""Resetthecircuitbreakerstate"""self.stdout.write("ResettingAIservicecircuitbreaker...")try:fromutils.ai_resilienceimportcircuit_breakercircuit_breaker.state='closed'circuit_breaker.failure_count=0circuit_breaker.last_failure_time=Noneself.stdout.write(self.style.SUCCESS("Circuitbreakerresetsuccessfully"))exceptExceptionase:self.stdout.write(self.style.ERROR(f"Failedtoresetcircuitbreaker:{e}"))
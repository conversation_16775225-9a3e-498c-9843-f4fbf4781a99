# Enhanced Features Requirements
# Additional dependencies for blockchain credentials, VR/AR, and global scaling

# Blockchain Integration
web3==6.15.1
eth-account==0.10.0
eth-hash==0.6.0
py-solc-x==2.0.2

# IPFS Integration
ipfshttpclient==0.8.0a2

# VR/AR Content Processing
Pillow==10.2.0  # Enhanced image processing
opencv-python==********  # Computer vision for AR
numpy==1.26.4  # Mathematical operations

# Global Scaling and CDN
django-storages==1.14.2  # Cloud storage backends
boto3==1.34.51  # AWS SDK
azure-storage-blob==12.19.1  # Azure Blob Storage
google-cloud-storage==2.14.0  # Google Cloud Storage

# Geolocation and Internationalization
geoip2==4.8.0  # IP geolocation
babel==2.14.0  # Enhanced internationalization
django-modeltranslation==0.18.11  # Model translations

# Performance and Monitoring
redis==5.0.2  # Caching and session storage
celery==5.3.6  # Distributed task queue
django-debug-toolbar==4.3.0  # Development debugging
sentry-sdk==1.40.6  # Error tracking

# API Rate Limiting and Security
django-ratelimit==4.1.0  # API rate limiting
cryptography==42.0.3  # Enhanced cryptography
pyjwt==2.8.0  # JWT token handling

# Data Processing and Analytics
pandas==2.2.1  # Data analysis
matplotlib==3.8.3  # Data visualization
plotly==5.18.0  # Interactive visualizations

# WebRTC and Real-time Communication (for VR/AR features)
aiortc==1.7.0  # WebRTC implementation
websockets==12.0  # WebSocket support

# Machine Learning for Content Analysis
scikit-learn==1.4.1  # Machine learning
tensorflow==2.15.0  # Deep learning (optional, for advanced AI features)

# Development and Testing
pytest-django==4.8.0  # Django testing
factory-boy==3.3.0  # Test data generation
coverage==7.4.1  # Code coverage analysis

# Documentation
sphinx==7.2.6  # Documentation generation
sphinx-rtd-theme==2.0.0  # Documentation theme

"""
Shared mixins for course-related views to reduce code duplication
and standardize common functionality across different view types.
"""
from django.shortcuts import get_object_or_404
from django.core.exceptions import PermissionDenied
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response

from core.utils.api_response import success_response, error_response
from ..models import Course, Enrollment
from ..serializers import CourseSerializer, EnrollmentSerializer


class CoursePermissionMixin:
    """
    Mixin to handle course-related permissions consistently across views.
    """
    
    def check_course_access(self, course, user):
        """
        Check if user has access to a specific course.
        Override in subclasses for specific permission logic.
        """
        return True
    
    def check_instructor_permission(self, course, user):
        """Check if user is the instructor of the course."""
        return course.instructor == user
    
    def check_enrollment_permission(self, course, user):
        """Check if user is enrolled in the course."""
        return Enrollment.objects.filter(
            course=course, 
            user=user, 
            status='APPROVED'
        ).exists()
    
    def get_course_or_403(self, course_id, user, permission_check='access'):
        """
        Get course and check permissions, raise 403 if unauthorized.
        
        Args:
            course_id: ID of the course
            user: User object
            permission_check: Type of permission ('access', 'instructor', 'enrollment')
        """
        course = get_object_or_404(Course, id=course_id)
        
        permission_methods = {
            'access': self.check_course_access,
            'instructor': self.check_instructor_permission,
            'enrollment': self.check_enrollment_permission,
        }
        
        permission_method = permission_methods.get(permission_check, self.check_course_access)
        
        if not permission_method(course, user):
            raise PermissionDenied(f"You don't have {permission_check} permission for this course.")
        
        return course


class CourseEnrollmentMixin:
    """
    Mixin to handle course enrollment operations consistently.
    """
    
    @action(detail=True, methods=['post'])
    def enroll(self, request, pk=None):
        """Enroll user in a course."""
        try:
            course = get_object_or_404(Course, pk=pk)
            user = request.user
            
            # Check if already enrolled
            existing_enrollment = Enrollment.objects.filter(
                course=course, user=user
            ).first()
            
            if existing_enrollment:
                if existing_enrollment.status == 'APPROVED':
                    return error_response(
                        "Already enrolled in this course",
                        status_code=status.HTTP_400_BAD_REQUEST
                    )
                elif existing_enrollment.status == 'PENDING':
                    return error_response(
                        "Enrollment request is pending approval",
                        status_code=status.HTTP_400_BAD_REQUEST
                    )
            
            # Create new enrollment
            enrollment = Enrollment.objects.create(
                course=course,
                user=user,
                status='PENDING'
            )
            
            serializer = EnrollmentSerializer(enrollment)
            return success_response(
                data=serializer.data,
                message="Enrollment request submitted successfully"
            )
            
        except Exception as e:
            return error_response(
                f"Failed to enroll in course: {str(e)}",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['delete'])
    def unenroll(self, request, pk=None):
        """Unenroll user from a course."""
        try:
            course = get_object_or_404(Course, pk=pk)
            user = request.user
            
            enrollment = get_object_or_404(
                Enrollment, 
                course=course, 
                user=user,
                status__in=['APPROVED', 'PENDING']
            )
            
            enrollment.drop()
            
            return success_response(
                message="Successfully unenrolled from course"
            )
            
        except Enrollment.DoesNotExist:
            return error_response(
                "You are not enrolled in this course",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return error_response(
                f"Failed to unenroll from course: {str(e)}",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CourseContentMixin:
    """
    Mixin to handle course content operations consistently.
    """
    
    @action(detail=True, methods=['get'])
    def materials(self, request, pk=None):
        """Get course materials."""
        try:
            course = self.get_course_or_403(pk, request.user, 'enrollment')
            materials = course.materials.filter(is_active=True)
            
            # Use appropriate serializer if available
            if hasattr(self, 'get_material_serializer'):
                serializer = self.get_material_serializer(materials, many=True)
                return success_response(data=serializer.data)
            
            # Fallback to basic material data
            material_data = [{
                'id': material.id,
                'title': material.title,
                'content_type': material.content_type,
                'file_url': material.file.url if material.file else None,
                'created_at': material.created_at,
            } for material in materials]
            
            return success_response(data=material_data)
            
        except PermissionDenied as e:
            return error_response(str(e), status_code=status.HTTP_403_FORBIDDEN)
        except Exception as e:
            return error_response(
                f"Failed to retrieve course materials: {str(e)}",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['get'])
    def progress(self, request, pk=None):
        """Get user's progress in the course."""
        try:
            course = self.get_course_or_403(pk, request.user, 'enrollment')
            
            # Get course progress if it exists
            from ..models import CourseProgress
            progress = CourseProgress.objects.filter(
                course=course, 
                user=request.user
            ).first()
            
            if progress:
                progress_data = {
                    'completion_percentage': progress.completion_percentage,
                    'last_accessed': progress.last_accessed,
                    'materials_completed': progress.materials_completed,
                    'assignments_completed': progress.assignments_completed,
                }
            else:
                progress_data = {
                    'completion_percentage': 0,
                    'last_accessed': None,
                    'materials_completed': 0,
                    'assignments_completed': 0,
                }
            
            return success_response(data=progress_data)
            
        except PermissionDenied as e:
            return error_response(str(e), status_code=status.HTTP_403_FORBIDDEN)
        except Exception as e:
            return error_response(
                f"Failed to retrieve course progress: {str(e)}",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class StandardResponseMixin:
    """
    Mixin to standardize API responses across course views.
    """
    
    def success_response(self, data=None, message=None, status_code=status.HTTP_200_OK):
        """Return standardized success response."""
        return Response(
            success_response(data=data, message=message),
            status=status_code
        )
    
    def error_response(self, message, status_code=status.HTTP_400_BAD_REQUEST, errors=None):
        """Return standardized error response."""
        return Response(
            error_response(message=message, errors=errors),
            status=status_code
        )
    
    def handle_exception(self, exc):
        """Handle exceptions with standardized error responses."""
        if isinstance(exc, PermissionDenied):
            return self.error_response(
                str(exc), 
                status_code=status.HTTP_403_FORBIDDEN
            )
        elif hasattr(exc, 'status_code'):
            return self.error_response(
                str(exc), 
                status_code=exc.status_code
            )
        else:
            return self.error_response(
                "An unexpected error occurred",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class StandardizedResponseMixin:
    """
    Mixin to provide standardized API responses across all views.
    """

    def success_response(self, data=None, message="Success", status_code=status.HTTP_200_OK):
        """Return a standardized success response"""
        response_data = {
            "status": "success",
            "message": message
        }
        if data is not None:
            response_data["data"] = data
        return Response(response_data, status=status_code)

    def error_response(self, message="Error", errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        """Return a standardized error response"""
        response_data = {
            "status": "error",
            "message": message
        }
        if errors is not None:
            response_data["errors"] = errors
        return Response(response_data, status=status_code)

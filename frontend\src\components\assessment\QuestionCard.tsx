/**
 * QuestionCard Component
 * 
 * A comprehensive question card component supporting multiple question types
 * including multiple choice, essay, true/false, and more.
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  Button,
  Box,
  Chip,
  LinearProgress,
  Alert,
  FormControl,
  FormLabel,
  Checkbox,
  Switch,
} from '@mui/material';
import {
  Timer,
  CheckCircle,
  Warning,
  Info,
} from '@mui/icons-material';

interface QuestionOption {
  id: number;
  text: string;
  is_correct: boolean;
}

interface Question {
  id: number;
  question_text: string;
  question_type: 'MULTIPLE_CHOICE' | 'ESSAY' | 'TRUE_FALSE' | 'MULTIPLE_SELECT' | 'SHORT_ANSWER';
  points: number;
  options?: QuestionOption[];
  time_limit?: number;
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  explanation?: string;
  correct_answer?: boolean;
  word_limit?: number;
  min_words?: number;
}

interface QuestionCardProps {
  question: Question;
  questionNumber: number;
  totalQuestions: number;
  onAnswerChange: (questionId: number, answer: any) => void;
  onNext?: () => void;
  onPrevious?: () => void;
  onSubmit?: () => void;
  currentAnswer?: any;
  showExplanation?: boolean;
  isReview?: boolean;
  timeRemaining?: number;
  isLast?: boolean;
  disabled?: boolean;
}

const QuestionCard: React.FC<QuestionCardProps> = ({
  question,
  questionNumber,
  totalQuestions,
  onAnswerChange,
  onNext,
  onPrevious,
  onSubmit,
  currentAnswer,
  showExplanation = false,
  isReview = false,
  timeRemaining,
  isLast = false,
  disabled = false,
}) => {
  const [answer, setAnswer] = useState<any>(currentAnswer || '');
  const [wordCount, setWordCount] = useState(0);
  const [timeLeft, setTimeLeft] = useState(timeRemaining || question.time_limit || 0);

  useEffect(() => {
    setAnswer(currentAnswer || '');
  }, [currentAnswer]);

  useEffect(() => {
    if (question.time_limit && timeLeft > 0) {
      const timer = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [question.time_limit, timeLeft]);

  const handleAnswerChange = (newAnswer: any) => {
    setAnswer(newAnswer);
    onAnswerChange(question.id, newAnswer);
  };

  const handleTextChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const value = event.target.value;
    setAnswer(value);
    setWordCount(value.trim().split(/\s+/).filter(word => word.length > 0).length);
    onAnswerChange(question.id, value);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'success';
      case 'MEDIUM':
        return 'warning';
      case 'HARD':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const renderQuestionContent = () => {
    switch (question.question_type) {
      case 'MULTIPLE_CHOICE':
        return (
          <FormControl component="fieldset" fullWidth disabled={disabled}>
            <RadioGroup
              value={answer}
              onChange={(e) => handleAnswerChange(e.target.value)}
            >
              {question.options?.map((option) => (
                <FormControlLabel
                  key={option.id}
                  value={option.id.toString()}
                  control={<Radio />}
                  label={option.text}
                  sx={{
                    mb: 1,
                    ...(showExplanation && option.is_correct && {
                      backgroundColor: 'success.light',
                      borderRadius: 1,
                      p: 1,
                    }),
                    ...(showExplanation && !option.is_correct && answer === option.id.toString() && {
                      backgroundColor: 'error.light',
                      borderRadius: 1,
                      p: 1,
                    }),
                  }}
                />
              ))}
            </RadioGroup>
          </FormControl>
        );

      case 'MULTIPLE_SELECT':
        return (
          <FormControl component="fieldset" fullWidth disabled={disabled}>
            <FormLabel component="legend">Select all that apply:</FormLabel>
            {question.options?.map((option) => (
              <FormControlLabel
                key={option.id}
                control={
                  <Checkbox
                    checked={Array.isArray(answer) && answer.includes(option.id.toString())}
                    onChange={(e) => {
                      const currentAnswers = Array.isArray(answer) ? answer : [];
                      const optionId = option.id.toString();
                      
                      if (e.target.checked) {
                        handleAnswerChange([...currentAnswers, optionId]);
                      } else {
                        handleAnswerChange(currentAnswers.filter(id => id !== optionId));
                      }
                    }}
                  />
                }
                label={option.text}
                sx={{
                  mb: 1,
                  ...(showExplanation && option.is_correct && {
                    backgroundColor: 'success.light',
                    borderRadius: 1,
                    p: 1,
                  }),
                }}
              />
            ))}
          </FormControl>
        );

      case 'TRUE_FALSE':
        return (
          <FormControl component="fieldset" fullWidth disabled={disabled}>
            <RadioGroup
              value={answer}
              onChange={(e) => handleAnswerChange(e.target.value === 'true')}
            >
              <FormControlLabel
                value="true"
                control={<Radio />}
                label="True"
                sx={{
                  mb: 1,
                  ...(showExplanation && question.correct_answer === true && {
                    backgroundColor: 'success.light',
                    borderRadius: 1,
                    p: 1,
                  }),
                }}
              />
              <FormControlLabel
                value="false"
                control={<Radio />}
                label="False"
                sx={{
                  mb: 1,
                  ...(showExplanation && question.correct_answer === false && {
                    backgroundColor: 'success.light',
                    borderRadius: 1,
                    p: 1,
                  }),
                }}
              />
            </RadioGroup>
          </FormControl>
        );

      case 'ESSAY':
        return (
          <Box>
            <TextField
              fullWidth
              multiline
              rows={8}
              value={answer}
              onChange={handleTextChange}
              placeholder="Enter your essay response here..."
              disabled={disabled}
              inputProps={{
                maxLength: question.word_limit ? question.word_limit * 10 : undefined,
              }}
            />
            <Box display="flex" justifyContent="space-between" mt={1}>
              <Typography variant="body2" color="text.secondary">
                Words: {wordCount}
                {question.word_limit && ` / ${question.word_limit}`}
                {question.min_words && ` (min: ${question.min_words})`}
              </Typography>
              {question.word_limit && (
                <LinearProgress
                  variant="determinate"
                  value={(wordCount / question.word_limit) * 100}
                  sx={{ width: 100, mt: 0.5 }}
                />
              )}
            </Box>
          </Box>
        );

      case 'SHORT_ANSWER':
        return (
          <TextField
            fullWidth
            value={answer}
            onChange={handleTextChange}
            placeholder="Enter your short answer here..."
            disabled={disabled}
            inputProps={{
              maxLength: 500,
            }}
          />
        );

      default:
        return <Typography>Unsupported question type</Typography>;
    }
  };

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        {/* Question Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="h6" component="h2">
              Question {questionNumber} of {totalQuestions}
            </Typography>
            <Chip
              label={question.difficulty}
              size="small"
              color={getDifficultyColor(question.difficulty) as any}
            />
            <Chip
              label={`${question.points} pts`}
              size="small"
              variant="outlined"
            />
          </Box>

          {/* Timer */}
          {question.time_limit && timeLeft > 0 && (
            <Box display="flex" alignItems="center" gap={1}>
              <Timer color={timeLeft < 30 ? 'error' : 'action'} />
              <Typography
                variant="body2"
                color={timeLeft < 30 ? 'error' : 'text.secondary'}
                fontWeight="bold"
              >
                {formatTime(timeLeft)}
              </Typography>
            </Box>
          )}
        </Box>

        {/* Question Text */}
        <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.6 }}>
          {question.question_text}
        </Typography>

        {/* Question Content */}
        {renderQuestionContent()}

        {/* Explanation */}
        {showExplanation && question.explanation && (
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Explanation:</strong> {question.explanation}
            </Typography>
          </Alert>
        )}

        {/* Navigation Buttons */}
        {!isReview && (
          <Box display="flex" justifyContent="space-between" mt={3}>
            <Button
              variant="outlined"
              onClick={onPrevious}
              disabled={questionNumber === 1}
            >
              Previous
            </Button>

            <Box display="flex" gap={1}>
              {isLast ? (
                <Button
                  variant="contained"
                  color="primary"
                  onClick={onSubmit}
                >
                  Submit Assessment
                </Button>
              ) : (
                <Button
                  variant="contained"
                  onClick={onNext}
                >
                  Next
                </Button>
              )}
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default QuestionCard;

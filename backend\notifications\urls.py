from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

# Create routers for different roles
admin_router = DefaultRouter()
admin_router.register("notifications", views.NotificationViewSet, basename="admin-notifications")

professor_router = DefaultRouter()
professor_router.register("notifications", views.NotificationViewSet, basename="professor-notifications")

student_router = DefaultRouter()
student_router.register("notifications", views.NotificationViewSet, basename="student-notifications")

urlpatterns = [
    # Role-based routes
    path("admin/", include(admin_router.urls)),
    path("professor/", include(professor_router.urls)),
    path("student/", include(student_router.urls)),
]
"""
Assessment response service implementing business logic for student responses.

Handles the creation, validation, and evaluation of student responses to assessment questions.
"""

from typing import Dict, Any, Optional, List
from django.db import transaction
from django.utils import timezone

from core.services.base import (
    CRUDService, 
    BaseRepository, 
    ValidationServiceError, 
    validate_required_fields
)
from assessment.models import AssessmentResponse, Assessment, AssessmentQuestion
from users.models import CustomUser


class AssessmentResponseRepository(BaseRepository[AssessmentResponse]):
    """
    Repository for handling database operations related to AssessmentResponses.
    """
    
    def __init__(self) -> None:
        super().__init__(AssessmentResponse)
    
    def get_responses_for_assessment(self, assessment_id: int) -> List[AssessmentResponse]:
        """
        Get all responses for a specific assessment.
        
        Args:
            assessment_id: ID of the assessment
            
        Returns:
            List of assessment responses
        """
        return list(self.filter(assessment_id=assessment_id))
    
    def get_student_response(
        self, 
        assessment_id: int, 
        question_id: int, 
        student_id: int
    ) -> Optional[AssessmentResponse]:
        """
        Get a specific student's response to a question.
        
        Args:
            assessment_id: ID of the assessment
            question_id: ID of the question
            student_id: ID of the student
            
        Returns:
            Assessment response if found, None otherwise
        """
        try:
            return self.model.objects.get(
                assessment_id=assessment_id,
                question_id=question_id,
                student_id=student_id
            )
        except self.model.DoesNotExist:
            return None


class AssessmentResponseService(CRUDService[AssessmentResponse]):
    """
    Service for business logic related to AssessmentResponses.
    
    Handles response submission, validation, and evaluation with proper
    business rules and error handling.
    """
    
    def __init__(self, repository: Optional[AssessmentResponseRepository] = None) -> None:
        """
        Initialize the assessment response service.
        
        Args:
            repository: Optional repository instance
        """
        if repository is None:
            repository = AssessmentResponseRepository()
        super().__init__(repository)
    
    @transaction.atomic
    def submit_response(
        self, 
        assessment_id: int, 
        question_id: int, 
        student: CustomUser, 
        answer_data: Dict[str, Any]
    ) -> AssessmentResponse:
        """
        Submit a student's response to an assessment question.
        
        Args:
            assessment_id: ID of the assessment
            question_id: ID of the question
            student: Student submitting the response
            answer_data: Dictionary containing the answer data
            
        Returns:
            Created or updated assessment response
            
        Raises:
            ValidationServiceError: If validation fails
            NotFoundServiceError: If assessment or question not found
        """
        # Validate required fields
        validate_required_fields(answer_data, ['answer_text'])
        
        # Get assessment and question
        try:
            assessment = Assessment.objects.get(id=assessment_id)
            question = AssessmentQuestion.objects.get(id=question_id)
        except (Assessment.DoesNotExist, AssessmentQuestion.DoesNotExist) as e:
            raise ValidationServiceError(f"Invalid assessment or question: {e}")
        
        # Validate assessment is still active
        if assessment.completed:
            raise ValidationServiceError("Cannot submit response to completed assessment")
        
        # Validate student owns the assessment
        if assessment.student != student:
            raise ValidationServiceError("Student can only submit responses to their own assessments")
        
        # Check if response already exists
        existing_response = self.repository.get_student_response(
            assessment_id, question_id, student.id
        )
        
        if existing_response:
            # Update existing response
            response = self._update_response(existing_response, answer_data)
        else:
            # Create new response
            response = self._create_response(assessment, question, student, answer_data)
        
        # Evaluate the response
        self._evaluate_response(response, question)
        
        # Log the action
        self.log_action('submit_response', student, response)
        
        return response
    
    def _create_response(
        self, 
        assessment: Assessment, 
        question: AssessmentQuestion, 
        student: CustomUser, 
        answer_data: Dict[str, Any]
    ) -> AssessmentResponse:
        """
        Create a new assessment response.
        
        Args:
            assessment: Assessment instance
            question: Question instance
            student: Student instance
            answer_data: Answer data dictionary
            
        Returns:
            Created assessment response
        """
        response_data = {
            'assessment': assessment,
            'question': question,
            'student': student,
            'answer_text': answer_data.get('answer_text', ''),
            'answer': answer_data.get('answer', ''),  # Legacy field
            'student_answer': answer_data.get('student_answer'),
            'submitted_at': timezone.now(),
            'time_spent': answer_data.get('time_spent', 0)
        }
        
        return self.repository.create(**response_data)
    
    def _update_response(
        self, 
        response: AssessmentResponse, 
        answer_data: Dict[str, Any]
    ) -> AssessmentResponse:
        """
        Update an existing assessment response.
        
        Args:
            response: Existing response to update
            answer_data: New answer data
            
        Returns:
            Updated assessment response
        """
        update_data = {
            'answer_text': answer_data.get('answer_text', response.answer_text),
            'answer': answer_data.get('answer', response.answer),
            'student_answer': answer_data.get('student_answer', response.student_answer),
            'submitted_at': timezone.now(),
            'time_spent': answer_data.get('time_spent', response.time_spent)
        }
        
        return self.repository.update(response, **update_data)
    
    def _evaluate_response(
        self, 
        response: AssessmentResponse, 
        question: AssessmentQuestion
    ) -> None:
        """
        Evaluate a response and set correctness and points.
        
        Args:
            response: Response to evaluate
            question: Question being answered
        """
        # Use the question's check_answer method
        is_correct = question.check_answer(response.answer_text or response.answer)
        points_earned = question.points if is_correct else 0
        
        # Update response with evaluation results
        self.repository.update(response, 
            is_correct=is_correct,
            points_earned=points_earned
        )
    
    def get_assessment_responses(
        self, 
        assessment_id: int, 
        user: CustomUser
    ) -> List[AssessmentResponse]:
        """
        Get all responses for an assessment with permission checking.
        
        Args:
            assessment_id: ID of the assessment
            user: User requesting the responses
            
        Returns:
            List of assessment responses
            
        Raises:
            PermissionServiceError: If user lacks permission
        """
        # Validate permissions (admin/professor can see all, students only their own)
        if not (user.is_staff or user.role in ['ADMIN', 'PROFESSOR']):
            # Students can only see their own responses
            return list(self.repository.filter(
                assessment_id=assessment_id,
                student=user
            ))
        
        return self.repository.get_responses_for_assessment(assessment_id)
    
    def calculate_response_statistics(
        self, 
        assessment_id: int
    ) -> Dict[str, Any]:
        """
        Calculate statistics for responses to an assessment.
        
        Args:
            assessment_id: ID of the assessment
            
        Returns:
            Dictionary containing response statistics
        """
        responses = self.repository.get_responses_for_assessment(assessment_id)
        
        total_responses = len(responses)
        correct_responses = sum(1 for r in responses if r.is_correct)
        total_points_possible = sum(r.question.points for r in responses)
        total_points_earned = sum(r.points_earned for r in responses)
        
        return {
            'total_responses': total_responses,
            'correct_responses': correct_responses,
            'accuracy_rate': correct_responses / total_responses if total_responses > 0 else 0,
            'total_points_possible': total_points_possible,
            'total_points_earned': total_points_earned,
            'average_score': (total_points_earned / total_points_possible * 100) if total_points_possible > 0 else 0
        }

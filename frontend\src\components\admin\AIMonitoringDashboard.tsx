/**
 * AI Monitoring Dashboard Component
 * 
 * Comprehensive dashboard for monitoring AI service health, performance, and usage.
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Grid,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tabs,
  Tab,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  CheckCircle,
  Error,
  Warning,
  Speed,
  AttachMoney,
  People,
  Timer,
} from '@mui/icons-material';
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Bar<PERSON>hart,
  Bar,
  Pie<PERSON>hart,
  Pie,
  Cell,
} from 'recharts';

interface DashboardData {
  health_status: {
    status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
    issues: Array<{
      type: string;
      message: string;
      threshold?: string;
    }>;
    metrics: {
      total_requests: number;
      success_rate: number;
      performance: {
        avg_duration: number;
        max_duration: number;
        min_duration: number;
      };
    };
    configuration: {
      api_key_configured: boolean;
      model: string;
      fallback_enabled: boolean;
      service_initialized: boolean;
    };
  };
  quick_stats: {
    total_requests_24h: number;
    successful_requests_24h: number;
    success_rate_24h: number;
    avg_response_time_24h: number;
    total_tokens_24h: number;
    total_cost_24h: number;
  };
  service_breakdown: Array<{
    service_type: string;
    total: number;
    successful: number;
    avg_duration: number;
  }>;
  error_breakdown: Array<{
    error_type: string;
    count: number;
  }>;
  hourly_trend: Array<{
    hour: string;
    total_requests: number;
    successful_requests: number;
    avg_duration: number;
  }>;
}

const AIMonitoringDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/v1/ai/dashboard/overview/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch dashboard data');
      }

      const data = await response.json();
      setDashboardData(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
    
    // Set up auto-refresh
    const interval = setInterval(fetchDashboardData, refreshInterval * 1000);
    return () => clearInterval(interval);
  }, [refreshInterval]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'success';
      case 'degraded': return 'warning';
      case 'unhealthy': return 'error';
      default: return 'info';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle color="success" />;
      case 'degraded': return <Warning color="warning" />;
      case 'unhealthy': return <Error color="error" />;
      default: return <Timer color="info" />;
    }
  };

  const formatDuration = (seconds: number) => {
    return `${seconds.toFixed(2)}s`;
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(4)}`;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Error loading dashboard: {error}
      </Alert>
    );
  }

  if (!dashboardData) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        No dashboard data available
      </Alert>
    );
  }

  const { health_status, quick_stats, service_breakdown, error_breakdown, hourly_trend } = dashboardData;

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        AI Service Monitoring Dashboard
      </Typography>

      {/* Auto-refresh controls */}
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
        <FormControl size="small">
          <InputLabel>Refresh Interval</InputLabel>
          <Select
            value={refreshInterval}
            onChange={(e) => setRefreshInterval(Number(e.target.value))}
            label="Refresh Interval"
          >
            <MenuItem value={10}>10 seconds</MenuItem>
            <MenuItem value={30}>30 seconds</MenuItem>
            <MenuItem value={60}>1 minute</MenuItem>
            <MenuItem value={300}>5 minutes</MenuItem>
          </Select>
        </FormControl>
        <Typography variant="body2" color="text.secondary">
          Last updated: {new Date().toLocaleTimeString()}
        </Typography>
      </Box>

      {/* Health Status */}
      <Card sx={{ mb: 3 }}>
        <CardHeader
          title="System Health"
          avatar={getStatusIcon(health_status.status)}
        />
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Chip
              label={health_status.status.toUpperCase()}
              color={getStatusColor(health_status.status) as any}
              variant="filled"
            />
            <Typography variant="body2">
              Model: {health_status.configuration.model}
            </Typography>
            <Typography variant="body2">
              API Key: {health_status.configuration.api_key_configured ? '✓' : '✗'}
            </Typography>
          </Box>

          {health_status.issues.length > 0 && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              <Typography variant="subtitle2">Issues Detected:</Typography>
              {health_status.issues.map((issue, index) => (
                <Typography key={index} variant="body2">
                  • {issue.message}
                </Typography>
              ))}
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <TrendingUp color="primary" />
                <Typography variant="h6">{quick_stats.total_requests_24h}</Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Total Requests (24h)
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CheckCircle color="success" />
                <Typography variant="h6">{quick_stats.success_rate_24h.toFixed(1)}%</Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Success Rate (24h)
              </Typography>
              <LinearProgress
                variant="determinate"
                value={quick_stats.success_rate_24h}
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Speed color="info" />
                <Typography variant="h6">
                  {formatDuration(quick_stats.avg_response_time_24h)}
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Avg Response Time (24h)
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AttachMoney color="warning" />
                <Typography variant="h6">
                  {formatCurrency(quick_stats.total_cost_24h)}
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Total Cost (24h)
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Detailed Analytics Tabs */}
      <Card>
        <CardHeader
          title="Detailed Analytics"
        />
        <CardContent>
          <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
            <Tab label="Service Breakdown" />
            <Tab label="Error Analysis" />
            <Tab label="Hourly Trends" />
          </Tabs>

          {/* Service Breakdown Tab */}
          {activeTab === 0 && (
            <Box sx={{ mt: 3 }}>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Service Type</TableCell>
                      <TableCell align="right">Total Requests</TableCell>
                      <TableCell align="right">Success Rate</TableCell>
                      <TableCell align="right">Avg Duration</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {service_breakdown.map((service) => (
                      <TableRow key={service.service_type}>
                        <TableCell>{service.service_type}</TableCell>
                        <TableCell align="right">{service.total}</TableCell>
                        <TableCell align="right">
                          {((service.successful / service.total) * 100).toFixed(1)}%
                        </TableCell>
                        <TableCell align="right">
                          {formatDuration(service.avg_duration)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* Error Analysis Tab */}
          {activeTab === 1 && (
            <Box sx={{ mt: 3 }}>
              {error_breakdown.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={error_breakdown}
                      dataKey="count"
                      nameKey="error_type"
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      label
                    >
                      {error_breakdown.map((_, index) => (
                        <Cell key={`cell-${index}`} fill={`hsl(${index * 45}, 70%, 50%)`} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <Typography variant="body1" color="text.secondary" textAlign="center">
                  No errors in the selected time period
                </Typography>
              )}
            </Box>
          )}

          {/* Hourly Trends Tab */}
          {activeTab === 2 && (
            <Box sx={{ mt: 3 }}>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={hourly_trend}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="hour" 
                    tickFormatter={(value) => new Date(value).toLocaleTimeString([], { hour: '2-digit' })}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(value) => new Date(value).toLocaleString()}
                  />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="total_requests" 
                    stroke="#8884d8" 
                    name="Total Requests"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="successful_requests" 
                    stroke="#82ca9d" 
                    name="Successful Requests"
                  />
                </LineChart>
              </ResponsiveContainer>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default AIMonitoringDashboard;

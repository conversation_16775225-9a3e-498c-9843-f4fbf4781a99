import { test, expect } from '@playwright/test';

test.describe('Assessment System Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login as student
    await page.goto('/login');
    const username = process.env.TEST_STUDENT_USERNAME || 'testuser';
    const password = process.env.TEST_STUDENT_PASSWORD || 'testpass123';
    
    await page.fill('input[name="username"]', username);
    await page.fill('input[name="password"]', password);
    await page.click('button[type="submit"]');
    
    await expect(page).toHaveURL(/\/dashboard/);
  });

  test('should display available assessments', async ({ page }) => {
    await page.goto('/assessments');
    
    await expect(page.locator('h1')).toContainText('Assessments');
    await expect(page.locator('[data-testid="assessment-card"]')).toHaveCount.greaterThan(0);
    
    // Check assessment card elements
    const firstAssessment = page.locator('[data-testid="assessment-card"]').first();
    await expect(firstAssessment.locator('[data-testid="assessment-title"]')).toBeVisible();
    await expect(firstAssessment.locator('[data-testid="assessment-type"]')).toBeVisible();
    await expect(firstAssessment.locator('[data-testid="assessment-duration"]')).toBeVisible();
    await expect(firstAssessment.locator('[data-testid="assessment-questions"]')).toBeVisible();
  });

  test('should start an assessment', async ({ page }) => {
    await page.goto('/assessments');
    
    // Find available assessment
    const assessmentCard = page.locator('[data-testid="assessment-card"]').first();
    const startButton = assessmentCard.locator('[data-testid="start-assessment"]');
    
    // Check if assessment is available
    if (await startButton.isVisible()) {
      await startButton.click();
      
      // Should navigate to assessment page
      await expect(page).toHaveURL(/\/assessments\/\d+\/take/);
      
      // Check assessment interface
      await expect(page.locator('[data-testid="question-container"]')).toBeVisible();
      await expect(page.locator('[data-testid="question-number"]')).toBeVisible();
      await expect(page.locator('[data-testid="timer"]')).toBeVisible();
      await expect(page.locator('[data-testid="progress-bar"]')).toBeVisible();
    }
  });

  test('should answer multiple choice questions', async ({ page }) => {
    await page.goto('/assessments');
    
    // Start assessment
    const assessmentCard = page.locator('[data-testid="assessment-card"]').first();
    await assessmentCard.locator('[data-testid="start-assessment"]').click();
    
    // Wait for assessment to load
    await expect(page.locator('[data-testid="question-container"]')).toBeVisible();
    
    // Check if it's a multiple choice question
    const mcOptions = page.locator('[data-testid="mc-option"]');
    if (await mcOptions.count() > 0) {
      // Select first option
      await mcOptions.first().click();
      
      // Check that option is selected
      await expect(mcOptions.first().locator('input')).toBeChecked();
      
      // Move to next question
      const nextButton = page.locator('[data-testid="next-question"]');
      if (await nextButton.isVisible()) {
        await nextButton.click();
      }
    }
  });

  test('should answer essay questions', async ({ page }) => {
    await page.goto('/assessments');
    
    // Start assessment
    const assessmentCard = page.locator('[data-testid="assessment-card"]').first();
    await assessmentCard.locator('[data-testid="start-assessment"]').click();
    
    await expect(page.locator('[data-testid="question-container"]')).toBeVisible();
    
    // Check if it's an essay question
    const essayTextarea = page.locator('[data-testid="essay-answer"]');
    if (await essayTextarea.isVisible()) {
      // Type essay answer
      await essayTextarea.fill('This is my essay answer. It demonstrates my understanding of the topic and provides detailed explanations with examples.');
      
      // Check word count
      await expect(page.locator('[data-testid="word-count"]')).toBeVisible();
      
      // Move to next question
      const nextButton = page.locator('[data-testid="next-question"]');
      if (await nextButton.isVisible()) {
        await nextButton.click();
      }
    }
  });

  test('should handle true/false questions', async ({ page }) => {
    await page.goto('/assessments');
    
    // Start assessment
    const assessmentCard = page.locator('[data-testid="assessment-card"]').first();
    await assessmentCard.locator('[data-testid="start-assessment"]').click();
    
    await expect(page.locator('[data-testid="question-container"]')).toBeVisible();
    
    // Check if it's a true/false question
    const trueOption = page.locator('[data-testid="true-option"]');
    const falseOption = page.locator('[data-testid="false-option"]');
    
    if (await trueOption.isVisible() && await falseOption.isVisible()) {
      // Select true
      await trueOption.click();
      
      // Check that true is selected
      await expect(trueOption.locator('input')).toBeChecked();
      await expect(falseOption.locator('input')).not.toBeChecked();
      
      // Move to next question
      const nextButton = page.locator('[data-testid="next-question"]');
      if (await nextButton.isVisible()) {
        await nextButton.click();
      }
    }
  });

  test('should navigate between questions', async ({ page }) => {
    await page.goto('/assessments');
    
    // Start assessment
    const assessmentCard = page.locator('[data-testid="assessment-card"]').first();
    await assessmentCard.locator('[data-testid="start-assessment"]').click();
    
    await expect(page.locator('[data-testid="question-container"]')).toBeVisible();
    
    // Check initial question number
    const questionNumber = page.locator('[data-testid="question-number"]');
    await expect(questionNumber).toContainText('1');
    
    // Go to next question
    const nextButton = page.locator('[data-testid="next-question"]');
    if (await nextButton.isVisible()) {
      await nextButton.click();
      await expect(questionNumber).toContainText('2');
    }
    
    // Go back to previous question
    const prevButton = page.locator('[data-testid="prev-question"]');
    if (await prevButton.isVisible()) {
      await prevButton.click();
      await expect(questionNumber).toContainText('1');
    }
  });

  test('should use question navigator', async ({ page }) => {
    await page.goto('/assessments');
    
    // Start assessment
    const assessmentCard = page.locator('[data-testid="assessment-card"]').first();
    await assessmentCard.locator('[data-testid="start-assessment"]').click();
    
    await expect(page.locator('[data-testid="question-container"]')).toBeVisible();
    
    // Open question navigator
    const navigatorButton = page.locator('[data-testid="question-navigator"]');
    if (await navigatorButton.isVisible()) {
      await navigatorButton.click();
      
      // Check navigator is open
      await expect(page.locator('[data-testid="navigator-panel"]')).toBeVisible();
      
      // Check question status indicators
      await expect(page.locator('[data-testid="question-status"]')).toHaveCount.greaterThan(0);
      
      // Jump to specific question
      const questionButton = page.locator('[data-testid="question-button"]').nth(2);
      if (await questionButton.isVisible()) {
        await questionButton.click();
        
        // Should navigate to that question
        await expect(page.locator('[data-testid="question-number"]')).toContainText('3');
      }
    }
  });

  test('should flag questions for review', async ({ page }) => {
    await page.goto('/assessments');
    
    // Start assessment
    const assessmentCard = page.locator('[data-testid="assessment-card"]').first();
    await assessmentCard.locator('[data-testid="start-assessment"]').click();
    
    await expect(page.locator('[data-testid="question-container"]')).toBeVisible();
    
    // Flag question for review
    const flagButton = page.locator('[data-testid="flag-question"]');
    if (await flagButton.isVisible()) {
      await flagButton.click();
      
      // Check flag state
      await expect(flagButton).toHaveClass(/flagged/);
      
      // Check in navigator
      const navigatorButton = page.locator('[data-testid="question-navigator"]');
      if (await navigatorButton.isVisible()) {
        await navigatorButton.click();
        
        // First question should be flagged
        const firstQuestionStatus = page.locator('[data-testid="question-status"]').first();
        await expect(firstQuestionStatus).toHaveClass(/flagged/);
      }
    }
  });

  test('should show timer and handle time limits', async ({ page }) => {
    await page.goto('/assessments');
    
    // Start timed assessment
    const assessmentCard = page.locator('[data-testid="assessment-card"]').first();
    await assessmentCard.locator('[data-testid="start-assessment"]').click();
    
    await expect(page.locator('[data-testid="question-container"]')).toBeVisible();
    
    // Check timer is visible
    const timer = page.locator('[data-testid="timer"]');
    await expect(timer).toBeVisible();
    
    // Timer should show time remaining
    const timerText = await timer.textContent();
    expect(timerText).toMatch(/\d+:\d+/);
    
    // Check time warning (if applicable)
    // Note: This would require a short-duration test assessment
  });

  test('should submit assessment', async ({ page }) => {
    await page.goto('/assessments');
    
    // Start assessment
    const assessmentCard = page.locator('[data-testid="assessment-card"]').first();
    await assessmentCard.locator('[data-testid="start-assessment"]').click();
    
    await expect(page.locator('[data-testid="question-container"]')).toBeVisible();
    
    // Answer a few questions quickly
    for (let i = 0; i < 3; i++) {
      // Try to answer current question
      const mcOptions = page.locator('[data-testid="mc-option"]');
      if (await mcOptions.count() > 0) {
        await mcOptions.first().click();
      }
      
      const essayTextarea = page.locator('[data-testid="essay-answer"]');
      if (await essayTextarea.isVisible()) {
        await essayTextarea.fill('Sample answer');
      }
      
      const trueOption = page.locator('[data-testid="true-option"]');
      if (await trueOption.isVisible()) {
        await trueOption.click();
      }
      
      // Move to next question or submit
      const nextButton = page.locator('[data-testid="next-question"]');
      const submitButton = page.locator('[data-testid="submit-assessment"]');
      
      if (await submitButton.isVisible()) {
        await submitButton.click();
        break;
      } else if (await nextButton.isVisible()) {
        await nextButton.click();
      } else {
        break;
      }
    }
    
    // Handle submission confirmation
    const confirmSubmit = page.locator('[data-testid="confirm-submit"]');
    if (await confirmSubmit.isVisible()) {
      await confirmSubmit.click();
    }
    
    // Should navigate to results page
    await expect(page).toHaveURL(/\/assessments\/\d+\/results/);
  });

  test('should view assessment results', async ({ page }) => {
    // Navigate to completed assessments
    await page.goto('/assessments/completed');
    
    // Check completed assessments list
    await expect(page.locator('[data-testid="completed-assessment"]')).toHaveCount.greaterThan(0);
    
    // Click on first completed assessment
    await page.locator('[data-testid="view-results"]').first().click();
    
    // Should show results page
    await expect(page).toHaveURL(/\/assessments\/\d+\/results/);
    
    // Check results elements
    await expect(page.locator('[data-testid="final-score"]')).toBeVisible();
    await expect(page.locator('[data-testid="grade"]')).toBeVisible();
    await expect(page.locator('[data-testid="time-taken"]')).toBeVisible();
    await expect(page.locator('[data-testid="question-breakdown"]')).toBeVisible();
  });

  test('should review answers', async ({ page }) => {
    await page.goto('/assessments/completed');
    
    // View results of completed assessment
    await page.locator('[data-testid="view-results"]').first().click();
    
    // Click review answers
    const reviewButton = page.locator('[data-testid="review-answers"]');
    if (await reviewButton.isVisible()) {
      await reviewButton.click();
      
      // Should show review interface
      await expect(page.locator('[data-testid="review-container"]')).toBeVisible();
      
      // Check question review elements
      await expect(page.locator('[data-testid="question-review"]')).toHaveCount.greaterThan(0);
      
      // Check correct/incorrect indicators
      await expect(page.locator('[data-testid="answer-status"]')).toHaveCount.greaterThan(0);
      
      // Check explanations
      const explanations = page.locator('[data-testid="explanation"]');
      if (await explanations.count() > 0) {
        await expect(explanations.first()).toBeVisible();
      }
    }
  });

  test('should handle assessment retakes', async ({ page }) => {
    await page.goto('/assessments');
    
    // Find assessment that allows retakes
    const retakeButton = page.locator('[data-testid="retake-assessment"]');
    if (await retakeButton.isVisible()) {
      await retakeButton.click();
      
      // Should show retake confirmation
      await expect(page.locator('[data-testid="retake-confirmation"]')).toBeVisible();
      
      // Confirm retake
      await page.locator('[data-testid="confirm-retake"]').click();
      
      // Should start new attempt
      await expect(page).toHaveURL(/\/assessments\/\d+\/take/);
      await expect(page.locator('[data-testid="attempt-number"]')).toContainText('2');
    }
  });

  test('should be accessible', async ({ page }) => {
    await page.goto('/assessments');
    
    // Check for proper ARIA labels
    await expect(page.locator('[aria-label="Assessment list"]')).toBeVisible();
    
    // Start assessment and check accessibility
    const assessmentCard = page.locator('[data-testid="assessment-card"]').first();
    await assessmentCard.locator('[data-testid="start-assessment"]').click();
    
    await expect(page.locator('[data-testid="question-container"]')).toBeVisible();
    
    // Check question accessibility
    await expect(page.locator('[role="group"]')).toBeVisible();
    await expect(page.locator('[aria-labelledby]')).toHaveCount.greaterThan(0);
    
    // Check keyboard navigation
    await page.keyboard.press('Tab');
    const focusedElement = await page.evaluate(() => document.activeElement?.tagName);
    expect(['INPUT', 'BUTTON', 'TEXTAREA']).toContain(focusedElement);
  });

  test('should work on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/assessments');
    
    // Check mobile layout
    await expect(page.locator('[data-testid="mobile-assessment-list"]')).toBeVisible();
    
    // Start assessment on mobile
    const assessmentCard = page.locator('[data-testid="assessment-card"]').first();
    await assessmentCard.locator('[data-testid="start-assessment"]').click();
    
    // Check mobile assessment interface
    await expect(page.locator('[data-testid="mobile-question-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="mobile-timer"]')).toBeVisible();
    
    // Test mobile navigation
    const mobileNav = page.locator('[data-testid="mobile-question-nav"]');
    if (await mobileNav.isVisible()) {
      await mobileNav.click();
      await expect(page.locator('[data-testid="mobile-navigator"]')).toBeVisible();
    }
  });
});

import React, { useEffect, useState } from 'react';
import {
  Box,
  Paper,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Typography,
  Button,
  CircularProgress,
  Divider,
  Chip,
  useTheme,
  Tooltip,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Add as AddIcon,
  Chat as ChatIcon,
  History as HistoryIcon,
  AutoAwesome as MultiAgentIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { styled, alpha } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../app/hooks';
import {
  fetchConversations,
  deleteConversation,
  setActiveConversation,
  createConversation,
} from '../features/chat/chatSlice';
import Chat from '../features/chat/Chat';
import ChatModeTabNavigation from '../components/chat/ChatModeTabNavigation';

// Styled Components with theme-aware dark/light mode and RTL support
const PageContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flex: 1,
  backgroundColor: theme.palette.background.default,
  overflow: 'hidden',
  direction: theme.direction,
  width: '100%',
}));

const SidebarPaper = styled(Paper)(({ theme }) => ({
  width: 320,
  minWidth: 320,
  height: '100%',
  borderRadius: theme.spacing(2),
  borderInlineEnd: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.paper,
  backdropFilter: 'blur(20px)',
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: theme.shadows[3],
}));

const ChatPaper = styled(Paper)(({ theme }) => ({
  flex: 1,
  height: '100%',
  borderRadius: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  backdropFilter: 'blur(20px)',
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: theme.shadows[3],
  marginInlineStart: theme.spacing(2),
}));

const HeaderBox = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  background: theme.palette.mode === 'dark'
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
    : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  color: theme.palette.primary.contrastText,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  borderRadius: `${theme.spacing(2)} ${theme.spacing(2)} 0 0`,
}));

const StyledListItem = styled(ListItem)(({ theme }) => ({
  margin: theme.spacing(0.5, 1),
  borderRadius: theme.spacing(1.5),
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  cursor: 'pointer',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    [theme.direction === 'rtl' ? 'right' : 'left']: 0,
    width: '3px',
    height: '100%',
    background: 'transparent',
    transition: 'all 0.3s ease',
    borderRadius: '0 3px 3px 0',
  },
  '&:hover': {
    background: alpha(theme.palette.primary.main, 0.08),
    transform: theme.direction === 'rtl' ? 'translateX(-4px)' : 'translateX(4px)',
    '&::before': {
      background: alpha(theme.palette.primary.main, 0.3),
    },
  },
  '&.Mui-selected': {
    background: theme.palette.mode === 'dark'
      ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.15)} 0%, ${alpha(theme.palette.secondary.main, 0.15)} 100%)`
      : `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.12)} 0%, ${alpha(theme.palette.secondary.main, 0.12)} 100%)`,
    '&::before': {
      background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
    },
    '&:hover': {
      background: theme.palette.mode === 'dark'
        ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.2)} 0%, ${alpha(theme.palette.secondary.main, 0.2)} 100%)`
        : `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.18)} 0%, ${alpha(theme.palette.secondary.main, 0.18)} 100%)`,
    },
  },
}));

const EmptyStateBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  height: '100%',
  padding: theme.spacing(4),
  textAlign: 'center',
  color: theme.palette.text.secondary,
}));

const CreateButton = styled(Button)(({ theme }) => ({
  margin: theme.spacing(1),
  background: theme.palette.mode === 'dark'
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
    : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  color: theme.palette.primary.contrastText,
  borderRadius: theme.spacing(2),
  textTransform: 'none',
  fontWeight: 600,
  padding: theme.spacing(1.5, 3),
  boxShadow: theme.palette.mode === 'dark'
    ? `0 4px 20px ${alpha(theme.palette.primary.main, 0.3)}`
    : `0 4px 20px ${alpha(theme.palette.primary.main, 0.2)}`,
  '&:hover': {
    background: theme.palette.mode === 'dark'
      ? `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.secondary.dark} 100%)`
      : `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.secondary.dark} 100%)`,
    transform: 'translateY(-2px)',
    boxShadow: theme.palette.mode === 'dark'
      ? `0 8px 25px ${alpha(theme.palette.primary.main, 0.4)}`
      : `0 8px 25px ${alpha(theme.palette.primary.main, 0.3)}`,
  },
  '&:active': {
    transform: 'translateY(0)',
  },
}));



const ChatPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const theme = useTheme();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar' || theme.direction === 'rtl';
  const {
    conversations,
    activeConversation,
    loading,
  } = useAppSelector((state) => state.chat);
  const [selectedConversationId, setSelectedConversationId] = useState<number | null>(null);

  // Ensure conversations is always an array
  const conversationsArray = Array.isArray(conversations) ? conversations : [];

  useEffect(() => {
    dispatch(fetchConversations());
  }, [dispatch]);

  // Sync selectedConversationId with activeConversation from Redux
  useEffect(() => {
    if (activeConversation) {
      setSelectedConversationId(activeConversation.id);
    } else if (conversationsArray.length > 0 && !selectedConversationId) {
      setSelectedConversationId(conversationsArray[0].id);
      dispatch(setActiveConversation(conversationsArray[0]));
    }
  }, [activeConversation, conversationsArray, selectedConversationId, dispatch]);  const handleSelectConversation = (conversationId: number) => {
    setSelectedConversationId(conversationId);
    const conversation = conversationsArray.find(c => c.id === conversationId);
    if (conversation) {
      dispatch(setActiveConversation(conversation));
    }
  };

  const handleDeleteConversation = async (conversationId: number) => {
    await dispatch(deleteConversation(conversationId));
    if (selectedConversationId === conversationId) {
      const remainingConversations = conversationsArray.filter(c => c.id !== conversationId);
      if (remainingConversations.length > 0) {
        handleSelectConversation(remainingConversations[0].id);
      } else {
        setSelectedConversationId(null);
        dispatch(setActiveConversation(null));
      }
    }
  };  const handleCreateConversation = async () => {
    try {
      // Create a new conversation with default title like ChatGPT
      const result = await dispatch(createConversation({
        title: t('chat.defaultConversationTitle', 'ChatGPT'),
      }));
      
      if (createConversation.fulfilled.match(result)) {
        // The conversation is automatically added to Redux state and set as active
        // No need to refetch, just ensure it's selected in local state
        if (result.payload.id) {
          setSelectedConversationId(result.payload.id);
        }
      }
    } catch (error) {
      console.error('Failed to create conversation:', error);
    }
  };


  const selectedConversation = conversationsArray.find(c => c.id === selectedConversationId);  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', width: '100%' }}>
      <ChatModeTabNavigation />
      <PageContainer>
      {/* Sidebar */}
      <SidebarPaper elevation={0}>        <HeaderBox>
          <Box display="flex" alignItems="center" gap={1.5}>
            <Box
              sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                backgroundColor: 'action.hover',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <ChatIcon sx={{ fontSize: 20 }} />
            </Box>
            <Box>
              <Typography variant="h6" fontWeight={600} sx={{ lineHeight: 1.2 }}>
                {t('chat.chatHistory', 'Standard Chat')}
              </Typography>
              <Typography variant="caption" sx={{ opacity: 0.8, fontSize: '0.75rem' }}>
                {t('chat.standardChatSubtitle', 'Quick AI conversations')}
              </Typography>
            </Box>
          </Box><Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip 
              label={conversationsArray.length} 
              size="small" 
              sx={{
                backgroundColor: alpha(theme.palette.background.paper, 0.2), 
                color: theme.palette.primary.contrastText,
                fontWeight: 600,
                border: `1px solid ${alpha(theme.palette.primary.contrastText, 0.2)}`,
              }} 
            />
          </Box>
        </HeaderBox>

        <Box sx={{ p: 2, display: 'flex', gap: 1 }}>
          <CreateButton
            fullWidth
            startIcon={<AddIcon />}
            onClick={handleCreateConversation}
          >
            {t('chat.createNewConversation', 'Create New Conversation')}
          </CreateButton>
        </Box>

        <Divider sx={{ opacity: 0.1 }} />

        <Box sx={{ flex: 1, overflow: 'auto' }}>
          {loading ? (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress 
                size={24} 
                sx={{ 
                  color: theme.palette.primary.main,
                }} 
              />
            </Box>
          ) : conversationsArray.length === 0 ? (
            <Box p={3} textAlign="center">
              <Box
                sx={{
                  width: 60,
                  height: 60,
                  borderRadius: '50%',
                  background: theme.palette.mode === 'dark'
                    ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.2)} 0%, ${alpha(theme.palette.secondary.main, 0.2)} 100%)`
                    : `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mx: 'auto',
                  mb: 2,
                }}
              >
                <HistoryIcon sx={{ color: theme.palette.primary.main, fontSize: 28 }} />
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                {t('chat.noConversations', 'No conversations yet')}
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                {t('chat.startNewChat', 'Start a new chat to begin!')}
              </Typography>
            </Box>
          ) : (            <List sx={{ py: 1 }}>
              {conversationsArray.map((conversation) => (
                <StyledListItem
                  key={conversation.id}
                  selected={selectedConversationId === conversation.id}
                  onClick={() => handleSelectConversation(conversation.id)}
                >
                  <ListItemText
                    primary={
                      <Typography 
                        variant="subtitle2" 
                        fontWeight={selectedConversationId === conversation.id ? 600 : 500}
                        sx={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          color: selectedConversationId === conversation.id 
                            ? theme.palette.primary.main 
                            : theme.palette.text.primary,
                        }}
                      >
                        {conversation.title || t('chat.untitledConversation', 'Untitled Conversation')}
                      </Typography>
                    }
                    secondary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(conversation.created_at).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US')}
                        </Typography>
                        {conversation.messages && conversation.messages.length > 0 && (
                          <Chip
                            label={conversation.messages.length}
                            size="small"
                            sx={{
                              height: 18,
                              fontSize: '0.7rem',
                              bgcolor: alpha(theme.palette.primary.main, 0.1),
                              color: theme.palette.primary.main,
                              border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                            }}
                          />
                        )}
                      </Box>
                    }
                  />
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteConversation(conversation.id);
                    }}
                    sx={{
                      opacity: selectedConversationId === conversation.id ? 1 : 0.6,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        opacity: 1,
                        color: 'error.main',
                        transform: 'scale(1.1)',
                        background: alpha(theme.palette.error.main, 0.1),
                      },
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </StyledListItem>
              ))}
            </List>
          )}
        </Box>
      </SidebarPaper>      {/* Chat Area */}
      <ChatPaper elevation={0}>
        {selectedConversation ? (
          <Chat conversationId={selectedConversation.id} />
        ) : conversationsArray.length === 0 ? (
          // Show default ChatGPT interface when no conversations exist
          <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
            {/* Header for default chat */}
            <Box sx={{ 
              p: 3, 
              borderBottom: `1px solid ${theme.palette.divider}`,
              background: theme.palette.mode === 'dark'
                ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`
                : `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
            }}>
              <Typography variant="h5" fontWeight={600} color="primary.main" sx={{ mb: 1 }}>
                {t('chat.defaultConversationTitle', 'ChatGPT')}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('chat.aiAssistantSubtitle', 'Your AI-powered learning assistant')}
              </Typography>
            </Box>

            {/* Chat suggestions area */}
            <Box sx={{ 
              flex: 1, 
              display: 'flex', 
              flexDirection: 'column', 
              justifyContent: 'center', 
              alignItems: 'center',
              p: 4,
              textAlign: 'center'
            }}>
              <Box
                sx={{
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  background: theme.palette.mode === 'dark'
                    ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.2)} 0%, ${alpha(theme.palette.secondary.main, 0.2)} 100%)`
                    : `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mb: 3,
                  animation: 'pulse 2s infinite',
                  '@keyframes pulse': {
                    '0%': { transform: 'scale(1)' },
                    '50%': { transform: 'scale(1.05)' },
                    '100%': { transform: 'scale(1)' },
                  },
                }}
              >
                <ChatIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />
              </Box>

              <Typography variant="h6" gutterBottom fontWeight={600} color="text.primary" sx={{ mb: 2 }}>
                {t('chat.welcomeTitle', 'Welcome to AI Assistant')}
              </Typography>
              
              <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 400, lineHeight: 1.6, mb: 4 }}>
                {t('chat.welcomeMessage', 'I can help you with your studies, answer questions, and provide learning assistance. What would you like to explore today?')}
              </Typography>

              {/* Suggestion chips */}
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1.5, justifyContent: 'center', mb: 4, maxWidth: 500 }}>
                {[
                  t('chat.suggestion1', 'Help me understand a concept'),
                  t('chat.suggestion2', 'Create a study plan'),
                  t('chat.suggestion3', 'Explain a complex topic'),
                  t('chat.suggestion4', 'Review my notes'),
                ].map((suggestion, index) => (
                  <Chip
                    key={index}
                    label={suggestion}
                    clickable
                    onClick={handleCreateConversation}
                    sx={{
                      borderRadius: 2,
                      px: 1,
                      backgroundColor: alpha(theme.palette.primary.main, 0.08),
                      border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                      color: theme.palette.primary.main,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.15),
                        transform: 'translateY(-2px)',
                        boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`,
                      },
                    }}
                  />
                ))}
              </Box>

              <CreateButton
                startIcon={<AddIcon />}
                onClick={handleCreateConversation}
              >
                {t('chat.startNewConversation', 'Start New Conversation')}
              </CreateButton>
            </Box>
          </Box>
        ) : (
          // Show regular empty state when conversations exist but none selected
          <EmptyStateBox>
            <Box
              sx={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                background: theme.palette.mode === 'dark'
                  ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.2)} 0%, ${alpha(theme.palette.secondary.main, 0.2)} 100%)`
                  : `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mb: 3,
                animation: 'pulse 2s infinite',
                '@keyframes pulse': {
                  '0%': { transform: 'scale(1)' },
                  '50%': { transform: 'scale(1.05)' },
                  '100%': { transform: 'scale(1)' },
                },
              }}
            >
              <ChatIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />
            </Box>
            <Typography variant="h5" gutterBottom fontWeight={600} color="text.primary" sx={{ mb: 2 }}>
              {t('chat.selectConversationTitle', 'Select a Conversation')}
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 400, lineHeight: 1.6, mb: 3 }}>
              {t('chat.selectConversationMessage', 'Choose a conversation from the sidebar to start chatting, or create a new one.')}
            </Typography>          </EmptyStateBox>
        )}
      </ChatPaper>
    </PageContainer>
    </Box>
  );
};

export default ChatPage;

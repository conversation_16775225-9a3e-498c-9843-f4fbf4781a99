import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  <PERSON>ton,
  Chip,
  LinearProgress,
  Avatar,
  Divider,
  Stack,
  Paper,
  IconButton,
  Tooltip,
  Alert,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  Psychology as PsychologyIcon,
  TrendingUp as TrendingUpIcon,
  School as SchoolIcon,
  Analytics as AnalyticsIcon,
  AutoFixHigh as AutoFixHighIcon,
  Speed as SpeedIcon,
  EmojiEvents as EmojiEventsIcon,
  Lightbulb as LightbulbIcon,
  Refresh as RefreshIcon,
  PlayArrow as PlayArrowIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface LearningPath {
  id: string;
  title: string;
  description: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  progress: number;
  estimatedTime: string;
  skills: string[];
  adaptationReason: string;
}

interface StudentAnalytics {
  learningStyle: string;
  strengths: string[];
  weaknesses: string[];
  recommendedPace: string;
  engagementLevel: number;
  preferredContentType: string;
}

const AdaptiveLearningPage: React.FC = () => {
  const theme = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [adaptiveRecommendations, setAdaptiveRecommendations] = useState<LearningPath[]>([]);
  const [studentAnalytics, setStudentAnalytics] = useState<StudentAnalytics | null>(null);

  // Mock data for demonstration
  useEffect(() => {
    // Simulate loading adaptive learning data
    const mockData: LearningPath[] = [
      {
        id: '1',
        title: 'Personalized JavaScript Fundamentals',
        description: 'Customized learning path based on your visual learning preference and fast-paced style.',
        difficulty: 'Beginner',
        progress: 65,
        estimatedTime: '3 weeks',
        skills: ['Variables', 'Functions', 'DOM Manipulation', 'Async Programming'],
        adaptationReason: 'Adapted for visual learners with interactive examples'
      },
      {
        id: '2',
        title: 'Advanced React Patterns',
        description: 'AI-recommended advanced concepts based on your strong problem-solving skills.',
        difficulty: 'Advanced',
        progress: 30,
        estimatedTime: '5 weeks',
        skills: ['Hooks', 'Context API', 'Performance Optimization', 'Testing'],
        adaptationReason: 'Challenging content matched to your analytical thinking style'
      },
      {
        id: '3',
        title: 'Data Structures Deep Dive',
        description: 'Reinforcement learning path to strengthen your algorithmic thinking.',
        difficulty: 'Intermediate',
        progress: 45,
        estimatedTime: '4 weeks',
        skills: ['Arrays', 'Linked Lists', 'Trees', 'Graphs', 'Hash Tables'],
        adaptationReason: 'Focus on practical implementation based on your hands-on preference'
      }
    ];

    const mockAnalytics: StudentAnalytics = {
      learningStyle: 'Visual & Kinesthetic',
      strengths: ['Problem Solving', 'Logical Thinking', 'Quick Learning'],
      weaknesses: ['Theory Retention', 'Long Reading Sessions'],
      recommendedPace: 'Fast-paced with breaks',
      engagementLevel: 85,
      preferredContentType: 'Interactive tutorials with code examples'
    };

    setAdaptiveRecommendations(mockData);
    setStudentAnalytics(mockAnalytics);
  }, []);

  const handleGenerateNewPath = () => {
    setIsLoading(true);
    // Simulate AI generating new adaptive learning path
    setTimeout(() => {
      setIsLoading(false);
      // Could update recommendations here
    }, 2000);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner':
        return theme.palette.success.main;
      case 'Intermediate':
        return theme.palette.warning.main;
      case 'Advanced':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Header */}
        <motion.div variants={itemVariants}>
          <Box sx={{ mb: 4, textAlign: 'center' }}>
            <Avatar
              sx={{
                width: 80,
                height: 80,
                bgcolor: 'primary.main',
                mx: 'auto',
                mb: 2,
              }}
            >
              <PsychologyIcon sx={{ fontSize: 40 }} />
            </Avatar>
            <Typography
              variant="h3"
              component="h1"
              gutterBottom
              sx={{
                fontWeight: 700,
                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 2,
              }}
            >
              Adaptive Learning Platform
            </Typography>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{ maxWidth: 600, mx: 'auto', mb: 3 }}
            >
              AI-powered personalized learning paths that adapt to your unique learning style,
              pace, and goals in real-time.
            </Typography>
          </Box>
        </motion.div>

        {/* Student Analytics Dashboard */}
        {studentAnalytics && (
          <motion.div variants={itemVariants}>
            <Card sx={{ mb: 4, borderRadius: 3 }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <AnalyticsIcon sx={{ mr: 2, color: 'primary.main' }} />
                  <Typography variant="h5" fontWeight={600}>
                    Your Learning Profile
                  </Typography>
                  <Box sx={{ flexGrow: 1 }} />
                  <Tooltip title="Regenerate learning profile">
                    <IconButton onClick={handleGenerateNewPath} disabled={isLoading}>
                      <RefreshIcon />
                    </IconButton>
                  </Tooltip>
                </Box>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={4}>
                    <Paper sx={{ p: 2, borderRadius: 2, bgcolor: 'background.default' }}>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Learning Style
                      </Typography>
                      <Typography variant="h6" fontWeight={600}>
                        {studentAnalytics.learningStyle}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Paper sx={{ p: 2, borderRadius: 2, bgcolor: 'background.default' }}>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Engagement Level
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={studentAnalytics.engagementLevel}
                          sx={{ flexGrow: 1, mr: 2, height: 8, borderRadius: 4 }}
                        />
                        <Typography variant="h6" fontWeight={600}>
                          {studentAnalytics.engagementLevel}%
                        </Typography>
                      </Box>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Paper sx={{ p: 2, borderRadius: 2, bgcolor: 'background.default' }}>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Recommended Pace
                      </Typography>
                      <Typography variant="h6" fontWeight={600}>
                        {studentAnalytics.recommendedPace}
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>

                <Divider sx={{ my: 3 }} />

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" fontWeight={600} gutterBottom sx={{ color: 'success.main' }}>
                      <EmojiEventsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Strengths
                    </Typography>
                    <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                      {studentAnalytics.strengths.map((strength, index) => (
                        <Chip
                          key={index}
                          label={strength}
                          color="success"
                          variant="outlined"
                          size="small"
                        />
                      ))}
                    </Stack>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" fontWeight={600} gutterBottom sx={{ color: 'warning.main' }}>
                      <LightbulbIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Growth Areas
                    </Typography>
                    <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                      {studentAnalytics.weaknesses.map((weakness, index) => (
                        <Chip
                          key={index}
                          label={weakness}
                          color="warning"
                          variant="outlined"
                          size="small"
                        />
                      ))}
                    </Stack>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Adaptive Learning Paths */}
        <motion.div variants={itemVariants}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <AutoFixHighIcon sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h4" fontWeight={600}>
              Adaptive Learning Paths
            </Typography>
            <Box sx={{ flexGrow: 1 }} />
            <Button
              variant="contained"
              startIcon={<PlayArrowIcon />}
              onClick={handleGenerateNewPath}
              disabled={isLoading}
              sx={{ borderRadius: 3 }}
            >
              {isLoading ? 'Generating...' : 'Generate New Path'}
            </Button>
          </Box>

          <Alert severity="info" sx={{ mb: 3, borderRadius: 2 }}>
            These learning paths are automatically generated and updated based on your learning patterns,
            performance data, and preferences.
          </Alert>

          <Grid container spacing={3}>
            {adaptiveRecommendations.map((path, index) => (
              <Grid item xs={12} md={6} lg={4} key={path.id}>
                <motion.div
                  variants={itemVariants}
                  whileHover={{ y: -5 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card
                    sx={{
                      height: '100%',
                      borderRadius: 3,
                      border: `2px solid ${theme.palette.divider}`,
                      '&:hover': {
                        borderColor: theme.palette.primary.main,
                        boxShadow: theme.shadows[8],
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    <CardContent sx={{ p: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                        <SchoolIcon sx={{ mr: 2, color: 'primary.main', mt: 0.5 }} />
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="h6" fontWeight={600} gutterBottom>
                            {path.title}
                          </Typography>
                          <Chip
                            label={path.difficulty}
                            size="small"
                            sx={{
                              bgcolor: getDifficultyColor(path.difficulty),
                              color: 'white',
                              fontWeight: 600,
                            }}
                          />
                        </Box>
                      </Box>

                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ mb: 2, lineHeight: 1.6 }}
                      >
                        {path.description}
                      </Typography>

                      <Box sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2" fontWeight={600}>
                            Progress
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {path.progress}%
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={path.progress}
                          sx={{ height: 8, borderRadius: 4 }}
                        />
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <SpeedIcon sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary">
                          Estimated: {path.estimatedTime}
                        </Typography>
                      </Box>

                      <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                        Skills You'll Learn:
                      </Typography>
                      <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap sx={{ mb: 2 }}>
                        {path.skills.map((skill, skillIndex) => (
                          <Chip
                            key={skillIndex}
                            label={skill}
                            size="small"
                            variant="outlined"
                            color="primary"
                          />
                        ))}
                      </Stack>

                      <Paper
                        sx={{
                          p: 2,
                          bgcolor: (theme) => theme.palette.mode === 'dark' 
                            ? 'rgba(66, 165, 245, 0.05)' 
                            : 'rgba(25, 118, 210, 0.05)',
                          borderRadius: 2,
                          mb: 2,
                        }}
                      >
                        <Typography variant="caption" color="primary" fontWeight={600} gutterBottom display="block">
                          AI Adaptation Insight:
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {path.adaptationReason}
                        </Typography>
                      </Paper>

                      <Button
                        fullWidth
                        variant="contained"
                        startIcon={<TrendingUpIcon />}
                        sx={{ borderRadius: 2 }}
                      >
                        Continue Learning
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>

        {/* Loading State */}
        {isLoading && (
          <motion.div variants={itemVariants}>
            <Card sx={{ mt: 3, borderRadius: 3 }}>
              <CardContent sx={{ textAlign: 'center', py: 4 }}>
                <PsychologyIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  AI is analyzing your learning patterns...
                </Typography>
                <LinearProgress sx={{ maxWidth: 300, mx: 'auto', mt: 2 }} />
              </CardContent>
            </Card>
          </motion.div>
        )}
      </motion.div>
    </Container>
  );
};

export default AdaptiveLearningPage;

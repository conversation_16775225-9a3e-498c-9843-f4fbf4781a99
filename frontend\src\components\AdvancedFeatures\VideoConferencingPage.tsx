import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Badge
} from '@mui/material';
import {
  VideoCall,
  People,
  Schedule,
  PlayArrow,
  Stop,
  VideoLibrary,
  Settings,
  Add,
  Edit,
  Delete,
  Share,
  Download,
  Analytics,
  ExpandMore,
  RecordVoiceOver,
  ScreenShare,
  Chat,
  Poll
} from '@mui/icons-material';
import { videoConferencingService } from '../../services';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`video-tabpanel-${index}`}
      aria-labelledby={`video-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const VideoConferencingPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);

  // Meetings State
  const [meetings, setMeetings] = useState<any[]>([]);
  const [selectedMeeting, setSelectedMeeting] = useState<any>(null);
  const [meetingDialog, setMeetingDialog] = useState(false);

  // Recordings State
  const [recordings, setRecordings] = useState<any[]>([]);
  const [recordingDialog, setRecordingDialog] = useState(false);
  const [selectedRecording, setSelectedRecording] = useState<any>(null);

  // Analytics State
  const [analytics, setAnalytics] = useState<any>(null);

  // Form State
  const [newMeeting, setNewMeeting] = useState({
    title: '',
    description: '',
    startTime: '',
    duration: 60,
    providerId: 'zoom',
    isRecurring: false,
    participants: [] as string[],
    options: {
      passwordProtected: true,
      waitingRoom: true,
      muteOnEntry: true,
      autoRecord: false,
      enableBreakoutRooms: false,
      enableChat: true,
      enablePolls: false,
      enableWhiteboard: false
    }
  });

  // Providers
  const [providers, setProviders] = useState<any[]>([]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const loadMeetings = async () => {
    setLoading(true);
    try {
      const userMeetings = await videoConferencingService.getUserMeetings('demo-user', {
        status: 'all'
      });
      setMeetings(userMeetings);
    } catch (error) {
      console.error('Error loading meetings:', error);
      setMessage({ type: 'error', text: 'Failed to load meetings' });
    }
    setLoading(false);
  };

  const loadRecordings = async () => {
    setLoading(true);
    try {
      // Simulate loading recordings for all meetings
      const mockRecordings = [
        {
          recordingId: '1',
          meetingId: 'meeting1',
          title: 'Advanced Mathematics - Lecture 1',
          startTime: new Date().toISOString(),
          duration: 3600,
          fileSize: **********,
          format: 'mp4',
          quality: 'HD',
          status: 'available',
          viewCount: 15,
          downloadUrl: '#'
        },
        {
          recordingId: '2',
          meetingId: 'meeting2',
          title: 'Team Meeting - Project Review',
          startTime: new Date(Date.now() - 86400000).toISOString(),
          duration: 2700,
          fileSize: 800000000,
          format: 'mp4',
          quality: 'HD',
          status: 'available',
          viewCount: 8,
          downloadUrl: '#'
        }
      ];
      setRecordings(mockRecordings);
    } catch (error) {
      console.error('Error loading recordings:', error);
      setMessage({ type: 'error', text: 'Failed to load recordings' });
    }
    setLoading(false);
  };

  const loadAnalytics = async () => {
    setLoading(true);
    try {
      const institutionAnalytics = await videoConferencingService.getInstitutionAnalytics();
      setAnalytics(institutionAnalytics);
    } catch (error) {
      console.error('Error loading analytics:', error);
      setMessage({ type: 'error', text: 'Failed to load analytics' });
    }
    setLoading(false);
  };

  const loadProviders = async () => {
    try {
      const providersList = await videoConferencingService.getProviders();
      setProviders(providersList);
    } catch (error) {
      console.error('Error loading providers:', error);
    }
  };

  const scheduleMeeting = async () => {
    setLoading(true);
    try {
      const schedule = {
        title: newMeeting.title,
        description: newMeeting.description,
        startTime: newMeeting.startTime,
        duration: newMeeting.duration,
        timezone: 'UTC',
        isRecurring: newMeeting.isRecurring,
        hostId: 'demo-host',
        hostName: 'Demo Host',
        hostEmail: '<EMAIL>'
      };

      const participants = newMeeting.participants.map(email => ({
        userId: email,
        name: email.split('@')[0],
        email,
        role: 'participant' as const,
        isRequired: true,
        permissions: {
          canShare: true,
          canRecord: false,
          canManageParticipants: false,
          canUsePoll: true,
          canUseWhiteboard: true
        }
      }));

      const meeting = await videoConferencingService.scheduleMeeting(
        newMeeting.providerId,
        schedule,
        participants,
        newMeeting.options
      );

      setMeetings(prev => [...prev, meeting]);
      setMeetingDialog(false);
      resetForm();
      setMessage({ type: 'success', text: 'Meeting scheduled successfully!' });
    } catch (error) {
      console.error('Error scheduling meeting:', error);
      setMessage({ type: 'error', text: 'Failed to schedule meeting' });
    }
    setLoading(false);
  };

  const startInstantMeeting = async () => {
    setLoading(true);
    try {
      const instantMeeting = await videoConferencingService.createInstantMeeting(
        'zoom',
        'demo-host',
        {
          passwordProtected: false,
          waitingRoom: false,
          muteOnEntry: true,
          enableChat: true,
          enablePolls: false,
          enableWhiteboard: true
        }
      );

      setMessage({ 
        type: 'success', 
        text: `Instant meeting created! Meeting ID: ${instantMeeting.meetingNumber}` 
      });
    } catch (error) {
      console.error('Error creating instant meeting:', error);
      setMessage({ type: 'error', text: 'Failed to create instant meeting' });
    }
    setLoading(false);
  };

  const startMeeting = async (meetingId: string) => {
    try {
      const result = await videoConferencingService.startMeeting(meetingId);
      setMessage({ type: 'success', text: 'Meeting started successfully!' });
      // Open meeting in new window
      window.open(result.joinUrls.host, '_blank');
    } catch (error) {
      console.error('Error starting meeting:', error);
      setMessage({ type: 'error', text: 'Failed to start meeting' });
    }
  };

  const endMeeting = async (meetingId: string) => {
    try {
      await videoConferencingService.endMeeting(meetingId);
      setMessage({ type: 'success', text: 'Meeting ended successfully!' });
      loadMeetings(); // Refresh meetings list
    } catch (error) {
      console.error('Error ending meeting:', error);
      setMessage({ type: 'error', text: 'Failed to end meeting' });
    }
  };

  const resetForm = () => {
    setNewMeeting({
      title: '',
      description: '',
      startTime: '',
      duration: 60,
      providerId: 'zoom',
      isRecurring: false,
      participants: [],
      options: {
        passwordProtected: true,
        waitingRoom: true,
        muteOnEntry: true,
        autoRecord: false,
        enableBreakoutRooms: false,
        enableChat: true,
        enablePolls: false,
        enableWhiteboard: false
      }
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const getMeetingStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'info';
      case 'started': return 'success';
      case 'ended': return 'default';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  useEffect(() => {
    loadProviders();
    if (activeTab === 0) {
      loadMeetings();
    } else if (activeTab === 1) {
      loadRecordings();
    } else if (activeTab === 2) {
      loadAnalytics();
    }
  }, [activeTab]);

  return (
    <Box sx={{ width: '100%', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Video Conferencing System
      </Typography>

      {message && (
        <Alert 
          severity={message.type} 
          onClose={() => setMessage(null)}
          sx={{ mb: 2 }}
        >
          {message.text}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab icon={<VideoCall />} label="Meetings" />
          <Tab icon={<VideoLibrary />} label="Recordings" />
          <Tab icon={<Analytics />} label="Analytics" />
          <Tab icon={<Settings />} label="Settings" />
        </Tabs>
      </Box>

      {/* Meetings Tab */}
      <TabPanel value={activeTab} index={0}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6">Meeting Management</Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              startIcon={<VideoCall />}
              onClick={startInstantMeeting}
              disabled={loading}
            >
              Start Instant Meeting
            </Button>
            <Button
              variant="outlined"
              startIcon={<Schedule />}
              onClick={() => setMeetingDialog(true)}
            >
              Schedule Meeting
            </Button>
          </Box>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Meeting Title</TableCell>
                <TableCell>Start Time</TableCell>
                <TableCell>Duration</TableCell>
                <TableCell>Provider</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Participants</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {meetings.map((meeting) => (
                <TableRow key={meeting.meetingId}>
                  <TableCell>{meeting.title}</TableCell>
                  <TableCell>
                    {new Date(meeting.startTime).toLocaleString()}
                  </TableCell>
                  <TableCell>{meeting.duration} min</TableCell>
                  <TableCell>
                    <Chip label={meeting.providerId} size="small" />
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={meeting.status} 
                      color={getMeetingStatusColor(meeting.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Badge badgeContent={meeting.participants?.length || 0} color="primary">
                      <People />
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {meeting.status === 'scheduled' && (
                      <>
                        <Tooltip title="Start Meeting">
                          <IconButton onClick={() => startMeeting(meeting.meetingId)}>
                            <PlayArrow />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit Meeting">
                          <IconButton onClick={() => { setSelectedMeeting(meeting); setMeetingDialog(true); }}>
                            <Edit />
                          </IconButton>
                        </Tooltip>
                      </>
                    )}
                    {meeting.status === 'started' && (
                      <Tooltip title="End Meeting">
                        <IconButton onClick={() => endMeeting(meeting.meetingId)} color="error">
                          <Stop />
                        </IconButton>
                      </Tooltip>
                    )}
                    <Tooltip title="Share Meeting">
                      <IconButton>
                        <Share />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Recordings Tab */}
      <TabPanel value={activeTab} index={1}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6">Meeting Recordings</Typography>
        </Box>

        <Grid container spacing={3}>
          {recordings.map((recording) => (
            <Grid item xs={12} md={6} lg={4} key={recording.recordingId}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom noWrap>
                    {recording.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {new Date(recording.startTime).toLocaleDateString()}
                  </Typography>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Duration:</Typography>
                    <Typography variant="body2">{formatDuration(recording.duration)}</Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Size:</Typography>
                    <Typography variant="body2">{formatFileSize(recording.fileSize)}</Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="body2">Views:</Typography>
                    <Typography variant="body2">{recording.viewCount}</Typography>
                  </Box>

                  <Chip 
                    label={recording.status} 
                    color={recording.status === 'available' ? 'success' : 'default'}
                    size="small"
                    sx={{ mb: 2 }}
                  />

                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      size="small"
                      startIcon={<PlayArrow />}
                      onClick={() => { setSelectedRecording(recording); setRecordingDialog(true); }}
                    >
                      View
                    </Button>
                    <Button
                      size="small"
                      startIcon={<Download />}
                      onClick={() => window.open(recording.downloadUrl, '_blank')}
                    >
                      Download
                    </Button>
                    <Button
                      size="small"
                      startIcon={<Share />}
                    >
                      Share
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      {/* Analytics Tab */}
      <TabPanel value={activeTab} index={2}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Total Meetings
                </Typography>
                <Typography variant="h3" color="primary">
                  {analytics?.totalMeetings || 0}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Total Duration
                </Typography>
                <Typography variant="h3" color="success.main">
                  {analytics?.totalDuration ? formatDuration(analytics.totalDuration) : '0h 0m'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Total Participants
                </Typography>
                <Typography variant="h3" color="info.main">
                  {analytics?.totalParticipants || 0}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Provider Usage
                </Typography>
                <List dense>
                  {analytics?.providerUsage?.map((provider: any, index: number) => (
                    <ListItem key={index}>
                      <ListItemText 
                        primary={provider.name}
                        secondary={`${provider.usage}%`}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Settings Tab */}
      <TabPanel value={activeTab} index={3}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Video Providers
                </Typography>
                <List>
                  {providers.map((provider, index) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={provider.displayName}
                        secondary={`Max Participants: ${provider.settings?.maxParticipants || 'Unlimited'}`}
                      />
                      <ListItemSecondaryAction>
                        <Switch checked={provider.isActive} />
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Default Meeting Settings
                </Typography>
                <List>
                  <ListItem>
                    <ListItemText primary="Auto-Record Meetings" />
                    <ListItemSecondaryAction>
                      <Switch />
                    </ListItemSecondaryAction>
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Enable Waiting Room" />
                    <ListItemSecondaryAction>
                      <Switch defaultChecked />
                    </ListItemSecondaryAction>
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Mute on Entry" />
                    <ListItemSecondaryAction>
                      <Switch defaultChecked />
                    </ListItemSecondaryAction>
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Enable Chat" />
                    <ListItemSecondaryAction>
                      <Switch defaultChecked />
                    </ListItemSecondaryAction>
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Schedule Meeting Dialog */}
      <Dialog open={meetingDialog} onClose={() => setMeetingDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Schedule New Meeting</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Meeting Title"
                value={newMeeting.title}
                onChange={(e) => setNewMeeting(prev => ({ ...prev, title: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Description"
                value={newMeeting.description}
                onChange={(e) => setNewMeeting(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="datetime-local"
                label="Start Time"
                value={newMeeting.startTime}
                onChange={(e) => setNewMeeting(prev => ({ ...prev, startTime: e.target.value }))}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="Duration (minutes)"
                value={newMeeting.duration}
                onChange={(e) => setNewMeeting(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Video Provider</InputLabel>
                <Select
                  value={newMeeting.providerId}
                  label="Video Provider"
                  onChange={(e) => setNewMeeting(prev => ({ ...prev, providerId: e.target.value }))}
                >
                  <MenuItem value="zoom">Zoom</MenuItem>
                  <MenuItem value="teams">Microsoft Teams</MenuItem>
                  <MenuItem value="webex">Cisco WebEx</MenuItem>
                  <MenuItem value="googlemeet">Google Meet</MenuItem>
                  <MenuItem value="bigbluebutton">BigBlueButton</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography>Meeting Options</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={newMeeting.options.passwordProtected}
                            onChange={(e) => setNewMeeting(prev => ({
                              ...prev,
                              options: { ...prev.options, passwordProtected: e.target.checked }
                            }))}
                          />
                        }
                        label="Password Protected"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={newMeeting.options.waitingRoom}
                            onChange={(e) => setNewMeeting(prev => ({
                              ...prev,
                              options: { ...prev.options, waitingRoom: e.target.checked }
                            }))}
                          />
                        }
                        label="Waiting Room"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={newMeeting.options.autoRecord}
                            onChange={(e) => setNewMeeting(prev => ({
                              ...prev,
                              options: { ...prev.options, autoRecord: e.target.checked }
                            }))}
                          />
                        }
                        label="Auto Record"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={newMeeting.options.enableBreakoutRooms}
                            onChange={(e) => setNewMeeting(prev => ({
                              ...prev,
                              options: { ...prev.options, enableBreakoutRooms: e.target.checked }
                            }))}
                          />
                        }
                        label="Breakout Rooms"
                      />
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => { setMeetingDialog(false); resetForm(); }}>Cancel</Button>
          <Button onClick={scheduleMeeting} variant="contained" disabled={loading}>
            Schedule Meeting
          </Button>
        </DialogActions>
      </Dialog>

      {/* Recording Details Dialog */}
      <Dialog open={recordingDialog} onClose={() => setRecordingDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Recording Details</DialogTitle>
        <DialogContent>
          {selectedRecording && (
            <Box>
              <Typography variant="h6">{selectedRecording.title}</Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                Date: {new Date(selectedRecording.startTime).toLocaleDateString()}
              </Typography>
              <Typography variant="body2">
                Duration: {formatDuration(selectedRecording.duration)}
              </Typography>
              <Typography variant="body2">
                Size: {formatFileSize(selectedRecording.fileSize)}
              </Typography>
              <Typography variant="body2">
                Views: {selectedRecording.viewCount}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRecordingDialog(false)}>Close</Button>
          <Button variant="contained" onClick={() => selectedRecording && window.open(selectedRecording.downloadUrl, '_blank')}>
            Download
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VideoConferencingPage;

# Generated by Django 4.2.7 on 2025-07-04 15:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Assignment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                ("due_date", models.DateTimeField()),
                ("total_points", models.PositiveIntegerField(default=100)),
                (
                    "weight",
                    models.DecimalField(
                        decimal_places=2,
                        default=10.0,
                        help_text="Percentage weight in final grade",
                        max_digits=5,
                    ),
                ),
                (
                    "file_attachment",
                    models.FileField(blank=True, null=True, upload_to="assignments/"),
                ),
                ("is_graded", models.BooleanField(default=False)),
                ("submission_count", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["due_date"],
            },
        ),
        migrations.CreateModel(
            name="CourseGrade",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "grade",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("A+", "A+"),
                            ("A", "A"),
                            ("A-", "A-"),
                            ("B+", "B+"),
                            ("B", "B"),
                            ("B-", "B-"),
                            ("C+", "C+"),
                            ("C", "C"),
                            ("C-", "C-"),
                            ("D+", "D+"),
                            ("D", "D"),
                            ("D-", "D-"),
                            ("F", "F"),
                        ],
                        max_length=2,
                        null=True,
                    ),
                ),
                ("numeric_grade", models.FloatField(blank=True, null=True)),
                ("comments", models.TextField(blank=True)),
                ("assigned_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="Grade",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("score", models.FloatField()),
                ("feedback", models.TextField(blank=True)),
                ("graded_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="Submission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("file", models.FileField(upload_to="submissions/")),
                ("submitted_at", models.DateTimeField(auto_now_add=True)),
                ("comments", models.TextField(blank=True)),
                (
                    "assignment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="submissions",
                        to="grades.assignment",
                    ),
                ),
            ],
        ),
    ]

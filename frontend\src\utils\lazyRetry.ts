import { ComponentType } from 'react';

/**
 * Retry utility for failed lazy component imports
 * This helps handle network issues and temporary loading failures
 * 
 * @param importFunc - Function that returns a promise resolving to a module
 * @param retryAttempts - Number of retry attempts (default: 3)
 * @param delay - Base delay between retries in ms (default: 1000)
 * @returns Promise that resolves to the module or rejects after all attempts fail
 */
export const lazyRetry = async <T = any>(
  importFunc: () => Promise<{ default: ComponentType<T> }>,
  retryAttempts: number = 3,
  delay: number = 1000
): Promise<{ default: ComponentType<T> }> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= retryAttempts; attempt++) {
    try {
      // Attempt to import the component
      const module = await importFunc();
      
      // Success! Return the module
      return module;
    } catch (error) {
      console.warn(`Lazy import attempt ${attempt}/${retryAttempts} failed:`, error);
      lastError = error as Error;

      // If this isn't the last attempt, wait before retrying
      if (attempt < retryAttempts) {
        // Exponential backoff: wait longer with each attempt
        const waitTime = delay * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }

  // All attempts failed, throw the last error
  console.error(`All ${retryAttempts} lazy import attempts failed. Last error:`, lastError);
  throw lastError;
};

/**
 * Enhanced retry with exponential backoff and jitter
 * Helps prevent thundering herd problems when many components fail simultaneously
 */
export const lazyRetryWithJitter = async <T = any>(
  importFunc: () => Promise<{ default: ComponentType<T> }>,
  retryAttempts: number = 3,
  baseDelay: number = 1000,
  maxDelay: number = 10000
): Promise<{ default: ComponentType<T> }> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= retryAttempts; attempt++) {
    try {
      const module = await importFunc();
      return module;
    } catch (error) {
      console.warn(`Lazy import attempt ${attempt}/${retryAttempts} failed:`, error);
      lastError = error as Error;

      if (attempt < retryAttempts) {
        // Exponential backoff with jitter
        const exponentialDelay = baseDelay * Math.pow(2, attempt - 1);
        const jitter = Math.random() * 0.5 + 0.5; // Random factor between 0.5 and 1
        const waitTime = Math.min(exponentialDelay * jitter, maxDelay);
        
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }

  console.error(`All ${retryAttempts} lazy import attempts failed. Last error:`, lastError);
  throw lastError;
};

/**
 * Network-aware retry that adjusts behavior based on connection quality
 */
export const lazyRetryNetworkAware = async <T = any>(
  importFunc: () => Promise<{ default: ComponentType<T> }>,
  retryAttempts: number = 3
): Promise<{ default: ComponentType<T> }> => {
  // Check network connection if available
  const connection = (navigator as any).connection;
  const isSlowConnection = connection && (
    connection.effectiveType === 'slow-2g' || 
    connection.effectiveType === '2g' ||
    connection.saveData
  );

  // Adjust retry parameters based on connection
  const baseDelay = isSlowConnection ? 2000 : 1000;
  const maxAttempts = isSlowConnection ? 2 : retryAttempts;

  return lazyRetryWithJitter(importFunc, maxAttempts, baseDelay);
};

/**
 * Cache for successful imports to avoid re-importing
 */
const importCache = new Map<string, Promise<{ default: ComponentType<any> }>>();

/**
 * Cached lazy retry - stores successful imports to avoid repeated network requests
 */
export const lazyRetryCached = async <T = any>(
  importFunc: () => Promise<{ default: ComponentType<T> }>,
  cacheKey: string,
  retryAttempts: number = 3
): Promise<{ default: ComponentType<T> }> => {
  // Check cache first
  if (importCache.has(cacheKey)) {
    return importCache.get(cacheKey)!;
  }

  // Create promise for this import
  const importPromise = lazyRetry(importFunc, retryAttempts);
  
  // Cache the promise
  importCache.set(cacheKey, importPromise);

  try {
    const result = await importPromise;
    return result;
  } catch (error) {
    // Remove failed import from cache
    importCache.delete(cacheKey);
    throw error;
  }
};

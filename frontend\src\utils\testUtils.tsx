import React, { ReactElement } from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { I18nextProvider } from 'react-i18next';
import { configureStore, EnhancedStore } from '@reduxjs/toolkit';

// Import your store configuration
import { enhancedStore } from '../store/enhancedStore';
import { theme } from '../theme';
import i18n from '../i18n/i18n';

// Mock data generators
import { generateMockStudent, generateMockCourse } from './mockDataGenerators';

// Custom matchers
import './customMatchers';

interface ExtendedRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  preloadedState?: any;
  store?: EnhancedStore;
  route?: string;
  theme?: any;
  locale?: string;
}

// Create a test store with optional preloaded state
function createTestStore(preloadedState?: any) {
  return configureStore({
    reducer: enhancedStore.getState(),
    preloadedState,
  });
}

// Custom render function that includes providers
function customRender(
  ui: ReactElement,
  {
    preloadedState,
    store = createTestStore(preloadedState),
    route = '/',
    theme: customTheme = theme,
    locale = 'en',
    ...renderOptions
  }: ExtendedRenderOptions = {}
): RenderResult & { store: EnhancedStore } {
  // Set the locale
  i18n.changeLanguage(locale);

  // Set initial route
  window.history.pushState({}, 'Test page', route);

  function Wrapper({ children }: { children?: React.ReactNode }) {
    return (
      <Provider store={store}>
        <BrowserRouter>
          <ThemeProvider theme={customTheme}>
            <I18nextProvider i18n={i18n}>
              {children}
            </I18nextProvider>
          </ThemeProvider>
        </BrowserRouter>
      </Provider>
    );
  }

  const renderResult = render(ui, { wrapper: Wrapper, ...renderOptions });

  return {
    ...renderResult,
    store,
  };
}

// Helper functions for common testing scenarios
export const testUtils = {
  // Wait for async operations
  waitForDataToLoad: async (timeout = 5000) => {
    return new Promise((resolve) => {
      const checkData = () => {
        const loadingElements = document.querySelectorAll('[data-testid*="loading"]');
        if (loadingElements.length === 0) {
          resolve(true);
        } else {
          setTimeout(checkData, 100);
        }
      };
      setTimeout(() => resolve(false), timeout);
      checkData();
    });
  },

  // Mock error boundary
  mockErrorBoundary: (shouldError = false) => {
    if (shouldError) {
      const error = new Error('Test error');
      error.stack = 'Test stack trace';
      throw error;
    }
  },

  // Simulate user interactions
  userInteractions: {
    fillForm: async (formData: Record<string, string>) => {
      const { userEvent } = await import('@testing-library/user-event');
      const user = userEvent.setup();
      
      for (const [fieldName, value] of Object.entries(formData)) {
        const input = document.querySelector(`[name="${fieldName}"]`) as HTMLInputElement;
        if (input) {
          await user.clear(input);
          await user.type(input, value);
        }
      }
    },

    clickButton: async (buttonText: string) => {
      const { userEvent } = await import('@testing-library/user-event');
      const user = userEvent.setup();
      const { getByRole } = await import('@testing-library/react');
      
      const button = getByRole('button', { name: buttonText });
      await user.click(button);
    },

    selectOption: async (selectName: string, optionText: string) => {
      const { userEvent } = await import('@testing-library/user-event');
      const user = userEvent.setup();
      const { getByLabelText, getByText } = await import('@testing-library/react');
      
      const select = getByLabelText(selectName);
      await user.click(select);
      const option = getByText(optionText);
      await user.click(option);
    },
  },

  // Mock API responses
  mockApiResponses: {
    success: (data: any) => ({
      ok: true,
      json: async () => data,
      status: 200,
    }),

    error: (message = 'API Error', status = 500) => ({
      ok: false,
      json: async () => ({ error: message }),
      status,
    }),

    loading: () => new Promise(() => {}), // Never resolves
  },

  // Component state assertions
  assertions: {
    hasLoading: (container: HTMLElement) => {
      return container.querySelector('[data-testid*="loading"]') !== null;
    },

    hasError: (container: HTMLElement, errorMessage?: string) => {
      const errorElement = container.querySelector('[data-testid*="error"]');
      if (!errorElement) return false;
      if (errorMessage) {
        return errorElement.textContent?.includes(errorMessage) || false;
      }
      return true;
    },

    hasData: (container: HTMLElement, testId: string) => {
      return container.querySelector(`[data-testid="${testId}"]`) !== null;
    },

    isAccessible: async (container: HTMLElement) => {
      const { axe } = await import('jest-axe');
      const results = await axe(container);
      return results.violations.length === 0;
    },
  },

  // Redux state helpers
  redux: {
    getStateSlice: (store: EnhancedStore, slice: string) => {
      return store.getState()[slice];
    },

    dispatchAction: (store: EnhancedStore, action: any) => {
      store.dispatch(action);
    },

    waitForStateChange: async (store: EnhancedStore, predicate: (state: any) => boolean) => {
      return new Promise((resolve) => {
        const unsubscribe = store.subscribe(() => {
          if (predicate(store.getState())) {
            unsubscribe();
            resolve(true);
          }
        });
      });
    },
  },

  // Performance testing
  performance: {
    measureRenderTime: async (renderFn: () => void) => {
      const start = performance.now();
      renderFn();
      const end = performance.now();
      return end - start;
    },

    measureMemoryUsage: () => {
      if ('memory' in performance) {
        return (performance as any).memory.usedJSHeapSize;
      }
      return null;
    },
  },

  // Mock data
  mockData: {
    student: generateMockStudent,
    course: generateMockCourse,
    
    // Generate arrays of mock data
    students: (count = 5) => Array.from({ length: count }, (_, i) => generateMockStudent({ id: i + 1 })),
    courses: (count = 3) => Array.from({ length: count }, (_, i) => generateMockCourse({ id: i + 1 })),
  },
};

// Re-export everything from React Testing Library
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';

// Export the custom render as the default render
export { customRender as render };

// Export additional testing utilities
export {
  createTestStore,
  generateMockStudent,
  generateMockCourse,
};

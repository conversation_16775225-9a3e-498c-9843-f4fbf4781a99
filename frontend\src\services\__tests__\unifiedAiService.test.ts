/**
 * Unified AI Service Tests
 *
 * Comprehensive tests for the Unified AI Service to ensure
 * it works correctly with ML operations and analytics.
 */

import unifiedAiService from '../unifiedAiService';
import { BaseAIService } from '../utils/BaseAIService';
import { AIServiceError } from '../utils/aiServiceUtils';
import mockAxios from 'jest-mock-axios';

// Use global axios mock from jest-mock-axios

describe('Unified AI Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Structure', () => {
    test('should be instance of BaseAIService', () => {
      expect(unifiedAiService).toBeInstanceOf(BaseAIService);
    });

    test('should have correct service info', () => {
      const info = unifiedAiService.getServiceInfo();
      expect(info.name).toBe('Unified AI');
      expect(info.endpoint).toContain('ai');
    });

    test('should have all required methods', () => {
      expect(typeof unifiedAiService.getCapabilities).toBe('function');
      expect(typeof unifiedAiService.getMLAnalytics).toBe('function');
      expect(typeof unifiedAiService.executeWorkflow).toBe('function');
      expect(typeof unifiedAiService.getPersonalizedLearningPath).toBe(
        'function'
      );
      expect(typeof unifiedAiService.getContentRecommendations).toBe(
        'function'
      );
      expect(typeof unifiedAiService.analyzeLearningPatterns).toBe('function');
      expect(typeof unifiedAiService.predictPerformance).toBe('function');
      expect(typeof unifiedAiService.getSystemHealth).toBe('function');
      expect(typeof unifiedAiService.getUsageAnalytics).toBe('function');
      expect(typeof unifiedAiService.testConnectivity).toBe('function');
    });
  });

  describe('getCapabilities', () => {
    test('should get AI capabilities successfully', async () => {
      const mockCapabilities = {
        available_agents: ['math_tutor', 'science_tutor', 'general_tutor'],
        specializations: {
          math_tutor: ['algebra', 'calculus', 'geometry'],
          science_tutor: ['physics', 'chemistry', 'biology'],
        },
        ml_available: true,
        langgraph_available: true,
      };

      // Mock the service method directly instead of axios
      jest.spyOn(unifiedAiService, 'getCapabilities').mockResolvedValueOnce(mockCapabilities);

      const result = await unifiedAiService.getCapabilities();

      expect(result).toEqual(mockCapabilities);
    });
  });

  describe('getMLAnalytics', () => {
    test('should get ML analytics successfully', async () => {
      const mockAnalytics = {
        learning_patterns: {
          preferred_learning_style: 'visual',
          peak_learning_hours: [9, 10, 14, 15],
          difficulty_progression: 'steady',
        },
        performance_predictions: {
          next_assessment_score: 0.85,
          completion_probability: 0.92,
          recommended_study_time: 120,
        },
        content_recommendations: [
          {
            id: 1,
            title: 'Advanced Calculus',
            type: 'video',
            score: 0.95,
            reason: 'Matches learning style and current progress',
          },
        ],
      };

      mockAxios.get.mockResolvedValueOnce({
        data: mockAnalytics,
      });

      const result = await unifiedAiService.getMLAnalytics(123, 456);

      expect(mockAxios.get).toHaveBeenCalledWith('ml-analytics/', {
        student_id: 123,
        course_id: 456,
      });
      expect(result).toEqual(mockAnalytics);
    });

    test('should get ML analytics without course ID', async () => {
      const mockAnalytics = {
        learning_patterns: {},
        performance_predictions: {},
      };

      mockAxios.get.mockResolvedValueOnce({
        data: mockAnalytics,
      });

      await unifiedAiService.getMLAnalytics(123);

      expect(mockAxios.get).toHaveBeenCalledWith('ml-analytics/', {
        student_id: 123,
        course_id: undefined,
      });
    });
  });

  describe('executeWorkflow', () => {
    test('should execute workflow successfully', async () => {
      const mockResult = {
        workflow_type: 'learning_path_generation',
        result: {
          path: ['intro_to_programming', 'data_structures', 'algorithms'],
          estimated_duration: '12 weeks',
          difficulty_level: 'intermediate',
        },
        execution_time: 2.5,
        success: true,
      };

      mockAxios.post.mockResolvedValueOnce({
        data: mockResult,
      });

      const parameters = {
        student_id: 123,
        target_skills: ['programming', 'algorithms'],
        time_constraint: '3 months',
      };

      const result = await unifiedAiService.executeWorkflow(
        'learning_path_generation',
        parameters
      );

      expect(mockAxios.post).toHaveBeenCalledWith('workflow/', {
        workflow_type: 'learning_path_generation',
        parameters,
      });
      expect(result).toEqual(mockResult);
    });
  });

  describe('getPersonalizedLearningPath', () => {
    test('should get personalized learning path successfully', async () => {
      const mockPath = {
        path_id: 'path_123',
        courses: [
          { id: 1, title: 'Introduction to Programming', order: 1 },
          { id: 2, title: 'Data Structures', order: 2 },
        ],
        estimated_duration: '16 weeks',
        difficulty_progression: 'gradual',
      };

      mockAxios.post.mockResolvedValueOnce({
        data: mockPath,
      });

      const result = await unifiedAiService.getPersonalizedLearningPath(
        123,
        456,
        ['programming fundamentals', 'problem solving']
      );

      expect(mockAxios.post).toHaveBeenCalledWith('learning-path/', {
        student_id: 123,
        course_id: 456,
        learning_objectives: ['programming fundamentals', 'problem solving'],
      });
      expect(result).toEqual(mockPath);
    });

    test('should handle empty learning objectives', async () => {
      const mockPath = { path_id: 'path_456', courses: [] };

      mockAxios.post.mockResolvedValueOnce({
        data: mockPath,
      });

      await unifiedAiService.getPersonalizedLearningPath(123, 456);

      expect(mockAxios.post).toHaveBeenCalledWith('learning-path/', {
        student_id: 123,
        course_id: 456,
        learning_objectives: [],
      });
    });
  });

  describe('getContentRecommendations', () => {
    test('should get content recommendations successfully', async () => {
      const studentProfile = {
        learning_style: 'visual',
        skill_level: 'intermediate',
        interests: ['programming', 'AI'],
      };

      const availableContent = [
        { id: 1, title: 'Python Basics', type: 'video' },
        { id: 2, title: 'Machine Learning Intro', type: 'article' },
      ];

      const learningObjectives = ['learn python', 'understand ML'];

      const mockRecommendations = {
        recommendations: [
          {
            content_id: 1,
            relevance_score: 0.95,
            reasoning: 'Matches visual learning style and programming interest',
          },
        ],
      };

      mockAxios.post.mockResolvedValueOnce({
        data: mockRecommendations,
      });

      const result = await unifiedAiService.getContentRecommendations(
        studentProfile,
        availableContent,
        learningObjectives
      );

      expect(mockAxios.post).toHaveBeenCalledWith(
        'content-recommendations/',
        {
          student_profile: studentProfile,
          available_content: availableContent,
          learning_objectives: learningObjectives,
        }
      );
      expect(result).toEqual(mockRecommendations);
    });
  });

  describe('analyzeLearningPatterns', () => {
    test('should analyze learning patterns successfully', async () => {
      const studentData = [
        { session_id: 1, duration: 60, topics: ['algebra'], performance: 0.8 },
        { session_id: 2, duration: 45, topics: ['geometry'], performance: 0.9 },
      ];

      const mockAnalysis = {
        patterns: {
          optimal_session_length: 50,
          best_performance_topics: ['geometry'],
          learning_velocity: 'fast',
        },
        insights: [
          'Student performs better in shorter sessions',
          'Strong aptitude for geometry',
        ],
      };

      mockAxios.post.mockResolvedValueOnce({
        data: mockAnalysis,
      });

      const result =
        await unifiedAiService.analyzeLearningPatterns(studentData);

      expect(mockAxios.post).toHaveBeenCalledWith('analyze-patterns/', {
        student_data: studentData,
      });
      expect(result).toEqual(mockAnalysis);
    });
  });

  describe('predictPerformance', () => {
    test('should predict performance successfully', async () => {
      const studentFeatures = {
        current_gpa: 3.5,
        study_hours_per_week: 15,
        previous_course_performance: [0.8, 0.9, 0.7],
      };

      const historicalData = [
        { course_id: 1, final_grade: 0.85 },
        { course_id: 2, final_grade: 0.92 },
      ];

      const mockPrediction = {
        predicted_grade: 0.87,
        confidence_interval: [0.82, 0.92],
        risk_factors: ['heavy course load'],
        recommendations: [
          'increase study time',
          'seek tutoring for weak areas',
        ],
      };

      mockAxios.post.mockResolvedValueOnce({
        data: mockPrediction,
      });

      const result = await unifiedAiService.predictPerformance(
        studentFeatures,
        historicalData
      );

      expect(mockAxios.post).toHaveBeenCalledWith('predict-performance/', {
        student_features: studentFeatures,
        historical_data: historicalData,
      });
      expect(result).toEqual(mockPrediction);
    });
  });

  describe('System Health and Analytics', () => {
    test('should get system health successfully', async () => {
      const mockHealth = {
        status: 'healthy',
        services: {
          ml_service: 'healthy',
          langgraph_service: 'healthy',
          database: 'healthy',
        },
        uptime: '99.9%',
        last_check: '2024-01-01T12:00:00Z',
      };

      mockAxios.get.mockResolvedValueOnce({
        data: mockHealth,
      });

      const result = await unifiedAiService.getSystemHealth();

      expect(mockAxios.get).toHaveBeenCalledWith('health/');
      expect(result).toEqual(mockHealth);
    });

    test('should get usage analytics successfully', async () => {
      const mockAnalytics = {
        total_requests: 1500,
        average_response_time: 2.3,
        popular_workflows: [
          'learning_path_generation',
          'content_recommendation',
        ],
        user_satisfaction: 0.92,
      };

      mockAxios.get.mockResolvedValueOnce({
        data: mockAnalytics,
      });

      const result = await unifiedAiService.getUsageAnalytics('30d');

      expect(mockAxios.get).toHaveBeenCalledWith('usage-analytics/', {
        time_range: '30d',
      });
      expect(result).toEqual(mockAnalytics);
    });

    test('should use default time range for usage analytics', async () => {
      mockAxios.get.mockResolvedValueOnce({ data: {} });

      await unifiedAiService.getUsageAnalytics();

      expect(mockAxios.get).toHaveBeenCalledWith('usage-analytics/', {
        time_range: '7d',
      });
    });

    test('should test connectivity successfully', async () => {
      const mockConnectivity = {
        status: 'connected',
        response_time: 150,
        services_reachable: ['ml_service', 'database', 'cache'],
      };

      mockAxios.get.mockResolvedValueOnce({
        data: mockConnectivity,
      });

      const result = await unifiedAiService.testConnectivity();

      expect(mockAxios.get).toHaveBeenCalledWith('test-connectivity/');
      expect(result).toEqual(mockConnectivity);
    });
  });

  describe('Configuration', () => {
    test('should have appropriate default configuration for ML operations', () => {
      const config = unifiedAiService.getConfig();

      expect(config.timeout).toBe(60000); // 60 seconds for ML operations
      expect(config.retries).toBe(2);
      expect(config.fallbackEnabled).toBe(true);
      expect(config.cacheEnabled).toBe(true);
      expect(config.cacheTTL).toBe(600000); // 10 minutes cache for analytics
    });
  });

  describe('Error Handling', () => {
    test('should handle ML service errors gracefully', async () => {
      const mlError = new AIServiceError(
        'ML service unavailable',
        'SERVER_ERROR',
        503
      );
      mockAxios.get.mockRejectedValueOnce(mlError);

      // Should handle gracefully with fallback
      await expect(unifiedAiService.getCapabilities()).resolves.not.toThrow();
    });

    test('should handle timeout errors for long-running operations', async () => {
      const timeoutError = new AIServiceError(
        'Operation timeout',
        'TIMEOUT_ERROR',
        408
      );
      mockAxios.post.mockRejectedValueOnce(timeoutError);

      // Should handle timeout gracefully
      await expect(
        unifiedAiService.executeWorkflow('test', {})
      ).resolves.not.toThrow();
    });
  });
});

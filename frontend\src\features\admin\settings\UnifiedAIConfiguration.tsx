import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Alert,
  Chip,
  CircularProgress,
  Switch,
  FormControlLabel,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  VisibilityOff as VisibilityOffIcon,
  Settings as SettingsIcon,
  TrendingUp as TrendingUpIcon,
  Speed as SpeedIcon,
  Security as SecurityIcon,
  Key as KeyIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartTooltip, 
  ResponsiveContainer
} from 'recharts';
import { useToast } from '../../../hooks/useToast';
import apiKeyService, { 
  ApiKeyStatus, 
  ModelConfig, 
  ModelInfo,
  TokenUsage,
  AdminInfo
} from '../../../services/apiKeyService';

interface UnifiedAIConfigurationProps {}

export const UnifiedAIConfiguration: React.FC<UnifiedAIConfigurationProps> = () => {
  // States
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [keyStatus, setKeyStatus] = useState<ApiKeyStatus | null>(null);
  const [modelConfig, setModelConfig] = useState<ModelConfig | null>(null);
  const [modelInfo, setModelInfo] = useState<ModelInfo | null>(null);
  const [tokenUsage, setTokenUsage] = useState<TokenUsage[]>([]);
  const [adminInfo, setAdminInfo] = useState<AdminInfo | null>(null);
    // Configuration states
  const [temperature, setTemperature] = useState(0.7);
  const [maxTokens, setMaxTokens] = useState(1500);
  const [enableCaching, setEnableCaching] = useState(true);
  const [enableMonitoring, setEnableMonitoring] = useState(true);
  const [rateLimitPerMinute, setRateLimitPerMinute] = useState(60);
  
  // Model-specific states
  const [selectedModel, setSelectedModel] = useState('gemini-2.0-flash');
  const [fallbackModel, setFallbackModel] = useState('gemini-1.5-pro');
    // Dialog states
  const [viewConfigDialog, setViewConfigDialog] = useState(false);
  const [testResultsDialog, setTestResultsDialog] = useState(false);
  const [apiKeyDialog, setApiKeyDialog] = useState(false);
  
  // API Key management states
  const [newApiKey, setNewApiKey] = useState('');
  const [showApiKey, setShowApiKey] = useState(false);
  
  const { showSuccess, showError } = useToast();  // Fetch configuration data
  const fetchConfiguration = async () => {
    console.log('🔄 Starting fetchConfiguration...');
    setLoading(true);
    try {
      // Use the unified AI configuration endpoint as the primary source
      console.log('📡 Calling getAIConfiguration...');
      const aiConfig = await apiKeyService.getAIConfiguration();
      console.log('📊 Received AI config:', aiConfig);
      
      if (aiConfig && aiConfig.config) {
        const config = aiConfig.config;
        const modelInfo = aiConfig.model_info;
        const adminInfo = aiConfig.admin_info;
        
        console.log('🔑 API key info from config:', {
          api_key_configured: config.api_key_configured,
          service_available: config.service_available,
          api_key_masked: config.api_key_masked
        });
        
        // Update API key status from unified config
        const newKeyStatus = {
          valid: config.api_key_configured || false,
          message: config.api_key_configured ? 'API key is valid and active' : 'No API key configured',
          key_preview: config.api_key_masked || 'Not configured',
          service_available: config.service_available || false,
        };
        
        console.log('🎯 Setting keyStatus to:', newKeyStatus);
        setKeyStatus(newKeyStatus);
        
        // Update configuration settings from unified response
        if (config.temperature !== undefined) setTemperature(config.temperature);
        if (config.max_tokens !== undefined) setMaxTokens(config.max_tokens);
        if (config.enable_caching !== undefined) setEnableCaching(config.enable_caching);
        if (config.enable_monitoring !== undefined) setEnableMonitoring(config.enable_monitoring);
        if (config.rate_limit_per_minute !== undefined) setRateLimitPerMinute(config.rate_limit_per_minute);
        if (config.default_model) setSelectedModel(config.default_model);
        if (config.fallback_model) setFallbackModel(config.fallback_model);
        
        // Update model info from unified response
        if (modelInfo) {
          setModelInfo({
            currentModel: modelInfo.currentModel || config.default_model,
            availableModels: modelInfo.availableModels || ['gemini-2.0-flash', 'gemini-1.5-pro'],
            fallbackModel: modelInfo.fallbackModel || config.fallback_model,
            temperature: modelInfo.temperature || config.temperature,
            maxTokens: modelInfo.maxTokens || config.max_tokens,
            timeout: modelInfo.timeout || config.timeout,
            retries: modelInfo.retries || config.retries,
          });
        }
        
        // Update admin info
        if (adminInfo) {
          setAdminInfo(adminInfo);
        }
        
        // For backward compatibility, still fetch usage data separately if needed
        try {
          const usage = await apiKeyService.getTokenUsage();
          setTokenUsage(Array.isArray(usage) ? usage : []);
        } catch (error) {
          console.warn('Could not fetch token usage, using empty array:', error);
          setTokenUsage([]);
        }
        
        console.log('✅ fetchConfiguration completed successfully');
        
      } else {
        throw new Error('Invalid response from unified AI configuration endpoint');
      }

    } catch (error: any) {
      console.error('❌ Error fetching configuration:', error);
      showError('Failed to fetch configuration data');
      
      // Fallback: try individual endpoints if unified endpoint fails
      try {
        console.log('🔄 Trying fallback API key test...');
        const status = await apiKeyService.testApiKey();
        console.log('📊 Fallback test result:', status);
        setKeyStatus(status);
      } catch (fallbackError) {
        console.error('❌ Fallback API key test also failed:', fallbackError);
      }
    } finally {
      setLoading(false);
    }
  };
  // Save configuration
  const saveConfiguration = async () => {
    setLoading(true);
    try {
      const configData = {
        temperature,
        max_tokens: maxTokens,
        enable_caching: enableCaching,
        enable_monitoring: enableMonitoring,
        rate_limit_per_minute: rateLimitPerMinute,
        default_model: selectedModel,
        fallback_model: fallbackModel,
      };

      await apiKeyService.updateAIConfiguration(configData);
      showSuccess('Configuration saved successfully');
      await fetchConfiguration(); // Refresh data
    } catch (error: any) {
      console.error('Error saving configuration:', error);
      showError('Failed to save configuration');
    } finally {
      setLoading(false);
    }
  };// Test API Key
  const testApiKey = async () => {
    setLoading(true);
    try {
      const result = await apiKeyService.testApiKey();
      setKeyStatus(result);
      setTestResultsDialog(true);
    } catch (error: any) {
      console.error('Error testing API key:', error);
      showError('Failed to test API key');
    } finally {
      setLoading(false);
    }
  };  // Update API Key
  const updateApiKey = async () => {
    if (!newApiKey.trim()) {
      showError('Please enter a valid API key');
      return;
    }

    setLoading(true);
    try {
      console.log('🔄 Starting API key update...');
      const result = await apiKeyService.updateApiKey(newApiKey);
      console.log('API key update result:', result);
      
      if (result.success !== false) {
        showSuccess('API key updated successfully');
        setNewApiKey('');
        setApiKeyDialog(false);
        
        console.log('⏳ Waiting for backend configuration to propagate...');
        // Wait a moment for backend configuration to propagate
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        console.log('🔄 Refreshing configuration data...');
        // Force refresh of all configuration data
        await fetchConfiguration();
        
        console.log('🧪 Testing API key to update status...');
        // Also test the API key to update status immediately
        const testResult = await apiKeyService.testApiKey();
        console.log('API key test result:', testResult);
        setKeyStatus(testResult);
        
        console.log('✅ API key update process complete');
      } else {
        throw new Error(result.error || 'Failed to update API key');
      }
    } catch (error: any) {
      console.error('❌ Error updating API key:', error);
      showError(error.message || 'Failed to update API key');
    } finally {
      setLoading(false);
    }
  };

  // Export configuration data
  const exportData = async () => {
    try {      const dataToExport = {
        keyStatus,
        modelConfig,
        modelInfo,
        tokenUsage,
        adminInfo,
        configuration: {
          temperature,
          maxTokens,
          enableCaching,
          enableMonitoring,
          rateLimitPerMinute,
        },
        totalTokens: Array.isArray(tokenUsage) ? tokenUsage.reduce((sum, item) => sum + (item.tokens || 0), 0) : 0,
        totalRequests: Array.isArray(tokenUsage) ? tokenUsage.reduce((sum, item) => sum + (item.requests || 0), 0) : 0,
      };

      const blob = new Blob([JSON.stringify(dataToExport, null, 2)], {
        type: 'application/json',
      });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `ai-config-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      showSuccess('Configuration exported successfully');
    } catch (error: any) {
      console.error('Error exporting data:', error);
      showError('Failed to export data');
    }
  };

  // Calculate usage statistics
  const usageStats = React.useMemo(() => {
    // Ensure tokenUsage is always an array
    const usageArray = Array.isArray(tokenUsage) ? tokenUsage : [];
    const totalTokens = usageArray.reduce((sum, item) => sum + (item.tokens || 0), 0);
    const totalRequests = usageArray.reduce((sum, item) => sum + (item.requests || 0), 0);
    const avgTokensPerRequest = totalRequests > 0 ? totalTokens / totalRequests : 0;
    
    return {
      totalTokens,
      totalRequests,
      avgTokensPerRequest: Math.round(avgTokensPerRequest),
    };
  }, [tokenUsage]);

  useEffect(() => {
    fetchConfiguration();
    
    // Load saved configuration
    const savedConfig = localStorage.getItem('unifiedAIConfig');
    if (savedConfig) {
      try {
        const config = JSON.parse(savedConfig);
        setTemperature(config.temperature || 0.7);
        setMaxTokens(config.maxTokens || 1500);
        setEnableCaching(config.enableCaching !== false);
        setEnableMonitoring(config.enableMonitoring !== false);
        setRateLimitPerMinute(config.rateLimitPerMinute || 60);
      } catch (error) {
        console.error('Error loading saved configuration:', error);
      }
    }
  }, []);

  // Save configuration to localStorage whenever it changes
  useEffect(() => {
    const config = {
      temperature,
      maxTokens,
      enableCaching,
      enableMonitoring,
      rateLimitPerMinute,
    };
    localStorage.setItem('unifiedAIConfig', JSON.stringify(config));
  }, [temperature, maxTokens, enableCaching, enableMonitoring, rateLimitPerMinute]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };  const getStatusColor = (isValid: boolean) => isValid ? 'success' : 'error';
  const getStatusText = (isValid: boolean) => isValid ? 'Valid' : 'Invalid';

  return (
    <Box sx={{ p: 3 }}>
      <Card elevation={3}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h4" component="h1" gutterBottom>
              Unified AI Configuration
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Refresh Data">
                <IconButton onClick={fetchConfiguration} disabled={loading}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Export Configuration">
                <IconButton onClick={exportData}>
                  <DownloadIcon />
                </IconButton>
              </Tooltip>              <Tooltip title="View Current Configuration">
                <IconButton onClick={() => setViewConfigDialog(true)}>
                  <ViewIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Update API Key">
                <IconButton onClick={() => setApiKeyDialog(true)}>
                  <KeyIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 3 }}>
            <Tab icon={<SettingsIcon />} label="Configuration" />
            <Tab icon={<TrendingUpIcon />} label="Usage Analytics" />
            <Tab icon={<SpeedIcon />} label="Performance" />
            <Tab icon={<SecurityIcon />} label="Security & Admin" />
          </Tabs>

          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          )}          {/* Configuration Tab */}
          {activeTab === 0 && (
            <Grid container spacing={3}>
              {/* API Key Setup Info */}
              {(!keyStatus || !keyStatus.valid) && (
                <Grid item xs={12}>
                  <Alert severity="warning" sx={{ mb: 3 }}>
                    <Typography variant="h6" gutterBottom>
                      API Key Required
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 2 }}>
                      To use all AI features (chat, study assistant, course generation), you need to configure a valid Google Gemini API key.
                    </Typography>
                    <Button
                      variant="contained"
                      onClick={() => setApiKeyDialog(true)}
                      startIcon={<KeyIcon />}
                      size="small"
                    >
                      Add API Key
                    </Button>
                  </Alert>
                </Grid>
              )}
              
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Model Configuration
                  </Typography>
                  <TextField
                    label="Temperature"
                    type="number"
                    value={temperature}
                    onChange={(e) => setTemperature(parseFloat(e.target.value))}
                    inputProps={{ min: 0, max: 2, step: 0.1 }}
                    fullWidth
                    margin="normal"
                    helperText="Controls randomness in responses (0.0 - 2.0)"
                  />
                  <TextField
                    label="Max Tokens"
                    type="number"
                    value={maxTokens}
                    onChange={(e) => setMaxTokens(parseInt(e.target.value))}
                    inputProps={{ min: 100, max: 4000 }}
                    fullWidth
                    margin="normal"
                    helperText="Maximum tokens per response"
                  />
                  <TextField
                    label="Rate Limit (per minute)"
                    type="number"
                    value={rateLimitPerMinute}
                    onChange={(e) => setRateLimitPerMinute(parseInt(e.target.value))}
                    inputProps={{ min: 1, max: 1000 }}
                    fullWidth
                    margin="normal"
                    helperText="API calls allowed per minute"
                  />
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Feature Settings
                  </Typography>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={enableCaching}
                        onChange={(e) => setEnableCaching(e.target.checked)}
                      />
                    }
                    label="Enable Response Caching"
                  />
                  <br />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={enableMonitoring}
                        onChange={(e) => setEnableMonitoring(e.target.checked)}
                      />
                    }
                    label="Enable Usage Monitoring"                  />
                </Paper>
                
                <Paper sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Model Configuration
                  </Typography>
                  <TextField
                    select
                    label="Primary Model"
                    value={selectedModel}
                    onChange={(e) => setSelectedModel(e.target.value)}
                    fullWidth
                    margin="normal"
                    helperText="Select the primary AI model to use"
                    SelectProps={{
                      native: true,
                    }}
                  >
                    {modelInfo?.availableModels?.map((model) => (
                      <option key={model} value={model}>
                        {model}
                      </option>
                    )) || (
                      <>
                        <option value="gemini-2.0-flash">gemini-2.0-flash</option>
                        <option value="gemini-1.5-pro">gemini-1.5-pro</option>
                        <option value="gemini-1.5-flash">gemini-1.5-flash</option>
                        <option value="gemini-pro">gemini-pro</option>
                      </>
                    )}
                  </TextField>
                  <TextField
                    select
                    label="Fallback Model"
                    value={fallbackModel}
                    onChange={(e) => setFallbackModel(e.target.value)}
                    fullWidth
                    margin="normal"
                    helperText="Select the fallback model when primary fails"
                    SelectProps={{
                      native: true,
                    }}
                  >
                    {modelInfo?.availableModels?.map((model) => (
                      <option key={model} value={model}>
                        {model}
                      </option>
                    )) || (
                      <>
                        <option value="gemini-1.5-pro">gemini-1.5-pro</option>
                        <option value="gemini-2.0-flash">gemini-2.0-flash</option>
                        <option value="gemini-1.5-flash">gemini-1.5-flash</option>
                        <option value="gemini-pro">gemini-pro</option>
                      </>
                    )}
                  </TextField>
                </Paper>

                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    API Key Management
                  </Typography>
                  {keyStatus && (
                    <Chip
                      label={getStatusText(keyStatus.valid)}
                      color={getStatusColor(keyStatus.valid)}
                      sx={{ mb: 2 }}
                    />
                  )}
                  <Box sx={{ mb: 2 }}>
                    <Button
                      variant="outlined"
                      onClick={() => setApiKeyDialog(true)}
                      startIcon={<AddIcon />}
                      sx={{ mr: 1 }}
                    >
                      Update API Key
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={testApiKey}
                      disabled={loading}
                    >
                      Test API Key
                    </Button>
                  </Box>
                  <Box sx={{ mt: 2 }}>
                    <Button
                      variant="contained"
                      onClick={saveConfiguration}
                      startIcon={<SaveIcon />}
                      disabled={loading}
                      sx={{ mr: 1 }}
                    >
                      Save Configuration
                    </Button>
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          )}

          {/* Usage Analytics Tab */}
          {activeTab === 1 && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Token Usage Trend
                  </Typography>
                  {Array.isArray(tokenUsage) && tokenUsage.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={tokenUsage.slice(-7)}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <RechartTooltip />
                        <Bar dataKey="tokens" fill="#8884d8" />
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                      No usage data available
                    </Typography>
                  )}
                </Paper>
              </Grid>

              <Grid item xs={12} md={4}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Usage Summary
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Total Tokens
                    </Typography>
                    <Typography variant="h5">
                      {usageStats.totalTokens.toLocaleString()}
                    </Typography>
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Total Requests
                    </Typography>
                    <Typography variant="h5">
                      {usageStats.totalRequests.toLocaleString()}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Avg Tokens/Request
                    </Typography>
                    <Typography variant="h5">
                      {usageStats.avgTokensPerRequest}
                    </Typography>
                  </Box>
                </Paper>
              </Grid>

              <Grid item xs={12}>                  <Paper sx={{ p: 3 }}>
                    <Typography variant="h6" gutterBottom>
                      Recent Usage History
                    </Typography>
                    <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                      {Array.isArray(tokenUsage) && tokenUsage.slice(0, 20).map((usage, index) => (
                        <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', py: 1, borderBottom: '1px solid #eee' }}>
                          <Typography variant="body2">
                            {usage.date || `Entry ${index + 1}`}
                          </Typography>
                          <Typography variant="body2">
                            {usage.tokens || 0} tokens, {usage.requests || 0} requests
                          </Typography>
                        </Box>
                      ))}
                      {(!Array.isArray(tokenUsage) || tokenUsage.length === 0) && (
                        <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                          No usage history available
                        </Typography>
                      )}
                    </Box>
                  </Paper>
              </Grid>
            </Grid>
          )}

          {/* Performance Tab */}
          {activeTab === 2 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Paper sx={{ p: 3 }}>                  <Typography variant="h6" gutterBottom>
                    Model Performance Metrics
                  </Typography>
                  {modelInfo ? (
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">
                          Current Model
                        </Typography>
                        <Typography variant="h6">
                          {modelInfo.currentModel}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">
                          Available Models
                        </Typography>
                        <Typography variant="h6">
                          {modelInfo.availableModels?.length || 0} models
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">
                          Fallback Model
                        </Typography>
                        <Typography variant="h6">
                          {modelInfo.fallbackModel}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">
                          Configuration
                        </Typography>
                        <Typography variant="body2">
                          Temperature: {modelInfo.temperature}<br/>
                          Max Tokens: {modelInfo.maxTokens}<br/>
                          Timeout: {modelInfo.timeout}ms<br/>
                          Retries: {modelInfo.retries}
                        </Typography>
                      </Grid>
                    </Grid>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No performance data available
                    </Typography>
                  )}
                </Paper>
              </Grid>
            </Grid>
          )}          {/* Security & Admin Tab */}
          {activeTab === 3 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Paper sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    API Key Configuration
                  </Typography>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">
                        Current API Key Status
                      </Typography>
                      {keyStatus ? (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                          <Chip
                            label={getStatusText(keyStatus.valid)}
                            color={getStatusColor(keyStatus.valid)}
                            size="small"
                          />
                          {keyStatus.message && (
                            <Typography variant="caption" color="text.secondary">
                              {keyStatus.message}
                            </Typography>
                          )}
                        </Box>
                      ) : (
                        <Typography variant="body2">
                          No API key configured
                        </Typography>
                      )}
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        <Button
                          variant="contained"
                          onClick={() => setApiKeyDialog(true)}
                          startIcon={<KeyIcon />}
                          size="small"
                        >
                          Update API Key
                        </Button>
                        <Button
                          variant="outlined"
                          onClick={testApiKey}
                          disabled={loading}
                          size="small"
                        >
                          Test Key
                        </Button>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
              
              <Grid item xs={12}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Admin Information
                  </Typography>                  {adminInfo ? (
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">
                          Admin User
                        </Typography>
                        <Typography variant="h6">
                          {adminInfo.username || 'Unknown'}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">
                          Role
                        </Typography>
                        <Typography variant="h6">
                          {adminInfo.role || 'Administrator'}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">
                          Last Login
                        </Typography>
                        <Typography variant="h6">
                          {adminInfo.lastLogin || 'Never'}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">
                          Total API Requests
                        </Typography>
                        <Typography variant="h6">
                          {adminInfo.totalRequests?.toLocaleString() || '0'}
                        </Typography>
                      </Grid>
                    </Grid>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No admin information available
                    </Typography>
                  )}
                </Paper>
              </Grid>
            </Grid>
          )}
        </CardContent>
      </Card>

      {/* View Configuration Dialog */}
      <Dialog open={viewConfigDialog} onClose={() => setViewConfigDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Current Configuration</DialogTitle>
        <DialogContent>
          <pre style={{ fontSize: '12px', overflow: 'auto' }}>
            {JSON.stringify({
              temperature,
              maxTokens,
              enableCaching,              enableMonitoring,
              rateLimitPerMinute,
              keyStatus,
              modelConfig,
              modelInfo,
            }, null, 2)}
          </pre>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewConfigDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>      {/* Test Results Dialog */}
      <Dialog open={testResultsDialog} onClose={() => setTestResultsDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>API Key Test Results</DialogTitle>
        <DialogContent>
          {keyStatus && (            <Box>
              <Alert severity={keyStatus.valid ? 'success' : 'error'} sx={{ mb: 2 }}>
                API Key is {keyStatus.valid ? 'valid' : 'invalid'}
              </Alert>
              {keyStatus.message && (
                <Typography variant="body2">
                  {keyStatus.message}
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTestResultsDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* API Key Update Dialog */}
      <Dialog open={apiKeyDialog} onClose={() => setApiKeyDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Update API Key</DialogTitle>        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Enter your Google Gemini API key to enable all AI services in the platform.
          </Typography>
          
            <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>How to get a Google Gemini API key:</strong><br />
              1. Visit <a href="https://aistudio.google.com/app/apikey" target="_blank" rel="noopener noreferrer">Google AI Studio</a><br />
              2. Sign in to your Google account<br />
              3. Click "Create API key"<br />
              4. Copy the key and paste it below
            </Typography>
          </Alert>
          
          <TextField
            label="API Key"
            type={showApiKey ? 'text' : 'password'}
            value={newApiKey}
            onChange={(e) => setNewApiKey(e.target.value)}
            fullWidth
            margin="normal"
            placeholder="sk-..."
            InputProps={{
              endAdornment: (
                <IconButton
                  onClick={() => setShowApiKey(!showApiKey)}
                  edge="end"
                >
                  {showApiKey ? <VisibilityOffIcon /> : <ViewIcon />}
                </IconButton>
              ),
            }}
            helperText="This key will be used for all AI services including chat, study assistant, and course generation."
          />
          {keyStatus && !keyStatus.valid && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              Current API key is invalid. Please enter a valid key.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApiKeyDialog(false)}>Cancel</Button>
          <Button 
            onClick={updateApiKey} 
            variant="contained" 
            disabled={loading || !newApiKey.trim()}
          >
            {loading ? <CircularProgress size={20} /> : 'Update Key'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UnifiedAIConfiguration;

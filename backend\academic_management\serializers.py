from rest_framework import serializers
from django.contrib.auth.models import User
from .models import (
    AcademicTerm, TranscriptRecord, CoursePrerequisite, 
    CourseWaitlist, EnrollmentHistory, AcademicStanding
)
from courses.models import Course


class AcademicTermSerializer(serializers.ModelSerializer):
    """Serializer for AcademicTerm model"""
    
    class Meta:
        model = AcademicTerm
        fields = [
            'id', 'name', 'term_type', 'start_date', 'end_date', 
            'registration_start', 'registration_end', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate(self, data):
        """Validate term dates"""
        if data.get('start_date') and data.get('end_date'):
            if data['start_date'] >= data['end_date']:
                raise serializers.ValidationError("End date must be after start date")
        
        if data.get('registration_start') and data.get('registration_end'):
            if data['registration_start'] >= data['registration_end']:
                raise serializers.ValidationError("Registration end date must be after registration start date")
        
        return data


class TranscriptRecordSerializer(serializers.ModelSerializer):
    """Serializer for TranscriptRecord model"""
    course_name = serializers.CharField(source='course.name', read_only=True)
    course_code = serializers.CharField(source='course.code', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)
    credits = serializers.IntegerField(source='course.credits', read_only=True)
    
    class Meta:
        model = TranscriptRecord
        fields = [
            'id', 'student', 'course', 'course_name', 'course_code',
            'term', 'term_name', 'grade', 'credits', 'grade_points',
            'is_transfer_credit', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'grade_points', 'created_at', 'updated_at']

    def validate_grade(self, value):
        """Validate grade value"""
        valid_grades = ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-', 'D+', 'D', 'F', 'W', 'I', 'P']
        if value not in valid_grades:
            raise serializers.ValidationError(f"Invalid grade. Must be one of: {', '.join(valid_grades)}")
        return value


class CoursePrerequisiteSerializer(serializers.ModelSerializer):
    """Serializer for CoursePrerequisite model"""
    course_name = serializers.CharField(source='course.name', read_only=True)
    course_code = serializers.CharField(source='course.code', read_only=True)
    prerequisite_course_name = serializers.CharField(source='prerequisite_course.name', read_only=True)
    prerequisite_course_code = serializers.CharField(source='prerequisite_course.code', read_only=True)
    
    class Meta:
        model = CoursePrerequisite
        fields = [
            'id', 'course', 'course_name', 'course_code',
            'prerequisite_course', 'prerequisite_course_name', 'prerequisite_course_code',
            'minimum_grade', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate(self, data):
        """Validate prerequisite relationship"""
        if data.get('course') == data.get('prerequisite_course'):
            raise serializers.ValidationError("A course cannot be a prerequisite of itself")
        return data


class CourseWaitlistSerializer(serializers.ModelSerializer):
    """Serializer for CourseWaitlist model"""
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    student_email = serializers.CharField(source='student.email', read_only=True)
    course_name = serializers.CharField(source='course.name', read_only=True)
    course_code = serializers.CharField(source='course.code', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)
    
    class Meta:
        model = CourseWaitlist
        fields = [
            'id', 'student', 'student_name', 'student_email',
            'course', 'course_name', 'course_code',
            'term', 'term_name', 'position', 'added_at',
            'status', 'notified_at', 'expires_at'
        ]
        read_only_fields = ['id', 'position', 'added_at', 'notified_at']

    def validate_status(self, value):
        """Validate waitlist status"""
        valid_statuses = ['waiting', 'notified', 'enrolled', 'expired', 'cancelled']
        if value not in valid_statuses:
            raise serializers.ValidationError(f"Invalid status. Must be one of: {', '.join(valid_statuses)}")
        return value


class EnrollmentHistorySerializer(serializers.ModelSerializer):
    """Serializer for EnrollmentHistory model"""
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    course_name = serializers.CharField(source='course.name', read_only=True)
    course_code = serializers.CharField(source='course.code', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)
    
    class Meta:
        model = EnrollmentHistory
        fields = [
            'id', 'student', 'student_name', 'course', 'course_name', 'course_code',
            'term', 'term_name', 'enrollment_date', 'drop_date',
            'status', 'grade', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_status(self, value):
        """Validate enrollment status"""
        valid_statuses = ['enrolled', 'dropped', 'completed', 'withdrawn']
        if value not in valid_statuses:
            raise serializers.ValidationError(f"Invalid status. Must be one of: {', '.join(valid_statuses)}")
        return value


class AcademicStandingSerializer(serializers.ModelSerializer):
    """Serializer for AcademicStanding model"""
    student_name = serializers.CharField(source='student.get_full_name', read_only=True)
    student_email = serializers.CharField(source='student.email', read_only=True)
    term_name = serializers.CharField(source='term.name', read_only=True)
    
    class Meta:
        model = AcademicStanding
        fields = [
            'id', 'student', 'student_name', 'student_email',
            'term', 'term_name', 'gpa', 'cumulative_gpa',
            'credits_attempted', 'credits_earned', 'academic_standing',
            'probation_reason', 'calculated_at', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'calculated_at', 'created_at', 'updated_at']

    def validate_gpa(self, value):
        """Validate GPA value"""
        if value < 0.0 or value > 4.0:
            raise serializers.ValidationError("GPA must be between 0.0 and 4.0")
        return value

    def validate_cumulative_gpa(self, value):
        """Validate cumulative GPA value"""
        if value < 0.0 or value > 4.0:
            raise serializers.ValidationError("Cumulative GPA must be between 0.0 and 4.0")
        return value

    def validate_academic_standing(self, value):
        """Validate academic standing"""
        valid_standings = ['good_standing', 'probation', 'suspension', 'dismissal', 'deans_list', 'honors']
        if value not in valid_standings:
            raise serializers.ValidationError(f"Invalid standing. Must be one of: {', '.join(valid_standings)}")
        return value


# Nested serializers for detailed views
class DetailedTranscriptRecordSerializer(TranscriptRecordSerializer):
    """Detailed serializer for transcript records with course information"""
    course_details = serializers.SerializerMethodField()
    
    def get_course_details(self, obj):
        return {
            'id': obj.course.id,
            'name': obj.course.name,
            'code': obj.course.code,
            'credits': obj.course.credits,
            'description': obj.course.description,
            'department': obj.course.department.name if obj.course.department else None
        }


class StudentTranscriptSerializer(serializers.Serializer):
    """Serializer for complete student transcript"""
    student_id = serializers.IntegerField()
    student_name = serializers.CharField()
    student_email = serializers.CharField()
    total_credits_attempted = serializers.IntegerField()
    total_credits_earned = serializers.IntegerField()
    cumulative_gpa = serializers.DecimalField(max_digits=3, decimal_places=2)
    current_academic_standing = serializers.CharField()
    transcript_records = DetailedTranscriptRecordSerializer(many=True)
    academic_standings = AcademicStandingSerializer(many=True)
    
    class Meta:
        fields = [
            'student_id', 'student_name', 'student_email',
            'total_credits_attempted', 'total_credits_earned',
            'cumulative_gpa', 'current_academic_standing',
            'transcript_records', 'academic_standings'
        ]


class PrerequisiteCheckSerializer(serializers.Serializer):
    """Serializer for prerequisite check results"""
    student_id = serializers.IntegerField()
    course_id = serializers.IntegerField()
    course_name = serializers.CharField()
    course_code = serializers.CharField()
    can_enroll = serializers.BooleanField()
    missing_prerequisites = serializers.ListField(
        child=serializers.DictField()
    )
    completed_prerequisites = serializers.ListField(
        child=serializers.DictField()
    )
    
    class Meta:
        fields = [
            'student_id', 'course_id', 'course_name', 'course_code',
            'can_enroll', 'missing_prerequisites', 'completed_prerequisites'
        ]


class WaitlistPositionSerializer(serializers.Serializer):
    """Serializer for waitlist position information"""
    waitlist_id = serializers.IntegerField()
    student_id = serializers.IntegerField()
    course_id = serializers.IntegerField()
    course_name = serializers.CharField()
    course_code = serializers.CharField()
    term_name = serializers.CharField()
    position = serializers.IntegerField()
    total_waitlist_size = serializers.IntegerField()
    estimated_enrollment_chance = serializers.CharField()
    added_at = serializers.DateTimeField()
    
    class Meta:
        fields = [
            'waitlist_id', 'student_id', 'course_id', 'course_name',
            'course_code', 'term_name', 'position', 'total_waitlist_size',
            'estimated_enrollment_chance', 'added_at'
        ]

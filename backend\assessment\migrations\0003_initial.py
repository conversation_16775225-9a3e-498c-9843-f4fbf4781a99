# Generated by Django 4.2.7 on 2025-07-04 15:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("core", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("assessment", "0002_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="studentlevel",
            name="student",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="level_profile",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="assessmentsettings",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="assessment_settings_created",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="assessmentresponse",
            name="assessment",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="responses",
                to="assessment.assessment",
            ),
        ),
        migrations.AddField(
            model_name="assessmentresponse",
            name="question",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="responses",
                to="assessment.assessmentquestion",
            ),
        ),
        migrations.AddField(
            model_name="assessmentresponse",
            name="student",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="assessment_responses",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="assessmentquestion",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="%(class)s_created",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="assessmentquestion",
            name="skills_assessed",
            field=models.ManyToManyField(
                blank=True, related_name="questions", to="core.skill"
            ),
        ),
        migrations.AddField(
            model_name="assessment",
            name="questions",
            field=models.ManyToManyField(
                through="assessment.AssessmentResponse",
                to="assessment.assessmentquestion",
            ),
        ),
        migrations.AddField(
            model_name="assessment",
            name="student",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="assessments",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="assessmentsettings",
            unique_together={("assessment_type",)},
        ),
        migrations.AddIndex(
            model_name="assessmentresponse",
            index=models.Index(
                fields=["assessment", "question"], name="assessment_response_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="assessmentresponse",
            index=models.Index(
                fields=["student", "is_correct"], name="student_correct_response_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="assessmentresponse",
            unique_together={("assessment", "question")},
        ),
    ]

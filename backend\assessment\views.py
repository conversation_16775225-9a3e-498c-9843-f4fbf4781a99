from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db.models import Avg, Count, Q
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from django.db import transaction
from django.apps import apps
from django.db import models
from datetime import timed<PERSON><PERSON>
import json
import random
import logging

from rest_framework import generics, permissions, serializers, status, views, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from .models import (
    Assessment,
    AssessmentQuestion,
    AssessmentResponse,
    AssessmentSettings,
    StudentLevel,
    LevelRequirement
)
from core.models import Skill
from .serializers import (
    AdminQuestionListSerializer,
    AIQuestionSuggestionSerializer,
    AssessmentDetailSerializer,
    AssessmentQuestionSerializer,
    AssessmentResponseSerializer,
    AssessmentSerializer,
    AssessmentSubmissionSerializer,
    InitialAssessmentSerializer,
    SkillSerializer,
    StudentLevelSerializer
)

# Import Course model
try:
    from courses.models import Course
except ImportError:
    # Fallback to importing from models module
    try:
        Course = apps.get_model("courses", "Course")
    except LookupError:
        # Define a placeholder Course model for development
        class Course(models.Model):
            title = models.CharField(max_length=200)
            course_code = models.CharField(max_length=10)
            
            class Meta:
                app_label = 'courses'

try:
    from courses.views.course_views import CourseSerializer
except ImportError:
    # Fallback if CourseSerializer doesn't exist
    CourseSerializer = None

# Use core permissions with fallbacks
try:
    from core.permissions import (
        CanTakeAssessments,
        IsAdminUser,
        IsProfessorUser,
        IsStudentUser
    )
except ImportError:
    from rest_framework.permissions import IsAuthenticated
    # Fallback permission classes
    CanTakeAssessments = IsAuthenticated
    IsAdminUser = IsAuthenticated
    IsProfessorUser = IsAuthenticated
    IsStudentUser = IsAuthenticated

# AI Service imports with fallbacks
try:
    from utils.consolidated_ai_service import (
        ConsolidatedAIError as AIServiceError,
        ContentGenerationError as ResponseGenerationError
    )
except ImportError:
    class AIServiceError(Exception):
        pass
    class ResponseGenerationError(Exception):
        pass

try:
    from core.exceptions import RateLimitError
except ImportError:
    class RateLimitError(Exception):
        pass

try:
    from utils.ai.services import get_ai_service
except ImportError:
    def get_ai_service():
        return None

try:
    from .prevent_mock_data import (
        prevent_mock_question_creation,
        prevent_mock_response_creation,
        sanitize_assessment_data
    )
except ImportError:
    def prevent_mock_question_creation(data):
        return False
    def prevent_mock_response_creation(data):
        return False
    def sanitize_assessment_data(data):
        return data

try:
    from .services import AssessmentService
except ImportError:
    class AssessmentService:
        def __init__(self, user=None):
            self.user = user
        def start_assessment(self, **kwargs):
            return {"status": "error", "message": "AssessmentService not available"}
        def complete_assessment(self, assessment):
            return assessment
        def get_student_progress(self, user):
            return {"progress": "Not available"}
        def analyze_strengths_weaknesses(self, assessment):
            return {"strengths": [], "weaknesses": []}
        def get_course_recommendations(self, assessment):
            return []
        def get_learning_path(self, assessment):
            return {}

User = get_user_model()
assessment_ai = None
logger = logging.getLogger(__name__)


class AssessmentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for handling assessment operations
    """
    serializer_class = AssessmentSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            return Assessment.objects.all()
        return Assessment.objects.filter(student=user)
    
    def perform_create(self, serializer):
        serializer.save(student=self.request.user)
    
    @action(detail=True, methods=['post'])
    def submit(self, request, pk=None):
        """Submit an assessment"""
        assessment = self.get_object()
        if assessment.student != request.user:
            return Response(
                {'error': 'Permission denied'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = AssessmentSubmissionSerializer(data=request.data)
        if serializer.is_valid():
            try:
                with transaction.atomic():
                    # Process assessment submission
                    assessment.submitted_at = timezone.now()
                    assessment.save()
                    
                    # Calculate score
                    total_questions = assessment.questions.count()
                    correct_answers = assessment.responses.filter(is_correct=True).count()
                    assessment.score = (correct_answers / total_questions) * 100 if total_questions > 0 else 0
                    assessment.save()
                    
                    return Response({
                        'message': 'Assessment submitted successfully',
                        'score': assessment.score,
                        'total_questions': total_questions,
                        'correct_answers': correct_answers
                    })
            except Exception as e:
                logger.error(f"Error submitting assessment: {e}")
                return Response(
                    {'error': 'Failed to submit assessment'}, 
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['get'])
    def questions(self, request, pk=None):
        """Get questions for an assessment"""
        assessment = self.get_object()
        questions = assessment.questions.all()
        serializer = AssessmentQuestionSerializer(questions, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def results(self, request, pk=None):
        """Get assessment results"""
        assessment = self.get_object()
        if assessment.student != request.user and not request.user.is_staff:
            return Response(
                {'error': 'Permission denied'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = AssessmentDetailSerializer(assessment)
        return Response(serializer.data)


class AssessmentQuestionViewSet(viewsets.ModelViewSet):
    """
    ViewSet for handling assessment questions
    """
    serializer_class = AssessmentQuestionSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return AssessmentQuestion.objects.all()
    
    def perform_create(self, serializer):
        # Check for mock data prevention
        if prevent_mock_question_creation(self.request.data):
            raise serializers.ValidationError("Mock question creation prevented")
        
        serializer.save(created_by=self.request.user)


class AssessmentResponseViewSet(viewsets.ModelViewSet):
    """
    ViewSet for handling assessment responses
    """
    serializer_class = AssessmentResponseSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            return AssessmentResponse.objects.all()
        return AssessmentResponse.objects.filter(assessment__student=user)
    
    def perform_create(self, serializer):
        # Check for mock data prevention
        if prevent_mock_response_creation(self.request.data):
            raise serializers.ValidationError("Mock response creation prevented")
        
        serializer.save()


class InitialAssessmentView(generics.CreateAPIView):
    """
    View for creating initial assessments
    """
    serializer_class = InitialAssessmentSerializer
    permission_classes = [IsAuthenticated, CanTakeAssessments]
    
    def create(self, request, *args, **kwargs):
        # Check if user already has an initial assessment
        existing_assessment = Assessment.objects.filter(
            student=request.user,
            assessment_type='initial'
        ).first()
        
        if existing_assessment:
            return Response(
                {'error': 'Initial assessment already taken'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            try:
                with transaction.atomic():
                    assessment = serializer.save(
                        student=request.user,
                        assessment_type='initial'
                    )
                    
                    # Use AssessmentService to start the assessment
                    service = AssessmentService(user=request.user)
                    result = service.start_assessment(
                        assessment_type='initial',
                        difficulty_level=request.data.get('difficulty_level', 'beginner')
                    )
                    
                    return Response({
                        'assessment_id': assessment.id,
                        'message': 'Initial assessment created successfully',
                        'service_result': result
                    }, status=status.HTTP_201_CREATED)
            except Exception as e:
                logger.error(f"Error creating initial assessment: {e}")
                return Response(
                    {'error': 'Failed to create initial assessment'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PlacementAssessmentView(generics.CreateAPIView):
    """
    View for creating placement assessments
    """
    serializer_class = AssessmentSerializer
    permission_classes = [IsAuthenticated, CanTakeAssessments]
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            try:
                with transaction.atomic():
                    assessment = serializer.save(
                        student=request.user,
                        assessment_type='placement'
                    )
                    
                    return Response({
                        'assessment_id': assessment.id,
                        'message': 'Placement assessment created successfully'
                    }, status=status.HTTP_201_CREATED)
            except Exception as e:
                logger.error(f"Error creating placement assessment: {e}")
                return Response(
                    {'error': 'Failed to create placement assessment'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class StudentProgressView(APIView):
    """
    View for getting student progress
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        try:
            service = AssessmentService(user=request.user)
            progress = service.get_student_progress(request.user)
            
            return Response({
                'progress': progress,
                'message': 'Student progress retrieved successfully'
            })
        except Exception as e:
            logger.error(f"Error getting student progress: {e}")
            return Response(
                {'error': 'Failed to retrieve progress'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CourseRecommendationsView(APIView):
    """
    View for getting course recommendations based on assessment
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, assessment_id):
        try:
            assessment = get_object_or_404(Assessment, id=assessment_id)
            
            if assessment.student != request.user and not request.user.is_staff:
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            service = AssessmentService(user=request.user)
            recommendations = service.get_course_recommendations(assessment)
            
            return Response({
                'recommendations': recommendations,
                'message': 'Course recommendations retrieved successfully'
            })
        except Exception as e:
            logger.error(f"Error getting course recommendations: {e}")
            return Response(
                {'error': 'Failed to retrieve recommendations'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SkillAssessmentView(APIView):
    """
    View for skill-based assessments
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get available skills for assessment"""
        skills = Skill.objects.all()
        serializer = SkillSerializer(skills, many=True)
        return Response(serializer.data)
    
    def post(self, request):
        """Create a skill-based assessment"""
        skill_ids = request.data.get('skill_ids', [])
        
        if not skill_ids:
            return Response(
                {'error': 'At least one skill must be selected'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            skills = Skill.objects.filter(id__in=skill_ids)
            
            with transaction.atomic():
                assessment = Assessment.objects.create(
                    student=request.user,
                    assessment_type='skill',
                    title=f"Skill Assessment - {', '.join(skill.name for skill in skills)}"
                )
                
                # Generate questions for selected skills
                # This is a placeholder - implement actual question generation
                
                return Response({
                    'assessment_id': assessment.id,
                    'message': 'Skill assessment created successfully',
                    'skills': [skill.name for skill in skills]
                }, status=status.HTTP_201_CREATED)
        except Exception as e:
            logger.error(f"Error creating skill assessment: {e}")
            return Response(
                {'error': 'Failed to create skill assessment'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class StudentLevelView(APIView):
    """
    View for getting and updating student levels
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get current student level"""
        try:
            student_level = StudentLevel.objects.get(student=request.user)
            serializer = StudentLevelSerializer(student_level)
            return Response(serializer.data)
        except StudentLevel.DoesNotExist:
            return Response(
                {'error': 'Student level not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error getting student level: {e}")
            return Response(
                {'error': 'Failed to retrieve student level'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def put(self, request):
        """Update student level"""
        try:
            student_level = StudentLevel.objects.get(student=request.user)
            serializer = StudentLevelSerializer(student_level, data=request.data, partial=True)
            
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except StudentLevel.DoesNotExist:
            return Response(
                {'error': 'Student level not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error updating student level: {e}")
            return Response(
                {'error': 'Failed to update student level'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AIQuestionGenerationView(APIView):
    """
    View for AI-powered question generation
    """
    permission_classes = [IsAuthenticated, IsProfessorUser]
    
    def post(self, request):
        """Generate questions using AI"""
        try:
            serializer = AIQuestionSuggestionSerializer(data=request.data)
            if serializer.is_valid():
                # This is a placeholder for AI question generation
                # Implement actual AI service integration
                
                ai_service = get_ai_service()
                if not ai_service:
                    return Response(
                        {'error': 'AI service not available'},
                        status=status.HTTP_503_SERVICE_UNAVAILABLE
                    )
                
                # Generate questions based on the request
                topic = serializer.validated_data.get('topic')
                difficulty = serializer.validated_data.get('difficulty', 'medium')
                count = serializer.validated_data.get('count', 5)
                
                # Placeholder response
                questions = []
                for i in range(count):
                    questions.append({
                        'question': f"Sample question {i+1} about {topic}",
                        'choices': ['A', 'B', 'C', 'D'],
                        'correct_answer': 'A',
                        'difficulty': difficulty
                    })
                
                return Response({
                    'questions': questions,
                    'message': f'Generated {count} questions for {topic}'
                })
            
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error generating AI questions: {e}")
            return Response(
                {'error': 'Failed to generate questions'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AssessmentAnalyticsView(APIView):
    """
    View for assessment analytics and statistics
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get assessment analytics"""
        try:
            user = request.user
            
            # Get user's assessments
            assessments = Assessment.objects.filter(student=user)
            
            # Calculate statistics
            total_assessments = assessments.count()
            completed_assessments = assessments.filter(submitted_at__isnull=False).count()
            average_score = assessments.aggregate(Avg('score'))['score__avg'] or 0
            
            # Get recent assessments
            recent_assessments = assessments.order_by('-created_at')[:5]
            
            return Response({
                'total_assessments': total_assessments,
                'completed_assessments': completed_assessments,
                'average_score': round(average_score, 2),
                'recent_assessments': AssessmentSerializer(recent_assessments, many=True).data,
                'message': 'Analytics retrieved successfully'
            })
        except Exception as e:
            logger.error(f"Error getting assessment analytics: {e}")
            return Response(
                {'error': 'Failed to retrieve analytics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Admin views
class AdminAssessmentView(APIView):
    """
    Admin view for managing assessments
    """
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def get(self, request):
        """Get all assessments for admin"""
        try:
            assessments = Assessment.objects.all()
            serializer = AssessmentSerializer(assessments, many=True)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error getting admin assessments: {e}")
            return Response(
                {'error': 'Failed to retrieve assessments'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def post(self, request):
        """Create assessment as admin"""
        try:
            serializer = AssessmentSerializer(data=request.data)
            if serializer.is_valid():
                assessment = serializer.save()
                return Response(
                    AssessmentSerializer(assessment).data,
                    status=status.HTTP_201_CREATED
                )
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error creating admin assessment: {e}")
            return Response(
                {'error': 'Failed to create assessment'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AdminQuestionView(APIView):
    """
    Admin view for managing questions
    """
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def get(self, request):
        """Get all questions for admin"""
        try:
            questions = AssessmentQuestion.objects.all()
            serializer = AdminQuestionListSerializer(questions, many=True)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error getting admin questions: {e}")
            return Response(
                {'error': 'Failed to retrieve questions'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def post(self, request):
        """Create question as admin"""
        try:
            serializer = AssessmentQuestionSerializer(data=request.data)
            if serializer.is_valid():
                question = serializer.save(created_by=request.user)
                return Response(
                    AssessmentQuestionSerializer(question).data,
                    status=status.HTTP_201_CREATED
                )
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error creating admin question: {e}")
            return Response(
                {'error': 'Failed to create question'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Utility views
class AssessmentSettingsView(APIView):
    """
    View for managing assessment settings
    """
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def get(self, request):
        """Get assessment settings"""
        try:
            settings = AssessmentSettings.objects.first()
            if not settings:
                # Create default settings
                settings = AssessmentSettings.objects.create()
            
            return Response({
                'settings': {
                    'id': settings.id,
                    'max_attempts': getattr(settings, 'max_attempts', 3),
                    'time_limit': getattr(settings, 'time_limit', 60),
                    'passing_score': getattr(settings, 'passing_score', 70)
                }
            })
        except Exception as e:
            logger.error(f"Error getting assessment settings: {e}")
            return Response(
                {'error': 'Failed to retrieve settings'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def put(self, request):
        """Update assessment settings"""
        try:
            settings = AssessmentSettings.objects.first()
            if not settings:
                settings = AssessmentSettings.objects.create()
            
            # Update settings based on request data
            for key, value in request.data.items():
                if hasattr(settings, key):
                    setattr(settings, key, value)
            
            settings.save()
            
            return Response({
                'message': 'Settings updated successfully',
                'settings': {
                    'id': settings.id,
                    'max_attempts': getattr(settings, 'max_attempts', 3),
                    'time_limit': getattr(settings, 'time_limit', 60),
                    'passing_score': getattr(settings, 'passing_score', 70)
                }
            })
        except Exception as e:
            logger.error(f"Error updating assessment settings: {e}")
            return Response(
                {'error': 'Failed to update settings'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

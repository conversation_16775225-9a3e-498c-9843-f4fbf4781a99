// Mock for utils/translationUtils
export const t = (key, params = {}) => {
  // Simple mock translation function that returns the key with params interpolated
  let result = key;
  Object.keys(params).forEach(param => {
    result = result.replace(`{{${param}}}`, params[param]);
  });
  return result;
};

export const translate = t;

export const formatMessage = (messageObj, params = {}) => {
  if (typeof messageObj === 'string') {
    return t(messageObj, params);
  }
  return t(messageObj.id || messageObj.defaultMessage || '', params);
};

export const getCurrentLanguage = () => 'en';

export const setLanguage = (language) => {
  // Mock implementation
  return Promise.resolve(language);
};

export const getSupportedLanguages = () => ['en', 'es', 'fr', 'de'];

export const isRTL = (language = 'en') => {
  const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
  return rtlLanguages.includes(language);
};

export const formatNumber = (number, locale = 'en') => {
  return new Intl.NumberFormat(locale).format(number);
};

export const formatDate = (date, locale = 'en', options = {}) => {
  return new Intl.DateTimeFormat(locale, options).format(new Date(date));
};

export const formatCurrency = (amount, currency = 'USD', locale = 'en') => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency
  }).format(amount);
};

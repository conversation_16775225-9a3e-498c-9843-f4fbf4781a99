"""AsynchronousAIServicewithRequestQueuingThismoduleprovidesasyncAIoperationswithproperqueuingandratelimiting."""importasyncioimport loggingimport timefromtypingimportAnyDictOptionalCallableListfromdataclassesimportdataclassfromenumimportEnumimportuuidfrom django.core.cacheimportcacheimport jsonlogger=logging.getLogger(__name__)classRequestPriority(Enum):"""Requestprioritylevels"""LOW=1NORMAL=2HIGH=3URGENT=4@dataclassclassAIRequest:"""AIservicerequestwithmetadata"""id:strprompt:strservice_type:strpriority:RequestPriorityuser_id:Optional[str]=Nonecontext:Optional[Dict[strAny]]=Nonecreated_at:float=Nonecallback:Optional[Callable]=Nonedef__post_init__(self):ifself.created_atisNone:self.created_at=time.time()ifself.idisNone:self.id=str(uuid.uuid4())classRequestQueue:"""PriorityqueueforAIrequests"""def__init__(selfmax_size:int=1000):self.max_size=max_sizeself.queues={RequestPriority.URGENT:[]RequestPriority.HIGH:[]RequestPriority.NORMAL:[]RequestPriority.LOW:[]}self.lock=asyncio.Lock()asyncdefenqueue(selfrequest:AIRequest)->bool:"""Addrequesttoqueue"""asyncwithself.lock:queue=self.queues[request.priority]iflen(queue)>=self.max_size//4:#Distributecapacityacrossprioritieslogger.warning(f"Queueforpriority{request.priority}isfull")returnFalsequeue.append(request)logger.debug(f"Enqueuedrequest{request.id}withpriority{request.priority}")returnTrueasyncdefdequeue(self)->Optional[AIRequest]:"""Getnextrequestfromqueue(highestpriorityfirst)"""asyncwithself.lock:forpriorityin[RequestPriority.URGENTRequestPriority.HIGHRequestPriority.NORMALRequestPriority.LOW]:queue=self.queues[priority]ifqueue:request=queue.pop(0)logger.debug(f"Dequeuedrequest{request.id}withpriority{priority}")returnrequestreturnNoneasyncdefget_queue_status(self)->Dict[strint]:"""Getcurrentqueuestatus"""asyncwithself.lock:return{priority.name:len(queue)forpriorityqueueinself.queues.items()}classRateLimiter:"""Tokenbucketratelimiter"""def__init__(selfrequests_per_minute:int=60):self.requests_per_minute=requests_per_minuteself.tokens=requests_per_minuteself.last_refill=time.time()self.lock=asyncio.Lock()asyncdefacquire(self)->bool:"""Trytoacquireatokenformakingarequest"""asyncwithself.lock:now=time.time()#Refilltokensbasedontimepassedtime_passed=now-self.last_refilltokens_to_add=time_passed*(self.requests_per_minute/60.0)self.tokens=min(self.requests_per_minuteself.tokens+tokens_to_add)self.last_refill=nowifself.tokens>=1:self.tokens-=1returnTruereturnFalseclassAsyncAIService:"""AsynchronousAIservicewithqueuingandratelimiting"""def__init__(selfrequests_per_minute:int=60max_concurrent:int=5):self.request_queue=RequestQueue()self.rate_limiter=RateLimiter(requests_per_minute)self.max_concurrent=max_concurrentself.active_requests=0self.processing_lock=asyncio.Lock()self.is_running=Falseself.worker_task=Noneasyncdefstart(self):"""StarttheasyncAIserviceworker"""ifself.is_running:returnself.is_running=Trueself.worker_task=asyncio.create_task(self._worker())logger.info("AsyncAIservicestarted")asyncdefstop(self):"""StoptheasyncAIserviceworker"""self.is_running=Falseifself.worker_task:self.worker_task.cancel()try:awaitself.worker_taskexceptasyncio.CancelledError:passlogger.info("AsyncAIservicestopped")asyncdefsubmit_request(selfprompt:strservice_type:str='general'priority:RequestPriority=RequestPriority.NORMALuser_id:Optional[str]=Nonecontext:Optional[Dict[strAny]]=None)->str:"""SubmitanAIrequestandreturnrequestID"""request=AIRequest(id=str(uuid.uuid4())prompt=promptservice_type=service_typepriority=priorityuser_id=user_idcontext=contextor{})success=awaitself.request_queue.enqueue(request)ifnotsuccess:raiseException("Requestqueueisfull")#Storerequestincacheforstatustrackingcache.set(f"ai_request_{request.id}"{'status':'queued''created_at':request.created_at'service_type':request.service_type'priority':request.priority.name}timeout=3600)returnrequest.idasyncdefget_request_status(selfrequest_id:str)->Optional[Dict[strAny]]:"""Getstatusofarequest"""returncache.get(f"ai_request_{request_id}")asyncdefget_request_result(selfrequest_id:str)->Optional[Dict[strAny]]:"""Getresultofacompletedrequest"""returncache.get(f"ai_result_{request_id}")asyncdef_worker(self):"""MainworkerloopforprocessingAIrequests"""whileself.is_running:try:#Checkifwecanprocessmorerequestsifself.active_requests>=self.max_concurrent:awaitasyncio.sleep(0.1)continue#Checkratelimitifnotawaitself.rate_limiter.acquire():awaitasyncio.sleep(1)continue#Getnextrequestrequest=awaitself.request_queue.dequeue()ifnotrequest:awaitasyncio.sleep(0.1)continue#Processrequestasynchronouslyasyncio.create_task(self._process_request(request))exceptExceptionase:logger.error(f"ErrorinAIserviceworker:{e}")awaitasyncio.sleep(1)asyncdef_process_request(selfrequest:AIRequest):"""ProcessasingleAIrequest"""asyncwithself.processing_lock:self.active_requests+=1try:#Updatestatuscache.set(f"ai_request_{request.id}"{'status':'processing''started_at':time.time()'service_type':request.service_type'priority':request.priority.name}timeout=3600)#ImportandusetheactualAIservicefrom.ai_config_simplifiedimportconfig_managerfrom.ai_resilienceimportwith_retryfallback_manager#GetAIservice(thiswouldbeyouractualimplementation)result=awaitself._call_ai_service(request.promptrequest.context)#Storeresultcache.set(f"ai_result_{request.id}"{'status':'completed''result':result'completed_at':time.time()'processing_time':time.time()-request.created_at}timeout=3600)#Updatestatuscache.set(f"ai_request_{request.id}"{'status':'completed''completed_at':time.time()'service_type':request.service_type'priority':request.priority.name}timeout=3600)logger.info(f"CompletedAIrequest{request.id}")exceptExceptionase:logger.error(f"ErrorprocessingAIrequest{request.id}:{e}")#Storeerrorresultcache.set(f"ai_result_{request.id}"{'status':'error''error':str(e)'completed_at':time.time()}timeout=3600)finally:asyncwithself.processing_lock:self.active_requests-=1asyncdef_call_ai_service(selfprompt:strcontext:Dict[strAny])->str:"""CalltheactualAIservice(placeholderforyourimplementation)"""#ThiswouldintegratewithyourexistingAIservice#Fornowsimulateprocessingtimeawaitasyncio.sleep(1)returnf"AIresponseto:{prompt[:50]}..."#GlobalasyncAIserviceinstanceasync_ai_service=AsyncAIService()
import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Alert,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  Badge,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Upload,
  Download,
  Share,
  Visibility,
  Delete,
  Refresh,
  Security,
  Assessment,
  TrendingUp,
  Settings,
  ExpandMore,
  CheckCircle,
  Warning,
  Error as ErrorIcon
} from '@mui/icons-material';
import { plagiarismDetectionService } from '../../services';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`plagiarism-tabpanel-${index}`}
      aria-labelledby={`plagiarism-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const PlagiarismDetectionPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);

  // Upload State
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [submissions, setSubmissions] = useState<any[]>([]);
  const [providers, setProviders] = useState<any[]>([]);
  const [selectedProvider, setSelectedProvider] = useState('turnitin');

  // Reports State
  const [reports, setReports] = useState<any[]>([]);
  const [selectedReport, setSelectedReport] = useState<any>(null);
  const [reportDialog, setReportDialog] = useState(false);

  // Analytics State
  const [analytics, setAnalytics] = useState<any>(null);
  const [courseAnalytics, setCourseAnalytics] = useState<any[]>([]);

  // Settings State
  const [settingsDialog, setSettingsDialog] = useState(false);
  const [providerSettings, setProviderSettings] = useState({
    sensitivity: 80,
    excludeQuotes: true,
    excludeBibliography: true,
    internetSearch: true,
    institutionDatabase: true
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedFiles(files);
  };

  const submitForAnalysis = async () => {
    if (selectedFiles.length === 0) {
      setMessage({ type: 'error', text: 'Please select files to analyze' });
      return;
    }

    setLoading(true);
    try {
      const submissionPromises = selectedFiles.map(async (file) => {
        const submission = {
          studentId: 'demo-student',
          studentName: 'Demo Student',
          assignmentId: `assignment-${Date.now()}`,
          assignmentTitle: 'Document Analysis',
          courseId: 'demo-course',
          file,
          metadata: {
            submissionDate: new Date().toISOString(),
            dueDate: new Date().toISOString(),
            language: 'en'
          }
        };

        return await plagiarismDetectionService.submitForAnalysis(
          submission,
          selectedProvider,
          providerSettings
        );
      });

      const results = await Promise.all(submissionPromises);
      setSubmissions(prev => [...prev, ...results]);
      setSelectedFiles([]);
      setMessage({ type: 'success', text: `${results.length} files submitted for analysis` });
    } catch (error) {
      console.error('Submission error:', error);
      setMessage({ type: 'error', text: 'Failed to submit files for analysis' });
    }
    setLoading(false);
  };

  const loadReports = async () => {
    setLoading(true);
    try {
      // Simulate loading reports
      const mockReports = [
        {
          reportId: '1',
          submissionId: 'sub1',
          status: 'completed',
          overallSimilarity: 15,
          matches: ['Source 1', 'Source 2'],
          flags: ['Minor similarities'],
          generatedAt: new Date().toISOString()
        },
        {
          reportId: '2',
          submissionId: 'sub2',
          status: 'completed',
          overallSimilarity: 45,
          matches: ['Academic Paper', 'Website'],
          flags: ['High similarity', 'Review needed'],
          generatedAt: new Date().toISOString()
        }
      ];
      setReports(mockReports);
    } catch (error) {
      console.error('Error loading reports:', error);
      setMessage({ type: 'error', text: 'Failed to load reports' });
    }
    setLoading(false);
  };

  const loadAnalytics = async () => {
    setLoading(true);
    try {
      const institutionReport = await plagiarismDetectionService.getInstitutionReport();
      setAnalytics(institutionReport);

      // Load course analytics
      const mockCourseAnalytics = [
        {
          courseId: 'course1',
          courseName: 'Advanced Mathematics',
          totalSubmissions: 45,
          averageSimilarity: 12,
          flaggedSubmissions: 3
        },
        {
          courseId: 'course2',
          courseName: 'English Literature',
          totalSubmissions: 38,
          averageSimilarity: 23,
          flaggedSubmissions: 8
        }
      ];
      setCourseAnalytics(mockCourseAnalytics);
    } catch (error) {
      console.error('Error loading analytics:', error);
      setMessage({ type: 'error', text: 'Failed to load analytics' });
    }
    setLoading(false);
  };

  const loadProviders = async () => {
    try {
      const providersList = await plagiarismDetectionService.getProviders();
      setProviders(providersList);
    } catch (error) {
      console.error('Error loading providers:', error);
    }
  };

  const downloadReport = async (reportId: string) => {
    try {
      const download = await plagiarismDetectionService.downloadReport(reportId, 'pdf');
      window.open(download.downloadUrl, '_blank');
      setMessage({ type: 'success', text: 'Report download started' });
    } catch (error) {
      console.error('Download error:', error);
      setMessage({ type: 'error', text: 'Failed to download report' });
    }
  };

  const getSimilarityColor = (similarity: number) => {
    if (similarity < 15) return 'success';
    if (similarity < 30) return 'warning';
    return 'error';
  };

  const getSimilarityIcon = (similarity: number) => {
    if (similarity < 15) return <CheckCircle color="success" />;
    if (similarity < 30) return <Warning color="warning" />;
    return <ErrorIcon color="error" />;
  };

  useEffect(() => {
    loadProviders();
    if (activeTab === 1) {
      loadReports();
    } else if (activeTab === 2) {
      loadAnalytics();
    }
  }, [activeTab]);

  return (
    <Box sx={{ width: '100%', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Plagiarism Detection System
      </Typography>

      {message && (
        <Alert 
          severity={message.type} 
          onClose={() => setMessage(null)}
          sx={{ mb: 2 }}
        >
          {message.text}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab icon={<Upload />} label="Submit Documents" />
          <Tab icon={<Assessment />} label="Reports" />
          <Tab icon={<TrendingUp />} label="Analytics" />
          <Tab icon={<Settings />} label="Settings" />
        </Tabs>
      </Box>

      {/* Submit Documents Tab */}
      <TabPanel value={activeTab} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Upload Documents for Analysis
                </Typography>
                <input
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx,.txt"
                  onChange={handleFileUpload}
                  style={{ marginBottom: 16, width: '100%' }}
                />
                
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Detection Provider</InputLabel>
                  <Select
                    value={selectedProvider}
                    label="Detection Provider"
                    onChange={(e) => setSelectedProvider(e.target.value)}
                  >
                    <MenuItem value="turnitin">Turnitin</MenuItem>
                    <MenuItem value="copyleaks">Copyleaks</MenuItem>
                    <MenuItem value="unicheck">Unicheck</MenuItem>
                    <MenuItem value="grammarly">Grammarly</MenuItem>
                  </Select>
                </FormControl>

                {selectedFiles.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2">Selected Files:</Typography>
                    <List dense>
                      {selectedFiles.map((file, index) => (
                        <ListItem key={index}>
                          <ListItemText primary={file.name} secondary={`${(file.size / 1024).toFixed(2)} KB`} />
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                )}

                <Button
                  variant="contained"
                  onClick={submitForAnalysis}
                  disabled={loading || selectedFiles.length === 0}
                  fullWidth
                >
                  {loading ? 'Submitting...' : 'Submit for Analysis'}
                </Button>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Recent Submissions
                </Typography>
                <List>
                  {submissions.map((submission, index) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={`Submission ${submission.submissionId}`}
                        secondary={`Status: ${submission.status} - Estimated: ${submission.estimatedTime} min`}
                      />
                      <ListItemSecondaryAction>
                        <Chip label={submission.status} color="primary" size="small" />
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Reports Tab */}
      <TabPanel value={activeTab} index={1}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6">Plagiarism Reports</Typography>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadReports}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Report ID</TableCell>
                <TableCell>Similarity Score</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Generated</TableCell>
                <TableCell>Flags</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {reports.map((report) => (
                <TableRow key={report.reportId}>
                  <TableCell>{report.reportId}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getSimilarityIcon(report.overallSimilarity)}
                      <Typography color={getSimilarityColor(report.overallSimilarity)}>
                        {report.overallSimilarity}%
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={report.status} 
                      color={report.status === 'completed' ? 'success' : 'warning'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {new Date(report.generatedAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {report.flags?.map((flag: string, index: number) => (
                      <Chip 
                        key={index} 
                        label={flag} 
                        size="small" 
                        sx={{ mr: 0.5 }}
                        color={flag.includes('High') ? 'error' : 'warning'}
                      />
                    ))}
                  </TableCell>
                  <TableCell>
                    <Tooltip title="View Report">
                      <IconButton onClick={() => { setSelectedReport(report); setReportDialog(true); }}>
                        <Visibility />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Download Report">
                      <IconButton onClick={() => downloadReport(report.reportId)}>
                        <Download />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Share Report">
                      <IconButton>
                        <Share />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Analytics Tab */}
      <TabPanel value={activeTab} index={2}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Institution Overview
                </Typography>
                {analytics && (
                  <>
                    <Typography variant="h4" color="primary">
                      {analytics.totalSubmissions || 0}
                    </Typography>
                    <Typography variant="body2">Total Submissions</Typography>
                    <Typography variant="h5" color="warning.main" sx={{ mt: 2 }}>
                      {analytics.averageSimilarity || 0}%
                    </Typography>
                    <Typography variant="body2">Average Similarity</Typography>
                    <Typography variant="h5" color="error.main" sx={{ mt: 2 }}>
                      {analytics.flaggedSubmissions || 0}
                    </Typography>
                    <Typography variant="body2">Flagged Submissions</Typography>
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Course Analytics
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Course</TableCell>
                        <TableCell>Submissions</TableCell>
                        <TableCell>Avg Similarity</TableCell>
                        <TableCell>Flagged</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {courseAnalytics.map((course) => (
                        <TableRow key={course.courseId}>
                          <TableCell>{course.courseName}</TableCell>
                          <TableCell>{course.totalSubmissions}</TableCell>
                          <TableCell>
                            <Chip 
                              label={`${course.averageSimilarity}%`}
                              color={getSimilarityColor(course.averageSimilarity)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Badge badgeContent={course.flaggedSubmissions} color="error">
                              <Assessment />
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Settings Tab */}
      <TabPanel value={activeTab} index={3}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Detection Settings
                </Typography>
                
                <TextField
                  fullWidth
                  label="Sensitivity Level"
                  type="number"
                  value={providerSettings.sensitivity}
                  onChange={(e) => setProviderSettings(prev => ({ 
                    ...prev, 
                    sensitivity: parseInt(e.target.value) 
                  }))}
                  InputProps={{ inputProps: { min: 0, max: 100 } }}
                  sx={{ mb: 2 }}
                />

                <FormControl component="fieldset" sx={{ width: '100%' }}>
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMore />}>
                      <Typography>Exclusion Settings</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <List>
                        <ListItem>
                          <ListItemText primary="Exclude Quotes" />
                          <ListItemSecondaryAction>
                            <Button
                              variant={providerSettings.excludeQuotes ? 'contained' : 'outlined'}
                              onClick={() => setProviderSettings(prev => ({ 
                                ...prev, 
                                excludeQuotes: !prev.excludeQuotes 
                              }))}
                            >
                              {providerSettings.excludeQuotes ? 'ON' : 'OFF'}
                            </Button>
                          </ListItemSecondaryAction>
                        </ListItem>
                        <ListItem>
                          <ListItemText primary="Exclude Bibliography" />
                          <ListItemSecondaryAction>
                            <Button
                              variant={providerSettings.excludeBibliography ? 'contained' : 'outlined'}
                              onClick={() => setProviderSettings(prev => ({ 
                                ...prev, 
                                excludeBibliography: !prev.excludeBibliography 
                              }))}
                            >
                              {providerSettings.excludeBibliography ? 'ON' : 'OFF'}
                            </Button>
                          </ListItemSecondaryAction>
                        </ListItem>
                        <ListItem>
                          <ListItemText primary="Internet Search" />
                          <ListItemSecondaryAction>
                            <Button
                              variant={providerSettings.internetSearch ? 'contained' : 'outlined'}
                              onClick={() => setProviderSettings(prev => ({ 
                                ...prev, 
                                internetSearch: !prev.internetSearch 
                              }))}
                            >
                              {providerSettings.internetSearch ? 'ON' : 'OFF'}
                            </Button>
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>
                    </AccordionDetails>
                  </Accordion>
                </FormControl>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Provider Configuration
                </Typography>
                <List>
                  {providers.map((provider, index) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={provider.displayName}
                        secondary={`Status: ${provider.isActive ? 'Active' : 'Inactive'}`}
                      />
                      <ListItemSecondaryAction>
                        <Chip 
                          label={provider.isActive ? 'Active' : 'Inactive'}
                          color={provider.isActive ? 'success' : 'default'}
                          size="small"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Report Details Dialog */}
      <Dialog open={reportDialog} onClose={() => setReportDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Plagiarism Report Details</DialogTitle>
        <DialogContent>
          {selectedReport && (
            <Box>
              <Typography variant="h6">
                Overall Similarity: {selectedReport.overallSimilarity}%
              </Typography>
              <Typography variant="body1" sx={{ mt: 2 }}>
                Matches Found: {selectedReport.matches?.length || 0}
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle1">Flags:</Typography>
                {selectedReport.flags?.map((flag: string, index: number) => (
                  <Chip key={index} label={flag} sx={{ mr: 1, mt: 1 }} />
                ))}
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReportDialog(false)}>Close</Button>
          <Button variant="contained" onClick={() => selectedReport && downloadReport(selectedReport.reportId)}>
            Download Full Report
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PlagiarismDetectionPage;

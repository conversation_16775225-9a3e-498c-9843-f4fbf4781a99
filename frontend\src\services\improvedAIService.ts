/**
 * Improved AI Service for Frontend
 * 
 * This service provides enhanced AI functionality with better error handling,
 * monitoring, and async support.
 */

import { API_ENDPOINTS } from '../config/api';
import { BaseAIService } from './utils/BaseAIService';

export interface AIHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  issues: Array<{
    type: string;
    message: string;
    threshold?: string;
  }>;
  metrics: {
    total_requests: number;
    success_rate: number;
    performance: {
      avg_duration: number;
      max_duration: number;
      min_duration: number;
    };
  };
  configuration: {
    api_key_configured: boolean;
    model: string;
    fallback_enabled: boolean;
    service_initialized: boolean;
  };
  timestamp: number;
}

export interface AIUsageAnalytics {
  period_hours: number;
  total_requests: number;
  requests_per_day: number;
  requests_per_hour: number;
  success_rate: number;
  service_breakdown: Record<string, {
    total: number;
    success: number;
    success_rate: number;
    avg_duration: number;
  }>;
  error_breakdown: Record<string, number>;
  cost_metrics: {
    total_cost: number;
    total_tokens: number;
    avg_cost_per_request: number;
  };
}

export interface AsyncAIRequest {
  id: string;
  status: 'queued' | 'processing' | 'completed' | 'error';
  result?: string;
  error?: string;
  created_at: number;
  completed_at?: number;
  processing_time?: number;
}

export interface AIConfiguration {
  default_model: string;
  temperature: number;
  max_tokens: number;
  timeout: number;
  retries: number;
  enable_fallback: boolean;
  enable_caching: boolean;
  cache_ttl: number;
}

class ImprovedAIService extends BaseAIService {
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private lastHealthStatus: AIHealthStatus | null = null;

  constructor() {
    super({
      serviceName: 'Improved AI Service',
      baseEndpoint: '/api/v1/ai/improved',
      config: {
        timeout: 30000,
        retries: 3,
        fallbackEnabled: true,
        cacheEnabled: true,
        cacheTTL: 300000,
      },
    });

    // Start periodic health checks
    this.startHealthMonitoring();
  }

  /**
   * Generate content with improved error handling and monitoring
   */
  async generateContent(
    prompt: string,
    options: {
      serviceType?: string;
      userId?: string;
      temperature?: number;
      maxTokens?: number;
      context?: Record<string, any>;
    } = {}
  ): Promise<string> {
    const requestData = {
      prompt,
      service_type: options.serviceType || 'general',
      user_id: options.userId,
      temperature: options.temperature,
      max_tokens: options.maxTokens,
      context: options.context || {},
    };

    try {
      const response = await this.post<{ content: string }>('generate/', requestData);
      return response.content;
    } catch (error) {
      console.error('AI content generation failed:', error);
      
      // Return user-friendly error message
      if (error instanceof Error) {
        if (error.message.includes('rate limit')) {
          throw new Error('AI service is currently busy. Please try again in a moment.');
        } else if (error.message.includes('api key')) {
          throw new Error('AI service configuration issue. Please contact support.');
        }
      }
      
      throw new Error('AI service is temporarily unavailable. Please try again later.');
    }
  }

  /**
   * Generate content asynchronously
   */
  async generateContentAsync(
    prompt: string,
    options: {
      serviceType?: string;
      priority?: 'low' | 'normal' | 'high' | 'urgent';
      userId?: string;
      context?: Record<string, any>;
    } = {}
  ): Promise<string> {
    const requestData = {
      prompt,
      service_type: options.serviceType || 'general',
      priority: options.priority || 'normal',
      user_id: options.userId,
      context: options.context || {},
    };

    const response = await this.post<{ request_id: string }>('generate-async/', requestData);
    return response.request_id;
  }

  /**
   * Get status of async request
   */
  async getAsyncStatus(requestId: string): Promise<AsyncAIRequest> {
    const response = await this.get<AsyncAIRequest>(`async-status/${requestId}/`);
    return response;
  }

  /**
   * Get result of async request
   */
  async getAsyncResult(requestId: string): Promise<AsyncAIRequest> {
    const response = await this.get<AsyncAIRequest>(`async-result/${requestId}/`);
    return response;
  }

  /**
   * Poll for async request completion
   */
  async waitForAsyncResult(
    requestId: string,
    options: {
      maxWaitTime?: number;
      pollInterval?: number;
    } = {}
  ): Promise<string> {
    const maxWaitTime = options.maxWaitTime || 60000; // 1 minute
    const pollInterval = options.pollInterval || 2000; // 2 seconds
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const status = await this.getAsyncStatus(requestId);
      
      if (status.status === 'completed') {
        const result = await this.getAsyncResult(requestId);
        if (result.result) {
          return result.result;
        }
      } else if (status.status === 'error') {
        throw new Error(status.error || 'Async AI request failed');
      }

      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }

    throw new Error('Async AI request timed out');
  }

  /**
   * Analyze student answer
   */
  async analyzeAnswer(
    studentAnswer: string,
    question: string,
    options: {
      userId?: string;
      context?: Record<string, any>;
    } = {}
  ): Promise<{
    score: number;
    feedback: string;
    suggestions: string[];
    key_concepts: string[];
    error?: string;
  }> {
    const requestData = {
      student_answer: studentAnswer,
      question,
      user_id: options.userId,
      context: options.context || {},
    };

    const response = await this.post<{
      score: number;
      feedback: string;
      suggestions: string[];
      key_concepts: string[];
      error?: string;
    }>('analyze-answer/', requestData);

    return response;
  }

  /**
   * Get AI service health status
   */
  async getHealthStatus(): Promise<AIHealthStatus> {
    try {
      const response = await this.get<AIHealthStatus>('health/');
      this.lastHealthStatus = response;
      return response;
    } catch (error) {
      console.error('Failed to get AI health status:', error);
      
      // Return degraded status if we can't get health info
      return {
        status: 'unknown',
        issues: [{ type: 'health_check_failed', message: 'Unable to check AI service health' }],
        metrics: {
          total_requests: 0,
          success_rate: 0,
          performance: { avg_duration: 0, max_duration: 0, min_duration: 0 }
        },
        configuration: {
          api_key_configured: false,
          model: 'unknown',
          fallback_enabled: false,
          service_initialized: false
        },
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get usage analytics
   */
  async getUsageAnalytics(days: number = 7): Promise<AIUsageAnalytics> {
    const response = await this.get<AIUsageAnalytics>(`analytics/?days=${days}`);
    return response;
  }

  /**
   * Update AI configuration
   */
  async updateConfiguration(config: Partial<AIConfiguration>): Promise<boolean> {
    try {
      await this.post('config/', { config });
      return true;
    } catch (error) {
      console.error('Failed to update AI configuration:', error);
      return false;
    }
  }

  /**
   * Update API key
   */
  async updateApiKey(apiKey: string): Promise<boolean> {
    try {
      await this.post('api-key/', { api_key: apiKey });
      return true;
    } catch (error) {
      console.error('Failed to update API key:', error);
      return false;
    }
  }

  /**
   * Start periodic health monitoring
   */
  private startHealthMonitoring(): void {
    // Check health every 30 seconds
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.getHealthStatus();
      } catch (error) {
        console.warn('Health check failed:', error);
      }
    }, 30000);
  }

  /**
   * Stop health monitoring
   */
  stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }

  /**
   * Get last known health status (cached)
   */
  getLastHealthStatus(): AIHealthStatus | null {
    return this.lastHealthStatus;
  }

  /**
   * Check if AI service is healthy
   */
  isHealthy(): boolean {
    return this.lastHealthStatus?.status === 'healthy';
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stopHealthMonitoring();
  }
}

// Export singleton instance
const improvedAIService = new ImprovedAIService();
export default improvedAIService;

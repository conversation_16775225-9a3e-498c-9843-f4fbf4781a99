"""
Standardized Course Views

This module provides API views for courses using the standardized response format.
"""
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated


class StandardizedCourseViewSet(viewsets.ViewSet):
    """ViewSet for courses using standardized response format."""
    permission_classes = [IsAuthenticated]

    def list(self, request, *args, **kwargs):
        """List all courses with standardized response."""
        return Response({
            "status": "success",
            "data": [],
            "message": "Courses retrieved successfully"
        })

    def retrieve(self, request, *args, **kwargs):
        """Retrieve a course with standardized response."""
        return Response({
            "status": "success",
            "data": {
                "id": kwargs.get('pk', 1),
                "title": "Sample Course",
                "course_code": "CS101",
                "description": "A sample course"
            },
            "message": "Course retrieved successfully"
        })

    def create(self, request, *args, **kwargs):
        """Create a course with standardized response."""
        return Response({
            "status": "success",
            "data": {
                "id": 1,
                "title": request.data.get("title", "New Course"),
                "course_code": request.data.get("course_code", "NEW101"),
                "description": request.data.get("description", "")
            },
            "message": "Course created successfully"
        }, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        """Update a course with standardized response."""
        return Response({
            "status": "success",
            "data": {
                "id": kwargs.get('pk', 1),
                "title": request.data.get("title", "Updated Course"),
                "course_code": request.data.get("course_code", "UPD101"),
                "description": request.data.get("description", "")
            },
            "message": "Course updated successfully"
        })

    def destroy(self, request, *args, **kwargs):
        """Delete a course with standardized response."""
        return Response({
            "status": "success",
            "message": "Course deleted successfully"
        }, status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=["get"])
    def materials(self, request, pk=None):
        """Get materials for a course with standardized response."""
        return Response({
            "status": "success",
            "data": [],
            "message": "Course materials retrieved successfully"
        })
import json
import loggingfromtypingimportAnyDictListfrom django.confimportsettingsfromlangchain.output_parsersimportResponseSchemaStructuredOutputParserfromlangchain_core.promptsimportChatPromptTemplate#ImporttheconsolidatedAIservicefrom utils.ai.servicesimportget_ai_servicelogger=logging.getLogger(__name__)#Definetheschemasforstructuredoutputpath_recommendation_schema=ResponseSchema(name="path_recommendation"description="Therecommendedlearningpathforthestudent(programmingcybersecurityfinanceormarketing)"type="string")confidence_schema=ResponseSchema(name="confidence"description="Confidencelevelintherecommendation(highmediumorlow)"type="string")strengths_schema=ResponseSchema(name="strengths"description="Listofstudent'sstrengthsbasedontheiranswers"type="array")areas_to_develop_schema=ResponseSchema(name="areas_to_develop"description="Listofareasthestudentmightwanttodevelopfurther"type="array")personalized_advice_schema=ResponseSchema(name="personalized_advice"description="Personalizedadviceforthestudentbasedontheiranswers"type="string")alternative_path_schema=ResponseSchema(name="alternative_path"description="Analternativepaththatmightalsobesuitableforthestudent"type="string")classAIPathAdvisor:def__init__(self):#UsetheconsolidatedAIserviceself.ai_service=get_ai_service()#We'llusetheLangChainmodelfromtheconsolidatedAIservice#Accessthelangchainmodelthroughtheprimaryprovidertry:if(hasattr(self.ai_service"primary_provider")andself.ai_service.primary_provider):self.llm=getattr(self.ai_service.primary_provider"langchain_model"None)else:self.llm=NoneexceptException:self.llm=None#Setuptheoutputparserself.output_parser=StructuredOutputParser.from_response_schemas([path_recommendation_schemaconfidence_schemastrengths_schemaareas_to_develop_schemapersonalized_advice_schemaalternative_path_schema])#Createtheformatinstructionsself.format_instructions=self.output_parser.get_format_instructions()#Createtheprompttemplateself.prompt_template=ChatPromptTemplate.from_template("""YouareanAIeducationaladvisorhelpingstudentsfindtheirideallearningpath.Analyzethestudent'sanswerstothefollowingassessmentquestionsandprovideadetailedrecommendation.QuestionsandAnswers:{questions_and_answers}PathDescriptions:-Programming:Softwaredevelopmentandbuildingdigitalapplications-Cybersecurity:Protectingdataandsystemsfromcyberthreats-Finance:Understandingfinancialmarketsandanalyzingeconomicdata-Marketing:Designingmarketingstrategiesandbuildingbrands{format_instructions}Provideathoughtfulanalysisthathelpsthestudentunderstandwhyaparticularpathmightbesuitableforthem.""")defget_recommendation(selfquestionsanswers):"""GenerateanAIrecommendationbasedonthestudent'sanswersArgs:questions(list):Listofassessmentquestionsanswers(dict):DictionarymappingquestionindicestoselectedpathvaluesReturns:dict:Structuredrecommendationwithpathconfidencestrengthsareastodeveloppersonalizedadviceandalternativepath"""try:#Formatthequestionsandanswersforthepromptquestions_and_answers=[]foridxquestioninenumerate(questions):answer_path=answers.get(str(idx))ifanswer_path:questions_and_answers.append(f"Q{idx+1}:{question['question']}")questions_and_answers.append(f"A{idx+1}:{answer_path}")formatted_qa="\n".join(questions_and_answers)#Createthepromptprompt=self.prompt_template.format(questions_and_answers=formatted_qaformat_instructions=self.format_instructions)#Logthequestionsandanswersfordebugginglogger.info(f"Processingassessmentwith{len(questions)}questionsand{len(answers)}answers")logger.info(f"FormattedQ&A:{formatted_qa}")#IfLangChainmodelisavailableuseitifself.llm:logger.info("UsingLangChainmodelforrecommendation")#GettheresponsefromtheLLMresponse=self.llm.invoke(prompt)#Parsetheresponseparsed_response=self.output_parser.parse(response.content)logger.info(f"LangChainresponse:{parsed_response}")else:logger.info("LangChainmodelnotavailableusingunifiedAIservice")#FallbacktousingtheunifiedAIservicedirectlystructure={"path_recommendation":"""confidence":"""strengths":[]"areas_to_develop":[]"personalized_advice":"""alternative_path":""}#FormatthepromptfortheunifiedAIserviceai_prompt=f"""YouareanAIeducationaladvisorhelpingstudentsfindtheirideallearningpath.Analyzethestudent'sanswerstothefollowingassessmentquestionsandprovideadetailedrecommendation.QuestionsandAnswers:{formatted_qa}PathDescriptions:-Programming:Softwaredevelopmentandbuildingdigitalapplications-Cybersecurity:Protectingdataandsystemsfromcyberthreats-Finance:Understandingfinancialmarketsandanalyzingeconomicdata-Marketing:DesigningmarketingstrategiesandbuildingbrandsProvideathoughtfulanalysisthathelpsthestudentunderstandwhyaparticularpathmightbesuitableforthem.Includethefollowinginformationinyourresponse:1.Therecommendedlearningpath(programmingcybersecurityfinanceormarketing)2.Yourconfidencelevelintherecommendation(highmediumorlow)3.Thestudent'sstrengthsbasedontheiranswers4.Areasthestudentmightwanttodevelopfurther5.Personalizedadviceforthestudent6.AnalternativepaththatmightalsobesuitableFormatyourresponseasaJSONobjectwiththefollowingkeys:path_recommendationconfidencestrengthsareas_to_developpersonalized_advicealternative_path"""#GeneratetherecommendationusingtheconsolidatedAIservicelogger.info("CallingconsolidatedAIservicegenerate_structured_content")try:parsed_response=self.ai_service.generate_structured_content(ai_promptstructure)logger.info(f"UnifiedAIserviceresponse:{parsed_response}")exceptExceptionase:logger.error(f"ErrorfromunifiedAIservice:{str(e)}")#Trydirectgenerationasafallbacklogger.info("Tryingdirectgenerationasfallback")try:raw_response=self.ai_service.generate_content(ai_prompt)logger.info(f"RawAIresponse:{raw_response}")#TrytoextractJSONfromtherawresponseimport jsontry:#TrytoparseasJSONdirectlyparsed_response=json.loads(raw_response)exceptjson.JSONDecodeError:#IfthatfailstrytoextractJSONfromthetextimport rejson_match=re.search(r"\{.*\}"raw_responsere.DOTALL)ifjson_match:parsed_response=json.loads(json_match.group())else:#Ifallelsefailscreateabasicresponseparsed_response=structureexceptExceptionasinner_e:logger.error(f"Errorinfallbackgeneration:{str(inner_e)}")raisereturnparsed_responseexceptExceptionase:logger.error(f"ErrorgeneratingAIrecommendation:{str(e)}"exc_info=True)#Returnamoreinformativedefaultrecommendationifthere'sanerrorreturn{"path_recommendation":"programming""confidence":"low""strengths":["problem-solving""technicalaptitude"]"areas_to_develop":["exploredifferentprogramminglanguages""buildpracticalprojects"]"personalized_advice":f"Weencounteredanissueanalyzingyourresponsesindetail.Basedontheavailableinformationprogrammingappearstobeagoodstartingpoint.Considerexploringdifferentprogramminglanguagesandbuildingsmallprojectstoseewhataspectsyouenjoymost.Errordetails:{str(e)[:100]}""alternative_path":"cybersecurity"}
[run]
source = .
omit = 
    */migrations/*
    */venv/*
    */env/*
    */fresh_venv/*
    */node_modules/*
    manage.py
    */settings/*
    */wsgi.py
    */asgi.py
    */conftest.py
    */test_*.py
    */tests.py
    */__pycache__/*
    */static/*
    */media/*
    */locale/*
    */templates/*
    */scripts/*
    */deployment/*
    test_django.py
    run_server.py

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[html]
directory = htmlcov
title = North Star University Test Coverage

[xml]
output = coverage.xml

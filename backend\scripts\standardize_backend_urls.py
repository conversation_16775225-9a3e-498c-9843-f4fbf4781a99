#!/usr/bin/env python3
"""
Script to standardize backend API URLs to use v1 versioning.
"""

import os
import re
from pathlib import Path


def standardize_api_urls(content):
    """Replace API URLs to use v1 versioning."""
    # Pattern to match '/api/{anything}' but not '/api/v1/{anything}'
    patterns = [
        (r"'/api/(?!v1/)([^']*?)'", r"'/api/v1/\1'"),
        (r'"/api/(?!v1/)([^"]*?)"', r'"/api/v1/\1"'),
        (r"`/api/(?!v1/)([^`]*?)`", r"`/api/v1/\1`"),
    ]
    
    updated_content = content
    changes_made = False
    
    for pattern, replacement in patterns:
        new_content = re.sub(pattern, replacement, updated_content)
        if new_content != updated_content:
            changes_made = True
            updated_content = new_content
    
    return updated_content, changes_made


def find_python_files(directory):
    """Find all Python files in a directory."""
    python_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    return python_files


def main():
    """Main function to process files."""
    backend_dir = Path(__file__).parent.parent
    test_files = find_python_files(os.path.join(backend_dir, 'tests'))
    
    total_files = 0
    changed_files = 0
    
    print(f"Found {len(test_files)} Python test files to process...")
    
    for file_path in test_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            updated_content, changed = standardize_api_urls(content)
            total_files += 1
            
            if changed:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(updated_content)
                changed_files += 1
                relative_path = os.path.relpath(file_path, backend_dir)
                print(f"Updated: {relative_path}")
                
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
    
    print(f"\nProcessing complete:")
    print(f"Total files processed: {total_files}")
    print(f"Files updated: {changed_files}")
    print(f"Files unchanged: {total_files - changed_files}")


if __name__ == "__main__":
    main()

"""
Blockchain Credentials Signals

This module contains Django signals for automatically creating blockchain
credentials and NFT achievements based on course completion and other events.
"""

import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone

from assessment.models import Assessment
from grades.models import CourseGrade
from .models import BlockchainCredential, NFTAchievement, CredentialTemplate, BlockchainNetwork
from .services import BlockchainCredentialService, NFTService

logger = logging.getLogger(__name__)


@receiver(post_save, sender=CourseGrade)
def create_credential_on_course_completion(sender, instance, created, **kwargs):
    """
    Automatically create a blockchain credential when a student 
    completes a course with a passing grade.
    """
    if created and instance.grade and instance.grade not in ['F', 'D-']:
        try:
            # Get or create a course completion template
            template, _ = CredentialTemplate.objects.get_or_create(
                credential_type='COURSE_COMPLETION',
                defaults={
                    'name': 'Course Completion Certificate',
                    'description': 'Certificate awarded for successful course completion',
                    'blockchain_enabled': True,
                    'is_active': True
                }
            )
            
            # Get default blockchain network
            network = BlockchainNetwork.objects.filter(is_active=True, is_testnet=True).first()
            if not network:
                logger.warning("No active blockchain network found for credential creation")
                return
            
            # Check if credential already exists
            if BlockchainCredential.objects.filter(
                student=instance.user,
                course=instance.course,
                template=template
            ).exists():
                return
            
            # Create blockchain credential
            credential = BlockchainCredential.objects.create(
                student=instance.user,
                template=template,
                title=f"Certificate of Completion - {instance.course.title}",
                description=f"This certifies that {instance.user.get_full_name() or instance.user.username} "
                           f"has successfully completed {instance.course.title} with a grade of {instance.grade}",
                course=instance.course,
                final_grade=instance.grade,
                grade_percentage=instance.numeric_grade,
                completion_date=timezone.now(),
                blockchain_network=network,
                competency_level=3,  # Default to intermediate
                skills_acquired=[],  # Could be populated from course data
                is_public=True
            )
            
            # Initiate blockchain minting
            try:
                blockchain_service = BlockchainCredentialService()
                blockchain_service.initiate_minting(credential)
                logger.info(f"Blockchain credential created for {instance.user.username} - {instance.course.title}")
            except Exception as e:
                logger.error(f"Failed to initiate minting for credential {credential.id}: {e}")
                
        except Exception as e:
            logger.error(f"Failed to create blockchain credential for course completion: {e}")


@receiver(post_save, sender=Assessment)
def create_nft_achievement_on_perfect_score(sender, instance, created, **kwargs):
    """
    Automatically create an NFT achievement when a student 
    achieves a perfect score on an assessment.
    """
    if instance.completed and instance.score == 100.0:
        try:
            # Get default blockchain network
            network = BlockchainNetwork.objects.filter(is_active=True, is_testnet=True).first()
            if not network:
                return
            
            # Check if achievement already exists
            if NFTAchievement.objects.filter(
                student=instance.student,
                related_assessment=instance,
                achievement_type='PERFECT_SCORE'
            ).exists():
                return
            
            # Create NFT achievement
            achievement = NFTAchievement.objects.create(
                student=instance.student,
                title=f"Perfect Score Achievement - {instance.title or 'Assessment'}",
                description=f"Awarded for achieving a perfect score of 100% on {instance.title or 'the assessment'}",
                achievement_type='PERFECT_SCORE',
                rarity='RARE',  # Perfect scores are rare
                related_assessment=instance,
                blockchain_network=network,
                xp_reward=500,  # Bonus XP for perfect score
                badge_level=3,
                criteria_met={
                    'score': 100.0,
                    'assessment_id': instance.id,
                    'assessment_type': instance.assessment_type
                }
            )
            
            # Initiate NFT minting
            try:
                nft_service = NFTService()
                nft_service.mint_achievement_nft(achievement)
                logger.info(f"Perfect score NFT achievement created for {instance.student.username}")
            except Exception as e:
                logger.error(f"Failed to mint NFT achievement {achievement.id}: {e}")
                
        except Exception as e:
            logger.error(f"Failed to create perfect score NFT achievement: {e}")


def create_milestone_achievements():
    """
    Function to create milestone achievements based on various criteria.
    This could be called periodically or triggered by specific events.
    """
    # This is a placeholder for milestone achievement logic
    # You could implement achievements for:
    # - Completing multiple courses
    # - Consecutive perfect scores
    # - Learning streaks
    # - Community participation
    # etc.
    pass

import { FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global teardown...');

  try {
    // Clean up test data
    console.log('🗑️ Cleaning up test data...');
    await cleanupTestData();
    console.log('✅ Test data cleaned up');

  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error to avoid masking test failures
  }

  console.log('✅ Global teardown completed');
}

async function cleanupTestData() {
  try {
    const response = await fetch('http://localhost:8000/api/test/cleanup/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.warn(`Failed to cleanup test data: ${response.statusText}`);
    } else {
      const data = await response.json();
      console.log('Test data cleaned up:', data);
    }
  } catch (error) {
    console.warn('Failed to cleanup test data:', error);
  }
}

export default globalTeardown;

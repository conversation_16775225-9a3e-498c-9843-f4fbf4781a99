// Mock for utils/levelUtils
export const levelColors = {
  1: '#4CAF50',
  2: '#2196F3',
  3: '#FF9800',
  4: '#f44336',
  5: '#9C27B0'
};

export const getLevelColor = (level) => {
  return levelColors[level] || '#757575';
};

export const getLevelName = (level) => {
  const levelNames = {
    1: 'Beginner',
    2: 'Intermediate',
    3: 'Advanced',
    4: 'Expert',
    5: 'Master'
  };
  return levelNames[level] || 'Unknown';
};

export const getNextLevel = (currentLevel) => {
  return Math.min(currentLevel + 1, 5);
};

export const getPreviousLevel = (currentLevel) => {
  return Math.max(currentLevel - 1, 1);
};

export const isValidLevel = (level) => {
  return Number.isInteger(level) && level >= 1 && level <= 5;
};

export const calculateLevelProgress = (currentXP, levelXPRequirement) => {
  if (!levelXPRequirement || levelXPRequirement === 0) return 0;
  return Math.min((currentXP / levelXPRequirement) * 100, 100);
};

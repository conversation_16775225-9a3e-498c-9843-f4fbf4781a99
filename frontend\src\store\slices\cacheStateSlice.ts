import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
  tags: string[]; // For cache invalidation by tags
}

interface CacheState {
  data: Record<string, CacheEntry>;
  lastCleanup: number;
  maxSize: number;
  currentSize: number;
}

const initialState: CacheState = {
  data: {},
  lastCleanup: Date.now(),
  maxSize: 100, // Maximum number of cache entries
  currentSize: 0,
};

const cacheStateSlice = createSlice({
  name: 'cache',
  initialState,
  reducers: {
    setCache: (
      state,
      action: PayloadAction<{
        key: string;
        data: any;
        ttl?: number;
        tags?: string[];
      }>
    ) => {
      const { key, data, ttl = 300000, tags = [] } = action.payload; // Default 5 minutes TTL

      // Remove expired entries if cache is full
      if (state.currentSize >= state.maxSize) {
        cacheStateSlice.caseReducers.cleanupExpired(state);
      }

      // If still full, remove oldest entry
      if (state.currentSize >= state.maxSize) {
        const oldestKey = Object.keys(state.data).reduce((oldest, currentKey) =>
          state.data[currentKey].timestamp < state.data[oldest].timestamp ? currentKey : oldest
        );
        delete state.data[oldestKey];
        state.currentSize--;
      }

      state.data[key] = {
        data,
        timestamp: Date.now(),
        ttl,
        tags,
      };

      if (!state.data[key]) {
        state.currentSize++;
      }
    },

    getCache: (state, action: PayloadAction<string>) => {
      const key = action.payload;
      const entry = state.data[key];

      if (!entry) {
        return;
      }

      // Check if entry has expired
      if (Date.now() - entry.timestamp > entry.ttl) {
        delete state.data[key];
        state.currentSize--;
        return;
      }

      // Update timestamp for LRU behavior
      entry.timestamp = Date.now();
    },

    removeCache: (state, action: PayloadAction<string>) => {
      const key = action.payload;
      if (state.data[key]) {
        delete state.data[key];
        state.currentSize--;
      }
    },

    invalidateByTag: (state, action: PayloadAction<string>) => {
      const tag = action.payload;
      const keysToRemove: string[] = [];

      Object.entries(state.data).forEach(([key, entry]) => {
        if (entry.tags.includes(tag)) {
          keysToRemove.push(key);
        }
      });

      keysToRemove.forEach(key => {
        delete state.data[key];
        state.currentSize--;
      });
    },

    invalidateByPattern: (state, action: PayloadAction<string>) => {
      const pattern = action.payload;
      const regex = new RegExp(pattern);
      const keysToRemove: string[] = [];

      Object.keys(state.data).forEach(key => {
        if (regex.test(key)) {
          keysToRemove.push(key);
        }
      });

      keysToRemove.forEach(key => {
        delete state.data[key];
        state.currentSize--;
      });
    },

    cleanupExpired: (state) => {
      const now = Date.now();
      const keysToRemove: string[] = [];

      Object.entries(state.data).forEach(([key, entry]) => {
        if (now - entry.timestamp > entry.ttl) {
          keysToRemove.push(key);
        }
      });

      keysToRemove.forEach(key => {
        delete state.data[key];
        state.currentSize--;
      });

      state.lastCleanup = now;
    },

    clearCache: (state) => {
      state.data = {};
      state.currentSize = 0;
      state.lastCleanup = Date.now();
    },

    setMaxSize: (state, action: PayloadAction<number>) => {
      state.maxSize = action.payload;

      // If current size exceeds new max size, remove oldest entries
      while (state.currentSize > state.maxSize) {
        const oldestKey = Object.keys(state.data).reduce((oldest, currentKey) =>
          state.data[currentKey].timestamp < state.data[oldest].timestamp ? currentKey : oldest
        );
        delete state.data[oldestKey];
        state.currentSize--;
      }
    },

    updateCacheTags: (
      state,
      action: PayloadAction<{ key: string; tags: string[] }>
    ) => {
      const { key, tags } = action.payload;
      if (state.data[key]) {
        state.data[key].tags = tags;
      }
    },

    extendTTL: (
      state,
      action: PayloadAction<{ key: string; additionalTime: number }>
    ) => {
      const { key, additionalTime } = action.payload;
      if (state.data[key]) {
        state.data[key].ttl += additionalTime;
      }
    },
  },
});

export const {
  setCache,
  getCache,
  removeCache,
  invalidateByTag,
  invalidateByPattern,
  cleanupExpired,
  clearCache,
  setMaxSize,
  updateCacheTags,
  extendTTL,
} = cacheStateSlice.actions;

// Selectors
export const selectCacheState = (state: { cache: CacheState }) => state.cache;

export const selectCacheEntry = (key: string) => (state: { cache: CacheState }) => {
  const entry = state.cache.data[key];
  if (!entry) return null;

  // Check if expired
  if (Date.now() - entry.timestamp > entry.ttl) {
    return null;
  }

  return entry.data;
};

export const selectCacheStats = (state: { cache: CacheState }) => ({
  currentSize: state.cache.currentSize,
  maxSize: state.cache.maxSize,
  lastCleanup: state.cache.lastCleanup,
  utilizationPercent: (state.cache.currentSize / state.cache.maxSize) * 100,
});

export const selectCacheKeys = (state: { cache: CacheState }) => 
  Object.keys(state.cache.data);

export const selectCacheByTag = (tag: string) => (state: { cache: CacheState }) => {
  const entries: Record<string, any> = {};
  const now = Date.now();

  Object.entries(state.cache.data).forEach(([key, entry]) => {
    if (entry.tags.includes(tag) && now - entry.timestamp <= entry.ttl) {
      entries[key] = entry.data;
    }
  });

  return entries;
};

export default cacheStateSlice.reducer;

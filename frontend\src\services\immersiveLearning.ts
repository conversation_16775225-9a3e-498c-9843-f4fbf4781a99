import { axiosInstance } from '../config/axios';
import { API_ENDPOINTS } from '../config/api';
import { 
  BlockchainCredential, 
  NFTAchievement, 
  WalletAddress, 
  SmartContract,
  blockchainCredentialsService
} from './blockchainCredentials';

// Types for immersive learning
export interface VRSpace {
  id: number;
  name: string;
  description: string;
  category: string;
  environment_type: string;
  capacity: number;
  scene_url: string;
  thumbnail_url: string;
  preview_video_url: string;
  metadata: any;
  technical_requirements: any;
  supported_devices: string[];
  is_active: boolean;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

export interface ARExperience {
  id: number;
  name: string;
  description: string;
  category: string;
  experience_type: string;
  content_url: string;
  thumbnail_url: string;
  preview_video_url: string;
  metadata: any;
  technical_requirements: any;
  supported_devices: string[];
  markers: any;
  tracking_data: any;
  is_active: boolean;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

export interface ImmersiveContent {
  id: number;
  title: string;
  description: string;
  content_type: string;
  course: number;
  lesson: number;
  vr_space: number;
  ar_experience: number;
  content_data: any;
  media_files: any;
  interaction_points: any;
  assessment_data: any;
  duration_minutes: number;
  difficulty_level: string;
  learning_objectives: string[];
  prerequisites: string[];
  is_published: boolean;
  created_at: string;
  updated_at: string;
}

export interface ImmersiveSession {
  id: number;
  student: number;
  content: number;
  session_type: string;
  device_type: string;
  device_info: any;
  start_time: string;
  end_time: string;
  duration_seconds: number;
  interaction_data: any;
  performance_metrics: any;
  progress_data: any;
  completion_percentage: number;
  quality_score: number;
  feedback: any;
  is_completed: boolean;
  created_at: string;
  updated_at: string;
}

export interface DeviceCompatibility {
  id: number;
  device_name: string;
  device_type: string;
  manufacturer: string;
  model: string;
  os_version: string;
  supported_features: string[];
  performance_rating: string;
  minimum_requirements: any;
  recommended_settings: any;
  is_supported: boolean;
  created_at: string;
  updated_at: string;
}

export interface ImmersiveAssessment {
  id: number;
  content: number;
  assessment_type: string;
  title: string;
  description: string;
  questions: any;
  scoring_criteria: any;
  passing_score: number;
  time_limit_minutes: number;
  max_attempts: number;
  is_required: boolean;
  created_at: string;
  updated_at: string;
}

export interface ImmersiveAnalytics {
  id: number;
  session: number;
  event_type: string;
  event_data: any;
  timestamp: string;
  user_position: any;
  user_interaction: any;
  performance_data: any;
  biometric_data: any;
  created_at: string;
}

export interface ImmersiveDashboard {
  total_vr_spaces: number;
  active_vr_spaces: number;
  total_ar_experiences: number;
  active_ar_experiences: number;
  total_immersive_content: number;
  published_content: number;
  total_sessions: number;
  active_sessions: number;
  average_session_duration: number;
  average_completion_rate: number;
  top_performing_content: any[];
  device_usage_stats: any;
  engagement_metrics: any;
  learning_outcomes: any;
}

// Blockchain integration interfaces
export interface ImmersiveCredentialRequest {
  student_id: number;
  content_id: number;
  session_id: number;
  credential_type: string;
  achievement_level: string;
  completion_data: any;
  performance_metrics: any;
  auto_issue: boolean;
}

export interface ImmersiveBlockchainIntegration {
  id: number;
  content_id: number;
  credential_template_id: number;
  nft_template_id: number;
  auto_issue_credential: boolean;
  auto_mint_nft: boolean;
  completion_threshold: number;
  quality_threshold: number;
  blockchain_network: string;
  smart_contract_address: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ImmersiveAchievementSummary {
  student_id: number;
  total_credentials: number;
  total_nfts: number;
  immersive_credentials: BlockchainCredential[];
  immersive_nfts: NFTAchievement[];
  latest_achievements: any[];
  achievement_stats: any;
}

// API Service Class
export class ImmersiveLearningService {
  // VR Spaces
  async getVRSpaces(): Promise<VRSpace[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.VR_SPACES);
    return response.data.results || response.data;
  }

  async getVRSpace(id: number): Promise<VRSpace> {
    const response = await axiosInstance.get(`${API_ENDPOINTS.IMMERSIVE_LEARNING.VR_SPACES}${id}/`);
    return response.data;
  }

  async createVRSpace(data: Partial<VRSpace>): Promise<VRSpace> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.VR_SPACES, data);
    return response.data;
  }

  async updateVRSpace(id: number, data: Partial<VRSpace>): Promise<VRSpace> {
    const response = await axiosInstance.put(`${API_ENDPOINTS.IMMERSIVE_LEARNING.VR_SPACES}${id}/`, data);
    return response.data;
  }

  async deleteVRSpace(id: number): Promise<void> {
    await axiosInstance.delete(`${API_ENDPOINTS.IMMERSIVE_LEARNING.VR_SPACES}${id}/`);
  }

  // AR Experiences
  async getARExperiences(): Promise<ARExperience[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.AR_EXPERIENCES);
    return response.data.results || response.data;
  }

  async getARExperience(id: number): Promise<ARExperience> {
    const response = await axiosInstance.get(`${API_ENDPOINTS.IMMERSIVE_LEARNING.AR_EXPERIENCES}${id}/`);
    return response.data;
  }

  async createARExperience(data: Partial<ARExperience>): Promise<ARExperience> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.AR_EXPERIENCES, data);
    return response.data;
  }

  async updateARExperience(id: number, data: Partial<ARExperience>): Promise<ARExperience> {
    const response = await axiosInstance.put(`${API_ENDPOINTS.IMMERSIVE_LEARNING.AR_EXPERIENCES}${id}/`, data);
    return response.data;
  }

  async deleteARExperience(id: number): Promise<void> {
    await axiosInstance.delete(`${API_ENDPOINTS.IMMERSIVE_LEARNING.AR_EXPERIENCES}${id}/`);
  }

  // Immersive Content
  async getImmersiveContent(): Promise<ImmersiveContent[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.CONTENT);
    return response.data.results || response.data;
  }

  async getImmersiveContentItem(id: number): Promise<ImmersiveContent> {
    const response = await axiosInstance.get(`${API_ENDPOINTS.IMMERSIVE_LEARNING.CONTENT}${id}/`);
    return response.data;
  }

  async createImmersiveContent(data: Partial<ImmersiveContent>): Promise<ImmersiveContent> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.CONTENT, data);
    return response.data;
  }

  async updateImmersiveContent(id: number, data: Partial<ImmersiveContent>): Promise<ImmersiveContent> {
    const response = await axiosInstance.put(`${API_ENDPOINTS.IMMERSIVE_LEARNING.CONTENT}${id}/`, data);
    return response.data;
  }

  async deleteImmersiveContent(id: number): Promise<void> {
    await axiosInstance.delete(`${API_ENDPOINTS.IMMERSIVE_LEARNING.CONTENT}${id}/`);
  }

  // Immersive Sessions
  async getImmersiveSessions(): Promise<ImmersiveSession[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.SESSIONS);
    return response.data.results || response.data;
  }

  async getImmersiveSession(id: number): Promise<ImmersiveSession> {
    const response = await axiosInstance.get(`${API_ENDPOINTS.IMMERSIVE_LEARNING.SESSIONS}${id}/`);
    return response.data;
  }

  async createImmersiveSession(data: Partial<ImmersiveSession>): Promise<ImmersiveSession> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.SESSIONS, data);
    return response.data;
  }

  async updateImmersiveSession(id: number, data: Partial<ImmersiveSession>): Promise<ImmersiveSession> {
    const response = await axiosInstance.put(`${API_ENDPOINTS.IMMERSIVE_LEARNING.SESSIONS}${id}/`, data);
    return response.data;
  }

  async deleteImmersiveSession(id: number): Promise<void> {
    await axiosInstance.delete(`${API_ENDPOINTS.IMMERSIVE_LEARNING.SESSIONS}${id}/`);
  }

  async startSession(contentId: number, data: any): Promise<ImmersiveSession> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.START_SESSION(contentId), data);
    return response.data;
  }

  async endSession(sessionId: number, data: any): Promise<ImmersiveSession> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.END_SESSION(sessionId), data);
    return response.data;
  }

  // Device Compatibility
  async getDeviceCompatibility(): Promise<DeviceCompatibility[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.DEVICE_COMPATIBILITY);
    return response.data.results || response.data;
  }

  async getDeviceCompatibilityItem(id: number): Promise<DeviceCompatibility> {
    const response = await axiosInstance.get(`${API_ENDPOINTS.IMMERSIVE_LEARNING.DEVICE_COMPATIBILITY}${id}/`);
    return response.data;
  }

  async createDeviceCompatibility(data: Partial<DeviceCompatibility>): Promise<DeviceCompatibility> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.DEVICE_COMPATIBILITY, data);
    return response.data;
  }

  async updateDeviceCompatibility(id: number, data: Partial<DeviceCompatibility>): Promise<DeviceCompatibility> {
    const response = await axiosInstance.put(`${API_ENDPOINTS.IMMERSIVE_LEARNING.DEVICE_COMPATIBILITY}${id}/`, data);
    return response.data;
  }

  async deleteDeviceCompatibility(id: number): Promise<void> {
    await axiosInstance.delete(`${API_ENDPOINTS.IMMERSIVE_LEARNING.DEVICE_COMPATIBILITY}${id}/`);
  }

  // Immersive Assessments
  async getImmersiveAssessments(): Promise<ImmersiveAssessment[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.ASSESSMENTS);
    return response.data.results || response.data;
  }

  async getImmersiveAssessment(id: number): Promise<ImmersiveAssessment> {
    const response = await axiosInstance.get(`${API_ENDPOINTS.IMMERSIVE_LEARNING.ASSESSMENTS}${id}/`);
    return response.data;
  }

  async createImmersiveAssessment(data: Partial<ImmersiveAssessment>): Promise<ImmersiveAssessment> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.ASSESSMENTS, data);
    return response.data;
  }

  async updateImmersiveAssessment(id: number, data: Partial<ImmersiveAssessment>): Promise<ImmersiveAssessment> {
    const response = await axiosInstance.put(`${API_ENDPOINTS.IMMERSIVE_LEARNING.ASSESSMENTS}${id}/`, data);
    return response.data;
  }

  async deleteImmersiveAssessment(id: number): Promise<void> {
    await axiosInstance.delete(`${API_ENDPOINTS.IMMERSIVE_LEARNING.ASSESSMENTS}${id}/`);
  }

  // Immersive Analytics
  async getImmersiveAnalytics(params?: any): Promise<ImmersiveAnalytics[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.ANALYTICS, { params });
    return response.data.results || response.data;
  }

  async createImmersiveAnalytics(data: Partial<ImmersiveAnalytics>): Promise<ImmersiveAnalytics> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.ANALYTICS, data);
    return response.data;
  }

  // Student endpoints
  async getMyImmersiveContent(): Promise<ImmersiveContent[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.STUDENT.MY_CONTENT);
    return response.data.results || response.data;
  }

  async getMySessions(): Promise<ImmersiveSession[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.STUDENT.MY_SESSIONS);
    return response.data.results || response.data;
  }

  async getMyProgress(): Promise<any> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.STUDENT.MY_PROGRESS);
    return response.data;
  }

  async getMyAchievements(): Promise<any[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.STUDENT.MY_ACHIEVEMENTS);
    return response.data.results || response.data;
  }

  // Admin endpoints
  async getAdminDashboard(): Promise<ImmersiveDashboard> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.ADMIN.DASHBOARD);
    return response.data;
  }

  async getContentManagement(): Promise<any> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.ADMIN.CONTENT_MANAGEMENT);
    return response.data;
  }

  async getSessionManagement(): Promise<any> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.ADMIN.SESSION_MANAGEMENT);
    return response.data;
  }

  async getAnalyticsDashboard(): Promise<any> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.ADMIN.ANALYTICS_DASHBOARD);
    return response.data;
  }

  // Utility methods
  async checkDeviceCompatibility(deviceInfo: any): Promise<boolean> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.CHECK_DEVICE_COMPATIBILITY, deviceInfo);
    return response.data.is_compatible;
  }

  async getRecommendedContent(params?: any): Promise<ImmersiveContent[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.RECOMMENDED_CONTENT, { params });
    return response.data.results || response.data;
  }

  async getFeaturedContent(): Promise<ImmersiveContent[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.FEATURED_CONTENT);
    return response.data.results || response.data;
  }

  async searchContent(query: string, filters?: any): Promise<ImmersiveContent[]> {
    const params = { q: query, ...filters };
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.SEARCH_CONTENT, { params });
    return response.data.results || response.data;
  }

  // Blockchain Integration Methods
  async requestImmersiveCredential(request: ImmersiveCredentialRequest): Promise<BlockchainCredential> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.REQUEST_CREDENTIAL, request);
    return response.data;
  }

  async getImmersiveBlockchainIntegrations(): Promise<ImmersiveBlockchainIntegration[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.BLOCKCHAIN_INTEGRATIONS);
    return response.data.results || response.data;
  }

  async getImmersiveBlockchainIntegration(id: number): Promise<ImmersiveBlockchainIntegration> {
    const response = await axiosInstance.get(`${API_ENDPOINTS.IMMERSIVE_LEARNING.BLOCKCHAIN_INTEGRATIONS}${id}/`);
    return response.data;
  }

  async createImmersiveBlockchainIntegration(data: Partial<ImmersiveBlockchainIntegration>): Promise<ImmersiveBlockchainIntegration> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.BLOCKCHAIN_INTEGRATIONS, data);
    return response.data;
  }

  async updateImmersiveBlockchainIntegration(id: number, data: Partial<ImmersiveBlockchainIntegration>): Promise<ImmersiveBlockchainIntegration> {
    const response = await axiosInstance.put(`${API_ENDPOINTS.IMMERSIVE_LEARNING.BLOCKCHAIN_INTEGRATIONS}${id}/`, data);
    return response.data;
  }

  async deleteImmersiveBlockchainIntegration(id: number): Promise<void> {
    await axiosInstance.delete(`${API_ENDPOINTS.IMMERSIVE_LEARNING.BLOCKCHAIN_INTEGRATIONS}${id}/`);
  }

  async getStudentImmersiveAchievements(studentId?: number): Promise<ImmersiveAchievementSummary> {
    const endpoint = studentId 
      ? `${API_ENDPOINTS.IMMERSIVE_LEARNING.STUDENT_ACHIEVEMENTS}?student_id=${studentId}`
      : API_ENDPOINTS.IMMERSIVE_LEARNING.STUDENT_ACHIEVEMENTS;
    const response = await axiosInstance.get(endpoint);
    return response.data;
  }

  async triggerSessionCompletion(sessionId: number, completionData: any): Promise<{ 
    credentials: BlockchainCredential[], 
    nfts: NFTAchievement[],
    session: ImmersiveSession 
  }> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.TRIGGER_SESSION_COMPLETION(sessionId), completionData);
    return response.data;
  }

  async getContentCredentialTemplates(contentId: number): Promise<any[]> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.CONTENT_CREDENTIAL_TEMPLATES(contentId));
    return response.data.results || response.data;
  }

  async linkCredentialTemplate(contentId: number, templateId: number, config: any): Promise<any> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.LINK_CREDENTIAL_TEMPLATE(contentId), {
      template_id: templateId,
      config
    });
    return response.data;
  }

  async linkNFTTemplate(contentId: number, templateId: number, config: any): Promise<any> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.LINK_NFT_TEMPLATE(contentId), {
      template_id: templateId,
      config
    });
    return response.data;
  }

  async checkCredentialEligibility(sessionId: number): Promise<{
    eligible: boolean,
    credential_types: string[],
    nft_types: string[],
    requirements_met: any
  }> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.CHECK_CREDENTIAL_ELIGIBILITY(sessionId));
    return response.data;
  }

  async generateAchievementMetadata(sessionId: number, achievementType: string): Promise<any> {
    const response = await axiosInstance.post(API_ENDPOINTS.IMMERSIVE_LEARNING.GENERATE_ACHIEVEMENT_METADATA, {
      session_id: sessionId,
      achievement_type: achievementType
    });
    return response.data;
  }

  async getImmersiveCredentialHistory(contentId?: number, studentId?: number): Promise<BlockchainCredential[]> {
    const params: any = {};
    if (contentId) params.content_id = contentId;
    if (studentId) params.student_id = studentId;
    
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.CREDENTIAL_HISTORY, { params });
    return response.data.results || response.data;
  }

  async getImmersiveNFTHistory(contentId?: number, studentId?: number): Promise<NFTAchievement[]> {
    const params: any = {};
    if (contentId) params.content_id = contentId;
    if (studentId) params.student_id = studentId;
    
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.NFT_HISTORY, { params });
    return response.data.results || response.data;
  }

  async verifyImmersiveCredential(credentialId: number): Promise<any> {
    return await blockchainCredentialsService.verifyCredential(credentialId);
  }

  async verifyImmersiveNFT(nftId: number): Promise<any> {
    return await blockchainCredentialsService.verifyNFT(nftId);
  }

  async getBlockchainConfigForContent(contentId: number): Promise<{
    blockchain_network: string,
    smart_contracts: SmartContract[],
    wallet_requirements: any,
    integration_status: string
  }> {
    const response = await axiosInstance.get(API_ENDPOINTS.IMMERSIVE_LEARNING.BLOCKCHAIN_CONFIG(contentId));
    return response.data;
  }

  async updateBlockchainConfigForContent(contentId: number, config: any): Promise<any> {
    const response = await axiosInstance.put(API_ENDPOINTS.IMMERSIVE_LEARNING.BLOCKCHAIN_CONFIG(contentId), config);
    return response.data;
  }
}

// Export singleton instance
export const immersiveLearningService = new ImmersiveLearningService();

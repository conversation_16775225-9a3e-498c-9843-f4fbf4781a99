import json
import logging
from datetime import timedelta
from django.conf import settings
from django.db import models
from django.utils import timezone

# Import the unified models from core app
from core.question_models import BaseQuestion, BaseQuestionResponse
from core.mixins import ProgressTrackingMixin

logger = logging.getLogger(__name__)

# Base Models
class StudentLevel(models.Model):
    """Consolidated model to track student's proficiency level across the platform"""
    LEVEL_CHOICES = [
        (1, "Beginner"),
        (2, "Elementary"),
        (3, "Intermediate"),
        (4, "Advanced"),
        (5, "Expert")
    ]
    
    student = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="level_profile"
    )
    current_level = models.IntegerField(choices=LEVEL_CHOICES, default=1)
    current_level_display = models.CharField(max_length=50, default="Beginner")
    last_assessment_date = models.DateTimeField(null=True, blank=True)
    recommended_courses = models.ManyToManyField(
        "courses.Course",
        blank=True,
        related_name="recommended_for_students"
    )
    skill_strengths = models.JSONField(
        default=dict,
        help_text="Areas where student shows strength"
    )
    skill_weaknesses = models.JSONField(
        default=dict,
        help_text="Areas where student needs improvement"
    )
    progression_history = models.JSONField(
        default=list,
        help_text="History of level changes",
        blank=True,
        null=True
    )
    
    def save(self, *args, **kwargs):
        # Ensure progression_history is initialized
        if self.progression_history is None:
            self.progression_history = []
        
        # Ensure current_level_display is set based on current_level
        for level_id, level_name in self.LEVEL_CHOICES:
            if self.current_level == level_id:
                self.current_level_display = level_name
                break
        
        # Ensure other JSON fields are initialized
        if self.skill_strengths is None:
            self.skill_strengths = {}
        if self.skill_weaknesses is None:
            self.skill_weaknesses = {}
        
        super().save(*args, **kwargs)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.student.username}'s Level: {self.get_current_level_display()}"


class LevelRequirement(models.Model):
    """
    Model defining requirements for each proficiency level.
    """
    level = models.IntegerField(choices=StudentLevel.LEVEL_CHOICES, unique=True)
    min_assessment_score = models.FloatField(default=60.0)
    required_skills = models.JSONField(default=list)
    min_completed_courses = models.IntegerField(default=0)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Requirements for {self.get_level_display()}"

    def get_level_display(self):
        """Get the display name for the level."""
        for level_id, level_name in StudentLevel.LEVEL_CHOICES:
            if self.level == level_id:
                return level_name
        return "Unknown"

    def get_requirements_dict(self):
        """
        Get requirements as a formatted dictionary.
        """
        return {
            "min_assessment_score": self.min_assessment_score,
            "required_skills": self.required_skills,
            "min_completed_courses": self.min_completed_courses,
            "description": self.description,
        }

    class Meta:
        ordering = ['level']
        verbose_name = "Level Requirement"
        verbose_name_plural = "Level Requirements"


class Assessment(models.Model):
    """Unified model for all types of assessments"""
    ASSESSMENT_TYPES = [
        ("PLACEMENT", "Placement Assessment"),
        ("COURSE", "Course Assessment"),
        ("MILESTONE", "Milestone Assessment"),
        ("QUIZ", "Quiz"),
        ("EXAM", "Exam"),
        ("PRACTICE", "Practice")
    ]
    
    STATUS_CHOICES = [
        ("PENDING", "Pending"),
        ("IN_PROGRESS", "In Progress"),
        ("COMPLETED", "Completed"),
        ("EXPIRED", "Expired")
    ]
    
    title = models.CharField(max_length=255, blank=True)
    description = models.TextField(blank=True)
    assessment_type = models.CharField(max_length=20, choices=ASSESSMENT_TYPES)
    student = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="assessments"
    )
    questions = models.ManyToManyField(
        "AssessmentQuestion",
        through="AssessmentResponse"
    )
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="PENDING")
    score = models.FloatField(null=True, blank=True)
    start_time = models.DateTimeField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)
    completed = models.BooleanField(default=False)
    skill_scores = models.JSONField(default=dict, help_text="Scores by skill category")
    feedback = models.JSONField(default=dict, help_text="Structured feedback")
    is_active = models.BooleanField(default=True)
    is_adaptive = models.BooleanField(default=True, help_text="Whether this is an adaptive assessment")
    
    # Adaptive assessment tracking
    adaptive_progression = models.JSONField(
        default=dict,
        help_text="Tracks the progression of difficulty during adaptive assessment"
    )
    current_difficulty_level = models.PositiveSmallIntegerField(
        default=1,
        help_text="Current difficulty level in adaptive assessment"
    )
    
    # Learning path for path-specific assessments
    learning_path = models.CharField(
        max_length=50,
        choices=[
            ("programming", "Programming"),
            ("cybersecurity", "Cybersecurity"),
            ("finance", "Finance"),
            ("marketing", "Marketing"),
            ("general", "General")
        ],
        default="general",
        help_text="The learning path this assessment is for"
    )
    
    # Store detailed results including level changes
    detailed_results = models.JSONField(
        default=dict,
        help_text="Detailed assessment results"
    )
    
    # Store initial and final levels for easy access
    initial_level = models.IntegerField(
        null=True,
        blank=True,
        help_text="Student's level before assessment"
    )
    final_level = models.IntegerField(
        null=True,
        blank=True,
        help_text="Student's level after assessment"
    )
    
    # Track if the level changed
    level_changed = models.BooleanField(
        default=False,
        help_text="Whether the student's level changed"
    )
    
    # Track time spent on assessment
    time_spent = models.IntegerField(
        null=True,
        blank=True,
        help_text="Time spent in seconds"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ["-created_at"]
    
    def __str__(self):
        return f"{self.title or self.get_assessment_type_display()} - {self.student.username}"


class AssessmentQuestion(BaseQuestion):
    """Model for assessment questions"""
    
    # Category field specific to assessment questions
    CATEGORY_CHOICES = [
        ("programming", "Programming"),
        ("cybersecurity", "Cybersecurity"),
        ("finance", "Finance"),
        ("marketing", "Marketing"),
        ("general", "General")
    ]
    
    # Category field specific to assessment questions
    category = models.CharField(
        max_length=50,
        choices=CATEGORY_CHOICES,
        default="general"
    )
    
    # Learning path field to match the category choices
    learning_path = models.CharField(
        max_length=50,
        choices=CATEGORY_CHOICES,
        default="general",
        help_text="The learning path this question belongs to"
    )
    
    # Fields to link with skills
    skills_assessed = models.ManyToManyField(
        "core.Skill",
        related_name="questions",
        blank=True
    )
    
    # Fields to control question visibility and usage
    is_public = models.BooleanField(
        default=True,
        help_text="Whether this question can be shown to students"
    )
    is_placement = models.BooleanField(
        default=False,
        help_text="Whether this question is for placement assessments"
    )
    ai_reviewed = models.BooleanField(
        default=False,
        help_text="Whether this question was reviewed by AI"
    )
    ai_suggested = models.BooleanField(default=False)
    
    # Property to maintain backward compatibility with code that uses 'text'
    @property
    def text(self):
        return self.question_text
    
    @text.setter
    def text(self, value):
        self.question_text = value
    
    def check_answer(self, answer):
        """Override to handle JSON correct_answer format and maintain backward compatibility"""
        import json

        # Handle the case where correct_answer is a JSON object
        correct_answer = self.correct_answer
        if isinstance(correct_answer, dict):
            # If it's a dict, extract the 'answer' field
            correct_answer = correct_answer.get('answer', correct_answer)
        elif isinstance(correct_answer, str):
            try:
                # Try to parse as JSON in case it's a JSON string
                parsed = json.loads(correct_answer)
                if isinstance(parsed, dict) and 'answer' in parsed:
                    correct_answer = parsed['answer']
            except (json.JSONDecodeError, TypeError):
                # If it's not valid JSON, use as-is
                pass

        # Now compare with the extracted correct answer
        if not answer or not correct_answer:
            return False

        # Convert both to strings and compare (case-insensitive)
        answer_str = str(answer).lower().strip()
        correct_str = str(correct_answer).lower().strip()

        return answer_str == correct_str
    
    def __str__(self):
        return f"{self.question_text[:50]}..."


class AssessmentResponse(BaseQuestionResponse):
    """Model to track student responses to questions"""

    assessment = models.ForeignKey(
        Assessment,
        on_delete=models.CASCADE,
        related_name="responses"
    )
    question = models.ForeignKey(
        AssessmentQuestion,
        on_delete=models.CASCADE,
        related_name="responses"
    )
    student = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="assessment_responses",
        null=True,
        blank=True
    )

    # Legacy fields for backward compatibility
    student_answer = models.JSONField(
        null=True,
        blank=True,
        help_text="JSON format answer (legacy)"
    )
    answer = models.TextField(
        blank=True,
        null=True,
        help_text="Text answer (legacy)"
    )
    feedback = models.TextField(blank=True)

    class Meta:
        # Allow multiple responses per question per assessment, but unique per student
        unique_together = ["assessment", "question", "student"]
        ordering = ["created_at"]
        indexes = [
            models.Index(fields=["assessment", "question"], name="assessment_response_idx"),
            models.Index(fields=["student", "is_correct"], name="student_correct_response_idx")
        ]

    def __str__(self):
        return f"{self.student.username} - {self.question.question_text[:30]}..."

    def evaluate(self) -> bool:
        """
        Evaluate the response and set is_correct and points_earned.

        Returns:
            True if the answer is correct, False otherwise
        """
        try:
            # Get the answer text from the primary field first, then fallbacks
            answer_text = self.answer_text or self.answer or ""

            # Handle legacy student_answer field
            if not answer_text and self.student_answer is not None:
                if isinstance(self.student_answer, (dict, list)):
                    import json
                    answer_text = json.dumps(self.student_answer)
                else:
                    answer_text = str(self.student_answer)

            # Use the question's check_answer method to evaluate the answer
            self.is_correct = self.question.check_answer(answer_text)

            # Set points earned based on correctness
            self.points_earned = self.question.points if self.is_correct else 0

            # Ensure answer_text is saved
            if not self.answer_text:
                self.answer_text = answer_text

            return self.is_correct

        except Exception as e:
            # Log the error and mark as incorrect
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error evaluating response {self.id}: {e}")
            self.is_correct = False
            self.points_earned = 0
            return False


class AssessmentSettings(models.Model):
    """Model for configuring assessment behavior and settings"""

    ASSESSMENT_TYPE_CHOICES = [
        ("PLACEMENT", "Placement Assessment"),
        ("COURSE", "Course Assessment"),
        ("MILESTONE", "Milestone Assessment"),
        ("QUIZ", "Quiz"),
        ("EXAM", "Exam"),
        ("PRACTICE", "Practice")
    ]

    # Assessment type this setting applies to (optional - if null, applies to all)
    assessment_type = models.CharField(
        max_length=20,
        choices=ASSESSMENT_TYPE_CHOICES,
        null=True,
        blank=True,
        help_text="Assessment type these settings apply to. Leave blank for global settings."
    )

    # Basic settings
    max_attempts = models.PositiveIntegerField(
        default=3,
        help_text="Maximum number of attempts allowed"
    )
    time_limit = models.PositiveIntegerField(
        default=60,
        help_text="Time limit in minutes"
    )
    passing_score = models.FloatField(
        default=70.0,
        help_text="Minimum score required to pass (percentage)"
    )

    # Advanced settings
    questions_per_assessment = models.PositiveIntegerField(
        default=10,
        help_text="Number of questions per assessment"
    )
    allow_retakes = models.BooleanField(
        default=True,
        help_text="Whether students can retake the assessment"
    )
    retake_cooldown_days = models.PositiveIntegerField(
        default=1,
        help_text="Days to wait before allowing retake"
    )

    # Adaptive assessment settings
    adaptive_difficulty = models.BooleanField(
        default=True,
        help_text="Enable adaptive difficulty adjustment"
    )
    difficulty_adjustment_threshold = models.FloatField(
        default=0.7,
        help_text="Threshold for adjusting difficulty (0.0-1.0)"
    )

    # Security settings
    randomize_questions = models.BooleanField(
        default=True,
        help_text="Randomize question order"
    )
    randomize_options = models.BooleanField(
        default=True,
        help_text="Randomize answer options"
    )
    prevent_backtracking = models.BooleanField(
        default=False,
        help_text="Prevent students from going back to previous questions"
    )

    # Feedback settings
    show_correct_answers = models.BooleanField(
        default=True,
        help_text="Show correct answers after completion"
    )
    show_explanations = models.BooleanField(
        default=True,
        help_text="Show explanations for answers"
    )
    immediate_feedback = models.BooleanField(
        default=False,
        help_text="Show feedback immediately after each question"
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="assessment_settings_created"
    )

    class Meta:
        verbose_name = "Assessment Settings"
        verbose_name_plural = "Assessment Settings"
        unique_together = ["assessment_type"]  # Only one setting per assessment type
        ordering = ["assessment_type"]

    def __str__(self):
        if self.assessment_type:
            return f"Settings for {self.get_assessment_type_display()}"
        return "Global Assessment Settings"

    @classmethod
    def get_settings_for_type(cls, assessment_type=None):
        """Get settings for a specific assessment type, falling back to global settings"""
        try:
            if assessment_type:
                return cls.objects.get(assessment_type=assessment_type)
        except cls.DoesNotExist:
            pass

        # Fall back to global settings (assessment_type=None)
        try:
            return cls.objects.get(assessment_type=None)
        except cls.DoesNotExist:
            # Return default settings if none exist
            return cls(
                max_attempts=3,
                time_limit=60,
                passing_score=70.0,
                questions_per_assessment=10,
                allow_retakes=True,
                retake_cooldown_days=1,
                adaptive_difficulty=True,
                difficulty_adjustment_threshold=0.7,
                randomize_questions=True,
                randomize_options=True,
                prevent_backtracking=False,
                show_correct_answers=True,
                show_explanations=True,
                immediate_feedback=False
            )

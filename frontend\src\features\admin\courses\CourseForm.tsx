import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { isRtlLang } from 'rtl-detect';
import {
  Box,
  Button,
  TextField,
  Typography,
  Paper,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  CircularProgress,
  Alert,
  Chip,
  Autocomplete,
  Divider,
} from '@mui/material';
import courseService from '@services/courseService';
import userService from '@services/userService';
import { useToast } from '@hooks/useToast';
import { handleApiError } from '@utils/errorHandling';

interface CourseFormData {
  title: string;
  course_code: string;
  description: string;
  department: number | null;
  instructor: number | null;
  credits: number;
  capacity: number; // Added capacity field
  waitlist_capacity: number; // Added waitlist capacity field
  semester: string; // Added semester field
  prerequisites: number[];
  corequisites: number[];
  skills: number[];
  academic_year: string;
  required_level: number;
  recommended_level: number;
  syllabus: File | null;
}

interface Skill {
  id: number;
  name: string;
}

interface Department {
  id: number;
  name: string;
  code: string;
}

interface Professor {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
}

interface Course {
  id: number;
  title: string;
  course_code: string;
}

const CourseForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { showSuccess, showError } = useToast();
  const { i18n } = useTranslation();
  const isRtl = isRtlLang(i18n.language);

  const [formData, setFormData] = useState<CourseFormData>({
    title: '',
    course_code: '',
    description: '',
    department: null,
    instructor: null,
    credits: 3,
    capacity: 30, // Default capacity
    waitlist_capacity: 10, // Default waitlist capacity
    semester: 'FALL', // Default semester
    prerequisites: [],
    corequisites: [],
    skills: [],
    academic_year: `${new Date().getFullYear()}-${new Date().getFullYear() + 1}`,
    required_level: 1,
    recommended_level: 1,
    syllabus: null,
  });

  const [departments, setDepartments] = useState<Department[]>([]);
  const [professors, setProfessors] = useState<Professor[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [skills, setSkills] = useState<Skill[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Helper function to safely get translations with Arabic fallback
  const getTranslation = (key: string, defaultValue: string): string => {
    // If language is Arabic, use hardcoded Arabic translations
    if (isRtl) {
      // Arabic translations for course form
      const arTranslations: Record<string, string> = {
        'courseForm.title': 'عنوان الدورة',
        'courseForm.courseCode': 'رمز الدورة',
        'courseForm.description': 'الوصف',
        'courseForm.department': 'القسم',
        'courseForm.professor': 'الأستاذ',
        'courseForm.credits': 'الساعات المعتمدة',
        'courseForm.academicYear': 'العام الدراسي',
        'courseForm.requiredLevel': 'المستوى المطلوب',
        'courseForm.recommendedLevel': 'المستوى الموصى به',
        'courseForm.prerequisites': 'المتطلبات السابقة',
        'courseForm.selectPrerequisites': 'اختر المتطلبات السابقة',
        'courseForm.corequisites': 'المتطلبات المتزامنة',
        'courseForm.selectCorequisites': 'اختر المتطلبات المتزامنة',
        'courseForm.skills': 'المهارات',
        'courseForm.selectSkills': 'اختر المهارات',
        'courseForm.syllabus': 'المنهج الدراسي (PDF)',
        'courseForm.uploadSyllabus': 'تحميل المنهج الدراسي',
        'courseForm.addCourse': 'إضافة دورة',
        'courseForm.editCourse': 'تعديل الدورة',
        'courseForm.createCourse': 'إنشاء دورة',
        'courseForm.updateCourse': 'تحديث الدورة',
        'courseForm.none': 'لا شيء',
        'courseForm.noDepartmentsAvailable':
          'لا توجد أقسام متاحة. الرجاء إضافة قسم أولاً.',
        'courseForm.noProfessorsAvailable': 'لا يوجد أساتذة متاحين',
        'courseForm.addDepartment': 'إضافة قسم',
        'courseForm.addProfessor': 'إضافة أستاذ',
        'courseForm.codePrefix': 'يجب أن يبدأ الرمز برمز القسم',
        'courseForm.englishInputsOnly':
          'يجب أن تكون جميع المدخلات باللغة الإنجليزية، حتى عندما تكون الواجهة باللغة العربية.',
        'courseForm.titleRequired': 'عنوان الدورة مطلوب',
        'courseForm.courseCodeRequired': 'رمز الدورة مطلوب',
        'courseForm.departmentRequired': 'القسم مطلوب',
        'courseForm.creditsRequired': 'الساعات المعتمدة مطلوبة',
        'courseForm.formHasErrors': 'يرجى تصحيح الأخطاء في النموذج',
        'courseForm.saveFailed': 'فشل حفظ الدورة',
        'courseForm.courseCreated': 'تم إنشاء الدورة بنجاح',
        'courseForm.courseUpdated': 'تم تحديث الدورة بنجاح',
        'courseForm.failedToLoadFormData': 'فشل تحميل بيانات النموذج',
        'common.cancel': 'إلغاء',
      };

      // Check if we have a hardcoded Arabic translation
      if (arTranslations[key]) {
        return arTranslations[key];
      }
    }

    const translation = t(key);
    return translation === key ? defaultValue : translation;
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch departments, professors, and courses in parallel
        // Use Promise.allSettled to handle individual promise rejections
        const results = await Promise.allSettled([
          courseService.getDepartments(),
          userService.getProfessors(),
          courseService.getCourses(),
          courseService.getSkills(),
        ]);

        console.log('API results:', results);

        // Extract responses from fulfilled promises
        const deptResponse =
          results[0].status === 'fulfilled' ? results[0].value : { data: [] };
        const profResponse =
          results[1].status === 'fulfilled' ? results[1].value : { data: [] };
        const coursesResponse =
          results[2].status === 'fulfilled' ? results[2].value : { data: [] };
        const skillsResponse =
          results[3].status === 'fulfilled' ? results[3].value : { data: [] };

        // Log any rejected promises
        results.forEach((result, index) => {
          if (result.status === 'rejected') {
            console.warn(`API request ${index} failed:`, result.reason);
          }
        });

        // Handle different response structures
        let departmentsData =
          deptResponse.data?.data || deptResponse.data || [];
        console.log('Departments data:', departmentsData);

        // If the data is not an array, try to extract it from a nested structure
        if (!Array.isArray(departmentsData)) {
          console.warn(
            'Departments data is not an array. Trying to extract departments...'
          );

          // Try to find an array in the response that contains department objects
          const possibleDepartments = Object.values(departmentsData).find(
            val =>
              Array.isArray(val) &&
              val.length > 0 &&
              val[0] &&
              typeof val[0] === 'object' &&
              'id' in val[0]
          );

          if (possibleDepartments && Array.isArray(possibleDepartments)) {
            console.log(
              'Found departments in alternative format:',
              possibleDepartments
            );
            departmentsData = possibleDepartments;
          } else {
            console.warn('Could not find departments array in response');
            departmentsData = [];
          }
        }

        // If we still don't have departments, try to create them from the departments list page
        if (departmentsData.length === 0) {
          console.warn(
            'No departments found in the response. Creating from hardcoded list...'
          );

          // Create departments from the hardcoded list in the departments page
          // Use string IDs to match what the backend expects
          const hardcodedDepartments = [
            { id: '1', name: 'Programming', code: 'PROG' },
            { id: '2', name: 'Cybersecurity', code: 'CSEC' },
            { id: '3', name: 'Finance', code: 'FIN' },
            { id: '4', name: 'Marketing', code: 'MKT' },
            { id: '5', name: 'Business', code: 'BUS' },
            { id: '6', name: 'Economics', code: 'ECON' },
            { id: '7', name: 'Computer Science', code: 'CS' },
            { id: '8', name: 'Data Science', code: 'DS' },
            { id: '9', name: 'Artificial Intelligence', code: 'AI' },
            { id: '10', name: 'Mathematics', code: 'MAT' },
          ];

          console.log('Using hardcoded departments with string IDs');
          departmentsData = hardcodedDepartments;
        }

        setDepartments(departmentsData);

        // Handle different response structures for professors
        let professorsData = profResponse.data?.data || profResponse.data || [];
        console.log('Professors data:', professorsData);

        // If the data is not an array, try to extract it from a nested structure
        if (!Array.isArray(professorsData)) {
          // Try to find an array in the response that contains professor objects
          const possibleProfessors = Object.values(professorsData).find(
            val =>
              Array.isArray(val) &&
              val.length > 0 &&
              val[0] &&
              typeof val[0] === 'object' &&
              'id' in val[0] &&
              'email' in val[0]
          );

          if (possibleProfessors && Array.isArray(possibleProfessors)) {
            console.log(
              'Found professors in alternative format:',
              possibleProfessors
            );
            professorsData = possibleProfessors;
          } else {
            console.warn('Could not find professors array in response');
            professorsData = [];
          }
        }

        setProfessors(professorsData);

        const coursesData =
          coursesResponse.data?.data || coursesResponse.data || [];
        setCourses(Array.isArray(coursesData) ? coursesData : []);

        // Handle skills data
        let skillsData = skillsResponse.data?.data || skillsResponse.data || [];
        console.log('Skills data:', skillsData);

        // If the data is not an array, try to extract it from a nested structure
        if (!Array.isArray(skillsData)) {
          // Try to find an array in the response that contains skill objects
          const possibleSkills = Object.values(skillsData).find(
            val =>
              Array.isArray(val) &&
              val.length > 0 &&
              val[0] &&
              typeof val[0] === 'object' &&
              'id' in val[0]
          );

          if (possibleSkills && Array.isArray(possibleSkills)) {
            console.log('Found skills in alternative format:', possibleSkills);
            skillsData = possibleSkills;
          } else {
            console.warn('Could not find skills array in response');
            skillsData = [];
          }
        }

        setSkills(skillsData);

        // If in edit mode, fetch the course data
        if (isEditMode && id) {
          const courseResponse = await courseService.getCourseById(id);
          const courseData = courseResponse.data;

          // Set form data with course data
          setFormData({
            title: courseData.title || '',
            course_code: courseData.course_code || '',
            description: courseData.description || '',
            department: courseData.department || null,
            instructor: courseData.instructor || null,
            credits: courseData.credits || 3,
            capacity: courseData.capacity || 30, // Added capacity field
            prerequisites: courseData.prerequisites || [],
            corequisites: courseData.corequisites || [],
            skills: courseData.skills || [],
            academic_year:
              courseData.academic_year ||
              new Date().getFullYear() + '-' + (new Date().getFullYear() + 1),
            required_level: courseData.required_level || 1,
            recommended_level: courseData.recommended_level || 1,
            syllabus: courseData.syllabus || null,
          });
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        const errorResult = handleApiError(error);
        showError(
          errorResult.message ||
            getTranslation(
              'courseForm.failedToLoadFormData',
              'Failed to load form data'
            )
        );
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id, isEditMode, showError, t]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>
  ) => {
    const { name, value } = e.target;
    if (!name) return;

    console.log(`Input changed: ${name} = ${value}`);

    // Special handling for department
    if (name === 'department') {
      console.log('Department selected:', value);
      console.log('Available departments:', departments);

      // Find the selected department
      const selectedDept = departments.find(dept => dept.id === value);
      console.log('Selected department:', selectedDept);

      // Auto-generate course code prefix based on department code
      if (selectedDept && selectedDept.code) {
        // Get the current course code
        const currentCode = formData.course_code || '';

        // If the course code is empty or doesn't start with the department code,
        // update it to start with the department code
        if (!currentCode || !currentCode.startsWith(selectedDept.code)) {
          // Extract any numbers from the current code
          const numbers = currentCode.match(/\d+/);
          const numberPart = numbers ? numbers[0] : '';

          // Create new course code with department code prefix
          const newCourseCode =
            `${selectedDept.code}${numberPart ? numberPart : ''}`.toUpperCase();

          // Update form data with new course code
          setFormData(prev => ({
            ...prev,
            department: value,
            course_code: newCourseCode,
          }));

          return; // Exit early since we've already updated the form data
        }
      }
    }

    setFormData(prev => {
      const newData = {
        ...prev,
        [name]: value,
      };
      console.log('Updated form data:', newData);
      return newData;
    });

    // Clear error when field is changed
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFormData(prev => ({
        ...prev,
        syllabus: e.target.files[0],
      }));
    }
  };

  const handleMultiSelectChange =
    (name: string) =>
    (_event: React.SyntheticEvent, newValue: Array<{ id: number }>) => {
      setFormData(prev => ({
        ...prev,
        [name]: newValue.map(item => item.id),
      }));
    };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title) {
      newErrors.title = t('courseForm.titleRequired');
    }

    if (!formData.course_code) {
      newErrors.course_code = t('courseForm.courseCodeRequired');
    }

    if (!formData.department) {
      newErrors.department = t('courseForm.departmentRequired');
    }

    if (!formData.credits || formData.credits <= 0) {
      newErrors.credits = t('courseForm.creditsRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async e => {
    e.preventDefault();

    if (!validateForm()) {
      showError(
        getTranslation(
          'courseForm.formHasErrors',
          'Please correct the errors in the form'
        )
      );
      return;
    }

    setSubmitting(true);

    try {
      // Create a comprehensive course data object
      const courseData = {
        title: formData.title?.trim() || '',
        course_code: formData.course_code?.trim()?.toUpperCase() || '',
        description: formData.description?.trim() || '',
        credits: parseInt(String(formData.credits)) || 3,
        capacity: parseInt(String(formData.capacity)) || 30,
        waitlist_capacity: parseInt(String(formData.waitlist_capacity)) || 10,
        semester: formData.semester || 'FALL',
        academic_year: formData.academic_year || `${new Date().getFullYear()}-${new Date().getFullYear() + 1}`,
        required_level: parseInt(String(formData.required_level)) || 1,
        recommended_level: parseInt(String(formData.recommended_level)) || 1,
        is_active: true,
        status: 'OPEN',
        enrolled_count: 0,
      };

      // Handle department - ensure it's a valid number or string
      if (formData.department) {
        const deptId = typeof formData.department === 'string' 
          ? parseInt(formData.department) 
          : formData.department;
        courseData.department = deptId;
      } else {
        // Find the first available department
        const firstDept = departments.find(dept => dept.id);
        courseData.department = firstDept ? 
          (typeof firstDept.id === 'string' ? parseInt(firstDept.id) : firstDept.id) : 
          1;
      }

      // Handle instructor - ensure it's a valid number or null
      if (formData.instructor) {
        const instrId = typeof formData.instructor === 'string' 
          ? parseInt(formData.instructor) 
          : formData.instructor;
        courseData.instructor = instrId;
      } else {
        // Set to null if no instructor selected
        courseData.instructor = null;
      }

      // Handle prerequisites and corequisites
      if (formData.prerequisites && formData.prerequisites.length > 0) {
        courseData.prerequisites = formData.prerequisites;
      }

      if (formData.corequisites && formData.corequisites.length > 0) {
        courseData.corequisites = formData.corequisites;
      }

      if (formData.skills && formData.skills.length > 0) {
        courseData.skills = formData.skills;
      }

      console.log('Sending course data:', courseData);

      // Convert to FormData for API compatibility
      const formDataToSend = new FormData();
      
      // Add all course data fields
      Object.entries(courseData).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          if (Array.isArray(value)) {
            // Handle array fields like prerequisites, corequisites, skills
            value.forEach((item, index) => {
              formDataToSend.append(`${key}[${index}]`, item.toString());
            });
          } else {
            formDataToSend.append(key, value.toString());
          }
        }
      });

      // Handle syllabus file upload
      if (formData.syllabus && formData.syllabus instanceof File) {
        formDataToSend.append('syllabus', formData.syllabus);
      }

      // Log the final form data being sent (but not files)
      const debugData = Object.fromEntries(
        Array.from(formDataToSend.entries()).filter(([key]) => key !== 'syllabus')
      );
      console.log('Sending form data:', debugData);

      if (isEditMode && id) {
        const response = await courseService.updateCourse(parseInt(id), formDataToSend);
        
        if (response.error) {
          throw new Error(response.error);
        }
        
        showSuccess(
          getTranslation(
            'courseForm.courseUpdated',
            'Course updated successfully'
          )
        );
      } else {
        const response = await courseService.createCourse(formDataToSend);
        
        if (response.error) {
          throw new Error(response.error);
        }
        
        showSuccess(
          getTranslation(
            'courseForm.courseCreated',
            'Course created successfully'
          )
        );
      }

      // Navigate back to courses list
      navigate('/admin/courses');
      
    } catch (error: any) {
      console.error('Error saving course:', error);

      // Handle different types of errors
      if (error.response?.data) {
        const errorData = error.response.data;
        
        // Handle validation errors
        if (errorData.status === 'error' && errorData.errors) {
          const formattedErrors: Record<string, string> = {};
          
          Object.entries(errorData.errors).forEach(([key, value]) => {
            if (Array.isArray(value)) {
              formattedErrors[key] = value[0]?.toString() || '';
            } else {
              formattedErrors[key] = value?.toString() || '';
            }
          });
          
          setErrors(formattedErrors);
          showError(errorData.message || 'Please correct the form errors');
        } else {
          // Handle general API errors
          showError(errorData.message || errorData.detail || 'Failed to save course');
        }
      } else {
        // Handle network or other errors
        showError(error.message || getTranslation('courseForm.saveFailed', 'Failed to save course'));
      }
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Box
        display='flex'
        justifyContent='center'
        alignItems='center'
        minHeight='400px'
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
      <Typography variant='h5' component='h1' gutterBottom>
        {isEditMode
          ? getTranslation('courseForm.editCourse', 'Edit Course')
          : getTranslation('courseForm.addCourse', 'Add Course')}
      </Typography>

      {isRtl && (
        <Alert severity='info' sx={{ mb: 2 }}>
          {getTranslation(
            'courseForm.englishInputsOnly',
            'All inputs should be in English, even when the interface is in Arabic.'
          )}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Box>
              <FormControl fullWidth error={!!errors.department} required>
                <InputLabel>
                  {getTranslation('courseForm.department', 'Department')}
                </InputLabel>
                <Select
                  name='department'
                  value={formData.department || ''}
                  onChange={handleChange}
                  label={t('courseForm.department')}
                  MenuProps={{ style: { maxHeight: 500 } }}
                >
                  {departments.length > 0 ? (
                    departments.map(dept => (
                      <MenuItem key={dept.id} value={dept.id.toString()}>
                        {dept.name} ({dept.code})
                      </MenuItem>
                    ))
                  ) : (
                    <MenuItem value='' disabled>
                      {getTranslation(
                        'courseForm.noDepartmentsAvailable',
                        'No departments available. Please add departments first.'
                      )}
                    </MenuItem>
                  )}
                </Select>
                {errors.department && (
                  <FormHelperText>{errors.department}</FormHelperText>
                )}
              </FormControl>

              {departments.length === 0 && (
                <Button
                  variant='outlined'
                  color='primary'
                  size='small'
                  onClick={() => navigate('/admin/departments')}
                  sx={{ mt: 1 }}
                >
                  {getTranslation('courseForm.addDepartment', 'Add Department')}
                </Button>
              )}
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={getTranslation('courseForm.title', 'Course Title')}
              name='title'
              value={formData.title}
              onChange={handleChange}
              error={!!errors.title}
              helperText={errors.title}
              required
              inputProps={{
                dir: 'ltr',
                style: { textAlign: 'left' },
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={getTranslation('courseForm.courseCode', 'Course Code')}
              name='course_code'
              value={formData.course_code}
              onChange={handleChange}
              error={!!errors.course_code}
              helperText={
                errors.course_code ||
                (formData.department
                  ? getTranslation(
                      'courseForm.codePrefix',
                      'Code should start with department code'
                    )
                  : '')
              }
              required
              inputProps={{
                dir: 'ltr',
                style: { textAlign: 'left' },
              }}
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label={getTranslation('courseForm.description', 'Description')}
              name='description'
              value={formData.description}
              onChange={handleChange}
              multiline
              rows={4}
              inputProps={{
                dir: 'ltr',
                style: { textAlign: 'left' },
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Box>
              <FormControl fullWidth>
                <InputLabel>
                  {getTranslation('courseForm.professor', 'Professor')}
                </InputLabel>
                <Select
                  name='instructor'
                  value={formData.instructor || ''}
                  onChange={handleChange}
                  label={getTranslation('courseForm.professor', 'Professor')}
                >
                  <MenuItem value=''>
                    <em>{getTranslation('courseForm.none', 'None')}</em>
                  </MenuItem>
                  {professors.length > 0 ? (
                    professors.map(prof => (
                      <MenuItem key={prof.id} value={prof.id}>
                        {prof.first_name || ''} {prof.last_name || ''}{' '}
                        {prof.email ? `(${prof.email})` : ''}
                      </MenuItem>
                    ))
                  ) : (
                    <MenuItem value='' disabled>
                      {getTranslation(
                        'courseForm.noProfessorsAvailable',
                        'No professors available'
                      )}
                    </MenuItem>
                  )}
                </Select>
              </FormControl>

              {professors.length === 0 && (
                <Button
                  variant='outlined'
                  color='primary'
                  size='small'
                  onClick={() => navigate('/admin/users')}
                  sx={{ mt: 1 }}
                >
                  {getTranslation('courseForm.addProfessor', 'Add Professor')}
                </Button>
              )}
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              type='number'
              label={getTranslation('courseForm.credits', 'Credits')}
              name='credits'
              value={formData.credits}
              onChange={handleChange}
              error={!!errors.credits}
              helperText={errors.credits}
              required
              InputProps={{
                inputProps: { min: 1, max: 6 },
              }}
              inputProps={{
                dir: 'ltr',
                style: { textAlign: 'left' },
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              type='number'
              label={getTranslation('courseForm.capacity', 'Capacity')}
              name='capacity'
              value={formData.capacity}
              onChange={handleChange}
              error={!!errors.capacity}
              helperText={errors.capacity}
              required
              InputProps={{
                inputProps: { min: 1, max: 500 },
              }}
              inputProps={{
                dir: 'ltr',
                style: { textAlign: 'left' },
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              type='number'
              label={getTranslation(
                'courseForm.waitlistCapacity',
                'Waitlist Capacity'
              )}
              name='waitlist_capacity'
              value={formData.waitlist_capacity}
              onChange={handleChange}
              InputProps={{
                inputProps: { min: 0, max: 100 },
              }}
              inputProps={{
                dir: 'ltr',
                style: { textAlign: 'left' },
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>
                {getTranslation('courseForm.semester', 'Semester')}
              </InputLabel>
              <Select
                name='semester'
                value={formData.semester}
                onChange={handleChange}
                label={getTranslation('courseForm.semester', 'Semester')}
              >
                <MenuItem value='FALL'>
                  {getTranslation('courseForm.fall', 'Fall')}
                </MenuItem>
                <MenuItem value='SPRING'>
                  {getTranslation('courseForm.spring', 'Spring')}
                </MenuItem>
                <MenuItem value='SUMMER'>
                  {getTranslation('courseForm.summer', 'Summer')}
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={getTranslation('courseForm.academicYear', 'Academic Year')}
              name='academic_year'
              value={formData.academic_year}
              onChange={handleChange}
              placeholder='2023-2024'
              inputProps={{
                dir: 'ltr',
                style: { textAlign: 'left' },
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              type='number'
              label={getTranslation(
                'courseForm.requiredLevel',
                'Required Level'
              )}
              name='required_level'
              value={formData.required_level}
              onChange={handleChange}
              InputProps={{
                inputProps: { min: 1, max: 10 },
              }}
              inputProps={{
                dir: 'ltr',
                style: { textAlign: 'left' },
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              type='number'
              label={getTranslation(
                'courseForm.recommendedLevel',
                'Recommended Level'
              )}
              name='recommended_level'
              value={formData.recommended_level}
              onChange={handleChange}
              InputProps={{
                inputProps: { min: 1, max: 10 },
              }}
              inputProps={{
                dir: 'ltr',
                style: { textAlign: 'left' },
              }}
            />
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Typography variant='h6' gutterBottom>
              {getTranslation('courseForm.prerequisites', 'Prerequisites')}
            </Typography>

            <Autocomplete
              multiple
              options={courses.filter(course => course.id !== Number(id))}
              getOptionLabel={option =>
                `${option.title} (${option.course_code})`
              }
              value={courses.filter(course =>
                formData.prerequisites.includes(course.id)
              )}
              onChange={handleMultiSelectChange('prerequisites')}
              renderInput={params => (
                <TextField
                  {...params}
                  label={getTranslation(
                    'courseForm.selectPrerequisites',
                    'Select prerequisites'
                  )}
                  placeholder={getTranslation(
                    'courseForm.selectPrerequisites',
                    'Select prerequisites'
                  )}
                  inputProps={{
                    ...params.inputProps,
                    dir: 'ltr',
                    style: { textAlign: 'left' },
                  }}
                />
              )}
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip
                    label={`${option.title} (${option.course_code})`}
                    {...getTagProps({ index })}
                    key={option.id}
                  />
                ))
              }
            />
          </Grid>

          <Grid item xs={12}>
            <Typography variant='h6' gutterBottom>
              {getTranslation('courseForm.corequisites', 'Corequisites')}
            </Typography>

            <Autocomplete
              multiple
              options={courses.filter(course => course.id !== Number(id))}
              getOptionLabel={option =>
                `${option.title} (${option.course_code})`
              }
              value={courses.filter(course =>
                formData.corequisites.includes(course.id)
              )}
              onChange={handleMultiSelectChange('corequisites')}
              renderInput={params => (
                <TextField
                  {...params}
                  label={getTranslation(
                    'courseForm.selectCorequisites',
                    'Select corequisites'
                  )}
                  placeholder={getTranslation(
                    'courseForm.selectCorequisites',
                    'Select corequisites'
                  )}
                  inputProps={{
                    ...params.inputProps,
                    dir: 'ltr',
                    style: { textAlign: 'left' },
                  }}
                />
              )}
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip
                    label={`${option.title} (${option.course_code})`}
                    {...getTagProps({ index })}
                    key={option.id}
                  />
                ))
              }
            />
          </Grid>

          <Grid item xs={12}>
            <Typography variant='h6' gutterBottom>
              {getTranslation('courseForm.skills', 'Skills')}
            </Typography>

            <Autocomplete
              multiple
              options={skills}
              getOptionLabel={option => option.name}
              value={skills.filter(skill => formData.skills.includes(skill.id))}
              onChange={handleMultiSelectChange('skills')}
              renderInput={params => (
                <TextField
                  {...params}
                  label={getTranslation(
                    'courseForm.selectSkills',
                    'Select skills'
                  )}
                  placeholder={getTranslation(
                    'courseForm.selectSkills',
                    'Select skills'
                  )}
                  inputProps={{
                    ...params.inputProps,
                    dir: 'ltr',
                    style: { textAlign: 'left' },
                  }}
                />
              )}
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip
                    label={option.name}
                    {...getTagProps({ index })}
                    key={option.id}
                  />
                ))
              }
            />
          </Grid>

          <Grid item xs={12}>
            <Typography variant='h6' gutterBottom>
              {getTranslation('courseForm.syllabus', 'Syllabus (PDF)')}
            </Typography>

            <input
              accept='application/pdf'
              style={{ display: 'none' }}
              id='syllabus-file'
              type='file'
              onChange={handleFileChange}
            />
            <label htmlFor='syllabus-file'>
              <Button variant='contained' component='span'>
                {getTranslation('courseForm.uploadSyllabus', 'Upload Syllabus')}
              </Button>
            </label>

            {formData.syllabus && (
              <Typography variant='body2' sx={{ mt: 1 }}>
                {typeof formData.syllabus === 'string' && formData.syllabus
                  ? formData.syllabus.split('/').pop()
                  : (formData.syllabus as File)?.name || 'Syllabus file'}
              </Typography>
            )}
          </Grid>

          <Grid item xs={12} sx={{ mt: 2 }}>
            <Box display='flex' justifyContent='space-between'>
              <Button
                variant='outlined'
                onClick={() => navigate('/admin/courses')}
                disabled={submitting}
              >
                {getTranslation('common.cancel', 'Cancel')}
              </Button>

              <Button
                type='submit'
                variant='contained'
                color='primary'
                disabled={submitting}
                startIcon={submitting ? <CircularProgress size={20} /> : null}
              >
                {isEditMode
                  ? getTranslation('courseForm.updateCourse', 'Update Course')
                  : getTranslation('courseForm.createCourse', 'Create Course')}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </form>
    </Paper>
  );
};

export default CourseForm;

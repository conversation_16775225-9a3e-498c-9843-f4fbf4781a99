import timeimportuuidfromtypingimportAnyDictListOptionalfrom django.contrib.authimportget_user_modelfrom django.utilsimport timezonefrom utils.ai_prompt_templatesimportPromptTypeadd_prompt_fragmentformat_prompt#ImportunifiedAIservicefrom utils.ai.servicesimportget_ai_servicefrom utils.ai_service_utilsimportget_ai_loggerget_error_messagefrom.modelsimportAIAssistantInteractionAIAssistantSessionAIAssistantSuggestionUser=get_user_model()logger=get_ai_logger("ai_assistant")classAIAssistantService:"""ServiceclassforAIAssistantfunctionality-modernizedtouseunifiedAIservice"""def__init__(self):self.service_name="ai_assistant"self.logger=get_ai_logger("ai_assistant")self.ai_service=get_ai_service()defget_suggestions(selfuser=Noneuser_type="all")->List[Dict[strAny]]:"""GetAIassistantsuggestionsbasedonusertype"""try:#Getsuggestionsfromdatabasesuggestions_qs=AIAssistantSuggestion.objects.filter(is_active=True)#Filterbyusertypeifspecifiedifuserandhasattr(user"user_type"):suggestions_qs=suggestions_qs.filter(target_user_type__in=["all"user.user_type])elifuser_type!="all":suggestions_qs=suggestions_qs.filter(target_user_type__in=["all"user_type])suggestions=[]forsuggestioninsuggestions_qs[:10]:#Limitto10suggestionssuggestions.append({"id":suggestion.id"text":suggestion.text"icon":suggestion.icon"category":suggestion.category})#Ifnodatabasesuggestionsreturndefaultsifnotsuggestions:suggestions=self._get_default_suggestions()returnsuggestionsexceptExceptionase:logger.error(f"ErrorgettingAIsuggestions:{str(e)}")returnself._get_default_suggestions()def_get_default_suggestions(self)->List[Dict[strAny]]:"""Getdefaultsuggestionswhendatabaseisempty"""return[{"id":1"text":"Showmeenrollmenttrendsforthissemester""icon":"TrendingUpIcon""category":"analytics"}{"id":2"text":"Whatdepartmentshavethehigheststudentperformance?""icon":"InsightsIcon""category":"analytics"}{"id":3"text":"Whichcourseshavethemoststudents?""icon":"SchoolIcon""category":"courses"}{"id":4"text":"Generateanoptimizedfacultyschedule""icon":"CalendarIcon""category":"scheduling"}{"id":5"text":"Createasummaryreportofstudentperformance""icon":"AssessmentIcon""category":"reports"}]defask_question(selfquestion:struser=Nonecontext:Dict=Nonesession_id:str=None)->Dict[strAny]:"""ProcessaquestionandreturnanAI-generatedanswer"""start_time=time.time()try:#Getorcreatesessionifnotsession_id:session_id=str(uuid.uuid4())session=self._get_or_create_session(session_idusercontext)#GenerateanswerusingAIserviceifself.ai_service:answer=self._generate_ai_answer(questioncontext)confidence_score=0.8#Defaultconfidenceelse:answer=self._generate_fallback_answer(question)confidence_score=0.5#Lowerconfidenceforfallbackresponse_time=time.time()-start_time#Saveinteractiononlyifsessionexists(userprovided)ifsession:interaction=AIAssistantInteraction.objects.create(session=sessionquestion=questionanswer=answerconfidence_score=confidence_scoreresponse_time=response_timemetadata=contextor{})#Updatesessionsession.total_interactions+=1session.save()return{"answer":answer"confidence_score":confidence_score"response_time":response_time"session_id":session_id"suggestions":self._get_follow_up_suggestions(questionanswer)}exceptExceptionase:logger.error(f"ErrorprocessingAIquestion:{str(e)}")return{"answer":"IapologizebutIencounteredanerrorprocessingyourquestion.Pleasetryagain.""confidence_score":0.0"response_time":time.time()-start_time"session_id":session_idorstr(uuid.uuid4())"suggestions":[]}def_get_or_create_session(selfsession_id:struser=Nonecontext:Dict=None)->AIAssistantSession:"""Getexistingsessionorcreatenewone"""try:session=AIAssistantSession.objects.get(session_id=session_id)ifcontext:session.context.update(context)session.save()returnsessionexceptAIAssistantSession.DoesNotExist:#Onlycreatesessionifuserisprovidedifuser:returnAIAssistantSession.objects.create(user=usersession_id=session_idcontext=contextor{})else:#ReturnNoneifnouserprovided-willbehandledbycallerreturnNonedef_generate_ai_answer(selfquestion:strcontext:Dict=None)->str:"""GenerateanswerusingAIservicewithstandardizedapproach"""try:prompt=f"""YouareahelpfulAIassistantforauniversitymanagementsystem.Pleaseprovideaclearconciseandhelpfulanswertothefollowingquestion:Question:{question}{f'Context:{context}'ifcontextelse''}Provideaprofessionalandinformativeresponse."""#Addeducationalcontextfragmentprompt=add_prompt_fragment(prompt"educational_context")returnself.generate_content(prompt)exceptExceptionase:self.logger.error(f"AIserviceerror:{str(e)}")returnself._generate_fallback_answer(question)def_generate_fallback_answer(selfquestion:str)->str:"""GeneratefallbackanswerwhenAIserviceisunavailable"""returnget_error_message("service_unavailable"f"Thankyouforyourquestion:'{question}'.I'mcurrentlyexperiencingtechnicaldifficultieswithmyAIprocessing.Pleasecontactsupportforassistanceortryrephrasingyourquestion.")def_get_follow_up_suggestions(selfquestion:stranswer:str)->List[str]:"""Generatefollow-upsuggestionsbasedonthequestionandanswer"""#Simplekeyword-basedsuggestionssuggestions=[]if"enrollment"inquestion.lower():suggestions.extend(["Showmeenrollmentbydepartment""Compareenrollmentwithprevioussemester"])elif"performance"inquestion.lower():suggestions.extend(["Showmegradedistribution""Whichcoursesneedimprovement?"])elif"course"inquestion.lower():suggestions.extend(["Showmecoursecompletionrates""Whichcoursesaremostpopular?"])returnsuggestions[:3]#Limitto3suggestions#Globalserviceinstanceai_assistant_service=AIAssistantService()
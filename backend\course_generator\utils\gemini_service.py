import logging#UseunifiedAIconfigurationsystemfrom utils.ai.servicesimportget_ai_servicelogger=logging.getLogger(__name__)classUnifiedAIService:"""ServiceforinteractingwithGoogle'sGeminiAPIThisisawrapperaroundtheunifiedAIserviceforbackwardcompatibility."""def__init__(self):"""InitializetheserviceusingtheunifiedAIservice"""try:#UsetheunifiedAIserviceself.ai_service=get_ai_service()#Checkiftheserviceisavailableself.is_configured=self.ai_serviceisnotNone#Usefallbackconfigurationsinceconfigcomesfromfrontendself.generation_config={'temperature':0.7'max_tokens':2048'top_p':0.9'top_k':40}ifself.is_configured:logger.info("GeminiAPIserviceinitializedsuccessfullyusingunifiedAIservice")else:logger.warning("UnifiedAIservicenotproperlyconfigured.Servicewillnotworkproperly.")exceptExceptionase:logger.error(f"ErrorinitializingGeminiservice:{e}")self.is_configured=Falsedefgenerate_text(selfprompttemperature=Nonemax_tokens=None):"""GeneratetextusingtheunifiedAIserviceArgs:prompt(str):TheprompttosendtotheAPItemperature(float):Controlsrandomness(0.0to1.0)max_tokens(int):MaximumnumberoftokenstogenerateReturns:str:Thegeneratedtext"""ifnotself.is_configured:logger.error("AIserviceisnotproperlyconfigured.Cannotgeneratetext.")return"Error:AIserviceisnotconfigured.PleasecheckyourAPIkey."try:#UsevaluesfromdynamicconfigifnotspecifiediftemperatureisNone:temperature=self.generation_config["temperature"]ifmax_tokensisNone:max_tokens=self.generation_config["max_tokens"]#UsetheunifiedAIservicetogeneratecontentresponse=self.ai_service.generate_content(prompttemperature=temperaturemax_tokens=max_tokens)returnresponseexceptExceptionase:logger.error(f"ErrorgeneratingtextwithunifiedAIservice:{e}")#Returnanerrormessagereturnf"Error:Failedtogeneratecontent.{str(e)}"#Nomockresponseneeded
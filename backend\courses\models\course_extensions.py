"""
CourseExtensionModelsThesemodelsprovidetype- specif icextensionsfor courseswithoutduplicatingcor ecoursedata.Thiseliminatestheduplicationbetweencoursesinteractive_learningand course_generator apps."""
from django.dbimpor t modelsfrom django.utilsimpor t timezonefrom django.utils.translationimpor t gettext_lazyas_from django.contrib.authimpor t get_user_modelUser = get_user_model()class InteractiveCourseFeatures(models.Model):"""
Interactivefeaturesextensionfor courses.ThismodelONLYcontainsinteractive- specif icfeaturesand gamif icationsettings.ItdoesNOTduplicatecoursetitledescriptionor othercor ecoursedata."""#Linktothemaincourse(OneToOneensuresoneextensionpercourse)course = models.OneToOneField('courses.Course'on_delete = models.CASCADErelated_name ='interactive_features'help_text = _("Thecoursethisinteractiveextensionbelongsto"))#Gamif icationsettingspoints_per_lesson = models.IntegerField(def ault =10help_text = _("Basepointsawardedfor completingalesson"))streak_bonus_multiplier = models.FloatField(def ault =1.5help_text = _("Multiplierfor pointswhenonastreak"))daily_goal_options = models.JSONField(def ault = listhelp_text = _("Availabledailygoaloptions(inminutes)"))#Featuretogglesbadges_enabled = models.BooleanField(def ault = Truehelp_text = _("Whetherbadgesareenabledfor thiscourse"))leaderboard_enabled = models.BooleanField(def ault = Truehelp_text = _("Whetherleaderboardsareenabledfor thiscourse"))achievements_enabled = models.BooleanField(def ault = Truehelp_text = _("Whetherachievementsareenabledfor thiscourse"))#Interactivesettingsdif ficulty_levels = models.JSONField(def ault = listhelp_text = _("Availabledif ficultylevelsfor thiscourse"))learning_paths = models.JSONField(def ault = listhelp_text = _("Predef inedlearningpathsfor dif ferentstudentgoals"))engagement_metrics = models.JSONField(def ault = dicthelp_text = _("Metricstotrackstudentengagement"))interactive_content = models.JSONField(def ault = listhelp_text = _("Course- specif icinteractivecontentconfigurations"))#Syncsettingssync_with_course = models.BooleanField(def ault = Truehelp_text = _("Whethertoautomaticallysyncwiththenor malcoursecontent"))last_synced_at = models.DateTimeField(null = Trueblank = True)#Metadatacreated_at = models.DateTimeField(auto_now_add = True)updated_at = models.DateTimeField(auto_now = True)created_by = models.For eignKey(Useron_delete = models.SET_NULLnull = Trueblank = Truerelated_name ='created_interactive_features')def save(self*args**kwargs):"""
Overridesavetoupdatethecourse'sflags"""
super().save(*args**kwargs)#Updatecourseflagsif self.course:self.course.has_interactive_content = Trueif self.course.primary_type =='STANDARD':self.course.primary_type ='INTERACTIVE'self.course.save(update_fields =['has_interactive_content''primary_type'])class Meta:
    """verbose_name = _("InteractiveCourseFeatures")verbose_name_plural = _("InteractiveCourseFeatures")app_label ="courses"#Keepincoursesappfor consolidationdef __str__(self):
        """return f"Interactivefeaturesfor{self.course.course_code}"class AIGeneratedContent(models.Model):"""
AI- generatedcontentextensionfor courses.ThismodelONLYcontainsAI- specif iccontentand generationsettings.ItdoesNOTduplicatecoursetitledescriptionor othercor ecoursedata."""#Linktothemaincourse(OneToOneensuresoneextensionpercourse)course = models.OneToOneField('courses.Course'on_delete = models.CASCADErelated_name ='ai_content'help_text = _("ThecoursethisAIcontentbelongsto"))#AIProviderInfor mationai_provider = models.CharField(max_length =100def ault ="Gemini"help_text = _("NameoftheAIproviderusedfor generation"))ai_model = models.CharField(max_length =100def ault ="gemini-2.0- flash"help_text = _("Specif icAImodelusedfor generation"))generation_prompt = models.TextField(help_text = _("PromptusedtogeneratetheAIcontent"))#Generationsettingsai_options = models.JSONField(def ault = dicthelp_text = _("Optionsusedfor AIgeneration"))dif ficulty_level = models.CharField(max_length =20choices =[("BEGINNER"_("Beginner"))("INTERMEDIATE"_("Intermediate"))("ADVANCED"_("Advanced"))("MIXED"_("MixedLevels"))]def ault ="MIXED"help_text = _("Dif ficultylevelfor AIgeneration"))learning_style = models.CharField(max_length =20choices =[("VISUAL"_("Visual"))("AUDITORY"_("Auditor y"))("READING"_("Reading/Writing"))("KINESTHETIC"_("Kinesthetic"))("BALANCED"_("Balanced"))]def ault ="BALANCED"help_text = _("Learningstylefor AIgeneration"))#GeneratedMaterials(AI- specif iccontentonly)weekly_schedule = models.JSONField(def ault = listhelp_text = _("AI- generatedweeklyscheduleand topics"))lesson_plans = models.JSONField(def ault = listhelp_text = _("AI- generateddetailedlessonplans"))assessment_methods = models.JSONField(def ault = listhelp_text = _("AI- generatedassessmenttypesand rubrics"))recommended_readings = models.JSONField(def ault = listhelp_text = _("AI- generatedreadingrecommendations"))sample_quizzes = models.JSONField(def ault = listhelp_text = _("AI- generatedsamplequizquestions"))project_ideas = models.JSONField(def ault = listhelp_text = _("AI- generatedprojectdescriptions"))teaching_tips = models.JSONField(def ault = listhelp_text = _("AI- generatedteachingstrategies"))skills_gained = models.JSONField(def ault = listhelp_text = _("AI- generatedlistofskillsgained"))additional_resources = models.JSONField(def ault = listhelp_text = _("AI- generatedadditionalresources"))#Generationmetadataraw_ai_response = models.JSONField(def ault = dicthelp_text = _("Rawresponsefrom theAImodel"))generated_at = models.DateTimeField(def ault = timezone.nowhelp_text = _("Whenthiscontentwasgenerated"))last_regenerated = models.DateTimeField(auto_now = Truehelp_text = _("Whenthiscontentwaslastregenerated"))generation_cost = models.DecimalField(max_digits =10decimal_places =4null = Trueblank = Truehelp_text = _("Costofgeneratingthiscontent(if available)"))#Qualitymetricscontent_quality_scor e = models.FloatField(null = Trueblank = Truehelp_text = _("AI- assessedqualityscor eofgeneratedcontent"))human_review_status = models.CharField(max_length =20choices =[('PENDING'_('PendingReview'))('APPROVED'_('Approved'))('NEEDS_REVISION'_('NeedsRevision'))('REJECTED'_('Rejected'))]def ault ='PENDING'help_text = _("HumanreviewstatusofAIcontent"))review_notes = models.TextField(blank = Truehelp_text = _("Notesfrom humanreview"))#Metadatacreated_at = models.DateTimeField(auto_now_add = True)updated_at = models.DateTimeField(auto_now = True)created_by = models.For eignKey(Useron_delete = models.SET_NULLnull = Trueblank = Truerelated_name ='created_ai_content')reviewed_by = models.For eignKey(Useron_delete = models.SET_NULLnull = Trueblank = Truerelated_name ='reviewed_ai_content')def save(self*args**kwargs):"""
Overridesavetoupdatethecourse'sflags"""
super().save(*args**kwargs)#Updatecourseflagsif self.course:self.course.has_ai_content = Trueif self.course.primary_type =='STANDARD':self.course.primary_type ='AI_GENERATED'elif self.course.has_interactive_content:self.course.primary_type ='HYBRID'self.course.save(update_fields =['has_ai_content''primary_type'])class Meta:
    """verbose_name = _("AIGeneratedContent")verbose_name_plural = _("AIGeneratedContent")app_label ="courses"#Keepincoursesappfor consolidationdef __str__(self):
        """return f"AIcontentfor{self.course.course_code}"def regenerate_content(selfnew_prompt = None):"""
TriggerregenerationofAIcontent.Thismethodcanbecalledtor egeneratecontentwithnewparameters."""
if new_prompt:self.generation_prompt = new_prompt#Markfor regeneration(actualAIcallwouldbehand ledbyservice)self.human_review_status ='PENDING'self.save()#TODO:TriggeractualAIregenerationviaservicereturn Trueclass CourseTypeHistor y(models.Model):"""
Trackchangesincoursetypesfor auditingand analytics."""
COURSE_TYPE_CHOICES =[('STANDARD''Stand ardCourse')('INTERACTIVE''InteractiveCourse')('AI_GENERATED''AI- GeneratedCourse')('HYBRID''HybridCourse')]course = models.For eignKey('courses.Course'on_delete = models.CASCADErelated_name ='type_histor y')previous_type = models.CharField(max_length =20choices = COURSE_TYPE_CHOICEShelp_text = _("Previouscoursetype"))new_type = models.CharField(max_length =20choices = COURSE_TYPE_CHOICEShelp_text = _("Newcoursetype"))changed_by = models.For eignKey(Useron_delete = models.SET_NULLnull = Trueblank = True)reason = models.TextField(blank = Truehelp_text = _("Reasonfor thetypechange"))changed_at = models.DateTimeField(auto_now_add = True)class Meta:
    """verbose_name = _("CourseTypeHistor y")verbose_name_plural = _("CourseTypeHistor y")app_label ="courses"or dering =['- changed_at']def __str__(self):
        """return f"{self.course.course_code}:{self.previous_type}→{self.new_type}"
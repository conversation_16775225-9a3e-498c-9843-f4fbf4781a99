/* Professor <PERSON> Enhanced Styles */

/* Smooth animations for better performance */
.professor-dashboard {
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --shadow-light: 0 4px 20px rgba(0, 0, 0, 0.08);
  --shadow-medium: 0 8px 32px rgba(0, 0, 0, 0.12);
  --shadow-heavy: 0 16px 48px rgba(0, 0, 0, 0.16);
  --blur-light: blur(10px);
  --blur-medium: blur(15px);
  --blur-heavy: blur(20px);
}

/* Stats card enhancements */
.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.stats-card:hover::after {
  left: 100%;
}

/* Pulse animation for loading states */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Shimmer effect for loading cards */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer-effect {
  background: linear-gradient(
    90deg,
    #f0f0f0 0px,
    #e0e0e0 40px,
    #f0f0f0 80px
  );
  background-size: 200px;
  animation: shimmer 1.5s infinite;
}

/* Dark mode shimmer */
.dark-mode .shimmer-effect {
  background: linear-gradient(
    90deg,
    #2a2a2a 0px,
    #3a3a3a 40px,
    #2a2a2a 80px
  );
}

/* Responsive improvements */
@media (max-width: 600px) {
  .professor-dashboard {
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.2);
  }
  
  /* Reduce animations on mobile for better performance */
  .stats-card:hover {
    transform: translateY(-2px) !important;
  }
  
  /* Optimize blur effects for mobile */
  .stats-card {
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .stats-card {
    border: 2px solid currentColor !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .stats-card,
  .stats-icon,
  .pulse-animation,
  .shimmer-effect {
    animation: none !important;
    transition: none !important;
  }
  
  .stats-card:hover {
    transform: none !important;
  }
}

/* Focus styles for accessibility */
.stats-card:focus-visible {
  outline: 2px solid #1976d2;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .professor-dashboard {
    background: white !important;
  }
  
  .stats-card {
    background: white !important;
    border: 1px solid #ccc !important;
    box-shadow: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }
}

/* Loading skeleton styles */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.dark-mode .skeleton {
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  background-size: 200% 100%;
}

/* Enhanced glass morphism */
.glass-enhanced {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark-mode .glass-enhanced {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Smooth scroll behavior */
.professor-dashboard {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
.professor-dashboard::-webkit-scrollbar {
  width: 8px;
}

.professor-dashboard::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.professor-dashboard::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.professor-dashboard::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

.dark-mode .professor-dashboard::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.dark-mode .professor-dashboard::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

.dark-mode .professor-dashboard::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

"""
API Response Utilities

This module provides standardized response formatting for API endpoints.
It ensures consistent response structure across the entire application.
"""
from rest_framework import status
from rest_framework.response import Response


def success_response(data=None, message=None, status_code=status.HTTP_200_OK, meta=None):
    """
    Create a standardized success response.
    
    Args:
        data: The response data
        message: Optional success message
        status_code: HTTP status code (default: 200)
        meta: Optional metadata (pagination info, etc.)
    
    Returns:
        Response: DRF Response object with standardized format
    """
    response_data = {"status": "success"}
    
    if data is not None:
        response_data["data"] = data
    
    if message:
        response_data["message"] = message
    
    if meta:
        response_data["meta"] = meta
    
    return Response(response_data, status=status_code)


def error_response(message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
    """
    Create a standardized error response.
    
    Args:
        message: Error message
        errors: Optional field-specific validation errors
        status_code: HTTP status code (default: 400)
    
    Returns:
        Response: DRF Response object with standardized format
    """
    response_data = {
        "status": "error",
        "message": message
    }
    
    if errors:
        response_data["errors"] = errors
    
    return Response(response_data, status=status_code)


def not_found_response(message="Resource not found"):
    """
    Create a standardized 404 not found response.
    
    Args:
        message: Error message (default: "Resource not found")
    
    Returns:
        Response: DRF Response object with standardized format
    """
    return error_response(message, status_code=status.HTTP_404_NOT_FOUND)


def validation_error_response(errors, message="Validation error"):
    """
    Create a standardized validation error response.
    
    Args:
        errors: Field-specific validation errors
        message: Error message (default: "Validation error")
    
    Returns:
        Response: DRF Response object with standardized format
    """
    return error_response(message, errors=errors, status_code=status.HTTP_400_BAD_REQUEST)


def unauthorized_response(message="Authentication required"):
    """
    Create a standardized unauthorized response.
    
    Args:
        message: Error message (default: "Authentication required")
    
    Returns:
        Response: DRF Response object with standardized format
    """
    return error_response(message, status_code=status.HTTP_401_UNAUTHORIZED)


def forbidden_response(message="Permission denied"):
    """
    Create a standardized forbidden response.
    
    Args:
        message: Error message (default: "Permission denied")
    
    Returns:
        Response: DRF Response object with standardized format
    """
    return error_response(message, status_code=status.HTTP_403_FORBIDDEN)


def server_error_response(message="Internal server error"):
    """
    Create a standardized server error response.
    
    Args:
        message: Error message (default: "Internal server error")
    
    Returns:
        Response: DRF Response object with standardized format
    """
    return error_response(message, status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)

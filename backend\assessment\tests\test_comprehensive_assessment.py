"""
Comprehensive tests for assessment system.
"""

import pytest
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.test import APITestCase
from unittest.mock import patch, Mock
import json
from datetime import datetime, timedelta
from django.utils import timezone

from assessment.models import (
    Assessment, AssessmentQuestion, AssessmentResponse,
    StudentLevel, SkillAssessment
)
from courses.models import Course

User = get_user_model()


@pytest.mark.unit
class TestAssessmentModels:
    """Test assessment model functionality."""
    
    def test_assessment_creation(self, test_user, sample_assessment_data):
        """Test assessment creation."""
        assessment = Assessment.objects.create(
            student=test_user,
            **sample_assessment_data
        )
        
        assert assessment.title == sample_assessment_data['title']
        assert assessment.student == test_user
        assert assessment.created_at is not None
        assert not assessment.completed
    
    def test_assessment_question_creation(self, test_user, sample_question_data):
        """Test assessment question creation."""
        assessment = Assessment.objects.create(
            student=test_user,
            title='Test Assessment',
            assessment_type='QUIZ'
        )
        
        question = AssessmentQuestion.objects.create(
            assessment=assessment,
            **sample_question_data
        )
        
        assert question.question_text == sample_question_data['question_text']
        assert question.points == sample_question_data['points']
        assert question.assessment == assessment
    
    def test_assessment_response_creation(self, test_user):
        """Test assessment response creation."""
        assessment = Assessment.objects.create(
            student=test_user,
            title='Test Assessment',
            assessment_type='QUIZ'
        )
        
        question = AssessmentQuestion.objects.create(
            assessment=assessment,
            question_text='Test question',
            question_type='MULTIPLE_CHOICE',
            points=10
        )
        
        response = AssessmentResponse.objects.create(
            assessment=assessment,
            question=question,
            student_answer='Test answer',
            is_correct=True,
            points_earned=10
        )
        
        assert response.student_answer == 'Test answer'
        assert response.is_correct
        assert response.points_earned == 10
    
    def test_assessment_score_calculation(self, test_user):
        """Test assessment score calculation."""
        assessment = Assessment.objects.create(
            student=test_user,
            title='Test Assessment',
            assessment_type='QUIZ'
        )
        
        # Create questions
        q1 = AssessmentQuestion.objects.create(
            assessment=assessment,
            question_text='Question 1',
            question_type='MULTIPLE_CHOICE',
            points=10
        )
        
        q2 = AssessmentQuestion.objects.create(
            assessment=assessment,
            question_text='Question 2',
            question_type='MULTIPLE_CHOICE',
            points=15
        )
        
        # Create responses
        AssessmentResponse.objects.create(
            assessment=assessment,
            question=q1,
            student_answer='Correct',
            is_correct=True,
            points_earned=10
        )
        
        AssessmentResponse.objects.create(
            assessment=assessment,
            question=q2,
            student_answer='Wrong',
            is_correct=False,
            points_earned=0
        )
        
        # Calculate score
        assessment.calculate_score()
        assessment.refresh_from_db()
        
        assert assessment.score == 40.0  # 10/25 * 100
        assert assessment.points_earned == 10
        assert assessment.total_points == 25


@pytest.mark.api
class TestAssessmentAPI:
    """Test assessment API endpoints."""
    
    def test_create_assessment(self, authenticated_client, test_student, sample_assessment_data):
        """Test creating an assessment via API."""
        url = reverse('assessment:assessment-list')
        response = authenticated_client.post(url, sample_assessment_data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert Assessment.objects.filter(title=sample_assessment_data['title']).exists()
    
    def test_list_assessments(self, authenticated_client, test_student):
        """Test listing assessments."""
        # Create test assessments
        Assessment.objects.create(
            student=test_student,
            title='Assessment 1',
            assessment_type='QUIZ'
        )
        Assessment.objects.create(
            student=test_student,
            title='Assessment 2',
            assessment_type='EXAM'
        )
        
        url = reverse('assessment:assessment-list')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 2
    
    def test_get_assessment_detail(self, authenticated_client, test_student):
        """Test getting assessment details."""
        assessment = Assessment.objects.create(
            student=test_student,
            title='Test Assessment',
            assessment_type='QUIZ'
        )
        
        url = reverse('assessment:assessment-detail', kwargs={'pk': assessment.pk})
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['title'] == 'Test Assessment'
    
    def test_submit_assessment(self, authenticated_client, test_student):
        """Test submitting an assessment."""
        assessment = Assessment.objects.create(
            student=test_student,
            title='Test Assessment',
            assessment_type='QUIZ'
        )
        
        question = AssessmentQuestion.objects.create(
            assessment=assessment,
            question_text='What is 2 + 2?',
            question_type='MULTIPLE_CHOICE',
            points=10,
            correct_answer='4'
        )
        
        submission_data = {
            'responses': [
                {
                    'question_id': question.id,
                    'answer': '4'
                }
            ]
        }
        
        url = reverse('assessment:assessment-submit', kwargs={'pk': assessment.pk})
        response = authenticated_client.post(url, submission_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assessment.refresh_from_db()
        assert assessment.completed
    
    def test_assessment_unauthorized_access(self, api_client):
        """Test unauthorized access to assessments."""
        url = reverse('assessment:assessment-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


@pytest.mark.integration
class TestAssessmentIntegration:
    """Integration tests for assessment system."""
    
    def test_complete_assessment_flow(self, authenticated_client, test_student):
        """Test complete assessment workflow."""
        # 1. Create assessment
        assessment_data = {
            'title': 'Integration Test Assessment',
            'description': 'Test assessment for integration',
            'assessment_type': 'QUIZ',
            'time_limit': 60,
            'max_attempts': 3
        }
        
        create_url = reverse('assessment:assessment-list')
        create_response = authenticated_client.post(
            create_url, assessment_data, format='json'
        )
        assert create_response.status_code == status.HTTP_201_CREATED
        
        assessment_id = create_response.data['id']
        
        # 2. Add questions
        question_data = {
            'question_text': 'What is the capital of France?',
            'question_type': 'MULTIPLE_CHOICE',
            'points': 10,
            'options': [
                {'text': 'London', 'is_correct': False},
                {'text': 'Paris', 'is_correct': True},
                {'text': 'Berlin', 'is_correct': False},
                {'text': 'Madrid', 'is_correct': False}
            ]
        }
        
        question_url = reverse('assessment:question-list')
        question_response = authenticated_client.post(
            question_url, 
            {**question_data, 'assessment': assessment_id}, 
            format='json'
        )
        assert question_response.status_code == status.HTTP_201_CREATED
        
        # 3. Start assessment
        start_url = reverse('assessment:assessment-start', kwargs={'pk': assessment_id})
        start_response = authenticated_client.post(start_url)
        assert start_response.status_code == status.HTTP_200_OK
        
        # 4. Submit answers
        submission_data = {
            'responses': [
                {
                    'question_id': question_response.data['id'],
                    'answer': 'Paris'
                }
            ]
        }
        
        submit_url = reverse('assessment:assessment-submit', kwargs={'pk': assessment_id})
        submit_response = authenticated_client.post(
            submit_url, submission_data, format='json'
        )
        assert submit_response.status_code == status.HTTP_200_OK
        
        # 5. Get results
        results_url = reverse('assessment:assessment-results', kwargs={'pk': assessment_id})
        results_response = authenticated_client.get(results_url)
        assert results_response.status_code == status.HTTP_200_OK
        assert results_response.data['score'] == 100.0


@pytest.mark.ai
class TestAIAssessmentFeatures:
    """Test AI-powered assessment features."""
    
    @patch('assessment.services.AIService')
    def test_ai_question_generation(self, mock_ai_service, authenticated_client, test_student):
        """Test AI-powered question generation."""
        mock_ai_service.return_value.generate_questions.return_value = {
            'questions': [
                {
                    'question_text': 'AI generated question',
                    'question_type': 'MULTIPLE_CHOICE',
                    'options': ['A', 'B', 'C', 'D'],
                    'correct_answer': 'A'
                }
            ]
        }
        
        data = {
            'topic': 'Mathematics',
            'difficulty': 'INTERMEDIATE',
            'count': 5
        }
        
        url = reverse('assessment:ai-generate-questions')
        response = authenticated_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'questions' in response.data

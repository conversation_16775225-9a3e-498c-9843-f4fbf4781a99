/**
 * Advanced Personalization Types
 * 
 * This file contains all type definitions for the enhanced personalization system
 */

// ==========================================
// EMOTIONAL INTELLIGENCE TYPES
// ==========================================

export interface EmotionalLearningState {
  current_mood: 'motivated' | 'frustrated' | 'confident' | 'anxious' | 'curious' | 'neutral';
  stress_level: number; // 1-10 scale
  confidence_level: number; // 1-10 scale
  engagement_level: number; // 1-10 scale
  learning_readiness: number; // 1-10 scale
  detected_at: string; // ISO timestamp
}

export interface TypingPattern {
  average_typing_speed: number; // characters per minute
  pause_frequency: number; // pauses per minute
  backspace_frequency: number; // corrections per minute
  typing_rhythm_variance: number; // consistency measure
  session_duration: number; // minutes
}

export interface InteractionMetrics {
  response_time: number; // seconds to respond
  error_rate: number; // percentage of incorrect responses
  help_seeking_frequency: number; // requests for help per session
  task_completion_rate: number; // percentage of completed tasks
  session_engagement_score: number; // calculated engagement metric
}

// ==========================================
// VOICE PERSONALIZATION TYPES
// ==========================================

export interface VoiceLearningPreferences {
  preferred_speech_rate: number; // words per minute (80-200)
  preferred_voice_type: 'male' | 'female' | 'neutral';
  accent_preference: 'us' | 'uk' | 'au' | 'neutral';
  audio_learning_style: 'conversational' | 'lecture' | 'interactive';
  voice_enabled: boolean;
  volume_preference: number; // 0-1 scale
}

export interface VoiceCommand {
  command: string;
  action: () => void;
  description: string;
  enabled: boolean;
}

// ==========================================
// VISUAL PERSONALIZATION TYPES
// ==========================================

export interface VisualLearningPreferences {
  color_scheme: 'high_contrast' | 'warm' | 'cool' | 'monochrome' | 'dark' | 'light';
  diagram_complexity: 'simple' | 'detailed' | 'interactive';
  animation_preference: 'static' | 'subtle' | 'dynamic' | 'none';
  layout_preference: 'linear' | 'hierarchical' | 'network' | 'grid';
  font_size_multiplier: number; // 0.8-2.0
  contrast_level: number; // 1-5 scale
}

export interface ThemeCustomization {
  primary_color: string;
  secondary_color: string;
  background_pattern: 'none' | 'subtle' | 'geometric' | 'organic';
  spacing_preference: 'compact' | 'normal' | 'spacious';
  border_radius: 'sharp' | 'rounded' | 'circular';
}

// ==========================================
// MICRO-LEARNING TYPES
// ==========================================

export interface MicroLearningPreferences {
  optimal_chunk_duration: number; // minutes (5-30)
  attention_span_pattern: AttentionSpanCurve;
  break_frequency: number; // minutes between breaks
  preferred_content_density: 'light' | 'medium' | 'dense';
  chunking_strategy: 'time_based' | 'concept_based' | 'adaptive';
}

export interface AttentionSpanCurve {
  peak_attention_minutes: number;
  decline_rate: number; // percentage per minute
  recovery_time: number; // minutes needed for full recovery
  time_of_day_variance: TimeOfDayPattern[];
}

export interface TimeOfDayPattern {
  hour: number; // 0-23
  attention_multiplier: number; // 0.5-1.5
  energy_level: number; // 1-10
}

// ==========================================
// ADAPTIVE DIFFICULTY TYPES
// ==========================================

export interface DifficultyAdaptationSettings {
  adaptation_sensitivity: number; // 0.1-1.0 (how quickly to adapt)
  performance_window: number; // number of recent interactions to consider
  difficulty_bounds: {
    minimum: number; // 1-10
    maximum: number; // 1-10
    current: number; // 1-10
  };
  adaptation_triggers: AdaptationTrigger[];
}

export interface AdaptationTrigger {
  metric: 'accuracy' | 'speed' | 'frustration' | 'engagement';
  threshold: number;
  action: 'increase_difficulty' | 'decrease_difficulty' | 'maintain' | 'provide_hint';
  weight: number; // importance of this trigger
}

export interface PerformanceMetrics {
  accuracy: number; // 0-1
  speed: number; // 0-1 (relative to expected)
  consistency: number; // 0-1
  improvement_rate: number; // change over time
  frustration_indicators: number; // 0-1
  engagement_indicators: number; // 0-1
}

// ==========================================
// CONTEXTUAL LEARNING TYPES
// ==========================================

export interface LearningContext {
  device_type: 'mobile' | 'tablet' | 'desktop' | 'smart_tv';
  screen_size: {
    width: number;
    height: number;
    pixel_density: number;
  };
  environment: 'quiet' | 'noisy' | 'public' | 'private' | 'moving';
  available_time: number; // minutes
  interruption_likelihood: number; // 0-1
  network_quality: 'high' | 'medium' | 'low' | 'offline';
  time_of_day: number; // 0-23
  day_of_week: number; // 0-6 (Sunday = 0)
}

export interface ContextualAdaptation {
  content_format: 'full' | 'condensed' | 'bullet_points' | 'visual_only';
  interaction_mode: 'touch' | 'mouse' | 'keyboard' | 'voice' | 'gesture';
  media_preferences: MediaPreference[];
  notification_settings: NotificationSettings;
}

export interface MediaPreference {
  type: 'text' | 'image' | 'video' | 'audio' | 'interactive';
  priority: number; // 1-5
  bandwidth_requirement: 'low' | 'medium' | 'high';
}

// ==========================================
// GAMIFICATION TYPES
// ==========================================

export interface GamificationProfile {
  player_type: 'achiever' | 'explorer' | 'socializer' | 'killer' | 'philanthropist';
  motivation_drivers: MotivationDriver[];
  preferred_rewards: RewardType[];
  competition_comfort: number; // 1-10
  collaboration_preference: number; // 1-10
  challenge_seeking: number; // 1-10
  progress_visibility: 'public' | 'private' | 'friends_only';
}

export interface MotivationDriver {
  type: 'achievement' | 'social' | 'mastery' | 'autonomy' | 'purpose' | 'competition';
  strength: number; // 1-10
  context_dependent: boolean;
}

export interface RewardType {
  category: 'badges' | 'points' | 'leaderboards' | 'certificates' | 'unlockables' | 'social_recognition';
  preference_level: number; // 1-10
  frequency_preference: 'immediate' | 'milestone' | 'session_end' | 'weekly';
}

// ==========================================
// ACCESSIBILITY TYPES
// ==========================================

export interface AccessibilityProfile {
  visual_impairment: 'none' | 'low_vision' | 'color_blind' | 'blind';
  hearing_impairment: 'none' | 'hard_of_hearing' | 'deaf';
  motor_impairment: 'none' | 'limited_mobility' | 'severe_mobility';
  cognitive_considerations: CognitiveConsideration[];
  assistive_technologies: AssistiveTechnology[];
  accessibility_preferences: AccessibilityPreferences;
}

export interface CognitiveConsideration {
  type: 'adhd' | 'dyslexia' | 'autism' | 'memory_issues' | 'processing_speed';
  severity: 'mild' | 'moderate' | 'severe';
  accommodations_needed: string[];
}

export interface AssistiveTechnology {
  name: string;
  type: 'screen_reader' | 'voice_control' | 'eye_tracking' | 'switch_control' | 'magnifier';
  compatibility_requirements: string[];
}

export interface AccessibilityPreferences {
  high_contrast: boolean;
  large_text: boolean;
  reduced_motion: boolean;
  audio_descriptions: boolean;
  captions_enabled: boolean;
  keyboard_navigation_only: boolean;
}

// ==========================================
// NOTIFICATION TYPES
// ==========================================

export interface NotificationSettings {
  enabled: boolean;
  delivery_methods: NotificationMethod[];
  frequency_limits: FrequencyLimit[];
  quiet_hours: QuietHours;
  priority_filtering: boolean;
}

export interface NotificationMethod {
  type: 'push' | 'email' | 'sms' | 'in_app' | 'voice';
  enabled: boolean;
  priority_threshold: number; // 1-5
}

export interface FrequencyLimit {
  method: string;
  max_per_hour: number;
  max_per_day: number;
  cooldown_minutes: number;
}

export interface QuietHours {
  enabled: boolean;
  start_time: string; // HH:MM format
  end_time: string; // HH:MM format
  timezone: string;
  exceptions: string[]; // emergency notification types
}

// ==========================================
// COMPREHENSIVE PROFILE TYPE
// ==========================================

export interface AdvancedPersonalizationProfile {
  user_id: number;
  emotional_state: EmotionalLearningState;
  voice_preferences: VoiceLearningPreferences;
  visual_preferences: VisualLearningPreferences;
  micro_learning_preferences: MicroLearningPreferences;
  difficulty_adaptation: DifficultyAdaptationSettings;
  gamification_profile: GamificationProfile;
  accessibility_profile: AccessibilityProfile;
  notification_settings: NotificationSettings;
  learning_context: LearningContext;
  last_updated: string;
  version: string; // for schema versioning
}

// ==========================================
// API RESPONSE TYPES
// ==========================================

export interface PersonalizationResponse<T = any> {
  data: T;
  metadata: {
    personalization_applied: boolean;
    adaptations_made: string[];
    confidence_score: number;
    processing_time: number;
    fallback_used: boolean;
  };
  status: 'success' | 'partial' | 'fallback' | 'error';
}

export interface AdaptationResult {
  original_content: string;
  adapted_content: string;
  adaptations_applied: AdaptationType[];
  confidence_score: number;
  user_feedback_requested: boolean;
}

export interface AdaptationType {
  category: 'emotional' | 'visual' | 'audio' | 'difficulty' | 'chunking' | 'accessibility';
  specific_adaptation: string;
  reason: string;
  impact_level: 'low' | 'medium' | 'high';
}

// ==========================================
// UTILITY TYPES
// ==========================================

export type PersonalizationEngine = 
  | 'emotional_intelligence'
  | 'voice_personalization'
  | 'visual_adaptation'
  | 'micro_learning'
  | 'adaptive_difficulty'
  | 'contextual_adaptation'
  | 'gamification'
  | 'accessibility';

export interface EngineConfiguration {
  engine: PersonalizationEngine;
  enabled: boolean;
  priority: number; // 1-10
  fallback_behavior: 'skip' | 'use_default' | 'prompt_user';
  performance_threshold: number; // minimum acceptable performance
}

export interface PersonalizationMetrics {
  engagement_improvement: number; // percentage
  learning_efficiency: number; // percentage
  user_satisfaction: number; // 1-10 scale
  adaptation_accuracy: number; // percentage
  system_performance_impact: number; // milliseconds
}

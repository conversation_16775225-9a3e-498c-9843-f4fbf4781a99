"""
Real Blockchain Integration Service

This module provides actual blockchain interaction capabilities using Web3.py
for Ethereum-compatible networks and other blockchain SDKs.
"""

import logging
import json
import time
from decimal import Decimal
from typing import Dict, Optional, Any, Tuple
from dataclasses import dataclass

from django.conf import settings
from django.utils import timezone
from web3 import Web3
# from web3.middleware import geth_poa_middleware  # Disabled due to version compatibility
try:
    from web3.middleware import geth_poa_middleware
except ImportError:
    # Fallback for newer web3.py versions
    try:
        from web3.middleware.poa import geth_poa_middleware
    except ImportError:
        # Disable PoA middleware if not available
        geth_poa_middleware = None
from eth_account import Account
import ipfshttpclient
import requests

from .models import (
    BlockchainNetwork, 
    BlockchainCredential, 
    NFTAchievement, 
    SmartContract,
    WalletAddress
)

logger = logging.getLogger(__name__)


@dataclass
class BlockchainConfig:
    """Configuration for blockchain connections"""
    private_key: str
    gas_limit: int = 500000
    gas_price_gwei: int = 20
    confirmation_blocks: int = 1
    timeout: int = 300


class Web3ConnectionManager:
    """Manages Web3 connections to different blockchain networks"""
    
    def __init__(self):
        self.connections = {}
        self.contracts = {}
    
    def get_web3_connection(self, network: BlockchainNetwork) -> Web3:
        """Get or create Web3 connection for a network"""
        if network.id not in self.connections:
            w3 = Web3(Web3.HTTPProvider(network.rpc_url))
            
            # Add PoA middleware for networks that need it
            if network.network_type in ['POLYGON', 'BINANCE'] and geth_poa_middleware:
                w3.middleware_onion.inject(geth_poa_middleware, layer=0)
            
            # Validate connection
            if not w3.is_connected():
                raise ConnectionError(f"Failed to connect to {network.name}")
            
            self.connections[network.id] = w3
            logger.info(f"Connected to {network.name} (Chain ID: {network.chain_id})")
        
        return self.connections[network.id]
    
    def get_contract(self, network: BlockchainNetwork, contract_type: str):
        """Get smart contract instance"""
        key = f"{network.id}_{contract_type}"
        
        if key not in self.contracts:
            w3 = self.get_web3_connection(network)
            
            # Get contract from database
            try:
                smart_contract = SmartContract.objects.get(
                    blockchain_network=network,
                    contract_type=contract_type.upper(),
                    is_active=True
                )
                
                contract = w3.eth.contract(
                    address=smart_contract.contract_address,
                    abi=smart_contract.abi
                )
                
                self.contracts[key] = contract
                logger.info(f"Loaded {contract_type} contract for {network.name}")
                
            except SmartContract.DoesNotExist:
                logger.error(f"No {contract_type} contract found for {network.name}")
                return None
        
        return self.contracts[key]


class IPFSManager:
    """Manages IPFS operations for metadata storage"""
    
    def __init__(self, ipfs_url: str = '/ip4/127.0.0.1/tcp/5001'):
        try:
            self.client = ipfshttpclient.connect(ipfs_url)
            logger.info("Connected to IPFS")
        except Exception as e:
            # Only log at debug level to reduce noise
            logger.debug(f"Could not connect to IPFS: {e}")
            self.client = None
    
    def upload_metadata(self, metadata: Dict) -> Optional[str]:
        """Upload metadata to IPFS and return hash"""
        if not self.client:
            logger.warning("IPFS not available, using fallback storage")
            return None
        
        try:
            # Convert metadata to JSON
            json_data = json.dumps(metadata, indent=2)
            
            # Upload to IPFS
            result = self.client.add_json(metadata)
            ipfs_hash = result
            
            logger.info(f"Uploaded metadata to IPFS: {ipfs_hash}")
            return ipfs_hash
            
        except Exception as e:
            logger.error(f"Failed to upload to IPFS: {e}")
            return None
    
    def get_metadata(self, ipfs_hash: str) -> Optional[Dict]:
        """Retrieve metadata from IPFS"""
        if not self.client:
            return None
        
        try:
            metadata = self.client.get_json(ipfs_hash)
            return metadata
        except Exception as e:
            logger.error(f"Failed to retrieve from IPFS: {e}")
            return None


class RealBlockchainCredentialService:
    """Enhanced blockchain credential service with real blockchain integration"""
    
    def __init__(self, config: Optional[BlockchainConfig] = None):
        self.config = config or self._get_default_config()
        self.web3_manager = Web3ConnectionManager()
        self.ipfs_manager = IPFSManager()
    
    def _get_default_config(self) -> BlockchainConfig:
        """Get default blockchain configuration from settings"""
        return BlockchainConfig(
            private_key=getattr(settings, 'BLOCKCHAIN_PRIVATE_KEY', ''),
            gas_limit=getattr(settings, 'BLOCKCHAIN_GAS_LIMIT', 500000),
            gas_price_gwei=getattr(settings, 'BLOCKCHAIN_GAS_PRICE_GWEI', 20),
            confirmation_blocks=getattr(settings, 'BLOCKCHAIN_CONFIRMATION_BLOCKS', 1),
            timeout=getattr(settings, 'BLOCKCHAIN_TIMEOUT', 300)
        )
    
    def mint_credential_on_blockchain(self, credential: BlockchainCredential) -> Dict[str, Any]:
        """Mint a credential as an NFT on the blockchain"""
        try:
            logger.info(f"Starting blockchain minting for credential {credential.id}")
            
            # Update status
            credential.status = 'MINTING'
            credential.save(update_fields=['status'])
            
            # Get network and Web3 connection
            network = credential.blockchain_network
            w3 = self.web3_manager.get_web3_connection(network)
            
            # Get credential contract
            contract = self.web3_manager.get_contract(network, 'CREDENTIAL')
            if not contract:
                raise Exception("Credential contract not found")
            
            # Prepare metadata
            metadata = credential.get_metadata_for_blockchain()
            
            # Upload metadata to IPFS
            ipfs_hash = self.ipfs_manager.upload_metadata(metadata)
            if ipfs_hash:
                credential.ipfs_hash = ipfs_hash
                metadata_uri = f"ipfs://{ipfs_hash}"
            else:
                # Fallback to centralized storage
                metadata_uri = f"{settings.SITE_URL}/api/v1/blockchain/credentials/{credential.credential_id}/metadata"
            
            # Get student's wallet address
            student_wallet = self._get_student_wallet(credential.student, network)
            if not student_wallet:
                # Create custodial wallet for student
                student_wallet = self._create_custodial_wallet(credential.student, network)
            
            # Prepare account
            account = Account.from_key(self.config.private_key)
            
            # Build transaction
            nonce = w3.eth.get_transaction_count(account.address)
            gas_price = w3.to_wei(self.config.gas_price_gwei, 'gwei')
            
            # Generate unique token ID
            token_id = int(str(credential.id) + str(int(time.time()))[-6:])
            
            transaction = contract.functions.mintCredential(
                student_wallet.address,
                token_id,
                metadata_uri
            ).build_transaction({
                'chainId': network.chain_id,
                'gas': self.config.gas_limit,
                'gasPrice': gas_price,
                'nonce': nonce,
            })
            
            # Sign and send transaction
            signed_txn = w3.eth.account.sign_transaction(transaction, self.config.private_key)
            tx_hash = w3.eth.send_raw_transaction(signed_txn.rawTransaction)
            
            logger.info(f"Transaction sent: {tx_hash.hex()}")
            
            # Wait for confirmation
            receipt = w3.eth.wait_for_transaction_receipt(
                tx_hash, 
                timeout=self.config.timeout
            )
            
            if receipt.status == 1:
                # Transaction successful
                credential.transaction_hash = tx_hash.hex()
                credential.token_id = str(token_id)
                credential.smart_contract_address = contract.address
                credential.status = 'ACTIVE'
                credential.save(update_fields=[
                    'transaction_hash', 'token_id', 'smart_contract_address', 
                    'status', 'ipfs_hash'
                ])
                
                logger.info(f"Credential {credential.id} successfully minted")
                
                return {
                    'success': True,
                    'transaction_hash': tx_hash.hex(),
                    'token_id': token_id,
                    'gas_used': receipt.gasUsed,
                    'block_number': receipt.blockNumber
                }
            else:
                # Transaction failed
                credential.status = 'FAILED'
                credential.save(update_fields=['status'])
                
                return {
                    'success': False,
                    'error': 'Transaction failed',
                    'transaction_hash': tx_hash.hex()
                }
                
        except Exception as e:
            logger.error(f"Failed to mint credential {credential.id}: {e}")
            credential.status = 'FAILED'
            credential.save(update_fields=['status'])
            
            return {
                'success': False,
                'error': str(e)
            }
    
    def verify_credential_on_blockchain(self, credential: BlockchainCredential) -> Dict[str, Any]:
        """Verify credential authenticity on blockchain"""
        try:
            if not credential.transaction_hash:
                return {'verified': False, 'reason': 'No transaction hash'}
            
            network = credential.blockchain_network
            w3 = self.web3_manager.get_web3_connection(network)
            
            # Get transaction receipt
            receipt = w3.eth.get_transaction_receipt(credential.transaction_hash)
            
            if not receipt:
                return {'verified': False, 'reason': 'Transaction not found'}
            
            # Verify transaction status
            if receipt.status != 1:
                return {'verified': False, 'reason': 'Transaction failed'}
            
            # Get contract and verify token ownership
            contract = self.web3_manager.get_contract(network, 'CREDENTIAL')
            if contract and credential.token_id:
                try:
                    # Check if token exists and get owner
                    owner = contract.functions.ownerOf(int(credential.token_id)).call()
                    student_wallet = self._get_student_wallet(credential.student, network)
                    
                    if student_wallet and owner.lower() == student_wallet.address.lower():
                        ownership_verified = True
                    else:
                        ownership_verified = False
                        
                except Exception:
                    ownership_verified = False
            else:
                ownership_verified = None
            
            return {
                'verified': True,
                'transaction_hash': credential.transaction_hash,
                'block_number': receipt.blockNumber,
                'gas_used': receipt.gasUsed,
                'network': network.name,
                'ownership_verified': ownership_verified,
                'verification_time': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error verifying credential {credential.id}: {e}")
            return {'verified': False, 'reason': str(e)}
    
    def mint_nft_achievement(self, achievement: NFTAchievement) -> Dict[str, Any]:
        """Mint an NFT achievement on the blockchain"""
        try:
            logger.info(f"Starting NFT minting for achievement {achievement.id}")
            
            network = achievement.blockchain_network
            w3 = self.web3_manager.get_web3_connection(network)
            
            # Get NFT contract
            contract = self.web3_manager.get_contract(network, 'NFT')
            if not contract:
                raise Exception("NFT contract not found")
            
            # Prepare metadata
            metadata = achievement.get_nft_metadata()
            
            # Upload metadata to IPFS
            ipfs_hash = self.ipfs_manager.upload_metadata(metadata)
            if ipfs_hash:
                achievement.ipfs_metadata_hash = ipfs_hash
                metadata_uri = f"ipfs://{ipfs_hash}"
            else:
                metadata_uri = f"{settings.SITE_URL}/api/v1/blockchain/nft-achievements/{achievement.achievement_id}/metadata"
            
            # Get student's wallet
            student_wallet = self._get_student_wallet(achievement.student, network)
            if not student_wallet:
                student_wallet = self._create_custodial_wallet(achievement.student, network)
            
            # Prepare transaction
            account = Account.from_key(self.config.private_key)
            nonce = w3.eth.get_transaction_count(account.address)
            gas_price = w3.to_wei(self.config.gas_price_gwei, 'gwei')
            
            # Generate unique token ID
            token_id = int(str(achievement.id) + str(int(time.time()))[-6:])
            
            transaction = contract.functions.mintAchievement(
                student_wallet.address,
                token_id,
                metadata_uri
            ).build_transaction({
                'chainId': network.chain_id,
                'gas': self.config.gas_limit,
                'gasPrice': gas_price,
                'nonce': nonce,
            })
            
            # Sign and send
            signed_txn = w3.eth.account.sign_transaction(transaction, self.config.private_key)
            tx_hash = w3.eth.send_raw_transaction(signed_txn.rawTransaction)
            
            # Wait for confirmation
            receipt = w3.eth.wait_for_transaction_receipt(tx_hash, timeout=self.config.timeout)
            
            if receipt.status == 1:
                achievement.transaction_hash = tx_hash.hex()
                achievement.token_id = str(token_id)
                achievement.nft_contract_address = contract.address
                achievement.is_minted = True
                achievement.save(update_fields=[
                    'transaction_hash', 'token_id', 'nft_contract_address', 
                    'is_minted', 'ipfs_metadata_hash'
                ])
                
                # Set OpenSea URL (for supported networks)
                if network.network_type in ['ETHEREUM', 'POLYGON']:
                    opensea_base = "https://testnets.opensea.io" if network.is_testnet else "https://opensea.io"
                    achievement.opensea_url = f"{opensea_base}/assets/{contract.address}/{token_id}"
                    achievement.save(update_fields=['opensea_url'])
                
                logger.info(f"NFT achievement {achievement.id} successfully minted")
                
                return {
                    'success': True,
                    'transaction_hash': tx_hash.hex(),
                    'token_id': token_id,
                    'opensea_url': achievement.opensea_url
                }
            else:
                return {'success': False, 'error': 'Transaction failed'}
                
        except Exception as e:
            logger.error(f"Failed to mint NFT achievement {achievement.id}: {e}")
            return {'success': False, 'error': str(e)}
    
    def _get_student_wallet(self, student, network: BlockchainNetwork) -> Optional[WalletAddress]:
        """Get student's primary wallet for the network"""
        return WalletAddress.objects.filter(
            user=student,
            blockchain_network=network,
            is_verified=True,
            is_active=True,
            is_primary=True
        ).first()
    
    def _create_custodial_wallet(self, student, network: BlockchainNetwork) -> WalletAddress:
        """Create a custodial wallet for a student"""
        # Generate new wallet
        account = Account.create()
        
        wallet = WalletAddress.objects.create(
            user=student,
            blockchain_network=network,
            address=account.address,
            wallet_type='CUSTODIAL',
            label=f'University Custodial Wallet - {network.name}',
            is_verified=True,
            is_primary=True,
            verified_at=timezone.now()
        )
        
        logger.info(f"Created custodial wallet for {student.username} on {network.name}")
        return wallet
    
    def get_network_status(self, network: BlockchainNetwork) -> Dict[str, Any]:
        """Get current network status and gas prices"""
        try:
            w3 = self.web3_manager.get_web3_connection(network)
            
            latest_block = w3.eth.get_block('latest')
            gas_price = w3.eth.gas_price
            
            return {
                'connected': True,
                'latest_block': latest_block.number,
                'gas_price_gwei': w3.from_wei(gas_price, 'gwei'),
                'network_id': w3.net.version,
                'chain_id': network.chain_id
            }
            
        except Exception as e:
            logger.error(f"Failed to get network status for {network.name}: {e}")
            return {
                'connected': False,
                'error': str(e)
            }
    
    def estimate_gas_cost(self, network: BlockchainNetwork, operation: str) -> Dict[str, Any]:
        """Estimate gas cost for different operations"""
        try:
            w3 = self.web3_manager.get_web3_connection(network)
            gas_price = w3.eth.gas_price
            
            # Estimated gas limits for different operations
            gas_estimates = {
                'mint_credential': 250000,
                'mint_nft': 200000,
                'transfer': 21000,
                'verify': 50000
            }
            
            estimated_gas = gas_estimates.get(operation, 100000)
            cost_wei = estimated_gas * gas_price
            cost_eth = w3.from_wei(cost_wei, 'ether')
            
            return {
                'operation': operation,
                'estimated_gas': estimated_gas,
                'gas_price_gwei': w3.from_wei(gas_price, 'gwei'),
                'estimated_cost_wei': cost_wei,
                'estimated_cost_eth': float(cost_eth),
                'estimated_cost_usd': self._get_eth_price() * float(cost_eth) if network.network_type == 'ETHEREUM' else None
            }
            
        except Exception as e:
            logger.error(f"Failed to estimate gas cost: {e}")
            return {'error': str(e)}
    
    def _get_eth_price(self) -> float:
        """Get current ETH price in USD"""
        try:
            response = requests.get('https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd', timeout=10)
            data = response.json()
            return data['ethereum']['usd']
        except Exception:
            return 2000.0  # Fallback price


# Global instance
blockchain_service = RealBlockchainCredentialService()

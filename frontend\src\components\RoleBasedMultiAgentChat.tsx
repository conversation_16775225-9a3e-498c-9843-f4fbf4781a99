import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Typography,
  TextField,
  Chip,
  Paper,
  CircularProgress,

  List,
  ListItem,
  ListItemText,
  Button,
  useTheme,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Add as AddIcon,
  AutoAwesome,
  History as HistoryIcon,
} from '@mui/icons-material';
import { FiSend } from 'react-icons/fi';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { styled } from '@mui/material/styles';
import { RootState } from '../app/store';
import multiAgentService from '../services/multiAgentService';
import multiAgentConversationService, {
  MultiAgentConversation,
  MultiAgentMessage,
} from '../services/multiAgentConversationService';
import ChatModeTabNavigation from './chat/ChatModeTabNavigation';

// 🎭 Role-Based Multi-Agent Chat Component
// Adapts to user roles: <PERSON><PERSON><PERSON>, <PERSON>OF<PERSON>SOR, STUDENT

interface RoleConfig {
  name: string;
  code: string;
  color: string;
  icon: string;
  agents: AgentInfo[];
  sampleMessages: string[];
  description: string;
}

interface AgentInfo {
  name: string;
  icon: string;
  color: string;
  description: string;
  keywords: string[];
}

interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  agentUsed?: string;
  timestamp: Date;
  metadata?: any;
}

// Styled Components
const SidebarPaper = styled(Paper)(({ theme }) => ({
  width: 320,
  minWidth: 320,
  height: '100%',
  borderRadius: theme.spacing(2),
  borderRight: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.paper,
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: theme.shadows[3],
}));

const StyledListItem = styled(ListItem)(({ theme }) => ({
  margin: theme.spacing(0.5, 1),
  borderRadius: theme.spacing(1.5),
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  position: 'relative',
  overflow: 'hidden',
  '&:hover': {
    backgroundColor: 'action.hover',
    transform: 'translateX(4px)',
  },
  '&.Mui-selected': {
    backgroundColor: 'action.selected',
    '&:hover': {
      backgroundColor: 'action.hover',
    },
  },
}));

const CreateButton = styled(Button)(({ theme }) => ({
  margin: theme.spacing(1),
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  borderRadius: theme.spacing(2),
  textTransform: 'none',
  fontWeight: 600,
  padding: theme.spacing(1.5, 3),
  boxShadow: theme.shadows[4],
  '&:hover': {
    backgroundColor: theme.palette.primary.dark,
    transform: 'translateY(-2px)',
    boxShadow: theme.shadows[8],
  },
}));

const roleConfigs: RoleConfig[] = [
  {
    name: 'Student',
    code: 'STUDENT',
    color: '#2196F3',
    icon: '🎓',
    description: 'Access tutoring, learning support, and career guidance',
    agents: [
      {
        name: 'General Tutor',
        icon: '🎓',
        color: '#2196F3',
        description: 'Get help with any subject',
        keywords: ['help', 'explain', 'learn'],
      },
      {
        name: 'Math Tutor',
        icon: '🔢',
        color: '#4CAF50',
        description: 'Mathematics and problem solving',
        keywords: ['math', 'equation', 'calculate'],
      },
      {
        name: 'Science Tutor',
        icon: '🔬',
        color: '#FF9800',
        description: 'Science concepts and experiments',
        keywords: ['science', 'biology', 'chemistry'],
      },
      {
        name: 'Language Tutor',
        icon: '📝',
        color: '#9C27B0',
        description: 'Writing and language arts',
        keywords: ['write', 'essay', 'grammar'],
      },
      {
        name: 'Career Advisor',
        icon: '🎯',
        color: '#F44336',
        description: 'Academic and career guidance',
        keywords: ['career', 'advice', 'guidance'],
      },
    ],
    sampleMessages: [
      'Help me understand quadratic equations',
      'Explain photosynthesis process',
      'How do I write a better essay?',
      'What career path should I choose?',
      'I need help with my homework',
    ],
  },
  {
    name: 'Professor',
    code: 'PROFESSOR',
    color: '#4CAF50',
    icon: '👨‍🏫',
    description: 'Create content, assessments, and manage curriculum',
    agents: [
      {
        name: 'Assessor',
        icon: '📊',
        color: '#F44336',
        description: 'Create quizzes and assessments',
        keywords: ['quiz', 'test', 'assessment'],
      },
      {
        name: 'Content Creator',
        icon: '✨',
        color: '#FF9800',
        description: 'Generate educational content',
        keywords: ['create', 'content', 'lesson'],
      },
      {
        name: 'General Tutor',
        icon: '🎓',
        color: '#2196F3',
        description: 'Understand student needs',
        keywords: ['help', 'explain', 'teach'],
      },
      {
        name: 'Math Tutor',
        icon: '🔢',
        color: '#4CAF50',
        description: 'Math curriculum development',
        keywords: ['math', 'curriculum', 'problems'],
      },
      {
        name: 'Science Tutor',
        icon: '🔬',
        color: '#9C27B0',
        description: 'Science lesson planning',
        keywords: ['science', 'experiment', 'lab'],
      },
    ],
    sampleMessages: [
      'Create a calculus quiz for intermediate students',
      'Generate lesson content on cellular biology',
      'Help me design an assessment for algebra',
      'Create practice problems for physics',
      'Develop a writing assignment rubric',
    ],
  },
  {
    name: 'Admin',
    code: 'ADMIN',
    color: '#F44336',
    icon: '👑',
    description: 'System analytics, institutional planning, and oversight',
    agents: [
      {
        name: 'Assessor',
        icon: '📊',
        color: '#F44336',
        description: 'System-wide assessment analytics',
        keywords: ['analytics', 'assessment', 'data'],
      },
      {
        name: 'Content Creator',
        icon: '✨',
        color: '#FF9800',
        description: 'Institutional content creation',
        keywords: ['institutional', 'policy', 'content'],
      },
      {
        name: 'Advisor',
        icon: '🎯',
        color: '#9C27B0',
        description: 'Institutional guidance and planning',
        keywords: ['planning', 'strategy', 'guidance'],
      },
      {
        name: 'General Tutor',
        icon: '🎓',
        color: '#2196F3',
        description: 'System oversight and monitoring',
        keywords: ['oversight', 'monitoring', 'system'],
      },
      {
        name: 'Math Tutor',
        icon: '🔢',
        color: '#4CAF50',
        description: 'Math program oversight',
        keywords: ['program', 'math', 'oversight'],
      },
    ],
    sampleMessages: [
      'Analyze system-wide learning patterns',
      'Create institutional assessment strategy',
      'Generate administrative reports',
      'Plan curriculum improvements',
      'Monitor student progress trends',
    ],
  },
];

const RoleBasedMultiAgentChat: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  const navigate = useNavigate();
  const theme = useTheme();
  
  // Get user's role configuration based on their actual role
  const getUserRoleConfig = (): RoleConfig => {
    const userRole = user?.role?.toUpperCase() || 'STUDENT';
    return roleConfigs.find(role => role.code === userRole) || roleConfigs[0];
  };

  const [currentRole] = useState<RoleConfig>(getUserRoleConfig());
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeAgent, setActiveAgent] = useState<string | null>(null);
  const [conversations, setConversations] = useState<MultiAgentConversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<MultiAgentConversation | null>(null);
  const [loadingConversations, setLoadingConversations] = useState(false);
  const messageEndRef = useRef<HTMLDivElement>(null);

  // Load conversations on component mount
  useEffect(() => {
    loadConversations();
  }, []);

  // Convert ChatMessage to MultiAgentMessage format
  const convertToMultiAgentMessage = (msg: ChatMessage): MultiAgentMessage => ({
    id: msg.id,
    content: msg.content,
    isUser: msg.isUser,
    agentUsed: msg.agentUsed,
    timestamp: msg.timestamp,
    metadata: msg.metadata,
  });

  const loadConversations = async () => {
    setLoadingConversations(true);
    try {
      const loadedConversations = await multiAgentConversationService.getConversations();
      setConversations(loadedConversations);
    } catch (error) {
      console.error('Error loading conversations:', error);
    } finally {
      setLoadingConversations(false);
    }
  };

  const createNewConversation = async () => {
    try {
      const newConv = await multiAgentConversationService.createConversation({
        title: 'New Multi-Agent Chat',
        userRole: currentRole.code,
      });
      setConversations(prev => [newConv, ...prev]);
      setActiveConversation(newConv);
      setMessages([]);
    } catch (error) {
      console.error('Error creating conversation:', error);
    }
  };

  const selectConversation = async (conversation: MultiAgentConversation) => {
    setActiveConversation(conversation);
    setMessages(conversation.messages.map(msg => ({
      id: msg.id,
      content: msg.content,
      isUser: msg.isUser,
      agentUsed: msg.agentUsed,
      timestamp: msg.timestamp,
      metadata: msg.metadata,
    })));
  };

  const deleteConversation = async (conversationId: string) => {
    try {
      await multiAgentConversationService.deleteConversation(conversationId);
      setConversations(prev => prev.filter(conv => conv.id !== conversationId));
      if (activeConversation?.id === conversationId) {
        setActiveConversation(null);
        setMessages([]);
      }
    } catch (error) {
      console.error('Error deleting conversation:', error);
    }
  };

  // Update conversation when messages change
  useEffect(() => {
    if (activeConversation && messages.length > 0) {
      const updateConversation = async () => {
        try {
          await multiAgentConversationService.updateConversation(
            activeConversation.id,
            messages.map(convertToMultiAgentMessage)
          );
          // Update the conversation in the list
          setConversations(prev => prev.map(conv => 
            conv.id === activeConversation.id 
              ? { ...conv, messages: messages.map(convertToMultiAgentMessage), updatedAt: new Date() }
              : conv
          ));
        } catch (error) {
          console.error('Error updating conversation:', error);
        }
      };
      updateConversation();
    }
  }, [messages, activeConversation]);

  const scrollToBottom = useCallback(() => {
    if (messageEndRef.current) {
      messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  const predictAgent = (message: string, role: RoleConfig): AgentInfo => {
    const lowerMessage = message.toLowerCase();

    // Check role-specific agent priorities
    for (const agent of role.agents) {
      if (agent.keywords.some(keyword => lowerMessage.includes(keyword))) {
        return agent;
      }
    }

    // Default to first agent for the role
    return role.agents[0];
  };

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    // Create new conversation if none is active
    if (!activeConversation) {
      await createNewConversation();
    }

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputMessage,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Predict which agent will handle this based on user's role
    const predictedAgent = predictAgent(inputMessage, currentRole);
    setActiveAgent(predictedAgent.name);

    try {
      // Send message with user context using multi-agent service
      const response = await multiAgentService.testAgentResponse({
        agent_type: predictedAgent.name.toLowerCase().replace(' ', '_'),
        message: inputMessage,
        context: {
          user_role: user?.role,
          user_name: user?.first_name || user?.username,
          user_id: user?.id,
        },
      });

      console.log('Multi-agent response:', response); // Debug log

      // Handle different response structures
      let content = '';
      if (response?.data?.response?.content) {
        content = response.data.response.content;
      } else if (response?.data?.message) {
        content = response.data.message;
      } else if ((response as any)?.message) {
        content = (response as any).message;
      } else {
        content = `${currentRole.icon} ${predictedAgent.name}: I received your message, ${user?.first_name || user?.username || 'there'}! I'm here to help with ${predictedAgent.description.toLowerCase()}.`;
      }

      const botMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content,
        isUser: false,
        agentUsed:
          response?.data?.agent_type ||
          response?.data?.agent_name ||
          predictedAgent.name.toLowerCase().replace(' ', '_'),
        timestamp: new Date(),
        metadata: response?.data?.response?.metadata || (response as any)?.metadata,
      };

      setMessages(prev => [...prev, botMessage]);
    } catch (error: any) {
      console.error('Multi-agent chat error:', error); // Debug log
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: `${currentRole.icon} Hello ${user?.first_name || user?.username || 'there'}! As a ${currentRole.name}, this would be handled by ${predictedAgent.name} (${predictedAgent.icon}). The AI system recognizes your role and will provide ${currentRole.name.toLowerCase()}-specific assistance.`,
        isUser: false,
        agentUsed: predictedAgent.name.toLowerCase().replace(' ', '_'),
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    }

    setInputMessage('');
    setIsLoading(false);
    setActiveAgent(null);
  };

  const getAgentInfo = (agentUsed: string): AgentInfo => {
    for (const agent of currentRole.agents) {
      if (agentUsed?.includes(agent.name.toLowerCase().replace(' ', '_'))) {
        return agent;
      }
    }
    return currentRole.agents[0];
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', width: '100%' }}>
      <ChatModeTabNavigation />
      <Box 
        sx={{ 
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          overflow: 'hidden',
          width: '100%',
          minHeight: 0, // Important for proper flex behavior
          // Removed complex pseudo-selector to avoid Stylis parser issues
        }}
      >
      {/* Main Content */}
      <Box sx={{ flex: 1, display: 'flex', overflow: 'hidden', width: '100%', minHeight: 0 }}>
        {/* Left Sidebar - Conversations */}
        <SidebarPaper>
          {/* Sidebar Header */}
          <Box
            sx={{
              p: 2,
              backgroundColor: 'primary.main',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              boxShadow: 2,
              flexShrink: 0,
            }}
          >
            <Box
              sx={{
                width: 32,
                height: 32,
                borderRadius: '50%',
                backgroundColor: 'action.hover',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backdropFilter: 'blur(10px)',
              }}
            >
              <HistoryIcon sx={{ fontSize: 16, color: 'white' }} />
            </Box>
            <Box sx={{ flex: 1 }}>
              <Typography variant='subtitle1' fontWeight={600} sx={{ lineHeight: 1.2 }}>
                Previous Conversations
              </Typography>
              <Typography variant='caption' sx={{ opacity: 0.8, fontSize: '0.7rem' }}>
                {currentRole.name} Chat History
              </Typography>
            </Box>
          </Box>

          {/* New Conversation Button */}
          <Box sx={{ p: 1, borderBottom: `1px solid ${theme.palette.divider}` }}>
            <CreateButton
              fullWidth
              startIcon={<AddIcon />}
              onClick={createNewConversation}
              size="small"
            >
              New Chat
            </CreateButton>
          </Box>

          {/* Conversations List */}
          <Box sx={{ flex: 1, overflow: 'auto' }}>
            {loadingConversations ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress size={24} />
              </Box>
            ) : conversations.length === 0 ? (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  No conversations yet. Start a new chat!
                </Typography>
              </Box>
            ) : (
              <List sx={{ p: 0 }}>
                {conversations.map((conversation) => (
                  <StyledListItem
                    key={conversation.id}
                    selected={activeConversation?.id === conversation.id}
                    onClick={() => selectConversation(conversation)}
                  >
                    <ListItemText
                      primary={
                        <Typography variant="body2" noWrap sx={{ fontWeight: 500 }}>
                          {conversation.title}
                        </Typography>
                      }
                      secondary={
                        <Typography variant="caption" color="text.secondary">
                          {conversation.updatedAt.toLocaleDateString()}
                        </Typography>
                      }
                    />
                    <Tooltip title="Delete conversation">
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteConversation(conversation.id);
                        }}
                        sx={{
                          opacity: 0.6,
                          '&:hover': { opacity: 1, color: 'error.main' },
                        }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </StyledListItem>
                ))}
              </List>
            )}
          </Box>
        </SidebarPaper>

        {/* Right Side - Chat Area */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', minHeight: 0, overflow: 'hidden', ml: 2 }}>
          {/* Chat Header */}
          <Box
            sx={{
              p: 2,
              backgroundColor: 'primary.main',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              boxShadow: 2,
              flexShrink: 0,
              borderRadius: `${theme.spacing(2)} ${theme.spacing(2)} 0 0`,
            }}
          >
            <Box
              sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                backgroundColor: 'action.hover',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backdropFilter: 'blur(10px)',
              }}
            >
              <AutoAwesome sx={{ fontSize: 20, color: 'white' }} />
            </Box>
            <Box sx={{ flex: 1 }}>
              <Typography variant='h6' fontWeight={600} sx={{ lineHeight: 1.2 }}>
                Multi-Agent Chat - {currentRole.name}
              </Typography>
              <Typography variant='caption' sx={{ opacity: 0.8, fontSize: '0.75rem' }}>
                Specialized AI experts for {user?.first_name || user?.username || 'User'}
              </Typography>
            </Box>
            
            {/* User Info Display */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                backgroundColor: 'action.hover',
                px: 3,
                py: 1,
                borderRadius: 20,
                backdropFilter: 'blur(10px)',
              }}
            >
              <Box sx={{ textAlign: 'right' }}>
                <Typography variant='body2' sx={{ fontSize: '0.9rem', fontWeight: 600 }}>
                  {user?.first_name || user?.username || 'User'}
                </Typography>
                <Typography variant='caption' sx={{ fontSize: '0.75rem', opacity: 0.8 }}>
                  {currentRole.name}
                </Typography>
              </Box>
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  borderRadius: '50%',
                  backgroundColor: currentRole.color,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontWeight: 600,
                  fontSize: '0.9rem',
                }}
              >
                {(user?.first_name || user?.username || 'U').charAt(0).toUpperCase()}
              </Box>
            </Box>
            
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                backgroundColor: 'action.hover',
                px: 2,
                py: 0.5,
                borderRadius: 20,
                backdropFilter: 'blur(10px)',
              }}
            >
              <Box
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: '#4ade80',
                  // Simple opacity animation without keyframes
                  opacity: 0.8,
                }}
              />
              <Typography variant='body2' sx={{ fontSize: '0.8rem' }}>
                Online
              </Typography>
            </Box>
          </Box>
          {/* Messages container */}
          <Box
            sx={{
              flex: 1,
              overflow: 'auto',
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              gap: 1,
              position: 'relative',
              zIndex: 1,
              minHeight: 0,
              backgroundColor: 'background.default',
              // Simplified scrollbar styling to avoid Stylis parser issues
              overflowY: 'auto',
            }}
          >
            {messages.length === 0 && (
              <Box 
                sx={{ 
                  textAlign: 'center', 
                  py: 6,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%',
                }}
              >
                <Box
                  sx={{
                    width: 70,
                    height: 70,
                    borderRadius: '50%',
                    backgroundColor: 'action.hover',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 2.5,
                    // Simple static styling without animation
                    transform: 'none',
                  }}
                >
                  <span style={{ fontSize: '36px' }}>{currentRole.icon}</span>
                </Box>
                <Typography variant='h6' color='text.primary' sx={{ mb: 1, fontWeight: 600 }}>
                  Welcome to {currentRole.name} Multi-Agent Chat
                </Typography>
                <Typography variant='body2' color='text.secondary' sx={{ mb: 3, maxWidth: 350, lineHeight: 1.5 }}>
                  {currentRole.description}. Choose from the sample messages or start typing to see which agent responds!
                </Typography>
              </Box>
            )}

            {messages.map(message => (
              <Box
                key={message.id}
                sx={{
                  display: 'flex',
                  justifyContent: message.isUser ? 'flex-end' : 'flex-start',
                  mb: 1,
                }}
              >
                <Paper
                  sx={{
                    p: 2.5,
                    maxWidth: '70%',
                    borderRadius: message.isUser ? '20px 20px 4px 20px' : '20px 20px 20px 4px',
                    backgroundColor: message.isUser
                      ? currentRole.color
                      : 'background.paper',
                    color: message.isUser ? 'white' : 'text.primary',
                    boxShadow: message.isUser ? 4 : 2,
                    border: message.isUser ? 'none' : '1px solid',
                    borderColor: message.isUser ? 'transparent' : 'divider',
                    position: 'relative',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      transform: message.isUser ? 'translateY(-1px)' : 'translateX(2px)',
                      boxShadow: message.isUser ? 6 : 4,
                    },
                  }}
                >
                  {!message.isUser && message.agentUsed && (
                    <Box sx={{ mb: 1 }}>
                      <Chip
                        icon={
                          <span>
                            {getAgentInfo(message.agentUsed).icon}
                          </span>
                        }
                        label={getAgentInfo(message.agentUsed).name}
                        size='small'
                        sx={{
                          backgroundColor: 'primary.light',
                          color: 'primary.main',
                          border: '1px solid',
                          borderColor: 'primary.main',
                          fontWeight: 500,
                        }}
                      />
                    </Box>
                  )}
                  <Typography variant='body2' sx={{ lineHeight: 1.6 }}>
                    {message.content}
                  </Typography>
                  <Typography
                    variant='caption'
                    display='block'
                    sx={{ 
                      mt: 1, 
                      opacity: 0.7,
                      fontSize: '0.7rem',
                    }}
                  >
                    {message.timestamp.toLocaleTimeString()}
                  </Typography>
                </Paper>
              </Box>
            ))}

            {isLoading && (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'flex-start',
                  mb: 2,
                }}
              >
                <Paper
                  sx={{
                    p: 2,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    borderRadius: '20px 20px 20px 4px',
                    backgroundColor: 'background.paper',
                    boxShadow: 2,
                  }}
                >
                  <CircularProgress size={16} />
                  <Typography variant='body2'>
                    {activeAgent} is thinking...
                  </Typography>
                </Paper>
              </Box>
            )}
            <div ref={messageEndRef} />
          </Box>

          {/* Input form */}
          <Box
            sx={{
              p: 2,
              borderTop: '1px solid',
              borderColor: 'divider',
              backgroundColor: 'background.default',
              backdropFilter: 'blur(20px)',
              flexShrink: 0,
              borderRadius: `0 0 ${theme.spacing(2)} ${theme.spacing(2)}`,
            }}
          >
            <Paper
              component='form'
              onSubmit={(e) => {
                e.preventDefault();
                sendMessage();
              }}
              elevation={0}
              sx={{
                p: 1.5,
                display: 'flex',
                alignItems: 'flex-end',
                gap: 1.5,
                borderRadius: 20,
                backgroundColor: 'background.paper',
                backdropFilter: 'blur(10px)',
                border: '1px solid',
                borderColor: 'divider',
                boxShadow: 1,
                '&:focus-within': {
                  borderColor: 'primary.main',
                  boxShadow: 3,
                },
                transition: 'all 0.2s ease',
              }}
            >
              <TextField
                fullWidth
                value={inputMessage}
                onChange={e => setInputMessage(e.target.value)}
                placeholder={`Type a message as ${currentRole.name.toLowerCase()}...`}
                disabled={isLoading}
                multiline
                maxRows={4}
                variant="standard"
                InputProps={{
                  disableUnderline: true,
                  sx: {
                    fontSize: '1rem',
                    color: 'text.primary',
                    py: 1,
                  },
                }}
                onKeyDown={e => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                  }
                }}
              />
              <IconButton
                type='submit'
                disabled={!inputMessage.trim() || isLoading}
                sx={{
                  backgroundColor: currentRole.color,
                  color: 'white',
                  width: 40,
                  height: 40,
                  boxShadow: 2,
                  '&:hover': {
                    backgroundColor: currentRole.color,
                    transform: 'scale(1.05)',
                    boxShadow: 4,
                  },
                  '&:disabled': {
                    backgroundColor: 'action.disabled',
                    color: 'text.disabled',
                    transform: 'none',
                    boxShadow: 'none',
                  },
                  transition: 'all 0.2s ease',
                }}
              >
                {isLoading ? (
                  <Box
                    sx={{
                      width: 16,
                      height: 16,
                      border: '2px solid currentColor',
                      borderTop: '2px solid transparent',
                      borderRadius: '50%',
                      // Use Material-UI's CircularProgress instead of custom animation
                      // animation: 'spin 1s linear infinite',
                    }}
                  />
                ) : (
                  <FiSend size={16} />
                )}
              </IconButton>
            </Paper>

            {/* Prediction */}
            {inputMessage && (
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Typography variant='caption' color='text.secondary'>
                  Predicted agent: {predictAgent(inputMessage, currentRole).icon}{' '}
                  {predictAgent(inputMessage, currentRole).name}
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
    </Box>
  );
};

export default RoleBasedMultiAgentChat;

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface LoadingState {
  [key: string]: boolean;
}

interface ModalState {
  [modalId: string]: {
    isOpen: boolean;
    data?: any;
  };
}

interface NotificationState {
  id: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  autoHide?: boolean;
  duration?: number;
}

interface UIState {
  loading: boolean;
  loadingStates: LoadingState;
  modals: ModalState;
  notifications: NotificationState[];
  sidebarOpen: boolean;
  drawerOpen: boolean;
  theme: 'light' | 'dark' | 'auto';
  language: string;
  viewport: {
    width: number;
    height: number;
    isMobile: boolean;
    isTablet: boolean;
    isDesktop: boolean;
  };
}

const initialState: UIState = {
  loading: false,
  loadingStates: {},
  modals: {},
  notifications: [],
  sidebarOpen: true,
  drawerOpen: false,
  theme: 'auto',
  language: 'en',
  viewport: {
    width: window.innerWidth,
    height: window.innerHeight,
    isMobile: window.innerWidth < 768,
    isTablet: window.innerWidth >= 768 && window.innerWidth < 1024,
    isDesktop: window.innerWidth >= 1024,
  },
};

const uiStateSlice = createSlice({
  name: 'uiState',
  initialState,
  reducers: {
    // Global loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },

    // Specific loading states for different operations
    setLoadingState: (state, action: PayloadAction<{ key: string; loading: boolean }>) => {
      const { key, loading } = action.payload;
      state.loadingStates[key] = loading;
    },

    clearLoadingState: (state, action: PayloadAction<string>) => {
      delete state.loadingStates[action.payload];
    },

    clearAllLoadingStates: (state) => {
      state.loadingStates = {};
    },

    // Modal management
    openModal: (state, action: PayloadAction<{ modalId: string; data?: any }>) => {
      const { modalId, data } = action.payload;
      state.modals[modalId] = {
        isOpen: true,
        data,
      };
    },

    closeModal: (state, action: PayloadAction<string>) => {
      const modalId = action.payload;
      if (state.modals[modalId]) {
        state.modals[modalId].isOpen = false;
      }
    },

    closeAllModals: (state) => {
      Object.keys(state.modals).forEach(modalId => {
        state.modals[modalId].isOpen = false;
      });
    },

    // Notification management
    addNotification: (state, action: PayloadAction<Omit<NotificationState, 'id'>>) => {
      const notification: NotificationState = {
        ...action.payload,
        id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      };
      state.notifications.push(notification);
    },

    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(
        notification => notification.id !== action.payload
      );
    },

    clearAllNotifications: (state) => {
      state.notifications = [];
    },

    // Layout state
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },

    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },

    toggleDrawer: (state) => {
      state.drawerOpen = !state.drawerOpen;
    },

    setDrawerOpen: (state, action: PayloadAction<boolean>) => {
      state.drawerOpen = action.payload;
    },

    // Theme management
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'auto'>) => {
      state.theme = action.payload;
    },

    // Language management
    setLanguage: (state, action: PayloadAction<string>) => {
      state.language = action.payload;
    },

    // Viewport management
    updateViewport: (state, action: PayloadAction<{ width: number; height: number }>) => {
      const { width, height } = action.payload;
      state.viewport = {
        width,
        height,
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024,
      };
    },

    // Bulk state updates for performance
    updateUIState: (state, action: PayloadAction<Partial<UIState>>) => {
      return { ...state, ...action.payload };
    },

    // Reset to initial state
    resetUIState: () => initialState,
  },
});

export const {
  setLoading,
  setLoadingState,
  clearLoadingState,
  clearAllLoadingStates,
  openModal,
  closeModal,
  closeAllModals,
  addNotification,
  removeNotification,
  clearAllNotifications,
  toggleSidebar,
  setSidebarOpen,
  toggleDrawer,
  setDrawerOpen,
  setTheme,
  setLanguage,
  updateViewport,
  updateUIState,
  resetUIState,
} = uiStateSlice.actions;

// Selectors
export const selectUIState = (state: { uiState: UIState }) => state.uiState;
export const selectLoading = (state: { uiState: UIState }) => state.uiState.loading;
export const selectLoadingState = (key: string) => (state: { uiState: UIState }) => 
  state.uiState.loadingStates[key] || false;
export const selectModalState = (modalId: string) => (state: { uiState: UIState }) =>
  state.uiState.modals[modalId] || { isOpen: false };
export const selectNotifications = (state: { uiState: UIState }) => state.uiState.notifications;
export const selectViewport = (state: { uiState: UIState }) => state.uiState.viewport;

export default uiStateSlice.reducer;

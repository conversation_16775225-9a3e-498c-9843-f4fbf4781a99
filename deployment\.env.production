# Production Environment Configuration for North Star University
# Copy this file to backend/.env.production and update with your actual values

# Django Settings
DJANGO_SECRET_KEY=your-super-secure-secret-key-here-generate-new-one
DJANGO_DEBUG=False
DJANGO_ENVIRONMENT=production
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# Database Configuration (PostgreSQL recommended for production)
DB_NAME=north_star_university
DB_USER=your_db_user
DB_PASSWORD=your_secure_db_password
DB_HOST=localhost
DB_PORT=5432

# Alternative: SQLite for smaller deployments
# DATABASE_URL=sqlite:///db.sqlite3

# AI Service Configuration
GEMINI_API_KEY=your-gemini-api-key-here

# API Key Security
API_ENCRYPTION_KEY=generate-using-fernet-generate-key
ACCEPT_ANY_API_KEY=False
FALLBACK_ON_INVALID_KEY=False
VALIDATE_API_KEY_BEFORE_STORAGE=True

# Email Configuration
EMAIL_HOST=smtp.hostinger.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
EMAIL_USE_TLS=True
DEFAULT_FROM_EMAIL=North Star University <<EMAIL>>

# Redis Configuration (for caching and Celery)
REDIS_URL=redis://localhost:6379/0

# Site Configuration
SITE_URL=https://your-domain.com

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Security Settings
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# Logging
LOG_DIR=/var/log/north-star-university

# Static and Media Files
STATIC_ROOT=/var/www/north-star-university/static
MEDIA_ROOT=/var/www/north-star-university/media

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Performance Settings
GUNICORN_WORKERS=3
GUNICORN_TIMEOUT=120
GUNICORN_MAX_REQUESTS=1000
GUNICORN_MAX_REQUESTS_JITTER=100

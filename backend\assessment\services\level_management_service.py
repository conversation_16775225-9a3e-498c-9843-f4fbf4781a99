"""
Level management service implementing business logic for student academic levels.

Handles level progression, assessment-based level updates, and level analytics.
"""

from typing import Dict, Any, List, Optional
from django.db import transaction
from django.utils import timezone

from core.services.base import (
    BaseService, 
    ValidationServiceError, 
    NotFoundServiceError
)
from assessment.models import StudentLevel, Assessment
from users.models import CustomUser


class LevelManagementService(BaseService):
    """
    Service for managing student academic levels and progression.
    
    Provides business logic for level updates, progression tracking,
    and level-based recommendations.
    """
    
    def __init__(self) -> None:
        """Initialize the level management service."""
        super().__init__()
    
    def get_or_create_student_level(self, student: CustomUser) -> StudentLevel:
        """
        Get or create a student level record for a student.
        
        Args:
            student: Student user instance
            
        Returns:
            StudentLevel instance for the student
        """
        student_level, created = StudentLevel.objects.get_or_create(
            student=student,
            defaults={
                'current_level': 1,
                'current_level_display': 'Beginner',
                'skill_strengths': {},
                'skill_weaknesses': {},
                'progression_history': []
            }
        )
        
        if created:
            self.log_action('create_student_level', student, student_level)
        
        return student_level
    
    @transaction.atomic
    def update_level_from_assessment(
        self, 
        student: CustomUser, 
        assessment: Assessment, 
        new_level: int
    ) -> Dict[str, Any]:
        """
        Update student level based on assessment results.
        
        Args:
            student: Student whose level to update
            assessment: Assessment that determined the new level
            new_level: New academic level (1-5)
            
        Returns:
            Dictionary with level update information
            
        Raises:
            ValidationServiceError: If new_level is invalid
        """
        if not 1 <= new_level <= 5:
            raise ValidationServiceError(f"Invalid level: {new_level}. Must be between 1 and 5.")
        
        student_level = self.get_or_create_student_level(student)
        previous_level = student_level.current_level
        
        # Only update if level actually changed
        if new_level == previous_level:
            return {
                'level_changed': False,
                'previous_level': previous_level,
                'current_level': new_level,
                'level_display': dict(StudentLevel.LEVEL_CHOICES)[new_level]
            }
        
        # Update the level using the model's method
        level_changed = student_level.update_level(
            new_level=new_level,
            reason=f"{assessment.assessment_type} Assessment (Score: {assessment.score}%)",
            assessment=assessment
        )
        
        # Log the level change
        self.log_action(
            'level_update',
            student,
            student_level,
            previous_level=previous_level,
            new_level=new_level,
            assessment_id=assessment.id,
            assessment_score=assessment.score
        )
        
        return {
            'level_changed': level_changed,
            'previous_level': previous_level,
            'current_level': new_level,
            'level_display': dict(StudentLevel.LEVEL_CHOICES)[new_level],
            'assessment_id': assessment.id,
            'assessment_score': assessment.score
        }
    
    def calculate_level_from_score(
        self, 
        assessment_score: float, 
        assessment_type: str
    ) -> int:
        """
        Calculate appropriate academic level based on assessment score.
        
        Args:
            assessment_score: Score percentage (0-100)
            assessment_type: Type of assessment
            
        Returns:
            Calculated academic level (1-5)
        """
        if assessment_type == "PLACEMENT":
            # More granular level determination for placement assessments
            if assessment_score >= 90:
                return 5  # Expert
            elif assessment_score >= 75:
                return 4  # Advanced
            elif assessment_score >= 60:
                return 3  # Intermediate
            elif assessment_score >= 40:
                return 2  # Elementary
            else:
                return 1  # Beginner
        else:
            # Conservative level adjustment for regular assessments
            if assessment_score >= 85:
                return 4  # Advanced
            elif assessment_score >= 70:
                return 3  # Intermediate
            elif assessment_score >= 55:
                return 2  # Elementary
            else:
                return 1  # Beginner
    
    def get_level_progression_analytics(self, student: CustomUser) -> Dict[str, Any]:
        """
        Get analytics for a student's level progression.
        
        Args:
            student: Student to analyze
            
        Returns:
            Dictionary containing progression analytics
        """
        student_level = self.get_or_create_student_level(student)
        
        # Calculate progression metrics
        progression_history = student_level.progression_history or []
        total_assessments = len(progression_history)
        
        level_changes = [
            entry for entry in progression_history 
            if entry.get('level_changed', False)
        ]
        
        # Calculate time-based metrics
        if progression_history:
            first_assessment = min(
                progression_history, 
                key=lambda x: x.get('date', timezone.now().isoformat())
            )
            time_on_platform = (
                timezone.now() - 
                timezone.datetime.fromisoformat(first_assessment['date'])
            ).days
        else:
            time_on_platform = 0
        
        return {
            'current_level': student_level.current_level,
            'current_level_display': student_level.current_level_display,
            'total_assessments': total_assessments,
            'level_changes': len(level_changes),
            'time_on_platform_days': time_on_platform,
            'progression_history': progression_history,
            'skill_strengths': student_level.skill_strengths,
            'skill_weaknesses': student_level.skill_weaknesses,
            'last_assessment_date': student_level.last_assessment_date
        }
    
    def get_level_requirements(self, target_level: int) -> Dict[str, Any]:
        """
        Get requirements for advancing to a target level.
        
        Args:
            target_level: Target academic level
            
        Returns:
            Dictionary with level requirements
        """
        from assessment.models import LevelRequirement
        
        try:
            requirements = LevelRequirement.objects.get(level=target_level)
            return requirements.get_requirements_dict()
        except LevelRequirement.DoesNotExist:
            # Return default requirements if not configured
            return {
                'min_assessment_score': 70.0,
                'required_skills': [],
                'min_completed_courses': 1,
                'description': f'Requirements for level {target_level}'
            }
    
    def check_level_advancement_eligibility(
        self, 
        student: CustomUser
    ) -> Dict[str, Any]:
        """
        Check if a student is eligible for level advancement.
        
        Args:
            student: Student to check
            
        Returns:
            Dictionary with eligibility information
        """
        student_level = self.get_or_create_student_level(student)
        current_level = student_level.current_level
        next_level = current_level + 1
        
        if next_level > 5:
            return {
                'eligible': False,
                'reason': 'Already at maximum level',
                'current_level': current_level,
                'next_level': None
            }
        
        # Get requirements for next level
        requirements = self.get_level_requirements(next_level)
        
        # Check recent assessment scores
        recent_assessments = Assessment.objects.filter(
            student=student,
            completed=True,
            end_time__gte=timezone.now() - timezone.timedelta(days=30)
        ).order_by('-end_time')[:5]
        
        if not recent_assessments.exists():
            return {
                'eligible': False,
                'reason': 'No recent assessments completed',
                'current_level': current_level,
                'next_level': next_level,
                'requirements': requirements
            }
        
        # Calculate average score of recent assessments
        avg_score = sum(a.score for a in recent_assessments) / len(recent_assessments)
        min_required_score = requirements.get('min_assessment_score', 70.0)
        
        eligible = avg_score >= min_required_score
        
        return {
            'eligible': eligible,
            'reason': 'Meets requirements' if eligible else f'Average score {avg_score:.1f}% below required {min_required_score}%',
            'current_level': current_level,
            'next_level': next_level,
            'requirements': requirements,
            'current_avg_score': avg_score,
            'recent_assessments_count': len(recent_assessments)
        }
    
    def get_students_by_level(self, level: int) -> List[Dict[str, Any]]:
        """
        Get all students at a specific academic level.
        
        Args:
            level: Academic level to filter by
            
        Returns:
            List of student information dictionaries
        """
        student_levels = StudentLevel.objects.filter(current_level=level).select_related('student')
        
        return [
            {
                'student_id': sl.student.id,
                'student_username': sl.student.username,
                'student_email': sl.student.email,
                'level': sl.current_level,
                'level_display': sl.current_level_display,
                'last_assessment_date': sl.last_assessment_date,
                'progression_history_count': len(sl.progression_history or [])
            }
            for sl in student_levels
        ]

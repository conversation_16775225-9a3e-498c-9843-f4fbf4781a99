"""DatabaseModelsforAIServiceMonitoringandAnalyticsThesemodelsprovidepersistentstorageforAImetricsrequestsandanalytics."""from django.dbimportmodelsfrom django.contrib.authimportget_user_modelfrom django.utilsimport timezoneimportuuidUser=get_user_model()classAIServiceMetric(models.Model):"""ModelforstoringAIservicemetrics"""classServiceType(models.TextChoices):GENERAL='general''General'CHATBOT='chatbot''Chatbot'ASSESSMENT='assessment''Assessment'COURSE_GENERATOR='course_generator''CourseGenerator'STUDY_ASSISTANT='study_assistant''StudyAssistant'INTERACTIVE_LEARNING='InteractiveLearning'classOperation(models.TextChoices):GENERATE_CONTENT='generate_content''GenerateContent'ANALYZE_ANSWER='analyze_answer''AnalyzeAnswer'GET_SUGGESTIONS='get_suggestions''GetSuggestions'CHAT_RESPONSE='chat_response''ChatResponse'id=models.UUIDField(primary_key=Truedefault=uuid.uuid4editable=False)timestamp=models.DateTimeField(default=timezone.nowdb_index=True)service_type=models.CharField(max_length=50choices=ServiceType.choicesdb_index=True)operation=models.CharField(max_length=50choices=Operation.choicesdb_index=True)duration=models.FloatField(help_text="Durationinseconds")success=models.BooleanField(db_index=True)error_type=models.CharField(max_length=100blank=Truenull=True)user=models.ForeignKey(Useron_delete=models.SET_NULLnull=Trueblank=True)model_used=models.CharField(max_length=100blank=Truenull=True)tokens_used=models.IntegerField(null=Trueblank=True)cost=models.DecimalField(max_digits=10decimal_places=6null=Trueblank=True)classMeta:db_table='ai_service_metrics'indexes=[models.Index(fields=['timestamp''service_type'])models.Index(fields=['success''timestamp'])models.Index(fields=['user''timestamp'])]def__str__(self):returnf"{self.service_type}.{self.operation}-{self.duration:.2f}s-{'✓'ifself.successelse'✗'}"classAIRequest(models.Model):"""ModelfortrackingAIrequests"""classStatus(models.TextChoices):QUEUED='queued''Queued'PROCESSING='processing''Processing'COMPLETED='completed''Completed'FAILED='failed''Failed'CANCELLED='cancelled''Cancelled'classPriority(models.TextChoices):LOW='low''Low'NORMAL='normal''Normal'HIGH='high''High'URGENT='urgent''Urgent'id=models.UUIDField(primary_key=Truedefault=uuid.uuid4editable=False)created_at=models.DateTimeField(default=timezone.nowdb_index=True)updated_at=models.DateTimeField(auto_now=True)status=models.CharField(max_length=20choices=Status.choicesdefault=Status.QUEUEDdb_index=True)priority=models.CharField(max_length=10choices=Priority.choicesdefault=Priority.NORMAL)service_type=models.CharField(max_length=50db_index=True)operation=models.CharField(max_length=50)user=models.ForeignKey(Useron_delete=models.SET_NULLnull=Trueblank=True)#Requestdataprompt=models.TextField()context=models.JSONField(default=dictblank=True)#Responsedataresult=models.TextField(blank=Truenull=True)error_message=models.TextField(blank=Truenull=True)#Metadataprocessing_started_at=models.DateTimeField(null=Trueblank=True)completed_at=models.DateTimeField(null=Trueblank=True)processing_time=models.FloatField(null=Trueblank=Truehelp_text="Processingtimeinseconds")classMeta:db_table='ai_requests'indexes=[models.Index(fields=['status''priority''created_at'])models.Index(fields=['user''created_at'])models.Index(fields=['service_type''created_at'])]def__str__(self):returnf"AIRequest{self.id}-{self.status}-{self.service_type}"@propertydefis_completed(self):returnself.statusin[self.Status.COMPLETEDself.Status.FAILEDself.Status.CANCELLED]defmark_processing(self):"""Markrequestasprocessing"""self.status=self.Status.PROCESSINGself.processing_started_at=timezone.now()self.save(update_fields=['status''processing_started_at''updated_at'])defmark_completed(selfresult:str):"""Markrequestascompletedwithresult"""self.status=self.Status.COMPLETEDself.result=resultself.completed_at=timezone.now()ifself.processing_started_at:self.processing_time=(self.completed_at-self.processing_started_at).total_seconds()self.save(update_fields=['status''result''completed_at''processing_time''updated_at'])defmark_failed(selferror_message:str):"""Markrequestasfailedwitherror"""self.status=self.Status.FAILEDself.error_message=error_messageself.completed_at=timezone.now()ifself.processing_started_at:self.processing_time=(self.completed_at-self.processing_started_at).total_seconds()self.save(update_fields=['status''error_message''completed_at''processing_time''updated_at'])classAIConfiguration(models.Model):"""ModelforstoringAIconfigurationhistory"""id=models.UUIDField(primary_key=Truedefault=uuid.uuid4editable=False)created_at=models.DateTimeField(default=timezone.now)updated_by=models.ForeignKey(Useron_delete=models.SET_NULLnull=Trueblank=True)is_active=models.BooleanField(default=Falsedb_index=True)#Configurationfieldsdefault_model=models.CharField(max_length=100default='gemini-2.0-flash')temperature=models.FloatField(default=0.7)max_tokens=models.IntegerField(default=2048)timeout=models.IntegerField(default=30000help_text="Timeoutinmilliseconds")retries=models.IntegerField(default=3)enable_fallback=models.BooleanField(default=True)enable_caching=models.BooleanField(default=True)cache_ttl=models.IntegerField(default=300help_text="CacheTTLinseconds")#Ratelimitingrequests_per_minute=models.IntegerField(default=60)max_concurrent_requests=models.IntegerField(default=5)#Additionalsettingssettings=models.JSONField(default=dictblank=True)classMeta:db_table='ai_configurations'indexes=[models.Index(fields=['is_active''created_at'])]def__str__(self):returnf"AIConfig{self.id}-{self.default_model}-{'Active'ifself.is_activeelse'Inactive'}"defactivate(self):"""Activatethisconfigurationanddeactivateothers"""#DeactivateallotherconfigurationsAIConfiguration.objects.filter(is_active=True).update(is_active=False)#Activatethisoneself.is_active=Trueself.save(update_fields=['is_active'])@classmethoddefget_active_config(cls):"""Getthecurrentlyactiveconfiguration"""returncls.objects.filter(is_active=True).first()classAIUsageStatistics(models.Model):"""ModelforstoringaggregatedAIusagestatistics"""classPeriod(models.TextChoices):HOURLY='hourly''Hourly'DAILY='daily''Daily'WEEKLY='weekly''Weekly'MONTHLY='monthly''Monthly'id=models.UUIDField(primary_key=Truedefault=uuid.uuid4editable=False)period=models.CharField(max_length=10choices=Period.choicesdb_index=True)period_start=models.DateTimeField(db_index=True)period_end=models.DateTimeField()service_type=models.CharField(max_length=50db_index=True)#Aggregatedmetricstotal_requests=models.IntegerField(default=0)successful_requests=models.IntegerField(default=0)failed_requests=models.IntegerField(default=0)avg_duration=models.FloatField(default=0.0)max_duration=models.FloatField(default=0.0)min_duration=models.FloatField(default=0.0)total_tokens=models.BigIntegerField(default=0)total_cost=models.DecimalField(max_digits=10decimal_places=6default=0)#Additionalstatisticsunique_users=models.IntegerField(default=0)peak_requests_per_hour=models.IntegerField(default=0)created_at=models.DateTimeField(default=timezone.now)classMeta:db_table='ai_usage_statistics'unique_together=['period''period_start''service_type']indexes=[models.Index(fields=['period''period_start''service_type'])models.Index(fields=['period_start''service_type'])]def__str__(self):returnf"{self.service_type}-{self.period}-{self.period_start.date()}"@propertydefsuccess_rate(self):"""Calculatesuccessrate"""ifself.total_requests==0:return0.0return(self.successful_requests/self.total_requests)*100
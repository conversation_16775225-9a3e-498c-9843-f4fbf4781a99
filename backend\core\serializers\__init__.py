#Importserializersdirectlytoavoidcirculardependenciesfrom.base_course_serializersimport(BaseCourseSerializerUnifiedCourseSerializerBaseCourseProgressSerializer)#Re-exportmaincoreserializerstry:from..serializersimportStudentSkillProgressSerializerexceptImportError:StudentSkillProgressSerializer=None#Importthemissingserializersfromparentmoduletry:from..serializersimport(StudentSkillProgressSerializerUserProgressSerializerContentReferenceSerializerContentMappingSerializerSkillSerializer)exceptImportError:#Createstubserializerstopreventimporterrorsfromrest_frameworkimportserializersclassUserProgressSerializer(serializers.Serializer):passclassStudentSkillProgressSerializer(serializers.Serializer):pass
"""
Unified course views that integrate regular courses with interactive versions.
This provides a single API to manage both regular and interactive courses.
"""
import logging
from django.shortcuts import get_object_or_404
from django.db.models import Q
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated

from ..models import Course
from ..serializers import CourseSerializer
from ..utils.view_mixins import (
    CoursePermissionMixin,
    CourseContentMixin,
    CourseResponseMixin
)

logger = logging.getLogger(__name__)


class CreateInteractiveVersionView(APIView):
    """
    Stub for creating interactive versions - feature no longer supported
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request, course_id=None):
        """
        Return error indicating interactive learning is no longer supported
        """
        return Response(
            {"detail": "Interactive learning feature has been removed and is no longer supported"},
            status=status.HTTP_501_NOT_IMPLEMENTED
        )


class UnifiedCourseViewSet(CoursePermissionMixin, CourseContentMixin, 
                             CourseResponseMixin, viewsets.ModelViewSet):
    """
    Unified API for courses that includes interactive course information.
    
    This viewset provides:
    - List of all courses with interactive version info
    - Create interactive versions of existing courses
    - View course details including interactive options
    """
    queryset = Course.objects.all()
    serializer_class = CourseSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_base_queryset(self):
        """Get base queryset for courses"""
        return self.get_optimized_course_queryset()
    
    def list(self, request, *args, **kwargs):
        """
        Get all courses with interactive version information
        """
        try:
            courses = self.get_optimized_course_queryset().filter(is_active=True)
            courses_data = [course.to_dict_with_interactive() for course in courses]
            
            return self.success_response({
                'count': len(courses_data),
                'results': courses_data
            })
        except Exception as e:
            logger.error(f"Failed to retrieve courses: {str(e)}")
            return self.error_response(f'Failed to retrieve courses: {str(e)}')
    
    def retrieve(self, request, pk=None):
        """
        Get detailed course information including interactive version
        """
        try:
            course = self.get_course_or_403(pk)
            course_data = course.to_dict_with_interactive()
            
            # Add additional details for single course view
            course_data.update({
                'materials_count': course.materials.count() if hasattr(course, 'materials') else 0,
                'enrollments_count': course.enrollments.count() if hasattr(course, 'enrollments') else 0,
                'created_at': course.created_at,
                'updated_at': course.updated_at
            })
            
            return self.success_response(course_data)
        except Exception as e:
            logger.error(f"Failed to retrieve course {pk}: {str(e)}")
            return self.error_response(f'Failed to retrieve course: {str(e)}')
    
    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAdminUser])
    def create_interactive_version(self, request, pk=None):
        """
        Create an interactive version of this course
        """
        try:
            course = get_object_or_404(Course, pk=pk)
            
            if course.has_interactive_version:
                return Response(
                    {'error': 'This course already has an interactive version'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get interactive version settings from request
            interactive_data = request.data.get('interactive_settings', {})
            
            # Create interactive version
            interactive_version = course.create_interactive_version(**interactive_data)
            
            # Return updated course data
            course_data = course.to_dict_with_interactive()
            
            return Response({
                'message': 'Interactive version created successfully',
                'course': course_data,
                'interactive_version_id': interactive_version.id
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to create interactive version: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['get'])
    def interactive_details(self, request, pk=None):
        """
        Get detailed interactive course information
        """
        try:
            course = get_object_or_404(Course, pk=pk)
            
            if not course.has_interactive_version:
                return Response(
                    {'error': 'This course does not have an interactive version'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            interactive = course.get_interactive_version()
            
            # Get interactive course details
            interactive_data = {
                'id': interactive.id,
                'course_id': course.id,
                'course_title': course.title,
                'course_code': course.course_code,
                'interactive_title': interactive.interactive_title,
                'interactive_description': interactive.interactive_description,
                'points_per_lesson': interactive.points_per_lesson,
                'streak_bonus_multiplier': interactive.streak_bonus_multiplier,
                'daily_goal_options': interactive.daily_goal_options,
                'badges_enabled': interactive.badges_enabled,
                'leaderboard_enabled': interactive.leaderboard_enabled,
                'achievements_enabled': interactive.achievements_enabled,
                'difficulty_levels': interactive.difficulty_levels,
                'learning_paths': interactive.learning_paths,
                'interactive_content': interactive.interactive_content,
                'lesson_count': interactive.lessons.count() if hasattr(interactive, 'lessons') else 0,
                'created_at': interactive.created_at,
                'updated_at': interactive.updated_at
            }
            
            # Get student progress if user is a student
            if hasattr(request.user, 'interactive_progress'):
                # Interactive learning progress removed - app no longer exists
                interactive_data['student_progress'] = None
            
            return Response(interactive_data)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to retrieve interactive details: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def with_interactive(self, request):
        """
        Get only courses that have interactive versions
        """
        try:
            courses = Course.objects.filter(
                interactive_version__isnull=False,
                is_active=True
            ).select_related('department', 'instructor', 'interactive_version')
            
            courses_data = []
            for course in courses:
                course_data = course.to_dict_with_interactive()
                courses_data.append(course_data)
            
            return Response({
                'count': len(courses_data),
                'results': courses_data
            })
        except Exception as e:
            return Response(
                {'error': f'Failed to retrieve interactive courses: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def without_interactive(self, request):
        """
        Get only courses that don't have interactive versions yet
        """
        try:
            courses = Course.objects.filter(
                interactive_version__isnull=True,
                is_active=True
            ).select_related('department', 'instructor')
            
            courses_data = []
            for course in courses:
                course_data = course.to_dict_with_interactive()
                courses_data.append(course_data)
            
            return Response({
                'count': len(courses_data),
                'results': courses_data
            })
        except Exception as e:
            return Response(
                {'error': f'Failed to retrieve non-interactive courses: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

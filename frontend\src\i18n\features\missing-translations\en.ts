export const missingEnTranslations = {
  // Direction for LTR support
  direction: 'ltr',

  // Service Worker translations
  serviceWorker: {
    updateNow: 'Update Now',
    newVersion: 'New Version Available',
  },

  // Home page translations (missing keys)
  home: {
    title: '<PERSON><PERSON>',
    subtitle: 'Welcome to',
    tagline: 'The Future of Education',
    description:
      '<PERSON><PERSON> combines cutting-edge technology with innovative teaching methods to provide a personalized learning experience that prepares you for the careers of tomorrow.',
    getStarted: 'Get Started',
    learnMore: 'Learn More',
    scrollDown: 'Scroll Down',
    aboutSection: {
      whoWeAre: 'Who We Are',
      aboutUniversity: 'About Masar',
      description:
        'Masar is a modern educational platform that combines traditional academic excellence with innovative digital learning approaches. We focus on practical skills development while providing a solid theoretical foundation.',
      theory: 'Theory',
      practice: 'Practice',
      aiPowered: 'AI-Powered',
      fullyDigital: 'Fully Digital',
      ourLearningApproach: 'Our Learning Approach',
      established: 'Established 2020',
      motto: 'Learn, Apply, Innovate',
    },
    whySection: {
      whyNorthStar: 'Why Masar',
      whatSetsUsApart: 'What Sets Us Apart',
      description:
        "At Masar, we're reimagining higher education for the digital age. Our innovative approach combines cutting-edge technology with personalized learning experiences to prepare you for success in a rapidly changing world.",
      aiPersonalized: 'AI-Personalized Learning',
      aiPersonalizedDesc:
        'Our AI-powered platform adapts to your learning style, pace, and goals to create a truly personalized educational experience.',
      practicalExperiences: 'Hands-On Experiences',
      practicalExperiencesDesc:
        'Learn by doing with real-world projects, simulations, and practical applications that build job-ready skills employers value.',
      launchProject: 'Capstone Project',
      launchProjectDesc:
        "Every program culminates in a capstone project where you'll apply your skills to solve real-world problems.",
      accredited: 'Fully Accredited',
      accreditedDesc:
        'Our programs meet rigorous academic standards and are recognized by leading industry partners.',
      digitalEnvironment: 'Digital Environment',
      digitalEnvironmentDesc:
        'Access world-class education from anywhere with our fully digital campus and learning resources.',
    },
  },

  // Settings translations
  settings: {
    language: 'Language',
  },

  // Discover translations
  discover: {
    title: 'Discover Your Path',
    programming: 'Programming',
    cybersecurity: 'Cybersecurity',
    finance: 'Finance',
    marketing: 'Marketing',
    exploreOptions: 'Explore Your Options',
    ourSpecializations: 'Our Specialization Tracks',
    mainDescription:
      'Choose from our four specialized learning tracks: Programming, Cybersecurity, Finance, and Marketing. Each track offers a comprehensive curriculum with hands-on projects and personalized guidance to prepare you for a successful career.',
    viewAll: 'View All Specializations',
    paths: {
      programming: {
        description:
          'Learn programming fundamentals and software development to build web, mobile, and AI applications.',
      },
      cybersecurity: {
        description:
          'Learn how to protect systems, networks, and data from cyber threats and attacks.',
      },
      finance: {
        description:
          'Explore the world of finance, investment, financial markets, and financial planning.',
      },
      marketing: {
        description:
          'Learn digital marketing strategies, social media marketing, content marketing, and marketing analytics.',
      },
    },
  },

  // Specializations translations
  specializations: {
    common: {
      skills: 'Skills',
      learnMore: 'Learn More',
      featuredCourses: 'Featured Courses',
      applyNow: 'Apply Now',
      averageSalaryRange: 'Average Salary Range',
      level: {
        beginner: 'Beginner',
        intermediate: 'Intermediate',
        advanced: 'Advanced',
      },
      weeks: 'weeks',
      skillsTranslations: {
        webDevelopment: 'Web Development',
        appDevelopment: 'App Development',
        softwareEngineering: 'Software Engineering',
        artificialIntelligence: 'Artificial Intelligence',
        networkSecurity: 'Network Security',
        ethicalHacking: 'Ethical Hacking',
        securityAnalysis: 'Security Analysis',
        threatDetection: 'Threat Detection',
        financialAnalysis: 'Financial Analysis',
        investmentStrategies: 'Investment Strategies',
        riskManagement: 'Risk Management',
        marketResearch: 'Market Research',
        digitalMarketing: 'Digital Marketing',
        brandStrategy: 'Brand Strategy',
        socialMedia: 'Social Media',
        marketAnalysis: 'Market Analysis',
      },
    },
    programming: {
      title: 'Programming Specialization',
      subtitle: 'Build the Future with Code',
      description:
        'Master the art of programming and software development. Learn to build web applications, mobile apps, and AI solutions in our comprehensive programming program.',
      overview: 'Program Overview',
      overviewDescription:
        "Our programming specialization provides comprehensive training in software development, web technologies, and modern programming practices. You'll learn to build scalable applications and work with cutting-edge technologies.",
      programHighlights: 'Program Highlights',
      highlightsList: [
        'Hands-on experience with modern programming languages and frameworks',
        'Real-world project development and portfolio building',
        'Expert instruction from industry professionals',
        'Preparation for technical interviews and certifications',
        'Capstone project involving full-stack application development',
      ],
      careerOpportunities: 'Career Opportunities',
      careerOpportunitiesDescription:
        'Software developers are in high demand across all industries. Our graduates work in various roles building applications and systems that power the digital world.',
      learningApproach: 'Learning Approach',
      learningApproachDescription:
        'Our programming program combines theoretical computer science concepts with hands-on coding practice in real development environments.',
      learningMethods: [
        'Interactive coding labs and workshops',
        'Real-world project development',
        'Code review and pair programming',
        'Industry guest speakers and mentorship',
        'Collaborative team development projects',
      ],
      readyToStart: 'Ready to Start Your Programming Journey?',
      joinDescription:
        'Join our programming program and become part of the next generation of software developers.',
      courses: {
        fullStackWebDev: {
          title: 'Full-Stack Web Development',
          description:
            'Learn to build modern web applications using popular frameworks and technologies.',
          skills: [
            'HTML/CSS',
            'JavaScript',
            'React',
            'Node.js',
            'Database Design',
          ],
        },
        mobileAppDev: {
          title: 'Mobile App Development',
          description:
            'Create native and cross-platform mobile applications for iOS and Android.',
          skills: [
            'React Native',
            'Swift',
            'Kotlin',
            'Mobile UI/UX',
            'App Store Deployment',
          ],
        },
        dataStructures: {
          title: 'Data Structures & Algorithms',
          description:
            'Master fundamental computer science concepts for efficient problem solving.',
          skills: [
            'Algorithm Design',
            'Data Structures',
            'Problem Solving',
            'Code Optimization',
          ],
        },
        cloudComputing: {
          title: 'Cloud Computing & DevOps',
          description:
            'Learn cloud platforms and deployment strategies for scalable applications.',
          skills: [
            'AWS/Azure',
            'Docker',
            'Kubernetes',
            'CI/CD',
            'Infrastructure as Code',
          ],
        },
        aiMachineLearning: {
          title: 'AI & Machine Learning',
          description:
            'Build intelligent applications using machine learning and AI technologies.',
          skills: [
            'Python',
            'Machine Learning',
            'Deep Learning',
            'Data Analysis',
            'AI Frameworks',
          ],
        },
        softwareArchitecture: {
          title: 'Software Architecture & Design',
          description:
            'Learn to design scalable, maintainable software systems using modern architectural patterns.',
          skills: [
            'Design Patterns',
            'Microservices',
            'System Design',
            'Architecture Patterns',
          ],
        },
      },
      careers: {
        webDeveloper: {
          title: 'Web Developer',
          description:
            'Build and maintain websites and web applications for businesses and organizations.',
        },
        mobileDeveloper: {
          title: 'Mobile Developer',
          description:
            'Develop mobile applications for smartphones and tablets across different platforms.',
        },
        softwareEngineer: {
          title: 'Software Engineer',
          description:
            'Design and develop software systems and applications for various industries.',
        },
        aiEngineer: {
          title: 'AI Engineer',
          description:
            'Develop artificial intelligence solutions and machine learning models for business applications.',
        },
      },
    },
    cybersecurity: {
      title: 'Cybersecurity Specialization',
      subtitle: 'Protect the Digital World',
      description:
        'Master the art of cybersecurity and become a guardian of digital assets. Learn to identify, prevent, and respond to cyber threats in our comprehensive cybersecurity program.',
      overview: 'Program Overview',
      overviewDescription:
        "Our cybersecurity specialization provides comprehensive training in protecting digital assets, networks, and systems from cyber threats. You'll learn both defensive and offensive security techniques, preparing you for a successful career in cybersecurity.",
      programHighlights: 'Program Highlights',
      highlightsList: [
        'Hands-on experience with industry-standard security tools',
        'Real-world simulation environments for practical learning',
        'Expert instruction from certified security professionals',
        'Preparation for industry certifications (CISSP, CEH, Security+)',
        'Capstone project involving a comprehensive security assessment',
      ],
      careerOpportunities: 'Career Opportunities',
      careerOpportunitiesDescription:
        'Cybersecurity professionals are in high demand across all industries. Our graduates work in various roles protecting organizations from cyber threats.',
      learningApproach: 'Learning Approach',
      learningApproachDescription:
        'Our cybersecurity program combines theoretical knowledge with hands-on practical experience in simulated environments.',
      learningMethods: [
        'Interactive labs and simulations',
        'Real-world case studies',
        'Capture the Flag (CTF) competitions',
        'Industry guest speakers',
        'Collaborative team projects',
      ],
      readyToStart: 'Ready to Start Your Cybersecurity Journey?',
      joinDescription:
        'Join our cybersecurity program and become part of the next generation of digital defenders.',
      courses: {
        networkSecurity: {
          title: 'Network Security Fundamentals',
          description:
            'Learn to secure network infrastructure and protect against network-based attacks.',
          skills: [
            'Firewall Configuration',
            'VPN Setup',
            'Network Monitoring',
            'Intrusion Detection',
          ],
        },
        ethicalHacking: {
          title: 'Ethical Hacking & Penetration Testing',
          description:
            'Master the techniques used by ethical hackers to identify and exploit vulnerabilities.',
          skills: [
            'Vulnerability Assessment',
            'Penetration Testing',
            'Social Engineering',
            'Web Application Security',
          ],
        },
        securityOperations: {
          title: 'Security Operations Center (SOC)',
          description:
            'Learn to monitor, detect, and respond to security incidents in real-time.',
          skills: [
            'SIEM Tools',
            'Incident Response',
            'Threat Hunting',
            'Security Monitoring',
          ],
        },
        securityGovernance: {
          title: 'Security Governance & Compliance',
          description:
            'Understand security frameworks, policies, and regulatory compliance requirements.',
          skills: [
            'Risk Assessment',
            'Policy Development',
            'Compliance Auditing',
            'Security Frameworks',
          ],
        },
        secureDevelopment: {
          title: 'Secure Software Development',
          description:
            'Learn to build secure applications and implement security throughout the development lifecycle.',
          skills: [
            'Secure Coding',
            'Code Review',
            'Application Security',
            'DevSecOps',
          ],
        },
        digitalForensics: {
          title: 'Digital Forensics & Incident Response',
          description:
            'Master the techniques for investigating cyber crimes and responding to security incidents.',
          skills: [
            'Digital Evidence',
            'Forensic Analysis',
            'Incident Investigation',
            'Chain of Custody',
          ],
        },
      },
      careers: {
        securityAnalyst: {
          title: 'Security Analyst',
          description:
            'Monitor and analyze security events, investigate incidents, and implement security measures.',
        },
        penetrationTester: {
          title: 'Penetration Tester',
          description:
            'Conduct authorized simulated attacks to identify vulnerabilities in systems and networks.',
        },
        securityEngineer: {
          title: 'Security Engineer',
          description:
            'Design and implement security solutions, configure security tools, and maintain security infrastructure.',
        },
        incidentResponder: {
          title: 'Incident Response Specialist',
          description:
            'Lead response efforts during security incidents, conduct forensic analysis, and develop response procedures.',
        },
        securityConsultant: {
          title: 'Security Consultant',
          description:
            'Provide expert security advice to organizations, conduct security assessments, and develop security strategies.',
        },
        ciso: {
          title: 'Chief Information Security Officer (CISO)',
          description:
            'Lead organizational security strategy, manage security teams, and ensure compliance with security policies.',
        },
      },
    },
    finance: {
      title: 'Finance Specialization',
      subtitle: 'Master the World of Finance',
      description:
        'Dive deep into the world of finance and learn to make strategic financial decisions. Our comprehensive finance program covers everything from financial markets to corporate finance and fintech innovations.',
      overview: 'Program Overview',
      overviewDescription:
        "Our finance specialization provides comprehensive training in financial analysis, investment management, and financial planning. You'll learn to analyze markets, manage risks, and make strategic financial decisions that drive business success.",
      programHighlights: 'Program Highlights',
      highlightsList: [
        'Real-world financial modeling and analysis',
        'Access to professional financial software and tools',
        'Case studies from leading financial institutions',
        'Preparation for CFA and FRM certifications',
        'Capstone project with actual investment portfolio management',
      ],
      careerOpportunities: 'Career Opportunities',
      careerOpportunitiesDescription:
        'Finance professionals are essential in every industry. Our graduates work in investment banking, corporate finance, fintech, and financial consulting.',
      learningApproach: 'Learning Approach',
      learningApproachDescription:
        'Our finance program combines theoretical knowledge with practical application using real market data and financial tools.',
      learningMethods: [
        'Financial modeling workshops',
        'Market simulation exercises',
        'Industry expert guest lectures',
        'Portfolio management projects',
        'Financial case study analysis',
      ],
      readyToStart: 'Ready to Start Your Finance Journey?',
      joinDescription:
        'Join our finance program and become part of the next generation of financial leaders.',
      courses: {
        financialMarkets: {
          title: 'Financial Markets & Instruments',
          description:
            'Understand how financial markets operate and learn about various financial instruments.',
          skills: [
            'Market Analysis',
            'Trading Strategies',
            'Risk Assessment',
            'Portfolio Theory',
          ],
        },
        investmentManagement: {
          title: 'Investment Management',
          description:
            'Learn to build and manage investment portfolios for optimal returns.',
          skills: [
            'Portfolio Management',
            'Asset Allocation',
            'Performance Analysis',
            'Investment Research',
          ],
        },
        corporateFinance: {
          title: 'Corporate Finance',
          description:
            'Master corporate financial decision-making, capital structure, and valuation.',
          skills: [
            'Financial Planning',
            'Capital Budgeting',
            'Valuation Methods',
            'Mergers & Acquisitions',
          ],
        },
        fintech: {
          title: 'Financial Technology (FinTech)',
          description:
            'Explore the intersection of finance and technology in the digital age.',
          skills: [
            'Blockchain Technology',
            'Digital Payments',
            'Robo-Advisors',
            'Cryptocurrency',
          ],
        },
      },
      careers: {
        financialAnalyst: {
          title: 'Financial Analyst',
          description:
            'Analyze financial data and market trends to help companies make investment decisions.',
        },
        investmentManager: {
          title: 'Investment Manager',
          description:
            'Manage investment portfolios for individuals or institutions to meet financial goals.',
        },
        corporateFinance: {
          title: 'Corporate Finance Manager',
          description:
            'Oversee financial planning, capital budgeting, and financial risk management for corporations.',
        },
        riskManager: {
          title: 'Risk Manager',
          description:
            'Identify, analyze, and mitigate financial risks for organizations.',
        },
        fintechSpecialist: {
          title: 'FinTech Specialist',
          description:
            'Develop and implement innovative financial technology solutions.',
        },
      },
    },
  },

  // Learning Journey translations
  learningJourney: {
    title: 'Learning Journey',
    subtitle: 'Your Path to Success',
    description:
      'Our structured learning approach guides you through every step of your educational journey, from discovering your interests to building real-world projects.',
    steps: {
      0: {
        title: 'Discover Your Path',
        description:
          'Take assessments to identify your strengths and interests. Our AI-powered system helps you find the perfect specialization.',
      },
      1: {
        title: 'Learn',
        description:
          'Engage with interactive content and expert-led sessions. Our curriculum combines theory with practical examples.',
      },
      2: {
        title: 'Practice',
        description:
          'Work on real-world simulations and hands-on exercises. Apply your knowledge to solve actual problems.',
      },
      3: {
        title: 'Build Your Project',
        description:
          'Create a capstone project that showcases your skills. Work with mentors to develop a real-world application.',
      },
    },
  },

  // University Stats translations
  universityStats: {
    title: 'Masar by the Numbers',
    subtitle:
      'Our global impact and commitment to excellence has built a thriving educational community with impressive outcomes.',
    ourImpact: 'Our Impact',
    studentsEnrolled: 'Students Enrolled',
    coursesOffered: 'Courses Offered',
    employmentRate: 'Employment Rate',
    countriesRepresented: 'Countries Represented',
    universityDescription:
      'A globally accredited digital learning platform specializing in entrepreneurship, offering flexible learning that combines theory with hands-on experience.',
    footer: {
      explore: 'Explore',
      courses: 'Courses',
      programs: 'Programs',
      admissions: 'Admissions',
      aboutUs: 'About Us',
      contact: 'Contact',
      specializations: 'Specializations',
      programming: 'Programming',
      cybersecurity: 'Cybersecurity',
      finance: 'Finance',
      marketing: 'Marketing',
      viewAll: 'View All',
      contactUs: 'Contact Us',
      address: '1234 University Street\nDigital Campus, CA 90210',
      email: 'Email: <EMAIL>',
      phone: 'Phone: +****************',
      copyright: '© 2025 Masar. All rights reserved.',
      privacyPolicy: 'Privacy Policy',
      termsOfService: 'Terms of Service',
    },
  },

  // Auth translations
  auth: {
    signIn: 'Sign In',
    signUp: 'Sign Up',
    createAccount: 'Create Account',
    dontHaveAccount: "Don't have an account?",
    startYourJourney: 'Start Your Journey',
    forgotPassword: 'Forgot Password?',
    resetPassword: 'Reset Password',
    alreadyHaveAccount: 'Already have an account?',
    rememberMe: 'Remember Me',
    registration: {
      welcome: 'Welcome to Masar',
      askPassword: 'Create a secure password',
      askLastName: "What's your last name?",
      askEmail: "What's your email address?",
      askUsername: 'What would you like your username to be?',
      emailFormat: 'Please enter a valid email address',
      firstNamePlaceholder: 'Enter your first name',
      lastNamePlaceholder: 'Enter your last name',
      emailPlaceholder: 'Enter your email address',
      usernamePlaceholder: 'Choose a username',
      passwordPlaceholder: 'Create a password',
      confirmPasswordPlaceholder: 'Confirm your password',
      agreeToTerms: 'I agree to the Terms of Service and Privacy Policy',
      createAccount: 'Create Account',
      alreadyHaveAccount: 'Already have an account? Sign in',
      suggestedUsername: 'Suggested username',
      usernameLength: 'Username must be at least 3 characters long',
    },
  },

  // Assessment translations (additional)
  assessment: {
    findYourPath: 'Find Your Perfect Learning Path',
    description:
      'Answer a few questions about your interests, skills, and career goals to discover which specialization is the best fit for you.',
    startNow: 'Start Assessment',
    recommendedPath: 'Your Perfect Learning Path',
    recommendationExplanation:
      'Based on your answers, we recommend the following learning path:',
    analyzingAnswers: 'Analyzing your answers...',
    personalizedPath: 'Personalized Path',
    handsOnProjects: 'Hands-on Projects',
    expertGuidance: 'Expert Guidance',
    areasToDevelop: 'Areas to Develop',
    alternativePath: 'Alternative Path to Explore',
    explorePath: 'Explore This Path',
    resultsSaved: 'Your results are saved',
    takeAgain: 'Take Assessment Again',
    completePlacementAssessment: 'Complete Your Placement Assessment',
    placementAssessmentDescription:
      'Take the placement assessment to determine your level and get personalized course recommendations.',
    startAssessment: 'Start Assessment',
    title: 'Assessment',
    preview: 'Preview',
    levelMapping: 'Level Mapping'
  },

  // Path Advisor translations
  pathAdvisor: {
    title: 'Path Advisor',
    chatWithAdvisor: 'Chat with Path Advisor',
    getPersonalizedHelp: 'Get personalized guidance for your learning journey',
    onlineNow: 'Online Now',
    typeMessage: 'Type your message...',
    send: 'Send',
    welcomeMessage:
      "Hi! I'm your Path Advisor. I'm here to help you find the perfect learning path based on your interests and goals. How can I assist you today?",
    suggestedQuestions: {
      title: 'Suggested Questions',
      availablePaths: 'What learning paths are available?',
      findPath: 'How do I find the right path for me?',
      assessmentWork: 'How does the assessment work?',
    },
  },

  // Admin Dashboard translations
  admin: {
    dashboard: {
      title: 'Admin Dashboard',
      subtitle:
        'Comprehensive overview of university operations and statistics',
      totalUsers: 'Total Users',
      departments: 'Departments',
      assessments: 'Assessments',
      quickActions: 'Quick Actions',
      quickActionsDescription:
        'Access key administrative functions with one click',
      userDistribution: 'User Distribution',
      coursesByDepartment: 'Courses by Department',
      studentLevelDistribution: 'Student Level Distribution',
      studentPerformance: 'Student Performance',
      interactiveLearning: 'Interactive Learning',
      courseGenerator: 'Course Generator',
      notificationsList: 'Recent Notifications',
      calendarOverview: 'Calendar Overview',
      overviewCharts: 'Overview Charts',
      studentStatistics: 'Student Statistics',
      interactiveAndAI: 'Interactive Learning & AI',
      systemAndActivity: 'System & Activity',
      communicationCenter: 'Communication Center',
      planningAndAssistance: 'Planning & Assistance',

      // Quick Actions
      manageUsers: 'Manage Users',
      manageUsersTooltip: 'Manage Users',
      manageCourses: 'Manage Courses',
      manageCoursesTooltip: 'Manage Courses',
      departmentsTooltip: 'Manage Departments',
      studentLevels: 'Student Levels',
      studentLevelsTooltip: 'Manage Student Levels',
      pendingRegistrations: 'Pending Registrations',
      pendingRegistrationsTooltip: 'Manage Pending Registrations',
      assessmentQuestions: 'Assessment Questions',
      assessmentQuestionsTooltip: 'Manage Assessment Questions',
      aiDecisions: 'AI Decisions',
      aiDecisionsTooltip: 'Manage AI Decisions',
      aiAssessmentAnalysis: 'AI Assessment Analysis',
      aiAssessmentAnalysisTooltip: 'Manage AI Assessment Analysis',
      courseGeneratorTooltip: 'Manage Course Generator',
      interactiveLearningTooltip: 'Manage Interactive Learning',
      gradesOverview: 'Grades Overview',
      gradesOverviewTooltip: 'Manage Grades Overview',
      notifications: 'Notifications',
      notificationsTooltip: 'Manage Notifications',
      analytics: 'Analytics',
      analyticsTooltip: 'Manage Analytics',
      leaderboards: 'Leaderboards',
      leaderboardsTooltip: 'Manage Leaderboards',
      settings: 'Settings',
      settingsTooltip: 'Manage Settings',
      courses: 'Courses',

      // Dashboard Data Messages
      noCourseData: 'No course data available',
      noStudentLevelData: 'No student level data available',
      noInteractiveLearningData: 'No interactive learning data available',
      noAIGeneratedContentData: 'No AI-generated content data available',
      interactiveLearningEngagement: 'Interactive Learning Engagement',
      upcomingEvents: 'Upcoming Events',
      viewFullCalendar: 'View Full Calendar',
      assessmentManagement: 'Assessment Management',

      // System Status
      systemStatus: {
        title: 'System Status',
        systemUptime: 'System Uptime',
        days: 'days',
        hours: 'hours',
        minutes: 'minutes',
        seconds: 'seconds',
        databaseStatus: 'Database Status',
        connected: 'Connected',
        apiStatus: 'API Status',
        running: 'Running',
        lastBackup: 'Last Backup',
        storageUsage: 'Storage Usage',
        memoryUsage: 'Memory Usage',
        cpuUsage: 'CPU Usage',
        resourceUsage: 'Resource Usage',
        lastUpdated: 'Last Updated',
      },

      // Recent Activity
      recentActivity: {
        title: 'Recent Activity',
        user_login: 'User Login',
        course_created: 'Course Created',
        backup_completed: 'Backup Completed',
        assessment_submitted: 'Assessment Submitted',
        system_warning: 'System Warning',
      },

      // Recent Announcements
      recentAnnouncements: {
        noAnnouncements: 'No recent announcements',
      },

      // Recent Notifications
      recentNotifications: {
        by: 'by',
        unread: 'Unread',
      },

      // AI Assistant
      aiAssistant: {
        title: 'AI Assistant',
        beta: 'BETA',
        description:
          'Ask me anything about your university data, reports, or administrative tasks.',
        suggestionsTitle: 'Try asking about:',
        placeholder: 'Ask a question...',
        disclaimer:
          'Responses are generated by AI and may require verification.',
      },
    },
    statistics: {
      students: 'Students',
      professors: 'Professors',
      admins: 'Admins',
      totalUsers: 'Total Users',
      totalCourses: 'Total Courses',
      activeCourses: 'Active Courses',
      departments: 'Departments',
      assessments: 'Assessments',
      interactiveLessons: 'Interactive Lessons',
      aiGeneratedContent: 'AI-Generated Content',
    },
  },

  // Common translations
  common: {
    university: 'Masar',
    copyright: '© 2025 Masar. All rights reserved.',
    email: 'Email',
    password: 'Password',
    back: 'Back',
    next: 'Next',
    finish: 'Finish',
    learnMore: 'Learn More',
    refresh: 'Refresh',
    lastUpdated: 'Last updated',
    justNow: 'just now',
    timeAgo: {
      minutes: '{{count}} minutes ago',
      hours: '{{count}} hours ago',
      days: '{{count}} days ago',
    },
    actions: 'Actions',
    inactive: 'Inactive',
    viewDetails: 'View Details',
    active: 'Active',
    completion: 'Completion',
    lessons: 'Lessons',
    draft: 'Draft',
    refreshData: 'Refresh Data',
    switchToDark: 'Switch to Dark Mode',
    toggleDarkMode: 'Toggle Dark Mode',
    students: 'Students',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    logout: 'Logout',
  },

  // Dashboard translations
  dashboard: {
    studentPerformance: 'Student Performance',
    studentPerformanceDescription:
      'Real-time visualization of student performance metrics across different dimensions.',
    timeRange: 'Time Range',
    day: 'Day',
    week: 'Week',
    month: 'Month',
    year: 'Year',
    engagement: 'Engagement',
    completion: 'Completion',
    accuracy: 'Accuracy',
    retention: 'Retention',
    satisfaction: 'Satisfaction',
    recentNotifications: 'Recent Notifications',
  },

  // API Key Management translations
  apiKey: {
    management: 'API Key Management',
  },

  // User Management translations
  userManagement: {
    createUser: 'Create New User',
    editUser: 'Edit User',
    deleteUser: 'Delete User',
    userDetails: 'User Details',
    userList: 'User List',
    usersList: 'Users List',
    usersManagement: 'Users Management',
    username: 'Username',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    firstName: 'First Name',
    lastName: 'Last Name',
    name: 'Name',
    role: 'Role',
    dateOfBirth: 'Date of Birth',
    phoneNumber: 'Phone Number',
    address: 'Address',
    department: 'Department',
    studentId: 'Student ID',
    profilePicture: 'Profile Picture',
    uploadProfilePicture: 'Upload Profile Picture',
    selectedFile: 'Selected file',
    passwordsDontMatch: "Passwords don't match",
    failedToCreateUser: 'Failed to create user',
    userCreatedSuccessfully: 'User created successfully',
    duplicateUsername: 'A user with this username already exists',
    duplicateEmail: 'A user with this email already exists',
    duplicateUsernameAndEmail: 'Both username and email are already in use',
    validationErrors: 'Please correct the errors in the form',
    admin: 'Admin',
    professor: 'Professor',
    student: 'Student',
    addUser: 'Add User',
    searchUsers: 'Search Users',
    status: 'Status',
    active: 'Active',
    inactive: 'Inactive',
    actions: 'Actions',
    viewProfile: 'View Profile',
    manageLevel: 'Manage Level',
    confirmDelete: 'Are you sure you want to delete this user?',
    failedToDeleteUser: 'Failed to delete user',
    personalInfo: 'Personal Information',
    accountInfo: 'Account Information',
    additionalInfo: 'Additional Information',
    noUsers: 'No users found',
    noUsersFound: 'No users match your search criteria',
    tryDifferentSearch: 'Try a different search term',
    addFirstUser: 'Add your first user to get started',
  },

  // Interactive Learning translations
  interactiveLearning: {
    title: 'Interactive Learning',
    tabs: {
      dashboard: 'Dashboard',
      courses: 'Courses',
      learningPaths: 'Learning Paths',
    },
    stats: {
      totalCourses: 'Total Courses',
      across: 'across',
      totalLessons: 'Total Lessons',
      perCourse: 'per course',
      totalStudents: 'Total Students',
      completions: 'Completions',
      rate: 'rate',
      averageScore: 'Average Score',
      engagement: 'Engagement',
    },
    lessonsLabel: 'Lessons',
  },

  // Course translations
  courses: {
    newCourse: 'New Course',
  },

  // Course Details translations
  courseDetails: {
    loading: 'Loading...',
    error: {
      title: 'Error loading course',
    },
    backToCourses: 'Back to Courses',
  },

  // Chat translations
  chat: {
    standardChat: 'Standard Chat',
    multiAgentChat: 'Multi-Agent Chat',
    standardChatSubtitle: 'Quick AI conversations',
    untitledConversation: 'Untitled Conversation',
    chatHistory: 'Chat History',
    newConversation: 'New Conversation',
    noConversations: 'No conversations yet. Start a new chat!',
    startNewChat: 'Start a new chat to begin!',
    welcomeTitle: 'Welcome to AI Assistant',
    welcomeMessage: 'Select a conversation from the sidebar to start chatting, or create a new one to begin.',
    startNewConversation: 'Start New Conversation',
    createNewConversation: 'Create New Conversation',
    conversationTitle: 'Conversation Title',
    enterTitle: 'Enter a title for your conversation...',
    defaultConversationTitle: 'ChatGPT',
    newChat: 'New Chat',
  },

  // Chat and AI Agent translations
  'Standard Chat': 'Standard Chat',
  'Multi-Agent Chat': 'Multi-Agent Chat',
  'Chat Demo': 'Chat Demo',
  'Agent Status': 'Agent Status',
  'Agent Testing': 'Agent Testing',
  'AI Course Recommendations': 'AI Course Recommendations',

  // Student Dashboard translations
  student: {
    dashboard: {
      title: 'Student Dashboard',
      currentLevel: 'Current Level',
      levelTooltip: 'Your current academic level',
      coursesTooltip: 'Number of courses you are enrolled in',
      gpaTooltip: 'Your current Grade Point Average',
      assignmentsTooltip: 'Pending assignments to complete',
      attendanceTooltip: 'Your attendance percentage',
      completedCourses: 'Completed Courses',
      completedTooltip: 'Number of courses you have completed',
      noProgressData: 'No progress data available',
      upcomingEvents: 'Upcoming Events',
      noUpcomingEvents: 'No upcoming events',
      noNotifications: 'No new notifications',
    },
    learningPath: {
      title: 'Learning Path',
      dashboardDescription:
        'View your personalized learning journey based on your current level and track your progress through courses.',
      viewPath: 'View Learning Path',
    },
    level: {
      beginner: 'BEGINNER',
      intermediate: 'INTERMEDIATE',
      advanced: 'ADVANCED',
    },
    gpa: 'GPA',
    exploreCourses: 'Explore Courses',
    allCourses: 'All Courses',
  },

  // Navigation translations
  navigation: {
    courses: 'Courses',
    assignments: 'Assignments',
    attendance: 'Attendance',
    dashboard: 'Dashboard',
    studentCourses: 'My Courses',
    interactiveLearning: 'Interactive Learning',
    learningInsights: 'Learning Insights',
    grades: 'Grades',
    users: 'Users',
    assessmentQuestions: 'Assessment Questions',
    studentLevels: 'Student Levels',
    settings: 'Settings',
    apiKey: {
      management: 'API Key Management'
    }
  },

  // AI Agent translations
  aiAgent: {
    courseAssistant: 'Course Assistant',
  },

  // Activity translations
  activity: {
    title: 'Activity',
  },

  // Student Progress translations
  studentProgress: {
    title: 'Student Progress',
    weeklyProgress: 'Weekly Progress',
    learningGoals: 'Learning Goals',
    achievementRate: 'Achievement Rate',
    completedCourses: 'Completed Courses',
    status: {
      completed: 'Completed',
    },
  },

  // Calendar translations
  calendar: {
    week: 'Week',
    month: 'Month',
  },

  // Notifications translations
  notifications: {
    viewAll: 'View All',
  },

  // Sports translations
  sports: {
    title: 'Sports',
  },

  // Training List translations
  trainingList: {
    title: 'Training List',
  },
};

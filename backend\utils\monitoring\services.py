"""ConsolidatedMonitoringServicesThismoduleconsolidatesmonitoringfunctionalityfrom:-monitoring_utils.py-analytics_utils.py-performance_monitoring.py"""import timeimport loggingfromtypingimportDictAnyOptionalListfromdatetimeimport datetimetimedeltafrom django.utilsimport timezonefrom django.dbimportconnectionfromfunctoolsimportwrapslogger=logging.getLogger(__name__)classMonitoringService:"""Unifiedmonitoringserviceforperformanceanalyticsandsystemhealth."""def__init__(self):self.metrics={}self.start_time=timezone.now()deftrack_performance(selfoperation:strduration:floatmetadata:Dict=None):"""Trackperformancemetricsforoperations"""metric_key=f"performance:{operation}"ifmetric_keynotinself.metrics:self.metrics[metric_key]={'count':0'total_duration':0'min_duration':float('inf')'max_duration':0'last_execution':None}metric=self.metrics[metric_key]metric['count']+=1metric['total_duration']+=durationmetric['min_duration']=min(metric['min_duration']duration)metric['max_duration']=max(metric['max_duration']duration)metric['last_execution']=timezone.now()ifmetadata:metric['metadata']=metadata#Logslowoperationsifduration>5.0:#5secondsthresholdlogger.warning(f"Slowoperationdetected:{operation}took{duration:.2f}s")deftrack_user_activity(selfuser_id:intactivity:strdetails:Dict=None):"""Trackuseractivityforanalytics"""activity_key=f"user_activity:{user_id}"ifactivity_keynotinself.metrics:self.metrics[activity_key]=[]self.metrics[activity_key].append({'activity':activity'timestamp':timezone.now()'details':detailsor{}})#Keeponlylast100activitiesperuserinmemoryiflen(self.metrics[activity_key])>100:self.metrics[activity_key]=self.metrics[activity_key][-100:]deftrack_api_usage(selfendpoint:strmethod:strstatus_code:intduration:float):"""TrackAPIendpointusage"""api_key=f"api:{endpoint}:{method}"ifapi_keynotinself.metrics:self.metrics[api_key]={'total_requests':0'success_count':0'error_count':0'total_duration':0'status_codes':{}}metric=self.metrics[api_key]metric['total_requests']+=1metric['total_duration']+=durationif200<=status_code<300:metric['success_count']+=1else:metric['error_count']+=1#Trackstatuscodedistributionstatus_key=str(status_code)metric['status_codes'][status_key]=metric['status_codes'].get(status_key0)+1deftrack_ai_usage(selfmodel:stroperation:strtokens_used:intcost:float=0):"""TrackAIserviceusage"""ai_key=f"ai:{model}:{operation}"ifai_keynotinself.metrics:self.metrics[ai_key]={'requests':0'total_tokens':0'total_cost':0'last_used':None}metric=self.metrics[ai_key]metric['requests']+=1metric['total_tokens']+=tokens_usedmetric['total_cost']+=costmetric['last_used']=timezone.now()defget_performance_summary(self)->Dict[strAny]:"""Getperformancemetricssummary"""performance_metrics={k:vforkvinself.metrics.items()ifk.startswith('performance:')}summary={}forkeymetricinperformance_metrics.items():operation=key.replace('performance:''')avg_duration=metric['total_duration']/metric['count']ifmetric['count']>0else0summary[operation]={'count':metric['count']'avg_duration':round(avg_duration3)'min_duration':metric['min_duration']ifmetric['min_duration']!=float('inf')else0'max_duration':metric['max_duration']'last_execution':metric['last_execution']}returnsummarydefget_api_summary(self)->Dict[strAny]:"""GetAPIusagesummary"""api_metrics={k:vforkvinself.metrics.items()ifk.startswith('api:')}summary={}forkeymetricinapi_metrics.items():endpoint_method=key.replace('api:''')avg_duration=metric['total_duration']/metric['total_requests']ifmetric['total_requests']>0else0success_rate=(metric['success_count']/metric['total_requests']*100)ifmetric['total_requests']>0else0summary[endpoint_method]={'total_requests':metric['total_requests']'success_rate':round(success_rate2)'avg_duration':round(avg_duration3)'status_codes':metric['status_codes']}returnsummarydefget_ai_usage_summary(self)->Dict[strAny]:"""GetAIusagesummary"""ai_metrics={k:vforkvinself.metrics.items()ifk.startswith('ai:')}summary={}total_cost=0total_tokens=0forkeymetricinai_metrics.items():model_operation=key.replace('ai:''')summary[model_operation]={'requests':metric['requests']'total_tokens':metric['total_tokens']'total_cost':round(metric['total_cost']4)'avg_tokens_per_request':round(metric['total_tokens']/metric['requests']2)ifmetric['requests']>0else0'last_used':metric['last_used']}total_cost+=metric['total_cost']total_tokens+=metric['total_tokens']summary['_totals']={'total_cost':round(total_cost4)'total_tokens':total_tokens}returnsummarydefget_system_health(self)->Dict[strAny]:"""Getsystemhealthmetrics"""#Databaseconnectioncheckdb_healthy=Truedb_query_time=Nonetry:start_time=time.time()withconnection.cursor()ascursor:cursor.execute("SELECT1")db_query_time=time.time()-start_timeexceptExceptionase:db_healthy=Falselogger.error(f"Databasehealthcheckfailed:{e}")#Memoryusage(simplified)importpsutilmemory_usage=psutil.virtual_memory().percentuptime=timezone.now()-self.start_timereturn{'status':'healthy'ifdb_healthyandmemory_usage<90else'warning''uptime_seconds':int(uptime.total_seconds())'database':{'healthy':db_healthy'query_time':round(db_query_time3)ifdb_query_timeelseNone}'memory':{'usage_percent':memory_usage}'timestamp':timezone.now()}defclear_metrics(self):"""Clearallmetrics(usefulfortesting)"""self.metrics.clear()logger.info("Monitoringmetricscleared")defmonitor_performance(operation_name:str=None):"""DecoratortomonitorfunctionperformanceArgs:operation_name:Customnamefortheoperation(defaultstofunctionname)"""defdecorator(func):@wraps(func)defwrapper(*args**kwargs):operation=operation_nameorf"{func.__module__}.{func.__name__}"start_time=time.time()try:result=func(*args**kwargs)duration=time.time()-start_timemonitoring_service.track_performance(operationduration{'success':True})returnresultexceptExceptionase:duration=time.time()-start_timemonitoring_service.track_performance(operationduration{'success':False'error':str(e)})raisereturnwrapperreturndecoratordefmonitor_api_call(func):"""DecoratortomonitorAPIcalls"""@wraps(func)defwrapper(request*args**kwargs):start_time=time.time()try:response=func(request*args**kwargs)duration=time.time()-start_time#Extractendpointinfoendpoint=request.pathmethod=request.methodstatus_code=getattr(response'status_code'200)monitoring_service.track_api_usage(endpointmethodstatus_codeduration)returnresponseexceptExceptionase:duration=time.time()-start_timemonitoring_service.track_api_usage(request.pathrequest.method500duration)raisereturnwrapper#Globalmonitoringserviceinstance(lazyinitialization)_monitoring_service_instance=Nonedefget_monitoring_service():"""Getorcreatetheglobalmonitoringserviceinstance"""global_monitoring_service_instanceif_monitoring_service_instanceisNone:_monitoring_service_instance=MonitoringService()return_monitoring_service_instance#Forbackwardscompatibilitymonitoring_service=get_monitoring_service
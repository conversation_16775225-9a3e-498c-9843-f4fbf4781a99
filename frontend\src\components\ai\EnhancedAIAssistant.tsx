import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  TextField,
  Button,
  Chip,
  CircularProgress,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Psychology as PsychologyIcon,
  School as SchoolIcon,
  Science as ScienceIcon,
  Language as LanguageIcon,
  Assessment as AssessmentIcon,
  Lightbulb as LightbulbIcon,
  Create as CreateIcon,
  Analytics as AnalyticsIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import aiAssistantService, {
  SuggestionItem,
  AiAssistantResponse,
} from '../../services/aiAssistantService';
import aiAgentService, {
  AgentResponse,
  TutoringRequest,
} from '../../services/aiAgentService';
import unifiedAiService from '../../services/unifiedAiService';
import {
  createUserErrorMessage,
  logServiceError,
  shouldUseFallback,
  createFallbackResponse,
} from '../../services/utils/errorHandling';

interface EnhancedAIAssistantProps {
  studentId?: number;
  courseId?: number;
  context?: Record<string, any>;
}

const EnhancedAIAssistant: React.FC<EnhancedAIAssistantProps> = ({
  studentId,
  courseId,
  context = {},
}) => {
  const [question, setQuestion] = useState('');
  const [response, setResponse] = useState<AiAssistantResponse | null>(null);
  const [agentResponse, setAgentResponse] = useState<AgentResponse | null>(
    null
  );
  const [suggestions, setSuggestions] = useState<SuggestionItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string>('');
  const [activeAgent, setActiveAgent] = useState<string>('general');
  const [capabilities, setCapabilities] = useState<any>(null);

  // Agent icons mapping
  const agentIcons: Record<string, React.ReactNode> = {
    general: <PsychologyIcon />,
    math: <SchoolIcon />,
    science: <ScienceIcon />,
    language: <LanguageIcon />,
    assessment: <AssessmentIcon />,
    advisor: <LightbulbIcon />,
    content: <CreateIcon />,
  };

  useEffect(() => {
    loadSuggestions();
    loadCapabilities();
  }, []);

  const loadSuggestions = async () => {
    try {
      const userContext = { studentId, courseId, ...context };
      const suggestionsData =
        await aiAssistantService.getPersonalizedSuggestions(userContext);
      setSuggestions(suggestionsData);
    } catch (error) {
      logServiceError(error, 'Loading personalized suggestions');

      // Use standardized fallback logic
      if (shouldUseFallback(error)) {
        try {
          const fallbackSuggestions = await aiAssistantService.getSuggestions();
          setSuggestions(fallbackSuggestions);
        } catch (fallbackError) {
          logServiceError(fallbackError, 'Loading fallback suggestions');
          // Use hardcoded fallback
          const hardcodedFallback = createFallbackResponse('suggestions');
          setSuggestions(hardcodedFallback.suggestions || []);
        }
      } else {
        setError(createUserErrorMessage(error, 'Loading suggestions'));
      }
    }
  };

  const loadCapabilities = async () => {
    try {
      const caps = await unifiedAiService.getCapabilities();
      setCapabilities(caps);
    } catch (error) {
      logServiceError(error, 'Loading AI capabilities');

      if (shouldUseFallback(error)) {
        // Use fallback capabilities
        const fallbackCaps = createFallbackResponse('general', {
          available_agents: ['general'],
          specializations: { general: ['basic assistance'] },
          ml_available: false,
          langgraph_available: false,
        });
        setCapabilities(fallbackCaps);
      }
    }
  };

  const handleAskQuestion = async () => {
    if (!question.trim()) return;

    setLoading(true);
    setError(null);
    setResponse(null);
    setAgentResponse(null);

    try {
      if (activeAgent === 'general') {
        // Use general AI assistant
        const result = await aiAssistantService.askQuestion(
          question,
          { studentId, courseId, ...context },
          sessionId
        );
        setResponse(result);
        setSessionId(result.session_id);
      } else {
        // Use specialized agent
        const request: TutoringRequest = {
          question,
          subject: activeAgent,
          student_level: context.student_level || 'intermediate',
          context: { studentId, courseId, ...context },
        };

        const result = await aiAgentService.getTutoringHelp(request, context);
        setAgentResponse(result);
      }
    } catch (error: any) {
      logServiceError(error, 'Processing AI question');
      const userMessage = createUserErrorMessage(
        error,
        'Processing your question'
      );
      setError(userMessage);

      // If fallback is appropriate, provide a fallback response
      if (shouldUseFallback(error)) {
        const fallbackResponse = createFallbackResponse('content', {
          answer:
            "I apologize, but I'm currently unable to process your question. Please try again in a few moments.",
          confidence_score: 0,
          response_time: 0,
          session_id: sessionId || '',
          suggestions: [
            'Try rephrasing your question',
            'Check back in a few minutes',
            'Contact support if the issue persists',
          ],
        });
        setResponse(fallbackResponse as AiAssistantResponse);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSuggestionClick = (suggestion: SuggestionItem) => {
    setQuestion(suggestion.text);
  };

  const handleAgentChange = (agent: string) => {
    setActiveAgent(agent);
    setResponse(null);
    setAgentResponse(null);
  };

  const renderResponse = () => {
    const currentResponse = response || agentResponse;
    if (!currentResponse) return null;

    return (
      <Card sx={{ mt: 2 }}>
        <CardContent>
          <Typography variant='h6' gutterBottom>
            {activeAgent === 'general'
              ? 'AI Assistant Response'
              : `${activeAgent.charAt(0).toUpperCase() + activeAgent.slice(1)} Tutor Response`}
          </Typography>

          <Typography variant='body1' paragraph>
            {currentResponse.content || currentResponse.answer}
          </Typography>

          {currentResponse.confidence && (
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant='body2' color='text.secondary'>
                Confidence: {Math.round(currentResponse.confidence * 100)}%
              </Typography>
            </Box>
          )}

          {currentResponse.recommendations &&
            currentResponse.recommendations.length > 0 && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant='subtitle2'>Recommendations</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  {currentResponse.recommendations.map((rec, index) => (
                    <Chip
                      key={index}
                      label={rec.description || rec.type}
                      variant='outlined'
                      size='small'
                      sx={{ mr: 1, mb: 1 }}
                    />
                  ))}
                </AccordionDetails>
              </Accordion>
            )}

          {currentResponse.reasoning &&
            currentResponse.reasoning.length > 0 && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant='subtitle2'>Reasoning</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  {currentResponse.reasoning.map((reason, index) => (
                    <Typography key={index} variant='body2' paragraph>
                      • {reason}
                    </Typography>
                  ))}
                </AccordionDetails>
              </Accordion>
            )}
        </CardContent>
      </Card>
    );
  };

  return (
    <Box>
      <Card>
        <CardContent>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              mb: 2,
            }}
          >
            <Typography variant='h5' component='h2'>
              Enhanced AI Assistant
            </Typography>
            <Box>
              <Tooltip title='Refresh suggestions'>
                <IconButton onClick={loadSuggestions} size='small'>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title='View analytics'>
                <IconButton size='small'>
                  <AnalyticsIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Agent Selection */}
          <Typography variant='subtitle2' gutterBottom>
            Choose AI Agent:
          </Typography>
          <Grid container spacing={1} sx={{ mb: 2 }}>
            {[
              'general',
              'math',
              'science',
              'language',
              'assessment',
              'advisor',
              'content',
            ].map(agent => (
              <Grid item key={agent}>
                <Chip
                  icon={agentIcons[agent]}
                  label={agent.charAt(0).toUpperCase() + agent.slice(1)}
                  onClick={() => handleAgentChange(agent)}
                  color={activeAgent === agent ? 'primary' : 'default'}
                  variant={activeAgent === agent ? 'filled' : 'outlined'}
                />
              </Grid>
            ))}
          </Grid>

          {/* Question Input */}
          <TextField
            fullWidth
            multiline
            rows={3}
            variant='outlined'
            label='Ask your question...'
            value={question}
            onChange={e => setQuestion(e.target.value)}
            sx={{ mb: 2 }}
          />

          <Button
            variant='contained'
            onClick={handleAskQuestion}
            disabled={loading || !question.trim()}
            fullWidth
            sx={{ mb: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : 'Ask AI'}
          </Button>

          {/* Error Display */}
          {error && (
            <Alert severity='error' sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {/* Suggestions */}
          {suggestions.length > 0 && (
            <Box>
              <Typography variant='subtitle2' gutterBottom>
                Suggested Questions:
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {suggestions.slice(0, 6).map(suggestion => (
                  <Chip
                    key={suggestion.id}
                    label={suggestion.text}
                    onClick={() => handleSuggestionClick(suggestion)}
                    variant='outlined'
                    size='small'
                  />
                ))}
              </Box>
            </Box>
          )}

          {/* AI Capabilities Status */}
          {capabilities && (
            <Box
              sx={{ mt: 2, p: 1, bgcolor: 'background.paper', borderRadius: 1 }}
            >
              <Typography variant='caption' color='text.secondary'>
                AI Status: {capabilities.available_agents?.length || 0} agents
                available
                {capabilities.ml_available && ' • ML Analytics enabled'}
                {capabilities.langgraph_available &&
                  ' • Advanced workflows enabled'}
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Response Display */}
      {renderResponse()}
    </Box>
  );
};

export default EnhancedAIAssistant;

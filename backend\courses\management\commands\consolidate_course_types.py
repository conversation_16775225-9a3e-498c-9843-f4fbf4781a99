"""DjangoManagementCommandtoConsolidateCourseDataThiscommandmigratesdatafromtheoldduplicatedstructuretothenewconsolidatedstructure.Iteliminatesduplicationbetweencoursesinteractive_learningandcourse_generatorapps."""fromdjango.core.management.baseimportBaseCommandfromdjango.dbimporttransactionfromdjango.utilsimporttimezonefromdjango.contrib.authimportget_user_modelfromcourses.modelsimportCourseInteractiveCourseFeaturesAIGeneratedContentfrominteractive_learning.modelsimportInteractiveCourseVersionfromcourse_generator.modelsimportGeneratedCourseContentUser=get_user_model()classCommand(BaseCommand):help='Consolidatecoursedatafrom3separateappsintounifiedstructure'defadd_arguments(selfparser):parser.add_argument('--dry-run'action='store_true'help='Runindry-runmode(noactualchanges)')parser.add_argument('--verbose'action='store_true'help='Verboseoutput')defhandle(self*args**options):self.dry_run=options['dry_run']self.verbose=options['verbose']ifself.dry_run:self.stdout.write(self.style.WARNING('🔍DRYRUNMODE-Nochangeswillbemade'))self.stdout.write(self.style.SUCCESS('🚀StartingCourseDataConsolidation'))try:withtransaction.atomic():#Step1:ConsolidateInteractiveCourseDataself.consolidate_interactive_courses()#Step2:ConsolidateAI-GeneratedCourseDataself.consolidate_ai_courses()#Step3:UpdateCoursePrimaryTypesself.update_course_types()#Step4:GenerateStatisticsself.generate_statistics()ifself.dry_run:self.stdout.write(self.style.WARNING('🔄DRYRUNCOMPLETE-Rollingbacktransaction'))raiseException("Dryrun-rollbacktransaction")self.stdout.write(self.style.SUCCESS('✅Coursedataconsolidationcompletedsuccessfully!'))exceptExceptionase:if"Dryrun"instr(e):self.stdout.write(self.style.SUCCESS('✅Dryruncompleted-nochangesmade'))else:self.stdout.write(self.style.ERROR(f'❌Errorduringconsolidation:{e}'))raisedefconsolidate_interactive_courses(self):"""MigratedatafromInteractiveCourseVersiontoInteractiveCourseFeatures"""self.stdout.write('\n📱ConsolidatingInteractiveCourseData...')interactive_versions=InteractiveCourseVersion.objects.all()created_count=0updated_count=0forversionininteractive_versions:try:#Getorcreatethebasecoursebase_course=version.base_courseifnotbase_course:self.stdout.write(self.style.WARNING(f'⚠️Nobasecourseforinteractiveversion{version.id}'))continue#CreateorupdateInteractiveCourseFeaturesfeaturescreated=InteractiveCourseFeatures.objects.get_or_create(course=base_coursedefaults={'points_per_lesson':getattr(version'points_per_lesson'10)'streak_bonus_multiplier':getattr(version'streak_bonus_multiplier'1.5)'daily_goal_options':getattr(version'daily_goal_options'[])'badges_enabled':getattr(version'badges_enabled'True)'leaderboard_enabled':getattr(version'leaderboard_enabled'True)'achievements_enabled':getattr(version'achievements_enabled'True)'difficulty_levels':getattr(version'difficulty_levels'[])'learning_paths':getattr(version'learning_paths'[])'engagement_metrics':getattr(version'engagement_metrics'{})'interactive_content':getattr(version'interactive_content'[])'sync_with_course':getattr(version'sync_with_course'True)'last_synced_at':getattr(version'last_synced_at'None)})ifcreated:created_count+=1else:updated_count+=1ifself.verbose:action="Created"ifcreatedelse"Updated"self.stdout.write(f'{action}interactivefeaturesfor{base_course.course_code}')exceptExceptionase:self.stdout.write(self.style.ERROR(f'❌Errorprocessinginteractiveversion{version.id}:{e}'))self.stdout.write(self.style.SUCCESS(f'✅Interactivecourses:{created_count}created{updated_count}updated'))defconsolidate_ai_courses(self):"""MigratedatafromGeneratedCourseContenttoAIGeneratedContent"""self.stdout.write('\n🤖ConsolidatingAI-GeneratedCourseData...')generated_contents=GeneratedCourseContent.objects.all()created_count=0updated_count=0forcontentingenerated_contents:try:#Getthebasecoursebase_course=content.base_courseifnotbase_course:self.stdout.write(self.style.WARNING(f'⚠️Nobasecourseforgeneratedcontent{content.id}'))continue#CreateorupdateAIGeneratedContentai_contentcreated=AIGeneratedContent.objects.get_or_create(course=base_coursedefaults={'ai_provider':getattr(content'provider_name''Unknown')'ai_model':'gemini-2.0-flash'#Defaultforexistingcontent'generation_prompt':'Migratedfromoldsystem''ai_options':getattr(content'ai_options'{})'difficulty_level':getattr(content'ai_difficulty_override''MIXED')or'MIXED''learning_style':'BALANCED'#Default'weekly_schedule':getattr(content'weekly_schedule'[])'lesson_plans':getattr(content'lesson_plans'[])'assessment_methods':getattr(content'assessment_methods'[])'recommended_readings':getattr(content'recommended_readings'[])'sample_quizzes':getattr(content'sample_quizzes'[])'project_ideas':getattr(content'project_ideas'[])'teaching_tips':getattr(content'teaching_tips'[])'skills_gained':getattr(content'skills_gained'[])'additional_resources':getattr(content'additional_resources'[])'raw_ai_response':getattr(content'raw_ai_response'{})'generated_at':getattr(content'generated_at'timezone.now())'created_by':getattr(content'created_by'None)'human_review_status':'APPROVED'#Assumeexistingcontentisapproved})ifcreated:created_count+=1else:updated_count+=1ifself.verbose:action="Created"ifcreatedelse"Updated"self.stdout.write(f'{action}AIcontentfor{base_course.course_code}')exceptExceptionase:self.stdout.write(self.style.ERROR(f'❌ErrorprocessingAIcontent{content.id}:{e}'))self.stdout.write(self.style.SUCCESS(f'✅AIcourses:{created_count}created{updated_count}updated'))defupdate_course_types(self):"""Updateprimary_typeforallcoursesbasedontheirfeatures"""self.stdout.write('\n🔄UpdatingCoursePrimaryTypes...')updated_count=0forcourseinCourse.objects.all():old_type=course.primary_typenew_type='STANDARD'#Determinenewtypebasedonfeatureshas_interactive=hasattr(course'interactive_features')has_ai=hasattr(course'ai_content')ifhas_interactiveandhas_ai:new_type='HYBRID'elifhas_interactive:new_type='INTERACTIVE'elifhas_ai:new_type='AI_GENERATED'#Updateifchangedifold_type!=new_type:course.primary_type=new_typecourse.save(update_fields=['primary_type'])updated_count+=1ifself.verbose:self.stdout.write(f'Updated{course.course_code}:{old_type}→{new_type}')self.stdout.write(self.style.SUCCESS(f'✅Coursetypesupdated:{updated_count}courses'))defgenerate_statistics(self):"""Generateanddisplayconsolidationstatistics"""self.stdout.write('\n📊ConsolidationStatistics:')total_courses=Course.objects.count()standard_courses=Course.objects.filter(primary_type='STANDARD').count()interactive_courses=Course.objects.filter(primary_type='INTERACTIVE').count()ai_courses=Course.objects.filter(primary_type='AI_GENERATED').count()hybrid_courses=Course.objects.filter(primary_type='HYBRID').count()interactive_features=InteractiveCourseFeatures.objects.count()ai_content=AIGeneratedContent.objects.count()self.stdout.write(f'📚TotalCourses:{total_courses}')self.stdout.write(f'📖StandardCourses:{standard_courses}')self.stdout.write(f'🎮InteractiveCourses:{interactive_courses}')self.stdout.write(f'🤖AI-GeneratedCourses:{ai_courses}')self.stdout.write(f'🔄HybridCourses:{hybrid_courses}')self.stdout.write(f'✨InteractiveFeaturesCreated:{interactive_features}')self.stdout.write(f'🧠AIContentCreated:{ai_content}')#Calculatepercentagesiftotal_courses>0:self.stdout.write('\n📈Distribution:')self.stdout.write(f'Standard:{(standard_courses/total_courses)*100:.1f}%')self.stdout.write(f'Interactive:{(interactive_courses/total_courses)*100:.1f}%')self.stdout.write(f'AI-Generated:{(ai_courses/total_courses)*100:.1f}%')self.stdout.write(f'Hybrid:{(hybrid_courses/total_courses)*100:.1f}%')defcheck_data_integrity(self):"""Checkdataintegrityafterconsolidation"""self.stdout.write('\n🔍CheckingDataIntegrity...')issues=[]#Checkforcourseswithfeaturesbutwrongflagscourses_with_interactive_features=Course.objects.filter(interactive_features__isnull=Falsehas_interactive_content=False)ifcourses_with_interactive_features.exists():issues.append(f'{courses_with_interactive_features.count()}courseshaveinteractivefeaturesbuthas_interactive_content=False')courses_with_ai_content=Course.objects.filter(ai_content__isnull=Falsehas_ai_content=False)ifcourses_with_ai_content.exists():issues.append(f'{courses_with_ai_content.count()}courseshaveAIcontentbuthas_ai_content=False')ifissues:self.stdout.write(self.style.WARNING('⚠️Dataintegrityissuesfound:'))forissueinissues:self.stdout.write(f'-{issue}')else:self.stdout.write(self.style.SUCCESS('✅Nodataintegrityissuesfound'))
"""Course Type Service

This service manages the different course types (standard, interactive, AI-generated) and provides
methods to get courses based on admin settings and student preferences.
"""

from typing import Dict, List, Optional

from django.db.models import QuerySet
from django.contrib.auth import get_user_model
from django.db.models import Q, Case, When, Value, CharField
from django.utils.translation import gettext_lazy as _

from courses.models import Course
from courses.models.visibility_settings import CourseVisibilitySettings, StudentCoursePreference
from course_generator.models import GeneratedCourseContent

User = get_user_model()


class CourseTypeService:
    """
    Service for managing different course types and their visibility to students.
    """

    @staticmethod
    def get_visible_courses_for_student(student: User, level_filter: Optional[int] = None) -> QuerySet:
        """
        Get all courses visible to a specific student based on admin settings and
        the student's level and preferences.
        
        Args:
            student: Student user instance
            level_filter: Optional level filter (1-5)
            
        Returns:
            QuerySet of Course objects visible to the student
        """
        # Get visibility settings
        settings = CourseVisibilitySettings.get_current_settings()
        
        if not settings.courses_enabled:
            return Course.objects.none()
        
        # Get visible course types
        visible_types = settings.get_visible_course_types()
        
        if not visible_types:
            return Course.objects.none()
        
        # Build base queryset
        queryset = Course.objects.filter(is_active=True)
        
        # Apply level filter if provided
        if level_filter:
            queryset = queryset.filter(required_level__lte=level_filter)
        else:
            # Filter by student's current level
            try:
                student_level = student.level_profile.current_level
                queryset = queryset.filter(required_level__lte=student_level)
            except:
                # If no level profile, show beginner courses only
                queryset = queryset.filter(required_level__lte=1)
        
        # Apply course type filters
        type_filters = Q()
        
        if 'STANDARD' in visible_types:
            # Standard courses: courses without interactive or AI content
            type_filters |= Q(has_interactive_content=False, has_ai_content=False)
        
        if 'INTERACTIVE' in visible_types:
            # Interactive courses: courses with interactive content
            type_filters |= Q(has_interactive_content=True)
        
        if 'AI_GENERATED' in visible_types:
            # AI-generated courses: courses with AI content
            type_filters |= Q(has_ai_content=True)
        
        queryset = queryset.filter(type_filters)
        
        # Add course type annotation for frontend
        queryset = queryset.annotate(
            course_type=Case(
                When(has_interactive_content=True, has_ai_content=True, then=Value('HYBRID')),
                When(has_interactive_content=True, then=Value('INTERACTIVE')),
                When(has_ai_content=True, then=Value('AI_GENERATED')),
                default=Value('STANDARD'),
                output_field=CharField()
            )
        )
        
        return queryset
    
    @staticmethod
    def get_course_details_with_type(course_id: int, student: User) -> Dict:
        """
        Get detailed course information including type-specific data.
        
        Args:
            course_id: Course ID
            student: Student user instance
            
        Returns:
            Dictionary with course details and type-specific information
        """
        try:
            course = Course.objects.get(id=course_id, is_active=True)
        except Course.DoesNotExist:
            return {'error': 'Course not found'}
        
        # Check if student can access this course
        visible_courses = CourseTypeService.get_visible_courses_for_student(student)
        
        if not visible_courses.filter(id=course_id).exists():
            return {'error': 'Course not accessible'}
        
        # Base course data
        course_data = {
            'id': course.id,
            'title': course.title,
            'description': course.description,
            'course_code': course.course_code,
            'credits': course.credits,
            'semester': course.semester,
            'academic_year': course.academic_year,
            'required_level': course.required_level,
            'recommended_level': course.recommended_level,
            'instructor': {
                'id': course.instructor.id if course.instructor else None,
                'name': course.instructor.get_full_name() if course.instructor else 'TBA'
            },
            'has_interactive_content': course.has_interactive_content,
            'has_ai_content': course.has_ai_content,
            'available_types': []
        }
        
        # Determine course type and available alternatives
        settings = CourseVisibilitySettings.get_current_settings()
        
        # Check for AI-generated version
        if course.has_ai_content and settings.show_ai_generated_courses:
            try:
                ai_version = course.generated_content
                course_data['available_types'].append({
                    'type': 'AI_GENERATED',
                    'title': course.title,
                    'description': course.description,
                    'features': [
                        'AI-personalized content',
                        'Adaptive difficulty',
                        'Personalized recommendations',
                        'Smart content generation'
                    ]
                })
                
                if course.has_ai_content:
                    course_data['ai_data'] = {
                        'provider': ai_version.provider_name,
                        'version': ai_version.provider_version,
                        'generated_at': ai_version.generated_at.isoformat() if ai_version.generated_at else None
                    }
            except:
                pass
        
        # Always include standard version if visible
        if settings.show_standard_courses:
            course_data['available_types'].append({
                'type': 'STANDARD',
                'title': course.title,
                'description': course.description,
                'features': [
                    'Traditional learning approach',
                    'Structured curriculum',
                    'Standard assessments',
                    'Classic educational methods'
                ]
            })
        
        # Get student's preferred type
        try:
            preferences = student.course_preferences
            preferred_type = preferences.get_effective_course_type()
            course_data['recommended_type'] = preferred_type
        except:
            # No preferences set, use admin default
            try:
                student_level = student.level_profile.current_level
                course_data['recommended_type'] = settings.get_default_course_type_for_level(student_level)
            except:
                course_data['recommended_type'] = 'STANDARD'
        
        return course_data
    
    @staticmethod
    def get_student_course_preferences(student: User) -> Dict:
        """
        Get or create student course preferences.
        
        Args:
            student: Student user instance
            
        Returns:
            Dictionary with student preferences
        """
        preferences, created = StudentCoursePreference.objects.get_or_create(
            student=student,
            defaults={
                'preferred_course_type': None,
                'prefer_gamification': True,
                'prefer_ai_personalization': True
            }
        )
        
        settings = CourseVisibilitySettings.get_current_settings()
        
        return {
            'preferred_course_type': preferences.preferred_course_type,
            'effective_course_type': preferences.get_effective_course_type(),
            'prefer_gamification': preferences.prefer_gamification,
            'prefer_ai_personalization': preferences.prefer_ai_personalization,
            'can_change_preferences': settings.allow_student_choice and not settings.force_course_type,
            'available_types': settings.get_visible_course_types(),
            'created': created
        }
    
    @staticmethod
    def update_student_course_preferences(student: User, preferences_data: Dict) -> Dict:
        """
        Update student course preferences.
        
        Args:
            student: Student user instance
            preferences_data: Dictionary with preference updates
            
        Returns:
            Dictionary with updated preferences or error
        """
        settings = CourseVisibilitySettings.get_current_settings()
        
        if not settings.allow_student_choice or settings.force_course_type:
            return {'error': 'Student preferences are not allowed by admin settings'}
        
        preferences, _ = StudentCoursePreference.objects.get_or_create(student=student)
        
        # Update preferences
        if 'preferred_course_type' in preferences_data:
            course_type = preferences_data['preferred_course_type']
            if course_type and settings.is_course_type_visible(course_type):
                preferences.preferred_course_type = course_type
            elif course_type is None:
                preferences.preferred_course_type = None
            else:
                return {'error': f'Course type {course_type} is not available'}
        
        if 'prefer_gamification' in preferences_data:
            preferences.prefer_gamification = bool(preferences_data['prefer_gamification'])
        
        if 'prefer_ai_personalization' in preferences_data:
            preferences.prefer_ai_personalization = bool(preferences_data['prefer_ai_personalization'])
        
        preferences.save()
        
        return CourseTypeService.get_student_course_preferences(student)
    
    @staticmethod
    def get_admin_visibility_settings() -> Dict:
        """
        Get current admin visibility settings.
        
        Returns:
            Dictionary with current settings
        """
        settings = CourseVisibilitySettings.get_current_settings()
        
        return {
            'courses_enabled': settings.courses_enabled,
            'show_standard_courses': settings.show_standard_courses,
            'show_interactive_courses': settings.show_interactive_courses,
            'show_ai_generated_courses': settings.show_ai_generated_courses,
            'default_course_type': settings.default_course_type,
            'allow_student_choice': settings.allow_student_choice,
            'force_course_type': settings.force_course_type,
            'beginner_default_type': settings.beginner_default_type,
            'intermediate_default_type': settings.intermediate_default_type,
            'advanced_default_type': settings.advanced_default_type,
            'visible_types': settings.get_visible_course_types()
        }
    
    @staticmethod
    def get_course_statistics() -> Dict:
        """
        Get statistics about course types in the system.
        
        Returns:
            Dictionary with course statistics
        """
        total_courses = Course.objects.filter(is_active=True).count()
        standard_courses = Course.objects.filter(
            is_active=True,
            has_interactive_content=False,
            has_ai_content=False
        ).count()
        interactive_courses = Course.objects.filter(
            is_active=True,
            has_interactive_content=True
        ).count()
        ai_courses = Course.objects.filter(
            is_active=True,
            has_ai_content=True
        ).count()
        hybrid_courses = Course.objects.filter(
            is_active=True,
            has_interactive_content=True,
            has_ai_content=True
        ).count()
        
        return {
            'total_courses': total_courses,
            'standard_courses': standard_courses,
            'interactive_courses': interactive_courses,
            'ai_generated_courses': ai_courses,
            'hybrid_courses': hybrid_courses,
            'course_breakdown': {
                'standard_percentage': (standard_courses / total_courses * 100) if total_courses > 0 else 0,
                'interactive_percentage': (interactive_courses / total_courses * 100) if total_courses > 0 else 0,
                'ai_percentage': (ai_courses / total_courses * 100) if total_courses > 0 else 0,
                'hybrid_percentage': (hybrid_courses / total_courses * 100) if total_courses > 0 else 0
            }
        }

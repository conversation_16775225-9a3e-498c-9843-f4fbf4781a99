/**
 * Enhanced Analytics Dashboard
 * Integrates with the new backend analytics API
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Chip,
  Avatar,
  LinearProgress,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
  Alert,
  Skeleton,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  People,
  School,
  Assessment,
  Schedule,
  Refresh,
  Settings,
  NotificationsActive,
  Analytics,
} from '@mui/icons-material';
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  RadialLinearScale,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler,
} from 'chart.js';
import { format, parseISO } from 'date-fns';

import { enhancedAnalyticsService, DashboardAnalytics, RealTimeStats } from '../../services/enhancedAnalyticsService';
import { realTimeService, RealTimeEventHandlers } from '../../services/realTimeService';
import { useToast } from '../../hooks/useToast';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  RadialLinearScale,
  Title,
  ChartTooltip,
  Legend,
  Filler
);

interface EnhancedAnalyticsDashboardProps {
  isAdmin?: boolean;
  userId?: number;
}

const EnhancedAnalyticsDashboard: React.FC<EnhancedAnalyticsDashboardProps> = ({
  isAdmin = false,
  userId,
}) => {
  const [analytics, setAnalytics] = useState<DashboardAnalytics | null>(null);
  const [realTimeStats, setRealTimeStats] = useState<RealTimeStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [connectionStatus, setConnectionStatus] = useState(false);

  const { showToast } = useToast();

  // Load analytics data
  const loadAnalytics = useCallback(async (useCache = true) => {
    try {
      setError(null);
      
      const data = isAdmin 
        ? await enhancedAnalyticsService.getAdminAnalytics()
        : await enhancedAnalyticsService.getDashboardAnalytics(useCache);
      
      if (data) {
        setAnalytics(data);
        setLastUpdated(new Date());
      } else {
        setError('Failed to load analytics data');
      }
    } catch (err) {
      setError('Error loading analytics');
      console.error('Analytics error:', err);
    } finally {
      setLoading(false);
    }
  }, [isAdmin]);

  // Load real-time stats
  const loadRealTimeStats = useCallback(async () => {
    try {
      const stats = await enhancedAnalyticsService.getRealTimeStats();
      if (stats) {
        setRealTimeStats(stats);
      }
    } catch (err) {
      console.error('Real-time stats error:', err);
    }
  }, []);

  // Set up real-time event handlers
  useEffect(() => {
    const handlers: RealTimeEventHandlers = {
      onAnalyticsUpdate: (data) => {
        setRealTimeStats(data);
        if (autoRefresh) {
          loadAnalytics(false); // Force refresh
        }
      },
      onConnectionEstablished: () => {
        setConnectionStatus(true);
        showToast('Connected to real-time updates', 'success');
      },
      onConnectionLost: () => {
        setConnectionStatus(false);
        showToast('Lost connection to real-time updates', 'warning');
      },
      onError: (error) => {
        showToast(`Real-time error: ${error}`, 'error');
      },
    };

    realTimeService.setEventHandlers(handlers);

    return () => {
      realTimeService.setEventHandlers({});
    };
  }, [autoRefresh, loadAnalytics, showToast]);

  // Initial load and auto-refresh setup
  useEffect(() => {
    loadAnalytics();
    loadRealTimeStats();

    let interval: NodeJS.Timeout | null = null;
    if (autoRefresh) {
      interval = setInterval(() => {
        loadRealTimeStats();
        realTimeService.requestAnalyticsUpdate();
      }, 30000); // 30 seconds
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [loadAnalytics, loadRealTimeStats, autoRefresh]);

  // Handle manual refresh
  const handleRefresh = () => {
    setLoading(true);
    loadAnalytics(false);
    loadRealTimeStats();
  };

  // Chart configurations
  const usageTrendsChartData = {
    labels: analytics?.trends?.map(trend => 
      format(parseISO(trend.date), 'MMM dd')
    ) || [],
    datasets: [
      {
        label: 'Daily Logins',
        data: analytics?.trends?.map(trend => trend.logins) || [],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.4,
        fill: true,
      },
    ],
  };

  const userDistributionChartData = {
    labels: Object.keys(analytics?.user_distribution || {}),
    datasets: [
      {
        data: Object.values(analytics?.user_distribution || {}),
        backgroundColor: [
          '#FF6384',
          '#36A2EB',
          '#FFCE56',
          '#4BC0C0',
          '#9966FF',
        ],
      },
    ],
  };

  const courseCompletionChartData = {
    labels: analytics?.course_analytics?.popular_courses?.map(course => 
      course.course_name.length > 20 
        ? course.course_name.substring(0, 20) + '...'
        : course.course_name
    ) || [],
    datasets: [
      {
        label: 'Completion Rate (%)',
        data: analytics?.course_analytics?.popular_courses?.map(course => 
          course.completion_rate
        ) || [],
        backgroundColor: 'rgba(54, 162, 235, 0.8)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
      },
    ],
  };

  if (loading && !analytics) {
    return (
      <Box sx={{ p: 3 }}>
        <Grid container spacing={3}>
          {[1, 2, 3, 4].map((item) => (
            <Grid item xs={12} sm={6} md={3} key={item}>
              <Card>
                <CardContent>
                  <Skeleton variant="text" height={40} />
                  <Skeleton variant="text" height={60} />
                  <Skeleton variant="rectangular" height={100} />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          {isAdmin ? 'Admin Analytics Dashboard' : 'Analytics Dashboard'}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Chip
            icon={<NotificationsActive />}
            label={connectionStatus ? 'Connected' : 'Disconnected'}
            color={connectionStatus ? 'success' : 'error'}
            variant="outlined"
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
              />
            }
            label="Auto Refresh"
          />
          
          <Tooltip title="Refresh Data">
            <IconButton onClick={handleRefresh} disabled={loading}>
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Real-time Stats */}
      {realTimeStats && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Real-time Statistics
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6} sm={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {realTimeStats.users_online}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Users Online
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="secondary">
                    {realTimeStats.active_sessions}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Sessions
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    Last Updated: {format(parseISO(realTimeStats.timestamp), 'HH:mm:ss')}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Overview Cards */}
      {analytics && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <People />
                  </Avatar>
                  <Box>
                    <Typography variant="h4">
                      {analytics.overview.total_users.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Users
                    </Typography>
                    <Typography variant="body2" color="success.main">
                      +{analytics.overview.active_users_week} active this week
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                    <School />
                  </Avatar>
                  <Box>
                    <Typography variant="h4">
                      {analytics.overview.total_courses.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Courses
                    </Typography>
                    <Typography variant="body2" color="success.main">
                      {analytics.overview.active_courses} active
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                    <Assessment />
                  </Avatar>
                  <Box>
                    <Typography variant="h4">
                      {analytics.overview.total_assessments.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Assessments
                    </Typography>
                    <Typography variant="body2" color="info.main">
                      {analytics.overview.assessment_responses_week} responses this week
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                    <TrendingUp />
                  </Avatar>
                  <Box>
                    <Typography variant="h4">
                      {analytics.performance.user_engagement_score.toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Engagement Score
                    </Typography>
                    <Typography variant="body2" color="success.main">
                      {analytics.performance.avg_completion_rate.toFixed(1)}% completion rate
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Charts */}
      {analytics && (
        <Grid container spacing={3}>
          {/* Usage Trends */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Usage Trends (30 Days)
                </Typography>
                {analytics.trends && analytics.trends.length > 0 ? (
                  <Line
                    data={usageTrendsChartData}
                    options={{
                      responsive: true,
                      plugins: {
                        legend: {
                          position: 'top' as const,
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                        },
                      },
                    }}
                  />
                ) : (
                  <Typography>No trend data available</Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* User Distribution */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  User Distribution
                </Typography>
                {Object.keys(analytics.user_distribution).length > 0 ? (
                  <Doughnut
                    data={userDistributionChartData}
                    options={{
                      responsive: true,
                      plugins: {
                        legend: {
                          position: 'bottom' as const,
                        },
                      },
                    }}
                  />
                ) : (
                  <Typography>No distribution data available</Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Course Completion Rates */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Course Completion Rates
                </Typography>
                {analytics.course_analytics?.popular_courses?.length > 0 ? (
                  <Bar
                    data={courseCompletionChartData}
                    options={{
                      responsive: true,
                      plugins: {
                        legend: {
                          position: 'top' as const,
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          max: 100,
                        },
                      },
                    }}
                  />
                ) : (
                  <Typography>No course data available</Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Assessment Analytics */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Assessment Performance
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Average Score
                  </Typography>
                  <Typography variant="h4" color="primary">
                    {analytics.assessment_analytics?.average_score?.toFixed(1) || 0}%
                  </Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Recent Performance
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={analytics.assessment_analytics?.recent_performance?.avg_score || 0}
                    sx={{ mt: 1 }}
                  />
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    {analytics.assessment_analytics?.recent_performance?.total_responses || 0} responses this week
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Course Categories */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Course Categories
                </Typography>
                {Object.entries(analytics.course_analytics?.course_categories || {}).map(([category, count]) => (
                  <Box key={category} sx={{ mb: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">{category}</Typography>
                      <Typography variant="body2">{count}</Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={(count / Math.max(...Object.values(analytics.course_analytics?.course_categories || {}))) * 100}
                      sx={{ mt: 0.5 }}
                    />
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Last Updated */}
      {lastUpdated && (
        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Last updated: {format(lastUpdated, 'PPpp')}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default EnhancedAnalyticsDashboard;

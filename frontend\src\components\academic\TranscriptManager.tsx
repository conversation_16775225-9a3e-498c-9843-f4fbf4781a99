/**
 * 📋 Transcript Management Component
 * 
 * Features:
 * - Get student transcripts (with term-by-term breakdown)
 * - Generate official transcripts (PDF)
 * - Add/update transcript records (admin functions)
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  CircularProgress,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { 
  Description, 
  Download, 
  Add, 
  ExpandMore,
  School,
  Grade,
  TrendingUp
} from '@mui/icons-material';
import { academicService, Transcript, TranscriptRecord } from '../../services/academicService';

interface TranscriptManagerProps {
  studentId?: number;
  isAdmin?: boolean;
}

const TranscriptManager: React.FC<TranscriptManagerProps> = ({ studentId, isAdmin = false }) => {
  const [transcript, setTranscript] = useState<Transcript | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [formData, setFormData] = useState({
    student: studentId || '',
    course: '',
    term: '',
    grade: '',
    credits_earned: 0,
    is_transfer: false
  });

  useEffect(() => {
    loadTranscript();
  }, [studentId]);

  const loadTranscript = async () => {
    try {
      setLoading(true);
      const transcriptData = await academicService.getTranscript(studentId);
      setTranscript(transcriptData);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to load transcript');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateOfficialTranscript = async () => {
    try {
      const pdfBlob = await academicService.generateOfficialTranscript(studentId);
      
      // Create download link
      const url = window.URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `transcript_${transcript?.student.username || 'student'}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      setError(err.message || 'Failed to generate official transcript');
    }
  };

  const handleAddRecord = async () => {
    try {
      await academicService.addTranscriptRecord(formData);
      setOpenAddDialog(false);
      loadTranscript();
      resetForm();
    } catch (err: any) {
      setError(err.message || 'Failed to add transcript record');
    }
  };

  const resetForm = () => {
    setFormData({
      student: studentId || '',
      course: '',
      term: '',
      grade: '',
      credits_earned: 0,
      is_transfer: false
    });
  };

  const getGradeColor = (grade: string) => {
    const gradeValue = grade.charAt(0);
    switch (gradeValue) {
      case 'A': return 'success';
      case 'B': return 'info';
      case 'C': return 'warning';
      case 'D': return 'error';
      case 'F': return 'error';
      default: return 'default';
    }
  };

  const calculateTermGPA = (records: TranscriptRecord[]) => {
    return academicService.calculateGPA(records);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (!transcript) {
    return (
      <Alert severity="info">
        No transcript data found for this student.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Description color="primary" />
          Transcript Management
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={handleGenerateOfficialTranscript}
          >
            Download PDF
          </Button>
          {isAdmin && (
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setOpenAddDialog(true)}
            >
              Add Record
            </Button>
          )}
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Student Summary */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="primary" gutterBottom>
                Student Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">Name</Typography>
                  <Typography variant="h6">
                    {transcript.student.first_name} {transcript.student.last_name}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">Email</Typography>
                  <Typography>{transcript.student.email}</Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="primary" gutterBottom>
                Academic Summary
              </Typography>
              <Box display="flex" flexDirection="column" gap={1}>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="body2" color="text.secondary">
                    Cumulative GPA:
                  </Typography>
                  <Chip 
                    label={academicService.formatGPA(transcript.cumulative_gpa)} 
                    color={transcript.cumulative_gpa >= 3.0 ? 'success' : 'warning'}
                    icon={<TrendingUp />}
                  />
                </Box>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="body2" color="text.secondary">
                    Total Credits:
                  </Typography>
                  <Typography>{transcript.total_credits}</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Terms and Courses */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <School />
            Academic Record by Term
          </Typography>

          {transcript.terms.map((termData, index) => (
            <Accordion key={index} sx={{ mt: 2 }}>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
                  <Typography variant="h6">{termData.term.name}</Typography>
                  <Box display="flex" gap={2} mr={2}>
                    <Chip 
                      label={`GPA: ${academicService.formatGPA(termData.term_gpa)}`}
                      color={termData.term_gpa >= 3.0 ? 'success' : 'warning'}
                      size="small"
                    />
                    <Chip 
                      label={`${termData.term_credits} Credits`}
                      variant="outlined"
                      size="small"
                    />
                  </Box>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <TableContainer component={Paper}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Course Code</TableCell>
                        <TableCell>Course Title</TableCell>
                        <TableCell align="center">Credits</TableCell>
                        <TableCell align="center">Grade</TableCell>
                        <TableCell align="center">Grade Points</TableCell>
                        <TableCell align="center">Transfer</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {termData.records.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell>{record.course.course_code}</TableCell>
                          <TableCell>{record.course.title}</TableCell>
                          <TableCell align="center">{record.course.credits}</TableCell>
                          <TableCell align="center">
                            <Chip 
                              label={record.grade}
                              color={getGradeColor(record.grade)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell align="center">{record.grade_points}</TableCell>
                          <TableCell align="center">
                            {record.is_transfer && (
                              <Chip label="Transfer" variant="outlined" size="small" />
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </AccordionDetails>
            </Accordion>
          ))}
        </CardContent>
      </Card>

      {/* Add Record Dialog */}
      <Dialog open={openAddDialog} onClose={() => setOpenAddDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add Transcript Record</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Course ID"
                value={formData.course}
                onChange={(e) => setFormData({ ...formData, course: e.target.value })}
                placeholder="Enter course ID"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Term ID"
                value={formData.term}
                onChange={(e) => setFormData({ ...formData, term: e.target.value })}
                placeholder="Enter term ID"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Grade</InputLabel>
                <Select
                  value={formData.grade}
                  onChange={(e) => setFormData({ ...formData, grade: e.target.value })}
                >
                  <MenuItem value="A+">A+</MenuItem>
                  <MenuItem value="A">A</MenuItem>
                  <MenuItem value="A-">A-</MenuItem>
                  <MenuItem value="B+">B+</MenuItem>
                  <MenuItem value="B">B</MenuItem>
                  <MenuItem value="B-">B-</MenuItem>
                  <MenuItem value="C+">C+</MenuItem>
                  <MenuItem value="C">C</MenuItem>
                  <MenuItem value="C-">C-</MenuItem>
                  <MenuItem value="D+">D+</MenuItem>
                  <MenuItem value="D">D</MenuItem>
                  <MenuItem value="F">F</MenuItem>
                  <MenuItem value="W">W</MenuItem>
                  <MenuItem value="I">I</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Credits Earned"
                type="number"
                value={formData.credits_earned}
                onChange={(e) => setFormData({ ...formData, credits_earned: parseInt(e.target.value) })}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Transfer Credit</InputLabel>
                <Select
                  value={formData.is_transfer}
                  onChange={(e) => setFormData({ ...formData, is_transfer: e.target.value as boolean })}
                >
                  <MenuItem value={false}>No</MenuItem>
                  <MenuItem value={true}>Yes</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenAddDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleAddRecord}>
            Add Record
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TranscriptManager;

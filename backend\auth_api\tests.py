"""
Comprehensive tests for authentication API.
"""

import pytest
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.test import APITestCase
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch, Mock
import json

from .security_enhancements import (
    PasswordSecurityValidator,
    SessionSecurity,
    ThreatDetection,
    SecurityAuditLogger
)

User = get_user_model()


@pytest.mark.auth
class TestUserRegistration:
    """Test user registration functionality."""
    
    def test_valid_registration(self, api_client):
        """Test successful user registration."""
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'SecurePass123!',
            'password_confirm': 'SecurePass123!',
            'first_name': 'New',
            'last_name': 'User',
            'role': 'STUDENT'
        }
        
        url = reverse('auth_api:register')
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert User.objects.filter(username='newuser').exists()
        assert 'access' in response.data
        assert 'refresh' in response.data
    
    def test_registration_with_weak_password(self, api_client):
        """Test registration with weak password."""
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': '123',
            'password_confirm': '123',
            'first_name': 'New',
            'last_name': 'User',
            'role': 'STUDENT'
        }
        
        url = reverse('auth_api:register')
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'password' in response.data
    
    def test_registration_with_existing_username(self, api_client, test_user):
        """Test registration with existing username."""
        data = {
            'username': test_user.username,
            'email': '<EMAIL>',
            'password': 'SecurePass123!',
            'password_confirm': 'SecurePass123!',
            'first_name': 'New',
            'last_name': 'User',
            'role': 'STUDENT'
        }
        
        url = reverse('auth_api:register')
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'username' in response.data
    
    def test_registration_with_existing_email(self, api_client, test_user):
        """Test registration with existing email."""
        data = {
            'username': 'newuser',
            'email': test_user.email,
            'password': 'SecurePass123!',
            'password_confirm': 'SecurePass123!',
            'first_name': 'New',
            'last_name': 'User',
            'role': 'STUDENT'
        }
        
        url = reverse('auth_api:register')
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'email' in response.data
    
    def test_registration_password_mismatch(self, api_client):
        """Test registration with password mismatch."""
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'SecurePass123!',
            'password_confirm': 'DifferentPass123!',
            'first_name': 'New',
            'last_name': 'User',
            'role': 'STUDENT'
        }
        
        url = reverse('auth_api:register')
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST


@pytest.mark.auth
class TestUserLogin:
    """Test user login functionality."""
    
    def test_valid_login(self, api_client, test_user):
        """Test successful login."""
        data = {
            'username': test_user.username,
            'password': 'testpass123'
        }
        
        url = reverse('auth_api:login')
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'access' in response.data
        assert 'refresh' in response.data
        assert 'user' in response.data
    
    def test_invalid_credentials(self, api_client, test_user):
        """Test login with invalid credentials."""
        data = {
            'username': test_user.username,
            'password': 'wrongpassword'
        }
        
        url = reverse('auth_api:login')
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_login_nonexistent_user(self, api_client):
        """Test login with nonexistent user."""
        data = {
            'username': 'nonexistent',
            'password': 'password123'
        }
        
        url = reverse('auth_api:login')
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    @patch('auth_api.views.ThreatDetection.detect_brute_force')
    def test_brute_force_protection(self, mock_detect, api_client, test_user):
        """Test brute force attack protection."""
        mock_detect.return_value = {
            'is_brute_force': True,
            'risk_level': 'HIGH'
        }
        
        data = {
            'username': test_user.username,
            'password': 'wrongpassword'
        }
        
        url = reverse('auth_api:login')
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_429_TOO_MANY_REQUESTS


@pytest.mark.auth
class TestTokenRefresh:
    """Test JWT token refresh functionality."""
    
    def test_valid_token_refresh(self, api_client, test_user):
        """Test successful token refresh."""
        refresh = RefreshToken.for_user(test_user)
        
        data = {'refresh': str(refresh)}
        url = reverse('auth_api:token_refresh')
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'access' in response.data
    
    def test_invalid_refresh_token(self, api_client):
        """Test refresh with invalid token."""
        data = {'refresh': 'invalid_token'}
        url = reverse('auth_api:token_refresh')
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


@pytest.mark.auth
class TestUserProfile:
    """Test user profile functionality."""
    
    def test_get_user_profile(self, authenticated_client, test_user):
        """Test getting user profile."""
        url = reverse('auth_api:profile')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['username'] == test_user.username
        assert response.data['email'] == test_user.email
    
    def test_update_user_profile(self, authenticated_client, test_user):
        """Test updating user profile."""
        data = {
            'first_name': 'Updated',
            'last_name': 'Name'
        }
        
        url = reverse('auth_api:profile')
        response = authenticated_client.patch(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        test_user.refresh_from_db()
        assert test_user.first_name == 'Updated'
        assert test_user.last_name == 'Name'
    
    def test_profile_unauthorized(self, api_client):
        """Test accessing profile without authentication."""
        url = reverse('auth_api:profile')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


@pytest.mark.security
class TestPasswordSecurity:
    """Test password security validation."""
    
    def test_password_strength_validation(self):
        """Test password strength validation."""
        validator = PasswordSecurityValidator()
        
        # Strong password
        is_valid, issues = validator.validate_password_strength('SecurePass123!')
        assert is_valid
        assert len(issues) == 0
        
        # Weak password
        is_valid, issues = validator.validate_password_strength('123')
        assert not is_valid
        assert len(issues) > 0
    
    def test_password_with_personal_info(self):
        """Test password validation with personal information."""
        validator = PasswordSecurityValidator()
        user_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
        
        # Password containing username
        is_valid, issues = validator.validate_password_strength(
            'testuser123!', user_data
        )
        assert not is_valid
        assert any('personal information' in issue for issue in issues)


@pytest.mark.security
class TestSessionSecurity:
    """Test session security features."""
    
    def test_session_fingerprint_generation(self):
        """Test session fingerprint generation."""
        from django.test import RequestFactory
        
        factory = RequestFactory()
        request = factory.get('/')
        request.META['HTTP_USER_AGENT'] = 'Test Browser'
        request.META['HTTP_ACCEPT_LANGUAGE'] = 'en-US'
        request.META['HTTP_ACCEPT_ENCODING'] = 'gzip'
        
        fingerprint = SessionSecurity.create_session_fingerprint(request)
        assert isinstance(fingerprint, str)
        assert len(fingerprint) == 64  # SHA256 hex digest length
    
    def test_session_token_generation(self):
        """Test secure session token generation."""
        token = SessionSecurity.generate_session_token()
        assert isinstance(token, str)
        assert len(token) > 20  # URL-safe base64 encoded


@pytest.mark.security
class TestThreatDetection:
    """Test threat detection system."""
    
    @patch('django.core.cache.cache.get')
    def test_brute_force_detection(self, mock_cache_get):
        """Test brute force attack detection."""
        # Simulate high number of attempts
        mock_cache_get.return_value = 15
        
        result = ThreatDetection.detect_brute_force('***********', 'testuser')
        
        assert result['is_brute_force']
        assert result['risk_level'] == 'HIGH'
    
    @patch('django.core.cache.cache.get')
    def test_credential_stuffing_detection(self, mock_cache_get):
        """Test credential stuffing detection."""
        from django.test import RequestFactory
        
        # Simulate many rapid attempts
        mock_cache_get.return_value = [1, 2, 3, 4, 5] * 5  # 25 attempts
        
        factory = RequestFactory()
        request = factory.post('/login/')
        request.META['REMOTE_ADDR'] = '***********'
        
        is_stuffing = ThreatDetection.detect_credential_stuffing(request)
        assert is_stuffing


@pytest.mark.integration
class TestAuthenticationIntegration(APITestCase):
    """Integration tests for authentication system."""
    
    def setUp(self):
        """Set up test data."""
        self.user_data = {
            'username': 'integrationuser',
            'email': '<EMAIL>',
            'password': 'SecurePass123!',
            'password_confirm': 'SecurePass123!',
            'first_name': 'Integration',
            'last_name': 'User',
            'role': 'STUDENT'
        }
    
    def test_complete_auth_flow(self):
        """Test complete authentication flow."""
        # 1. Register user
        register_url = reverse('auth_api:register')
        register_response = self.client.post(
            register_url, self.user_data, format='json'
        )
        self.assertEqual(register_response.status_code, status.HTTP_201_CREATED)
        
        # 2. Login with new user
        login_data = {
            'username': self.user_data['username'],
            'password': self.user_data['password']
        }
        login_url = reverse('auth_api:login')
        login_response = self.client.post(login_url, login_data, format='json')
        self.assertEqual(login_response.status_code, status.HTTP_200_OK)
        
        # 3. Access protected resource
        access_token = login_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        profile_url = reverse('auth_api:profile')
        profile_response = self.client.get(profile_url)
        self.assertEqual(profile_response.status_code, status.HTTP_200_OK)
        
        # 4. Refresh token
        refresh_token = login_response.data['refresh']
        refresh_url = reverse('auth_api:token_refresh')
        refresh_response = self.client.post(
            refresh_url, {'refresh': refresh_token}, format='json'
        )
        self.assertEqual(refresh_response.status_code, status.HTTP_200_OK)
    
    def test_logout_and_token_blacklist(self):
        """Test logout and token blacklisting."""
        # Create and login user
        user = User.objects.create_user(
            username='logoutuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        
        # Logout
        logout_url = reverse('auth_api:logout')
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        logout_response = self.client.post(
            logout_url, {'refresh': str(refresh)}, format='json'
        )
        self.assertEqual(logout_response.status_code, status.HTTP_200_OK)
        
        # Try to use blacklisted token
        profile_url = reverse('auth_api:profile')
        profile_response = self.client.get(profile_url)
        self.assertEqual(profile_response.status_code, status.HTTP_401_UNAUTHORIZED)

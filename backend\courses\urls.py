"""
Consolidated URL configuration for the courses app.
This replaces multiple URL files with a single organized structure.
"""

from django.urls import include, path
from rest_framework.routers import DefaultRouter
from rest_framework_nested.routers import NestedDefaultRouter

# Import main viewsets
from .views.course_views import CourseViewSet
from .views.standardized_views import StandardizedCourseViewSet
from .views.standardized_student_views import (
    StandardizedStudentCourseViewSet,
    StandardizedStudentMaterialViewSet,
    StandardizedStudentAssignmentViewSet,
    StandardizedStudentAttendanceViewSet,
    StandardizedStudentProgressViewSet
)

# Import admin views
from .admin_views import (
    CourseViewSet as AdminCourseViewSet,
    AdminCourseAssignmentViewSet,
    AdminDepartmentListView,
    AdminDepartmentDetailView,
    AdminDashboardStatsView
)

# Import professor views
from .professor_views import (
    ProfessorCourseViewSet,
    Professor<PERSON><PERSON><PERSON>ViewSet,
    <PERSON><PERSON><PERSON><PERSON>View,
    get_attendance_analytics,
    get_students_at_risk,
    export_attendance
)

# Import office hours views
from .office_hours_views import OfficeHoursViewSet

# Import content and analytics views
from .views.content_preferences_views import (
    CourseContentTypePreferenceViewSet,
    StudentContentTypePreferenceViewSet
)
from .views.content_analytics_views import ContentAnalyticsViewSet

# Import multi-agent recommendation views
from .multi_agent_recommendation_views import (
    get_multi_agent_course_recommendations,
    get_role_based_recommendations,
    get_assessment_based_recommendations,
    get_subject_specific_recommendations,
    get_recommendation_agents_status
)

# Import the new unified AI configuration view
from .admin_views import AdminAIConfigurationView

# Import study time views
from .study_time_views import StudySessionViewSet, StudyGoalViewSet

app_name = "courses"

# Main router for public API
router = DefaultRouter()
router.register(r"courses", CourseViewSet, basename="courses")
router.register(r"standardized-courses", StandardizedCourseViewSet, basename="standardized-courses")
router.register(r"office-hours", OfficeHoursViewSet, basename="office-hours")

# Content management
router.register(r"course-content-preferences", CourseContentTypePreferenceViewSet, basename="course-content-preferences")
router.register(r"student-content-preferences", StudentContentTypePreferenceViewSet, basename="student-content-preferences")
router.register(r"content-analytics", ContentAnalyticsViewSet, basename="content-analytics")

# Study time management
router.register(r"study-sessions", StudySessionViewSet, basename="study-sessions")
router.register(r"study-goals", StudyGoalViewSet, basename="study-goals")

# Admin router
admin_router = DefaultRouter()
admin_router.register(r"courses", AdminCourseViewSet, basename="admin-courses")
admin_router.register(r"assignments", AdminCourseAssignmentViewSet, basename="admin-assignments")

# Admin nested router for course resources
admin_nested_router = NestedDefaultRouter(admin_router, "courses", lookup="course")
try:
    from .views.admin_views import (
        AdminCourseMaterialViewSet,
        AdminCourseAssignmentViewSet,
        AdminCourseStudentViewSet
    )
    admin_nested_router.register(r"materials", AdminCourseMaterialViewSet, basename="admin-course-materials")
    admin_nested_router.register(r"assignments", AdminCourseAssignmentViewSet, basename="admin-course-assignments")
    admin_nested_router.register(r"students", AdminCourseStudentViewSet, basename="admin-course-students")
except ImportError:
    # Create placeholder viewsets if not available
    pass

# Professor router
professor_router = DefaultRouter()
professor_router.register(r"courses", ProfessorCourseViewSet, basename="professor-courses")
professor_router.register(r"assignments", ProfessorAssignmentViewSet, basename="professor-assignments")
professor_router.register(r"office-hours", OfficeHoursViewSet, basename="professor-office-hours")

# Student router
student_router = DefaultRouter()
student_router.register(r"courses", StandardizedStudentCourseViewSet, basename="student-courses")

# Student nested router for course resources
student_nested_router = NestedDefaultRouter(student_router, "courses", lookup="course")
student_nested_router.register(r"materials", StandardizedStudentMaterialViewSet, basename="student-course-materials")
student_nested_router.register(r"assignments", StandardizedStudentAssignmentViewSet, basename="student-course-assignments")
student_nested_router.register(r"attendance", StandardizedStudentAttendanceViewSet, basename="student-course-attendance")
student_nested_router.register(r"progress", StandardizedStudentProgressViewSet, basename="student-course-progress")

urlpatterns = [
    # Main API endpoints
    path("", include(router.urls)),
    
    # Admin endpoints
    path("admin/", include([
        path("", include(admin_router.urls)),
        path("dashboard/stats/", AdminDashboardStatsView.as_view(), name="admin-dashboard-stats"),
        path("departments/", AdminDepartmentListView.as_view(), name="admin-departments"),
        path("departments/<int:pk>/", AdminDepartmentDetailView.as_view(), name="admin-department-detail"),
        # Include nested admin course endpoints
        path("", include("courses.admin_urls")),
    ])),
    
    # Professor endpoints
    path("professor/", include([
        path("", include(professor_router.urls)),
        path("dashboard/", ProfessorDashboardView.as_view(), name="professor-dashboard"),
        path("attendance/stats/", get_attendance_analytics, name="professor-attendance-stats"),
        path("attendance/analytics/<int:course_id>/", get_attendance_analytics, name="attendance-analytics"),
        path("students/at-risk/<int:course_id>/", get_students_at_risk, name="students-at-risk"),
        path("attendance/export/<int:course_id>/", export_attendance, name="export-attendance"),
    ])),
    
    # Student endpoints
    path("student/", include([
        path("", include(student_router.urls)),
        path("", include(student_nested_router.urls)),
    ])),
    
    # Multi-agent recommendations
    path("recommendations/", include([
        path("multi-agent/", get_multi_agent_course_recommendations, name="multi-agent-recommendations"),
        path("role-based/", get_role_based_recommendations, name="role-based-recommendations"),
        path("assessment-based/", get_assessment_based_recommendations, name="assessment-based-recommendations"),
        path("subject-specific/", get_subject_specific_recommendations, name="subject-specific-recommendations"),
        path("agents-status/", get_recommendation_agents_status, name="recommendation-agents-status"),
    ])),
    
    # AI Configuration (Admin only) - Unified dynamic endpoint
    path("admin/ai-config/", AdminAIConfigurationView.as_view(), name="admin-ai-config"),
]

"""URLpatternsforexamsecurityendpoints"""from django.urlsimportpathfrom.importexam_security_viewsurlpatterns=[#Examsessionmanagementpath('session/start/'exam_security_views.start_exam_sessionname='start_exam_session')path('session/<str:session_token>/status/'exam_security_views.get_session_statusname='get_session_status')path('session/<str:session_token>/submit/'exam_security_views.submit_exam_sessionname='submit_exam_session')path('session/<str:session_token>/heartbeat/'exam_security_views.heartbeatname='exam_heartbeat')#Lockdownmanagementpath('lockdown/violation/'exam_security_views.log_lockdown_violationname='log_lockdown_violation')path('lockdown/validate/'exam_security_views.validate_lockdown_environmentname='validate_lockdown_environment')path('session/<str:session_token>/violations/'exam_security_views.get_session_violationsname='get_session_violations')#Browserintegrationpath('session/<str:session_token>/seb-config/'exam_security_views.download_seb_configname='download_seb_config')path('session/<str:session_token>/respondus-link/'exam_security_views.get_respondus_linkname='get_respondus_link')]
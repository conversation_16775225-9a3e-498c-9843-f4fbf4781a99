"""AuthenticationRateLimitingandSecurityThismoduleprovidesadvancedratelimitingandsecurityfeaturesforauthenticationendpointstopreventbruteforceattacksandaccounttakeoverattempts."""import loggingimport timefromfunctoolsimportwrapsfromtypingimportAnyDictOptionalfrom django.confimportsettingsfrom django.core.cacheimportcachefrom django.httpimportJsonResponsefromdjango_ratelimit.exceptionsimportRatelimitedlogger=logging.getLogger(__name__)classAuthenticationRateLimit:"""Advancedratelimitingforauthenticationendpoints"""#CachekeypatternsFAILED_ATTEMPTS_KEY="auth_failed_attempts_{}"LOCKOUT_KEY="auth_lockout_{}"SUSPICIOUS_ACTIVITY_KEY="auth_suspicious_{}"#ConfigurationMAX_FAILED_ATTEMPTS=getattr(settings"AUTH_MAX_FAILED_ATTEMPTS"5)LOCKOUT_DURATION=getattr(settings"AUTH_LOCKOUT_DURATION"1800)#30minutesFAILED_ATTEMPTS_WINDOW=getattr(settings"AUTH_FAILED_ATTEMPTS_WINDOW"3600)#1hour@staticmethoddefget_client_ip(request)->str:"""GetclientIPaddresswithproxysupport"""x_forwarded_for=request.META.get("HTTP_X_FORWARDED_FOR")ifx_forwarded_for:#TakethefirstIPincaseofmultipleproxiesip=x_forwarded_for.split("")[0].strip()else:ip=request.META.get("REMOTE_ADDR""unknown")returnip@classmethoddefis_locked_out(clsidentifier:str)->bool:"""Checkifidentifieriscurrentlylockedout"""lockout_until=cache.get(cls.LOCKOUT_KEY.format(identifier))iflockout_untilandtime.time()<lockout_until:returnTrue#Cleanupexpiredlockoutiflockout_untilandtime.time()>=lockout_until:cache.delete(cls.LOCKOUT_KEY.format(identifier))returnFalse@classmethoddefget_failed_attempts(clsidentifier:str)->int:"""Getnumberoffailedattemptsforidentifier"""returncache.get(cls.FAILED_ATTEMPTS_KEY.format(identifier)0)@classmethoddefrecord_failed_attempt(clsidentifier:strrequest_data:Optional[Dict]=None):"""Recordfailedauthenticationattempt"""key=cls.FAILED_ATTEMPTS_KEY.format(identifier)attempts=cache.get(key0)+1cache.set(keyattemptstimeout=cls.FAILED_ATTEMPTS_WINDOW)#Logsuspiciousactivitylogger.warning(f"Failedauthenticationattempt#{attempts}for{identifier}."f"Requestdata:{request_dataor'N/A'}")#Lockoutaftermaxattemptsifattempts>=cls.MAX_FAILED_ATTEMPTS:lockout_until=time.time()+cls.LOCKOUT_DURATIONcache.set(cls.LOCKOUT_KEY.format(identifier)lockout_untiltimeout=cls.LOCKOUT_DURATION)#Markassuspiciousactivitycls._mark_suspicious_activity(identifierattempts)logger.error(f"Accountlockedout:{identifier}after{attempts}failedattempts."f"Lockoutduration:{cls.LOCKOUT_DURATION}seconds")@classmethoddefclear_failed_attempts(clsidentifier:str):"""Clearfailedattemptsonsuccessfullogin"""cache.delete(cls.FAILED_ATTEMPTS_KEY.format(identifier))cache.delete(cls.LOCKOUT_KEY.format(identifier))cache.delete(cls.SUSPICIOUS_ACTIVITY_KEY.format(identifier))logger.info(f"Clearedfailedattemptsfor{identifier}")@classmethoddef_mark_suspicious_activity(clsidentifier:strattempts:int):"""Markidentifierashavingsuspiciousactivity"""suspicious_data={"timestamp":time.time()"failed_attempts":attempts"marked_at":time.strftime("%Y-%m-%d%H:%M:%S")}cache.set(cls.SUSPICIOUS_ACTIVITY_KEY.format(identifier)suspicious_datatimeout=86400)#24hours@classmethoddefis_suspicious(clsidentifier:str)->bool:"""Checkifidentifierhassuspiciousactivity"""returncache.get(cls.SUSPICIOUS_ACTIVITY_KEY.format(identifier))isnotNone@classmethoddefget_lockout_info(clsidentifier:str)->Optional[Dict[strAny]]:"""Getlockoutinformationforidentifier"""lockout_until=cache.get(cls.LOCKOUT_KEY.format(identifier))iflockout_until:remaining_time=max(0int(lockout_until-time.time()))return{"locked_out":True"lockout_until":lockout_until"remaining_seconds":remaining_time"remaining_minutes":remaining_time//60}returnNonedefrate_limit_auth(func):"""DecoratorforauthenticationratelimitingAppliesmultiplelayersofratelimiting:1.IP-basedratelimiting(10requestsperminute)2.Account-basedlockoutafterfailedattempts3.Suspiciousactivitydetection"""@wraps(func)defwrapper(selfrequest*args**kwargs):#Applyratelimitingmanuallysincethedecoratordoesn'tworkwellwithclassmethodsfromdjango_ratelimit.coreimportis_ratelimited#Checkratelimitingifis_ratelimited(request=requestgroup="auth"fn=funckey="ip"rate="10/m"method="POST"increment=True):logger.warning(f"RatelimitexceededforIP:{AuthenticationRateLimit.get_client_ip(request)}")returnJsonResponse({"status":"error""message":"Ratelimitexceeded.Pleaseslowdownyourrequests.""retry_after":60#1minute}status=429)ip=AuthenticationRateLimit.get_client_ip(request)username=request.data.get("username""")ifhasattr(request"data")else""#CheckIP-basedlockoutifAuthenticationRateLimit.is_locked_out(ip):lockout_info=AuthenticationRateLimit.get_lockout_info(ip)logger.warning(f"BlockedrequestfromlockedoutIP:{ip}")returnJsonResponse({"status":"error""message":"ToomanyfailedattemptsfromthisIPaddress.Pleasetryagainlater.""lockout_info":lockout_info"retry_after":(lockout_info["remaining_seconds"]iflockout_infoelseNone)}status=429)#Checkusername-basedlockoutifusernameandAuthenticationRateLimit.is_locked_out(username):lockout_info=AuthenticationRateLimit.get_lockout_info(username)logger.warning(f"Blockedrequestforlockedoutuser:{username}")returnJsonResponse({"status":"error""message":"Accounttemporarilylockedduetomultiplefailedloginattempts.""lockout_info":lockout_info"retry_after":(lockout_info["remaining_seconds"]iflockout_infoelseNone)}status=429)#CheckforsuspiciousactivityifAuthenticationRateLimit.is_suspicious(ip)or(usernameandAuthenticationRateLimit.is_suspicious(username)):logger.warning(f"SuspiciousactivitydetectedforIP:{ip}Username:{username}")try:#Calloriginalfunctionresponse=func(selfrequest*args**kwargs)#Handleresponsebasedonstatusifhasattr(response"status_code"):ifresponse.status_codein[*********]:#AuthenticationfailedAuthenticationRateLimit.record_failed_attempt(ip{"username":username"user_agent":request.META.get("HTTP_USER_AGENT""")"timestamp":time.time()})ifusername:AuthenticationRateLimit.record_failed_attempt(username{"ip":ip"user_agent":request.META.get("HTTP_USER_AGENT""")"timestamp":time.time()})#Addratelimitinginfotoresponseifhasattr(response"data")andisinstance(response.datadict):failed_attempts_ip=(AuthenticationRateLimit.get_failed_attempts(ip))failed_attempts_user=(AuthenticationRateLimit.get_failed_attempts(username)ifusernameelse0)response.data.update({"rate_limit_info":{"failed_attempts":max(failed_attempts_ipfailed_attempts_user)"max_attempts":AuthenticationRateLimit.MAX_FAILED_ATTEMPTS"remaining_attempts":max(0AuthenticationRateLimit.MAX_FAILED_ATTEMPTS-max(failed_attempts_ipfailed_attempts_user))}})elifresponse.status_codein[200201]:#AuthenticationsuccessfulAuthenticationRateLimit.clear_failed_attempts(ip)ifusername:AuthenticationRateLimit.clear_failed_attempts(username)logger.info(f"Successfulauthenticationforuser:{username}IP:{ip}")returnresponseexceptExceptionase:logger.error(f"Errorinratelimitingwrapper:{e}")#Don'tblocktherequestduetoratelimitingerrorsreturnfunc(selfrequest*args**kwargs)returnwrapperdefget_rate_limit_status(identifier:str)->Dict[strAny]:"""Getcurrentratelimitingstatusforanidentifier"""return{"failed_attempts":AuthenticationRateLimit.get_failed_attempts(identifier)"is_locked_out":AuthenticationRateLimit.is_locked_out(identifier)"is_suspicious":AuthenticationRateLimit.is_suspicious(identifier)"lockout_info":AuthenticationRateLimit.get_lockout_info(identifier)"max_attempts":AuthenticationRateLimit.MAX_FAILED_ATTEMPTS}defclear_rate_limit(identifier:str)->bool:"""Clearratelimitingdataforanidentifier(adminfunction)"""try:AuthenticationRateLimit.clear_failed_attempts(identifier)logger.info(f"Adminclearedratelimitingfor:{identifier}")returnTrueexceptExceptionase:logger.error(f"Failedtoclearratelimitingfor{identifier}:{e}")returnFalse
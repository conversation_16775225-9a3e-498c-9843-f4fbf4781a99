"""ConsolidatedAdminInterfacesforCourseTypesThismoduleprovidesunifiedadmininterfacesformanagingtheconsolidatedcoursestructure.Iteliminatestheneedtomanagecoursesacrossmultipleapps."""from django.contribimportadminfrom django.utils.htmlimportformat_htmlfrom django.urlsimport reversefrom django.utils.translationimportgettext_lazyas_from.modelsimportCourseInteractiveCourseFeaturesAIGeneratedContentCourseTypeHistoryclassInteractiveCourseFeaturesinline(admin.StackedInline):"""InlineformanaginginteractivefeaturesdirectlyintheCourseadmin"""model=InteractiveCourseFeaturesextra=0classes=['collapse']fieldsets=((_('GamificationSettings'){'fields':('points_per_lesson''streak_bonus_multiplier''daily_goal_options')})(_('FeatureToggles'){'fields':('badges_enabled''leaderboard_enabled''achievements_enabled')})(_('InteractiveContent'){'fields':('difficulty_levels''learning_paths''engagement_metrics''interactive_content')'classes':['collapse']})(_('SyncSettings'){'fields':('sync_with_course''last_synced_at')'classes':['collapse']}))classAIGeneratedContentInline(admin.StackedInline):"""InlineformanagingAI-generatedcontentdirectlyintheCourseadmin"""model=AIGeneratedContentextra=0classes=['collapse']fieldsets=((_('AIProvider'){'fields':('ai_provider''ai_model''generation_prompt')})(_('GenerationSettings'){'fields':('ai_options''difficulty_level''learning_style')})(_('GeneratedMaterials'){'fields':('weekly_schedule''lesson_plans''assessment_methods''recommended_readings''sample_quizzes''project_ideas')'classes':['collapse']})(_('QualityControl'){'fields':('human_review_status''content_quality_score''review_notes')'classes':['collapse']})(_('Metadata'){'fields':('generated_at''last_regenerated''generation_cost')'classes':['collapse']}))classCourseTypeHistoryInline(admin.TabularInline):"""Inlineforviewingcoursetypechangehistory"""model=CourseTypeHistoryextra=0readonly_fields=['previous_type''new_type''changed_by''changed_at''reason']can_delete=Falseordering=['-changed_at']@admin.register(Course)classConsolidatedCourseAdmin(admin.ModelAdmin):"""ConsolidatedCourseAdminthatmanagesallcoursetypesinoneplace.Thisreplacestheneedforseparateadmininterfacesfordifferentcoursetypes."""list_display=['course_code''title''primary_type''type_indicators''instructor''department''semester''academic_year''is_active']list_filter=['primary_type''has_interactive_content''has_ai_content''is_active''semester''department''required_level''academic_year']search_fields=['course_code''title''description''instructor__username''instructor__first_name''instructor__last_name']readonly_fields=['created_at''updated_at''has_interactive_content''has_ai_content']inlines=[InteractiveCourseFeaturesinlineAIGeneratedContentInlineCourseTypeHistoryInline]fieldsets=((_('BasicInformation'){'fields':('course_code''title''description''credits')})(_('Scheduling'){'fields':('semester''academic_year''department''instructor')})(_('CourseType'){'fields':('primary_type''has_interactive_content''has_ai_content')'description':_('Coursetypeisautomaticallyupdatedbasedonassociatedfeatures')})(_('Level&Prerequisites'){'fields':('required_level''recommended_level''prerequisites''next_level_courses')})(_('Skills'){'fields':('skills_required''skills_developed')'classes':['collapse']})(_('CourseSettings'){'fields':('is_active''max_students''has_assessment')'classes':['collapse']})(_('Metadata'){'fields':('created_at''updated_at')'classes':['collapse']}))filter_horizontal=['prerequisites''next_level_courses''skills_required''skills_developed']deftype_indicators(selfobj):"""Displayvisualindicatorsforcoursetypes"""indicators=[]ifobj.has_interactive_content:indicators.append('<spanstyle="color:#28a745;">🎮Interactive</span>')ifobj.has_ai_content:indicators.append('<spanstyle="color:#007bff;">🤖AI</span>')ifnotindicators:indicators.append('<spanstyle="color:#6c757d;">📖Standard</span>')returnformat_html(''.join(indicators))type_indicators.short_description=_('TypeIndicators')defget_queryset(selfrequest):"""Optimizequerysettoreducedatabasequeries"""returnsuper().get_queryset(request).select_related('department''instructor').prefetch_related('interactive_features''ai_content')defsave_model(selfrequestobjformchange):"""Overridesavetotrackcoursetypechanges"""ifchange:#Gettheoriginalobjecttocompareoriginal=Course.objects.get(pk=obj.pk)iforiginal.primary_type!=obj.primary_type:#RecordthetypechangeCourseTypeHistory.objects.create(course=objprevious_type=original.primary_typenew_type=obj.primary_typechanged_by=request.userreason=f"Changedviaadmininterface")super().save_model(requestobjformchange)actions=['make_interactive''make_ai_generated''make_standard']defmake_interactive(selfrequestqueryset):"""Bulkactiontoaddinteractivefeaturestocourses"""count=0forcourseinqueryset:ifnothasattr(course'interactive_features'):InteractiveCourseFeatures.objects.create(course=course)count+=1self.message_user(request_('%(count)dcoursesnowhaveinteractivefeatures.')%{'count':count})make_interactive.short_description=_('Addinteractivefeaturestoselectedcourses')defmake_ai_generated(selfrequestqueryset):"""BulkactiontoaddAI-generatedcontenttocourses"""count=0forcourseinqueryset:ifnothasattr(course'ai_content'):AIGeneratedContent.objects.create(course=coursegeneration_prompt="Defaultpromptforbulkcreation")count+=1self.message_user(request_('%(count)dcoursesnowhaveAI-generatedcontent.')%{'count':count})make_ai_generated.short_description=_('AddAIcontenttoselectedcourses')defmake_standard(selfrequestqueryset):"""Bulkactiontoconvertcoursestostandardtype"""count=0forcourseinqueryset:ifcourse.primary_type!='STANDARD':course.primary_type='STANDARD'course.save()count+=1self.message_user(request_('%(count)dcoursesconvertedtostandardtype.')%{'count':count})make_standard.short_description=_('Convertselectedcoursestostandardtype')@admin.register(InteractiveCourseFeatures)classInteractiveCourseFeautresAdmin(admin.ModelAdmin):"""Standaloneadminforinteractivecoursefeatures"""list_display=['course_code''course_title''points_per_lesson''badges_enabled''leaderboard_enabled''achievements_enabled''last_synced_at']list_filter=['badges_enabled''leaderboard_enabled''achievements_enabled''sync_with_course']search_fields=['course__course_code''course__title']readonly_fields=['created_at''updated_at']defcourse_code(selfobj):returnobj.course.course_codecourse_code.short_description=_('CourseCode')defcourse_title(selfobj):returnobj.course.titlecourse_title.short_description=_('CourseTitle')@admin.register(AIGeneratedContent)classAIGeneratedContentAdmin(admin.ModelAdmin):"""StandaloneadminforAI-generatedcontent"""list_display=['course_code''course_title''ai_provider''ai_model''human_review_status''content_quality_score''generated_at']list_filter=['ai_provider''ai_model''human_review_status''difficulty_level''learning_style']search_fields=['course__course_code''course__title''generation_prompt']readonly_fields=['generated_at''last_regenerated''created_at''updated_at']fieldsets=((_('CourseInformation'){'fields':('course')})(_('AIConfiguration'){'fields':('ai_provider''ai_model''generation_prompt''ai_options')})(_('GenerationSettings'){'fields':('difficulty_level''learning_style')})(_('QualityControl'){'fields':('human_review_status''content_quality_score''review_notes''reviewed_by')})(_('GeneratedContent'){'fields':('weekly_schedule''lesson_plans''assessment_methods''recommended_readings''sample_quizzes''project_ideas''teaching_tips''skills_gained''additional_resources')'classes':['collapse']})(_('Metadata'){'fields':('raw_ai_response''generated_at''last_regenerated''generation_cost''created_at''updated_at')'classes':['collapse']}))defcourse_code(selfobj):returnobj.course.course_codecourse_code.short_description=_('CourseCode')defcourse_title(selfobj):returnobj.course.titlecourse_title.short_description=_('CourseTitle')actions=['approve_content''mark_for_revision''regenerate_content']defapprove_content(selfrequestqueryset):"""BulkapproveAIcontent"""count=queryset.update(human_review_status='APPROVED'reviewed_by=request.user)self.message_user(request_('%(count)dAIcontentitemsapproved.')%{'count':count})approve_content.short_description=_('ApproveselectedAIcontent')defmark_for_revision(selfrequestqueryset):"""MarkAIcontentasneedingrevision"""count=queryset.update(human_review_status='NEEDS_REVISION'reviewed_by=request.user)self.message_user(request_('%(count)dAIcontentitemsmarkedforrevision.')%{'count':count})mark_for_revision.short_description=_('Markselectedcontentforrevision')@admin.register(CourseTypeHistory)classCourseTypeHistoryAdmin(admin.ModelAdmin):"""Adminforviewingcoursetypechangehistory"""list_display=['course_code''course_title''previous_type''new_type''changed_by''changed_at']list_filter=['previous_type''new_type''changed_at']search_fields=['course__course_code''course__title''reason']readonly_fields=['course''previous_type''new_type''changed_by''changed_at']date_hierarchy='changed_at'defcourse_code(selfobj):returnobj.course.course_codecourse_code.short_description=_('CourseCode')defcourse_title(selfobj):returnobj.course.titlecourse_title.short_description=_('CourseTitle')defhas_add_permission(selfrequest):"""Preventmanualcreationofhistoryrecords"""returnFalse
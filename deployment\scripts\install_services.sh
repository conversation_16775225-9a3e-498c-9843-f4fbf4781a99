#!/bin/bash

# Install systemd services for North Star University

set -e

PROJECT_NAME="north-star-university"
PROJECT_DIR="/var/www/$PROJECT_NAME"

# Colors
GREEN='\033[0;32m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log "Installing systemd services..."

# Copy service files
sudo cp $PROJECT_DIR/deployment/systemd/*.service /etc/systemd/system/

# Reload systemd
sudo systemctl daemon-reload

# Enable services
log "Enabling services..."
sudo systemctl enable $PROJECT_NAME.service
sudo systemctl enable ${PROJECT_NAME}-celery.service
sudo systemctl enable ${PROJECT_NAME}-celery-beat.service

log "Services installed and enabled!"
log "To start services:"
log "  sudo systemctl start $PROJECT_NAME"
log "  sudo systemctl start ${PROJECT_NAME}-celery"
log "  sudo systemctl start ${PROJECT_NAME}-celery-beat"
log ""
log "To check status:"
log "  sudo systemctl status $PROJECT_NAME"
log "  sudo systemctl status ${PROJECT_NAME}-celery"
log "  sudo systemctl status ${PROJECT_NAME}-celery-beat"

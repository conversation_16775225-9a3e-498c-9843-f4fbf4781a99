"""MonitoringUtilitiesModuleConsolidatedmonitoringandanalyticsfunctionality."""from.servicesimportMonitoringServicemonitoring_service#Importfunctionsfromthelegacymonitoring.pyfiletry:from..monitoringimportget_monitoring_metricsreset_monitoring_metricsexceptImportError:#Fallbackifthefunctionsdon'texistdefget_monitoring_metrics():return{}defreset_monitoring_metrics():pass__all__=['MonitoringService''monitoring_service''get_monitoring_metrics''reset_monitoring_metrics']
"""
Advanced Assessment Engine Services
Implements the core logic for adaptive questioning, plagiarism detection,
secure browser lockdown, and peer assessment functionality.
"""

import asyncio
import json
import logging
import math
import random
import re
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any

import numpy as np
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db import transaction
from django.utils import timezone

from .models import Assessment, AssessmentQuestion, AssessmentResponse
from .advanced_models import (
    AdaptiveAssessmentConfig,
    AdaptiveQuestionSelection,
    AssessmentIntegrityLog,
    PeerAssessment,
    PeerReview,
    PlagiarismDetectionResult,
    SecureAssessmentSession,
    SelfReflection
)

try:
    from utils.ai.services import get_ai_service
    from utils.consolidated_ai_service import ConsolidatedAIService
except ImportError:
    def get_ai_service():
        return None
    class ConsolidatedAIService:
        def __init__(self):
            pass
        async def analyze_text_similarity(self, text1, text2):
            return {'similarity_score': 0.0}

User = get_user_model()
logger = logging.getLogger(__name__)


class AdaptiveQuestioningEngine:
    """
    Implements adaptive questioning algorithms including Item Response Theory (IRT)
    and Computer Adaptive Testing (CAT) methodologies.
    """
    
    def __init__(self, config: AdaptiveAssessmentConfig):
        self.config = config
        self.ai_service = get_ai_service()
    
    def start_adaptive_assessment(self, assessment: Assessment) -> Dict[str, Any]:
        """Initialize an adaptive assessment session"""
        try:
            # Initialize the adaptive session
            session_data = {
                'current_ability': self.config.initial_difficulty,
                'standard_error': 1.0,
                'questions_answered': 0,
                'difficulty_history': [],
                'ability_history': [],
                'algorithm_state': {
                    'theta': self.config.initial_difficulty,
                    'sem': 1.0,
                    'items_administered': []
                }
            }
            
            # Store session data in assessment
            assessment.adaptive_progression = session_data
            assessment.current_difficulty_level = int(self.config.initial_difficulty * 5) + 1  # Scale to 1-5
            assessment.save()
            
            # Select first question
            next_question = self._select_next_question(assessment, session_data)
            
            return {
                'status': 'success',
                'session_data': session_data,
                'next_question': next_question,
                'estimated_ability': session_data['current_ability']
            }
            
        except Exception as e:
            logger.error(f"Error starting adaptive assessment: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _select_next_question(self, assessment: Assessment, session_data: Dict) -> Optional[AssessmentQuestion]:
        """Select the next optimal question based on the current ability estimate"""
        try:
            current_ability = session_data['current_ability']
            administered_items = session_data['algorithm_state'].get('items_administered', [])
            
            # Get available questions for the assessment path
            available_questions = AssessmentQuestion.objects.filter(
                learning_path=assessment.learning_path,
                is_public=True
            ).exclude(id__in=administered_items)
            
            if not available_questions.exists():
                return None
            
            if self.config.question_selection_method == 'maximum_information':
                return self._select_maximum_information_question(
                    available_questions, current_ability
                )
            elif self.config.question_selection_method == 'weighted_random':
                return self._select_weighted_random_question(
                    available_questions, current_ability
                )
            else:  # random
                return available_questions.order_by('?').first()
                
        except Exception as e:
            logger.error(f"Error selecting next question: {e}")
            return None
    
    def _select_maximum_information_question(self, questions, ability: float) -> AssessmentQuestion:
        """Select question that provides maximum information at current ability level"""
        best_question = None
        max_information = 0
        
        for question in questions:
            # Calculate information function value
            # Using simplified IRT 2PL model: I(θ) = a²P(θ)(1-P(θ))
            discrimination = question.difficulty_level * 1.5  # Approximate discrimination parameter
            difficulty = question.difficulty_level
            
            # Probability of correct response
            prob_correct = 1 / (1 + math.exp(-discrimination * (ability - difficulty)))
            
            # Information value
            information = (discrimination ** 2) * prob_correct * (1 - prob_correct)
            
            if information > max_information:
                max_information = information
                best_question = question
        
        return best_question or questions.first()
    
    def _select_weighted_random_question(self, questions, ability: float) -> AssessmentQuestion:
        """Select question using weighted random selection based on information value"""
        weights = []
        questions_list = list(questions)
        
        for question in questions_list:
            discrimination = question.difficulty_level * 1.5
            difficulty = question.difficulty_level
            prob_correct = 1 / (1 + math.exp(-discrimination * (ability - difficulty)))
            information = (discrimination ** 2) * prob_correct * (1 - prob_correct)
            weights.append(information + 0.1)  # Add small constant to avoid zero weights
        
        # Weighted random selection
        total_weight = sum(weights)
        rand_val = random.uniform(0, total_weight)
        cumulative_weight = 0
        
        for i, weight in enumerate(weights):
            cumulative_weight += weight
            if rand_val <= cumulative_weight:
                return questions_list[i]
        
        return questions_list[-1]  # Fallback
    
    def process_response(self, assessment: Assessment, question: AssessmentQuestion, 
                        response: AssessmentResponse) -> Dict[str, Any]:
        """Process a response and update ability estimate"""
        try:
            session_data = assessment.adaptive_progression or {}
            
            # Record the question selection
            selection_order = session_data.get('questions_answered', 0) + 1
            AdaptiveQuestionSelection.objects.create(
                assessment=assessment,
                question=question,
                selection_order=selection_order,
                difficulty_at_selection=question.difficulty_level,
                estimated_ability_before=session_data.get('current_ability', 0.5),
                was_correct=response.is_correct,
                response_time_seconds=response.response_time_seconds,
                algorithm_state=session_data.get('algorithm_state', {})
            )
            
            # Update ability estimate
            if self.config.algorithm == 'item_response_theory':
                new_ability, new_sem = self._update_ability_irt(
                    session_data, question, response.is_correct
                )
            elif self.config.algorithm == 'cat_adaptive':
                new_ability, new_sem = self._update_ability_cat(
                    session_data, question, response.is_correct
                )
            else:  # simple_threshold
                new_ability, new_sem = self._update_ability_simple(
                    session_data, question, response.is_correct
                )
            
            # Update session data
            session_data.update({
                'current_ability': new_ability,
                'standard_error': new_sem,
                'questions_answered': selection_order,
                'difficulty_history': session_data.get('difficulty_history', []) + [question.difficulty_level],
                'ability_history': session_data.get('ability_history', []) + [new_ability]
            })
            
            # Update algorithm state
            session_data['algorithm_state']['theta'] = new_ability
            session_data['algorithm_state']['sem'] = new_sem
            session_data['algorithm_state']['items_administered'].append(question.id)
            
            # Save updated session data
            assessment.adaptive_progression = session_data
            assessment.current_difficulty_level = int(new_ability * 5) + 1
            assessment.save()
            
            # Update the question selection record with new ability
            selection = AdaptiveQuestionSelection.objects.filter(
                assessment=assessment, 
                selection_order=selection_order
            ).first()
            if selection:
                selection.estimated_ability_after = new_ability
                selection.save()
            
            # Check termination criteria
            should_terminate = self._check_termination_criteria(session_data)
            
            # Select next question if not terminating
            next_question = None
            if not should_terminate:
                next_question = self._select_next_question(assessment, session_data)
                if not next_question:
                    should_terminate = True
            
            return {
                'status': 'success',
                'new_ability': new_ability,
                'standard_error': new_sem,
                'should_terminate': should_terminate,
                'next_question': next_question,
                'session_data': session_data
            }
            
        except Exception as e:
            logger.error(f"Error processing adaptive response: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _update_ability_irt(self, session_data: Dict, question: AssessmentQuestion, 
                           is_correct: bool) -> Tuple[float, float]:
        """Update ability estimate using Item Response Theory"""
        current_ability = session_data.get('current_ability', 0.5)
        current_sem = session_data.get('standard_error', 1.0)
        
        # IRT parameters (simplified 2PL model)
        discrimination = question.difficulty_level * 1.5
        difficulty = question.difficulty_level
        
        # Calculate probability of correct response
        prob_correct = 1 / (1 + math.exp(-discrimination * (current_ability - difficulty)))
        
        # Information function
        information = (discrimination ** 2) * prob_correct * (1 - prob_correct)
        
        # Update ability using Fisher information
        if information > 0:
            likelihood_derivative = discrimination * (is_correct - prob_correct)
            new_ability = current_ability + likelihood_derivative / information
            new_sem = 1 / math.sqrt(1 / (current_sem ** 2) + information)
        else:
            new_ability = current_ability
            new_sem = current_sem
        
        # Bound ability estimate
        new_ability = max(-3.0, min(3.0, new_ability))
        new_ability = (new_ability + 3.0) / 6.0  # Normalize to 0-1 range
        
        return new_ability, new_sem
    
    def _update_ability_cat(self, session_data: Dict, question: AssessmentQuestion, 
                           is_correct: bool) -> Tuple[float, float]:
        """Update ability estimate using Computer Adaptive Testing methodology"""
        # Simplified CAT implementation
        current_ability = session_data.get('current_ability', 0.5)
        adjustment = self.config.difficulty_adjustment_factor
        
        if is_correct:
            new_ability = current_ability + adjustment * (1 - current_ability)
        else:
            new_ability = current_ability - adjustment * current_ability
        
        # Calculate standard error based on number of questions
        questions_answered = session_data.get('questions_answered', 0) + 1
        new_sem = 1.0 / math.sqrt(questions_answered)
        
        return max(0.0, min(1.0, new_ability)), new_sem
    
    def _update_ability_simple(self, session_data: Dict, question: AssessmentQuestion, 
                              is_correct: bool) -> Tuple[float, float]:
        """Update ability using simple threshold-based approach"""
        current_ability = session_data.get('current_ability', 0.5)
        adjustment = self.config.difficulty_adjustment_factor
        
        # Adjust based on question difficulty and correctness
        difficulty_factor = abs(question.difficulty_level - current_ability)
        
        if is_correct:
            if question.difficulty_level > current_ability:
                # Correct answer on difficult question - increase ability more
                new_ability = current_ability + adjustment * (1 + difficulty_factor)
            else:
                # Correct answer on easy question - small increase
                new_ability = current_ability + adjustment * 0.5
        else:
            if question.difficulty_level < current_ability:
                # Wrong answer on easy question - decrease ability more
                new_ability = current_ability - adjustment * (1 + difficulty_factor)
            else:
                # Wrong answer on difficult question - small decrease
                new_ability = current_ability - adjustment * 0.5
        
        new_ability = max(0.0, min(1.0, new_ability))
        questions_answered = session_data.get('questions_answered', 0) + 1
        new_sem = 1.0 / math.sqrt(questions_answered + 1)
        
        return new_ability, new_sem
    
    def _check_termination_criteria(self, session_data: Dict) -> bool:
        """Check if the adaptive assessment should terminate"""
        questions_answered = session_data.get('questions_answered', 0)
        current_sem = session_data.get('standard_error', 1.0)
        current_ability = session_data.get('current_ability', 0.5)
        
        # Minimum/maximum questions
        if questions_answered < self.config.min_questions:
            return False
        if questions_answered >= self.config.max_questions:
            return True
        
        # Standard error threshold
        if current_sem < self.config.stopping_criteria_theta:
            return True
        
        # Mastery threshold (if enabled)
        if self.config.enable_mastery_threshold:
            if current_ability >= self.config.mastery_threshold and current_sem < 0.5:
                return True
        
        return False


class PlagiarismDetectionEngine:
    """
    AI-powered plagiarism detection system that analyzes text for similarity,
    AI-generated content, and potential academic dishonesty.
    """
    
    def __init__(self):
        self.ai_service = ConsolidatedAIService()
        self.similarity_cache = {}
    
    async def analyze_response(self, response: AssessmentResponse) -> PlagiarismDetectionResult:
        """Analyze a response for potential plagiarism"""
        try:
            # Create or get existing result
            result, created = PlagiarismDetectionResult.objects.get_or_create(
                response=response,
                defaults={'status': 'PROCESSING'}
            )
            
            if not created and result.status == 'COMPLETED':
                return result
            
            result.status = 'PROCESSING'
            result.save()
            
            start_time = time.time()
            
            # Extract text content
            text_content = self._extract_text_content(response)
            if not text_content or len(text_content.strip()) < 10:
                result.status = 'COMPLETED'
                result.overall_similarity_score = 0.0
                result.similarity_level = 'LOW'
                result.save()
                return result
            
            # Perform multiple types of analysis
            analyses = await asyncio.gather(
                self._detect_ai_generated_content(text_content),
                self._check_semantic_similarity(text_content, response),
                self._check_lexical_similarity(text_content, response),
                self._search_external_sources(text_content),
                return_exceptions=True
            )
            
            ai_score, semantic_score, lexical_score, external_sources = analyses
            
            # Handle any exceptions
            if isinstance(ai_score, Exception):
                ai_score = 0.0
            if isinstance(semantic_score, Exception):
                semantic_score = 0.0
            if isinstance(lexical_score, Exception):
                lexical_score = 0.0
            if isinstance(external_sources, Exception):
                external_sources = []
            
            # Calculate overall similarity
            overall_score = self._calculate_overall_similarity(
                ai_score, semantic_score, lexical_score
            )
            
            # Determine similarity level
            similarity_level = self._determine_similarity_level(overall_score)
            
            # Update result
            result.ai_detection_score = ai_score
            result.semantic_similarity_score = semantic_score
            result.lexical_similarity_score = lexical_score
            result.overall_similarity_score = overall_score
            result.similarity_level = similarity_level
            result.similar_sources = external_sources
            result.processing_time_seconds = time.time() - start_time
            result.status = 'COMPLETED'
            
            # Determine if human review is needed
            result.requires_human_review = (
                overall_score > 0.6 or 
                ai_score > 0.7 or 
                len(external_sources) > 0
            )
            
            # Calculate confidence
            result.confidence_level = self._calculate_confidence(
                ai_score, semantic_score, lexical_score, len(external_sources)
            )
            
            result.save()
            
            return result
            
        except Exception as e:
            logger.error(f"Error in plagiarism analysis: {e}")
            result.status = 'FAILED'
            result.save()
            return result
    
    def _extract_text_content(self, response: AssessmentResponse) -> str:
        """Extract text content from various response formats"""
        text_content = ""
        
        if response.answer:
            text_content += response.answer + " "
        
        if response.student_answer:
            if isinstance(response.student_answer, dict):
                # Extract text from JSON structure
                for key, value in response.student_answer.items():
                    if isinstance(value, str):
                        text_content += value + " "
                    elif isinstance(value, list):
                        for item in value:
                            if isinstance(item, str):
                                text_content += item + " "
            elif isinstance(response.student_answer, str):
                text_content += response.student_answer + " "
        
        return text_content.strip()
    
    async def _detect_ai_generated_content(self, text: str) -> float:
        """Detect if content is likely AI-generated"""
        try:
            # Use AI service to analyze text patterns
            prompt = f"""
            Analyze the following text and determine the probability that it was generated by AI.
            Consider factors like:
            - Writing style consistency
            - Vocabulary complexity
            - Sentence structure patterns
            - Topic coherence
            - Human-like errors or inconsistencies
            
            Text to analyze:
            {text}
            
            Respond with only a number between 0.0 and 1.0, where:
            0.0 = Definitely human-written
            1.0 = Definitely AI-generated
            """
            
            if self.ai_service:
                response = await self.ai_service.generate_text(prompt)
                # Extract probability from response
                score_match = re.search(r'0\.\d+|1\.0', response)
                if score_match:
                    return float(score_match.group())
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error detecting AI content: {e}")
            return 0.0
    
    async def _check_semantic_similarity(self, text: str, response: AssessmentResponse) -> float:
        """Check semantic similarity against known responses"""
        try:
            # Get other responses to the same question
            other_responses = AssessmentResponse.objects.filter(
                question=response.question
            ).exclude(id=response.id).values_list('answer', 'student_answer')
            
            max_similarity = 0.0
            
            for other_answer, other_student_answer in other_responses:
                other_text = self._extract_text_from_fields(other_answer, other_student_answer)
                if other_text and len(other_text.strip()) > 10:
                    # Use AI service for semantic similarity
                    similarity = await self._calculate_semantic_similarity(text, other_text)
                    max_similarity = max(max_similarity, similarity)
                    
                    if max_similarity > 0.8:  # Early termination for high similarity
                        break
            
            return max_similarity
            
        except Exception as e:
            logger.error(f"Error checking semantic similarity: {e}")
            return 0.0
    
    async def _check_lexical_similarity(self, text: str, response: AssessmentResponse) -> float:
        """Check lexical/surface-level similarity"""
        try:
            # Get other responses to the same question
            other_responses = AssessmentResponse.objects.filter(
                question=response.question
            ).exclude(id=response.id).values_list('answer', 'student_answer')
            
            max_similarity = 0.0
            text_words = set(text.lower().split())
            
            for other_answer, other_student_answer in other_responses:
                other_text = self._extract_text_from_fields(other_answer, other_student_answer)
                if other_text:
                    other_words = set(other_text.lower().split())
                    if len(text_words) > 0 and len(other_words) > 0:
                        # Jaccard similarity
                        intersection = len(text_words.intersection(other_words))
                        union = len(text_words.union(other_words))
                        similarity = intersection / union if union > 0 else 0.0
                        max_similarity = max(max_similarity, similarity)
            
            return max_similarity
            
        except Exception as e:
            logger.error(f"Error checking lexical similarity: {e}")
            return 0.0
    
    async def _search_external_sources(self, text: str) -> List[Dict]:
        """Search for similar content in external sources"""
        try:
            # This would integrate with plagiarism databases, search engines, etc.
            # For now, return empty list as external API integration would be needed
            return []
            
        except Exception as e:
            logger.error(f"Error searching external sources: {e}")
            return []
    
    async def _calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """Calculate semantic similarity between two texts"""
        try:
            cache_key = f"semantic_sim_{hash(text1 + text2)}"
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            if self.ai_service:
                result = await self.ai_service.analyze_text_similarity(text1, text2)
                similarity = result.get('similarity_score', 0.0)
                
                # Cache result for 1 hour
                cache.set(cache_key, similarity, 3600)
                return similarity
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error calculating semantic similarity: {e}")
            return 0.0
    
    def _extract_text_from_fields(self, answer: str, student_answer: Any) -> str:
        """Helper to extract text from answer fields"""
        text = ""
        if answer:
            text += answer + " "
        if student_answer:
            if isinstance(student_answer, str):
                text += student_answer + " "
            elif isinstance(student_answer, dict):
                for value in student_answer.values():
                    if isinstance(value, str):
                        text += value + " "
        return text.strip()
    
    def _calculate_overall_similarity(self, ai_score: float, semantic_score: float, 
                                    lexical_score: float) -> float:
        """Calculate overall similarity score from component scores"""
        # Weighted combination of different similarity measures
        weights = {
            'ai': 0.3,
            'semantic': 0.4,
            'lexical': 0.3
        }
        
        overall = (
            ai_score * weights['ai'] +
            semantic_score * weights['semantic'] +
            lexical_score * weights['lexical']
        )
        
        return min(1.0, max(0.0, overall))
    
    def _determine_similarity_level(self, score: float) -> str:
        """Determine similarity level category"""
        if score <= 0.3:
            return 'LOW'
        elif score <= 0.6:
            return 'MEDIUM'
        elif score <= 0.8:
            return 'HIGH'
        else:
            return 'VERY_HIGH'
    
    def _calculate_confidence(self, ai_score: float, semantic_score: float, 
                            lexical_score: float, external_sources_count: int) -> float:
        """Calculate confidence in the plagiarism detection result"""
        # Higher confidence with multiple indicators
        indicators = [ai_score > 0.5, semantic_score > 0.5, lexical_score > 0.5, external_sources_count > 0]
        confidence = sum(indicators) / len(indicators)
        
        # Adjust based on score magnitudes
        max_score = max(ai_score, semantic_score, lexical_score)
        confidence = (confidence + max_score) / 2
        
        return min(1.0, max(0.0, confidence))


class SecureAssessmentManager:
    """
    Manages secure browser lockdown and integrity monitoring for high-stakes assessments.
    """
    
    def __init__(self):
        self.violation_thresholds = {
            'focus_loss': 5,
            'tab_switch': 3,
            'copy_paste_attempts': 2,
            'suspicious_activity': 1
        }
    
    def initialize_secure_session(self, assessment: Assessment, request) -> SecureAssessmentSession:
        """Initialize a secure assessment session"""
        try:
            # Extract browser and device information
            browser_info = self._extract_browser_info(request)
            device_fingerprint = self._generate_device_fingerprint(browser_info)
            
            # Create secure session
            session = SecureAssessmentSession.objects.create(
                assessment=assessment,
                student=assessment.student,
                browser_info=browser_info,
                device_fingerprint=device_fingerprint,
                ip_address=self._get_client_ip(request),
                status='PENDING'
            )
            
            # Log session start
            self._log_integrity_event(
                assessment, 'SESSION_START', 
                {'session_id': str(session.session_id)}, request
            )
            
            return session
            
        except Exception as e:
            logger.error(f"Error initializing secure session: {e}")
            raise
    
    def start_secure_session(self, session: SecureAssessmentSession) -> Dict[str, Any]:
        """Start the secure assessment session"""
        try:
            session.status = 'ACTIVE'
            session.start_time = timezone.now()
            session.save()
            
            # Generate security configuration for frontend
            security_config = {
                'session_id': str(session.session_id),
                'security_level': session.security_level,
                'disable_copy_paste': session.disable_copy_paste,
                'disable_right_click': session.disable_right_click,
                'disable_print_screen': session.disable_print_screen,
                'block_external_sites': session.block_external_sites,
                'enable_monitoring': {
                    'screen_recording': session.enable_screen_recording,
                    'webcam': session.enable_webcam_monitoring,
                    'audio': session.enable_audio_monitoring
                },
                'violation_thresholds': self.violation_thresholds
            }
            
            return {
                'status': 'success',
                'security_config': security_config,
                'session_token': self._generate_session_token(session)
            }
            
        except Exception as e:
            logger.error(f"Error starting secure session: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def log_security_event(self, session_id: str, event_type: str, 
                          event_data: Dict, request) -> bool:
        """Log a security event during assessment"""
        try:
            session = SecureAssessmentSession.objects.get(session_id=session_id)
            
            # Calculate risk score
            risk_score = self._calculate_risk_score(event_type, event_data)
            
            # Log the event
            self._log_integrity_event(
                session.assessment, event_type, event_data, request, risk_score
            )
            
            # Update session based on event type
            if event_type == 'FOCUS_LOSS':
                session.focus_loss_count += 1
            elif event_type == 'TAB_SWITCH':
                session.tab_switch_count += 1
            elif event_type in ['COPY_ATTEMPT', 'PASTE_ATTEMPT', 'RIGHT_CLICK', 'PRINT_SCREEN']:
                session.log_violation(event_type, event_data)
            
            session.save()
            
            # Check if session should be terminated
            if self._should_terminate_session(session, event_type):
                self._terminate_session(session, f"Excessive violations: {event_type}")
                return False
            
            return True
            
        except SecureAssessmentSession.DoesNotExist:
            logger.error(f"Secure session not found: {session_id}")
            return False
        except Exception as e:
            logger.error(f"Error logging security event: {e}")
            return False
    
    def validate_session(self, session_id: str, request) -> Dict[str, Any]:
        """Validate that a secure session is still valid"""
        try:
            session = SecureAssessmentSession.objects.get(session_id=session_id)
            
            # Check session validity
            if not session.is_session_valid():
                return {
                    'valid': False,
                    'reason': 'Session no longer valid',
                    'action': 'terminate'
                }
            
            # Verify IP address hasn't changed (if strict mode)
            current_ip = self._get_client_ip(request)
            if session.security_level in ['HIGH', 'MAXIMUM'] and session.ip_address != current_ip:
                session.log_violation('IP_CHANGE', {'old_ip': session.ip_address, 'new_ip': current_ip})
                return {
                    'valid': False,
                    'reason': 'IP address changed',
                    'action': 'terminate'
                }
            
            # Update last activity
            session.last_activity = timezone.now()
            session.save()
            
            return {
                'valid': True,
                'remaining_time': self._calculate_remaining_time(session),
                'violation_count': session.violation_count
            }
            
        except SecureAssessmentSession.DoesNotExist:
            return {
                'valid': False,
                'reason': 'Session not found',
                'action': 'redirect'
            }
        except Exception as e:
            logger.error(f"Error validating session: {e}")
            return {
                'valid': False,
                'reason': 'Validation error',
                'action': 'terminate'
            }
    
    def end_secure_session(self, session_id: str, reason: str = 'completed') -> bool:
        """End a secure assessment session"""
        try:
            session = SecureAssessmentSession.objects.get(session_id=session_id)
            session.status = 'COMPLETED' if reason == 'completed' else 'TERMINATED'
            session.end_time = timezone.now()
            session.save()
            
            # Log session end
            self._log_integrity_event(
                session.assessment, 'SESSION_END',
                {'reason': reason, 'duration_minutes': self._calculate_session_duration(session)},
                None  # No request object needed for session end
            )
            
            return True
            
        except SecureAssessmentSession.DoesNotExist:
            logger.error(f"Secure session not found for ending: {session_id}")
            return False
        except Exception as e:
            logger.error(f"Error ending secure session: {e}")
            return False
    
    def _extract_browser_info(self, request) -> Dict[str, Any]:
        """Extract browser and system information from request"""
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        return {
            'user_agent': user_agent,
            'accept_language': request.META.get('HTTP_ACCEPT_LANGUAGE', ''),
            'accept_encoding': request.META.get('HTTP_ACCEPT_ENCODING', ''),
            'screen_resolution': request.POST.get('screen_resolution', ''),
            'timezone_offset': request.POST.get('timezone_offset', ''),
            'platform': request.POST.get('platform', ''),
            'browser_name': request.POST.get('browser_name', ''),
            'browser_version': request.POST.get('browser_version', '')
        }
    
    def _generate_device_fingerprint(self, browser_info: Dict) -> str:
        """Generate a unique device fingerprint"""
        import hashlib
        
        fingerprint_data = (
            browser_info.get('user_agent', '') +
            browser_info.get('screen_resolution', '') +
            browser_info.get('timezone_offset', '') +
            browser_info.get('platform', '')
        )
        
        return hashlib.sha256(fingerprint_data.encode()).hexdigest()[:32]
    
    def _get_client_ip(self, request) -> str:
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip or '127.0.0.1'
    
    def _log_integrity_event(self, assessment: Assessment, event_type: str, 
                           event_data: Dict, request, risk_score: float = 0.0):
        """Log an integrity event"""
        try:
            AssessmentIntegrityLog.objects.create(
                assessment=assessment,
                student=assessment.student,
                event_type=event_type,
                event_data=event_data,
                user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                ip_address=self._get_client_ip(request) if request else '127.0.0.1',
                screen_resolution=event_data.get('screen_resolution', ''),
                risk_score=risk_score
            )
        except Exception as e:
            logger.error(f"Error logging integrity event: {e}")
    
    def _calculate_risk_score(self, event_type: str, event_data: Dict) -> float:
        """Calculate risk score for an event"""
        risk_scores = {
            'SESSION_START': 0.0,
            'SESSION_END': 0.0,
            'QUESTION_VIEW': 0.0,
            'ANSWER_SUBMIT': 0.0,
            'FOCUS_LOSS': 0.2,
            'TAB_SWITCH': 0.4,
            'COPY_ATTEMPT': 0.8,
            'PASTE_ATTEMPT': 0.8,
            'RIGHT_CLICK': 0.3,
            'PRINT_SCREEN': 0.9,
            'SUSPICIOUS_ACTIVITY': 0.7,
            'VIOLATION': 1.0
        }
        
        base_score = risk_scores.get(event_type, 0.5)
        
        # Adjust based on event frequency
        frequency_multiplier = event_data.get('frequency', 1)
        if frequency_multiplier > 1:
            base_score = min(1.0, base_score * (1 + 0.2 * (frequency_multiplier - 1)))
        
        return base_score
    
    def _should_terminate_session(self, session: SecureAssessmentSession, event_type: str) -> bool:
        """Determine if session should be terminated based on violations"""
        if event_type in ['COPY_ATTEMPT', 'PASTE_ATTEMPT'] and session.violation_count >= 2:
            return True
        if event_type == 'TAB_SWITCH' and session.tab_switch_count >= 3:
            return True
        if event_type == 'FOCUS_LOSS' and session.focus_loss_count >= 5:
            return True
        if session.violation_count >= 5:
            return True
        
        return False
    
    def _terminate_session(self, session: SecureAssessmentSession, reason: str):
        """Terminate a session due to violations"""
        session.status = 'TERMINATED'
        session.end_time = timezone.now()
        session.save()
        
        # Log termination
        self._log_integrity_event(
            session.assessment, 'VIOLATION',
            {'termination_reason': reason, 'violation_count': session.violation_count},
            None
        )
    
    def _generate_session_token(self, session: SecureAssessmentSession) -> str:
        """Generate a secure session token"""
        import jwt
        from django.conf import settings
        
        payload = {
            'session_id': str(session.session_id),
            'student_id': session.student.id,
            'assessment_id': session.assessment.id,
            'exp': timezone.now() + timedelta(hours=4)  # Token expires in 4 hours
        }
        
        return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')
    
    def _calculate_remaining_time(self, session: SecureAssessmentSession) -> int:
        """Calculate remaining time in minutes"""
        if not session.start_time:
            return 0
        
        # Get assessment time limit from settings
        assessment_settings = session.assessment.assessment_settings.first()
        if assessment_settings:
            time_limit = assessment_settings.time_limit_minutes
        else:
            time_limit = 60  # Default 60 minutes
        
        elapsed = (timezone.now() - session.start_time).total_seconds() / 60
        remaining = max(0, time_limit - elapsed)
        
        return int(remaining)
    
    def _calculate_session_duration(self, session: SecureAssessmentSession) -> float:
        """Calculate session duration in minutes"""
        if session.start_time and session.end_time:
            return (session.end_time - session.start_time).total_seconds() / 60
        return 0.0


class PeerAssessmentManager:
    """
    Manages peer assessment and review processes including assignment,
    calibration, and quality control.
    """
    
    def __init__(self):
        self.ai_service = get_ai_service()
    
    def create_peer_assessment(self, assessment: Assessment, config: Dict[str, Any]) -> PeerAssessment:
        """Create a peer assessment from a completed assessment"""
        try:
            # Get all completed responses for this assessment
            responses = AssessmentResponse.objects.filter(
                assessment=assessment,
                is_correct__isnull=False  # Only completed responses
            )
            
            if responses.count() < 3:
                raise ValueError("Need at least 3 completed responses for peer assessment")
            
            # Create peer assessment for each response
            peer_assessments = []
            
            for response in responses:
                peer_assessment = PeerAssessment.objects.create(
                    original_assessment=assessment,
                    student_work=response,
                    number_of_reviewers=config.get('number_of_reviewers', 3),
                    anonymous_review=config.get('anonymous_review', True),
                    self_assessment_required=config.get('self_assessment_required', True),
                    evaluation_criteria=config.get('evaluation_criteria', []),
                    rubric=config.get('rubric', {}),
                    assignment_deadline=timezone.now() + timedelta(days=config.get('assignment_days', 3)),
                    review_deadline=timezone.now() + timedelta(days=config.get('review_days', 7)),
                    enable_calibration=config.get('enable_calibration', True)
                )
                
                peer_assessments.append(peer_assessment)
            
            # Assign reviewers
            self._assign_reviewers(peer_assessments)
            
            return peer_assessments[0]  # Return first one as representative
            
        except Exception as e:
            logger.error(f"Error creating peer assessment: {e}")
            raise
    
    def _assign_reviewers(self, peer_assessments: List[PeerAssessment]):
        """Assign reviewers to peer assessments using balanced assignment"""
        try:
            # Get all students who submitted responses
            students = list(set([pa.student_work.student for pa in peer_assessments]))
            random.shuffle(students)
            
            # Create assignment matrix to ensure balanced reviewing
            for peer_assessment in peer_assessments:
                reviewee = peer_assessment.student_work.student
                available_reviewers = [s for s in students if s != reviewee]
                
                # Select reviewers (ensure diversity if possible)
                selected_reviewers = available_reviewers[:peer_assessment.number_of_reviewers]
                
                # Create peer review assignments
                for reviewer in selected_reviewers:
                    PeerReview.objects.create(
                        peer_assessment=peer_assessment,
                        reviewer=reviewer,
                        reviewee=reviewee
                    )
                
                # Update status
                peer_assessment.status = 'ASSIGNED'
                peer_assessment.save()
                
        except Exception as e:
            logger.error(f"Error assigning reviewers: {e}")
            raise
    
    def submit_peer_review(self, review_id: int, review_data: Dict[str, Any]) -> PeerReview:
        """Submit a peer review"""
        try:
            review = PeerReview.objects.get(id=review_id)
            
            # Update review with submitted data
            review.scores = review_data.get('scores', {})
            review.overall_score = review_data.get('overall_score')
            review.qualitative_feedback = review_data.get('qualitative_feedback', '')
            review.suggestions_for_improvement = review_data.get('suggestions_for_improvement', '')
            review.time_spent_minutes = review_data.get('time_spent_minutes')
            review.status = 'COMPLETED'
            review.submitted_at = timezone.now()
            
            # Calculate review quality using AI
            if self.ai_service:
                review.review_quality_score = self._calculate_review_quality(review)
            
            review.save()
            
            # Check if all reviews for this peer assessment are complete
            peer_assessment = review.peer_assessment
            total_reviews = peer_assessment.reviews.count()
            completed_reviews = peer_assessment.reviews.filter(status='COMPLETED').count()
            
            if completed_reviews == total_reviews:
                peer_assessment.status = 'COMPLETED'
                peer_assessment.save()
                
                # Generate aggregated feedback
                self._generate_aggregated_feedback(peer_assessment)
            
            return review
            
        except Exception as e:
            logger.error(f"Error submitting peer review: {e}")
            raise
    
    def _calculate_review_quality(self, review: PeerReview) -> float:
        """Calculate the quality of a peer review using AI analysis"""
        try:
            feedback_text = f"{review.qualitative_feedback} {review.suggestions_for_improvement}"
            
            if len(feedback_text.strip()) < 20:
                return 0.2  # Very low quality for minimal feedback
            
            # Use AI to assess review quality
            prompt = f"""
            Evaluate the quality of this peer review feedback on a scale of 0.0 to 1.0.
            Consider factors like:
            - Constructiveness and helpfulness
            - Specificity and detail
            - Balance of positive and critical feedback
            - Actionable suggestions
            - Clarity and coherence
            
            Feedback to evaluate:
            {feedback_text}
            
            Respond with only a number between 0.0 and 1.0.
            """
            
            if self.ai_service:
                response = self.ai_service.generate_text(prompt)
                # Extract score from response
                score_match = re.search(r'0\.\d+|1\.0', response)
                if score_match:
                    return float(score_match.group())
            
            # Fallback: simple heuristic based on length and keywords
            quality_keywords = ['specific', 'improve', 'suggest', 'consider', 'strength', 'weakness']
            keyword_count = sum(1 for word in quality_keywords if word in feedback_text.lower())
            length_score = min(1.0, len(feedback_text) / 200)  # Normalize by 200 characters
            keyword_score = min(1.0, keyword_count / 3)  # Normalize by 3 keywords
            
            return (length_score + keyword_score) / 2
            
        except Exception as e:
            logger.error(f"Error calculating review quality: {e}")
            return 0.5  # Default middle score
    
    def _generate_aggregated_feedback(self, peer_assessment: PeerAssessment):
        """Generate aggregated feedback from all peer reviews"""
        try:
            reviews = peer_assessment.reviews.filter(status='COMPLETED')
            
            if not reviews.exists():
                return
            
            # Aggregate scores
            aggregated_scores = {}
            all_feedback = []
            all_suggestions = []
            
            for review in reviews:
                # Aggregate numerical scores
                for criterion, score in review.scores.items():
                    if criterion not in aggregated_scores:
                        aggregated_scores[criterion] = []
                    aggregated_scores[criterion].append(score)
                
                # Collect feedback
                if review.qualitative_feedback:
                    all_feedback.append(review.qualitative_feedback)
                if review.suggestions_for_improvement:
                    all_suggestions.append(review.suggestions_for_improvement)
            
            # Calculate average scores
            final_scores = {}
            for criterion, scores in aggregated_scores.items():
                final_scores[criterion] = sum(scores) / len(scores)
            
            # Generate AI-powered summary if available
            summary_feedback = ""
            if self.ai_service and all_feedback:
                summary_feedback = self._generate_feedback_summary(all_feedback, all_suggestions)
            
            # Store aggregated results
            response = peer_assessment.student_work
            if not hasattr(response, 'peer_feedback'):
                response.peer_feedback = {}
            
            response.peer_feedback.update({
                'aggregated_scores': final_scores,
                'average_overall_score': sum(r.overall_score for r in reviews if r.overall_score) / reviews.count(),
                'feedback_summary': summary_feedback,
                'individual_feedback': [
                    {
                        'feedback': r.qualitative_feedback,
                        'suggestions': r.suggestions_for_improvement,
                        'quality_score': r.review_quality_score
                    }
                    for r in reviews
                ],
                'review_count': reviews.count(),
                'generated_at': timezone.now().isoformat()
            })
            
            response.save()
            
        except Exception as e:
            logger.error(f"Error generating aggregated feedback: {e}")
    
    def _generate_feedback_summary(self, feedback_list: List[str], suggestions_list: List[str]) -> str:
        """Generate AI-powered summary of peer feedback"""
        try:
            all_text = " ".join(feedback_list + suggestions_list)
            
            prompt = f"""
            Summarize the following peer review feedback into a coherent, helpful summary.
            Focus on:
            - Common themes and patterns
            - Key strengths identified
            - Main areas for improvement
            - Most actionable suggestions
            
            Peer feedback to summarize:
            {all_text}
            
            Provide a concise summary (2-3 paragraphs) that would be helpful to the student.
            """
            
            if self.ai_service:
                return self.ai_service.generate_text(prompt)
            
            return "Peer feedback summary not available."
            
        except Exception as e:
            logger.error(f"Error generating feedback summary: {e}")
            return "Error generating feedback summary."


class SelfReflectionManager:
    """
    Manages self-reflection components for metacognitive learning support.
    """
    
    def __init__(self):
        self.ai_service = get_ai_service()
    
    def create_reflection_prompts(self, assessment: Assessment, reflection_type: str) -> List[str]:
        """Generate contextual reflection prompts based on assessment and type"""
        try:
            base_prompts = {
                'PRE_ASSESSMENT': [
                    "What are your main learning goals for this assessment?",
                    "What strategies do you plan to use to approach this assessment?",
                    "How confident do you feel about the topics covered?",
                    "What do you expect to be the most challenging parts?"
                ],
                'POST_ASSESSMENT': [
                    "How do you feel about your performance on this assessment?",
                    "Which questions or topics did you find most challenging and why?",
                    "What strategies worked well for you during the assessment?",
                    "What would you do differently if you could take this assessment again?",
                    "How well did your preparation match the actual assessment?"
                ],
                'PEER_REVIEW': [
                    "What did you learn from reviewing your peers' work?",
                    "How did the peer feedback compare to your self-assessment?",
                    "Which suggestions from peers do you plan to implement?",
                    "How has reviewing others' work changed your understanding?"
                ],
                'LEARNING_REFLECTION': [
                    "What key concepts have you learned from this assessment experience?",
                    "How does this assessment connect to your overall learning goals?",
                    "What skills have you developed through this process?",
                    "How will you apply what you've learned in future assessments?"
                ]
            }
            
            prompts = base_prompts.get(reflection_type, [])
            
            # Customize prompts based on assessment content if AI is available
            if self.ai_service and assessment.title:
                custom_prompts = self._generate_custom_prompts(assessment, reflection_type, prompts)
                return custom_prompts
            
            return prompts
            
        except Exception as e:
            logger.error(f"Error creating reflection prompts: {e}")
            return ["Reflect on your learning experience."]
    
    def _generate_custom_prompts(self, assessment: Assessment, reflection_type: str, base_prompts: List[str]) -> List[str]:
        """Generate AI-customized reflection prompts"""
        try:
            prompt = f"""
            Generate 4-5 thoughtful reflection questions for a {reflection_type.lower().replace('_', ' ')} 
            about an assessment titled "{assessment.title}" in the {assessment.learning_path} domain.
            
            Base prompts for reference:
            {chr(10).join(f"- {p}" for p in base_prompts)}
            
            Create questions that are:
            - Specific to the subject matter
            - Encourage metacognitive thinking
            - Help students identify learning strategies
            - Promote self-awareness and growth
            
            Return only the questions, one per line.
            """
            
            response = self.ai_service.generate_text(prompt)
            custom_prompts = [line.strip('- ').strip() for line in response.split('\n') if line.strip()]
            
            # Fallback to base prompts if AI generation fails
            return custom_prompts if len(custom_prompts) >= 3 else base_prompts
            
        except Exception as e:
            logger.error(f"Error generating custom prompts: {e}")
            return base_prompts
    
    def submit_reflection(self, assessment: Assessment, student: User, reflection_type: str, 
                         reflection_data: Dict[str, Any]) -> SelfReflection:
        """Submit a self-reflection"""
        try:
            reflection, created = SelfReflection.objects.get_or_create(
                assessment=assessment,
                student=student,
                reflection_type=reflection_type,
                defaults={
                    'prompts': reflection_data.get('prompts', []),
                    'responses': reflection_data.get('responses', {}),
                    'learning_goals': reflection_data.get('learning_goals', []),
                    'achieved_outcomes': reflection_data.get('achieved_outcomes', []),
                    'confidence_level': reflection_data.get('confidence_level'),
                    'effort_level': reflection_data.get('effort_level'),
                    'strategy_effectiveness': reflection_data.get('strategy_effectiveness', {}),
                    'identified_strengths': reflection_data.get('identified_strengths', ''),
                    'identified_weaknesses': reflection_data.get('identified_weaknesses', ''),
                    'improvement_plans': reflection_data.get('improvement_plans', ''),
                    'time_spent_minutes': reflection_data.get('time_spent_minutes'),
                    'completed_at': timezone.now()
                }
            )
            
            if not created:
                # Update existing reflection
                for field, value in reflection_data.items():
                    if hasattr(reflection, field) and value is not None:
                        setattr(reflection, field, value)
                reflection.completed_at = timezone.now()
                reflection.save()
            
            # Generate AI insights if available
            if self.ai_service:
                insights = self._generate_reflection_insights(reflection)
                reflection.ai_insights = insights
                reflection.save()
            
            return reflection
            
        except Exception as e:
            logger.error(f"Error submitting reflection: {e}")
            raise
    
    def _generate_reflection_insights(self, reflection: SelfReflection) -> Dict[str, Any]:
        """Generate AI-powered insights from reflection responses"""
        try:
            responses_text = " ".join(str(v) for v in reflection.responses.values())
            strengths_weaknesses = f"{reflection.identified_strengths} {reflection.identified_weaknesses}"
            
            prompt = f"""
            Analyze this student's self-reflection and provide insights for learning improvement.
            
            Reflection responses: {responses_text}
            Identified strengths/weaknesses: {strengths_weaknesses}
            Confidence level: {reflection.confidence_level}/5
            Effort level: {reflection.effort_level}/5
            
            Provide insights in the following areas:
            1. Learning patterns identified
            2. Metacognitive awareness level
            3. Recommended learning strategies
            4. Areas for development focus
            
            Format as JSON with keys: patterns, awareness_level, strategies, focus_areas
            """
            
            response = self.ai_service.generate_text(prompt)
            
            # Try to parse as JSON, fallback to text
            try:
                insights = json.loads(response)
            except json.JSONDecodeError:
                insights = {'summary': response}
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating reflection insights: {e}")
            return {'error': 'Could not generate insights'}

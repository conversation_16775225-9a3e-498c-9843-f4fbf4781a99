"""
AI Prompt Templates

Consolidated prompt templates for various AI operations.
"""

from typing import Dict, Any


class PromptTemplates:
    """Centralized prompt templates for AI operations."""
    
    @staticmethod
    def course_generation(course_data: Dict[str, Any]) -> str:
        """Generate prompt for course content creation"""
        return f"""
Create comprehensive course content for:

Course Title: {course_data.get('title', 'Unknown Course')}
Description: {course_data.get('description', 'No description provided')}
Level: {course_data.get('level', 'Beginner')}

Please provide a detailed course structure including:
1. Learning Objectives
2. Weekly Schedule
3. Assessment Methods
4. Required Materials
"""
    
    @staticmethod
    def assessment_generation(topic: str, difficulty: str = 'medium') -> str:
        """Generate prompt for assessment creation"""
        return f"""
Create an assessment for the topic: {topic}
Difficulty level: {difficulty}

Generate 5 multiple-choice questions with:
- Clear question text
- 4 answer options
- Correct answer indication
- Brief explanation
"""
    
    @staticmethod
    def tutoring_response(question: str, subject: str = "General") -> str:
        """Generate prompt for tutoring responses"""
        return f"""
You are a helpful educational tutor. Please provide a comprehensive answer to this student question:

Question: {question}
Subject: {subject}

Please provide clear explanations and examples.
"""

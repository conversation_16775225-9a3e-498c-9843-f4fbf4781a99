"""Unified Content Views

This module provides API views for accessing course content from all providers.
"""
import logging
from django.shortcuts import get_object_or_404
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from core.content_types import CONTENT_TYPES
from core.services import ContentProviderService
# from core.services.content_preference_service import ContentPreferenceService
from core.services.content_provider import ModelJSONEncoder

# Import Course model directly
try:
    from .models import Course
except ImportError:
    # Fallback to importing from models module
    from django.apps import apps
    try:
        Course = apps.get_model("courses", "Course")
    except LookupError:
        # Define a placeholder Course model for development
        from django.db import models
        class Course(models.Model):
            title = models.CharField(max_length=200)
            course_code = models.CharField(max_length=10)
            description = models.TextField(blank=True)


class UnifiedContentViewSet(viewsets.ViewSet):
    """ViewSet for accessing unified course content from all providers.
    
    This ViewSet provides endpoints for retrieving course content from all providers
    including standard courses and AI-generated content.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def list(self, request):
        """List all courses with their content providers."""
        # Get all courses
        courses = Course.objects.all()
        
        # Build response data
        data = []
        for course in courses:
            providers = []
            
            # Add standard provider
            providers.append({
                "type": CONTENT_TYPES["STANDARD"],
                "name": "Standard Course Content",
                "description": "Regular course materials and content"
            })
            
            # Add AI-generated provider if available
            if hasattr(course, "has_ai_content") and course.has_ai_content:
                providers.append({
                    "type": CONTENT_TYPES["AI_GENERATED"],
                    "name": "AI-Generated Content",
                    "description": "Content generated by AI"
                })
            
            data.append({
                "id": course.id,
                "title": course.title,
                "course_code": course.course_code,
                "description": course.description,
                "providers": providers
            })
        
        return Response(data)
    
    def retrieve(self, request, pk=None):
        """Retrieve content for a specific course from all providers."""
        # Get the course
        course = get_object_or_404(Course, id=pk)
        
        # Get content from all providers
        content_type = request.query_params.get("content_type")
        provider_type = request.query_params.get("provider_type")
        ignore_preferences = request.query_params.get("ignore_preferences") == "true"
        
        # Get content from all providers
        content = ContentProviderService.get_course_content(
            course.id,
            content_type=content_type,
            provider_type=provider_type
        )
        
        # Apply content type preferences if not explicitly ignored
        # Temporarily disabled due to import issues
        # if not ignore_preferences:
        #     # Get content preferences for this course and user
        #     preferences = ContentPreferenceService.get_content_preferences(
        #         course_id=course.id,
        #         user_id=request.user.id
        #     )
        #
        #     # Filter content based on preferences
        #     content = ContentPreferenceService.filter_content_by_preferences(
        #         content, preferences
        #     )
        #
        #     # Sort content based on preferences
        #     content = ContentPreferenceService.sort_content_by_preferences(
        #         content, preferences
        #     )

        # Include default preferences in the response
        preferences_data = {
            "show_standard_content": True,
            "show_interactive_content": False,
            "show_ai_content": True,
            "default_content_type": CONTENT_TYPES["STANDARD"],
            "source": "default"
        }
        
        # Build response data
        data = {
            "id": course.id,
            "title": course.title,
            "course_code": course.course_code,
            "description": course.description,
            "content": content,
            "content_preferences": preferences_data
        }
        
        # Use the custom JSON encoder to handle model instances
        import json
        json_data = json.dumps(data, cls=ModelJSONEncoder)
        return Response(json.loads(json_data))
    
    @action(detail=True, methods=["get"])
    def content_item(self, request, pk=None):
        """Retrieve a specific content item."""
        # Get the content item ID and provider type from query params
        content_id = request.query_params.get("content_id")
        provider_type = request.query_params.get("provider_type")
        
        if not content_id or not provider_type:
            return Response(
                {"error": "content_id and provider_type are required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get the content item
        content_item = ContentProviderService.get_content_by_id(
            content_id, provider_type
        )
        
        if not content_item:
            return Response(
                {"error": "Content item not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Use the custom JSON encoder to handle model instances
        import json
        json_data = json.dumps(content_item, cls=ModelJSONEncoder)
        return Response(json.loads(json_data))
    
    @action(detail=True, methods=["get"])
    def providers(self, request, pk=None):
        """List all content providers for a course."""
        # Get the course
        course = get_object_or_404(Course, id=pk)
        
        # Build response data
        providers = []
        
        # Add standard provider
        providers.append({
            "type": CONTENT_TYPES["STANDARD"],
            "name": "Standard Course Content",
            "description": "Regular course materials and content"
        })
        
        # Add AI-generated provider if available
        if hasattr(course, "has_ai_content") and course.has_ai_content:
            providers.append({
                "type": CONTENT_TYPES["AI_GENERATED"],
                "name": "AI-Generated Content",
                "description": "Content generated by AI"
            })
        
        return Response(providers)
    
    @action(detail=True, methods=["get"])
    def content_preferences(self, request, pk=None):
        """Get content type preferences for a course and the current user."""
        # Get the course
        course = get_object_or_404(Course, id=pk)
        
        # Get content preferences (temporarily disabled)
        # preferences = ContentPreferenceService.get_content_preferences(
        #     course_id=course.id,
        #     user_id=request.user.id
        # )
        preferences = {
            "show_standard_content": True,
            "show_interactive_content": False,
            "show_ai_content": True,
            "default_content_type": "STANDARD",
            "source": "default"
        }
        
        # Format the response
        data = {
            "show_standard_content": preferences.get("show_standard_content", True),
            "show_interactive_content": preferences.get("show_interactive_content", False),
            "show_ai_content": preferences.get("show_ai_content", False),
            "default_content_type": preferences.get("default_content_type", CONTENT_TYPES["STANDARD"]),
            "source": preferences.get("source", "system")
        }
        
        # Include override reason if available
        if "override_reason" in preferences:
            data["override_reason"] = preferences["override_reason"]
        
        return Response(data)
    
    @action(detail=True, methods=["post"])
    def update_progress(self, request, pk=None):
        """Update progress for a content item."""
        # Get the course
        course = get_object_or_404(Course, id=pk)
        
        # Get the content item ID, content type, and progress data from request data
        content_id = request.data.get("content_id")
        content_type = request.data.get("content_type")
        progress_data = request.data.get("progress_data", {})
        
        if not content_id or not content_type:
            return Response(
                {"error": "content_id and content_type are required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get the content object based on type
        try:
            content_object = None
            if content_type.startswith("material"):
                # Handle material content
                from courses.models import Material
                content_object = Material.objects.get(id=content_id)
            elif content_type.startswith("ai_generated"):
                # Handle AI-generated content
                from course_generator.models import GeneratedContent
                content_object = GeneratedContent.objects.get(id=content_id)
            else:
                # Default to course
                content_object = course
            
            if not content_object:
                return Response(
                    {"error": f"Content object of type {content_type} with id {content_id} not found"},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Get the progress service
            from core.services.progress_service import progress_service
            
            # Extract progress percentage and completion status
            percentage = progress_data.get("completion_percentage", 0)
            is_completed = progress_data.get("is_completed", False)
            
            # Log the progress update attempt
            logger = logging.getLogger(__name__)
            logger.info(f"Updating progress for user {request.user.username}, content {content_object}, type {content_type}")
            logger.info(f"Progress data: {progress_data}")
            
            # Update progress
            try:
                result = progress_service.update_progress(
                    user=request.user,
                    content_object=content_object,
                    percentage=percentage,
                    completed=is_completed,
                    data=progress_data
                )
                logger.info(f"Progress updated successfully: {result}")
            except Exception as e:
                logger.error(f"Error updating progress: {e}")
                import traceback
                logger.error(traceback.format_exc())
                raise
            
            return Response({
                "content_id": content_id,
                "content_type": content_type,
                "progress": {
                    "id": result.id,
                    "completion_percentage": result.completion_percentage,
                    "is_completed": result.is_completed,
                    "last_activity": (
                        result.last_accessed.isoformat() if result.last_accessed else None
                    )
                }
            })
        except Exception as e:
            import traceback
            traceback.print_exc()
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

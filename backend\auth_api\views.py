﻿import logging
from django.contrib.auth import get_user_model
from rest_framework import permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from django.contrib.auth import authenticate

# Configure logger
logger = logging.getLogger(__name__)
User = get_user_model()

class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        # Add custom claims
        token['role'] = getattr(user, 'role', 'STUDENT')
        return token
    
    def validate(self, attrs):
        # Get the username_or_email from the request
        username_or_email = attrs.get(self.username_field)
        password = attrs.get('password')
        
        # Try to find user by username or email
        user = None
        try:
            # First try by username
            user = User.objects.get(username=username_or_email)
        except User.DoesNotExist:
            try:
                # Then try by email
                user = User.objects.get(email=username_or_email)
                # If found by email, replace the username field with actual username
                attrs[self.username_field] = user.username
            except User.DoesNotExist:
                pass
        
        # Now call the parent validate method
        return super().validate(attrs)

class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer
    
    def post(self, request, *args, **kwargs):
        """Override the post method with enhanced logging"""
        username_or_email = request.data.get('username')
        
        logger.info(f"Login attempt for: {username_or_email}")
        
        try:
            response = super().post(request, *args, **kwargs)
            logger.info(f"Authentication successful for user: {username_or_email}")
            return Response({
                "status": "success",
                "message": "Authentication successful",
                "data": response.data
            })
        except Exception as e:
            logger.error(f"Authentication failed for user {username_or_email}: {str(e)}")
            return Response({
                "status": "error", 
                "message": "Invalid username or password"
            }, status=status.HTTP_401_UNAUTHORIZED)

class UserProfileView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get the current user's profile"""
        user = request.user
        return Response({
            "status": "success",
            "data": {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "role": getattr(user, 'role', 'STUDENT'),
                "first_name": user.first_name,
                "last_name": user.last_name
            },
            "message": "User profile retrieved successfully"
        })

class PendingRegistrationsView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        return Response({"data": [], "message": "No pending registrations"})

class PendingRegistrationsCountView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        return Response({"data": {"count": 0}, "message": "No pending registrations"})

class RegistrationApprovalView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, user_id):
        return Response({"message": "Registration approval not implemented yet"})

@api_view(["POST"])
@permission_classes([AllowAny])
def register(request):
    """Handle student registration requests"""
    username = request.data.get('username')
    email = request.data.get('email')
    password = request.data.get('password')
    first_name = request.data.get('first_name', '')
    last_name = request.data.get('last_name', '')
    
    if not username or not email or not password:
        return Response({
            "status": "error",
            "message": "Username, email and password are required"
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name,
            role='STUDENT',
            is_active=True  # For now, activate immediately
        )
        
        return Response({
            "status": "success",
            "data": {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "role": user.role
            },
            "message": "Registration successful"
        }, status=status.HTTP_201_CREATED)
        
    except Exception as e:
        return Response({
            "status": "error",
            "message": str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

"""Centralizedserviceforhandlingstudentleveloperations"""import loggingfrom django.appsimportappsfrom django.confimportsettingsfrom django.dbimporttransactionfrom django.utilsimport timezonelogger=logging.getLogger(__name__)#StandardizedleveldefinitionsLEVEL_DEFINITIONS={1:{"name":"Beginner""required_courses":2"required_score":70}2:{"name":"Elementary""required_courses":3"required_score":75}3:{"name":"Intermediate""required_courses":3"required_score":80}4:{"name":"Advanced""required_courses":4"required_score":85}5:{"name":"Expert""required_courses":5"required_score":90}}classLevelService:"""Centralizedserviceforhandlingstudentleveloperations"""@staticmethoddefget_student_level(student_id):"""Getastudent'scurrentlevel"""StudentLevel=apps.get_model("assessment""StudentLevel")try:returnStudentLevel.objects.get(student_id=student_id)exceptStudentLevel.DoesNotExist:User=apps.get_model(settings.AUTH_USER_MODEL.split(".")[0]settings.AUTH_USER_MODEL.split(".")[1])try:student=User.objects.get(id=student_id)#CreatewithaninitialhistoryentryreturnStudentLevel.objects.create(student=studentcurrent_level=1current_level_display="Beginner"progression_history=[{"id":f"{student.id}-0""date":timezone.now().isoformat()"from_level":1"from_level_display":"Beginner""to_level":1"to_level_display":"Beginner""reason":"Initiallevel""changed_by":"System""level_changed":False}])exceptUser.DoesNotExist:#Returnadefaultleveliftheuserdoesn'texistlogger.error(f"UserwithID{student_id}notfound")returnNone@staticmethoddefupdate_student_level(student_idnew_levelreason="Assessment"admin_id=Noneassessment_id=None):"""Updateastudent'slevel"""StudentLevel=apps.get_model("assessment""StudentLevel")User=apps.get_model(settings.AUTH_USER_MODEL.split(".")[0]settings.AUTH_USER_MODEL.split(".")[1])withtransaction.atomic():#Getorcreatethestudentlevelstudent_level=LevelService.get_student_level(student_id)#Ifstudent_levelisNonetrytocreateitifstudent_levelisNone:logger.info(f"CreatingnewstudentlevelforstudentID{student_id}")try:#Getthestudentstudent=User.objects.get(id=student_id)#Createanewstudentlevelstudent_level=StudentLevel.objects.create(student=studentcurrent_level=new_levelcurrent_level_display=LEVEL_DEFINITIONS.get(new_level{}).get("name""Unknown")last_assessment_date=timezone.now()progression_history=[{"id":f"{student_id}-0""date":timezone.now().isoformat()"from_level":1"from_level_display":"Beginner""to_level":new_level"to_level_display":LEVEL_DEFINITIONS.get(new_level{}).get("name""Unknown")"reason":reason"changed_by":"Admin"ifadmin_idelse"System""level_changed":new_level!=1}])#Logthelevelcreationlogger.info(f"Creatednewstudentlevelforstudent{student_id}withlevel{new_level}")returnstudent_levelexceptUser.DoesNotExist:logger.error(f"Cannotupdatelevel:UserwithID{student_id}notfound")returnNoneexceptExceptionase:logger.error(f"Errorcreatingstudentlevel:{str(e)}"exc_info=True)returnNone#Don'tupdateifthelevelisthesameifstudent_level.current_level==new_level:logger.info(f"Student{student_id}levelalreadyat{new_level}noupdateneeded")returnstudent_level#Usetheupdate_levelmethodtoensureconsistenthistoryentries#Createamockassessmentobjectifassessment_idisprovidedassessment=Noneifassessment_id:assessment={"id":assessment_id}#Updatethelevelusingthemodel'smethodstudent_level.update_level(new_level=new_levelreason=reasonassessment=assessmentadmin_id=admin_id)#Logthelevelchangelogger.info(f"Student{student_id}levelupdatedto{new_level}."f"Reason:{reason}")returnstudent_level@staticmethoddefget_level_progression_data(student_id):"""Getlevelprogressiondataforastudent"""StudentLevel=apps.get_model("assessment""StudentLevel")student_level=LevelService.get_student_level(student_id)#GetcompletedcoursesforthestudentUser=apps.get_model(settings.AUTH_USER_MODEL.split(".")[0]settings.AUTH_USER_MODEL.split(".")[1])student=User.objects.get(id=student_id)#GetthemostrecentassessmentAssessment=apps.get_model("assessment""Assessment")last_assessment=(Assessment.objects.filter(student=studentstatus="COMPLETED").order_by("-end_time").first())#Buildlevelprogressiondatalevels_data=[]forleveldefinitioninLEVEL_DEFINITIONS.items():#Countcompletedcoursesatthislevelcompleted_courses=(student.completed_courses.filter(difficulty_level=level).count()ifhasattr(student"completed_courses")else0)#Checkifthislevelhasbeenachievedachieved_date=Noneforentryinstudent_level.progression_history:ifentry.get("to_level")==level:achieved_date=entry.get("date")breaklevels_data.append({"level":level"display_name":definition["name"]"achieved_date":achieved_date"requirements":{"courses_completed":completed_courses"required_courses":definition["required_courses"]"assessment_score":(last_assessment.scoreiflast_assessmentelse0)"required_score":definition["required_score"]}})#Calculateprogresstowardsnextlevelcurrent_level=student_level.current_levelnext_level=current_level+1ifcurrent_level<5else5#Ifatmaxlevelprogressis100%ifnext_level==current_level:next_level_progress=100else:#Calculateprogressbasedoncoursesandassessmentscorenext_level_def=LEVEL_DEFINITIONS.get(next_level{})#Courseprogresscompleted_courses=(student.completed_courses.filter(difficulty_level=current_level).count()ifhasattr(student"completed_courses")else0)required_courses=next_level_def.get("required_courses"1)course_progress=min(100(completed_courses/required_courses)*100)#Assessmentprogressrequired_score=next_level_def.get("required_score"100)assessment_score=last_assessment.scoreiflast_assessmentelse0assessment_progress=min(100(assessment_score/required_score)*100)#Overallprogress(averageofcourseandassessmentprogress)next_level_progress=(course_progress+assessment_progress)/2#Checkifstudentcanadvancetonextlevelcan_advance=LevelService.can_advance_level(student_id)return{"student_id":student_id"levels":levels_data"current_level":current_level"next_level_progress":next_level_progress"can_advance":can_advance}@staticmethoddefcan_advance_level(student_id):"""Checkifastudentcanadvancetothenextlevel"""student_level=LevelService.get_student_level(student_id)current_level=student_level.current_level#Ifalreadyatmaxlevelcan'tadvanceifcurrent_level>=5:returnFalse#Getrequirementsfornextlevelnext_level=current_level+1next_level_def=LEVEL_DEFINITIONS.get(next_level{})#GetstudentandcheckcompletedcoursesUser=apps.get_model(settings.AUTH_USER_MODEL.split(".")[0]settings.AUTH_USER_MODEL.split(".")[1])student=User.objects.get(id=student_id)completed_courses=(student.completed_courses.filter(difficulty_level=current_level).count()ifhasattr(student"completed_courses")else0)ifcompleted_courses<next_level_def.get("required_courses"1):returnFalse#CheckassessmentscoreAssessment=apps.get_model("assessment""Assessment")last_assessment=(Assessment.objects.filter(student=studentstatus="COMPLETED").order_by("-end_time").first())ifnotlast_assessment:returnFalserequired_score=next_level_def.get("required_score"100)returnlast_assessment.score>=required_score@staticmethoddefget_all_student_levels():"""Getallstudentlevels(foradminuse)"""StudentLevel=apps.get_model("assessment""StudentLevel")returnStudentLevel.objects.all().order_by("-last_assessment_date")level_service=LevelService()
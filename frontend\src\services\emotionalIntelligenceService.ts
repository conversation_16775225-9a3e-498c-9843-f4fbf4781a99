/**
 * Emotional Intelligence Service
 * 
 * Detects and adapts to student emotional states during learning
 */

import { EmotionalLearningState } from './personalizedLearningService';

interface TypingPattern {
  average_typing_speed: number; // characters per minute
  pause_frequency: number; // pauses per minute
  backspace_frequency: number; // corrections per minute
  typing_rhythm_variance: number; // consistency measure
  session_duration: number; // minutes
}

interface InteractionMetrics {
  response_time: number; // seconds to respond
  error_rate: number; // percentage of incorrect responses
  help_seeking_frequency: number; // requests for help per session
  task_completion_rate: number; // percentage of completed tasks
  session_engagement_score: number; // calculated engagement metric
}

class EmotionalIntelligenceService {
  private typingHistory: Map<string, TypingPattern[]> = new Map();
  private interactionHistory: Map<string, InteractionMetrics[]> = new Map();

  /**
   * Analyze typing patterns to detect emotional state
   */
  async detectEmotionalState(
    userId: string,
    interactionData: any[]
  ): Promise<EmotionalLearningState> {
    try {
      const typingPattern = this.analyzeTypingPattern(interactionData);
      const interactionMetrics = this.analyzeInteractionMetrics(interactionData);
      
      return {
        current_mood: this.inferMood(typingPattern, interactionMetrics),
        stress_level: this.calculateStressLevel(typingPattern, interactionMetrics),
        confidence_level: this.assessConfidence(interactionMetrics),
        engagement_level: this.measureEngagement(interactionMetrics),
        learning_readiness: this.evaluateReadiness(typingPattern, interactionMetrics),
        detected_at: new Date().toISOString()
      };
    } catch (error) {
      console.warn('Emotional state detection failed, using neutral state:', error);
      return this.getNeutralEmotionalState();
    }
  }

  /**
   * Adapt content based on emotional state
   */
  async adaptContentToEmotion(
    content: string,
    emotionalState: EmotionalLearningState
  ): Promise<string> {
    let adaptedContent = content;

    // High stress adaptation
    if (emotionalState.stress_level > 7) {
      adaptedContent = this.simplifyContent(adaptedContent);
      adaptedContent = this.addCalming(adaptedContent);
    }

    // Low confidence adaptation
    if (emotionalState.confidence_level < 4) {
      adaptedContent = this.addEncouragement(adaptedContent);
      adaptedContent = this.addSupportiveLanguage(adaptedContent);
    }

    // Low engagement adaptation
    if (emotionalState.engagement_level < 5) {
      adaptedContent = this.gamifyContent(adaptedContent);
      adaptedContent = this.addInteractiveElements(adaptedContent);
    }

    // Frustrated mood adaptation
    if (emotionalState.current_mood === 'frustrated') {
      adaptedContent = this.addBreakSuggestion(adaptedContent);
      adaptedContent = this.simplifyLanguage(adaptedContent);
    }

    // Anxious mood adaptation
    if (emotionalState.current_mood === 'anxious') {
      adaptedContent = this.addReassurance(adaptedContent);
      adaptedContent = this.breakIntoSteps(adaptedContent);
    }

    return adaptedContent;
  }

  /**
   * Provide emotional support suggestions
   */
  async getEmotionalSupport(
    emotionalState: EmotionalLearningState
  ): Promise<string[]> {
    const suggestions: string[] = [];

    if (emotionalState.stress_level > 7) {
      suggestions.push(
        "Take a 5-minute break and try some deep breathing",
        "Consider breaking this into smaller chunks",
        "Remember: it's okay to take your time"
      );
    }

    if (emotionalState.confidence_level < 4) {
      suggestions.push(
        "You're doing great! Every expert was once a beginner",
        "Try reviewing the basics first",
        "Consider asking for help - it's a sign of strength"
      );
    }

    if (emotionalState.engagement_level < 5) {
      suggestions.push(
        "Try a different learning approach",
        "Set a small, achievable goal",
        "Reward yourself for progress made"
      );
    }

    return suggestions;
  }

  // Private helper methods
  private analyzeTypingPattern(interactionData: any[]): TypingPattern {
    // Simplified analysis - in production, this would be more sophisticated
    const recentInteractions = interactionData.slice(-10);
    
    return {
      average_typing_speed: this.calculateTypingSpeed(recentInteractions),
      pause_frequency: this.calculatePauseFrequency(recentInteractions),
      backspace_frequency: this.calculateBackspaceFrequency(recentInteractions),
      typing_rhythm_variance: this.calculateRhythmVariance(recentInteractions),
      session_duration: this.calculateSessionDuration(recentInteractions)
    };
  }

  private analyzeInteractionMetrics(interactionData: any[]): InteractionMetrics {
    const recentInteractions = interactionData.slice(-10);
    
    return {
      response_time: this.calculateAverageResponseTime(recentInteractions),
      error_rate: this.calculateErrorRate(recentInteractions),
      help_seeking_frequency: this.calculateHelpSeekingFrequency(recentInteractions),
      task_completion_rate: this.calculateCompletionRate(recentInteractions),
      session_engagement_score: this.calculateEngagementScore(recentInteractions)
    };
  }

  private inferMood(
    typing: TypingPattern,
    interaction: InteractionMetrics
  ): EmotionalLearningState['current_mood'] {
    // Simplified mood inference logic
    if (interaction.error_rate > 0.3 && typing.backspace_frequency > 10) {
      return 'frustrated';
    }
    if (interaction.response_time > 30 && typing.pause_frequency > 5) {
      return 'anxious';
    }
    if (interaction.task_completion_rate > 0.8 && interaction.session_engagement_score > 7) {
      return 'confident';
    }
    if (interaction.session_engagement_score > 8) {
      return 'motivated';
    }
    if (interaction.help_seeking_frequency > 3) {
      return 'curious';
    }
    return 'neutral';
  }

  private calculateStressLevel(
    typing: TypingPattern,
    interaction: InteractionMetrics
  ): number {
    let stress = 1;
    
    // High error rate indicates stress
    stress += interaction.error_rate * 3;
    
    // Frequent backspaces indicate stress
    stress += Math.min(typing.backspace_frequency / 5, 3);
    
    // Long response times can indicate stress
    stress += Math.min(interaction.response_time / 10, 2);
    
    // High variance in typing rhythm indicates stress
    stress += Math.min(typing.typing_rhythm_variance * 2, 2);
    
    return Math.min(Math.round(stress), 10);
  }

  private assessConfidence(interaction: InteractionMetrics): number {
    let confidence = 5; // Start neutral
    
    // High completion rate increases confidence
    confidence += interaction.task_completion_rate * 3;
    
    // Low error rate increases confidence
    confidence += (1 - interaction.error_rate) * 2;
    
    // Quick responses can indicate confidence
    if (interaction.response_time < 10) {
      confidence += 1;
    }
    
    // Too much help seeking might indicate low confidence
    confidence -= Math.min(interaction.help_seeking_frequency / 2, 2);
    
    return Math.max(1, Math.min(Math.round(confidence), 10));
  }

  private measureEngagement(interaction: InteractionMetrics): number {
    return Math.max(1, Math.min(Math.round(interaction.session_engagement_score), 10));
  }

  private evaluateReadiness(
    typing: TypingPattern,
    interaction: InteractionMetrics
  ): number {
    let readiness = 5;
    
    // Good engagement indicates readiness
    readiness += (interaction.session_engagement_score - 5) / 2;
    
    // Consistent typing indicates readiness
    readiness += (5 - typing.typing_rhythm_variance) / 2;
    
    // Reasonable response times indicate readiness
    if (interaction.response_time > 5 && interaction.response_time < 20) {
      readiness += 1;
    }
    
    return Math.max(1, Math.min(Math.round(readiness), 10));
  }

  private getNeutralEmotionalState(): EmotionalLearningState {
    return {
      current_mood: 'neutral',
      stress_level: 3,
      confidence_level: 5,
      engagement_level: 5,
      learning_readiness: 5,
      detected_at: new Date().toISOString()
    };
  }

  // Content adaptation methods
  private simplifyContent(content: string): string {
    // Add simplification markers and break complex sentences
    return content.replace(/\. /g, '.\n\n')
                 .replace(/^/, '🌟 **Simplified explanation:**\n\n');
  }

  private addCalming(content: string): string {
    const calmingPhrases = [
      "Take your time with this - there's no rush.",
      "Remember to breathe and stay relaxed.",
      "You're doing fine, let's take this step by step."
    ];
    const randomPhrase = calmingPhrases[Math.floor(Math.random() * calmingPhrases.length)];
    return `💙 ${randomPhrase}\n\n${content}`;
  }

  private addEncouragement(content: string): string {
    const encouragements = [
      "You're making great progress!",
      "Keep up the excellent work!",
      "You've got this - I believe in you!",
      "Every step forward is an achievement!"
    ];
    const randomEncouragement = encouragements[Math.floor(Math.random() * encouragements.length)];
    return `🌟 ${randomEncouragement}\n\n${content}`;
  }

  private addSupportiveLanguage(content: string): string {
    return content.replace(/you must/gi, 'you can')
                 .replace(/you should/gi, 'you might want to')
                 .replace(/wrong/gi, 'not quite right')
                 .replace(/error/gi, 'learning opportunity');
  }

  private gamifyContent(content: string): string {
    return `🎮 **Learning Challenge:**\n\n${content}\n\n🏆 Complete this to earn progress points!`;
  }

  private addInteractiveElements(content: string): string {
    return `${content}\n\n💡 **Quick Check:** Can you think of an example of this concept?\n\n🤔 **Reflection:** How does this relate to what you already know?`;
  }

  private addBreakSuggestion(content: string): string {
    return `⏸️ **Feeling stuck?** Consider taking a 5-minute break before continuing.\n\n${content}`;
  }

  private simplifyLanguage(content: string): string {
    return content.replace(/utilize/gi, 'use')
                 .replace(/demonstrate/gi, 'show')
                 .replace(/implement/gi, 'do')
                 .replace(/consequently/gi, 'so')
                 .replace(/therefore/gi, 'so');
  }

  private addReassurance(content: string): string {
    return `🤗 **Don't worry** - this is a common topic that many students find challenging at first.\n\n${content}\n\n✨ **Remember:** Making mistakes is part of learning!`;
  }

  private breakIntoSteps(content: string): string {
    const sentences = content.split('. ');
    if (sentences.length > 2) {
      return sentences.map((sentence, index) => 
        `**Step ${index + 1}:** ${sentence}${sentence.endsWith('.') ? '' : '.'}`
      ).join('\n\n');
    }
    return content;
  }

  // Simplified calculation methods (in production, these would be more sophisticated)
  private calculateTypingSpeed(interactions: any[]): number {
    return 120; // Default WPM
  }

  private calculatePauseFrequency(interactions: any[]): number {
    return 2; // Default pauses per minute
  }

  private calculateBackspaceFrequency(interactions: any[]): number {
    return 3; // Default corrections per minute
  }

  private calculateRhythmVariance(interactions: any[]): number {
    return 2; // Default variance
  }

  private calculateSessionDuration(interactions: any[]): number {
    return 15; // Default session duration in minutes
  }

  private calculateAverageResponseTime(interactions: any[]): number {
    return 12; // Default response time in seconds
  }

  private calculateErrorRate(interactions: any[]): number {
    return 0.1; // Default 10% error rate
  }

  private calculateHelpSeekingFrequency(interactions: any[]): number {
    return 1; // Default help requests per session
  }

  private calculateCompletionRate(interactions: any[]): number {
    return 0.8; // Default 80% completion rate
  }

  private calculateEngagementScore(interactions: any[]): number {
    return 6; // Default engagement score
  }
}

export const emotionalIntelligenceService = new EmotionalIntelligenceService();
export default emotionalIntelligenceService;

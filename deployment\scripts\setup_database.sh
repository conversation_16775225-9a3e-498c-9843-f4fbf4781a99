#!/bin/bash

# Database Setup Script for North Star University
# This script sets up PostgreSQL database for production

set -e

# Configuration
DB_NAME="north_star_university"
DB_USER="north_star_user"
PROJECT_NAME="north-star-university"
PROJECT_DIR="/var/www/$PROJECT_NAME"
BACKEND_DIR="$PROJECT_DIR/backend"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    log "Installing PostgreSQL..."
    sudo apt update
    sudo apt install -y postgresql postgresql-contrib
fi

# Start PostgreSQL service
log "Starting PostgreSQL service..."
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Generate random password for database user
DB_PASSWORD=$(openssl rand -base64 32)

log "Setting up database and user..."

# Create database and user
sudo -u postgres psql << EOF
-- Create database
CREATE DATABASE $DB_NAME;

-- Create user
CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';

-- Grant privileges
ALTER ROLE $DB_USER SET client_encoding TO 'utf8';
ALTER ROLE $DB_USER SET default_transaction_isolation TO 'read committed';
ALTER ROLE $DB_USER SET timezone TO 'UTC';
GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;

-- Exit
\q
EOF

log "Database setup completed!"
log "Database Name: $DB_NAME"
log "Database User: $DB_USER"
log "Database Password: $DB_PASSWORD"

# Update .env.production file with database credentials
ENV_FILE="$PROJECT_DIR/deployment/.env.production"
if [ -f "$ENV_FILE" ]; then
    log "Updating .env.production with database credentials..."
    
    # Create backup
    cp "$ENV_FILE" "${ENV_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
    
    # Update database settings
    sed -i "s/DB_NAME=.*/DB_NAME=$DB_NAME/" "$ENV_FILE"
    sed -i "s/DB_USER=.*/DB_USER=$DB_USER/" "$ENV_FILE"
    sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASSWORD/" "$ENV_FILE"
    
    log "Environment file updated!"
else
    warning "Environment file not found at $ENV_FILE"
    log "Please manually update your environment file with these database credentials:"
    log "DB_NAME=$DB_NAME"
    log "DB_USER=$DB_USER"
    log "DB_PASSWORD=$DB_PASSWORD"
fi

# Test database connection
log "Testing database connection..."
if PGPASSWORD=$DB_PASSWORD psql -h localhost -U $DB_USER -d $DB_NAME -c "SELECT version();" > /dev/null 2>&1; then
    log "Database connection successful!"
else
    error "Database connection failed!"
fi

# Run Django migrations if backend is available
if [ -d "$BACKEND_DIR" ] && [ -f "$BACKEND_DIR/manage.py" ]; then
    log "Running Django migrations..."
    cd $BACKEND_DIR
    
    # Activate virtual environment if it exists
    if [ -d "$PROJECT_DIR/venv" ]; then
        source "$PROJECT_DIR/venv/bin/activate"
    fi
    
    # Copy environment file to backend directory
    cp "$ENV_FILE" "$BACKEND_DIR/.env.production"
    
    # Run migrations
    python manage.py migrate --settings=settings.production
    
    log "Django migrations completed!"
else
    warning "Django backend not found. Please run migrations manually after deployment."
fi

log "Database setup completed successfully!"
log ""
log "IMPORTANT: Save these database credentials securely:"
log "Database Name: $DB_NAME"
log "Database User: $DB_USER"
log "Database Password: $DB_PASSWORD"

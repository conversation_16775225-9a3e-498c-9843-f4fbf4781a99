from rest_framework import serializers

try:
    from ..course_relationships import CourseRelationship
except ImportError:
    # Create a placeholder if the model doesn't exist
    CourseRelationship = None


class CourseRelationshipSerializer(serializers.ModelSerializer):
    """Serializer for course relationships"""
    from_course_code = serializers.SerializerMethodField()
    to_course_code = serializers.SerializerMethodField()
    relationship_type_display = serializers.CharField(
        source="get_relationship_type_display",
        read_only=True
    )

    class Meta:
        model = CourseRelationship
        fields = [
            "id", "from_course", "from_course_code", "to_course",
            "to_course_code", "relationship_type", "relationship_type_display",
            "description", "recommended", "created_at", "updated_at"
        ]

    def get_from_course_code(self, obj):
        if obj.from_course:
            return obj.from_course.course_code
        return None

    def get_to_course_code(self, obj):
        if obj.to_course:
            return obj.to_course.course_code
        return None
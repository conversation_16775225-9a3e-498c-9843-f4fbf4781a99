import { configureStore, combineReducers, Middleware } from '@reduxjs/toolkit';
import { authReducer } from '../features/auth/authSlice';
import adminReducer from '../features/admin/adminSlice';
import { coursesReducer } from '../features/admin/courses';
import studentReducer from '../features/student/studentSlice';
import gradesReducer from '../features/student/gradeSlice';
import chatReducer from '../features/chat/chatSlice';
import themeReducer from '../features/theme/themeSlice';
import studyTimeReducer from '../features/study-time/studyTimeSlice';
import transliterationReducer from '../redux/slices/transliterationSlice';
import progressionReducer from '../store/slices/progressionSlice';

// Enhanced state management slices
import uiStateReducer from './slices/uiStateSlice';
import errorStateReducer from './slices/errorStateSlice';
import cacheStateReducer from './slices/cacheStateSlice';

// Create the root reducer
const rootReducer = combineReducers({
  // Existing reducers
  auth: authReducer,
  admin: adminReducer,
  courses: coursesReducer,
  student: studentReducer,
  grades: gradesReducer,
  chat: chatReducer,
  theme: themeReducer,
  studyTime: studyTimeReducer,
  transliteration: transliterationReducer,
  progression: progressionReducer,
  
  // Enhanced state management
  uiState: uiStateReducer,
  errorState: errorStateReducer,
  cache: cacheStateReducer,
});

// Error handling middleware
const errorHandlingMiddleware: Middleware = (store) => (next) => (action) => {
  try {
    return next(action);
  } catch (error) {
    console.error('Redux Error:', error);
    
    // Dispatch error to error state
    store.dispatch({
      type: 'errorState/setError',
      payload: {
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        action: action.type,
      },
    });
    
    throw error;
  }
};

// Performance monitoring middleware
const performanceMiddleware: Middleware = () => (next) => (action) => {
  const start = performance.now();
  const result = next(action);
  const end = performance.now();
  
  if (end - start > 16) { // Log slow actions (>16ms)
    console.warn(`Slow Redux action: ${action.type} took ${end - start}ms`);
  }
  
  return result;
};

// State persistence middleware
const persistenceMiddleware: Middleware = (store) => (next) => (action) => {
  const result = next(action);
  
  // Persist certain state changes to localStorage
  const state = store.getState();
  const persistableState = {
    theme: state.theme,
    auth: { 
      isAuthenticated: state.auth.isAuthenticated,
      user: state.auth.user 
    },
  };
  
  try {
    localStorage.setItem('persistedState', JSON.stringify(persistableState));
  } catch (error) {
    console.warn('Failed to persist state:', error);
  }
  
  return result;
};

// Load persisted state
const loadPersistedState = () => {
  try {
    const persistedState = localStorage.getItem('persistedState');
    return persistedState ? JSON.parse(persistedState) : undefined;
  } catch (error) {
    console.warn('Failed to load persisted state:', error);
    return undefined;
  }
};

// Configure the enhanced store
export const enhancedStore = configureStore({
  reducer: rootReducer,
  preloadedState: loadPersistedState(),
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types for serialization check
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        // Ignore these field paths in all actions
        ignoredActionsPaths: ['meta.arg', 'payload.timestamp'],
        // Ignore these paths in the state
        ignoredPaths: ['cache.data'],
      },
      // Enable immutability and state equality checks in development
      immutableCheck: process.env.NODE_ENV === 'development',
    })
      .concat(errorHandlingMiddleware)
      .concat(performanceMiddleware)
      .concat(persistenceMiddleware),
  devTools: process.env.NODE_ENV === 'development' && {
    name: 'North Star University',
    trace: true,
    traceLimit: 25,
  },
});

// Create typed hooks
export type RootState = ReturnType<typeof rootReducer>;
export type AppDispatch = typeof enhancedStore.dispatch;

// Enhanced selector creators
export const createAppSelector = <T>(
  selector: (state: RootState) => T
) => selector;

// State shape validator for development
if (process.env.NODE_ENV === 'development') {
  enhancedStore.subscribe(() => {
    const state = enhancedStore.getState();
    
    // Validate critical state shapes
    if (!state.auth || typeof state.auth.isAuthenticated !== 'boolean') {
      console.warn('Invalid auth state shape');
    }
    
    if (!state.uiState || typeof state.uiState.loading !== 'boolean') {
      console.warn('Invalid UI state shape');
    }
  });
}

export default enhancedStore;

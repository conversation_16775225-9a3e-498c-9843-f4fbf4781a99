"""
Blockchain Credential Services

This module provides services for managing blockchain operations, such as
minting credentials and NFT achievements, and verifying blockchain data.
"""

import logging
from django.conf import settings
from .blockchain_service import blockchain_service

logger = logging.getLogger(__name__)


class BlockchainCredentialService:
    """Service class for managing blockchain credentials"""

    def __init__(self):
        self.blockchain_service = blockchain_service

    def initiate_minting(self, credential):
        """Initiate the minting process for a credential"""
        logger.info(f"Initiating minting for credential {credential.id}")
        
        # Use real blockchain service
        return self.blockchain_service.mint_credential_on_blockchain(credential)

    def mint_credential(self, credential):
        """Mint a credential on the blockchain"""
        return self.blockchain_service.mint_credential_on_blockchain(credential)

    def verify_credential(self, credential):
        """Verify a credential on the blockchain"""
        return self.blockchain_service.verify_credential_on_blockchain(credential)

    def generate_certificate(self, credential):
        """Generate a certificate for a given credential"""
        logger.info(f"Generating certificate for credential {credential.id}")

        # Enhanced certificate generation with blockchain verification
        verification_result = self.verify_credential(credential)
        
        certificate_data = {
            'url': f"{settings.SITE_URL}/api/v1/blockchain/credentials/{credential.credential_id}/certificate",
            'size_bytes': 204800,
            'format': 'PDF',
            'blockchain_verified': verification_result.get('verified', False),
            'verification_data': verification_result
        }
        return certificate_data

    def get_network_status(self, network):
        """Get blockchain network status"""
        return self.blockchain_service.get_network_status(network)

    def estimate_gas_cost(self, network, operation):
        """Estimate gas cost for blockchain operations"""
        return self.blockchain_service.estimate_gas_cost(network, operation)


class NFTService:
    """Service class for managing NFT achievements"""

    def __init__(self):
        self.blockchain_service = blockchain_service

    def mint_achievement_nft(self, achievement):
        """Mint an NFT achievement on the blockchain"""
        return self.blockchain_service.mint_nft_achievement(achievement)

    def verify_nft_ownership(self, achievement):
        """Verify NFT ownership on the blockchain"""
        # This would use similar logic to credential verification
        # but specifically for NFT ownership
        try:
            network = achievement.blockchain_network
            w3 = self.blockchain_service.web3_manager.get_web3_connection(network)
            contract = self.blockchain_service.web3_manager.get_contract(network, 'NFT')
            
            if contract and achievement.token_id:
                owner = contract.functions.ownerOf(int(achievement.token_id)).call()
                student_wallet = self.blockchain_service._get_student_wallet(
                    achievement.student, network
                )
                
                if student_wallet and owner.lower() == student_wallet.address.lower():
                    return {
                        'verified': True,
                        'owner': owner,
                        'token_id': achievement.token_id,
                        'contract_address': contract.address
                    }
            
            return {'verified': False, 'reason': 'Ownership verification failed'}
            
        except Exception as e:
            logger.error(f"Failed to verify NFT ownership for {achievement.id}: {e}")
            return {'verified': False, 'reason': str(e)}

    def get_nft_metadata(self, achievement):
        """Get NFT metadata from IPFS or fallback storage"""
        if achievement.ipfs_metadata_hash:
            # Try to get from IPFS first
            metadata = self.blockchain_service.ipfs_manager.get_metadata(
                achievement.ipfs_metadata_hash
            )
            if metadata:
                return metadata
        
        # Fallback to database metadata
        return achievement.get_nft_metadata()



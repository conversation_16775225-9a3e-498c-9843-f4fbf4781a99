import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Alert,
  AlertTitle,
  Divider,
  Stack,
  IconButton,
  Collapse,
} from '@mui/material';
import {
  FiAlertCircle as ErrorIcon,
  FiRefreshCw as RefreshIcon,
  FiHome as HomeIcon,
  FiChevronDown as ExpandIcon,
  FiChevronUp as CollapseIcon,
  FiBug as BugIcon,
} from 'react-icons/fi';
import { withTranslation, WithTranslation } from 'react-i18next';

interface ErrorDetails {
  error: Error;
  errorInfo: ErrorInfo;
  timestamp: string;
  userAgent: string;
  url: string;
  userId?: string;
}

interface Props extends WithTranslation {
  children: ReactNode;
  fallback?: ReactNode;
  onReset?: () => void;
  onError?: (errorDetails: ErrorDetails) => void;
  level?: 'page' | 'section' | 'component';
  resetKeys?: Array<string | number>;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
  isExpanded: boolean;
  retryCount: number;
}

class GlobalErrorBoundary extends Component<Props, State> {
  private resetTimeoutId: number | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      isExpanded: false,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    const errorDetails: ErrorDetails = {
      error,
      errorInfo,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.getUserId(),
    };

    // Log error details
    console.error('Error caught by GlobalErrorBoundary:', errorDetails);

    // Call external error reporting service
    this.reportError(errorDetails);

    // Update state with error info
    this.setState({
      error,
      errorInfo,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(errorDetails);
    }
  }

  componentDidUpdate(prevProps: Props): void {
    const { resetKeys } = this.props;
    const { hasError } = this.state;

    if (hasError && prevProps.resetKeys !== resetKeys) {
      if (resetKeys?.some((resetKey, idx) => 
        prevProps.resetKeys?.[idx] !== resetKey
      )) {
        this.handleReset();
      }
    }
  }

  componentWillUnmount(): void {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  private getUserId = (): string | undefined => {
    try {
      const userData = localStorage.getItem('auth');
      return userData ? JSON.parse(userData)?.user?.id : undefined;
    } catch {
      return undefined;
    }
  };

  private reportError = async (errorDetails: ErrorDetails): Promise<void> => {
    try {
      // Send error to monitoring service
      const errorReport = {
        ...errorDetails,
        level: this.props.level || 'component',
        stack: errorDetails.error.stack,
        componentStack: errorDetails.errorInfo.componentStack,
      };

      // Send to error tracking service (e.g., Sentry, LogRocket, etc.)
      if (window.gtag) {
        window.gtag('event', 'exception', {
          description: errorDetails.error.toString(),
          fatal: false,
        });
      }

      // You can also send to your backend error tracking endpoint
      // await fetch('/api/v1/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport),
      // });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  private handleReset = (): void => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      isExpanded: false,
      retryCount: prevState.retryCount + 1,
    }));

    if (this.props.onReset) {
      this.props.onReset();
    }
  };

  private handleRetryWithDelay = (): void => {
    this.setState({ hasError: false });
    
    this.resetTimeoutId = window.setTimeout(() => {
      this.handleReset();
    }, 1000);
  };

  private toggleExpanded = (): void => {
    this.setState(prevState => ({
      isExpanded: !prevState.isExpanded,
    }));
  };

  private getErrorSeverityColor = (): 'error' | 'warning' => {
    const { level } = this.props;
    return level === 'page' ? 'error' : 'warning';
  };

  private renderErrorContent = (): ReactNode => {
    const { t, level = 'component' } = this.props;
    const { error, errorInfo, errorId, isExpanded, retryCount } = this.state;

    const severity = this.getErrorSeverityColor();
    const isPageLevel = level === 'page';

    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          p: isPageLevel ? 4 : 2,
          minHeight: isPageLevel ? '50vh' : 'auto',
        }}
      >
        <Paper
          elevation={isPageLevel ? 3 : 1}
          sx={{
            p: isPageLevel ? 4 : 2,
            maxWidth: isPageLevel ? 600 : '100%',
            width: '100%',
            borderRadius: 2,
            textAlign: 'center',
            borderLeft: `4px solid ${severity === 'error' ? 'error.main' : 'warning.main'}`,
          }}
        >
          <Stack spacing={2} alignItems="center">
            <ErrorIcon 
              color={severity === 'error' ? 'error' : 'warning'} 
              size={isPageLevel ? 60 : 40} 
            />

            <Typography 
              variant={isPageLevel ? 'h5' : 'h6'} 
              component="h2" 
              color={severity}
            >
              {t(`errorBoundary.${level}.title`, 'Something went wrong')}
            </Typography>

            <Typography 
              variant="body2" 
              color="text.secondary"
              sx={{ maxWidth: 400 }}
            >
              {t(
                `errorBoundary.${level}.message`,
                level === 'page' 
                  ? "We're sorry, but an error occurred while loading this page."
                  : "This component encountered an error and couldn't render properly."
              )}
            </Typography>

            {retryCount > 0 && (
              <Alert severity="info" sx={{ mt: 1 }}>
                <Typography variant="caption">
                  {t('errorBoundary.retryAttempt', 'Retry attempt: {{count}}', { count: retryCount })}
                </Typography>
              </Alert>
            )}

            <Stack direction="row" spacing={1} justifyContent="center">
              <Button
                variant="contained"
                color="primary"
                size={isPageLevel ? 'medium' : 'small'}
                startIcon={<RefreshIcon />}
                onClick={retryCount > 2 ? this.handleRetryWithDelay : this.handleReset}
              >
                {t('errorBoundary.tryAgain', 'Try Again')}
              </Button>

              {isPageLevel && (
                <Button
                  variant="outlined"
                  size="medium"
                  onClick={() => (window.location.href = '/')}
                  startIcon={<HomeIcon />}
                >
                  {t('errorBoundary.goHome', 'Go Home')}
                </Button>
              )}

              {errorId && (
                <Button
                  variant="text"
                  size="small"
                  startIcon={<BugIcon />}
                  onClick={this.toggleExpanded}
                  endIcon={isExpanded ? <CollapseIcon /> : <ExpandIcon />}
                >
                  {t('errorBoundary.details', 'Details')}
                </Button>
              )}
            </Stack>

            <Collapse in={isExpanded} sx={{ width: '100%' }}>
              <Alert severity={severity} sx={{ mt: 2, textAlign: 'left' }}>
                <AlertTitle>
                  {t('errorBoundary.errorDetails', 'Error Details')}
                </AlertTitle>
                
                <Typography variant="caption" color="text.secondary">
                  Error ID: {errorId}
                </Typography>
                
                <Typography
                  variant="body2"
                  component="div"
                  sx={{ wordBreak: 'break-word', mt: 1 }}
                >
                  {error?.toString()}
                </Typography>
                
                {errorInfo && isPageLevel && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      {t('errorBoundary.componentStack', 'Component Stack')}:
                    </Typography>
                    <Box
                      component="pre"
                      sx={{
                        mt: 1,
                        p: 1,
                        bgcolor: 'background.default',
                        borderRadius: 1,
                        fontSize: '0.75rem',
                        maxHeight: '200px',
                        overflow: 'auto',
                      }}
                    >
                      {errorInfo.componentStack}
                    </Box>
                  </Box>
                )}
              </Alert>
            </Collapse>
          </Stack>
        </Paper>
      </Box>
    );
  };

  render(): ReactNode {
    if (this.state.hasError) {
      // If a custom fallback is provided, use it
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return this.renderErrorContent();
    }

    return this.props.children;
  }
}

export default withTranslation()(GlobalErrorBoundary);

"""
URL patterns for Syrian dialect API endpoints
"""

from django.urls import path
from .syrian_dialect_views import (
    SyrianDialectConfigView,
    SyrianDialectConversionView,
    SyrianAIResponseView,
    SyrianCulturalContextView,
    SyrianDialectTestView,
    SyrianDialectStatusView
)

urlpatterns = [
    # Syrian dialect configuration
    path('config/', SyrianDialectConfigView.as_view(), name='syrian-dialect-config'),

    # Text conversion to Syrian dialect
    path('convert/', SyrianDialectConversionView.as_view(), name='syrian-dialect-convert'),

    # AI response with Syrian dialect
    path('ai-response/', SyrianAIResponseView.as_view(), name='syrian-ai-response'),

    # Cultural context
    path('cultural-context/', SyrianCulturalContextView.as_view(), name='syrian-cultural-context'),

    # Testing endpoints
    path('test/', SyrianDialectTestView.as_view(), name='syrian-dialect-test'),
    path('status/', SyrianDialectStatusView.as_view(), name='syrian-dialect-status'),
]
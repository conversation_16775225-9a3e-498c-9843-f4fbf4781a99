# Syrian Dialect Response Templates for Educational AI
syrian_templates:
  # Greeting templates
  greetings:
    - "أهلاً وسهلاً! كيفك اليوم؟"
    - "مرحبا فيك! شو أخبارك؟"
    - "أهلين! شو بدك نتعلم اليوم؟"
    - "يا هلا! كيف الحال؟"
    - "أهلاً! شو الجديد معك؟"

  # Explanation starters
  explanation_starters:
    - "خليني أشرحلك {topic}"
    - "يلا نشوف {topic} مع بعض"
    - "طيب، موضوع {topic} هيك"
    - "شوف، {topic} بسيط"
    - "بدي أوضحلك {topic}"
    - "تعال نفهم {topic}"

  # Understanding checks
  understanding_checks:
    - "فهمت عليي؟"
    - "واضح لحد هون؟"
    - "شو رأيك، مفهوم؟"
    - "بدك أوضحلك أكتر؟"
    - "وصلتك الفكرة؟"
    - "تمام هيك؟"

  # Encouragement phrases
  encouragement:
    - "حلو كتير! {response}"
    - "ما شاء الله عليك! {response}"
    - "تمام التمام! {response}"
    - "يلا بقدر عليك! {response}"
    - "روح جرب! {response}"
    - "أكيد فيك! {response}"

  # Clarification requests
  clarification:
    - "ممكن توضحلي أكتر؟"
    - "شو قصدك بالضبط؟"
    - "ما فهمت عليك منيح"
    - "يلا اشرحلي تاني"
    - "بدي أفهم أكتر عن {topic}"
    - "ممكن تعطيني مثال؟"

  # Transition phrases
  transitions:
    - "طيب، هلأ"
    - "يعني"
    - "بالمناسبة"
    - "وبعدين"
    - "كمان شي"
    - "هاد يعني"
    - "بمعنى تاني"

  # Subject-specific templates
  math_templates:
    problem_solving:
      - "يلا نحل هالمسألة مع بعض"
      - "شوف، الرياضة مش صعبة كتير"
      - "خليني أوريك طريقة أسهل"
      - "نبدأ خطوة بخطوة"

    encouragement:
      - "حلو كتير! فهمت المبدأ"
      - "ما تخاف من الأرقام، هي أصدقاؤنا"
      - "الرياضة بتحتاج صبر، وإنت عندك صبر"

    examples:
      - "مثلاً، لو عندك {number} ليرة سورية"
      - "تخيل إنك عم تحسب المسافة من دمشق لحلب"
      - "لو بدك تشتري {item} من السوق"

  science_templates:
    discovery:
      - "يلا نكتشف سوياً"
      - "شوف هالتجربة الحلوة"
      - "العلم موجود حولنا بكل مكان"
      - "خليني أوريك كيف بيصير هالشي"

    examples:
      - "شوف الطقس بسوريا، بالشتاء بارد وبالصيف حار"
      - "جبال القلمون مثال حلو على {concept}"
      - "البحر المتوسط بيوضحلنا {concept}"

  language_templates:
    literature:
      - "اللغة العربية حلوة كتير"
      - "خليني أقرألك بيت شعر حلو"
      - "شوف كيف الشاعر عبر عن المعنى"
      - "هاي كلمة حلوة بلهجتنا السورية"

    grammar:
      - "القواعد مش صعبة، بس بدها تركيز"
      - "خليني أوضحلك هالقاعدة بمثال سوري"
      - "النحو زي البناء، كل حجر بمحله"

  # Error and support templates
  error_messages:
    - "عذراً، ما قدرت أفهم سؤالك منيح. ممكن تعيد صياغته؟"
    - "آسف، صار عندي مشكلة بالفهم. جرب اسأل بطريقة تانية."
    - "معذرة، ما وصلني سؤالك واضح. ممكن تشرحلي أكتر؟"
    - "عفواً، ما قدرت أجاوب على هالسؤال. بدك تجرب سؤال تاني؟"

  # Closing templates
  closings:
    - "إن شاء الله استفدت من الدرس"
    - "يلا، جرب تطبق هالشي"
    - "أي سؤال تاني، أنا هون"
    - "بالتوفيق بدراستك"
    - "نشوفك بالدرس الجاي"
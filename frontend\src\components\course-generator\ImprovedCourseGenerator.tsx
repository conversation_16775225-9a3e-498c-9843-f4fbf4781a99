import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  Card<PERSON>ontent,
  Ty<PERSON>graphy,
  TextField,
  Button,
  <PERSON><PERSON>,
  Step,
  StepLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  LinearProgress,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Tooltip,
  Fade,
  Slide,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  AutoAwesome,
  ExpandMore,
  School,
  Psychology,
  Assessment,
  ContentCopy,
  Download,
  Refresh,
  CheckCircle,
  Error as ErrorIcon,
} from '../icons';

const GeneratorContainer = styled(Box)(({ theme }) => ({
  maxWidth: '1200px',
  margin: '0 auto',
  padding: theme.spacing(3),
}));

const StyledCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.1)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: '20px',
  marginBottom: theme.spacing(3),
}));

const GenerateButton = styled(Button)(({ theme }) => ({
  background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
  borderRadius: '12px',
  padding: theme.spacing(1.5, 4),
  fontSize: '1.1rem',
  fontWeight: 600,
  textTransform: 'none',
  '&:hover': {
    background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 25px rgba(33, 150, 243, 0.3)',
  },
  '&:disabled': {
    background: 'rgba(255, 255, 255, 0.1)',
    color: 'rgba(255, 255, 255, 0.5)',
  },
}));

const ProgressCard = styled(Card)(({ theme }) => ({
  background:
    'linear-gradient(135deg, rgba(33, 150, 243, 0.1), rgba(156, 39, 176, 0.1))',
  border: '1px solid rgba(33, 150, 243, 0.3)',
  borderRadius: '16px',
  padding: theme.spacing(2),
}));

const ResultCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.05)',
  backdropFilter: 'blur(10px)',
  border: '1px solid rgba(255, 255, 255, 0.1)',
  borderRadius: '16px',
  marginTop: theme.spacing(2),
}));

interface CourseGenerationForm {
  title: string;
  description: string;
  department: string;
  level: number;
  credits: number;
  keywords: string[];
  learningObjectives: string[];
  targetAudience: string;
  duration: number;
}

interface GenerationResult {
  courseOutline: any;
  assessments: any[];
  resources: any[];
  status: 'success' | 'error' | 'partial';
  message: string;
}

const ImprovedCourseGenerator: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState<CourseGenerationForm>({
    title: '',
    description: '',
    department: '',
    level: 1,
    credits: 3,
    keywords: [],
    learningObjectives: [],
    targetAudience: '',
    duration: 15,
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [result, setResult] = useState<GenerationResult | null>(null);
  const [currentKeyword, setCurrentKeyword] = useState('');
  const [currentObjective, setCurrentObjective] = useState('');

  const steps = [
    'Basic Information',
    'Course Details',
    'Learning Objectives',
    'Generate & Review',
  ];

  const departments = [
    'Computer Science',
    'Mathematics',
    'Biology',
    'Chemistry',
    'Physics',
    'English',
    'Business',
    'Psychology',
    'Engineering',
    'Art & Design',
  ];

  const handleNext = () => {
    setActiveStep(prevStep => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep(prevStep => prevStep - 1);
  };

  const addKeyword = () => {
    if (
      currentKeyword.trim() &&
      !formData.keywords.includes(currentKeyword.trim())
    ) {
      setFormData(prev => ({
        ...prev,
        keywords: [...prev.keywords, currentKeyword.trim()],
      }));
      setCurrentKeyword('');
    }
  };

  const removeKeyword = (keyword: string) => {
    setFormData(prev => ({
      ...prev,
      keywords: prev.keywords.filter(k => k !== keyword),
    }));
  };

  const addObjective = () => {
    if (
      currentObjective.trim() &&
      !formData.learningObjectives.includes(currentObjective.trim())
    ) {
      setFormData(prev => ({
        ...prev,
        learningObjectives: [
          ...prev.learningObjectives,
          currentObjective.trim(),
        ],
      }));
      setCurrentObjective('');
    }
  };

  const removeObjective = (objective: string) => {
    setFormData(prev => ({
      ...prev,
      learningObjectives: prev.learningObjectives.filter(o => o !== objective),
    }));
  };

  const generateCourse = async () => {
    setIsGenerating(true);
    setGenerationProgress(0);
    setResult(null);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 500);

      // Make API call to course generation service
      const response = await fetch('/api/v1/course-generator/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          course_title: formData.title,
          course_description: formData.description,
          department: formData.department,
          level: formData.level,
          options: {
            keywords: formData.keywords,
            credits: formData.credits,
            target_audience: formData.targetAudience,
            duration_weeks: formData.duration,
            learning_objectives: formData.learningObjectives,
          },
        }),
      });

      clearInterval(progressInterval);
      setGenerationProgress(100);

      const data = await response.json();

      if (data.status === 'success') {
        setResult({
          courseOutline: data.content?.course_outline || {},
          assessments: data.content?.assessments || [],
          resources: data.content?.resources || [],
          status: 'success',
          message: 'Course generated successfully!',
        });
      } else {
        setResult({
          courseOutline: {},
          assessments: [],
          resources: [],
          status: 'error',
          message: data.message || 'Failed to generate course',
        });
      }
    } catch (error) {
      setResult({
        courseOutline: {},
        assessments: [],
        resources: [],
        status: 'error',
        message: 'Network error occurred while generating course',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const canProceed = () => {
    switch (activeStep) {
      case 0:
        return formData.title && formData.description && formData.department;
      case 1:
        return formData.level && formData.credits && formData.targetAudience;
      case 2:
        return formData.learningObjectives.length > 0;
      default:
        return true;
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <TextField
              fullWidth
              label='Course Title'
              value={formData.title}
              onChange={e =>
                setFormData(prev => ({ ...prev, title: e.target.value }))
              }
              margin='normal'
              required
              sx={{ mb: 3 }}
            />
            <TextField
              fullWidth
              label='Course Description'
              value={formData.description}
              onChange={e =>
                setFormData(prev => ({ ...prev, description: e.target.value }))
              }
              margin='normal'
              multiline
              rows={4}
              required
              sx={{ mb: 3 }}
            />
            <FormControl fullWidth required>
              <InputLabel>Department</InputLabel>
              <Select
                value={formData.department}
                onChange={e =>
                  setFormData(prev => ({ ...prev, department: e.target.value }))
                }
                label='Department'
              >
                {departments.map(dept => (
                  <MenuItem key={dept} value={dept}>
                    {dept}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        );

      case 1:
        return (
          <Box>
            <Box display='flex' gap={2} mb={3}>
              <FormControl sx={{ minWidth: 120 }}>
                <InputLabel>Level</InputLabel>
                <Select
                  value={formData.level}
                  onChange={e =>
                    setFormData(prev => ({
                      ...prev,
                      level: Number(e.target.value),
                    }))
                  }
                  label='Level'
                >
                  <MenuItem value={1}>Beginner</MenuItem>
                  <MenuItem value={2}>Intermediate</MenuItem>
                  <MenuItem value={3}>Advanced</MenuItem>
                </Select>
              </FormControl>
              <FormControl sx={{ minWidth: 120 }}>
                <InputLabel>Credits</InputLabel>
                <Select
                  value={formData.credits}
                  onChange={e =>
                    setFormData(prev => ({
                      ...prev,
                      credits: Number(e.target.value),
                    }))
                  }
                  label='Credits'
                >
                  {[1, 2, 3, 4, 5, 6].map(credit => (
                    <MenuItem key={credit} value={credit}>
                      {credit}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <TextField
                label='Duration (weeks)'
                type='number'
                value={formData.duration}
                onChange={e =>
                  setFormData(prev => ({
                    ...prev,
                    duration: Number(e.target.value),
                  }))
                }
                sx={{ minWidth: 120 }}
              />
            </Box>

            <TextField
              fullWidth
              label='Target Audience'
              value={formData.targetAudience}
              onChange={e =>
                setFormData(prev => ({
                  ...prev,
                  targetAudience: e.target.value,
                }))
              }
              margin='normal'
              placeholder='e.g., Undergraduate students, Working professionals, etc.'
              sx={{ mb: 3 }}
            />

            <Box>
              <Typography variant='h6' gutterBottom>
                Keywords
              </Typography>
              <Box display='flex' gap={1} mb={2}>
                <TextField
                  label='Add keyword'
                  value={currentKeyword}
                  onChange={e => setCurrentKeyword(e.target.value)}
                  onKeyPress={e => e.key === 'Enter' && addKeyword()}
                  size='small'
                />
                <Button onClick={addKeyword} variant='outlined'>
                  Add
                </Button>
              </Box>
              <Box display='flex' gap={1} flexWrap='wrap'>
                {formData.keywords.map(keyword => (
                  <Chip
                    key={keyword}
                    label={keyword}
                    onDelete={() => removeKeyword(keyword)}
                    color='primary'
                    variant='outlined'
                  />
                ))}
              </Box>
            </Box>
          </Box>
        );

      case 2:
        return (
          <Box>
            <Typography variant='h6' gutterBottom>
              Learning Objectives
            </Typography>
            <Box display='flex' gap={1} mb={3}>
              <TextField
                fullWidth
                label='Add learning objective'
                value={currentObjective}
                onChange={e => setCurrentObjective(e.target.value)}
                onKeyPress={e => e.key === 'Enter' && addObjective()}
                placeholder='Students will be able to...'
              />
              <Button onClick={addObjective} variant='outlined'>
                Add
              </Button>
            </Box>
            <Box>
              {formData.learningObjectives.map((objective, index) => (
                <Chip
                  key={index}
                  label={`${index + 1}. ${objective}`}
                  onDelete={() => removeObjective(objective)}
                  color='secondary'
                  variant='outlined'
                  sx={{ mb: 1, mr: 1, maxWidth: '100%' }}
                />
              ))}
            </Box>
          </Box>
        );

      case 3:
        return (
          <Box>
            <Typography variant='h6' gutterBottom>
              Review & Generate
            </Typography>
            <Box mb={3}>
              <Typography variant='body1' gutterBottom>
                <strong>Title:</strong> {formData.title}
              </Typography>
              <Typography variant='body1' gutterBottom>
                <strong>Department:</strong> {formData.department}
              </Typography>
              <Typography variant='body1' gutterBottom>
                <strong>Level:</strong>{' '}
                {formData.level === 1
                  ? 'Beginner'
                  : formData.level === 2
                    ? 'Intermediate'
                    : 'Advanced'}
              </Typography>
              <Typography variant='body1' gutterBottom>
                <strong>Credits:</strong> {formData.credits}
              </Typography>
              <Typography variant='body1' gutterBottom>
                <strong>Keywords:</strong> {formData.keywords.join(', ')}
              </Typography>
              <Typography variant='body1' gutterBottom>
                <strong>Learning Objectives:</strong>{' '}
                {formData.learningObjectives.length}
              </Typography>
            </Box>

            {isGenerating && (
              <ProgressCard>
                <Typography variant='h6' gutterBottom>
                  Generating Course Content...
                </Typography>
                <LinearProgress
                  variant='determinate'
                  value={generationProgress}
                  sx={{ mb: 2, height: 8, borderRadius: 4 }}
                />
                <Typography variant='body2' color='text.secondary'>
                  {generationProgress < 30
                    ? 'Analyzing requirements...'
                    : generationProgress < 60
                      ? 'Generating course outline...'
                      : generationProgress < 90
                        ? 'Creating assessments...'
                        : 'Finalizing content...'}
                </Typography>
              </ProgressCard>
            )}

            {result && (
              <Fade in>
                <ResultCard>
                  <CardContent>
                    <Box display='flex' alignItems='center' gap={1} mb={2}>
                      {result.status === 'success' ? (
                        <CheckCircle color='success' />
                      ) : (
                        <ErrorIcon color='error' />
                      )}
                      <Typography variant='h6'>{result.message}</Typography>
                    </Box>

                    {result.status === 'success' && result.courseOutline && (
                      <Box>
                        <Accordion>
                          <AccordionSummary expandIcon={<ExpandMore />}>
                            <Typography variant='h6'>Course Outline</Typography>
                          </AccordionSummary>
                          <AccordionDetails>
                            <pre
                              style={{
                                whiteSpace: 'pre-wrap',
                                fontSize: '0.875rem',
                              }}
                            >
                              {JSON.stringify(result.courseOutline, null, 2)}
                            </pre>
                          </AccordionDetails>
                        </Accordion>

                        <Box mt={2} display='flex' gap={1}>
                          <Button
                            startIcon={<ContentCopy />}
                            onClick={() =>
                              navigator.clipboard.writeText(
                                JSON.stringify(result, null, 2)
                              )
                            }
                          >
                            Copy Result
                          </Button>
                          <Button
                            startIcon={<Download />}
                            onClick={() => {
                              const blob = new Blob(
                                [JSON.stringify(result, null, 2)],
                                { type: 'application/json' }
                              );
                              const url = URL.createObjectURL(blob);
                              const a = document.createElement('a');
                              a.href = url;
                              a.download = `${formData.title.replace(/\s+/g, '_')}_course.json`;
                              a.click();
                            }}
                          >
                            Download
                          </Button>
                        </Box>
                      </Box>
                    )}
                  </CardContent>
                </ResultCard>
              </Fade>
            )}

            {!isGenerating && !result && (
              <GenerateButton
                fullWidth
                onClick={generateCourse}
                startIcon={<AutoAwesome />}
                size='large'
              >
                Generate Course Content
              </GenerateButton>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <GeneratorContainer>
      <Box textAlign='center' mb={4}>
        <Typography variant='h3' fontWeight={700} gutterBottom>
          AI Course Generator
        </Typography>
        <Typography variant='h6' color='text.secondary'>
          Create comprehensive course content with AI assistance
        </Typography>
      </Box>

      <StyledCard>
        <CardContent sx={{ p: 4 }}>
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map(label => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          <Box minHeight='400px'>{renderStepContent(activeStep)}</Box>

          <Box display='flex' justifyContent='space-between' mt={4}>
            <Button
              onClick={handleBack}
              disabled={activeStep === 0}
              variant='outlined'
            >
              Back
            </Button>

            {activeStep < steps.length - 1 && (
              <Button
                onClick={handleNext}
                disabled={!canProceed()}
                variant='contained'
              >
                Next
              </Button>
            )}
          </Box>
        </CardContent>
      </StyledCard>
    </GeneratorContainer>
  );
};

export default ImprovedCourseGenerator;

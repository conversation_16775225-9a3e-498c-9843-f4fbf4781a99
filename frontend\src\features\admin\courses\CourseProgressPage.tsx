import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Typography,
  Box,
  Paper,
  Breadcrumbs,
  Link,
  Button,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardContent,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import {
  FiArrowLeft as ArrowBackIcon,
  FiTrendingUp as TrendingUpIcon,
  FiUsers as UsersIcon,
  FiBarChart as BarChartIcon,
  FiTarget as TargetIcon,
  FiClock as ClockIcon,
  FiCheckCircle as CheckCircleIcon,
} from 'react-icons/fi';

import { courseService } from '../../../services/courseService';
import { useToast } from '../../../hooks/useToast';

const CourseProgressPage: React.FC = () => {
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();
  const { showError } = useToast();
  const { t } = useTranslation();

  const [loading, setLoading] = useState<boolean>(true);
  const [course, setCourse] = useState<any>(null);
  const [students, setStudents] = useState<any[]>([]);
  const [progressData, setProgressData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch course and progress data
  useEffect(() => {
    const fetchData = async () => {
      if (!courseId) {
        setError('No course ID provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fetch course data
        const courseResponse = await courseService.getCourse(parseInt(courseId));
        if (courseResponse?.data) {
          setCourse(courseResponse.data);
        }

        // Fetch students
        const studentsResponse = await courseService.getCourseStudents(parseInt(courseId));
        if (studentsResponse?.data) {
          setStudents(Array.isArray(studentsResponse.data) ? studentsResponse.data : []);
        }

        // Fetch progress data
        const progressResponse = await courseService.getCourseProgress(parseInt(courseId));
        if (progressResponse?.data) {
          setProgressData(progressResponse.data);
        }

        setError(null);
      } catch (err: any) {
        console.error('Error fetching course progress:', err);
        setError(err.message || 'Failed to load course progress');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [courseId]);

  const handleBack = () => {
    navigate(`/admin/courses/${courseId}`);
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'success';
    if (progress >= 60) return 'warning';
    return 'error';
  };

  const calculateOverallProgress = () => {
    if (!students.length) return 0;
    const totalProgress = students.reduce((sum, student) => sum + (student.progress || 0), 0);
    return Math.round(totalProgress / students.length);
  };

  const getCompletedStudents = () => {
    return students.filter(student => (student.progress || 0) >= 100).length;
  };

  const getActiveStudents = () => {
    return students.filter(student => (student.progress || 0) > 0 && (student.progress || 0) < 100).length;
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" p={5}>
          <CircularProgress />
          <Typography sx={{ ml: 2 }}>
            {t('common.loading', 'Loading...')}
          </Typography>
        </Box>
      </Container>
    );
  }

  if (error || !course) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error || t('common.error.courseNotFound', 'Course not found')}
        </Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
        >
          {t('common.backToCourse', 'Back to Course')}
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 2 }}>
        <Link component={RouterLink} to="/admin/dashboard">
          {t('common.dashboard', 'Dashboard')}
        </Link>
        <Link component={RouterLink} to="/admin/courses">
          {t('common.courses', 'Courses')}
        </Link>
        <Link component={RouterLink} to={`/admin/courses/${courseId}`}>
          {course.title}
        </Link>
        <Typography color="text.primary">
          {t('courseProgress.title', 'Course Progress')}
        </Typography>
      </Breadcrumbs>

      {/* Back Button */}
      <Button
        variant="outlined"
        startIcon={<ArrowBackIcon />}
        onClick={handleBack}
        sx={{ mb: 3 }}
      >
        {t('navigation.backToCourse', 'Back to Course')}
      </Button>

      {/* Course Header */}
      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
          {t('courseProgress.title', 'Course Progress')}: {course.title}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {t('courseProgress.description', 'Track student progress and course completion metrics')}
        </Typography>
      </Paper>

      {/* Progress Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'primary.main', mx: 'auto', mb: 2 }}>
                <UsersIcon />
              </Avatar>
              <Typography variant="h4" fontWeight="bold" color="primary">
                {students.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('courseProgress.totalStudents', 'Total Students')}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'success.main', mx: 'auto', mb: 2 }}>
                <CheckCircleIcon />
              </Avatar>
              <Typography variant="h4" fontWeight="bold" color="success.main">
                {getCompletedStudents()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('courseProgress.completed', 'Completed')}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'warning.main', mx: 'auto', mb: 2 }}>
                <ClockIcon />
              </Avatar>
              <Typography variant="h4" fontWeight="bold" color="warning.main">
                {getActiveStudents()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('courseProgress.inProgress', 'In Progress')}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: 'info.main', mx: 'auto', mb: 2 }}>
                <TargetIcon />
              </Avatar>
              <Typography variant="h4" fontWeight="bold" color="info.main">
                {calculateOverallProgress()}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('courseProgress.averageProgress', 'Average Progress')}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Overall Progress */}
      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          {t('courseProgress.overallProgress', 'Overall Course Progress')}
        </Typography>
        <Box sx={{ mb: 2 }}>
          <LinearProgress
            variant="determinate"
            value={calculateOverallProgress()}
            sx={{ height: 10, borderRadius: 5 }}
            color={getProgressColor(calculateOverallProgress()) as any}
          />
        </Box>
        <Typography variant="body2" color="text.secondary">
          {calculateOverallProgress()}% of students have made progress in this course
        </Typography>
      </Paper>

      {/* Student Progress Table */}
      <Paper elevation={2} sx={{ mb: 3 }}>
        <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
          <Typography variant="h6">
            {t('courseProgress.studentProgress', 'Individual Student Progress')}
          </Typography>
        </Box>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t('courseProgress.student', 'Student')}</TableCell>
                <TableCell>{t('courseProgress.email', 'Email')}</TableCell>
                <TableCell>{t('courseProgress.progress', 'Progress')}</TableCell>
                <TableCell>{t('courseProgress.status', 'Status')}</TableCell>
                <TableCell>{t('courseProgress.lastActivity', 'Last Activity')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {students.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    <Box sx={{ py: 4 }}>
                      <UsersIcon size={48} color="gray" />
                      <Typography variant="h6" color="text.secondary" gutterBottom>
                        {t('courseProgress.noStudents', 'No students enrolled')}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {t('courseProgress.noStudentsDesc', 'Students will appear here once they enroll in this course')}
                      </Typography>
                    </Box>
                  </TableCell>
                </TableRow>
              ) : (
                students.map((student) => (
                  <TableRow key={student.id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ mr: 2, bgcolor: 'primary.light' }}>
                          {(student.first_name || 'S').charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {student.first_name} {student.last_name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            ID: {student.id}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {student.email || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ minWidth: 120 }}>
                        <LinearProgress
                          variant="determinate"
                          value={student.progress || 0}
                          sx={{ mb: 1 }}
                          color={getProgressColor(student.progress || 0) as any}
                        />
                        <Typography variant="body2">
                          {student.progress || 0}%
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={
                          (student.progress || 0) >= 100
                            ? t('courseProgress.completed', 'Completed')
                            : (student.progress || 0) > 0
                            ? t('courseProgress.inProgress', 'In Progress')
                            : t('courseProgress.notStarted', 'Not Started')
                        }
                        color={
                          (student.progress || 0) >= 100
                            ? 'success'
                            : (student.progress || 0) > 0
                            ? 'warning'
                            : 'default'
                        }
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {student.last_activity 
                          ? new Date(student.last_activity).toLocaleDateString()
                          : t('courseProgress.noActivity', 'No activity')
                        }
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Actions */}
      <Paper elevation={2} sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          {t('courseProgress.actions', 'Progress Actions')}
        </Typography>
        <Grid container spacing={2}>
          <Grid item>
            <Button
              variant="contained"
              startIcon={<BarChartIcon />}
              onClick={() => navigate(`/admin/courses/${courseId}/analytics`)}
            >
              {t('courseProgress.viewAnalytics', 'View Analytics')}
            </Button>
          </Grid>
          <Grid item>
            <Button
              variant="outlined"
              startIcon={<TrendingUpIcon />}
              onClick={() => navigate(`/admin/courses/${courseId}/reports`)}
            >
              {t('courseProgress.generateReport', 'Generate Report')}
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default CourseProgressPage;

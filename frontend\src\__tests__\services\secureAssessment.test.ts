import assessmentService from '../../services/assessmentService';
import axiosInstance from '../../config/axios';
import { API_ENDPOINTS } from '../../config/api';

// Mock axios and navigator
jest.mock('../../config/axios');
const mockedAxios = axiosInstance as jest.Mocked<typeof axiosInstance>;

// Mock navigator and window objects
Object.defineProperty(global, 'navigator', {
  value: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    platform: 'Win32',
  },
  writable: true,
});

// Only define window if it doesn't exist or extend existing window
if (typeof global.window === 'undefined') {
  Object.defineProperty(global, 'window', {
    value: {
      screen: {
        width: 1920,
        height: 1080,
      },
    },
    writable: true,
  });
} else {
  // Extend existing window object
  global.window.screen = {
    width: 1920,
    height: 1080,
  };
}

describe('Secure Assessment Features', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('initializeSecureSession', () => {
    it('should successfully initialize a secure session', async () => {
      const mockResponse = {
        data: {
          session_id: 'secure-session-123',
          assessment_id: 'test-assessment',
          security_level: 'high',
          restrictions: ['no_copy_paste', 'fullscreen_required', 'tab_switching_blocked'],
          expires_at: '2024-01-01T12:00:00Z',
        },
      };

      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      const deviceInfo = {
        device_type: 'desktop',
        os: 'Windows',
        trusted: true,
      };

      const result = await assessmentService.initializeSecureSession('test-assessment', deviceInfo);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${API_ENDPOINTS.ASSESSMENT.BASE}/secure/initialize/test-assessment`,
        {
          device_info: deviceInfo,
          screen_resolution: '1920x1080',
          timezone_offset: expect.any(Number),
          platform: 'Win32',
          browser_name: 'Chrome',
          browser_version: expect.any(String),
        }
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle initialization errors', async () => {
      const mockError = {
        response: {
          data: { message: 'Device not trusted' },
        },
      };

      mockedAxios.post.mockRejectedValueOnce(mockError);

      const result = await assessmentService.initializeSecureSession('test-assessment', {});

      expect(result).toEqual({
        status: 'error',
        message: 'Device not trusted',
        error: mockError.response.data,
      });
    });
  });

  describe('startSecureSession', () => {
    it('should successfully start a secure session', async () => {
      const mockResponse = {
        data: {
          status: 'active',
          started_at: '2024-01-01T10:00:00Z',
          security_checks_passed: true,
          monitoring_active: true,
        },
      };

      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      const result = await assessmentService.startSecureSession('secure-session-123');

      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${API_ENDPOINTS.ASSESSMENT.BASE}/secure/start/secure-session-123`
      );
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('logSecurityEvent', () => {
    it('should successfully log a security event', async () => {
      const mockResponse = {
        data: {
          event_logged: true,
          timestamp: '2024-01-01T10:30:00Z',
          severity: 'medium',
        },
      };

      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      const eventData = {
        tab_switch_detected: true,
        duration: 5000,
        url_visited: 'https://google.com',
      };

      const result = await assessmentService.logSecurityEvent(
        'secure-session-123',
        'tab_switch',
        eventData
      );

      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${API_ENDPOINTS.ASSESSMENT.BASE}/secure/log-event/secure-session-123`,
        {
          event_type: 'tab_switch',
          event_data: eventData,
        }
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle different event types', async () => {
      const mockResponse = { data: { event_logged: true } };
      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      await assessmentService.logSecurityEvent('session-123', 'copy_attempt', {
        selected_text: 'some question text',
        timestamp: Date.now(),
      });

      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${API_ENDPOINTS.ASSESSMENT.BASE}/secure/log-event/session-123`,
        {
          event_type: 'copy_attempt',
          event_data: {
            selected_text: 'some question text',
            timestamp: expect.any(Number),
          },
        }
      );
    });
  });

  describe('validateSecureSession', () => {
    it('should successfully validate a secure session', async () => {
      const mockResponse = {
        data: {
          valid: true,
          status: 'active',
          violations: [],
          security_score: 95,
          remaining_time: 3600,
        },
      };

      mockedAxios.get.mockResolvedValueOnce(mockResponse);

      const result = await assessmentService.validateSecureSession('secure-session-123');

      expect(mockedAxios.get).toHaveBeenCalledWith(
        `${API_ENDPOINTS.ASSESSMENT.BASE}/secure/validate/secure-session-123`
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle invalid session', async () => {
      const mockResponse = {
        data: {
          valid: false,
          status: 'terminated',
          violations: ['excessive_tab_switching', 'copy_paste_attempt'],
          reason: 'Security violations detected',
        },
      };

      mockedAxios.get.mockResolvedValueOnce(mockResponse);

      const result = await assessmentService.validateSecureSession('invalid-session');

      expect(result.valid).toBe(false);
      expect(result.violations).toContain('excessive_tab_switching');
    });
  });

  describe('endSecureSession', () => {
    it('should successfully end a secure session', async () => {
      const mockResponse = {
        data: {
          session_ended: true,
          ended_at: '2024-01-01T11:00:00Z',
          duration: 3600,
          security_summary: {
            violations: 1,
            score: 85,
            events_logged: 15,
          },
        },
      };

      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      const result = await assessmentService.endSecureSession('secure-session-123', 'completed');

      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${API_ENDPOINTS.ASSESSMENT.BASE}/secure/end/secure-session-123`,
        { reason: 'completed' }
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('should use default reason when none provided', async () => {
      const mockResponse = { data: { session_ended: true } };
      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      await assessmentService.endSecureSession('secure-session-123');

      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${API_ENDPOINTS.ASSESSMENT.BASE}/secure/end/secure-session-123`,
        { reason: 'completed' }
      );
    });
  });
});

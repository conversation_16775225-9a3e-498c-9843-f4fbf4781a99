import React, { useState, useEffect, useCallback } from 'react';
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from 'react-beautiful-dnd';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Chip,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  DragIndicator,
  KeyboardArrowUp,
  KeyboardArrowDown,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { DragItem } from '../../types';

interface DragDropOrderingProps {
  items: DragItem[];
  onOrderChange: (orderedItems: DragItem[]) => void;
  isSubmitting: boolean;
  value?: string;
}

const DragDropOrdering: React.FC<DragDropOrderingProps> = ({
  items,
  onOrderChange,
  isSubmitting,
  value,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [orderedItems, setOrderedItems] = useState<DragItem[]>([]);

  useEffect(() => {
    if (value) {
      try {
        const parsed = JSON.parse(value);
        setOrderedItems(parsed);
      } catch {
        setOrderedItems([...items]);
      }
    } else {
      // Shuffle items initially
      const shuffled = [...items].sort(() => Math.random() - 0.5);
      setOrderedItems(shuffled);
    }
  }, [items, value]);

  const handleDragEnd = useCallback(
    (result: DropResult) => {
      if (!result.destination) return;

      const newItems = Array.from(orderedItems);
      const [reorderedItem] = newItems.splice(result.source.index, 1);
      newItems.splice(result.destination.index, 0, reorderedItem);

      setOrderedItems(newItems);
      onOrderChange(newItems);
    },
    [orderedItems, onOrderChange]
  );

  const moveItem = useCallback(
    (index: number, direction: 'up' | 'down') => {
      const newItems = [...orderedItems];
      const targetIndex = direction === 'up' ? index - 1 : index + 1;
      
      if (targetIndex >= 0 && targetIndex < newItems.length) {
        [newItems[index], newItems[targetIndex]] = [newItems[targetIndex], newItems[index]];
        setOrderedItems(newItems);
        onOrderChange(newItems);
      }
    },
    [orderedItems, onOrderChange]
  );

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent, index: number) => {
      if (event.key === 'ArrowUp' && event.ctrlKey) {
        event.preventDefault();
        moveItem(index, 'up');
      } else if (event.key === 'ArrowDown' && event.ctrlKey) {
        event.preventDefault();
        moveItem(index, 'down');
      }
    },
    [moveItem]
  );

  return (
    <Box sx={{ mt: 2 }}>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        {t('assessment.dragDropOrdering.instruction')}
      </Typography>
      
      {isMobile && (
        <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
          {t('assessment.dragDropOrdering.mobileInstruction')}
        </Typography>
      )}

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="ordering-list">
          {(provided, snapshot) => (
            <Box
              {...provided.droppableProps}
              ref={provided.innerRef}
              sx={{
                minHeight: 200,
                backgroundColor: snapshot.isDraggingOver
                  ? theme.palette.action.hover
                  : 'transparent',
                borderRadius: 1,
                transition: 'background-color 0.2s ease',
              }}
            >
              {orderedItems.map((item, index) => (
                <Draggable
                  key={item.id}
                  draggableId={item.id}
                  index={index}
                  isDragDisabled={isSubmitting}
                >
                  {(provided, snapshot) => (
                    <Paper
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      elevation={snapshot.isDragging ? 4 : 1}
                      sx={{
                        mb: 1,
                        p: 2,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 2,
                        cursor: isSubmitting ? 'default' : 'move',
                        backgroundColor: snapshot.isDragging
                          ? theme.palette.primary.light
                          : 'background.paper',
                        transform: snapshot.isDragging ? 'rotate(3deg)' : 'none',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          backgroundColor: !isSubmitting && !snapshot.isDragging
                            ? theme.palette.action.hover
                            : undefined,
                        },
                      }}
                      tabIndex={0}
                      onKeyDown={(e) => handleKeyDown(e, index)}
                      role="button"
                      aria-label={t('assessment.dragDropOrdering.itemLabel', {
                        position: index + 1,
                        content: item.content,
                      })}
                    >
                      <Box
                        {...provided.dragHandleProps}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          color: theme.palette.text.secondary,
                        }}
                      >
                        <DragIndicator />
                      </Box>
                      
                      <Chip
                        label={index + 1}
                        size="small"
                        sx={{
                          backgroundColor: theme.palette.primary.main,
                          color: theme.palette.primary.contrastText,
                          fontWeight: 'bold',
                        }}
                      />
                      
                      <Typography variant="body1" sx={{ flex: 1 }}>
                        {item.content}
                      </Typography>
                      
                      {isMobile && (
                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                          <IconButton
                            size="small"
                            onClick={() => moveItem(index, 'up')}
                            disabled={index === 0 || isSubmitting}
                            aria-label={t('assessment.dragDropOrdering.moveUp')}
                          >
                            <KeyboardArrowUp />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => moveItem(index, 'down')}
                            disabled={index === orderedItems.length - 1 || isSubmitting}
                            aria-label={t('assessment.dragDropOrdering.moveDown')}
                          >
                            <KeyboardArrowDown />
                          </IconButton>
                        </Box>
                      )}
                    </Paper>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </Box>
          )}
        </Droppable>
      </DragDropContext>
      
      <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
        {t('assessment.dragDropOrdering.keyboardShortcut')}
      </Typography>
    </Box>
  );
};

export default DragDropOrdering;

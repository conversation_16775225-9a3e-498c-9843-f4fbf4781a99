"""
Enhanced Course model with improved validation, indexes, and constraints.
This file contains the enhanced version of the Course model with:
- Proper field validators
- Database indexes for performance
- Database constraints for data integrity
- Improved relationships
"""

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator, RegexValidator
from django.db import models
from django.db.models import Case, Count, Q, When
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
import re

# Import base models from core app
from core.models import (
    BaseContent,
    BaseModel,
    Course as CourseBase,
    CourseContentBase,
    CourseRelationshipBase,
    ProgressTrackable,
    Skill
)

# Import the Department model
from .department import Department


def validate_course_code(value):
    """Validate course code format (e.g., CS101, MATH200)"""
    pattern = r'^[A-Z]{2,4}\d{3,4}[A-Z]?$'
    if not re.match(pattern, value):
        raise ValidationError(
            _('Course code must be in format like CS101, MATH200, or PHYS101L'),
            code='invalid_course_code'
        )


def validate_credits(value):
    """Validate credit hours are within reasonable range"""
    if value < 0 or value > 12:
        raise ValidationError(
            _('Credits must be between 0 and 12'),
            code='invalid_credits'
        )


def validate_capacity(value):
    """Validate course capacity is reasonable"""
    if value < 1 or value > 500:
        raise ValidationError(
            _('Course capacity must be between 1 and 500'),
            code='invalid_capacity'
        )


def validate_duration_minutes(value):
    """Validate course duration in minutes"""
    if value < 15 or value > 480:  # 15 minutes to 8 hours
        raise ValidationError(
            _('Duration must be between 15 minutes and 8 hours'),
            code='invalid_duration'
        )


def validate_academic_year(value):
    """Validate academic year format (YYYY-YYYY)"""
    pattern = r'^\d{4}-\d{4}$'
    if not re.match(pattern, value):
        raise ValidationError(
            _('Academic year must be in format YYYY-YYYY (e.g., 2023-2024)'),
            code='invalid_academic_year'
        )
    
    # Check that the years are consecutive
    start_year, end_year = map(int, value.split('-'))
    if end_year != start_year + 1:
        raise ValidationError(
            _('Academic year must have consecutive years'),
            code='invalid_academic_year_sequence'
        )


def get_current_academic_year():
    """Return current academic year in format '2023-2024'"""
    today = timezone.now()
    if today.month < 9:  # If before September
        return f"{today.year - 1}-{today.year}"
    return f"{today.year}-{today.year + 1}"


class CourseEnhanced(CourseBase):
    """
    Enhanced Course model with improved validation and constraints.
    
    This model inherits from CourseBase and adds university-specific fields,
    proper validation, database indexes, and constraints for data integrity.
    """
    
    # Enhanced field validators
    course_code = models.CharField(
        max_length=20,
        validators=[validate_course_code],
        help_text=_("Course code in format like CS101, MATH200")
    )
    
    credits = models.IntegerField(
        default=3,
        validators=[validate_credits, MinValueValidator(0), MaxValueValidator(12)],
        help_text=_("Credit hours (0-12)")
    )
    
    max_students = models.IntegerField(
        default=30,
        validators=[validate_capacity, MinValueValidator(1), MaxValueValidator(500)],
        help_text=_("Maximum number of students (1-500)")
    )
    
    academic_year = models.CharField(
        max_length=9,
        validators=[validate_academic_year],
        help_text=_("Academic year in format YYYY-YYYY"),
        default=get_current_academic_year
    )
    
    # Enhanced relationships with proper related names
    department = models.ForeignKey(
        Department,
        on_delete=models.CASCADE,
        related_name="department_courses",
        null=True,
        blank=True,
        help_text=_("Department offering this course")
    )
    
    instructor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name="taught_courses",
        null=True,
        blank=True,
        limit_choices_to={'role': 'PROFESSOR'},
        help_text=_("Primary instructor for this course")
    )
    
    # Many-to-many relationships with proper validation
    prerequisites = models.ManyToManyField(
        "self",
        symmetrical=False,
        blank=True,
        related_name="unlocks_courses",
        help_text=_("Courses that must be completed before taking this course")
    )
    
    next_level_courses = models.ManyToManyField(
        "self",
        symmetrical=False,
        blank=True,
        related_name="previous_level_courses",
        help_text=_("Recommended courses to take after completing this course")
    )
    
    # Skills relationships
    skills_required = models.ManyToManyField(
        "core.Skill",
        related_name="required_by_enhanced_courses",
        blank=True,
        help_text=_("Skills needed to take this course")
    )
    
    skills_developed = models.ManyToManyField(
        "core.Skill",
        related_name="developed_in_enhanced_courses",
        blank=True,
        help_text=_("Skills that will be developed in this course")
    )
    
    # Assessment flag
    has_assessment = models.BooleanField(
        default=False,
        help_text=_("Whether this course has an assessment")
    )
    
    # Course type for unified management
    COURSE_TYPE_CHOICES = [
        ('STANDARD', _('Standard Course')),
        ('INTERACTIVE', _('Interactive Course')),
        ('AI_GENERATED', _('AI-Generated Course')),
        ('HYBRID', _('Hybrid Course'))
    ]
    
    primary_type = models.CharField(
        max_length=20,
        choices=COURSE_TYPE_CHOICES,
        default='STANDARD',
        help_text=_("Primary type of this course for unified management")
    )
    
    # Duration tracking for sessions
    typical_session_duration = models.PositiveIntegerField(
        null=True,
        blank=True,
        validators=[validate_duration_minutes],
        help_text=_("Typical session duration in minutes")
    )
    
    # Enhanced metadata
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="courses_created",
        help_text=_("User who created this course")
    )
    
    last_modified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="courses_modified",
        help_text=_("User who last modified this course")
    )
    
    class Meta:
        ordering = ["-created_at", "title"]
        app_label = "courses"
        verbose_name = _("Course")
        verbose_name_plural = _("Courses")
        
        # Performance indexes
        indexes = [
            models.Index(fields=['course_code'], name='idx_course_code'),
            models.Index(fields=['instructor', 'semester'], name='idx_instructor_semester'),
            models.Index(fields=['department', 'is_active'], name='idx_dept_active'),
            models.Index(fields=['academic_year', 'semester'], name='idx_academic_semester'),
            models.Index(fields=['required_level'], name='idx_required_level'),
            models.Index(fields=['primary_type'], name='idx_primary_type'),
            models.Index(fields=['is_active', 'semester', 'academic_year'], name='idx_active_schedule'),
            models.Index(fields=['created_at'], name='idx_created_at'),
            models.Index(fields=['updated_at'], name='idx_updated_at'),
        ]
        
        # Data integrity constraints
        constraints = [
            models.UniqueConstraint(
                fields=['course_code', 'semester', 'academic_year'],
                name='unique_course_schedule'
            ),
            models.CheckConstraint(
                check=Q(credits__gte=0) & Q(credits__lte=12),
                name='valid_credits_range'
            ),
            models.CheckConstraint(
                check=Q(max_students__gte=1) & Q(max_students__lte=500),
                name='valid_capacity_range'
            ),
            models.CheckConstraint(
                check=Q(required_level__gte=1) & Q(required_level__lte=5),
                name='valid_required_level'
            ),
            models.CheckConstraint(
                check=Q(recommended_level__gte=1) & Q(recommended_level__lte=5),
                name='valid_recommended_level'
            ),
            models.CheckConstraint(
                check=Q(typical_session_duration__gte=15) & Q(typical_session_duration__lte=480),
                name='valid_session_duration'
            ),
        ]
    
    def __str__(self):
        return f"{self.course_code} - {self.title}"
    
    def clean(self):
        """Additional model validation"""
        super().clean()
        
        # Ensure recommended level is not lower than required level
        if self.recommended_level and self.required_level:
            if self.recommended_level < self.required_level:
                raise ValidationError({
                    'recommended_level': _('Recommended level cannot be lower than required level')
                })
        
        # Validate instructor role
        if self.instructor and hasattr(self.instructor, 'role'):
            if self.instructor.role != 'PROFESSOR':
                raise ValidationError({
                    'instructor': _('Instructor must have PROFESSOR role')
                })
        
        # Validate dates
        if self.start_date and self.end_date:
            if self.start_date >= self.end_date:
                raise ValidationError({
                    'end_date': _('End date must be after start date')
                })
    
    def save(self, *args, **kwargs):
        """Override save to add automatic field updates"""
        self.full_clean()  # Validate before saving
        
        # Update modification tracking
        if hasattr(self, '_current_user'):
            if not self.pk:  # New record
                self.created_by = self._current_user
            self.last_modified_by = self._current_user
        
        super().save(*args, **kwargs)
    
    # Properties for backward compatibility
    @property
    def has_interactive_version(self):
        """Check if this course has an interactive version"""
        return self.has_interactive_content
    
    def get_enrollment_count(self):
        """Get current enrollment count"""
        return self.enrollments.filter(status='APPROVED').count()
    
    def get_available_spots(self):
        """Get number of available spots"""
        return max(0, self.max_students - self.get_enrollment_count())
    
    def is_full(self):
        """Check if course is at capacity"""
        return self.get_enrollment_count() >= self.max_students
    
    def can_enroll_student(self, student):
        """Check if a student can enroll in this course"""
        if not self.is_active:
            return False, "Course is not active"
        
        if self.is_full():
            return False, "Course is full"
        
        # Check prerequisites
        if self.prerequisites.exists():
            student_completed = set(
                student.enrollments.filter(
                    status='COMPLETED',
                    course__in=self.prerequisites.all()
                ).values_list('course_id', flat=True)
            )
            required = set(self.prerequisites.values_list('id', flat=True))
            
            if not required.issubset(student_completed):
                return False, "Prerequisites not met"
        
        # Check if already enrolled
        if self.enrollments.filter(user=student, status__in=['PENDING', 'APPROVED']).exists():
            return False, "Already enrolled"
        
        return True, "Can enroll"
    
    def get_performance_metrics(self):
        """Get course performance metrics"""
        enrollments = self.enrollments.filter(status='APPROVED')
        total_enrolled = enrollments.count()
        
        if total_enrolled == 0:
            return {
                'total_enrolled': 0,
                'completion_rate': 0,
                'average_grade': None,
                'dropout_rate': 0
            }
        
        completed = enrollments.filter(status='COMPLETED').count()
        dropped = enrollments.filter(status='DROPPED').count()
        
        # Calculate grades if available
        from grades.models import CourseGrade
        grades = CourseGrade.objects.filter(course=self)
        avg_grade = grades.aggregate(avg=models.Avg('numeric_grade'))['avg']
        
        return {
            'total_enrolled': total_enrolled,
            'completion_rate': (completed / total_enrolled) * 100 if total_enrolled > 0 else 0,
            'average_grade': round(avg_grade, 2) if avg_grade else None,
            'dropout_rate': (dropped / total_enrolled) * 100 if total_enrolled > 0 else 0
        }


class CoursePrerequisite(CourseRelationshipBase):
    """
    Explicit model for course prerequisites with additional metadata.
    This allows for more complex prerequisite relationships.
    """
    prerequisite_course = models.ForeignKey(
        CourseEnhanced,
        on_delete=models.CASCADE,
        related_name="unlocks_course_relations"
    )
    
    target_course = models.ForeignKey(
        CourseEnhanced,
        on_delete=models.CASCADE,
        related_name="prerequisite_relations"
    )
    
    # Minimum grade required in prerequisite
    minimum_grade = models.CharField(
        max_length=2,
        choices=[
            ('A', 'A'), ('B', 'B'), ('C', 'C'), ('D', 'D'), ('F', 'F')
        ],
        default='C',
        help_text=_("Minimum grade required in prerequisite course")
    )
    
    # Whether this prerequisite is mandatory or recommended
    is_mandatory = models.BooleanField(
        default=True,
        help_text=_("Whether this prerequisite is mandatory or just recommended")
    )
    
    class Meta:
        unique_together = ['prerequisite_course', 'target_course']
        verbose_name = _("Course Prerequisite")
        verbose_name_plural = _("Course Prerequisites")
    
    def __str__(self):
        return f"{self.prerequisite_course.course_code} → {self.target_course.course_code}"


class CourseSection(BaseModel):
    """
    Model for course sections to handle multiple sections of the same course.
    """
    course = models.ForeignKey(
        CourseEnhanced,
        on_delete=models.CASCADE,
        related_name="sections"
    )
    
    section_number = models.CharField(
        max_length=10,
        help_text=_("Section identifier (e.g., A, B, 001, 002)")
    )
    
    instructor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="section_instructor",
        limit_choices_to={'role': 'PROFESSOR'}
    )
    
    meeting_times = models.JSONField(
        default=dict,
        help_text=_("Meeting times and locations in JSON format")
    )
    
    max_enrollment = models.PositiveIntegerField(
        default=30,
        validators=[MinValueValidator(1), MaxValueValidator(200)]
    )
    
    is_active = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ['course', 'section_number']
        verbose_name = _("Course Section")
        verbose_name_plural = _("Course Sections")
        indexes = [
            models.Index(fields=['course', 'section_number']),
            models.Index(fields=['instructor']),
        ]
    
    def __str__(self):
        return f"{self.course.course_code} - Section {self.section_number}"
    
    def get_enrollment_count(self):
        """Get current enrollment count for this section"""
        return self.section_enrollments.filter(status='APPROVED').count()
    
    def is_full(self):
        """Check if section is at capacity"""
        return self.get_enrollment_count() >= self.max_enrollment

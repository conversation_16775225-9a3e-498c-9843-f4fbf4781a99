import React, { useState, useEffect, useCallback } from 'react';
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from 'react-beautiful-dnd';
import {
  Box,
  Paper,
  Typography,
  Grid,
  useTheme,
  useMediaQuery,
  Chip,
  Button,
} from '@mui/material';
import {
  DragIndicator,
  Refresh,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { MatchingPair } from '../../types';

interface DragDropMatchingProps {
  pairs: MatchingPair[];
  onMatchChange: (matches: Record<string, string>) => void;
  isSubmitting: boolean;
  value?: string;
}

interface MatchState {
  leftItems: MatchingPair[];
  rightItems: MatchingPair[];
  matches: Record<string, string>;
}

const DragDropMatching: React.FC<DragDropMatchingProps> = ({
  pairs,
  onMatchChange,
  isSubmitting,
  value,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [matchState, setMatchState] = useState<MatchState>({
    leftItems: [],
    rightItems: [],
    matches: {},
  });

  useEffect(() => {
    let initialMatches: Record<string, string> = {};
    
    if (value) {
      try {
        initialMatches = JSON.parse(value);
      } catch {
        // Invalid JSON, use empty matches
      }
    }

    const leftItems = pairs.map(pair => ({ ...pair, content: pair.left }));
    const rightItems = [...pairs.map(pair => ({ ...pair, content: pair.right }))]
      .sort(() => Math.random() - 0.5); // Shuffle right items

    setMatchState({
      leftItems,
      rightItems,
      matches: initialMatches,
    });
  }, [pairs, value]);

  const handleDragEnd = useCallback(
    (result: DropResult) => {
      const { source, destination, draggableId } = result;
      
      if (!destination) return;

      // Handle dropping from unmatched to matched area
      if (source.droppableId === 'unmatched' && destination.droppableId.startsWith('match-')) {
        const leftId = destination.droppableId.replace('match-', '');
        const newMatches = { ...matchState.matches };
        
        // Remove any existing match for this right item
        Object.keys(newMatches).forEach(key => {
          if (newMatches[key] === draggableId) {
            delete newMatches[key];
          }
        });
        
        // Add new match
        newMatches[leftId] = draggableId;
        
        const updatedState = { ...matchState, matches: newMatches };
        setMatchState(updatedState);
        onMatchChange(newMatches);
      }
      
      // Handle removing match (dropping back to unmatched)
      else if (source.droppableId.startsWith('match-') && destination.droppableId === 'unmatched') {
        const leftId = source.droppableId.replace('match-', '');
        const newMatches = { ...matchState.matches };
        delete newMatches[leftId];
        
        const updatedState = { ...matchState, matches: newMatches };
        setMatchState(updatedState);
        onMatchChange(newMatches);
      }
      
      // Handle moving between matches
      else if (source.droppableId.startsWith('match-') && destination.droppableId.startsWith('match-')) {
        const sourceLeftId = source.droppableId.replace('match-', '');
        const destLeftId = destination.droppableId.replace('match-', '');
        
        if (sourceLeftId !== destLeftId) {
          const newMatches = { ...matchState.matches };
          const draggedItem = newMatches[sourceLeftId];
          
          // Remove from source
          delete newMatches[sourceLeftId];
          
          // Remove any existing match at destination
          Object.keys(newMatches).forEach(key => {
            if (newMatches[key] === newMatches[destLeftId]) {
              delete newMatches[key];
            }
          });
          
          // Add to destination
          newMatches[destLeftId] = draggedItem;
          
          const updatedState = { ...matchState, matches: newMatches };
          setMatchState(updatedState);
          onMatchChange(newMatches);
        }
      }
    },
    [matchState, onMatchChange]
  );

  const resetMatches = useCallback(() => {
    const updatedState = { ...matchState, matches: {} };
    setMatchState(updatedState);
    onMatchChange({});
  }, [matchState, onMatchChange]);

  const getUnmatchedRightItems = useCallback(() => {
    const matchedIds = Object.values(matchState.matches);
    return matchState.rightItems.filter(item => !matchedIds.includes(item.rightId));
  }, [matchState]);

  return (
    <Box sx={{ mt: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="body2" color="text.secondary">
          {t('assessment.dragDropMatching.instruction')}
        </Typography>
        <Button
          startIcon={<Refresh />}
          onClick={resetMatches}
          disabled={isSubmitting}
          size="small"
        >
          {t('assessment.dragDropMatching.reset')}
        </Button>
      </Box>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Grid container spacing={2}>
          {/* Left Column - Items to match */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle2" gutterBottom>
              {t('assessment.dragDropMatching.leftColumn')}
            </Typography>
            {matchState.leftItems.map((item) => (
              <Paper
                key={item.leftId}
                elevation={1}
                sx={{
                  mb: 1,
                  p: 2,
                  minHeight: 60,
                  display: 'flex',
                  alignItems: 'center',
                  backgroundColor: theme.palette.grey[50],
                }}
              >
                <Typography variant="body1" sx={{ flex: 1 }}>
                  {item.left}
                </Typography>
                
                <Droppable droppableId={`match-${item.leftId}`}>
                  {(provided, snapshot) => (
                    <Box
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      sx={{
                        width: isMobile ? 100 : 150,
                        minHeight: 40,
                        border: `2px dashed ${
                          snapshot.isDraggingOver
                            ? theme.palette.primary.main
                            : theme.palette.divider
                        }`,
                        borderRadius: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: snapshot.isDraggingOver
                          ? theme.palette.primary.light
                          : 'transparent',
                        transition: 'all 0.2s ease',
                      }}
                    >
                      {matchState.matches[item.leftId] && (
                        <Draggable
                          draggableId={matchState.matches[item.leftId]}
                          index={0}
                          isDragDisabled={isSubmitting}
                        >
                          {(provided, snapshot) => {
                            const matchedItem = matchState.rightItems.find(
                              ri => ri.rightId === matchState.matches[item.leftId]
                            );
                            return (
                              <Paper
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                elevation={snapshot.isDragging ? 4 : 2}
                                sx={{
                                  p: 1,
                                  backgroundColor: theme.palette.success.light,
                                  cursor: isSubmitting ? 'default' : 'move',
                                  transform: snapshot.isDragging ? 'rotate(3deg)' : 'none',
                                  transition: 'all 0.2s ease',
                                }}
                              >
                                <Typography variant="body2" align="center">
                                  {matchedItem?.right}
                                </Typography>
                              </Paper>
                            );
                          }}
                        </Draggable>
                      )}
                      {provided.placeholder}
                    </Box>
                  )}
                </Droppable>
              </Paper>
            ))}
          </Grid>

          {/* Right Column - Available items */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle2" gutterBottom>
              {t('assessment.dragDropMatching.rightColumn')}
            </Typography>
            
            <Droppable droppableId="unmatched">
              {(provided, snapshot) => (
                <Paper
                  ref={provided.innerRef}
                  {...provided.droppableProps}
                  elevation={1}
                  sx={{
                    p: 2,
                    minHeight: 300,
                    backgroundColor: snapshot.isDraggingOver
                      ? theme.palette.action.hover
                      : theme.palette.grey[50],
                    border: `2px dashed ${theme.palette.divider}`,
                    borderRadius: 1,
                  }}
                >
                  <Typography variant="caption" color="text.secondary" gutterBottom>
                    {t('assessment.dragDropMatching.unmatchedArea')}
                  </Typography>
                  
                  {getUnmatchedRightItems().map((item, index) => (
                    <Draggable
                      key={item.rightId}
                      draggableId={item.rightId}
                      index={index}
                      isDragDisabled={isSubmitting}
                    >
                      {(provided, snapshot) => (
                        <Paper
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          elevation={snapshot.isDragging ? 4 : 2}
                          sx={{
                            mb: 1,
                            p: 1.5,
                            cursor: isSubmitting ? 'default' : 'move',
                            backgroundColor: snapshot.isDragging
                              ? theme.palette.primary.light
                              : 'background.paper',
                            transform: snapshot.isDragging ? 'rotate(3deg)' : 'none',
                            transition: 'all 0.2s ease',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1,
                          }}
                        >
                          <Box
                            {...provided.dragHandleProps}
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              color: theme.palette.text.secondary,
                            }}
                          >
                            <DragIndicator fontSize="small" />
                          </Box>
                          <Typography variant="body2">
                            {item.right}
                          </Typography>
                        </Paper>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </Paper>
              )}
            </Droppable>
          </Grid>
        </Grid>
      </DragDropContext>
      
      <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
        <Typography variant="caption" color="text.secondary">
          {t('assessment.dragDropMatching.progress', {
            matched: Object.keys(matchState.matches).length,
            total: matchState.leftItems.length,
          })}
        </Typography>
        
        {Object.keys(matchState.matches).length === matchState.leftItems.length && (
          <Chip
            label={t('assessment.dragDropMatching.complete')}
            color="success"
            size="small"
          />
        )}
      </Box>
    </Box>
  );
};

export default DragDropMatching;

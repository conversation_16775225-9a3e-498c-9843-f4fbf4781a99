"""AIAgentcompatibilitymodule.ThismoduleprovidesacompatibilitylayerfortheAIagentfunctionalitythatwaspreviouslyinai_agent.py.ItusestheunifiedAIservicetoprovidethesamefunctionality."""import loggingfromtypingimportAnyDictListOptional#ImportstandardizedAIserviceutilitiesimport logginglogger=logging.getLogger(__name__)from utils.ai.servicesimportget_ai_serviceAIServiceError#GetAIserviceinstanceai_service=get_ai_service()#Usestandardizedloggerlogger=logging.getLogger(__name__)classUniversityAIAgent:"""AIagentfortheuniversitymanagementsystem.ThisclassprovidesachatinterfaceforinteractingwiththeAIagent.ItusestheunifiedAIservicetogenerateresponses."""def__init__(self):"""InitializetheAIagent."""self.ai_service=ai_serviceself.sessions={}logger.info("InitializedUniversityAIAgent")defchat(selfmessage:strsession_id:str="default")->Dict[strAny]:"""ChatwiththeAIagent.Args:message:Themessagetosendtotheagentsession_id:ThesessionIDforconversationcontinuityReturns:Adictionarywiththeagent'sresponse"""try:#Initializesessionifitdoesn'texistifsession_idnotinself.sessions:self.sessions[session_id]={"history":[]"context":{}}#Addmessagetohistoryself.sessions[session_id]["history"].append({"role":"user""content":message})#Generatepromptwithhistoryprompt=self._generate_prompt(messageself.sessions[session_id]["history"])#Generateresponseresponse_text=self.ai_service.generate_content(prompt)#Addresponsetohistoryself.sessions[session_id]["history"].append({"role":"assistant""content":response_text})#Limithistorytolast10messagesiflen(self.sessions[session_id]["history"])>10:self.sessions[session_id]["history"]=self.sessions[session_id]["history"][-10:]return{"response":response_text"session_id":session_id}exceptExceptionase:logger.error(f"ErrorinAIagentchat:{str(e)}")raiseAIServiceError(f"Failedtogenerateagentresponse:{str(e)}")defreset_memory(selfsession_id:str="default")->None:"""Resettheagent'smemoryforasession.Args:session_id:ThesessionIDtoreset"""ifsession_idinself.sessions:self.sessions[session_id]={"history":[]"context":{}}logger.info(f"Resetmemoryforsession{session_id}")else:logger.warning(f"Session{session_id}notfound")def_generate_prompt(selfmessage:strhistory:List[Dict[strstr]])->str:"""GenerateapromptfortheAIservicebasedonthemessageandhistory.Args:message:Thecurrentmessagehistory:TheconversationhistoryReturns:Thegeneratedprompt"""#Createasystempromptsystem_prompt="""YouareahelpfulAIassistantforauniversitymanagementsystem.Youcanhelpwithcourseinformationstudentqueriesandadministrativetasks.Provideconciseaccurateandhelpfulresponses.Ifyoudon'tknowsomethingsaysoratherthanmakingupinformation."""#Formathistoryhistory_text=""forentryinhistory[:-1]:#Excludethecurrentmessagerole="User"ifentry["role"]=="user"else"Assistant"history_text+=f"{role}:{entry['content']}\n\n"#Combineeverythingprompt=f"{system_prompt}\n\n"ifhistory_text:prompt+=f"Previousconversation:\n{history_text}\n"prompt+=f"User:{message}\n\nAssistant:"returnprompt#Createsingletoninstanceuniversity_ai_agent=UniversityAIAgent()#Exportthesingletoninstance__all__=["university_ai_agent"]
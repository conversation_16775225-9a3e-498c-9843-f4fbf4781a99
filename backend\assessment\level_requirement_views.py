"""API views for level requirement operations"""
import logging
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import status
from rest_framework.permissions import IsAdminUser, IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from .models import LevelRequirement, StudentLevel

logger = logging.getLogger(__name__)


class LevelRequirementView(APIView):
    """View for getting and updating level requirements"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get all level requirements"""
        # Only staff can view level requirements
        if not request.user.is_staff:
            return Response(
                {"detail": "You do not have permission to view level requirements."},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get all level requirements
        level_requirements = LevelRequirement.objects.all().order_by("level")
        
        # Format the response
        response_data = {
            "level_requirements": [
                {
                    "level": req.level,
                    "name": dict(StudentLevel.LEVEL_CHOICES).get(req.level, "Unknown"),
                    "min_assessment_score": req.min_assessment_score,
                    "min_completed_courses": req.min_completed_courses,
                    "description": req.description,
                    "required_skills": req.required_skills
                }
                for req in level_requirements
            ]
        }
        
        return Response(response_data)
    
    def post(self, request):
        """Update a single level requirement"""
        # Only staff can update level requirements
        if not request.user.is_staff:
            return Response(
                {"detail": "You do not have permission to update level requirements."},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get the data from the request
        level = request.data.get("level")
        min_assessment_score = request.data.get("min_assessment_score")
        min_completed_courses = request.data.get("min_completed_courses", 0)
        description = request.data.get("description", "")
        required_skills = request.data.get("required_skills", [])
        
        # Validate the data
        if level is None or min_assessment_score is None:
            return Response(
                {"detail": "level and min_assessment_score are required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update or create the level requirement
        level_requirement, created = LevelRequirement.objects.update_or_create(
            level=level,
            defaults={
                "min_assessment_score": min_assessment_score,
                "min_completed_courses": min_completed_courses,
                "description": description,
                "required_skills": required_skills
            }
        )
        
        return Response({
            "level": level_requirement.level,
            "min_assessment_score": level_requirement.min_assessment_score,
            "min_completed_courses": level_requirement.min_completed_courses,
            "description": level_requirement.description,
            "required_skills": level_requirement.required_skills,
            "created": created
        })


class LevelRequirementBulkUpdateView(APIView):
    """View for bulk updating level requirements"""
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def post(self, request):
        """Bulk update level requirements"""
        # Get the data from the request
        requirements = request.data.get("requirements", [])
        
        if not requirements:
            return Response(
                {"detail": "No requirements provided."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update or create each level requirement
        updated_requirements = []
        for req in requirements:
            level = req.get("level")
            min_score_required = req.get("min_score_required")
            description = req.get("description", "")
            
            # Validate the data
            if level is None or min_score_required is None:
                continue
            
            # Update or create the level requirement
            level_requirement, created = LevelRequirement.objects.update_or_create(
                level=level,
                defaults={
                    "min_assessment_score": min_score_required,
                    "description": description
                }
            )
            
            updated_requirements.append({
                "level": level_requirement.level,
                "min_assessment_score": level_requirement.min_assessment_score,
                "description": level_requirement.description,
                "created": created
            })
        
        return Response({
            "updated_requirements": updated_requirements,
            "count": len(updated_requirements)
        })

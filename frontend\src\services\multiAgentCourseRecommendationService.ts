import axiosInstance from '../config/axios';
import { API_ENDPOINTS } from '../config/api';

// 🎯 Multi-Agent Course Recommendation Service
// Provides AI-powered course recommendations using specialized agents

export interface CourseRecommendation {
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  source: 'ai_agent' | 'database' | 'system';
  course_id?: number;
}

export interface AgentRecommendation {
  agent: string;
  recommendations: CourseRecommendation[];
  reasoning: string;
  confidence: number;
}

export interface MultiAgentRecommendationResponse {
  status: string;
  user_role: string;
  recommendations: Record<string, AgentRecommendation>;
  agents_used: string[];
  fallback_used: boolean;
  metadata?: {
    user_id: number;
    user_role: string;
    request_method: string;
    context_provided: boolean;
    agents_available: boolean;
  };
}

export interface RecommendationContext {
  focus_area?: string;
  difficulty_level?: string;
  subject_preference?: string;
  assessment_data?: any;
  [key: string]: any;
}

export interface AgentStatus {
  name: string;
  type: string;
  specialization: string;
  use_case: string;
}

export interface AgentsStatusResponse {
  agents_available: boolean;
  available_agents: AgentStatus[];
  recommendation_types: string[];
}

const multiAgentCourseRecommendationService = {
  /**
   * Get personalized course recommendations using multi-agent system
   */
  getMultiAgentRecommendations: async (
    context?: RecommendationContext
  ): Promise<MultiAgentRecommendationResponse> => {
    try {
      const response = await axiosInstance.post(
        API_ENDPOINTS.COURSES.RECOMMENDATIONS.MULTI_AGENT,
        {
          context: context || {},
        }
      );
      return response.data;
    } catch (error: any) {
      console.error('Error getting multi-agent recommendations:', error);
      throw new Error(
        error.response?.data?.message || 'Failed to get course recommendations'
      );
    }
  },

  /**
   * Get subject-specific course recommendations
   */
  getSubjectSpecificRecommendations: async (
    subject: string,
    context?: RecommendationContext
  ): Promise<any> => {
    try {
      const response = await axiosInstance.post(
        API_ENDPOINTS.COURSES.RECOMMENDATIONS.SUBJECT_SPECIFIC,
        {
          subject,
          context: context || {},
        }
      );
      return response.data;
    } catch (error: any) {
      console.error('Error getting subject-specific recommendations:', error);
      throw new Error(
        error.response?.data?.message ||
          'Failed to get subject-specific recommendations'
      );
    }
  },

  /**
   * Get assessment-based course recommendations
   */
  getAssessmentBasedRecommendations: async (
    assessmentData?: any
  ): Promise<any> => {
    try {
      const response = await axiosInstance.post(
        API_ENDPOINTS.COURSES.RECOMMENDATIONS.ASSESSMENT_BASED,
        {
          assessment_data: assessmentData,
        }
      );
      return response.data;
    } catch (error: any) {
      console.error('Error getting assessment-based recommendations:', error);
      throw new Error(
        error.response?.data?.message ||
          'Failed to get assessment-based recommendations'
      );
    }
  },

  /**
   * Get role-based course recommendations
   */
  getRoleBasedRecommendations: async (): Promise<any> => {
    try {
      const response = await axiosInstance.get(
        API_ENDPOINTS.COURSES.RECOMMENDATIONS.ROLE_BASED
      );
      return response.data;
    } catch (error: any) {
      console.error('Error getting role-based recommendations:', error);
      throw new Error(
        error.response?.data?.message ||
          'Failed to get role-based recommendations'
      );
    }
  },

  /**
   * Get status of recommendation agents
   */
  getAgentsStatus: async (): Promise<AgentsStatusResponse> => {
    try {
      const response = await axiosInstance.get(
        API_ENDPOINTS.COURSES.RECOMMENDATIONS.AGENTS_STATUS
      );
      return response.data;
    } catch (error: any) {
      console.error('Error getting agents status:', error);
      throw new Error(
        error.response?.data?.message || 'Failed to get agents status'
      );
    }
  },

  /**
   * Get quick recommendations based on user role
   */
  getQuickRecommendations: async (
    userRole: string
  ): Promise<CourseRecommendation[]> => {
    try {
      const response =
        await multiAgentCourseRecommendationService.getRoleBasedRecommendations();

      // Extract recommendations from all agents
      const allRecommendations: CourseRecommendation[] = [];

      if (response.recommendations) {
        Object.values(response.recommendations).forEach((agentRec: any) => {
          if (agentRec.recommendations) {
            allRecommendations.push(...agentRec.recommendations);
          }
        });
      }

      // Sort by priority and return top 5
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      allRecommendations.sort(
        (a, b) =>
          (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0)
      );

      return allRecommendations.slice(0, 5);
    } catch (error) {
      console.error('Error getting quick recommendations:', error);
      return multiAgentCourseRecommendationService.getFallbackRecommendations(
        userRole
      );
    }
  },

  /**
   * Get fallback recommendations when AI agents are not available
   */
  getFallbackRecommendations: (userRole: string): CourseRecommendation[] => {
    const fallbackRecommendations: Record<string, CourseRecommendation[]> = {
      STUDENT: [
        {
          title: 'Introduction to Programming',
          description: 'Learn the fundamentals of programming with Python',
          priority: 'high',
          source: 'system',
        },
        {
          title: 'Mathematics for Computer Science',
          description: 'Essential mathematical concepts for CS students',
          priority: 'high',
          source: 'system',
        },
        {
          title: 'Data Structures and Algorithms',
          description: 'Core computer science concepts and problem-solving',
          priority: 'medium',
          source: 'system',
        },
      ],
      PROFESSOR: [
        {
          title: 'Course Design and Development',
          description: 'Learn how to create engaging educational content',
          priority: 'high',
          source: 'system',
        },
        {
          title: 'Assessment and Evaluation Methods',
          description: 'Modern approaches to student assessment',
          priority: 'medium',
          source: 'system',
        },
        {
          title: 'Educational Technology Integration',
          description: 'Using technology to enhance learning outcomes',
          priority: 'medium',
          source: 'system',
        },
      ],
      ADMIN: [
        {
          title: 'Institutional Planning and Strategy',
          description: 'Strategic planning for educational institutions',
          priority: 'high',
          source: 'system',
        },
        {
          title: 'Educational Data Analytics',
          description: 'Using data to improve institutional outcomes',
          priority: 'high',
          source: 'system',
        },
        {
          title: 'Policy Development and Implementation',
          description: 'Creating and implementing educational policies',
          priority: 'medium',
          source: 'system',
        },
      ],
    };

    return (
      fallbackRecommendations[userRole] || fallbackRecommendations['STUDENT']
    );
  },

  /**
   * Format recommendations for display
   */
  formatRecommendationsForDisplay: (
    response: MultiAgentRecommendationResponse
  ): any[] => {
    const formattedRecommendations: any[] = [];

    if (response.recommendations) {
      Object.entries(response.recommendations).forEach(
        ([category, agentRec]) => {
          formattedRecommendations.push({
            category: category
              .replace(/_/g, ' ')
              .replace(/\b\w/g, l => l.toUpperCase()),
            agent: agentRec.agent,
            recommendations: agentRec.recommendations,
            reasoning: agentRec.reasoning,
            confidence: agentRec.confidence,
            icon: multiAgentCourseRecommendationService.getAgentIcon(
              agentRec.agent
            ),
          });
        }
      );
    }

    return formattedRecommendations;
  },

  /**
   * Get icon for agent type
   */
  getAgentIcon: (agentType: string): string => {
    const agentIcons: Record<string, string> = {
      advisor_agent: '🎯',
      assessor_agent: '📊',
      content_creator_agent: '✨',
      math_tutor_agent: '🔢',
      science_tutor_agent: '🔬',
      language_tutor_agent: '📝',
      tutor_agent: '🎓',
      fallback_system: '🤖',
    };

    return agentIcons[agentType] || '🤖';
  },

  /**
   * Get recommendation priority color
   */
  getPriorityColor: (priority: string): string => {
    const priorityColors: Record<string, string> = {
      high: '#f44336',
      medium: '#ff9800',
      low: '#4caf50',
    };

    return priorityColors[priority] || '#757575';
  },
};

export default multiAgentCourseRecommendationService;

/**
 * AI Service Registry
 *
 * Centralized registry for managing all AI services, their health status,
 * configuration, and providing unified access patterns.
 */

import { BaseAIService } from './BaseAIService';
import { AIServiceConfig, clearAIServiceCache } from './aiServiceUtils';
import { logError } from './errorHandling';

export interface ServiceInfo {
  name: string;
  instance: BaseAIService;
  status: 'healthy' | 'unhealthy' | 'unknown';
  lastHealthCheck: number;
  errorCount: number;
  lastError?: any;
}

export interface ServiceRegistryConfig {
  healthCheckInterval: number; // milliseconds
  maxErrorCount: number;
  autoRecovery: boolean;
}

class ServiceRegistry {
  private services: Map<string, ServiceInfo> = new Map();
  private config: ServiceRegistryConfig;
  private healthCheckTimer?: NodeJS.Timeout;

  constructor(config: Partial<ServiceRegistryConfig> = {}) {
    this.config = {
      healthCheckInterval: 300000, // 5 minutes
      maxErrorCount: 5,
      autoRecovery: true,
      ...config,
    };
  }

  /**
   * Register an AI service
   */
  register(key: string, service: BaseAIService): void {
    this.services.set(key, {
      name: service.getServiceInfo().name,
      instance: service,
      status: 'unknown',
      lastHealthCheck: 0,
      errorCount: 0,
    });

    console.log(
      `🔧 Registered AI service: ${key} (${service.getServiceInfo().name})`
    );
  }

  /**
   * Get a registered service
   */
  get(key: string): BaseAIService | undefined {
    const serviceInfo = this.services.get(key);
    return serviceInfo?.instance;
  }

  /**
   * Get all registered services
   */
  getAll(): Map<string, ServiceInfo> {
    return new Map(this.services);
  }

  /**
   * Get service health status
   */
  getServiceStatus(key: string): 'healthy' | 'unhealthy' | 'unknown' {
    const serviceInfo = this.services.get(key);
    return serviceInfo?.status || 'unknown';
  }

  /**
   * Get overall system health
   */
  getSystemHealth(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    services: { [key: string]: string };
    healthyCount: number;
    totalCount: number;
  } {
    const services: { [key: string]: string } = {};
    let healthyCount = 0;
    const totalCount = this.services.size;

    for (const [key, info] of this.services) {
      services[key] = info.status;
      if (info.status === 'healthy') {
        healthyCount++;
      }
    }

    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (healthyCount === totalCount) {
      status = 'healthy';
    } else if (healthyCount > 0) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }

    return {
      status,
      services,
      healthyCount,
      totalCount,
    };
  }

  /**
   * Perform health check on a specific service
   */
  async checkServiceHealth(key: string): Promise<boolean> {
    const serviceInfo = this.services.get(key);
    if (!serviceInfo) {
      return false;
    }

    try {
      const healthStatus = await serviceInfo.instance.getHealthStatus();
      const isHealthy = healthStatus.status === 'healthy';

      serviceInfo.status = isHealthy ? 'healthy' : 'unhealthy';
      serviceInfo.lastHealthCheck = Date.now();

      if (isHealthy && serviceInfo.errorCount > 0) {
        // Service recovered, reset error count
        serviceInfo.errorCount = 0;
        serviceInfo.lastError = undefined;
        console.log(`✅ Service ${key} recovered`);
      }

      return isHealthy;
    } catch (error) {
      serviceInfo.status = 'unhealthy';
      serviceInfo.lastHealthCheck = Date.now();
      serviceInfo.errorCount++;
      serviceInfo.lastError = error;

      logError(error, `Health check for ${key}`, {
        errorCount: serviceInfo.errorCount,
      });
      return false;
    }
  }

  /**
   * Perform health check on all services
   */
  async checkAllServicesHealth(): Promise<{ [key: string]: boolean }> {
    const results: { [key: string]: boolean } = {};

    const healthChecks = Array.from(this.services.keys()).map(async key => {
      const isHealthy = await this.checkServiceHealth(key);
      results[key] = isHealthy;
      return { key, isHealthy };
    });

    await Promise.allSettled(healthChecks);
    return results;
  }

  /**
   * Start automatic health monitoring
   */
  startHealthMonitoring(): void {
    if (this.healthCheckTimer) {
      return; // Already running
    }

    console.log(
      `🏥 Starting health monitoring (interval: ${this.config.healthCheckInterval}ms)`
    );

    this.healthCheckTimer = setInterval(async () => {
      try {
        await this.checkAllServicesHealth();
        const systemHealth = this.getSystemHealth();

        if (systemHealth.status !== 'healthy') {
          console.warn(
            `⚠️ System health: ${systemHealth.status} (${systemHealth.healthyCount}/${systemHealth.totalCount} services healthy)`
          );
        }
      } catch (error) {
        logError(error, 'Health monitoring');
      }
    }, this.config.healthCheckInterval);
  }

  /**
   * Stop automatic health monitoring
   */
  stopHealthMonitoring(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = undefined;
      console.log('🛑 Stopped health monitoring');
    }
  }

  /**
   * Record an error for a service
   */
  recordError(key: string, error: any): void {
    const serviceInfo = this.services.get(key);
    if (!serviceInfo) {
      return;
    }

    serviceInfo.errorCount++;
    serviceInfo.lastError = error;

    // Mark as unhealthy if error count exceeds threshold
    if (serviceInfo.errorCount >= this.config.maxErrorCount) {
      serviceInfo.status = 'unhealthy';
      console.warn(
        `🚨 Service ${key} marked as unhealthy (${serviceInfo.errorCount} errors)`
      );
    }
  }

  /**
   * Update service configuration
   */
  updateServiceConfig(key: string, config: Partial<AIServiceConfig>): boolean {
    const serviceInfo = this.services.get(key);
    if (!serviceInfo) {
      return false;
    }

    serviceInfo.instance.updateConfig(config);
    console.log(`⚙️ Updated configuration for service ${key}`);
    return true;
  }

  /**
   * Update configuration for all services
   */
  updateAllServicesConfig(config: Partial<AIServiceConfig>): void {
    for (const serviceInfo of this.services.values()) {
      serviceInfo.instance.updateConfig(config);
    }
    console.log('⚙️ Updated configuration for all services');
  }

  /**
   * Enable/disable fallback for a service
   */
  setServiceFallback(key: string, enabled: boolean): boolean {
    const serviceInfo = this.services.get(key);
    if (!serviceInfo) {
      console.error(`Service ${key} not found in registry`);
      return false;
    }

    serviceInfo.instance.setFallbackEnabled(enabled);
    console.log(
      `🔄 ${enabled ? 'Enabled' : 'Disabled'} fallback for service ${key}`
    );
    return true;
  }

  /**
   * Clear cache for a specific service or all services
   */
  clearServiceCache(key?: string): void {
    if (key) {
      clearAIServiceCache(key);
      console.log(`🗑️ Cleared cache for service ${key}`);
    } else {
      clearAIServiceCache();
      console.log('🗑️ Cleared cache for all services');
    }
  }

  /**
   * Get service statistics
   */
  getServiceStats(key: string): {
    name: string;
    status: string;
    errorCount: number;
    lastHealthCheck: number;
    config: AIServiceConfig;
  } | null {
    const serviceInfo = this.services.get(key);
    if (!serviceInfo) {
      return null;
    }

    return {
      name: serviceInfo.name,
      status: serviceInfo.status,
      errorCount: serviceInfo.errorCount,
      lastHealthCheck: serviceInfo.lastHealthCheck,
      config: serviceInfo.instance.getConfig(),
    };
  }

  /**
   * Get all service statistics
   */
  getAllServiceStats(): { [key: string]: any } {
    const stats: { [key: string]: any } = {};

    for (const [key, serviceInfo] of this.services) {
      stats[key] = {
        name: serviceInfo.name,
        status: serviceInfo.status,
        errorCount: serviceInfo.errorCount,
        lastHealthCheck: serviceInfo.lastHealthCheck,
        config: serviceInfo.instance.getConfig(),
      };
    }

    return stats;
  }

  /**
   * Reset error counts for all services
   */
  resetErrorCounts(): void {
    for (const serviceInfo of this.services.values()) {
      serviceInfo.errorCount = 0;
      serviceInfo.lastError = undefined;
    }
    console.log('🔄 Reset error counts for all services');
  }

  /**
   * Shutdown the registry
   */
  shutdown(): void {
    this.stopHealthMonitoring();
    this.services.clear();
    console.log('🛑 Service registry shutdown');
  }
}

// Create and export singleton instance
export const serviceRegistry = new ServiceRegistry();

// Auto-start health monitoring in production
if (process.env.NODE_ENV === 'production') {
  serviceRegistry.startHealthMonitoring();
}

export default serviceRegistry;

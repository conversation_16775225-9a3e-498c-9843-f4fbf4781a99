"""
URL patterns for the utils app. 
This module defines the URL patterns for the utils app including 
AI endpoints and monitoring dashboard.
"""

from django.urls import include, path

# Import views
from . import ai_config_views

# Import URL patterns
from .ai_config_urls import urlpatterns as ai_config_urlpatterns

urlpatterns = [
    # AI Configuration endpoints
    path("ai/", include(ai_config_urlpatterns)),
    
    # Direct AI endpoints (for frontend API calls)
    # Chat endpoints - order matters! More specific paths first
    path("ai/chat/conversations/", ai_config_views.conversations_endpoint, name="ai_chat_conversations"),
    path("ai/chat/conversations/<int:conversation_id>/", ai_config_views.conversation_detail_endpoint, name="ai_chat_conversation_detail"),
    path("ai/chat/", ai_config_views.chat_endpoint, name="ai_chat"),
    path("ai/multi-agent-chat/", ai_config_views.multi_agent_chat_endpoint, name="ai_multi_agent_chat"),
    path("ai/assistant/", ai_config_views.assistant_endpoint, name="ai_assistant"),
    path("ai/tutor/", ai_config_views.tutor_endpoint, name="ai_tutor"),
    path("ai/health/", ai_config_views.ai_health_check, name="ai_health_check"),
]

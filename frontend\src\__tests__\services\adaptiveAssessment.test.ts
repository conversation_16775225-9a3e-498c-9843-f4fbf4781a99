import assessmentService from '../../services/assessmentService';
import axiosInstance from '../../config/axios';
import { API_ENDPOINTS } from '../../config/api';

// Mock axios
jest.mock('../../config/axios');
const mockedAxios = axiosInstance as jest.Mocked<typeof axiosInstance>;

describe('Adaptive Assessment Features', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('startAdaptiveAssessment', () => {
    it('should successfully start an adaptive assessment', async () => {
      const mockResponse = {
        data: {
          session_id: 'adaptive-123',
          assessment_id: 'test-assessment',
          status: 'started',
          initial_difficulty: 0.5,
        },
      };

      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      const result = await assessmentService.startAdaptiveAssessment('test-assessment', {
        initial_difficulty: 0.5,
        max_questions: 20,
      });

      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${API_ENDPOINTS.ASSESSMENT.BASE}/adaptive/start/test-assessment`,
        {
          initial_difficulty: 0.5,
          max_questions: 20,
        }
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle errors when starting adaptive assessment', async () => {
      const mockError = {
        response: {
          data: { message: 'Assessment not found' },
        },
      };

      mockedAxios.post.mockRejectedValueOnce(mockError);

      const result = await assessmentService.startAdaptiveAssessment('invalid-assessment');

      expect(result).toEqual({
        status: 'error',
        message: 'Assessment not found',
        error: mockError.response.data,
      });
    });
  });

  describe('submitAdaptiveResponse', () => {
    it('should successfully submit an adaptive response', async () => {
      const mockResponse = {
        data: {
          is_correct: true,
          difficulty_adjustment: 0.1,
          next_question_available: true,
          confidence_score: 0.85,
        },
      };

      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      const result = await assessmentService.submitAdaptiveResponse(
        'test-assessment',
        'question-123',
        'correct answer'
      );

      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${API_ENDPOINTS.ASSESSMENT.BASE}/adaptive/test-assessment/response`,
        {
          question_id: 'question-123',
          answer: 'correct answer',
          response_time: expect.any(Number),
        }
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle network errors gracefully', async () => {
      mockedAxios.post.mockRejectedValueOnce(new Error('Network Error'));

      const result = await assessmentService.submitAdaptiveResponse(
        'test-assessment',
        'question-123',
        'answer'
      );

      expect(result.status).toBe('error');
      expect(result.message).toContain('Failed to submit adaptive response');
    });
  });

  describe('getNextAdaptiveQuestion', () => {
    it('should successfully get next adaptive question', async () => {
      const mockResponse = {
        data: {
          question: {
            id: 'question-456',
            text: 'What is the capital of France?',
            type: 'multiple_choice',
            options: ['London', 'Berlin', 'Paris', 'Madrid'],
            difficulty: 0.6,
          },
          progress: {
            questions_answered: 5,
            estimated_ability: 0.7,
            confidence_interval: [0.6, 0.8],
          },
        },
      };

      mockedAxios.get.mockResolvedValueOnce(mockResponse);

      const result = await assessmentService.getNextAdaptiveQuestion('test-assessment');

      expect(mockedAxios.get).toHaveBeenCalledWith(
        `${API_ENDPOINTS.ASSESSMENT.BASE}/adaptive/test-assessment/next-question`
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle end of assessment scenario', async () => {
      const mockResponse = {
        data: {
          assessment_complete: true,
          final_ability_estimate: 0.75,
          total_questions: 15,
          accuracy: 0.8,
        },
      };

      mockedAxios.get.mockResolvedValueOnce(mockResponse);

      const result = await assessmentService.getNextAdaptiveQuestion('test-assessment');

      expect(result.assessment_complete).toBe(true);
      expect(result.final_ability_estimate).toBe(0.75);
    });
  });
});

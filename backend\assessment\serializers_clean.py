from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework import serializers
from .models import (
    AIAssessmentController,
    Assessment,
    AssessmentQuestion,
    AssessmentResponse,
    AssessmentSettings,
    CompetencyBadge,
    LearningAnalytics,
    LearningPathway,
    LevelRequirement,
    PathwayProgress,
    ProgressionMilestone,
    ProgressReport,
    SkillGap,
    StudentBadge,
    StudentLevel,
    StudentMilestone,
    StudentProgressTracking
)

# Import Skill from core app where it's actually defined
from core.models import Skill

# Import Course model directly
try:
    from courses.models import Course
except ImportError:
    # Fallback to importing from models module
    from django.apps import apps
    try:
        Course = apps.get_model("courses", "Course")
    except LookupError:
        # Define a placeholder Course model for development
        from django.db import models
        class Course(models.Model):
            title = models.CharField(max_length=200)
            course_code = models.Char<PERSON>ield(max_length=10)

User = get_user_model()


class AssessmentResponseSerializer(serializers.ModelSerializer):
    """Serializer for assessment responses"""
    question_text = serializers.CharField(source="question.question_text", read_only=True)
    points_possible = serializers.IntegerField(source="question.points", read_only=True)
    
    # Add a combined field for the answer that prioritizes answer_text
    combined_answer = serializers.SerializerMethodField()
    
    def get_combined_answer(self, obj):
        """Get the answer from the primary field first then fallbacks"""
        # First try the primary field
        if obj.answer_text:
            return obj.answer_text
        # Then try the legacy fields
        elif obj.answer:
            return obj.answer
        elif obj.student_answer:
            # If it's a dict or JSON return as is
            return obj.student_answer
        # Default to empty string
        return ""
    
    class Meta:
        model = AssessmentResponse
        fields = [
            "id", "question", "question_text", "answer_text", "answer", 
            "student_answer", "combined_answer", "is_correct", "points_earned", 
            "points_possible", "time_spent", "feedback", "created_at"
        ]
        read_only_fields = ["is_correct", "points_earned", "created_at", "combined_answer"]


class AssessmentSkillSerializer(serializers.ModelSerializer):
    """Serializer for assessment skills"""
    skill_name = serializers.CharField(source="name", read_only=True)
    
    class Meta:
        model = Skill
        fields = ["id", "name", "skill_name", "category", "description"]


class AssessmentQuestionAdminSerializer(serializers.ModelSerializer):
    # Include text field for backward compatibility - make it required
    text = serializers.CharField(source="question_text", required=True)
    
    class Meta:
        model = AssessmentQuestion
        fields = [
            "id", "text", "question_type", "category", "learning_path", 
            "difficulty_level", "options", "points", "skills_assessed", 
            "correct_answer", "explanation", "ai_suggested", "ai_reviewed", 
            "is_public", "is_placement", "ai_generated", "created_at", 
            "updated_at", "created_by"
        ]
        extra_kwargs = {
            'id': {'read_only': False, 'required': False},  # Allow id in input but don't require it
            'created_at': {'read_only': True},
            'updated_at': {'read_only': True}
        }
    
    def validate_correct_answer(self, value):
        """Validate that correct_answer is properly formatted"""
        if isinstance(value, str):
            # Convert string to object format
            return {"answer": value, "explanation": ""}
        elif not isinstance(value, dict):
            raise serializers.ValidationError("Correct answer must be a string or an object with 'answer' field")
        elif "answer" not in value:
            raise serializers.ValidationError("Correct answer object must contain 'answer' field")
        return value
    
    def to_representation(self, instance):
        """Ensure correct_answer is properly formatted in response"""
        data = super().to_representation(instance)
        
        # Format correct_answer if needed
        if "correct_answer" in data:
            correct_answer = data["correct_answer"]
            if isinstance(correct_answer, str):
                data["correct_answer"] = {"answer": correct_answer, "explanation": ""}
            elif isinstance(correct_answer, dict) and "answer" not in correct_answer:
                # Try to convert to proper format
                data["correct_answer"] = {"answer": str(correct_answer), "explanation": ""}
            elif isinstance(correct_answer, dict) and "answer" in correct_answer:
                # Ensure explanation field exists
                if "explanation" not in correct_answer:
                    data["correct_answer"]["explanation"] = ""
        
        # Ensure both text and question_text fields are present for compatibility
        if "question_text" in data and not data.get("text"):
            data["text"] = data["question_text"]
        elif "text" in data and not data.get("question_text"):
            data["question_text"] = data["text"]
        
        return data
    
    def validate(self, data):
        """Validate that all required fields are present and properly mapped"""
        # Handle text field mapping
        if 'text' in data and not data.get('question_text'):
            data['question_text'] = data['text']
        elif not data.get('question_text') and not data.get('text'):
            raise serializers.ValidationError("Question text is required (either as 'text' or 'question_text')")
        
        # Ensure question_type is present
        if not data.get('question_type'):
            raise serializers.ValidationError("question_type is required")
        
        # Ensure correct_answer is present
        if 'correct_answer' not in data or data['correct_answer'] is None:
            raise serializers.ValidationError("correct_answer is required")
        
        # Set default values for optional fields
        if 'options' not in data:
            data['options'] = []
        if 'difficulty_level' not in data:
            data['difficulty_level'] = 1
        if 'points' not in data:
            data['points'] = 1
        if 'category' not in data:
            data['category'] = 'general'
        
        return data
    
    def create(self, validated_data):
        # Ensure question_text is set from text
        if 'text' in validated_data:
            validated_data['question_text'] = validated_data['text']
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        # Ensure question_text is set from text
        if 'text' in validated_data:
            validated_data['question_text'] = validated_data['text']
        return super().update(instance, validated_data)


class AIQuestionReviewSerializer(serializers.ModelSerializer):
    ai_confidence = serializers.FloatField(read_only=True)
    ai_flags = serializers.ListField(read_only=True)
    
    class Meta:
        model = AssessmentQuestion
        fields = "__all__"


class AssessmentAdminSerializer(serializers.ModelSerializer):
    student_name = serializers.SerializerMethodField()
    student_email = serializers.SerializerMethodField()
    student_id = serializers.SerializerMethodField()
    assessment_type_display = serializers.CharField(source="get_assessment_type_display", read_only=True)
    responses = AssessmentResponseSerializer(many=True, read_only=True)
    initial_level_display = serializers.SerializerMethodField()
    final_level_display = serializers.SerializerMethodField()
    initial_level = serializers.SerializerMethodField()
    final_level = serializers.SerializerMethodField()
    level_changed = serializers.SerializerMethodField()
    score_display = serializers.SerializerMethodField()
    
    def get_student_id(self, obj):
        """Get student ID ensuring it's always available"""
        if obj.student:
            return obj.student.id
        return None
    
    def get_student_email(self, obj):
        """Get student email ensuring it's always available"""
        if obj.student:
            return obj.student.email
        return None
    
    def get_student_name(self, obj):
        """Get student name ensuring it's not 'undefined undefined'"""
        if not obj.student:
            return "Unknown Student"
        
        # Try to get full name using get_full_name method
        full_name = (obj.student.get_full_name() if callable(obj.student.get_full_name) 
                    else obj.student.get_full_name)
        
        # If full name is empty or contains 'undefined' build from first and last name
        if not full_name or "undefined" in full_name:
            first_name = getattr(obj.student, "first_name", "")
            last_name = getattr(obj.student, "last_name", "")
            
            # If both first and last name are empty use username or email
            if not first_name and not last_name:
                return (getattr(obj.student, "username", "") or 
                       getattr(obj.student, "email", "") or 
                       f"User ID: {obj.student.id}")
            
            # Otherwise combine first and last name
            return f"{first_name} {last_name}".strip()
        
        return full_name
    
    class Meta:
        model = Assessment
        fields = [
            "id", "title", "description", "assessment_type", "assessment_type_display",
            "student", "student_name", "student_email", "student_id", "status", 
            "score", "score_display", "start_time", "end_time", "completed", 
            "time_spent", "initial_level", "initial_level_display", "final_level", 
            "final_level_display", "level_changed", "detailed_results", "responses", 
            "created_at", "updated_at"
        ]
        read_only_fields = [
            "student_name", "student_email", "student_id", "assessment_type_display",
            "score_display", "initial_level", "initial_level_display", "final_level",
            "final_level_display", "level_changed", "responses", "created_at", "updated_at"
        ]
    
    def get_score_display(self, obj):
        """Format score as percentage with % symbol"""
        if obj.score is not None:
            return f"{obj.score}%"
        return None
    
    def get_initial_level(self, obj):
        """Get initial level from detailed_results or student level"""
        # First try to get from detailed_results
        detailed_results = obj.detailed_results or {}
        initial_level = detailed_results.get("initial_level")
        
        # If not found try to get from student level
        if initial_level is None:
            try:
                student_level = StudentLevel.objects.filter(student=obj.student).first()
                if (student_level and student_level.progression_history and 
                    len(student_level.progression_history) > 0):
                    # Find the history entry for this assessment
                    for entry in student_level.progression_history:
                        if entry.get("assessment_id") == obj.id:
                            initial_level = entry.get("from_level")
                            break
            except Exception as e:
                print(f"Error getting student level: {e}")
        
        return initial_level
    
    def get_final_level(self, obj):
        """Get final level from detailed_results or student level"""
        # First try to get from detailed_results
        detailed_results = obj.detailed_results or {}
        final_level = detailed_results.get("final_level")
        
        # If not found try to get from student level
        if final_level is None:
            try:
                student_level = StudentLevel.objects.filter(student=obj.student).first()
                if student_level:
                    # If we have a student level use the current level as the final level
                    final_level = student_level.current_level
                    
                    # Also check progression history for this assessment
                    if (student_level.progression_history and 
                        len(student_level.progression_history) > 0):
                        for entry in student_level.progression_history:
                            if entry.get("assessment_id") == obj.id:
                                final_level = entry.get("to_level")
                                break
            except Exception as e:
                print(f"Error getting student level: {e}")
        
        return final_level
    
    def get_level_changed(self, obj):
        """Get level_changed from detailed_results or calculate it"""
        # First try to get from detailed_results
        detailed_results = obj.detailed_results or {}
        level_changed = detailed_results.get("level_changed")
        
        # If not found calculate from initial and final levels
        if level_changed is None:
            initial_level = self.get_initial_level(obj)
            final_level = self.get_final_level(obj)
            if initial_level is not None and final_level is not None:
                level_changed = initial_level != final_level
            else:
                level_changed = False
        
        return level_changed
    
    def get_initial_level_display(self, obj):
        """Get display name for initial level"""
        initial_level = self.get_initial_level(obj)
        if initial_level is None:
            return None
        
        # First try to get from detailed_results
        detailed_results = obj.detailed_results or {}
        initial_level_display = detailed_results.get("initial_level_display")
        
        # If not found try to get from student level
        if initial_level_display is None:
            try:
                student_level = StudentLevel.objects.filter(student=obj.student).first()
                if (student_level and student_level.progression_history and 
                    len(student_level.progression_history) > 0):
                    # Find the history entry for this assessment
                    for entry in student_level.progression_history:
                        if entry.get("assessment_id") == obj.id:
                            initial_level_display = entry.get("from_level_display")
                            break
            except Exception as e:
                print(f"Error getting student level: {e}")
        
        # If still not found use the level choices
        if initial_level_display is None:
            level_choices = dict(StudentLevel.LEVEL_CHOICES)
            initial_level_display = level_choices.get(initial_level, "Unknown")
        
        return initial_level_display
    
    def get_final_level_display(self, obj):
        """Get display name for final level"""
        final_level = self.get_final_level(obj)
        if final_level is None:
            return None
        
        # First try to get from detailed_results
        detailed_results = obj.detailed_results or {}
        final_level_display = detailed_results.get("final_level_display")
        
        # If not found try to get from student level
        if final_level_display is None:
            try:
                student_level = StudentLevel.objects.filter(student=obj.student).first()
                if student_level:
                    # If we have a student level use the current level display as the final level display
                    final_level_display = student_level.current_level_display
                    
                    # Also check progression history for this assessment
                    if (student_level.progression_history and 
                        len(student_level.progression_history) > 0):
                        for entry in student_level.progression_history:
                            if entry.get("assessment_id") == obj.id:
                                final_level_display = entry.get("to_level_display")
                                break
            except Exception as e:
                print(f"Error getting student level: {e}")
        
        # If still not found use the level choices
        if final_level_display is None:
            level_choices = dict(StudentLevel.LEVEL_CHOICES)
            final_level_display = level_choices.get(final_level, "Unknown")
        
        return final_level_display


class AssessmentQuestionSerializer(serializers.ModelSerializer):
    """Serializer for assessment questions"""
    # Include text field for backward compatibility
    text = serializers.CharField(source="question_text", required=True)
    
    class Meta:
        model = AssessmentQuestion
        fields = [
            "id", "text", "question_type", "category", "learning_path", 
            "difficulty_level", "options", "points", "skills_assessed", 
            "correct_answer", "ai_suggested", "ai_reviewed", "is_public", 
            "is_placement", "ai_generated"
        ]
        read_only_fields = ["created_at", "updated_at"]
    
    def validate_correct_answer(self, value):
        """Validate that correct_answer is properly formatted"""
        if isinstance(value, str):
            # Convert string to object format
            return {"answer": value, "explanation": ""}
        elif not isinstance(value, dict):
            raise serializers.ValidationError("Correct answer must be a string or an object with 'answer' field")
        elif "answer" not in value:
            raise serializers.ValidationError("Correct answer object must contain 'answer' field")
        return value
    
    def to_representation(self, instance):
        """Format correct answer in representation"""
        data = super(serializers.ModelSerializer, self).to_representation(instance)
        
        # Format correct_answer if needed and it's included
        if "correct_answer" in data:
            correct_answer = data["correct_answer"]
            if isinstance(correct_answer, str):
                data["correct_answer"] = {"answer": correct_answer, "explanation": ""}
            elif isinstance(correct_answer, dict) and "answer" not in correct_answer:
                # Try to convert to proper format
                data["correct_answer"] = {"answer": str(correct_answer), "explanation": ""}
            elif isinstance(correct_answer, dict) and "answer" in correct_answer:
                # Ensure explanation field exists
                if "explanation" not in correct_answer:
                    data["correct_answer"]["explanation"] = ""
        
        # Only include correct answer if explicitly requested
        if not self.context.get("show_correct_answer", False):
            data.pop("correct_answer", None)
        
        return data


class AssessmentQuestionDetailSerializer(AssessmentQuestionSerializer):
    """Detailed serializer for admin/instructor use"""
    
    class Meta(AssessmentQuestionSerializer.Meta):
        fields = AssessmentQuestionSerializer.Meta.fields + [
            "correct_answer", "created_by", "is_active", "created_at", "updated_at"
        ]


class AssessmentResultBaseSerializer(serializers.ModelSerializer):
    """Base serializer for assessment results"""
    responses = AssessmentResponseSerializer(many=True, read_only=True)
    skill_analysis = serializers.SerializerMethodField()
    recommendations = serializers.SerializerMethodField()
    
    class Meta:
        model = Assessment
        fields = [
            "id", "assessment_type", "score", "responses", "skill_analysis", 
            "recommendations", "completed", "start_time", "end_time", "created_at"
        ]
        read_only_fields = ["score", "skill_analysis", "recommendations", "completed", "created_at"]
    
    def get_skill_analysis(self, obj):
        """Get skill analysis from detailed results"""
        if not obj.completed:
            return None
        return obj.detailed_results.get("skill_analysis", {})
    
    def get_recommendations(self, obj):
        """Get recommendations based on assessment results"""
        if not obj.completed:
            return None
        return obj.detailed_results.get("recommendations", [])


class AssessmentDetailSerializer(serializers.ModelSerializer):
    results = AssessmentResultBaseSerializer(many=True, read_only=True)
    questions = AssessmentQuestionSerializer(many=True, read_only=True)
    responses = AssessmentResponseSerializer(many=True, read_only=True)
    student_name = serializers.SerializerMethodField()
    course = serializers.PrimaryKeyRelatedField(queryset=Course.objects.all(), required=False, allow_null=True)
    
    def get_student_name(self, obj):
        """Get student name ensuring it's not 'undefined undefined'"""
        if not obj.student:
            return "Unknown Student"
        
        # Try to get full name using get_full_name method
        full_name = (obj.student.get_full_name() if callable(obj.student.get_full_name) 
                    else obj.student.get_full_name)
        
        # If full name is empty or contains 'undefined' build from first and last name
        if not full_name or "undefined" in full_name:
            first_name = getattr(obj.student, "first_name", "")
            last_name = getattr(obj.student, "last_name", "")
            
            # If both first and last name are empty use username or email
            if not first_name and not last_name:
                return getattr(obj.student, "username", "") or getattr(obj.student, "email", "Unknown Student")
            
            # Otherwise combine first and last name
            return f"{first_name} {last_name}".strip()
        
        return full_name
    
    student_level = serializers.SerializerMethodField()
    initial_level = serializers.SerializerMethodField()
    final_level = serializers.SerializerMethodField()
    level_changed = serializers.SerializerMethodField()
    student_name = serializers.CharField(source="student.get_full_name", read_only=True)
    student_email = serializers.CharField(source="student.email", read_only=True)
    student_id = serializers.IntegerField(source="student.id", read_only=True)
    initial_level_display = serializers.SerializerMethodField()
    final_level_display = serializers.SerializerMethodField()
    strengths = serializers.SerializerMethodField()
    weaknesses = serializers.SerializerMethodField()
    assessment_type_display = serializers.CharField(source="get_assessment_type_display", read_only=True)
    score_display = serializers.SerializerMethodField()
    
    class Meta:
        model = Assessment
        fields = [
            "id", "student", "course", "questions", "results", "responses", 
            "start_time", "end_time", "status", "score", "score_display", 
            "assessment_type", "assessment_type_display", "detailed_results", 
            "student_level", "initial_level", "final_level", "level_changed", 
            "student_name", "student_email", "initial_level_display", 
            "final_level_display", "student_id", "strengths", "weaknesses", "time_spent"
        ]
        read_only_fields = [
            "start_time", "end_time", "score", "score_display", "detailed_results", 
            "student_level", "initial_level", "final_level", "level_changed", 
            "initial_level_display", "final_level_display", "assessment_type_display", 
            "student_email", "strengths", "weaknesses", "responses", "time_spent"
        ]
    
    def get_score_display(self, obj):
        """Format score as percentage with % symbol"""
        if obj.score is not None:
            return f"{obj.score}%"
        return None
    
    def get_student_level(self, obj):
        """Get student level information"""
        try:
            if not obj.student:
                return None
                
            student_level = StudentLevel.objects.filter(student=obj.student).first()
            if not student_level:
                return {"current_level": 1, "current_level_display": "Beginner"}
            
            return {
                "current_level": student_level.current_level,
                "current_level_display": student_level.current_level_display,
                "last_assessment_date": (student_level.last_assessment_date.isoformat() 
                                       if student_level.last_assessment_date else None)
            }
        except Exception as e:
            print(f"Error getting student level: {e}")
            return None
    
    def get_initial_level(self, obj):
        """Get initial level from detailed_results or student level"""
        # First try to get from detailed_results
        detailed_results = obj.detailed_results or {}
        initial_level = detailed_results.get("initial_level")
        
        # If not found try to get from student level
        if initial_level is None:
            try:
                student_level = StudentLevel.objects.filter(student=obj.student).first()
                if (student_level and student_level.progression_history and 
                    len(student_level.progression_history) > 0):
                    # Find the history entry for this assessment
                    for entry in student_level.progression_history:
                        if entry.get("assessment_id") == obj.id:
                            initial_level = entry.get("from_level")
                            break
            except Exception as e:
                print(f"Error getting student level: {e}")
        
        return initial_level
    
    def get_final_level(self, obj):
        """Get final level from detailed_results or student level"""
        # First try to get from detailed_results
        detailed_results = obj.detailed_results or {}
        final_level = detailed_results.get("final_level")
        
        # If not found try to get from student level
        if final_level is None:
            try:
                student_level = StudentLevel.objects.filter(student=obj.student).first()
                if student_level:
                    # If we have a student level use the current level as the final level
                    final_level = student_level.current_level
                    
                    # Also check progression history for this assessment
                    if (student_level.progression_history and 
                        len(student_level.progression_history) > 0):
                        for entry in student_level.progression_history:
                            if entry.get("assessment_id") == obj.id:
                                final_level = entry.get("to_level")
                                break
            except Exception as e:
                print(f"Error getting student level: {e}")
        
        return final_level
    
    def get_level_changed(self, obj):
        """Get level changed flag from detailed_results"""
        detailed_results = obj.detailed_results or {}
        return detailed_results.get("level_changed", False)
    
    def get_strengths(self, obj):
        """Get strengths from detailed_results"""
        detailed_results = obj.detailed_results or {}
        return detailed_results.get("strengths", [])
    
    def get_weaknesses(self, obj):
        """Get weaknesses from detailed_results"""
        detailed_results = obj.detailed_results or {}
        return detailed_results.get("weaknesses", [])
    
    def get_initial_level_display(self, obj):
        """Get display name for initial level"""
        initial_level = self.get_initial_level(obj)
        if initial_level is None:
            return None
        
        # First try to get from detailed_results
        detailed_results = obj.detailed_results or {}
        initial_level_display = detailed_results.get("initial_level_display")
        
        # If not found try to get from student level
        if initial_level_display is None:
            try:
                student_level = StudentLevel.objects.filter(student=obj.student).first()
                if (student_level and student_level.progression_history and 
                    len(student_level.progression_history) > 0):
                    # Find the history entry for this assessment
                    for entry in student_level.progression_history:
                        if entry.get("assessment_id") == obj.id:
                            initial_level_display = entry.get("from_level_display")
                            break
            except Exception as e:
                print(f"Error getting student level: {e}")
        
        # If still not found use the level choices
        if initial_level_display is None:
            level_choices = dict(StudentLevel.LEVEL_CHOICES)
            initial_level_display = level_choices.get(initial_level, "Unknown")
        
        return initial_level_display
    
    def get_final_level_display(self, obj):
        """Get display name for final level"""
        final_level = self.get_final_level(obj)
        if final_level is None:
            return None
        
        # First try to get from detailed_results
        detailed_results = obj.detailed_results or {}
        final_level_display = detailed_results.get("final_level_display")
        
        # If not found try to get from student level
        if final_level_display is None:
            try:
                student_level = StudentLevel.objects.filter(student=obj.student).first()
                if student_level:
                    # If we have a student level use the current level display as the final level display
                    final_level_display = student_level.current_level_display
                    
                    # Also check progression history for this assessment
                    if (student_level.progression_history and 
                        len(student_level.progression_history) > 0):
                        for entry in student_level.progression_history:
                            if entry.get("assessment_id") == obj.id:
                                final_level_display = entry.get("to_level_display")
                                break
            except Exception as e:
                print(f"Error getting student level: {e}")
        
        # If still not found use the level choices
        if final_level_display is None:
            level_choices = dict(StudentLevel.LEVEL_CHOICES)
            final_level_display = level_choices.get(final_level, "Unknown")
        
        return final_level_display


class AssessmentSubmissionSerializer(serializers.Serializer):
    question_id = serializers.IntegerField()
    answer = serializers.CharField()
    time_spent_seconds = serializers.IntegerField(required=False)


class InitialAssessmentSerializer(serializers.Serializer):
    category = serializers.CharField(required=False)
    difficulty_level = serializers.IntegerField(required=False, min_value=1, max_value=5)


class AssessmentAnalysisSerializer(serializers.Serializer):
    assessment_id = serializers.IntegerField()
    responses = serializers.ListField(child=serializers.DictField())
    feedback = serializers.DictField(required=False)
    recommendations = serializers.ListField(required=False)


class AIQuestionSuggestionSerializer(serializers.Serializer):
    category = serializers.CharField(required=False)
    difficulty_level = serializers.IntegerField(min_value=1, max_value=5)
    count = serializers.IntegerField(required=False, min_value=1, max_value=10)
    topic = serializers.CharField(required=False, allow_blank=True)


class AdminQuestionListSerializer(serializers.ModelSerializer):
    class Meta:
        model = AssessmentQuestion
        fields = "__all__"


class AIDecisionSerializer(serializers.ModelSerializer):
    """Serializer for AI assessment decisions"""
    student_name = serializers.CharField(source="student.username", read_only=True)
    decision_type_display = serializers.CharField(source="get_decision_type_display", read_only=True)
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    reviewer_name = serializers.CharField(source="reviewed_by.username", read_only=True)
    
    class Meta:
        model = AIAssessmentController
        fields = [
            "id", "decision_type", "decision_type_display", "original_decision", 
            "modified_decision", "status", "status_display", "student", "student_name", 
            "assessment", "confidence_score", "admin_notes", "requires_review", 
            "reviewed_by", "reviewer_name", "created_at", "updated_at"
        ]
        read_only_fields = ["created_at", "updated_at"]


class AISettingsSerializer(serializers.Serializer):
    """Serializer for AI assessment settings"""
    auto_approval_threshold = serializers.FloatField(min_value=0.0, max_value=1.0)
    review_threshold = serializers.FloatField(min_value=0.0, max_value=1.0)
    required_review_types = serializers.ListField(
        child=serializers.ChoiceField(choices=AIAssessmentController.DECISION_TYPES)
    )
    ai_settings = serializers.DictField()
    decision_weights = serializers.DictField()
    
    def validate(self, data):
        """Validate settings data"""
        if data["auto_approval_threshold"] <= data["review_threshold"]:
            raise serializers.ValidationError("Auto-approval threshold must be higher than review threshold")
        
        weights = data.get("decision_weights", {})
        if weights and abs(sum(weights.values()) - 1.0) > 0.001:
            raise serializers.ValidationError("Decision weights must sum to 1.0")
        
        return data


class StudentLevelSerializer(serializers.ModelSerializer):
    """Serializer for student level information"""
    level_name = serializers.CharField(source="get_current_level_display", read_only=True)
    next_level_requirements = serializers.SerializerMethodField()
    
    class Meta:
        model = StudentLevel
        fields = [
            "id", "student", "current_level", "level_name", "last_assessment_date", 
            "skill_strengths", "skill_weaknesses", "progression_history", 
            "next_level_requirements", "created_at", "updated_at"
        ]
        read_only_fields = ["level_name", "next_level_requirements", "created_at", "updated_at"]
    
    def get_next_level_requirements(self, obj):
        """Get requirements for next level"""
        return obj.get_next_level_requirements()


class LevelRequirementSerializer(serializers.ModelSerializer):
    class Meta:
        model = LevelRequirement
        fields = "__all__"


class SkillSerializer(serializers.ModelSerializer):
    """Serializer for the Skill model"""
    
    class Meta:
        model = Skill
        fields = ["id", "name", "description", "category", "created_at"]


class SkillWithAssessmentSerializer(serializers.ModelSerializer):
    """Serializer for skills with their assessment data"""
    assessment_data = serializers.SerializerMethodField()
    questions_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Skill
        fields = ["id", "name", "category", "description", "assessment_data", "questions_count"]
    
    def get_assessment_data(self, obj):
        """Get aggregated assessment data for this skill"""
        responses = AssessmentResponse.objects.filter(question__skills_assessed=obj)
        total_responses = responses.count()
        
        if total_responses == 0:
            return {"average_score": 0, "success_rate": 0, "total_attempts": 0}
        
        correct_responses = responses.filter(is_correct=True).count()
        avg_score = (responses.aggregate(
            avg_score=serializers.DecimalField(max_digits=5, decimal_places=2)(
                serializers.Avg("points_earned")
            )
        )["avg_score"] or 0)
        
        return {
            "average_score": float(avg_score),
            "success_rate": (correct_responses / total_responses) * 100,
            "total_attempts": total_responses
        }
    
    def get_questions_count(self, obj):
        """Get count of questions assessing this skill"""
        return obj.questions.count()


class LearningPathwaySerializer(serializers.ModelSerializer):
    required_skills = SkillSerializer(many=True, read_only=True)
    skills_developed = SkillSerializer(many=True, read_only=True)
    
    class Meta:
        model = LearningPathway
        fields = "__all__"


class ProgressionMilestoneSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProgressionMilestone
        fields = "__all__"


class StudentMilestoneSerializer(serializers.ModelSerializer):
    milestone_name = serializers.CharField(source="milestone.name", read_only=True)
    
    class Meta:
        model = StudentMilestone
        fields = [
            "id", "student", "milestone", "milestone_name", "date_achieved", 
            "is_completed", "verified_by", "evidence", "feedback"
        ]


class CompetencyBadgeSerializer(serializers.ModelSerializer):
    skills_demonstrated = SkillSerializer(many=True, read_only=True)
    
    class Meta:
        model = CompetencyBadge
        fields = "__all__"


class StudentBadgeSerializer(serializers.ModelSerializer):
    badge_details = CompetencyBadgeSerializer(source="badge", read_only=True)
    
    class Meta:
        model = StudentBadge
        fields = ["id", "student", "badge", "badge_details", "date_earned", "awarded_by", "evidence"]


class AssessmentSerializer(serializers.ModelSerializer):
    """Serializer for Assessment model"""
    questions = AssessmentQuestionSerializer(many=True, read_only=True)
    responses = AssessmentResponseSerializer(many=True, read_only=True)
    time_remaining = serializers.SerializerMethodField()
    skills_assessed = SkillSerializer(many=True, read_only=True)
    student_level = serializers.SerializerMethodField()
    initial_level = serializers.SerializerMethodField()
    final_level = serializers.SerializerMethodField()
    level_changed = serializers.SerializerMethodField()
    student_name = serializers.SerializerMethodField()
    student_id = serializers.SerializerMethodField()
    
    def get_student_id(self, obj):
        """Get student ID ensuring it's always available"""
        if obj.student:
            return obj.student.id
        return None
    
    def get_student_name(self, obj):
        """Get student name ensuring it's not 'undefined undefined'"""
        if not obj.student:
            return "Unknown Student"
        
        # Try to get full name using get_full_name method
        full_name = (obj.student.get_full_name() if callable(obj.student.get_full_name) 
                    else obj.student.get_full_name)
        
        # If full name is empty or contains 'undefined' build from first and last name
        if not full_name or "undefined" in full_name:
            first_name = getattr(obj.student, "first_name", "")
            last_name = getattr(obj.student, "last_name", "")
            
            # If both first and last name are empty use username or email
            if not first_name and not last_name:
                return (getattr(obj.student, "username", "") or 
                       getattr(obj.student, "email", "") or 
                       f"User ID: {obj.student.id}")
            
            # Otherwise combine first and last name
            return f"{first_name} {last_name}".strip()
        
        return full_name
    
    class Meta:
        model = Assessment
        fields = [
            "id", "title", "description", "assessment_type", "questions", "responses", 
            "skills_assessed", "start_time", "end_time", "status", "score", "skill_scores", 
            "completed", "time_remaining", "created_at", "updated_at", "student_level", 
            "initial_level", "final_level", "level_changed", "student_id", "student_name"
        ]
        read_only_fields = [
            "start_time", "end_time", "status", "score", "skill_scores", "completed", 
            "created_at", "updated_at", "student_level", "initial_level", "final_level", 
            "level_changed"
        ]
    
    def get_time_remaining(self, obj):
        """Calculate remaining time for assessment"""
        if not obj.start_time or obj.completed:
            return None
        
        settings = AssessmentSettings.objects.filter(assessment_type=obj.assessment_type).first()
        if not settings:
            return None
        
        elapsed = timezone.now() - obj.start_time
        remaining = settings.time_limit_minutes * 60 - elapsed.total_seconds()
        return max(0, int(remaining))
    
    def get_student_level(self, obj):
        """Get student level information"""
        try:
            if not obj.student:
                return None
                
            student_level = StudentLevel.objects.filter(student=obj.student).first()
            if not student_level:
                return {"current_level": 1, "current_level_display": "Beginner"}
            
            return {
                "current_level": student_level.current_level,
                "current_level_display": student_level.current_level_display,
                "last_assessment_date": (student_level.last_assessment_date.isoformat() 
                                       if student_level.last_assessment_date else None)
            }
        except Exception as e:
            print(f"Error getting student level: {e}")
            return None
    
    def get_initial_level(self, obj):
        """Get initial level from detailed_results or student level"""
        # First try to get from detailed_results
        detailed_results = obj.detailed_results or {}
        initial_level = detailed_results.get("initial_level")
        
        # If not found try to get from student level
        if initial_level is None:
            try:
                student_level = StudentLevel.objects.filter(student=obj.student).first()
                if (student_level and student_level.progression_history and 
                    len(student_level.progression_history) > 0):
                    # Find the history entry for this assessment
                    for entry in student_level.progression_history:
                        if entry.get("assessment_id") == obj.id:
                            initial_level = entry.get("from_level")
                            break
            except Exception as e:
                print(f"Error getting student level: {e}")
        
        return initial_level
    
    def get_final_level(self, obj):
        """Get final level from detailed_results or student level"""
        # First try to get from detailed_results
        detailed_results = obj.detailed_results or {}
        final_level = detailed_results.get("final_level")
        
        # If not found try to get from student level
        if final_level is None:
            try:
                student_level = StudentLevel.objects.filter(student=obj.student).first()
                if student_level:
                    # If we have a student level use the current level as the final level
                    final_level = student_level.current_level
            except Exception as e:
                print(f"Error getting student level: {e}")
        
        return final_level
    
    def get_level_changed(self, obj):
        """Get level changed flag from detailed_results"""
        detailed_results = obj.detailed_results or {}
        return detailed_results.get("level_changed", False)


class AssessmentSubmitSerializer(serializers.Serializer):
    question_id = serializers.IntegerField()
    answer = serializers.CharField()
    time_taken = serializers.IntegerField(required=False)


class AssessmentSettingsSerializer(serializers.ModelSerializer):
    """Serializer for assessment settings"""
    
    class Meta:
        model = AssessmentSettings
        fields = [
            "id", "assessment_type", "questions_per_assessment", "time_limit_minutes", 
            "passing_score", "allow_retakes", "retake_cooldown_days", "show_correct_answers", 
            "randomize_questions", "adaptive_difficulty"
        ]


class PathwayProgressSerializer(serializers.ModelSerializer):
    """Serializer for tracking student progress in learning pathways"""
    student_name = serializers.CharField(source="student.get_full_name", read_only=True)
    learning_path_name = serializers.CharField(source="learning_path.name", read_only=True)
    current_milestone_name = serializers.CharField(source="current_milestone.name", read_only=True, allow_null=True)
    completed_milestones_count = serializers.SerializerMethodField()
    unified_progress_id = serializers.PrimaryKeyRelatedField(source="unified_progress", read_only=True, allow_null=True)
    
    class Meta:
        model = PathwayProgress
        fields = [
            "id", "student", "student_name", "learning_path", "learning_path_name", 
            "start_date", "completion_date", "is_completed", "progress_percentage", 
            "current_milestone", "current_milestone_name", "completed_milestones_count", 
            "last_activity_date", "notes", "unified_progress_id"
        ]
        read_only_fields = [
            "start_date", "completion_date", "progress_percentage", "completed_milestones_count", 
            "last_activity_date", "is_completed", "unified_progress_id"
        ]
    
    def get_completed_milestones_count(self, obj):
        """Get count of completed milestones"""
        return obj.completed_milestones.count()


# Keep StudentProgressTrackingSerializer as an alias for backward compatibility
StudentProgressTrackingSerializer = PathwayProgressSerializer


class SkillGapSerializer(serializers.ModelSerializer):
    """Serializer for the SkillGap model"""
    skill = SkillSerializer(read_only=True)
    
    class Meta:
        model = SkillGap
        fields = [
            "id", "student", "skill", "current_level", "target_level", "gap_size", 
            "priority", "identified_date", "target_date", "status", "recommendations", 
            "progress_notes"
        ]
        read_only_fields = ["identified_date"]
    
    def validate(self, data):
        """Validate the skill gap data"""
        if data.get("current_level", 0) >= data.get("target_level", 0):
            raise serializers.ValidationError("Target level must be higher than current level")
        return data


class LearningAnalyticsSerializer(serializers.ModelSerializer):
    """Serializer for LearningAnalytics model"""
    student = serializers.PrimaryKeyRelatedField(read_only=True)
    skill_progress = serializers.JSONField(read_only=True)
    learning_patterns = serializers.JSONField(read_only=True)
    engagement_metrics = serializers.JSONField(read_only=True)
    time_analytics = serializers.JSONField(read_only=True)
    assessment_analytics = serializers.JSONField(read_only=True)
    
    class Meta:
        model = LearningAnalytics
        fields = [
            "id", "student", "skill_progress", "learning_patterns", "engagement_metrics", 
            "time_analytics", "assessment_analytics", "generated_date", "analysis_period_start", 
            "analysis_period_end", "recommendations"
        ]
        read_only_fields = ["generated_date", "analysis_period_start", "analysis_period_end"]
    
    def validate(self, data):
        """Validate analytics data"""
        if data.get("analysis_period_end") and data.get("analysis_period_start"):
            if data["analysis_period_end"] <= data["analysis_period_start"]:
                raise serializers.ValidationError("Analysis period end must be after start date")
        return data


class ProgressReportSerializer(serializers.ModelSerializer):
    """Serializer for progress reports"""
    student_name = serializers.CharField(source="student.get_full_name", read_only=True)
    
    class Meta:
        model = ProgressReport
        fields = [
            "id", "student", "student_name", "report_type", "report_period_start", 
            "report_period_end", "skill_progress", "assessment_scores", "completion_rate", 
            "engagement_metrics", "recommendations", "generated_at"
        ]
        read_only_fields = ["generated_at"]
    
    def validate(self, data):
        """Validate report data"""
        if data.get("report_period_end") and data.get("report_period_start"):
            if data["report_period_end"] <= data["report_period_start"]:
                raise serializers.ValidationError("Report period end must be after start date")
        return data

# Generated by Django 4.2.7 on 2025-07-04 15:54

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="CourseGenerationRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "keywords",
                    models.TextField(
                        blank=True,
                        help_text="Comma-separated keywords for course content",
                        null=True,
                        verbose_name="Keywords",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Description of the desired course content",
                        null=True,
                        verbose_name="Description",
                    ),
                ),
                (
                    "difficulty_level",
                    models.CharField(
                        choices=[
                            ("beginner", "Beginner"),
                            ("intermediate", "Intermediate"),
                            ("advanced", "Advanced"),
                        ],
                        default="intermediate",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("PROCESSING", "Processing"),
                            ("COMPLETED", "Completed"),
                            ("FAILED", "Failed"),
                        ],
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                ("error_message", models.TextField(blank=True, null=True)),
                ("generated_content", models.JSONField(blank=True, default=dict)),
                ("content_sections", models.IntegerField(default=0)),
            ],
            options={
                "verbose_name": "Course Generation Request",
                "verbose_name_plural": "Course Generation Requests",
                "ordering": ["-created_at"],
            },
        ),
    ]

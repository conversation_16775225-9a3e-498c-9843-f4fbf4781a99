{
  "dashboard": {
    "title": "Professor Dash<PERSON>",
    "welcome": "Welcome back, Professor!",
    "subtitle": "{{courses}} courses • {{students}} students • {{assignments}} active assignments",
    "latestUpdates": "Latest updates from your courses",
    "totalStudents": "Total Students",
    "activeAssignments": "Active Assignments",
    "upcomingClasses": "Upcoming Classes",
    "avgAttendance": "Average Attendance",
      "courseOverview": "Course Overview",
      "recentActivity": "Recent Activity",
      "officeHours": "Office Hours",
      "announcements": "Announcements",
      "quickActions": "Quick Actions",
      "myCourses": "My Courses",
      "activeCourses": "Active Courses",
      "scheduleClass": "Schedule Class",
      "createAssignment": "Create Assignment",
      "takeAttendance": "Take Attendance",
      "gradeSubmissions": "Grade Submissions",
      "manageOfficeHours": "Manage Office Hours",
      "viewAllCourses": "View All Courses",
      "noRecentActivity": "No recent activity",
      "noOfficeHours": "No office hours scheduled",
      "noAnnouncements": "No announcements",
      "attendanceTrend": "Attendance Trend",
      "gradeDistribution": "Grade Distribution",
      "coursePerformance": "Course Performance",
      "assignmentStatus": "Assignment Status",
      "completed": "Completed",
      "inProgress": "In Progress",
      "pending": "Pending",
      "students": "students",
      "assignments": "assignments",
      "classes": "classes",
      "attendance": "attendance",
      "commonTasks": "Common tasks",
      "fetchError": "Failed to fetch dashboard data"
    }
  },
  "myCourses": "My Courses",
  "viewCourses": "View Courses",
  "officeHours": {
      "title": "Office Hours",
      "addNew": "Add Office Hours",
      "edit": "Edit",
      "delete": "Delete",
      "day": "Day",
      "startTime": "Start Time",
      "endTime": "End Time",
      "location": "Location",
      "online": "Online",
      "inPerson": "In Person",
      "notes": "Notes",
      "meetingLink": "Meeting Link",
      "activeNow": "Active Now",
      "todaysHours": "Today's Hours",
      "weeklySchedule": "Weekly Schedule",
      "noScheduled": "No office hours scheduled",
      "addFirst": "Add Your First Office Hours",
      "joinMeeting": "Join Meeting",
      "manage": "Manage Office Hours"
    },
    "announcements": {
      "title": "Course Announcements",
      "newAnnouncement": "New Announcement",
      "createNew": "Create New Announcement",
      "edit": "Edit Announcement",
      "selectCourse": "Select Course",
      "announcementTitle": "Announcement Title",
      "content": "Content",
      "course": "Course",
      "all": "All",
      "recent": "Recent",
      "important": "Important",
      "showRecent": "Show Recent",
      "showAll": "Show All",
      "noAnnouncements": "No Announcements Yet",
      "noRecentAnnouncements": "No recent announcements",
      "noImportantAnnouncements": "No important announcements",
      "createFirst": "Create First Announcement",
      "tryChangingFilter": "Try changing the filter or create a new announcement",
      "shareInformation": "Share important information with your students",
      "updateAnnouncement": "Update your course announcement",
      "new": "New",
      "timeAgo": {
        "justNow": "Just now",
        "minutesAgo": "{{count}}m ago",
        "hoursAgo": "{{count}}h ago",
        "daysAgo": "{{count}}d ago"
      }
    }
  },
  "courses": {
    "mathematics": "Mathematics",
    "physics": "Physics",
    "chemistry": "Chemistry",
    "biology": "Biology"
  },
  "common": {
    "save": "Save",
    "cancel": "Cancel",
    "edit": "Edit",
    "delete": "Delete",
    "create": "Create",
    "update": "Update",
    "close": "Close",
    "notifications": "Notifications",
    "moreOptions": "More Options",
    "loading": "Loading...",
    "error": "Error",
    "success": "Success",
    "required": "Required",
    "optional": "Optional",
    "title": "Title",
    "description": "Description",
    "location": "Location",
    "time": "Time",
    "date": "Date",
    "notes": "Notes",
    "actions": "Actions",
    "viewAll": "View All",
    "noData": "No data available",
    "refresh": "Refresh",
    "search": "Search",
    "filter": "Filter",
    "sort": "Sort",
    "export": "Export",
    "import": "Import",
    "settings": "Settings",
    "help": "Help",
    "about": "About",
    "contact": "Contact",
    "privacy": "Privacy",
    "terms": "Terms"
  },
  "days": {
    "monday": "Monday",
    "tuesday": "Tuesday",
    "wednesday": "Wednesday",
    "thursday": "Thursday",
    "friday": "Friday",
    "saturday": "Saturday",
    "sunday": "Sunday",
    "mon": "Mon",
    "tue": "Tue",
    "wed": "Wed",
    "thu": "Thu",
    "fri": "Fri",
    "sat": "Sat",
    "sun": "Sun"
  },
  "validation": {
    "required": "This field is required",
    "titleRequired": "Title is required",
    "contentRequired": "Content is required",
    "courseRequired": "Course selection is required",
    "locationRequired": "Location is required for in-person office hours",
    "meetingLinkRequired": "Meeting link is required for online office hours",
    "startTimeRequired": "Start time is required",
    "endTimeRequired": "End time is required",
    "dayRequired": "Day is required"
  }
}

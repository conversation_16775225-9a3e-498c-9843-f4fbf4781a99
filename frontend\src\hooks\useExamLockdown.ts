/**
 * React Hook for Exam Lockdown Verification
 * 
 * Provides lockdown validation, security monitoring, and violation detection.
 * Refuses to render exam content unless proper lockdown environment is detected.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import axiosInstance from '../config/axios';

interface LockdownEnvironment {
  is_seb: boolean;
  is_ldb: boolean;
  is_supported_browser: boolean;
  user_agent: string;
  handshake_token: string | null;
  lockdown_capable: boolean;
}

interface ExamSession {
  session_token: string;
  session_id: number;
  time_remaining_seconds: number;
  in_grace_period: boolean;
  grace_period_remaining: number;
  should_auto_submit: boolean;
  lockdown_enabled: boolean;
  current_time: string;
}

interface ViolationLog {
  type: string;
  timestamp: number;
  data?: any;
}

interface UseExamLockdownReturn {
  // Lockdown state
  isLockdownCapable: boolean;
  isLockdownActive: boolean;
  environment: LockdownEnvironment | null;
  handshakeToken: string | null;
  
  // Session state
  sessionData: ExamSession | null;
  timeRemaining: number;
  isInGracePeriod: boolean;
  shouldAutoSubmit: boolean;
  
  // Violation tracking
  violations: ViolationLog[];
  violationCount: number;
  
  // Loading states
  isLoading: boolean;
  isValidating: boolean;
  error: string | null;
  
  // Functions
  startExamSession: (assessmentId: number, lockdownConfig?: any) => Promise<boolean>;
  submitExamSession: (forceSubmit?: boolean) => Promise<boolean>;
  logViolation: (violationType: string, data?: any) => void;
  validateEnvironment: () => Promise<boolean>;
  
  // Component render guard
  canRenderExam: boolean;
}

export const useExamLockdown = (requireLockdown: boolean = true): UseExamLockdownReturn => {
  // State management
  const [environment, setEnvironment] = useState<LockdownEnvironment | null>(null);
  const [sessionData, setSessionData] = useState<ExamSession | null>(null);
  const [violations, setViolations] = useState<ViolationLog[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Refs for tracking
  const heartbeatInterval = useRef<NodeJS.Timeout | null>(null);
  const violationTimeouts = useRef<Set<NodeJS.Timeout>>(new Set());
  const lastFocusTime = useRef<number>(Date.now());
  const isMonitoring = useRef<boolean>(false);
  
  // Computed values
  const isLockdownCapable = environment?.lockdown_capable || false;
  const isLockdownActive = sessionData?.lockdown_enabled || false;
  const canRenderExam = !requireLockdown || (isLockdownCapable && environment?.handshake_token);
  const timeRemaining = sessionData?.time_remaining_seconds || 0;
  const isInGracePeriod = sessionData?.in_grace_period || false;
  const shouldAutoSubmit = sessionData?.should_auto_submit || false;
  
  /**
   * Validate lockdown environment
   */
  const validateEnvironment = useCallback(async (): Promise<boolean> => {
    setIsValidating(true);
    setError(null);
    
    try {
      const response = await axiosInstance.get('/api/v1/assessment/lockdown/validate/');
      
      if (response.data.success) {
        setEnvironment(response.data.environment);
        return response.data.environment.lockdown_capable;
      } else {
        setError('Failed to validate lockdown environment');
        return false;
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Environment validation failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsValidating(false);
    }
  }, []);
  
  /**
   * Start exam session with lockdown
   */
  const startExamSession = useCallback(async (
    assessmentId: number,
    lockdownConfig: any = {}
  ): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Validate environment first if lockdown is required
      if (requireLockdown) {
        const isValid = await validateEnvironment();
        if (!isValid) {
          throw new Error('Lockdown environment not valid');
        }
      }
      
      const response = await axiosInstance.post('/api/v1/assessment/session/start/', {
        assessment_id: assessmentId,
        lockdown_config: requireLockdown ? {
          ...lockdownConfig,
          browser_lockdown: true,
          disable_right_click: true,
          disable_copy_paste: true,
          disable_print: true,
          monitor_focus: true,
          detect_tab_switch: true
        } : {}
      });
      
      if (response.data.success) {
        setSessionData({
          session_token: response.data.session_token,
          session_id: response.data.session_id,
          time_remaining_seconds: response.data.duration_minutes * 60,
          in_grace_period: false,
          grace_period_remaining: 0,
          should_auto_submit: false,
          lockdown_enabled: !!response.data.lockdown_config,
          current_time: new Date().toISOString()
        });
        
        // Start monitoring if lockdown is active
        if (response.data.lockdown_config) {
          startSecurityMonitoring(response.data.session_token);
        }
        
        // Start heartbeat
        startHeartbeat(response.data.session_token);
        
        return true;
      } else {
        throw new Error(response.data.error || 'Failed to start exam session');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || 'Failed to start exam session';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [requireLockdown, validateEnvironment]);
  
  /**
   * Submit exam session
   */
  const submitExamSession = useCallback(async (forceSubmit: boolean = false): Promise<boolean> => {
    if (!sessionData) return false;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await axiosInstance.post(
        `/api/v1/assessment/session/${sessionData.session_token}/submit/`,
        { force_submit: forceSubmit }
      );
      
      if (response.data.success) {
        // Clean up monitoring
        stopSecurityMonitoring();
        setSessionData(null);
        return true;
      } else {
        throw new Error(response.data.error || 'Failed to submit exam');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Failed to submit exam';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [sessionData]);
  
  /**
   * Log security violation
   */
  const logViolation = useCallback((violationType: string, data: any = {}) => {
    if (!sessionData) return;
    
    const violation: ViolationLog = {
      type: violationType,
      timestamp: Date.now(),
      data
    };
    
    setViolations(prev => [...prev, violation]);
    
    // Send to server
    axiosInstance.post('/api/v1/assessment/lockdown/violation/', {
      session_token: sessionData.session_token,
      violation_type: violationType,
      violation_data: data,
      browser_info: {
        user_agent: navigator.userAgent,
        viewport: `${window.innerWidth}x${window.innerHeight}`,
        screen: `${screen.width}x${screen.height}`,
        timestamp: new Date().toISOString()
      },
      system_info: {
        platform: navigator.platform,
        language: navigator.language,
        online: navigator.onLine,
        cookie_enabled: navigator.cookieEnabled
      }
    }).catch(err => {
      console.error('Failed to log violation:', err);
    });
  }, [sessionData]);
  
  /**
   * Start security monitoring
   */
  const startSecurityMonitoring = useCallback((sessionToken: string) => {
    if (isMonitoring.current) return;
    isMonitoring.current = true;
    
    // Monitor tab switching / focus loss
    const handleVisibilityChange = () => {
      if (document.hidden) {
        lastFocusTime.current = Date.now();
        logViolation('FOCUS_LOSS', {
          event: 'visibility_change',
          timestamp: Date.now()
        });
      } else {
        const focusLostDuration = Date.now() - lastFocusTime.current;
        if (focusLostDuration > 1000) { // More than 1 second
          logViolation('BROWSER_SWITCH', {
            event: 'focus_regained',
            duration_ms: focusLostDuration,
            timestamp: Date.now()
          });
        }
      }
    };
    
    const handleFocusOut = () => {
      lastFocusTime.current = Date.now();
      logViolation('FOCUS_LOSS', {
        event: 'window_blur',
        timestamp: Date.now()
      });
    };
    
    const handleFocusIn = () => {
      const focusLostDuration = Date.now() - lastFocusTime.current;
      if (focusLostDuration > 1000) {
        logViolation('BROWSER_SWITCH', {
          event: 'window_focus',
          duration_ms: focusLostDuration,
          timestamp: Date.now()
        });
      }
    };
    
    // Monitor clipboard events
    const handleCopy = (e: ClipboardEvent) => {
      logViolation('COPY_PASTE', {
        event: 'copy',
        text_length: window.getSelection()?.toString().length || 0,
        timestamp: Date.now()
      });
    };
    
    const handlePaste = (e: ClipboardEvent) => {
      e.preventDefault();
      logViolation('COPY_PASTE', {
        event: 'paste_blocked',
        timestamp: Date.now()
      });
    };
    
    // Monitor right-click
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      logViolation('RIGHT_CLICK', {
        element: (e.target as Element)?.tagName,
        timestamp: Date.now()
      });
    };
    
    // Monitor print attempts
    const handleBeforePrint = () => {
      logViolation('SCREEN_CAPTURE', {
        event: 'print_attempt',
        timestamp: Date.now()
      });
    };
    
    // Monitor key combinations
    const handleKeyDown = (e: KeyboardEvent) => {
      // Block common problematic key combinations
      const blockedCombinations = [
        { ctrl: true, key: 'a' }, // Select all
        { ctrl: true, key: 'c' }, // Copy
        { ctrl: true, key: 'v' }, // Paste
        { ctrl: true, key: 'x' }, // Cut
        { ctrl: true, key: 's' }, // Save
        { ctrl: true, key: 'p' }, // Print
        { ctrl: true, key: 'f' }, // Find
        { ctrl: true, key: 'h' }, // History
        { ctrl: true, key: 'j' }, // Downloads
        { ctrl: true, key: 'k' }, // Search
        { ctrl: true, key: 'l' }, // Location bar
        { ctrl: true, key: 'n' }, // New window
        { ctrl: true, key: 't' }, // New tab
        { ctrl: true, key: 'w' }, // Close tab
        { ctrl: true, shift: true, key: 'i' }, // Dev tools
        { ctrl: true, shift: true, key: 'j' }, // Console
        { ctrl: true, shift: true, key: 'c' }, // Inspect
        { key: 'F12' }, // Dev tools
        { alt: true, key: 'Tab' }, // Alt+Tab
        { alt: true, key: 'F4' } // Alt+F4
      ];
      
      const isBlocked = blockedCombinations.some(combo => {
        if (combo.ctrl && !e.ctrlKey) return false;
        if (combo.alt && !e.altKey) return false;
        if (combo.shift && !e.shiftKey) return false;
        return combo.key.toLowerCase() === e.key.toLowerCase();
      });
      
      if (isBlocked) {
        e.preventDefault();
        e.stopPropagation();
        logViolation('KEYBOARD_SHORTCUT', {
          key: e.key,
          ctrl: e.ctrlKey,
          alt: e.altKey,
          shift: e.shiftKey,
          timestamp: Date.now()
        });
      }
    };
    
    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('blur', handleFocusOut);
    window.addEventListener('focus', handleFocusIn);
    document.addEventListener('copy', handleCopy);
    document.addEventListener('paste', handlePaste);
    document.addEventListener('contextmenu', handleContextMenu);
    window.addEventListener('beforeprint', handleBeforePrint);
    document.addEventListener('keydown', handleKeyDown);
    
    // Store cleanup function
    const cleanup = () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('blur', handleFocusOut);
      window.removeEventListener('focus', handleFocusIn);
      document.removeEventListener('copy', handleCopy);
      document.removeEventListener('paste', handlePaste);
      document.removeEventListener('contextmenu', handleContextMenu);
      window.removeEventListener('beforeprint', handleBeforePrint);
      document.removeEventListener('keydown', handleKeyDown);
      isMonitoring.current = false;
    };
    
    // Store cleanup for later use
    (window as any).__examLockdownCleanup = cleanup;
  }, [logViolation]);
  
  /**
   * Stop security monitoring
   */
  const stopSecurityMonitoring = useCallback(() => {
    if ((window as any).__examLockdownCleanup) {
      (window as any).__examLockdownCleanup();
      delete (window as any).__examLockdownCleanup;
    }
    
    if (heartbeatInterval.current) {
      clearInterval(heartbeatInterval.current);
      heartbeatInterval.current = null;
    }
    
    // Clear violation timeouts
    violationTimeouts.current.forEach(timeout => clearTimeout(timeout));
    violationTimeouts.current.clear();
  }, []);
  
  /**
   * Start heartbeat monitoring
   */
  const startHeartbeat = useCallback((sessionToken: string) => {
    if (heartbeatInterval.current) return;
    
    heartbeatInterval.current = setInterval(async () => {
      try {
        const response = await axiosInstance.post(
          `/api/v1/assessment/session/${sessionToken}/heartbeat/`
        );
        
        if (response.data.success) {
          setSessionData(prev => prev ? {
            ...prev,
            time_remaining_seconds: response.data.time_remaining_seconds,
            in_grace_period: response.data.in_grace_period,
            grace_period_remaining: response.data.grace_period_remaining,
            should_auto_submit: response.data.should_auto_submit,
            current_time: response.data.current_time
          } : null);
          
          // Auto-submit if time is up
          if (response.data.should_auto_submit) {
            await submitExamSession(true);
          }
        }
      } catch (err) {
        console.error('Heartbeat failed:', err);
      }
    }, 30000); // Every 30 seconds
  }, [submitExamSession]);
  
  // Initialize environment validation on mount
  useEffect(() => {
    if (requireLockdown) {
      validateEnvironment();
    }
  }, [requireLockdown, validateEnvironment]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopSecurityMonitoring();
    };
  }, [stopSecurityMonitoring]);
  
  return {
    // Lockdown state
    isLockdownCapable,
    isLockdownActive,
    environment,
    handshakeToken: environment?.handshake_token || null,
    
    // Session state
    sessionData,
    timeRemaining,
    isInGracePeriod,
    shouldAutoSubmit,
    
    // Violation tracking
    violations,
    violationCount: violations.length,
    
    // Loading states
    isLoading,
    isValidating,
    error,
    
    // Functions
    startExamSession,
    submitExamSession,
    logViolation,
    validateEnvironment,
    
    // Component render guard
    canRenderExam
  };
};

export default useExamLockdown;

"""
Models for Global Scaling and Internationalization

This module handles multi-region deployment, content localization,
and global infrastructure management.
"""

import json
from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType

from core.models import BaseModel


class Region(BaseModel):
    """Model representing different geographical regions"""
    
    REGION_TYPES = [
        ('COUNTRY', 'Country'),
        ('CONTINENT', 'Continent'),
        ('TIMEZONE', 'Timezone'),
        ('DATACENTER', 'Data Center Region'),
    ]
    
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10, unique=True)  # e.g., 'US', 'EU', 'APAC'
    region_type = models.CharField(max_length=20, choices=REGION_TYPES)
    
    # Geographical data
    timezone = models.CharField(max_length=50, default='UTC')
    currency_code = models.CharField(max_length=3, default='USD')  # ISO currency code
    
    # Infrastructure configuration
    primary_datacenter = models.CharField(max_length=100, blank=True)
    backup_datacenters = models.JSONField(default=list, blank=True)
    
    # CDN and performance
    cdn_endpoints = models.JSONField(default=list, blank=True)
    performance_metrics = models.JSONField(default=dict, blank=True)
    
    # Compliance and regulations
    data_residency_required = models.BooleanField(default=False)
    gdpr_compliant = models.BooleanField(default=False)
    compliance_notes = models.TextField(blank=True)
    
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "Region"
        verbose_name_plural = "Regions"
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.code})"


class Language(BaseModel):
    """Model for supported languages and localization"""
    
    name = models.CharField(max_length=100)  # e.g., "English", "Arabic"
    code = models.CharField(max_length=10, unique=True)  # e.g., 'en', 'ar', 'es'
    iso_code = models.CharField(max_length=5)  # e.g., 'en-US', 'ar-SA'
    
    # Text direction and formatting
    is_rtl = models.BooleanField(default=False)  # Right-to-left languages
    decimal_separator = models.CharField(max_length=1, default='.')
    thousand_separator = models.CharField(max_length=1, default=',')
    
    # Regional associations
    regions = models.ManyToManyField(Region, related_name='languages')
    
    # Translation status
    translation_completeness = models.FloatField(default=0.0)  # Percentage 0-100
    last_updated = models.DateTimeField(auto_now=True)
    
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False)
    
    class Meta:
        verbose_name = "Language"
        verbose_name_plural = "Languages"
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.code})"


class LocalizedContent(BaseModel):
    """Model for storing localized versions of content"""
    
    # Generic foreign key to any content model
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    
    language = models.ForeignKey(Language, on_delete=models.CASCADE)
    
    # Localized fields
    localized_data = models.JSONField(
        default=dict,
        help_text="JSON object containing localized field values"
    )
    
    # Translation metadata
    translation_status = models.CharField(
        max_length=20,
        choices=[
            ('PENDING', 'Pending Translation'),
            ('IN_PROGRESS', 'In Progress'),
            ('REVIEW', 'Under Review'),
            ('COMPLETED', 'Completed'),
            ('NEEDS_UPDATE', 'Needs Update'),
        ],
        default='PENDING'
    )
    
    translator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='translations'
    )
    
    quality_score = models.FloatField(default=0.0)  # AI-assessed quality 0-100
    review_notes = models.TextField(blank=True)
    
    class Meta:
        verbose_name = "Localized Content"
        verbose_name_plural = "Localized Content"
        unique_together = ['content_type', 'object_id', 'language']
        indexes = [
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['language', 'translation_status']),
        ]
    
    def __str__(self):
        return f"Localized content for {self.content_object} in {self.language.name}"


class GlobalConfiguration(BaseModel):
    """Model for global system configuration and feature flags"""
    
    CONFIG_TYPES = [
        ('FEATURE_FLAG', 'Feature Flag'),
        ('SCALING_RULE', 'Scaling Rule'),
        ('COMPLIANCE', 'Compliance Setting'),
        ('PERFORMANCE', 'Performance Configuration'),
        ('INTEGRATION', 'Third-party Integration'),
    ]
    
    name = models.CharField(max_length=100, unique=True)
    config_type = models.CharField(max_length=20, choices=CONFIG_TYPES)
    
    # Configuration data
    configuration = models.JSONField(default=dict)
    
    # Regional overrides
    regional_overrides = models.JSONField(
        default=dict,
        help_text="Region-specific configuration overrides"
    )
    
    # Activation controls
    is_active = models.BooleanField(default=True)
    regions = models.ManyToManyField(
        Region,
        blank=True,
        help_text="Regions where this configuration applies"
    )
    
    # Rollout controls
    rollout_percentage = models.FloatField(
        default=100.0,
        help_text="Percentage of users who see this configuration"
    )
    
    # Metadata
    description = models.TextField(blank=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='global_configs'
    )
    
    class Meta:
        verbose_name = "Global Configuration"
        verbose_name_plural = "Global Configurations"
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.get_config_type_display()})"
    
    def get_config_for_region(self, region_code):
        """Get configuration with regional overrides applied"""
        config = self.configuration.copy()
        
        if region_code in self.regional_overrides:
            config.update(self.regional_overrides[region_code])
        
        return config


class LoadBalancingRule(BaseModel):
    """Model for managing traffic distribution and load balancing"""
    
    ALGORITHM_CHOICES = [
        ('ROUND_ROBIN', 'Round Robin'),
        ('WEIGHTED', 'Weighted'),
        ('GEOLOCATION', 'Geolocation-based'),
        ('PERFORMANCE', 'Performance-based'),
        ('RANDOM', 'Random'),
    ]
    
    name = models.CharField(max_length=100)
    algorithm = models.CharField(max_length=20, choices=ALGORITHM_CHOICES)
    
    # Target configuration
    target_regions = models.ManyToManyField(Region, through='LoadBalancingTarget')
    
    # Rules and conditions
    conditions = models.JSONField(
        default=dict,
        help_text="Conditions for applying this load balancing rule"
    )
    
    # Traffic shaping
    max_requests_per_second = models.IntegerField(null=True, blank=True)
    failover_strategy = models.JSONField(default=dict)
    
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "Load Balancing Rule"
        verbose_name_plural = "Load Balancing Rules"
    
    def __str__(self):
        return f"{self.name} ({self.get_algorithm_display()})"


class LoadBalancingTarget(BaseModel):
    """Through model for load balancing targets with weights"""
    
    rule = models.ForeignKey(LoadBalancingRule, on_delete=models.CASCADE)
    region = models.ForeignKey(Region, on_delete=models.CASCADE)
    
    weight = models.IntegerField(default=100)  # Relative weight for traffic distribution
    is_active = models.BooleanField(default=True)
    
    # Health check configuration
    health_check_url = models.URLField(blank=True)
    health_check_interval = models.IntegerField(default=60)  # seconds
    
    # Performance metrics
    average_response_time = models.FloatField(null=True, blank=True)
    success_rate = models.FloatField(null=True, blank=True)
    
    class Meta:
        unique_together = ['rule', 'region']
    
    def __str__(self):
        return f"{self.rule.name} -> {self.region.name} (weight: {self.weight})"


class GlobalMetrics(BaseModel):
    """Model for storing global performance and usage metrics"""
    
    METRIC_TYPES = [
        ('PERFORMANCE', 'Performance Metric'),
        ('USAGE', 'Usage Metric'),
        ('ERROR', 'Error Metric'),
        ('BUSINESS', 'Business Metric'),
    ]
    
    metric_name = models.CharField(max_length=100)
    metric_type = models.CharField(max_length=20, choices=METRIC_TYPES)
    
    # Dimensional data
    region = models.ForeignKey(Region, on_delete=models.CASCADE, null=True, blank=True)
    timestamp = models.DateTimeField()
    
    # Metric values
    value = models.FloatField()
    unit = models.CharField(max_length=20, blank=True)  # e.g., 'ms', 'count', '%'
    
    # Additional context
    tags = models.JSONField(default=dict, blank=True)
    metadata = models.JSONField(default=dict, blank=True)
    
    class Meta:
        verbose_name = "Global Metric"
        verbose_name_plural = "Global Metrics"
        indexes = [
            models.Index(fields=['metric_name', 'timestamp']),
            models.Index(fields=['region', 'metric_type', 'timestamp']),
        ]
    
    def __str__(self):
        region_str = f" ({self.region.code})" if self.region else ""
        return f"{self.metric_name}: {self.value}{self.unit}{region_str}"


class ContentDeliveryNetwork(BaseModel):
    """Model for CDN configuration and management"""
    
    CDN_PROVIDERS = [
        ('CLOUDFLARE', 'Cloudflare'),
        ('AWS_CLOUDFRONT', 'AWS CloudFront'),
        ('AZURE_CDN', 'Azure CDN'),
        ('GOOGLE_CDN', 'Google Cloud CDN'),
        ('FASTLY', 'Fastly'),
        ('MAXCDN', 'MaxCDN'),
    ]
    
    name = models.CharField(max_length=100)
    provider = models.CharField(max_length=20, choices=CDN_PROVIDERS)
    
    # Configuration
    primary_domain = models.CharField(max_length=200)
    alternative_domains = models.JSONField(default=list, blank=True)
    
    # Geographic distribution
    edge_locations = models.JSONField(default=list, blank=True)
    regions = models.ManyToManyField(Region, related_name='cdn_networks')
    
    # Caching configuration
    cache_rules = models.JSONField(default=dict, blank=True)
    ttl_settings = models.JSONField(default=dict, blank=True)
    
    # Security settings
    ssl_configuration = models.JSONField(default=dict, blank=True)
    security_rules = models.JSONField(default=dict, blank=True)
    
    # Performance metrics
    cache_hit_ratio = models.FloatField(null=True, blank=True)
    average_response_time = models.FloatField(null=True, blank=True)
    
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "Content Delivery Network"
        verbose_name_plural = "Content Delivery Networks"
    
    def __str__(self):
        return f"{self.name} ({self.get_provider_display()})"


class RegionalCompliance(BaseModel):
    """Model for managing regional compliance requirements"""
    
    COMPLIANCE_TYPES = [
        ('GDPR', 'General Data Protection Regulation'),
        ('CCPA', 'California Consumer Privacy Act'),
        ('PIPEDA', 'Personal Information Protection and Electronic Documents Act'),
        ('LGPD', 'Lei Geral de Proteção de Dados'),
        ('PDPA', 'Personal Data Protection Act'),
        ('CUSTOM', 'Custom Compliance Requirement'),
    ]
    
    region = models.ForeignKey(Region, on_delete=models.CASCADE)
    compliance_type = models.CharField(max_length=20, choices=COMPLIANCE_TYPES)
    
    # Compliance details
    requirements = models.JSONField(default=dict)
    implementation_status = models.CharField(
        max_length=20,
        choices=[
            ('NOT_STARTED', 'Not Started'),
            ('IN_PROGRESS', 'In Progress'),
            ('IMPLEMENTED', 'Implemented'),
            ('AUDITED', 'Audited'),
        ],
        default='NOT_STARTED'
    )
    
    # Documentation
    documentation_url = models.URLField(blank=True)
    audit_notes = models.TextField(blank=True)
    last_audit_date = models.DateField(null=True, blank=True)
    next_audit_date = models.DateField(null=True, blank=True)
    
    # Contact information
    compliance_officer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "Regional Compliance"
        verbose_name_plural = "Regional Compliance Requirements"
        unique_together = ['region', 'compliance_type']
    
    def __str__(self):
        return f"{self.region.name} - {self.get_compliance_type_display()}"

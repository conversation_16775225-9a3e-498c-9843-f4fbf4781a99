"""ExamSecurityServiceProvidesserver-sidesecurityfeaturesforexams:-Server-verifiedcountdowntimer-Graceperiodmanagement-Auto-submitfunctionality-Lockdownconfiguration-SafeExamBrowserintegration"""import jsonimportuuidimportsecretsfromdatetimeimport datetimetimedeltatimezonefromtypingimportDictAnyOptionalTuplefrom django.confimportsettingsfrom django.utilsimport timezoneasdjango_timezonefrom django.core.cacheimportcachefrom django.dbimporttransactionfrom.modelsimportExamSessionLockdownViolationAssessmentAssessmentResponsefrom users.modelsimportCustomUserasUserimport logginglogger=logging.getLogger(__name__)classExamTimerService:"""Serviceformanagingexamtimerswithserververification"""def__init__(self):self.grace_period_seconds=300#5minutesself.heartbeat_interval=30#30secondsdefstart_exam_session(selfstudent:Userassessment:Assessmentlockdown_config:Optional[Dict]=None)->Dict[strAny]:"""Startanewexamsessionwithtimerandlockdownconfiguration"""try:withtransaction.atomic():#Checkifstudentalreadyhasanactivesessionexisting_session=ExamSession.objects.filter(student=studentassessment=assessmentend_time__isnull=True).first()ifexisting_session:return{'success':False'error':'Activeexamsessionalreadyexists''session_id':str(existing_session.id)}#Createnewexamsessionstart_time=django_timezone.now()duration_minutes=assessment.time_limitor120#Default2hoursscheduled_end_time=start_time+timedelta(minutes=duration_minutes)#Generatesecuresessiontokensession_token=self._generate_session_token()#Createsessionexam_session=ExamSession.objects.create(assessment=assessmentstudent=studentstart_time=start_timescheduled_end_time=scheduled_end_timesession_token=session_tokenlockdown_enabled=bool(lockdown_config)lockdown_config=lockdown_configor{}proctoring_enabled=assessment.proctoring_requiredlockdown_status='HARD'iflockdown_configelse'NONE')#Storesessiondataincacheforquickaccesscache_key=f"exam_session_{session_token}"session_data={'session_id':exam_session.id'student_id':student.id'assessment_id':assessment.id'start_time':start_time.isoformat()'scheduled_end_time':scheduled_end_time.isoformat()'duration_minutes':duration_minutes'lockdown_enabled':exam_session.lockdown_enabled'grace_period_used':False'warning_sent':False}cache.set(cache_keysession_datatimeout=duration_minutes*60+600)#Logsessionstartexam_session.log_event('SESSION_STARTED'{'start_time':start_time.isoformat()'duration_minutes':duration_minutes'lockdown_enabled':exam_session.lockdown_enabled})return{'success':True'session_token':session_token'session_id':exam_session.id'start_time':start_time.isoformat()'scheduled_end_time':scheduled_end_time.isoformat()'duration_minutes':duration_minutes'grace_period_seconds':self.grace_period_seconds'heartbeat_interval':self.heartbeat_interval'lockdown_config':exam_session.lockdown_config}exceptExceptionase:logger.error(f"Failedtostartexamsession:{str(e)}")return{'success':False'error':'Failedtostartexamsession'}defget_session_status(selfsession_token:str)->Dict[strAny]:"""Getcurrentsessionstatuswithtimeremaining"""try:cache_key=f"exam_session_{session_token}"session_data=cache.get(cache_key)ifnotsession_data:#Trytoloadfromdatabaseexam_session=ExamSession.objects.filter(session_token=session_tokenend_time__isnull=True).first()ifnotexam_session:return{'success':False'error':'Sessionnotfoundorexpired'}#Rebuildcachedatasession_data={'session_id':exam_session.id'student_id':exam_session.student.id'assessment_id':exam_session.assessment.id'start_time':exam_session.start_time.isoformat()'scheduled_end_time':exam_session.scheduled_end_time.isoformat()'duration_minutes':(exam_session.scheduled_end_time-exam_session.start_time).total_seconds()/60'lockdown_enabled':exam_session.lockdown_enabled'grace_period_used':False'warning_sent':False}cache.set(cache_keysession_datatimeout=3600)current_time=django_timezone.now()scheduled_end=datetime.fromisoformat(session_data['scheduled_end_time'].replace('Z''+00:00'))#Calculatetimeremainingtime_remaining=(scheduled_end-current_time).total_seconds()#Checkifingraceperiodin_grace_period=time_remaining<0andabs(time_remaining)<=self.grace_period_seconds#Checkifsessionshouldauto-expireiftime_remaining<-self.grace_period_seconds:returnself._auto_submit_session(session_token)#Sendwarningiflessthan5minutesremainingiftime_remaining<=300andnotsession_data.get('warning_sent'):session_data['warning_sent']=Truecache.set(cache_keysession_datatimeout=3600)exam_session=ExamSession.objects.get(id=session_data['session_id'])exam_session.log_event('TIME_WARNING'{'time_remaining_seconds':time_remaining'warning_type':'5_minute'})return{'success':True'session_id':session_data['session_id']'time_remaining_seconds':max(0time_remaining)'in_grace_period':in_grace_period'grace_period_remaining':max(0self.grace_period_seconds+time_remaining)ifin_grace_periodelse0'should_auto_submit':time_remaining<=0'lockdown_enabled':session_data['lockdown_enabled']'current_time':current_time.isoformat()}exceptExceptionase:logger.error(f"Failedtogetsessionstatus:{str(e)}")return{'success':False'error':'Failedtoretrievesessionstatus'}defsubmit_exam_session(selfsession_token:strforce_submit:bool=False)->Dict[strAny]:"""Submitexamsession(manualorauto-submit)"""try:withtransaction.atomic():exam_session=ExamSession.objects.filter(session_token=session_tokenend_time__isnull=True).first()ifnotexam_session:return{'success':False'error':'Sessionnotfoundoralreadysubmitted'}current_time=django_timezone.now()#Calculateactualdurationactual_duration=current_time-exam_session.start_time#Updatesessionexam_session.end_time=current_timeexam_session.actual_duration=actual_duration#Determinesubmissiontypescheduled_end=exam_session.scheduled_end_timetime_remaining=(scheduled_end-current_time).total_seconds()ifforce_submitortime_remaining<=-self.grace_period_seconds:submission_type='auto_submit'eliftime_remaining<=0:submission_type='grace_period_submit'else:submission_type='manual_submit'#Logsubmissionexam_session.log_event('SESSION_SUBMITTED'{'submission_type':submission_type'actual_duration_minutes':actual_duration.total_seconds()/60'time_remaining_seconds':time_remaining'forced':force_submit})exam_session.save()#Clearcachecache_key=f"exam_session_{session_token}"cache.delete(cache_key)return{'success':True'session_id':exam_session.id'submission_type':submission_type'actual_duration_minutes':actual_duration.total_seconds()/60'submitted_at':current_time.isoformat()}exceptExceptionase:logger.error(f"Failedtosubmitexamsession:{str(e)}")return{'success':False'error':'Failedtosubmitexamsession'}defheartbeat(selfsession_token:str)->Dict[strAny]:"""Processheartbeattoverifysessionisstillactive"""status=self.get_session_status(session_token)ifstatus['success']:#Updatelastactivitycache_key=f"exam_session_{session_token}_activity"cache.set(cache_keydjango_timezone.now().isoformat()timeout=300)#Logheartbeattry:exam_session=ExamSession.objects.get(id=status['session_id'])exam_session.log_event('HEARTBEAT'{'timestamp':django_timezone.now().isoformat()})exceptExamSession.DoesNotExist:passreturnstatusdef_auto_submit_session(selfsession_token:str)->Dict[strAny]:"""Auto-submitasessionthathasexceededgraceperiod"""returnself.submit_exam_session(session_tokenforce_submit=True)def_generate_session_token(self)->str:"""Generateasecuresessiontoken"""returnsecrets.token_urlsafe(32)classLockdownService:"""Serviceformanagingexamlockdownfeatures"""def__init__(self):self.violation_weights={'APP_SWITCH':3'BROWSER_SWITCH':4'UNAUTHORIZED_APP':5'COPY_PASTE':2'RIGHT_CLICK':1'DEVELOPER_TOOLS':5'SCREEN_CAPTURE':5'MULTIPLE_MONITORS':3'VIRTUAL_MACHINE':5'REMOTE_ACCESS':5'NETWORK_CHANGE':3'FULLSCREEN_EXIT':4'FOCUS_LOSS':2'KEYBOARD_SHORTCUT':2'MOUSE_LEAVE':1'CAMERA_BLOCKED':4'MICROPHONE_BLOCKED':4'OTHER':2}deflog_violation(selfsession_token:strviolation_type:strviolation_data:Dict=Nonebrowser_info:Dict=Nonesystem_info:Dict=None)->Dict[strAny]:"""Logalockdownviolation"""try:exam_session=ExamSession.objects.filter(session_token=session_tokenend_time__isnull=True).first()ifnotexam_session:return{'success':False'error':'Sessionnotfound'}#Determineseveritybasedonviolationtypeandhistoryseverity=self._calculate_violation_severity(exam_sessionviolation_typeviolation_data)#Createviolationrecordviolation=LockdownViolation.objects.create(exam_session=exam_sessionviolation_type=violation_typeseverity=severityviolation_data=violation_dataor{}browser_info=browser_infoor{}system_info=system_infoor{}auto_response=self._determine_auto_response(severityviolation_type))#Logtoexamsessionexam_session.log_event('LOCKDOWN_VIOLATION'{'violation_id':violation.id'violation_type':violation_type'severity':severity'timestamp':violation.timestamp.isoformat()})#Checkifsessionshouldbeterminatedviolation_count=exam_session.lockdown_violations.count()critical_violations=exam_session.lockdown_violations.filter(severity='CRITICAL').count()should_terminate=(critical_violations>=3orviolation_count>=10orviolation_typein['VIRTUAL_MACHINE''REMOTE_ACCESS'])ifshould_terminate:self._terminate_session_for_violations(exam_session)return{'success':True'violation_id':violation.id'severity':severity'auto_response':violation.auto_response'should_terminate':should_terminate'total_violations':violation_count}exceptExceptionase:logger.error(f"Failedtologviolation:{str(e)}")return{'success':False'error':'Failedtologviolation'}def_calculate_violation_severity(selfexam_session:ExamSessionviolation_type:strviolation_data:Dict=None)->str:"""Calculateviolationseveritybasedontypeandhistory"""base_weight=self.violation_weights.get(violation_type2)#Getrecentviolationsofsametyperecent_violations=exam_session.lockdown_violations.filter(violation_type=violation_typetimestamp__gte=django_timezone.now()-timedelta(minutes=10)).count()#Escalatebasedonrepetitionescalated_weight=base_weight+recent_violations#Determineseverityifescalated_weight>=7:return'CRITICAL'elifescalated_weight>=5:return'MAJOR'elifescalated_weight>=3:return'MINOR'elifescalated_weight>=2:return'WARNING'else:return'INFO'def_determine_auto_response(selfseverity:strviolation_type:str)->str:"""Determineautomaticresponsetoviolation"""ifseverity=='CRITICAL':return'Sessionterminationwarningissued'elifseverity=='MAJOR':return'Majorviolationwarningdisplayed'elifseverity=='MINOR':return'Minorviolationloggedandwarned'elifviolation_typein['DEVELOPER_TOOLS''SCREEN_CAPTURE']:return'Securitywarningdisplayed'else:return'Violationlogged'def_terminate_session_for_violations(selfexam_session:ExamSession):"""Terminatesessionduetoexcessiveviolations"""exam_session.lockdown_status='BREACHED'exam_session.end_time=django_timezone.now()exam_session.log_event('SESSION_TERMINATED'{'reason':'lockdown_violations''violation_count':exam_session.lockdown_violations.count()})exam_session.save()classSafeExamBrowserService:"""ServiceforgeneratingSafeExamBrowserconfiguration"""defgenerate_seb_config(selfassessment:Assessmentexam_session:ExamSession)->Dict[strAny]:"""GenerateSafeExamBrowserconfigurationfile"""config={"sebConfigPurpose":1#Startinganexam"sebServicePolicy":1#ForceuseofSEBservice"sebServerURL":f"{settings.FRONTEND_URL}/exam/{exam_session.session_token}""startURL":f"{settings.FRONTEND_URL}/exam/{exam_session.session_token}""quitURL":f"{settings.FRONTEND_URL}/exam/complete"#Examsettings"examSessionClearCookiesOnStart":True"examSessionClearCookiesOnEnd":True"browserWindowAllowReload":False"browserWindowShowURL":False#Securitysettings"allowQuit":False"ignoreExitKeys":True"enableAltEsc":False"enableAltF4":False"enableAltTab":False"enableCtrlAltDel":False"enableF1":False"enableF2":False"enableF3":False"enableF4":False"enableF5":False"enableF6":False"enableF7":False"enableF8":False"enableF9":False"enableF10":False"enableF11":False"enableF12":False"enableEsc":False"enablePrintScreen":False"enableRightMouse":False#Networksettings"proxies":{}"URLFilterEnable":True"URLFilterEnableContentFilter":True"blacklistURLFilter":[".*google.*"".*bing.*"".*yahoo.*"".*wikipedia.*"".*stackoverflow.*"".*github.*"]"whitelistURLFilter":[f"{settings.FRONTEND_URL}/*"f"{settings.BACKEND_URL}/*"]#Monitoring"allowVideoCapture":assessment.proctoring_required"allowAudioCapture":assessment.proctoring_required"detectVirtualMachine":True"forceAppFolderInstall":True#Applicationrestrictions"prohibitedProcesses":[{"active":True"currentUser":True"description":"Calculator""executable":"calc.exe""identifier":"calculator""originalName":"CALC.EXE""windowHandling":1}{"active":True"currentUser":True"description":"TaskManager""executable":"taskmgr.exe""identifier":"taskmanager""originalName":"TASKMGR.EXE""windowHandling":1}]#Browsersettings"newBrowserWindowByLinkBlockForeign":True"newBrowserWindowByScriptBlockForeign":True"browserWindowAllowAddressBar":False"browserWindowAllowNavigationBar":False"browserWindowAllowDeveloperConsole":False#Customheadersforauthentication"sendBrowserExamKey":True"browserExamKey":exam_session.session_token#Timersettings"examTimeLimit":assessment.time_limit*60ifassessment.time_limitelse7200#seconds"showTimeLeft":True#Metadata"originatorVersion":"3.3.0""sebVersion":"3.3.0""examID":str(assessment.id)"sessionID":str(exam_session.id)"institutionName":"NorthStarUniversity""examName":assessment.title}returnconfigdefgenerate_respondus_ldb_link(selfassessment:Assessmentexam_session:ExamSession)->str:"""GenerateRespondusLockDownBrowserdeeplink"""base_url="respondus://lockdown"params={"url":f"{settings.FRONTEND_URL}/exam/{exam_session.session_token}""title":assessment.title"id":str(assessment.id)"session":exam_session.session_token"time_limit":assessment.time_limitor120"password":self._generate_ldb_password()}param_string="&".join([f"{k}={v}"forkvinparams.items()])returnf"{base_url}?{param_string}"def_generate_ldb_password(self)->str:"""GenerateapasswordforLockDownBrowsersession"""returnsecrets.token_hex(16)#Initializeservicesexam_timer_service=ExamTimerService()lockdown_service=LockdownService()seb_service=SafeExamBrowserService()
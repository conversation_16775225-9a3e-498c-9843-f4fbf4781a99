fromdjango.contrib.authimportget_user_modelfromdjango.core.management.baseimportBaseCommandfromdjango.utilsimporttimezonefromcourses.modelsimportCourseDepartmentUser=get_user_model()classCommand(BaseCommand):help="Createsamplecoursesfortesting"defhandle(self*args**options):self.stdout.write("🎓Creatingsamplecoursesanddepartments...")#Firstcreatesampledepartmentsdepartments_data=[{"name":"ComputerScience""code":"CS"}{"name":"Mathematics""code":"MATH"}{"name":"Biology""code":"BIO"}{"name":"English""code":"ENG"}{"name":"Chemistry""code":"CHEM"}{"name":"Business""code":"BUS"}]departments={}fordept_dataindepartments_data:deptcreated=Department.objects.get_or_create(code=dept_data["code"]defaults={"name":dept_data["name"]})departments[dept_data["code"]]=deptifcreated:self.stdout.write(f"✅Createddepartment:{dept.name}")#Createsampleinstructorifnoneexistsinstructor=Nonetry:instructor=User.objects.filter(role="PROFESSOR").first()ifnotinstructor:instructor=User.objects.create_user(username="sample_professor"email="<EMAIL>"password="temp123"role="PROFESSOR"first_name="Sample"last_name="Professor")self.stdout.write("✅Createdsampleprofessor")exceptExceptionase:self.stdout.write(f"⚠️Couldnotcreateinstructor:{e}")courses_data=[{"title":"IntroductiontoProgramming""course_code":"CS101""description":"LearnPythonprogrammingfundamentalswithhands-onprojectsandreal-worldapplications""credits":3"department":departments.get("CS")"instructor":instructor"is_active":True"is_published":True"semester":"FALL""level":1}{"title":"CalculusI:LimitsandDerivatives""course_code":"MATH101""description":"Masterdifferentialcalculusconceptsincludinglimitsderivativesandtheirapplications""credits":4"department":departments.get("MATH")"instructor":instructor"is_active":True"is_published":True"semester":"FALL""level":1}{"title":"Biology101:IntroductiontoLifeSciences""course_code":"BIO101""description":"Explorethefundamentalsofbiologicalsciencesfromcellularbiologytoecosystems""credits":3"department":departments.get("BIO")"instructor":instructor"is_active":True"is_published":True"semester":"FALL""level":1}{"title":"EnglishCompositionandWriting""course_code":"ENG101""description":"Developacademicwritingandcommunicationskillsforprofessionalsuccess""credits":3"department":departments.get("ENG")"instructor":instructor"is_active":True"is_published":True"semester":"FALL""level":1}{"title":"DataStructuresandAlgorithms""course_code":"CS201""description":"Computersciencefundamentalsincludingarraystreesgraphsandalgorithmicthinking""credits":4"department":departments.get("CS")"instructor":instructor"is_active":True"is_published":True"semester":"SPRING""level":2}{"title":"ChemistryforEngineers""course_code":"CHEM101""description":"Appliedchemistryconceptsspecificallydesignedforengineeringstudents""credits":3"department":departments.get("CHEM")"instructor":instructor"is_active":True"is_published":True"semester":"FALL""level":1}{"title":"LinearAlgebra""course_code":"MATH201""description":"Vectorspacesmatricesandlineartransformationswithpracticalapplications""credits":3"department":departments.get("MATH")"instructor":instructor"is_active":True"is_published":True"semester":"SPRING""level":2}{"title":"ProfessionalCommunication""course_code":"BUS101""description":"Workplacecommunicationandpresentationskillsforcareeradvancement""credits":2"department":departments.get("BUS")"instructor":instructor"is_active":True"is_published":True"semester":"FALL""level":1}]created_count=0existing_count=0forcourse_dataincourses_data:try:coursecreated=Course.objects.get_or_create(course_code=course_data["course_code"]defaults=course_data)ifcreated:created_count+=1self.stdout.write(self.style.SUCCESS(f"✅Created:{course.title}({course.course_code})"))else:existing_count+=1self.stdout.write(self.style.WARNING(f"⚠️Alreadyexists:{course.title}({course.course_code})"))exceptExceptionase:self.stdout.write(self.style.ERROR(f'❌Errorcreatingcourse{course_data["course_code"]}:{e}'))total_courses=Course.objects.count()self.stdout.write("\n📊SUMMARY:")self.stdout.write(f"Created:{created_count}courses")self.stdout.write(f"Existing:{existing_count}courses")self.stdout.write(f"Totalindatabase:{total_courses}courses")ifcreated_count>0:self.stdout.write(self.style.SUCCESS(f"\n🎉Successfullycreated{created_count}samplecourses!"))else:self.stdout.write(self.style.SUCCESS("\n✅Allsamplecoursesalreadyexist!"))#Createsampleenrollmentsfortestingself.stdout.write("\n👥Creatingsampleenrollments...")try:fromcourses.modelsimportEnrollmentstudents=User.objects.filter(role="STUDENT")[:3]#Getupto3studentscourses=Course.objects.all()[:5]#Getupto5coursesenrollment_count=0forstudentinstudents:forcourseincourses[:2]:#Enrolleachstudentin2coursesenrollmentcreated=Enrollment.objects.get_or_create(user=studentcourse=coursedefaults={"enrollment_date":timezone.now()})ifcreated:enrollment_count+=1self.stdout.write(f"✅Created{enrollment_count}sampleenrollments")exceptExceptionase:self.stdout.write(f"⚠️Couldnotcreateenrollments:{e}")self.stdout.write("\n🎯NEXTSTEPS:")self.stdout.write("1.Visit/admin/tomanagecourses")self.stdout.write("2.Testcourselistingat/api/courses/")self.stdout.write("3.Trycoursegenerationfeatures")self.stdout.write("4.Createinteractiveversionsofcourses")
// Mock for react-icons/fi
import React from 'react';

const createMockIcon = (name) => (props) => (
  <svg data-testid={`fi-${name.toLowerCase()}`} {...props}>
    <title>{name}</title>
  </svg>
);

export const FiHome = createMockIcon('Home');
export const FiMap = createMockIcon('Map');

// Export all other commonly used fi icons
export const FiBook = createMockIcon('Book');
export const FiBookOpen = createMockIcon('BookOpen');
export const FiAward = createMockIcon('Award');
export const FiTrendingUp = createMockIcon('TrendingUp');
export const FiCalendar = createMockIcon('Calendar');
export const FiClock = createMockIcon('Clock');
export const FiUser = createMockIcon('User');
export const FiMail = createMockIcon('Mail');
export const FiPhone = createMockIcon('Phone');
export const FiLock = createMockIcon('Lock');
export const FiUnlock = createMockIcon('Unlock');
export const FiCheck = createMockIcon('Check');
export const FiX = createMockIcon('X');
export const FiPlus = createMockIcon('Plus');
export const FiMinus = createMockIcon('Minus');
export const FiEdit = createMockIcon('Edit');
export const FiTrash = createMockIcon('Trash');
export const FiSave = createMockIcon('Save');
export const FiUpload = createMockIcon('Upload');
export const FiDownload = createMockIcon('Download');
export const FiRefreshCw = createMockIcon('RefreshCw');
export const FiSearch = createMockIcon('Search');
export const FiFilter = createMockIcon('Filter');
export const FiSettings = createMockIcon('Settings');
export const FiLogOut = createMockIcon('LogOut');
export const FiEye = createMockIcon('Eye');
export const FiEyeOff = createMockIcon('EyeOff');
export const FiBell = createMockIcon('Bell');
export const FiMessageSquare = createMockIcon('MessageSquare');
export const FiStar = createMockIcon('Star');
export const FiHeart = createMockIcon('Heart');
export const FiShare = createMockIcon('Share');
export const FiCopy = createMockIcon('Copy');
export const FiMoreHorizontal = createMockIcon('MoreHorizontal');
export const FiMoreVertical = createMockIcon('MoreVertical');
export const FiChevronDown = createMockIcon('ChevronDown');
export const FiChevronUp = createMockIcon('ChevronUp');
export const FiChevronLeft = createMockIcon('ChevronLeft');
export const FiChevronRight = createMockIcon('ChevronRight');
export const FiArrowLeft = createMockIcon('ArrowLeft');
export const FiArrowRight = createMockIcon('ArrowRight');
export const FiArrowUp = createMockIcon('ArrowUp');
export const FiArrowDown = createMockIcon('ArrowDown');
export const FiExternalLink = createMockIcon('ExternalLink');
export const FiLink = createMockIcon('Link');
export const FiArchive = createMockIcon('Archive');
export const FiFolder = createMockIcon('Folder');
export const FiFolderPlus = createMockIcon('FolderPlus');
export const FiFile = createMockIcon('File');
export const FiFileText = createMockIcon('FileText');
export const FiImage = createMockIcon('Image');
export const FiVideo = createMockIcon('Video');
export const FiMusic = createMockIcon('Music');
export const FiHeadphones = createMockIcon('Headphones');
export const FiCamera = createMockIcon('Camera');
export const FiMic = createMockIcon('Mic');
export const FiWifi = createMockIcon('Wifi');
export const FiBluetooth = createMockIcon('Bluetooth');
export const FiGlobe = createMockIcon('Globe');
export const FiMonitor = createMockIcon('Monitor');
export const FiSmartphone = createMockIcon('Smartphone');
export const FiTablet = createMockIcon('Tablet');
export const FiLaptop = createMockIcon('Laptop');
export const FiTv = createMockIcon('Tv');
export const FiPrinter = createMockIcon('Printer');
export const FiHardDrive = createMockIcon('HardDrive');
export const FiCpu = createMockIcon('Cpu');
export const FiDatabase = createMockIcon('Database');
export const FiServer = createMockIcon('Server');
export const FiCloud = createMockIcon('Cloud');
export const FiCloudRain = createMockIcon('CloudRain');
export const FiSun = createMockIcon('Sun');
export const FiMoon = createMockIcon('Moon');
export const FiZap = createMockIcon('Zap');
export const FiBattery = createMockIcon('Battery');
export const FiActivity = createMockIcon('Activity');
export const FiBarChart = createMockIcon('BarChart');
export const FiBarChart2 = createMockIcon('BarChart2');
export const FiPieChart = createMockIcon('PieChart');
export const FiTarget = createMockIcon('Target');
export const FiCompass = createMockIcon('Compass');
export const FiMapPin = createMockIcon('MapPin');
export const FiNavigation = createMockIcon('Navigation');
export const FiAnchor = createMockIcon('Anchor');
export const FiFlag = createMockIcon('Flag');
export const FiGift = createMockIcon('Gift');
export const FiShoppingCart = createMockIcon('ShoppingCart');
export const FiShoppingBag = createMockIcon('ShoppingBag');
export const FiCreditCard = createMockIcon('CreditCard');
export const FiDollarSign = createMockIcon('DollarSign');
export const FiPercent = createMockIcon('Percent');
export const FiTrendingDown = createMockIcon('TrendingDown');
export const FiPlayCircle = createMockIcon('PlayCircle');
export const FiPlay = createMockIcon('Play');
export const FiPause = createMockIcon('Pause');
export const FiStop = createMockIcon('Stop');
export const FiSkipBack = createMockIcon('SkipBack');
export const FiSkipForward = createMockIcon('SkipForward');
export const FiFastForward = createMockIcon('FastForward');
export const FiRewind = createMockIcon('Rewind');
export const FiVolume = createMockIcon('Volume');
export const FiVolume1 = createMockIcon('Volume1');
export const FiVolume2 = createMockIcon('Volume2');
export const FiVolumeX = createMockIcon('VolumeX');
export const FiSquare = createMockIcon('Square');
export const FiCircle = createMockIcon('Circle');
export const FiTriangle = createMockIcon('Triangle');
export const FiHexagon = createMockIcon('Hexagon');
export const FiShuffle = createMockIcon('Shuffle');
export const FiRepeat = createMockIcon('Repeat');
export const FiRepeat1 = createMockIcon('Repeat1');
export const FiRotateCcw = createMockIcon('RotateCcw');
export const FiRotateCw = createMockIcon('RotateCw');
export const FiMaximize = createMockIcon('Maximize');
export const FiMaximize2 = createMockIcon('Maximize2');
export const FiMinimize = createMockIcon('Minimize');
export const FiMinimize2 = createMockIcon('Minimize2');
export const FiMove = createMockIcon('Move');
export const FiCornerDownLeft = createMockIcon('CornerDownLeft');
export const FiCornerDownRight = createMockIcon('CornerDownRight');
export const FiCornerLeftDown = createMockIcon('CornerLeftDown');
export const FiCornerLeftUp = createMockIcon('CornerLeftUp');
export const FiCornerRightDown = createMockIcon('CornerRightDown');
export const FiCornerRightUp = createMockIcon('CornerRightUp');
export const FiCornerUpLeft = createMockIcon('CornerUpLeft');
export const FiCornerUpRight = createMockIcon('CornerUpRight');
export const FiCrosshair = createMockIcon('Crosshair');
export const FiLayers = createMockIcon('Layers');
export const FiPackage = createMockIcon('Package');
export const FiBox = createMockIcon('Box');
export const FiSliders = createMockIcon('Sliders');
export const FiToggleLeft = createMockIcon('ToggleLeft');
export const FiToggleRight = createMockIcon('ToggleRight');
export const FiAlertCircle = createMockIcon('AlertCircle');
export const FiAlertTriangle = createMockIcon('AlertTriangle');
export const FiInfo = createMockIcon('Info');
export const FiHelpCircle = createMockIcon('HelpCircle');
export const FiCheckCircle = createMockIcon('CheckCircle');
export const FiXCircle = createMockIcon('XCircle');
export const FiPlusCircle = createMockIcon('PlusCircle');
export const FiMinusCircle = createMockIcon('MinusCircle');
export const FiSlash = createMockIcon('Slash');
export const FiHash = createMockIcon('Hash');
export const FiAtSign = createMockIcon('AtSign');
export const FiGrid = createMockIcon('Grid');
export const FiColumns = createMockIcon('Columns');
export const FiSidebar = createMockIcon('Sidebar');
export const FiList = createMockIcon('List');
export const FiMenu = createMockIcon('Menu');
export const FiNavigation2 = createMockIcon('Navigation2');
export const FiCommand = createMockIcon('Command');
export const FiOption = createMockIcon('Option');
export const FiLoader = createMockIcon('Loader');
export const FiPenTool = createMockIcon('PenTool');
export const FiEdit2 = createMockIcon('Edit2');
export const FiEdit3 = createMockIcon('Edit3');
export const FiFeather = createMockIcon('Feather');
export const FiType = createMockIcon('Type');
export const FiBold = createMockIcon('Bold');
export const FiItalic = createMockIcon('Italic');
export const FiUnderline = createMockIcon('Underline');
export const FiCode = createMockIcon('Code');
export const FiTerminal = createMockIcon('Terminal');
export const FiGitBranch = createMockIcon('GitBranch');
export const FiGitCommit = createMockIcon('GitCommit');
export const FiGitMerge = createMockIcon('GitMerge');
export const FiGitPullRequest = createMockIcon('GitPullRequest');
export const FiGithub = createMockIcon('Github');
export const FiGitlab = createMockIcon('Gitlab');
export const FiCodepen = createMockIcon('Codepen');
export const FiCodesandbox = createMockIcon('Codesandbox');
export const FiFigma = createMockIcon('Figma');
export const FiFramer = createMockIcon('Framer');
export const FiInstagram = createMockIcon('Instagram');
export const FiLinkedin = createMockIcon('Linkedin');
export const FiTwitter = createMockIcon('Twitter');
export const FiFacebook = createMockIcon('Facebook');
export const FiYoutube = createMockIcon('Youtube');
export const FiTwitch = createMockIcon('Twitch');
export const FiSlack = createMockIcon('Slack');
export const FiDroplet = createMockIcon('Droplet');
export const FiUmbrella = createMockIcon('Umbrella');
export const FiWind = createMockIcon('Wind');
export const FiCloudDrizzle = createMockIcon('CloudDrizzle');
export const FiCloudLightning = createMockIcon('CloudLightning');
export const FiCloudSnow = createMockIcon('CloudSnow');
export const FiThermometer = createMockIcon('Thermometer');
export const FiSunrise = createMockIcon('Sunrise');
export const FiSunset = createMockIcon('Sunset');
export const FiCloudOff = createMockIcon('CloudOff');
export const FiAperture = createMockIcon('Aperture');
export const FiZoomIn = createMockIcon('ZoomIn');
export const FiZoomOut = createMockIcon('ZoomOut');

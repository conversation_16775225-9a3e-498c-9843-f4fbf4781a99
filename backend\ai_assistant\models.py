from django.contrib.auth import get_user_model
from django.db import models
from django.utils import timezone

User = get_user_model()


class AIAssistantSession(models.Model):
    """Track AI assistant sessions for analytics and improvement"""
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="ai_sessions"
    )
    session_id = models.CharField(max_length=100, unique=True)
    started_at = models.DateTimeField(default=timezone.now)
    ended_at = models.DateTimeField(null=True, blank=True)
    total_interactions = models.PositiveIntegerField(default=0)
    context = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ["-started_at"]

    def __str__(self):
        return f"AI Session {self.session_id} - {self.user.username}"


class AIAssistantInteraction(models.Model):
    """Store individual interactions for learning and improvement"""
    session = models.ForeignKey(
        AIAssistantSession,
        on_delete=models.CASCADE,
        related_name="interactions"
    )
    question = models.TextField()
    answer = models.TextField()
    confidence_score = models.FloatField(default=0.0)
    response_time = models.FloatField(help_text="Response time in seconds")
    feedback_rating = models.IntegerField(
        null=True,
        blank=True,
        choices=[
            (1, "Very Poor"),
            (2, "Poor"),
            (3, "Average"),
            (4, "Good"),
            (5, "Excellent")
        ]
    )
    created_at = models.DateTimeField(default=timezone.now)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"Interaction {self.id} - {self.session.session_id}"


class AIAssistantSuggestion(models.Model):
    """Predefined suggestions for the AI assistant"""
    text = models.CharField(max_length=500)
    icon = models.CharField(max_length=50, default="QuestionAnswerIcon")
    category = models.CharField(max_length=100, default="general")
    priority = models.IntegerField(
        default=0,
        help_text="Higher numbers appear first"
    )
    is_active = models.BooleanField(default=True)
    target_user_type = models.CharField(
        max_length=50,
        choices=[
            ("all", "All Users"),
            ("student", "Students"),
            ("professor", "Professors"),
            ("admin", "Administrators")
        ],
        default="all"
    )
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-priority", "-created_at"]

    def __str__(self):
        return self.text[:50] + "..." if len(self.text) > 50 else self.text

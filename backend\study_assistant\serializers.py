fromrest_frameworkimportserializersfrom.modelsimport(PracticeQuestionSpacedRepetitionItemStudyMaterialStudyRecommendationStudySessionStudyTopic)classStudySessionSerializer(serializers.ModelSerializer):"""Serializerforstudysessions"""classMeta:model=StudySessionfields=["id""student""start_time""end_time""duration_minutes""focus_score""notes""created_at""updated_at"]read_only_fields=["student""start_time""end_time""duration_minutes"]classStudyTopicSerializer(serializers.ModelSerializer):"""Serializerforstudytopics"""skill_name=serializers.CharField(source="skill.name"read_only=True)course_title=serializers.CharField(source="course.title"read_only=True)classMeta:model=StudyTopicfields=["id""title""description""skill""skill_name""course""course_title""difficulty_level""created_at""updated_at"]classStudyMaterialSerializer(serializers.ModelSerializer):"""Serializerforstudymaterials"""topic_title=serializers.CharField(source="topic.title"read_only=True)classMeta:model=StudyMaterialfields=["id""topic""topic_title""title""content""material_type""ai_generated""created_at""updated_at"]classSpacedRepetitionItemSerializer(serializers.ModelSerializer):"""Serializerforspacedrepetitionitems"""material_title=serializers.CharField(source="material.title"read_only=True)material_content=serializers.CharField(source="material.content"read_only=True)material_type=serializers.CharField(source="material.material_type"read_only=True)topic_title=serializers.CharField(source="material.topic.title"read_only=True)classMeta:model=SpacedRepetitionItemfields=["id""student""material""material_title""material_content""material_type""topic_title""ease_factor""interval""repetitions""next_review_date""last_review_date""last_performance""created_at""updated_at"]read_only_fields=["student""material""ease_factor""interval""repetitions""next_review_date""last_review_date"]classPracticeQuestionSerializer(serializers.ModelSerializer):"""Serializerforpracticequestions"""topic_title=serializers.CharField(source="topic.title"read_only=True)success_rate=serializers.SerializerMethodField()classMeta:model=PracticeQuestionfields=["id""student""topic""topic_title""question_text""options""correct_answer""explanation""difficulty_level""question_type""times_answered""times_correct""last_answered""success_rate""created_at""updated_at"]read_only_fields=["student""times_answered""times_correct""last_answered"]defget_success_rate(selfobj):"""Getthesuccessrateforthisquestion"""returnobj.calculate_success_rate()classStudyRecommendationSerializer(serializers.ModelSerializer):"""Serializerforstudyrecommendations"""topics=StudyTopicSerializer(many=Trueread_only=True)classMeta:model=StudyRecommendationfields=["id""student""title""description""priority""topics""is_implemented""implemented_at""created_at""updated_at"]read_only_fields=["student""is_implemented""implemented_at"]
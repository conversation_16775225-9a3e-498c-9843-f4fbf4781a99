"""
Optimized Assessment models with performance improvements.

This module contains optimized versions of assessment models with:
- Proper database indexing
- Optimized QuerySets with select_related and prefetch_related
- Efficient pagination
- Reduced N+1 query problems
"""

import json
import logging
from datetime import timedelta
from typing import Dict, List, Optional, Any, TYPE_CHECKING

from django.conf import settings
from django.db import models, transaction
from django.db.models import (
    Q, F, Count, Avg, Sum, Max, Min, 
    Prefetch, Case, When, Value, 
    IntegerField, FloatField
)
from django.core.cache import cache
from django.core.paginator import Paginator
from django.utils import timezone
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType

# Import the unified models from core app
from core.question_models import BaseQuestion, BaseQuestionResponse
from core.mixins import ProgressTrackingMixin

if TYPE_CHECKING:
    from users.models import CustomUser

logger = logging.getLogger(__name__)


class OptimizedQuerySetMixin:
    """Mixin providing optimized QuerySet methods for better performance."""
    
    def with_related(self):
        """Add select_related for commonly accessed foreign keys."""
        return self.select_related(
            'student', 'student__profile', 'created_by'
        )
    
    def with_prefetch(self):
        """Add prefetch_related for commonly accessed many-to-many relationships."""
        return self.prefetch_related(
            'questions', 'responses', 'responses__question'
        )
    
    def optimized(self):
        """Combine all common optimizations."""
        return self.with_related().with_prefetch()


class StudentLevelQuerySet(models.QuerySet, OptimizedQuerySetMixin):
    """Optimized QuerySet for StudentLevel model."""
    
    def for_level(self, level: int):
        """Get students at a specific level."""
        return self.filter(current_level=level)
    
    def with_progression_data(self):
        """Include progression analytics data."""
        return self.annotate(
            total_assessments=Count('student__assessments'),
            avg_score=Avg('student__assessments__score'),
            level_changes=Count('student__assessments', 
                              filter=Q(student__assessments__level_changed=True))
        )
    
    def recent_activity(self, days: int = 30):
        """Filter students with recent activity."""
        cutoff_date = timezone.now() - timedelta(days=days)
        return self.filter(last_assessment_date__gte=cutoff_date)


class StudentLevelManager(models.Manager):
    """Optimized manager for StudentLevel model."""
    
    def get_queryset(self):
        return StudentLevelQuerySet(self.model, using=self._db)
    
    def for_level(self, level: int):
        return self.get_queryset().for_level(level)
    
    def with_analytics(self):
        return self.get_queryset().with_progression_data()
    
    def get_or_create_optimized(self, student: 'CustomUser') -> 'StudentLevel':
        """Get or create student level with caching for performance."""
        cache_key = f"student_level_{student.id}"
        student_level = cache.get(cache_key)
        
        if student_level is None:
            student_level, created = self.get_or_create(
                student=student,
                defaults={
                    'current_level': 1,
                    'current_level_display': 'Beginner',
                    'skill_strengths': {},
                    'skill_weaknesses': {},
                    'progression_history': []
                }
            )
            # Cache for 1 hour
            cache.set(cache_key, student_level, 3600)
        
        return student_level


class StudentLevel(models.Model):
    """
    Optimized model to track student's proficiency level across the platform.
    
    Performance improvements:
    - Added database indexes for frequently queried fields
    - Optimized manager with QuerySet methods
    - Caching support for frequently accessed data
    """
    
    LEVEL_CHOICES = [
        (1, "Beginner"),
        (2, "Elementary"), 
        (3, "Intermediate"),
        (4, "Advanced"),
        (5, "Expert")
    ]
    
    student = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="level_profile",
        db_index=True  # Index for faster lookups
    )
    current_level = models.IntegerField(
        choices=LEVEL_CHOICES,
        default=1,
        db_index=True  # Index for level-based queries
    )
    current_level_display = models.CharField(max_length=50, default="Beginner")
    last_assessment_date = models.DateTimeField(
        null=True, 
        blank=True,
        db_index=True  # Index for time-based queries
    )
    
    # JSON fields for flexible data storage
    skill_strengths = models.JSONField(
        default=dict,
        help_text="Areas where student shows strength"
    )
    skill_weaknesses = models.JSONField(
        default=dict,
        help_text="Areas where student needs improvement"
    )
    progression_history = models.JSONField(
        default=list,
        help_text="History of level changes",
        blank=True,
        null=True
    )
    
    # Timestamps with indexes
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, db_index=True)
    
    # Custom manager
    objects = StudentLevelManager()
    
    class Meta:
        verbose_name = "Student Level"
        verbose_name_plural = "Student Levels"
        indexes = [
            # Composite indexes for common query patterns
            models.Index(fields=['current_level', 'last_assessment_date'], name='level_recent_idx'),
            models.Index(fields=['student', 'current_level'], name='student_level_idx'),
            models.Index(fields=['updated_at', 'current_level'], name='updated_level_idx'),
        ]
        constraints = [
            models.CheckConstraint(
                check=Q(current_level__gte=1) & Q(current_level__lte=5),
                name='valid_level_range'
            )
        ]
    
    def __str__(self):
        return f"{self.student.username}'s Level: {self.get_current_level_display()}"
    
    def save(self, *args, **kwargs):
        """Override save to maintain data consistency and clear cache."""
        # Ensure progression_history is initialized
        if self.progression_history is None:
            self.progression_history = []
        
        # Ensure current_level_display is set based on current_level
        for level_id, level_name in self.LEVEL_CHOICES:
            if self.current_level == level_id:
                self.current_level_display = level_name
                break
        
        # Ensure other JSON fields are initialized
        if self.skill_strengths is None:
            self.skill_strengths = {}
        if self.skill_weaknesses is None:
            self.skill_weaknesses = {}
        
        super().save(*args, **kwargs)
        
        # Clear cache when data changes
        cache_key = f"student_level_{self.student_id}"
        cache.delete(cache_key)
    
    def update_level(self, new_level: int, reason: str = "Assessment", 
                    assessment=None, admin_id: Optional[int] = None) -> bool:
        """
        Update student level and record in progression history.
        
        Args:
            new_level: New level to set (1-5)
            reason: Reason for level change
            assessment: Assessment that caused the change
            admin_id: ID of admin who made the change
            
        Returns:
            True if level was changed, False otherwise
        """
        if new_level not in dict(self.LEVEL_CHOICES):
            raise ValueError(f"Invalid level: {new_level}")
        
        previous_level = self.current_level
        
        if new_level == previous_level:
            return False
        
        # Update level fields
        self.current_level = new_level
        self.current_level_display = dict(self.LEVEL_CHOICES).get(new_level, "Beginner")
        self.last_assessment_date = timezone.now()
        
        # Initialize progression_history if it's None
        if self.progression_history is None:
            self.progression_history = []
        
        # Record the level change in progression history
        history_entry = {
            "id": f"{self.student_id}-{len(self.progression_history)}",
            "date": timezone.now().isoformat(),
            "from_level": previous_level,
            "from_level_display": dict(self.LEVEL_CHOICES).get(previous_level, "Beginner"),
            "to_level": new_level,
            "to_level_display": dict(self.LEVEL_CHOICES).get(new_level, "Beginner"),
            "reason": reason,
            "changed_by": "Admin" if admin_id else "System",
            "level_changed": True
        }
        
        # Add assessment ID if provided
        if assessment:
            history_entry["assessment_id"] = assessment.id
        
        # Add admin ID if provided  
        if admin_id:
            history_entry["admin_id"] = admin_id
        
        self.progression_history.append(history_entry)
        self.save()
        
        return True


class AssessmentQuerySet(models.QuerySet, OptimizedQuerySetMixin):
    """Optimized QuerySet for Assessment model."""
    
    def completed(self):
        """Filter completed assessments."""
        return self.filter(status='COMPLETED', completed=True)
    
    def for_student(self, student: 'CustomUser'):
        """Filter assessments for a specific student."""
        return self.filter(student=student)
    
    def by_type(self, assessment_type: str):
        """Filter by assessment type."""
        return self.filter(assessment_type=assessment_type)
    
    def recent(self, days: int = 30):
        """Filter recent assessments."""
        cutoff_date = timezone.now() - timedelta(days=days)
        return self.filter(created_at__gte=cutoff_date)
    
    def with_scores(self):
        """Annotate with calculated score data."""
        return self.annotate(
            total_questions=Count('questions'),
            correct_responses=Count('responses', filter=Q(responses__is_correct=True)),
            calculated_score=Case(
                When(total_questions=0, then=Value(0)),
                default=F('correct_responses') * 100.0 / F('total_questions'),
                output_field=FloatField()
            )
        )
    
    def with_analytics(self):
        """Include analytics data for dashboards."""
        return self.annotate(
            response_count=Count('responses'),
            avg_response_time=Avg('responses__time_spent'),
            completion_rate=Case(
                When(questions__isnull=True, then=Value(0)),
                default=Count('responses') * 100.0 / Count('questions'),
                output_field=FloatField()
            )
        )


class AssessmentManager(models.Manager):
    """Optimized manager for Assessment model."""
    
    def get_queryset(self):
        return AssessmentQuerySet(self.model, using=self._db)
    
    def completed(self):
        return self.get_queryset().completed()
    
    def for_student(self, student: 'CustomUser'):
        return self.get_queryset().for_student(student)
    
    def get_student_stats(self, student: 'CustomUser') -> Dict[str, Any]:
        """Get comprehensive statistics for a student."""
        cache_key = f"student_assessment_stats_{student.id}"
        stats = cache.get(cache_key)
        
        if stats is None:
            assessments = self.for_student(student).completed()
            
            stats = {
                'total_assessments': assessments.count(),
                'average_score': assessments.aggregate(Avg('score'))['score__avg'] or 0,
                'highest_score': assessments.aggregate(Max('score'))['score__max'] or 0,
                'latest_assessment': assessments.order_by('-end_time').first(),
                'by_type': {}
            }
            
            # Get stats by assessment type
            for assessment_type, display_name in Assessment.ASSESSMENT_TYPES:
                type_assessments = assessments.filter(assessment_type=assessment_type)
                stats['by_type'][assessment_type] = {
                    'count': type_assessments.count(),
                    'avg_score': type_assessments.aggregate(Avg('score'))['score__avg'] or 0
                }
            
            # Cache for 30 minutes
            cache.set(cache_key, stats, 1800)
        
        return stats


class Assessment(models.Model):
    """
    Optimized unified model for all types of assessments.
    
    Performance improvements:
    - Added strategic database indexes
    - Optimized QuerySet and Manager
    - Efficient caching of computed values
    - Reduced database queries through select_related/prefetch_related
    """
    
    ASSESSMENT_TYPES = [
        ("PLACEMENT", "Placement Assessment"),
        ("COURSE", "Course Assessment"),
        ("MILESTONE", "Milestone Assessment"),
        ("QUIZ", "Quiz"),
        ("EXAM", "Exam"),
        ("PRACTICE", "Practice")
    ]
    
    STATUS_CHOICES = [
        ("PENDING", "Pending"),
        ("IN_PROGRESS", "In Progress"),
        ("COMPLETED", "Completed"),
        ("EXPIRED", "Expired")
    ]
    
    # Basic fields with strategic indexing
    title = models.CharField(max_length=255, blank=True, db_index=True)
    description = models.TextField(blank=True)
    assessment_type = models.CharField(
        max_length=20,
        choices=ASSESSMENT_TYPES,
        db_index=True  # Index for type-based filtering
    )
    
    # Foreign key with index
    student = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="assessments",
        db_index=True  # Index for student-based queries
    )
    
    # Many-to-many with through table (already optimized)
    questions = models.ManyToManyField(
        "AssessmentQuestion",
        through="AssessmentResponse"
    )
    
    # Status and scoring fields with indexes
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default="PENDING",
        db_index=True  # Index for status filtering
    )
    score = models.FloatField(null=True, blank=True, db_index=True)
    
    # Timestamp fields with indexes for time-based queries
    start_time = models.DateTimeField(null=True, blank=True, db_index=True)
    end_time = models.DateTimeField(null=True, blank=True, db_index=True)
    completed = models.BooleanField(default=False, db_index=True)
    
    # JSON fields for flexible data storage
    skill_scores = models.JSONField(
        default=dict,
        help_text="Scores by skill category"
    )
    feedback = models.JSONField(
        default=dict,
        help_text="Structured feedback"
    )
    detailed_results = models.JSONField(
        default=dict,
        help_text="Detailed assessment results"
    )
    adaptive_progression = models.JSONField(
        default=dict,
        help_text="Tracks the progression of difficulty during adaptive assessment"
    )
    
    # Boolean flags with indexes
    is_active = models.BooleanField(default=True, db_index=True)
    is_adaptive = models.BooleanField(
        default=True,
        help_text="Whether this is an adaptive assessment"
    )
    level_changed = models.BooleanField(
        default=False,
        help_text="Whether the student's level changed",
        db_index=True
    )
    
    # Tracking fields
    current_difficulty_level = models.PositiveSmallIntegerField(
        default=1,
        help_text="Current difficulty level in adaptive assessment"
    )
    learning_path = models.CharField(
        max_length=50,
        choices=[
            ("programming", "Programming"),
            ("cybersecurity", "Cybersecurity"),
            ("finance", "Finance"),
            ("marketing", "Marketing"),
            ("general", "General")
        ],
        default="general",
        help_text="The learning path this assessment is for",
        db_index=True  # Index for path-based filtering
    )
    
    # Level tracking with indexes
    initial_level = models.IntegerField(
        null=True,
        blank=True,
        help_text="Student's level before assessment",
        db_index=True
    )
    final_level = models.IntegerField(
        null=True,
        blank=True,
        help_text="Student's level after assessment",
        db_index=True
    )
    
    # Performance tracking
    time_spent = models.IntegerField(
        null=True,
        blank=True,
        help_text="Time spent in seconds"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, db_index=True)
    
    # Custom manager
    objects = AssessmentManager()
    
    class Meta:
        verbose_name = "Assessment"
        verbose_name_plural = "Assessments"
        ordering = ['-created_at']
        indexes = [
            # Composite indexes for common query patterns
            models.Index(fields=['student', 'status'], name='student_status_idx'),
            models.Index(fields=['assessment_type', 'completed'], name='type_completed_idx'),
            models.Index(fields=['student', 'assessment_type', 'completed'], name='student_type_completed_idx'),
            models.Index(fields=['learning_path', 'score'], name='path_score_idx'),
            models.Index(fields=['end_time', 'status'], name='end_time_status_idx'),
            models.Index(fields=['level_changed', 'assessment_type'], name='level_change_type_idx'),
            models.Index(fields=['created_at', 'student'], name='created_student_idx'),
        ]
        constraints = [
            models.CheckConstraint(
                check=Q(score__isnull=True) | (Q(score__gte=0) & Q(score__lte=100)),
                name='valid_score_range'
            )
        ]
    
    def __str__(self):
        return f"{self.title or self.assessment_type} - {self.student.username}"
    
    def save(self, *args, **kwargs):
        """Override save to ensure initial_level is set and clear relevant caches."""
        # Ensure initial_level is set regardless of whether this is a new assessment
        if self.initial_level is None and self.student is not None:
            # Get or create student level to determine initial level
            try:
                student_level, created = StudentLevel.objects.get_or_create(
                    student=self.student,
                    defaults={
                        "current_level": 1,
                        "current_level_display": "Beginner"
                    }
                )
                self.initial_level = student_level.current_level
                logger.info(f"Set initial_level to {self.initial_level} for assessment {self.id if self.id else 'new'}")
            except Exception as e:
                logger.error(f"Error setting initial_level: {str(e)}")
                # Set a default value to avoid null constraint errors
                self.initial_level = 1
        
        super().save(*args, **kwargs)
        
        # Clear relevant caches
        if self.student_id:
            cache.delete(f"student_assessment_stats_{self.student_id}")
    
    @transaction.atomic
    def submit(self) -> bool:
        """
        Submit and process the assessment with optimized database operations.
        
        Returns:
            True if submission was successful
        """
        if self.completed:
            raise ValueError("Assessment already completed")
        
        # Ensure initial_level is set before proceeding
        if self.initial_level is None:
            student_level, created = StudentLevel.objects.get_or_create(
                student=self.student,
                defaults={
                    "current_level": 1,
                    "current_level_display": "Beginner"
                }
            )
            self.initial_level = student_level.current_level
            self.save(update_fields=["initial_level"])
        
        # Calculate score efficiently
        self.score = self.calculate_score()
        self.completed = True
        self.end_time = timezone.now()
        self.time_spent = self.get_time_spent()
        self.status = "COMPLETED"
        
        # Calculate skill scores and store detailed results
        self._calculate_skill_scores()
        self._store_detailed_results()
        
        # Save the assessment with updated fields
        self.save()
        
        # Update student level
        level_info = self._update_student_level()
        
        # Create assessment result record
        self._create_assessment_result()
        
        logger.info(f"Assessment {self.id} submitted with score {self.score} and level info: {level_info}")
        
        return True
    
    def calculate_score(self) -> float:
        """Calculate score based on responses using optimized query."""
        if not self.completed:
            return 0
        
        # Use aggregate to calculate in database
        result = self.responses.aggregate(
            earned_points=Sum('points_earned'),
            total_points=Sum('question__points')
        )
        
        earned_points = result['earned_points'] or 0
        total_points = result['total_points'] or 0
        
        if total_points == 0:
            return 0
        
        return (earned_points / total_points) * 100
    
    def get_time_spent(self) -> int:
        """Get time spent on assessment in seconds."""
        if not self.start_time:
            return 0
        
        end = self.end_time or timezone.now()
        return int((end - self.start_time).total_seconds())
    
    def _calculate_skill_scores(self):
        """Calculate scores for each skill category using optimized queries."""
        # Use prefetch_related to optimize the query
        questions_with_skills = self.questions.prefetch_related('skills_assessed').all()
        responses_dict = {r.question_id: r for r in self.responses.select_related('question').all()}
        
        skill_scores = {}
        strengths = []
        weaknesses = []
        
        for question in questions_with_skills:
            if not question.skills_assessed.exists():
                continue
                
            response = responses_dict.get(question.id)
            if not response:
                continue
            
            for skill in question.skills_assessed.all():
                if skill.name not in skill_scores:
                    skill_scores[skill.name] = {
                        "total_questions": 0,
                        "correct_answers": 0
                    }
                
                skill_scores[skill.name]["total_questions"] += 1
                if response.is_correct:
                    skill_scores[skill.name]["correct_answers"] += 1
        
        # Calculate percentages and determine strengths/weaknesses
        for skill, scores in skill_scores.items():
            if scores["total_questions"] > 0:
                percentage = (scores["correct_answers"] / scores["total_questions"]) * 100
                skill_scores[skill]["percentage"] = percentage
                
                if percentage >= 70:
                    strengths.append(skill)
                elif percentage <= 40:
                    weaknesses.append(skill)
        
        self.skill_scores = skill_scores
        self.strengths = strengths
        self.weaknesses = weaknesses
    
    def _store_detailed_results(self):
        """Store detailed results with optimized data access."""
        try:
            detailed_results = {
                "score": self.score,
                "time_spent": self.time_spent,
                "questions_total": self.questions.count(),
                "questions_correct": self.responses.filter(is_correct=True).count(),
                "responses": [],
                "strengths": getattr(self, 'strengths', []),
                "weaknesses": getattr(self, 'weaknesses', []),
                "adaptive_progression": self.adaptive_progression,
                "is_adaptive": self.is_adaptive
            }
            
            # Optimize response data collection
            responses_data = self.responses.select_related('question').values(
                'question__id', 'question__question_text', 'question__question_type',
                'question__correct_answer', 'question__points',
                'answer_text', 'answer', 'student_answer', 'is_correct',
                'points_earned', 'time_spent'
            )
            
            for response_data in responses_data:
                # Process student answer
                student_answer = (response_data['answer_text'] or 
                                response_data['answer'] or 
                                response_data['student_answer'] or "")
                
                # Process correct answer
                correct_answer = response_data['question__correct_answer']
                if isinstance(correct_answer, str):
                    try:
                        correct_answer = json.loads(correct_answer)
                    except json.JSONDecodeError:
                        pass
                
                detailed_results["responses"].append({
                    "question_id": response_data['question__id'],
                    "question_text": response_data['question__question_text'],
                    "question_type": response_data['question__question_type'],
                    "student_answer": student_answer,
                    "correct_answer": correct_answer,
                    "is_correct": response_data['is_correct'],
                    "points_earned": response_data['points_earned'],
                    "points_possible": response_data['question__points'],
                    "time_spent": response_data['time_spent']
                })
            
            self.detailed_results = detailed_results
            
        except Exception as e:
            logger.error(f"Error storing detailed results: {str(e)}")
            self.detailed_results = {
                "score": self.score,
                "error": str(e),
                "responses": []
            }
    
    def _update_student_level(self) -> Dict[str, Any]:
        """Update student level based on assessment performance using optimized approach."""
        try:
            # Use the optimized manager method
            student_level = StudentLevel.objects.get_or_create_optimized(self.student)
            current_level = student_level.current_level
            
            # Store the initial level if not already set
            if self.initial_level is None:
                self.initial_level = current_level
                self.save(update_fields=["initial_level"])
            
            # Determine new level based on score and assessment type
            if self.assessment_type == "PLACEMENT":
                # More detailed level determination for placement assessments
                if self.score >= 90:
                    new_level = 5  # Expert
                elif self.score >= 75:
                    new_level = 4  # Advanced
                elif self.score >= 60:
                    new_level = 3  # Intermediate
                elif self.score >= 40:
                    new_level = 2  # Elementary
                else:
                    new_level = 1  # Beginner
            else:
                # For regular assessments, adjust level up or down conservatively
                if self.score >= 90:
                    new_level = min(5, current_level + 1)
                elif self.score <= 40:
                    new_level = max(1, current_level - 1)
                else:
                    new_level = current_level
            
            # Set the final level
            self.final_level = new_level
            self.level_changed = new_level != current_level
            
            # Save assessment with updated level information
            self.save(update_fields=["initial_level", "final_level", "level_changed"])
            
            # Update the student level if it changed
            if self.level_changed:
                student_level.update_level(
                    new_level=new_level,
                    reason=f"{self.assessment_type} Assessment (Score: {self.score}%)",
                    assessment=self
                )
            
            return {
                "current_level": student_level.current_level,
                "current_level_display": student_level.current_level_display,
                "initial_level": self.initial_level,
                "final_level": self.final_level,
                "level_changed": self.level_changed
            }
            
        except Exception as e:
            logger.error(f"Error updating student level: {str(e)}")
            return {
                "error": str(e),
                "current_level": 1,
                "current_level_display": "Beginner",
                "initial_level": self.initial_level or 1,
                "final_level": self.final_level or 1,
                "level_changed": False
            }
    
    def _create_assessment_result(self):
        """Create an AssessmentResult record with detailed analysis."""
        from .models import AssessmentResult
        
        try:
            # Calculate strengths and weaknesses
            strengths = getattr(self, 'strengths', [])
            weaknesses = getattr(self, 'weaknesses', [])
            
            # Generate recommendations
            recommendations = self._generate_recommendations(strengths, weaknesses)
            
            # Create or update the result record
            result, created = AssessmentResult.objects.update_or_create(
                assessment=self,
                defaults={
                    "skill_analysis": self.skill_scores,
                    "strengths": strengths,
                    "weaknesses": weaknesses,
                    "recommendations": recommendations
                }
            )
            
            logger.info(f"{'Created' if created else 'Updated'} AssessmentResult for assessment {self.id}")
            return result
            
        except Exception as e:
            logger.error(f"Error creating AssessmentResult: {str(e)}")
            return None
    
    def _generate_recommendations(self, strengths: List[str], weaknesses: List[str]) -> List[str]:
        """Generate recommendations based on strengths and weaknesses."""
        recommendations = []
        
        if weaknesses:
            recommendations.append(f"Focus on improving your skills in: {', '.join(weaknesses)}")
        
        if strengths:
            recommendations.append(f"Continue developing your strengths in: {', '.join(strengths)}")
        
        return recommendations


class AssessmentQuestionQuerySet(models.QuerySet, OptimizedQuerySetMixin):
    """Optimized QuerySet for AssessmentQuestion model."""
    
    def public_questions(self):
        """Filter public questions."""
        return self.filter(is_public=True)
    
    def placement_questions(self):
        """Filter placement questions."""
        return self.filter(is_placement=True)
    
    def by_category(self, category: str):
        """Filter by category."""
        return self.filter(category=category)
    
    def by_difficulty(self, difficulty: int):
        """Filter by difficulty level."""
        return self.filter(difficulty_level=difficulty)
    
    def with_skills(self):
        """Prefetch related skills."""
        return self.prefetch_related('skills_assessed')


class AssessmentQuestionManager(models.Manager):
    """Optimized manager for AssessmentQuestion model."""
    
    def get_queryset(self):
        return AssessmentQuestionQuerySet(self.model, using=self._db)
    
    def get_questions_for_assessment(self, assessment_type: str, learning_path: str = "general", 
                                   count: int = 10) -> List['AssessmentQuestion']:
        """Get optimized questions for an assessment."""
        cache_key = f"questions_{assessment_type}_{learning_path}_{count}"
        questions = cache.get(cache_key)
        
        if questions is None:
            base_filter = {
                'is_public': True,
                'is_placement': (assessment_type == "PLACEMENT")
            }
            
            if learning_path and learning_path != "general":
                # Try to get path-specific questions first
                path_questions = list(self.get_queryset()
                                    .filter(**base_filter, category=learning_path)
                                    .with_skills()
                                    .order_by('?')[:count])
                
                if len(path_questions) >= count:
                    questions = path_questions
                else:
                    # Fill remaining with general questions
                    general_questions = list(self.get_queryset()
                                           .filter(**base_filter, category="general")
                                           .exclude(id__in=[q.id for q in path_questions])
                                           .with_skills()
                                           .order_by('?')[:count - len(path_questions)])
                    questions = path_questions + general_questions
            else:
                questions = list(self.get_queryset()
                               .filter(**base_filter)
                               .with_skills()
                               .order_by('?')[:count])
            
            # Cache for 15 minutes
            cache.set(cache_key, questions, 900)
        
        return questions


class AssessmentQuestion(BaseQuestion):
    """
    Optimized model for assessment questions.
    
    Performance improvements:
    - Strategic database indexing
    - Optimized QuerySet methods
    - Efficient caching
    """
    
    CATEGORY_CHOICES = [
        ("programming", "Programming"),
        ("cybersecurity", "Cybersecurity"),
        ("finance", "Finance"),
        ("marketing", "Marketing"),
        ("general", "General")
    ]
    
    # Category field with index
    category = models.CharField(
        max_length=50,
        choices=CATEGORY_CHOICES,
        default="general",
        db_index=True  # Index for category-based filtering
    )
    
    # Learning path field matching category choices
    learning_path = models.CharField(
        max_length=50,
        choices=CATEGORY_CHOICES,
        default="general",
        help_text="The learning path this question belongs to",
        db_index=True
    )
    
    # Skills relationship with optimized access
    skills_assessed = models.ManyToManyField(
        "core.Skill",
        related_name="questions",
        blank=True
    )
    
    # Boolean fields with indexes for filtering
    is_public = models.BooleanField(
        default=True,
        help_text="Whether this question can be shown to students",
        db_index=True
    )
    is_placement = models.BooleanField(
        default=False,
        help_text="Whether this question is for placement assessments",
        db_index=True
    )
    ai_reviewed = models.BooleanField(
        default=False,
        help_text="Whether this question was reviewed by AI",
        db_index=True
    )
    ai_suggested = models.BooleanField(default=False, db_index=True)
    
    # Custom manager
    objects = AssessmentQuestionManager()
    
    class Meta:
        verbose_name = "Assessment Question"
        verbose_name_plural = "Assessment Questions"
        indexes = [
            # Composite indexes for common query patterns
            models.Index(fields=['category', 'is_public'], name='category_public_idx'),
            models.Index(fields=['is_placement', 'is_public'], name='placement_public_idx'),
            models.Index(fields=['learning_path', 'difficulty_level'], name='path_difficulty_idx'),
            models.Index(fields=['category', 'difficulty_level', 'is_public'], name='cat_diff_public_idx'),
            models.Index(fields=['ai_suggested', 'ai_reviewed'], name='ai_status_idx'),
        ]
    
    def __str__(self):
        return f"{self.question_text[:50]}..."


class AssessmentResponseQuerySet(models.QuerySet, OptimizedQuerySetMixin):
    """Optimized QuerySet for AssessmentResponse model."""
    
    def correct_responses(self):
        """Filter correct responses."""
        return self.filter(is_correct=True)
    
    def for_assessment(self, assessment_id: int):
        """Filter responses for a specific assessment."""
        return self.filter(assessment_id=assessment_id)
    
    def for_student(self, student: 'CustomUser'):
        """Filter responses for a specific student."""
        return self.filter(student=student)
    
    def with_question_data(self):
        """Include question data in query."""
        return self.select_related('question', 'assessment')


class AssessmentResponseManager(models.Manager):
    """Optimized manager for AssessmentResponse model."""
    
    def get_queryset(self):
        return AssessmentResponseQuerySet(self.model, using=self._db)
    
    def get_assessment_statistics(self, assessment_id: int) -> Dict[str, Any]:
        """Get statistics for an assessment with optimized queries."""
        cache_key = f"assessment_stats_{assessment_id}"
        stats = cache.get(cache_key)
        
        if stats is None:
            responses = self.get_queryset().for_assessment(assessment_id)
            
            # Use aggregate to calculate statistics in database
            aggregate_data = responses.aggregate(
                total_responses=Count('id'),
                correct_responses=Count('id', filter=Q(is_correct=True)),
                total_points_earned=Sum('points_earned'),
                avg_time_spent=Avg('time_spent')
            )
            
            # Calculate additional metrics
            total_responses = aggregate_data['total_responses'] or 0
            correct_responses = aggregate_data['correct_responses'] or 0
            
            stats = {
                'total_responses': total_responses,
                'correct_responses': correct_responses,
                'accuracy_rate': (correct_responses / total_responses * 100) if total_responses > 0 else 0,
                'total_points_earned': aggregate_data['total_points_earned'] or 0,
                'average_time_spent': aggregate_data['avg_time_spent'] or 0
            }
            
            # Cache for 10 minutes
            cache.set(cache_key, stats, 600)
        
        return stats


class AssessmentResponse(BaseQuestionResponse):
    """
    Optimized model to track student responses to questions.
    
    Performance improvements:
    - Strategic indexing for frequently queried fields
    - Optimized QuerySet methods
    - Efficient evaluation methods
    """
    
    # Foreign keys with indexes
    assessment = models.ForeignKey(
        Assessment,
        on_delete=models.CASCADE,
        related_name="responses",
        db_index=True
    )
    question = models.ForeignKey(
        AssessmentQuestion,
        on_delete=models.CASCADE,
        related_name="responses",
        db_index=True
    )
    student = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="assessment_responses",
        db_index=True
    )
    
    # Legacy fields for backward compatibility
    student_answer = models.JSONField(
        null=True,
        blank=True,
        help_text="JSON format answer (legacy)"
    )
    answer = models.TextField(
        blank=True,
        null=True,
        help_text="Text answer (legacy)"
    )
    feedback = models.TextField(blank=True)
    
    # Custom manager
    objects = AssessmentResponseManager()
    
    class Meta:
        verbose_name = "Assessment Response"
        verbose_name_plural = "Assessment Responses"
        unique_together = ["assessment", "question"]
        ordering = ["created_at"]
        indexes = [
            # Composite indexes for common query patterns
            models.Index(fields=["assessment", "question"], name="assessment_response_idx"),
            models.Index(fields=["student", "is_correct"], name="student_correct_response_idx"),
            models.Index(fields=["assessment", "is_correct"], name="assessment_correct_idx"),
            models.Index(fields=["question", "is_correct"], name="question_correct_idx"),
            models.Index(fields=["submitted_at", "assessment"], name="submitted_assessment_idx"),
        ]
    
    def __str__(self):
        return f"Response by {self.student.username} to {self.question.question_text[:30]}..."
    
    def save(self, *args, **kwargs):
        """Override save to clear relevant caches."""
        super().save(*args, **kwargs)
        
        # Clear assessment statistics cache
        if self.assessment_id:
            cache.delete(f"assessment_stats_{self.assessment_id}")
    
    def evaluate(self) -> bool:
        """
        Evaluate the response and set is_correct and points_earned.
        
        Returns:
            True if the answer is correct, False otherwise
        """
        try:
            # Get the answer text from the primary field first, then fallbacks
            answer_text = self.answer_text or self.answer or ""
            
            # Try to get student_answer if it's a JSON field and answer_text is empty
            if not answer_text and self.student_answer:
                if isinstance(self.student_answer, str):
                    try:
                        parsed_answer = json.loads(self.student_answer)
                        if isinstance(parsed_answer, dict) and "answer" in parsed_answer:
                            answer_text = parsed_answer["answer"]
                        elif isinstance(parsed_answer, str):
                            answer_text = parsed_answer
                    except json.JSONDecodeError:
                        answer_text = self.student_answer
                elif isinstance(self.student_answer, dict) and "answer" in self.student_answer:
                    answer_text = self.student_answer["answer"]
                elif isinstance(self.student_answer, dict) and "answer_text" in self.student_answer:
                    answer_text = self.student_answer["answer_text"]
                else:
                    answer_text = str(self.student_answer)
            
            # Ensure answer_text is set to something if it's still empty
            if not answer_text and self.student_answer is not None:
                if isinstance(self.student_answer, (dict, list)):
                    answer_text = json.dumps(self.student_answer)
                else:
                    answer_text = str(self.student_answer)
            
            # Use the BaseQuestion's check_answer method to evaluate the answer
            self.is_correct = self.question.check_answer(answer_text)
            
            # Set points earned based on correctness
            self.points_earned = self.question.points if self.is_correct else 0
            
            # Ensure answer_text is saved
            if not self.answer_text:
                self.answer_text = answer_text
            
            logger.info(f"Evaluation result for question {self.question.id}: {self.is_correct}, points: {self.points_earned}")
            
            return self.is_correct
            
        except Exception as e:
            logger.error(f"Error evaluating response: {str(e)}")
            self.is_correct = False
            self.points_earned = 0
            return False


# Additional optimized models can be added here following the same patterns...

class AssessmentResult(models.Model):
    """
    Optimized model for storing detailed assessment results.
    
    Performance improvements:
    - Strategic indexing
    - Efficient data storage
    """
    
    assessment = models.ForeignKey(
        Assessment,
        on_delete=models.CASCADE,
        related_name="results",
        db_index=True
    )
    skill_analysis = models.JSONField(default=dict)
    strengths = models.JSONField(default=list)
    weaknesses = models.JSONField(default=list)
    recommendations = models.JSONField(default=list)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    
    class Meta:
        verbose_name = "Assessment Result"
        verbose_name_plural = "Assessment Results"
        indexes = [
            models.Index(fields=['assessment', 'created_at'], name='result_assessment_idx'),
        ]
    
    def __str__(self):
        return f"Results for {self.assessment.title}"

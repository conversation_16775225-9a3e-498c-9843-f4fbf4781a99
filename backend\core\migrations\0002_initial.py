# Generated by Django 4.2.7 on 2025-07-04 15:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("core", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("contenttypes", "0002_remove_content_type_name"),
    ]

    operations = [
        migrations.AddField(
            model_name="userprogress",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="studentskillprogress",
            name="content_type",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="contenttypes.contenttype",
            ),
        ),
        migrations.AddField(
            model_name="studentskillprogress",
            name="skill",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="student_progress",
                to="core.skill",
            ),
        ),
        migrations.AddField(
            model_name="studentskillprogress",
            name="student",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="unified_skill_progress",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="studentlevel",
            name="level_updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="level_updates",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="studentlevel",
            name="student",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="student_level",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="skill",
            name="parent_skill",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="child_skills",
                to="core.skill",
            ),
        ),
        migrations.AddField(
            model_name="contentreference",
            name="content_type",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="contenttypes.contenttype",
            ),
        ),
        migrations.AddField(
            model_name="contentmapping",
            name="source",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="source_mappings",
                to="core.contentreference",
            ),
        ),
        migrations.AddField(
            model_name="contentmapping",
            name="target",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="target_mappings",
                to="core.contentreference",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="userprogress",
            unique_together={("user", "content_reference")},
        ),
        migrations.AddIndex(
            model_name="studentskillprogress",
            index=models.Index(
                fields=["content_type", "object_id"],
                name="core_studen_content_f9cd9f_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="studentskillprogress",
            unique_together={("student", "skill", "content_type", "object_id")},
        ),
        migrations.AddIndex(
            model_name="contentreference",
            index=models.Index(
                fields=["content_type", "object_id"],
                name="core_conten_content_525a9c_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="contentmapping",
            unique_together={("source", "target", "relationship_type")},
        ),
    ]

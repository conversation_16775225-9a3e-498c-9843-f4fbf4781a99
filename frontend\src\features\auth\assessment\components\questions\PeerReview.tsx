import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Rating,
  TextField,
  Button,
  Divider,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  Alert,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  RateReview,
  Send,
  Visibility,
  CheckCircle,
  Schedule,
  Person,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { PeerReviewConfig, ReviewCriterion } from '../../types';

interface PeerReviewProps {
  config: PeerReviewConfig;
  submissionId: string;
  onReviewSubmit: (review: PeerReviewData) => void;
  isSubmitting: boolean;
  value?: string;
}

interface PeerReviewData {
  submissionId: string;
  reviewerId: string;
  scores: Record<string, number>;
  comments: Record<string, string>;
  overallComment: string;
  overallRating: number;
}

interface PeerSubmission {
  id: string;
  authorName: string;
  isAnonymous: boolean;
  submittedAt: Date;
  files: FileData[];
  description?: string;
}

interface FileData {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
}

interface ExistingReview {
  id: string;
  reviewerName: string;
  isAnonymous: boolean;
  submittedAt: Date;
  scores: Record<string, number>;
  comments: Record<string, string>;
  overallComment: string;
  overallRating: number;
}

const PeerReview: React.FC<PeerReviewProps> = ({
  config,
  submissionId,
  onReviewSubmit,
  isSubmitting,
  value,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [reviewData, setReviewData] = useState<PeerReviewData>({
    submissionId,
    reviewerId: 'current-user', // In real app, get from auth context
    scores: {},
    comments: {},
    overallComment: '',
    overallRating: 0,
  });
  
  const [submission, setSubmission] = useState<PeerSubmission | null>(null);
  const [existingReviews, setExistingReviews] = useState<ExistingReview[]>([]);
  const [showSubmissionContent, setShowSubmissionContent] = useState(false);

  useEffect(() => {
    // Load submission data and existing reviews
    loadSubmissionData();
    
    // Parse existing review if editing
    if (value) {
      try {
        const parsed = JSON.parse(value);
        setReviewData(parsed);
      } catch {
        // Invalid JSON, ignore
      }
    }
  }, [submissionId, value]);

  const loadSubmissionData = useCallback(async () => {
    // Mock submission data - in real app, fetch from API
    const mockSubmission: PeerSubmission = {
      id: submissionId,
      authorName: config.anonymousReview ? 'Anonymous Student' : 'John Doe',
      isAnonymous: config.anonymousReview,
      submittedAt: new Date(),
      files: [
        {
          id: '1',
          name: 'research_paper.pdf',
          type: 'application/pdf',
          size: 2048000,
          url: '/mock/file1.pdf',
        },
        {
          id: '2',
          name: 'supporting_data.xlsx',
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          size: 512000,
          url: '/mock/file2.xlsx',
        },
      ],
      description: 'This is my final project submission for the course. I have included the main research paper and supporting data analysis.',
    };

    const mockReviews: ExistingReview[] = [
      {
        id: '1',
        reviewerName: config.anonymousReview ? 'Anonymous Reviewer 1' : 'Jane Smith',
        isAnonymous: config.anonymousReview,
        submittedAt: new Date(Date.now() - 86400000), // 1 day ago
        scores: {
          'content-quality': 4,
          'methodology': 3,
          'presentation': 5,
        },
        comments: {
          'content-quality': 'Good research topic with solid foundation',
          'methodology': 'Could improve data collection methods',
          'presentation': 'Excellent formatting and clear writing',
        },
        overallComment: 'Good work overall. The research is solid but could benefit from more rigorous methodology.',
        overallRating: 4,
      },
    ];

    setSubmission(mockSubmission);
    setExistingReviews(mockReviews);
  }, [submissionId, config.anonymousReview]);

  const handleScoreChange = useCallback(
    (criterionId: string, score: number) => {
      setReviewData(prev => ({
        ...prev,
        scores: { ...prev.scores, [criterionId]: score },
      }));
    },
    []
  );

  const handleCommentChange = useCallback(
    (criterionId: string, comment: string) => {
      setReviewData(prev => ({
        ...prev,
        comments: { ...prev.comments, [criterionId]: comment },
      }));
    },
    []
  );

  const handleOverallCommentChange = useCallback(
    (comment: string) => {
      setReviewData(prev => ({ ...prev, overallComment: comment }));
    },
    []
  );

  const handleOverallRatingChange = useCallback(
    (rating: number) => {
      setReviewData(prev => ({ ...prev, overallRating: rating }));
    },
    []
  );

  const handleSubmitReview = useCallback(() => {
    if (!isReviewComplete()) return;
    
    onReviewSubmit(reviewData);
  }, [reviewData, onReviewSubmit]);

  const isReviewComplete = useCallback(() => {
    const hasAllScores = config.reviewCriteria.every(
      criterion => reviewData.scores[criterion.id] > 0
    );
    const hasOverallRating = reviewData.overallRating > 0;
    const hasOverallComment = reviewData.overallComment.trim().length > 0;
    
    return hasAllScores && hasOverallRating && hasOverallComment;
  }, [config.reviewCriteria, reviewData]);

  const calculateWeightedScore = useCallback(() => {
    const totalWeight = config.reviewCriteria.reduce((sum, criterion) => sum + criterion.weight, 0);
    const weightedSum = config.reviewCriteria.reduce((sum, criterion) => {
      const score = reviewData.scores[criterion.id] || 0;
      return sum + (score * criterion.weight);
    }, 0);
    
    return totalWeight > 0 ? weightedSum / totalWeight : 0;
  }, [config.reviewCriteria, reviewData.scores]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!submission) {
    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="body2" color="text.secondary">
          {t('assessment.peerReview.loading')}
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ mt: 2 }}>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        {t('assessment.peerReview.instruction')}
      </Typography>

      {/* Submission Overview */}
      <Card elevation={1} sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar sx={{ mr: 2 }}>
              <Person />
            </Avatar>
            <Box>
              <Typography variant="h6">
                {submission.authorName}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {t('assessment.peerReview.submittedAt', {
                  date: submission.submittedAt.toLocaleDateString(),
                })}
              </Typography>
            </Box>
          </Box>

          {submission.description && (
            <Typography variant="body2" sx={{ mb: 2 }}>
              {submission.description}
            </Typography>
          )}

          <Typography variant="subtitle2" gutterBottom>
            {t('assessment.peerReview.attachments')} ({submission.files.length})
          </Typography>
          
          <List dense>
            {submission.files.map((file) => (
              <ListItem key={file.id} divider>
                <ListItemText
                  primary={file.name}
                  secondary={formatFileSize(file.size)}
                />
                <Button
                  startIcon={<Visibility />}
                  size="small"
                  onClick={() => window.open(file.url, '_blank')}
                >
                  {t('assessment.peerReview.view')}
                </Button>
              </ListItem>
            ))}
          </List>
        </CardContent>
        
        <CardActions>
          <Button
            startIcon={showSubmissionContent ? <Visibility /> : <Visibility />}
            onClick={() => setShowSubmissionContent(!showSubmissionContent)}
            size="small"
          >
            {showSubmissionContent
              ? t('assessment.peerReview.hideContent')
              : t('assessment.peerReview.showContent')}
          </Button>
        </CardActions>
      </Card>

      {/* Review Criteria */}
      <Typography variant="h6" gutterBottom>
        {t('assessment.peerReview.reviewCriteria')}
      </Typography>

      <Grid container spacing={2} sx={{ mb: 3 }}>
        {config.reviewCriteria.map((criterion) => (
          <Grid item xs={12} key={criterion.id}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                {criterion.name}
                <Chip
                  label={t('assessment.peerReview.weight', { weight: criterion.weight })}
                  size="small"
                  sx={{ ml: 1 }}
                />
              </Typography>
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {criterion.description}
              </Typography>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Typography variant="body2">
                  {t('assessment.peerReview.score')}:
                </Typography>
                <Rating
                  value={reviewData.scores[criterion.id] || 0}
                  onChange={(_, value) => handleScoreChange(criterion.id, value || 0)}
                  max={criterion.maxScore}
                  disabled={isSubmitting}
                />
                <Typography variant="caption" color="text.secondary">
                  ({reviewData.scores[criterion.id] || 0}/{criterion.maxScore})
                </Typography>
              </Box>

              <TextField
                fullWidth
                multiline
                rows={2}
                placeholder={t('assessment.peerReview.commentPlaceholder')}
                value={reviewData.comments[criterion.id] || ''}
                onChange={(e) => handleCommentChange(criterion.id, e.target.value)}
                disabled={isSubmitting}
                variant="outlined"
                size="small"
              />
            </Paper>
          </Grid>
        ))}
      </Grid>

      {/* Overall Review */}
      <Paper elevation={1} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          {t('assessment.peerReview.overallReview')}
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <Typography variant="body2">
            {t('assessment.peerReview.overallRating')}:
          </Typography>
          <Rating
            value={reviewData.overallRating}
            onChange={(_, value) => handleOverallRatingChange(value || 0)}
            disabled={isSubmitting}
          />
          <Typography variant="caption" color="text.secondary">
            {t('assessment.peerReview.weightedScore')}: {calculateWeightedScore().toFixed(1)}
          </Typography>
        </Box>

        <TextField
          fullWidth
          multiline
          rows={4}
          placeholder={t('assessment.peerReview.overallCommentPlaceholder')}
          value={reviewData.overallComment}
          onChange={(e) => handleOverallCommentChange(e.target.value)}
          disabled={isSubmitting}
          variant="outlined"
        />
      </Paper>

      {/* Existing Reviews */}
      {existingReviews.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            {t('assessment.peerReview.existingReviews')} ({existingReviews.length})
          </Typography>
          
          {existingReviews.map((review) => (
            <Paper key={review.id} elevation={1} sx={{ p: 2, mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Avatar size="small">
                    <RateReview />
                  </Avatar>
                  <Typography variant="subtitle2">
                    {review.reviewerName}
                  </Typography>
                  <Rating value={review.overallRating} readOnly size="small" />
                </Box>
                <Typography variant="caption" color="text.secondary">
                  {review.submittedAt.toLocaleDateString()}
                </Typography>
              </Box>
              
              <Typography variant="body2" sx={{ mb: 1 }}>
                {review.overallComment}
              </Typography>
              
              <Grid container spacing={1}>
                {config.reviewCriteria.map((criterion) => (
                  <Grid item key={criterion.id}>
                    <Chip
                      label={`${criterion.name}: ${review.scores[criterion.id] || 0}/${criterion.maxScore}`}
                      size="small"
                      variant="outlined"
                    />
                  </Grid>
                ))}
              </Grid>
            </Paper>
          ))}
        </Box>
      )}

      {/* Submit Review */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          {!isReviewComplete() && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              {t('assessment.peerReview.incompleteReview')}
            </Alert>
          )}
        </Box>
        
        <Button
          variant="contained"
          startIcon={<Send />}
          onClick={handleSubmitReview}
          disabled={!isReviewComplete() || isSubmitting}
        >
          {isSubmitting
            ? t('assessment.peerReview.submitting')
            : t('assessment.peerReview.submitReview')}
        </Button>
      </Box>
    </Box>
  );
};

export default PeerReview;

# Core Django and Web Framework
Django==4.2.7
djangorestframework==3.16.0
drf-nested-routers==0.94.2
django-cors-headers==4.3.1
django-extensions==3.2.3
channels==4.0.0
channels-redis==4.1.0

# Database
psycopg2-binary==2.9.7

# AI and Machine Learning - Core
google-generativeai==0.8.3
langchain==0.3.7
langchain-google-genai==2.0.4
langgraph==0.2.34
langchain-community==0.3.7

# AI and Machine Learning - Extended
scikit-learn==1.5.2
numpy==1.26.4
pandas==2.2.3
torch==2.5.1
transformers==4.46.3
sentence-transformers==3.3.1

# Custom ML Components
tensorflow==2.18.0
keras==3.7.0
xgboost==2.1.3
lightgbm==4.5.0

# Data Processing and Analysis
matplotlib==3.9.2
seaborn==0.13.2
plotly==5.24.1
scipy==1.14.1

# Utilities
python-dotenv==1.0.1
celery==5.4.0
redis==5.2.0
requests==2.32.3
Pillow==11.0.0

# Development and Testing
pytest==8.3.4
pytest-django==4.9.0
black==24.10.0
flake8==7.1.1

# Security
cryptography==41.0.7
django-ratelimit==4.1.0

# Additional AI Tools
# Only using Google Gemini - OpenAI and Anthropic removed

# Additional dependencies for enhanced functionality
pydantic==2.10.3
httpx==0.28.1
aiohttp==3.11.10

# Enhanced AI Service Dependencies
PyYAML==6.0.2  # For configuration files
dataclasses-json==0.6.7  # For structured data handling
asyncio-throttle==1.0.2  # For rate limiting
cachetools==5.5.0  # For advanced caching

# JWT Authentication
djangorestframework-simplejwt==5.3.0

# Production Server
gunicorn==21.2.0
whitenoise==6.6.0

"""AIServiceMonitoringandAnalyticsThismoduleprovidescomprehensivemonitoringmetricsandanalyticsforAIservices."""import loggingimport timefromtypingimportDictAnyListOptionalfromdataclassesimportdataclassasdictfromdatetimeimport datetimetimedeltafrom django.core.cacheimportcachefrom django.utilsimport timezoneimport jsonfromcollectionsimportdefaultdictdequelogger=logging.getLogger(__name__)@dataclassclassAIMetric:"""IndividualAIservicemetric"""timestamp:floatservice_type:stroperation:strduration:floatsuccess:boolerror_type:Optional[str]=Noneuser_id:Optional[str]=Nonemodel_used:Optional[str]=Nonetokens_used:Optional[int]=Nonecost:Optional[float]=NoneclassAIMetricsCollector:"""CollectsandaggregatesAIservicemetrics"""def__init__(selfmax_metrics:int=10000):self.max_metrics=max_metricsself.metrics=deque(maxlen=max_metrics)self.cache_key_prefix="ai_metrics"defrecord_metric(selfservice_type:stroperation:strduration:floatsuccess:boolerror_type:Optional[str]=Noneuser_id:Optional[str]=Nonemodel_used:Optional[str]=Nonetokens_used:Optional[int]=Nonecost:Optional[float]=None):"""Recordanewmetric"""metric=AIMetric(timestamp=time.time()service_type=service_typeoperation=operationduration=durationsuccess=successerror_type=error_typeuser_id=user_idmodel_used=model_usedtokens_used=tokens_usedcost=cost)self.metrics.append(metric)#Alsostoreincacheforpersistencecache_key=f"{self.cache_key_prefix}_{int(metric.timestamp)}"cache.set(cache_keyasdict(metric)timeout=86400)#24hourslogger.debug(f"RecordedAImetric:{service_type}.{operation}-{duration:.2f}s-{'success'ifsuccesselse'failure'}")defget_metrics_summary(selfhours:int=24)->Dict[strAny]:"""GetaggregatedmetricssummaryforthelastNhours"""cutoff_time=time.time()-(hours*3600)recent_metrics=[mforminself.metricsifm.timestamp>=cutoff_time]ifnotrecent_metrics:returnself._empty_summary()#Calculateaggregationstotal_requests=len(recent_metrics)successful_requests=sum(1forminrecent_metricsifm.success)failed_requests=total_requests-successful_requests#Servicetypebreakdownservice_breakdown=defaultdict(lambda:{'total':0'success':0'avg_duration':0})formetricinrecent_metrics:service_breakdown[metric.service_type]['total']+=1ifmetric.success:service_breakdown[metric.service_type]['success']+=1service_breakdown[metric.service_type]['avg_duration']+=metric.duration#Calculateaveragesforservice_datainservice_breakdown.values():ifservice_data['total']>0:service_data['avg_duration']/=service_data['total']service_data['success_rate']=service_data['success']/service_data['total']#Errorbreakdownerror_breakdown=defaultdict(int)formetricinrecent_metrics:ifnotmetric.successandmetric.error_type:error_breakdown[metric.error_type]+=1#Performancemetricsdurations=[m.durationforminrecent_metrics]avg_duration=sum(durations)/len(durations)ifdurationselse0max_duration=max(durations)ifdurationselse0min_duration=min(durations)ifdurationselse0#Costmetricstotal_cost=sum(m.costforminrecent_metricsifm.cost)total_tokens=sum(m.tokens_usedforminrecent_metricsifm.tokens_used)return{'period_hours':hours'total_requests':total_requests'successful_requests':successful_requests'failed_requests':failed_requests'success_rate':successful_requests/total_requestsiftotal_requests>0else0'performance':{'avg_duration':avg_duration'max_duration':max_duration'min_duration':min_duration}'service_breakdown':dict(service_breakdown)'error_breakdown':dict(error_breakdown)'cost_metrics':{'total_cost':total_cost'total_tokens':total_tokens'avg_cost_per_request':total_cost/total_requestsiftotal_requests>0andtotal_costelse0}'timestamp':time.time()}def_empty_summary(self)->Dict[strAny]:"""Returnemptysummarystructure"""return{'period_hours':0'total_requests':0'successful_requests':0'failed_requests':0'success_rate':0'performance':{'avg_duration':0'max_duration':0'min_duration':0}'service_breakdown':{}'error_breakdown':{}'cost_metrics':{'total_cost':0'total_tokens':0'avg_cost_per_request':0}'timestamp':time.time()}classAIPerformanceMonitor:"""MonitorsAIserviceperformanceandhealth"""def__init__(self):self.metrics_collector=AIMetricsCollector()self.alert_thresholds={'error_rate':0.1#10%errorrate'avg_response_time':30.0#30seconds'queue_size':100#100pendingrequests}deftrack_request(selfservice_type:stroperation:str):"""ContextmanagerfortrackingAIrequests"""returnAIRequestTracker(self.metrics_collectorservice_typeoperation)defget_health_status(self)->Dict[strAny]:"""GetcurrenthealthstatusofAIservices"""summary=self.metrics_collector.get_metrics_summary(hours=1)#Lasthourhealth_status={'status':'healthy''issues':[]'metrics':summary'timestamp':time.time()}#Checkerrorrateifsummary['success_rate']<(1-self.alert_thresholds['error_rate']):health_status['status']='degraded'health_status['issues'].append({'type':'high_error_rate''message':f"Errorrateis{(1-summary['success_rate'])*100:.1f}%"'threshold':f"{self.alert_thresholds['error_rate']*100}%"})#Checkresponsetimeifsummary['performance']['avg_duration']>self.alert_thresholds['avg_response_time']:health_status['status']='degraded'health_status['issues'].append({'type':'slow_response''message':f"Averageresponsetimeis{summary['performance']['avg_duration']:.1f}s"'threshold':f"{self.alert_thresholds['avg_response_time']}s"})#Checkifthereareanyrequestsinthelasthourifsummary['total_requests']==0:health_status['status']='unknown'health_status['issues'].append({'type':'no_activity''message':"NoAIrequestsinthelasthour"})returnhealth_statusdefget_usage_analytics(selfdays:int=7)->Dict[strAny]:"""Getdetailedusageanalytics"""summary=self.metrics_collector.get_metrics_summary(hours=days*24)#Additionalanalyticscouldbeaddedhere#Fornowreturnthesummarywithsomeadditionalcalculationsanalytics=summary.copy()#Calculaterequestsperdayifsummary['total_requests']>0:analytics['requests_per_day']=summary['total_requests']/daysanalytics['requests_per_hour']=summary['total_requests']/(days*24)else:analytics['requests_per_day']=0analytics['requests_per_hour']=0returnanalyticsclassAIRequestTracker:"""ContextmanagerfortrackingindividualAIrequests"""def__init__(selfmetrics_collector:AIMetricsCollectorservice_type:stroperation:str):self.metrics_collector=metrics_collectorself.service_type=service_typeself.operation=operationself.start_time=Noneself.user_id=Noneself.model_used=Noneself.tokens_used=Noneself.cost=Nonedef__enter__(self):self.start_time=time.time()returnselfdef__exit__(selfexc_typeexc_valexc_tb):duration=time.time()-self.start_timesuccess=exc_typeisNoneerror_type=exc_type.__name__ifexc_typeelseNoneself.metrics_collector.record_metric(service_type=self.service_typeoperation=self.operationduration=durationsuccess=successerror_type=error_typeuser_id=self.user_idmodel_used=self.model_usedtokens_used=self.tokens_usedcost=self.cost)defset_metadata(selfuser_id:Optional[str]=Nonemodel_used:Optional[str]=Nonetokens_used:Optional[int]=Nonecost:Optional[float]=None):"""Setadditionalmetadatafortherequest"""self.user_id=user_idself.model_used=model_usedself.tokens_used=tokens_usedself.cost=cost#Globalmonitoringinstanceai_monitor=AIPerformanceMonitor()
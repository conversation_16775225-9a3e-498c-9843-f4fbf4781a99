/**
 * Central Personalization Service
 * 
 * Orchestrates all personalization engines and provides unified interface
 */

import { 
  EmotionalLearningState, 
  VoiceLearningPreferences, 
  VisualLearningPreferences,
  MicroLearningPreferences,
  StudentProfile 
} from './personalizedLearningService';
import emotionalIntelligenceService from './emotionalIntelligenceService';

interface PersonalizationContext {
  userId: string;
  deviceType: 'mobile' | 'tablet' | 'desktop';
  timeOfDay: number;
  availableTime: number;
  networkQuality: 'high' | 'medium' | 'low';
}

class PersonalizationService {
  private userProfiles: Map<string, StudentProfile> = new Map();

  /**
   * Apply comprehensive personalization to content
   */
  async personalizeContent(
    content: string,
    userId: string,
    context: PersonalizationContext,
    interactionHistory: any[] = []
  ): Promise<string> {
    try {
      let personalizedContent = content;

      // Get user profile
      const profile = await this.getUserProfile(userId);
      if (!profile) {
        return content; // Return original if no profile
      }

      // 1. Emotional adaptation
      if (interactionHistory.length > 0) {
        const emotionalState = await emotionalIntelligenceService.detectEmotionalState(
          userId, 
          interactionHistory
        );
        personalizedContent = await emotionalIntelligenceService.adaptContentToEmotion(
          personalizedContent, 
          emotionalState
        );
      }

      // 2. Micro-learning chunking
      if (profile.micro_learning_preferences) {
        personalizedContent = this.chunkContent(
          personalizedContent, 
          profile.micro_learning_preferences
        );
      }

      // 3. Visual adaptation markers
      if (profile.visual_preferences) {
        personalizedContent = this.addVisualAdaptationMarkers(
          personalizedContent, 
          profile.visual_preferences
        );
      }

      // 4. Context-based adaptation
      personalizedContent = this.adaptToContext(personalizedContent, context);

      return personalizedContent;

    } catch (error) {
      console.warn('Personalization failed, returning original content:', error);
      return content;
    }
  }

  /**
   * Get or create user profile
   */
  private async getUserProfile(userId: string): Promise<StudentProfile | null> {
    try {
      // In a real implementation, this would fetch from your existing service
      // For now, return a cached profile or null
      return this.userProfiles.get(userId) || null;
    } catch (error) {
      console.warn('Failed to get user profile:', error);
      return null;
    }
  }

  /**
   * Cache user profile for quick access
   */
  setUserProfile(userId: string, profile: StudentProfile): void {
    this.userProfiles.set(userId, profile);
  }

  /**
   * Break content into micro-learning chunks
   */
  private chunkContent(
    content: string, 
    preferences: MicroLearningPreferences
  ): string {
    const { optimal_chunk_duration, chunking_strategy } = preferences;
    
    if (chunking_strategy === 'concept_based') {
      return this.chunkByConcepts(content);
    }
    
    if (chunking_strategy === 'time_based') {
      return this.chunkByTime(content, optimal_chunk_duration);
    }
    
    return content; // No chunking for 'adaptive' strategy in this simple implementation
  }

  private chunkByConcepts(content: string): string {
    // Split by paragraphs and add chunk separators
    const paragraphs = content.split('\n\n');
    if (paragraphs.length > 2) {
      return paragraphs.map((para, index) => 
        `**📚 Concept ${index + 1}:**\n\n${para}`
      ).join('\n\n---\n\n');
    }
    return content;
  }

  private chunkByTime(content: string, targetMinutes: number): string {
    // Estimate reading time (average 200 words per minute)
    const words = content.split(' ').length;
    const estimatedMinutes = words / 200;
    
    if (estimatedMinutes > targetMinutes) {
      const chunks = Math.ceil(estimatedMinutes / targetMinutes);
      const wordsPerChunk = Math.floor(words / chunks);
      
      const contentWords = content.split(' ');
      const chunkedContent = [];
      
      for (let i = 0; i < chunks; i++) {
        const start = i * wordsPerChunk;
        const end = Math.min((i + 1) * wordsPerChunk, contentWords.length);
        const chunk = contentWords.slice(start, end).join(' ');
        chunkedContent.push(`**⏱️ Part ${i + 1} of ${chunks}** (${targetMinutes} min read)\n\n${chunk}`);
      }
      
      return chunkedContent.join('\n\n---\n\n');
    }
    
    return content;
  }

  /**
   * Add visual adaptation markers for UI components to process
   */
  private addVisualAdaptationMarkers(
    content: string, 
    preferences: VisualLearningPreferences
  ): string {
    let adaptedContent = content;
    
    // Add theme markers
    adaptedContent = `[THEME:${preferences.color_scheme}]\n${adaptedContent}`;
    
    // Add font size markers
    if (preferences.font_size_multiplier !== 1) {
      adaptedContent = `[FONT_SIZE:${preferences.font_size_multiplier}]\n${adaptedContent}`;
    }
    
    // Add animation preference markers
    adaptedContent = `[ANIMATION:${preferences.animation_preference}]\n${adaptedContent}`;
    
    return adaptedContent;
  }

  /**
   * Adapt content based on current context
   */
  private adaptToContext(content: string, context: PersonalizationContext): string {
    let adaptedContent = content;
    
    // Mobile optimization
    if (context.deviceType === 'mobile') {
      adaptedContent = this.optimizeForMobile(adaptedContent);
    }
    
    // Time-based adaptation
    if (context.availableTime < 10) {
      adaptedContent = this.createQuickVersion(adaptedContent);
    }
    
    // Network optimization
    if (context.networkQuality === 'low') {
      adaptedContent = this.optimizeForLowBandwidth(adaptedContent);
    }
    
    return adaptedContent;
  }

  private optimizeForMobile(content: string): string {
    // Add mobile-friendly formatting
    return `📱 **Mobile Optimized**\n\n${content}`;
  }

  private createQuickVersion(content: string): string {
    // Create a condensed version for time-constrained users
    const sentences = content.split('. ');
    if (sentences.length > 3) {
      const keySentences = sentences.slice(0, 2);
      return `⚡ **Quick Summary:**\n\n${keySentences.join('. ')}.\n\n💡 *Want the full explanation? Let me know!*`;
    }
    return content;
  }

  private optimizeForLowBandwidth(content: string): string {
    // Remove heavy formatting for low bandwidth
    return content.replace(/[📚📱⚡💡🌟🎮🏆💙🤗✨⏱️]/g, '');
  }

  /**
   * Get TTS options based on voice preferences
   */
  getTTSOptions(preferences: VoiceLearningPreferences) {
    return {
      rate: preferences.preferred_speech_rate / 200, // Normalize to 0-1 range
      pitch: 1,
      volume: preferences.volume_preference,
      voice: preferences.preferred_voice_type,
      lang: preferences.accent_preference === 'uk' ? 'en-GB' : 'en-US'
    };
  }

  /**
   * Apply visual theme to document
   */
  applyVisualTheme(preferences: VisualLearningPreferences): void {
    try {
      const root = document.documentElement;
      
      // Apply color scheme
      root.setAttribute('data-color-scheme', preferences.color_scheme);
      
      // Apply font size
      root.style.setProperty('--font-size-multiplier', preferences.font_size_multiplier.toString());
      
      // Apply animation preference
      root.setAttribute('data-animation-preference', preferences.animation_preference);
      
      // Apply layout preference
      root.setAttribute('data-layout-preference', preferences.layout_preference);
      
    } catch (error) {
      console.warn('Failed to apply visual theme:', error);
    }
  }

  /**
   * Speak text using TTS with user preferences
   */
  async speakText(text: string, preferences: VoiceLearningPreferences): Promise<void> {
    if (!preferences.voice_enabled || !('speechSynthesis' in window)) {
      return;
    }

    try {
      // Cancel any ongoing speech
      speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);
      const options = this.getTTSOptions(preferences);
      
      utterance.rate = options.rate;
      utterance.pitch = options.pitch;
      utterance.volume = options.volume;
      utterance.lang = options.lang;

      // Try to find a voice that matches preferences
      const voices = speechSynthesis.getVoices();
      const preferredVoice = voices.find(voice => 
        voice.lang.startsWith(options.lang) && 
        voice.name.toLowerCase().includes(options.voice)
      );
      
      if (preferredVoice) {
        utterance.voice = preferredVoice;
      }

      speechSynthesis.speak(utterance);
    } catch (error) {
      console.warn('Text-to-speech failed:', error);
    }
  }
}

export const personalizationService = new PersonalizationService();
export default personalizationService;

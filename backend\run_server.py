#!/usr/bin/env python
"""Simple server runner that bypasses Django's migration checks"""
import os
import logging
import django
from django.core.wsgi import get_wsgi_application
from wsgiref import simple_server

def main():
    # Set up Django
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings.development")
    os.environ.setdefault("DJANGO_SECRET_KEY", "^_@t=)n2bbo11w%h(^-w=&cg=yxle68so2!5t$7pxm6@c$o+ul")
    
    # Configure logging to reduce noise
    logging.getLogger('blockchain_credentials').setLevel(logging.ERROR)
    logging.getLogger('ipfshttpclient').setLevel(logging.ERROR)
    
    django.setup()
    
    # Get WSGI application
    application = get_wsgi_application()
    
    # Create and run server
    host = '127.0.0.1'
    port = 8000
    
    server = simple_server.make_server(host, port, application)
    print(f"Starting Django backend server on http://{host}:{port}/")
    print("Press Ctrl+C to quit")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\nShutting down server...")
        server.server_close()

if __name__ == "__main__":
    main()

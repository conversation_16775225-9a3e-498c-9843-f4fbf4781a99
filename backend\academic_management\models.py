"""
Academic Management Models

This module contains comprehensive academic management models including:
- Student Transcript System
- Academic Calendar Integration
- Prerequisites Enforcement
- Academic Probation/Standing with GPA Tracking
- Course Waitlists and Enrollment Queue System
"""

import decimal
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db import models, transaction
from django.db.models import Avg, Sum, Count, Q, F
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from core.models import BaseModel
from courses.models import Course, Enrollment
from grades.models import CourseGrade

User = get_user_model()


class AcademicTerm(BaseModel):
    """
    Academic calendar management for semesters/terms.
    """
    TERM_TYPE_CHOICES = [
        ('FALL', _('Fall Semester')),
        ('SPRING', _('Spring Semester')),
        ('SUMMER', _('Summer Session')),
        ('WINTER', _('Winter Session')),
        ('INTERSESSION', _('Intersession')),
    ]
    
    STATUS_CHOICES = [
        ('FUTURE', _('Future')),
        ('CURRENT', _('Current')),
        ('PAST', _('Past')),
        ('REGISTRATION_OPEN', _('Registration Open')),
        ('REGISTRATION_CLOSED', _('Registration Closed')),
    ]
    
    name = models.CharField(
        max_length=100,
        help_text=_("e.g., Fall 2024, Spring 2025")
    )
    term_type = models.CharField(max_length=20, choices=TERM_TYPE_CHOICES)
    academic_year = models.CharField(
        max_length=9,
        help_text=_("Format: YYYY-YYYY (e.g., 2024-2025)")
    )
    
    # Important dates
    start_date = models.DateField()
    end_date = models.DateField()
    registration_start = models.DateTimeField()
    registration_end = models.DateTimeField()
    add_drop_deadline = models.DateField()
    withdrawal_deadline = models.DateField()
    final_grades_due = models.DateTimeField()
    
    # Status
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='FUTURE'
    )
    is_active = models.BooleanField(default=True)
    
    # Settings
    max_credits_per_student = models.PositiveIntegerField(
        default=18,
        help_text=_("Maximum credits a student can take this term")
    )
    min_credits_full_time = models.PositiveIntegerField(
        default=12,
        help_text=_("Minimum credits to be considered full-time")
    )
    
    class Meta:
        verbose_name = _("Academic Term")
        verbose_name_plural = _("Academic Terms")
        ordering = ['-start_date']
        indexes = [
            models.Index(fields=['academic_year', 'term_type']),
            models.Index(fields=['status', 'is_active']),
            models.Index(fields=['start_date', 'end_date']),
        ]
        constraints = [
            models.CheckConstraint(
                check=Q(end_date__gt=F('start_date')),
                name='term_end_after_start'
            ),
            models.CheckConstraint(
                check=Q(registration_end__gt=F('registration_start')),
                name='registration_end_after_start'
            ),
        ]
    
    def __str__(self):
        return self.name
    
    def clean(self):
        """Validate term dates and settings."""
        if self.start_date and self.end_date and self.start_date >= self.end_date:
            raise ValidationError(_("End date must be after start date"))
        
        if (self.registration_start and self.registration_end and 
            self.registration_start >= self.registration_end):
            raise ValidationError(_("Registration end must be after registration start"))
        
        if self.max_credits_per_student < self.min_credits_full_time:
            raise ValidationError(_("Max credits must be >= min credits for full-time"))
    
    def is_current_term(self) -> bool:
        """Check if this is the current active term."""
        today = timezone.now().date()
        return self.start_date <= today <= self.end_date and self.is_active
    
    def is_registration_open(self) -> bool:
        """Check if registration is currently open."""
        now = timezone.now()
        return (self.registration_start <= now <= self.registration_end and 
                self.status == 'REGISTRATION_OPEN')
    
    def can_add_drop(self) -> bool:
        """Check if add/drop is still allowed."""
        today = timezone.now().date()
        return today <= self.add_drop_deadline
    
    def can_withdraw(self) -> bool:
        """Check if withdrawal is still allowed."""
        today = timezone.now().date()
        return today <= self.withdrawal_deadline
    
    @classmethod
    def get_current_term(cls):
        """Get the current active term."""
        return cls.objects.filter(status='CURRENT', is_active=True).first()
    
    @classmethod
    def get_registration_term(cls):
        """Get the term currently open for registration."""
        return cls.objects.filter(status='REGISTRATION_OPEN', is_active=True).first()


class GradePointValue(BaseModel):
    """
    GPA calculation values for different grade types.
    """
    grade = models.CharField(max_length=2, unique=True)
    points = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(4.0)]
    )
    description = models.CharField(max_length=100, blank=True)
    
    class Meta:
        verbose_name = _("Grade Point Value")
        verbose_name_plural = _("Grade Point Values")
        ordering = ['-points']
    
    def __str__(self):
        return f"{self.grade}: {self.points} points"


class StudentAcademicRecord(BaseModel):
    """
    Comprehensive academic record for each student including GPA tracking.
    """
    ACADEMIC_STANDING_CHOICES = [
        ('GOOD', _('Good Standing')),
        ('PROBATION', _('Academic Probation')),
        ('SUSPENSION', _('Academic Suspension')),
        ('DISMISSAL', _('Academic Dismissal')),
        ('HONORS', _('Dean\'s List/Honors')),
        ('HIGH_HONORS', _('High Honors')),
    ]
    
    ENROLLMENT_STATUS_CHOICES = [
        ('FULL_TIME', _('Full-time')),
        ('PART_TIME', _('Part-time')),
        ('LEAVE', _('Leave of Absence')),
        ('WITHDRAWN', _('Withdrawn')),
        ('GRADUATED', _('Graduated')),
    ]
    
    student = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='academic_record'
    )
    
    # Student Information
    official_student_id = models.CharField(
        max_length=20,
        unique=True,
        help_text=_("Official student ID number")
    )
    entry_term = models.ForeignKey(
        AcademicTerm,
        on_delete=models.PROTECT,
        related_name='entering_students'
    )
    expected_graduation = models.DateField(null=True, blank=True)
    actual_graduation = models.DateField(null=True, blank=True)
    
    # Academic Standing
    current_standing = models.CharField(
        max_length=20,
        choices=ACADEMIC_STANDING_CHOICES,
        default='GOOD'
    )
    enrollment_status = models.CharField(
        max_length=20,
        choices=ENROLLMENT_STATUS_CHOICES,
        default='FULL_TIME'
    )
    
    # GPA Tracking
    cumulative_gpa = models.DecimalField(
        max_digits=4,
        decimal_places=3,
        default=decimal.Decimal('0.000'),
        validators=[MinValueValidator(0), MaxValueValidator(4.0)]
    )
    term_gpa = models.DecimalField(
        max_digits=4,
        decimal_places=3,
        default=decimal.Decimal('0.000'),
        validators=[MinValueValidator(0), MaxValueValidator(4.0)]
    )
    
    # Credit Hours
    total_credits_attempted = models.PositiveIntegerField(default=0)
    total_credits_earned = models.PositiveIntegerField(default=0)
    term_credits_attempted = models.PositiveIntegerField(default=0)
    term_credits_earned = models.PositiveIntegerField(default=0)
    
    # Academic Status Tracking
    probation_start_term = models.ForeignKey(
        AcademicTerm,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='probation_students'
    )
    consecutive_probation_terms = models.PositiveIntegerField(default=0)
    
    # Honors Recognition
    dean_list_terms = models.ManyToManyField(
        AcademicTerm,
        blank=True,
        related_name='dean_list_students'
    )
    
    class Meta:
        verbose_name = _("Student Academic Record")
        verbose_name_plural = _("Student Academic Records")
        indexes = [
            models.Index(fields=['student_id']),
            models.Index(fields=['current_standing']),
            models.Index(fields=['cumulative_gpa']),
            models.Index(fields=['enrollment_status']),
        ]
    
    def __str__(self):
        return f"{self.student.get_full_name()} - {self.student_id}"
    
    def calculate_gpa(self, term: Optional[AcademicTerm] = None) -> decimal.Decimal:
        """
        Calculate GPA for a specific term or cumulative.
        
        Args:
            term: Specific term to calculate GPA for. If None, calculates cumulative GPA.
            
        Returns:
            Calculated GPA as Decimal
        """
        # Get course grades for calculation
        grades_query = CourseGrade.objects.filter(user=self.student)
        
        if term:
            # Filter for specific term
            grades_query = grades_query.filter(course__term=term)
        
        # Get grades with point values
        total_points = decimal.Decimal('0.0')
        total_credits = 0
        
        for course_grade in grades_query.select_related('course'):
            try:
                grade_points = GradePointValue.objects.get(grade=course_grade.grade)
                credits = course_grade.course.credits
                total_points += grade_points.points * credits
                total_credits += credits
            except GradePointValue.DoesNotExist:
                continue  # Skip grades without point values (W, I, etc.)
        
        if total_credits == 0:
            return decimal.Decimal('0.000')
        
        return round(total_points / total_credits, 3)
    
    def update_gpa(self, term: Optional[AcademicTerm] = None):
        """Update GPA calculations and academic standing."""
        self.cumulative_gpa = self.calculate_gpa()
        
        if term:
            self.term_gpa = self.calculate_gpa(term)
        else:
            # Use current term
            current_term = AcademicTerm.get_current_term()
            if current_term:
                self.term_gpa = self.calculate_gpa(current_term)
        
        # Update academic standing based on GPA
        self.update_academic_standing()
        self.save()
    
    def update_academic_standing(self):
        """Update academic standing based on GPA and other factors."""
        gpa = self.cumulative_gpa
        
        if gpa >= decimal.Decimal('3.8'):
            self.current_standing = 'HIGH_HONORS'
            self.consecutive_probation_terms = 0
        elif gpa >= decimal.Decimal('3.5'):
            self.current_standing = 'HONORS'
            self.consecutive_probation_terms = 0
        elif gpa >= decimal.Decimal('2.0'):
            self.current_standing = 'GOOD'
            self.consecutive_probation_terms = 0
        elif gpa >= decimal.Decimal('1.5'):
            self.current_standing = 'PROBATION'
            if self.current_standing != 'PROBATION':
                self.probation_start_term = AcademicTerm.get_current_term()
                self.consecutive_probation_terms = 1
            else:
                self.consecutive_probation_terms += 1
        else:
            if self.consecutive_probation_terms >= 2:
                self.current_standing = 'SUSPENSION'
            elif self.consecutive_probation_terms >= 3:
                self.current_standing = 'DISMISSAL'
            else:
                self.current_standing = 'PROBATION'
    
    def is_eligible_for_enrollment(self) -> bool:
        """Check if student is eligible to enroll in courses."""
        return self.current_standing not in ['SUSPENSION', 'DISMISSAL']
    
    def get_credit_limit(self, term: AcademicTerm) -> int:
        """Get maximum credits student can take in a term."""
        if self.current_standing == 'PROBATION':
            return min(term.max_credits_per_student, 12)  # Limited credits on probation
        return term.max_credits_per_student


class TranscriptRecord(BaseModel):
    """
    Individual course record for student transcripts.
    """
    student_record = models.ForeignKey(
        StudentAcademicRecord,
        on_delete=models.CASCADE,
        related_name='transcript_records'
    )
    term = models.ForeignKey(AcademicTerm, on_delete=models.PROTECT)
    course = models.ForeignKey('courses.Course', on_delete=models.PROTECT)
    
    # Grade Information
    grade = models.CharField(max_length=2, blank=True)
    grade_points = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        null=True,
        blank=True
    )
    credits_attempted = models.PositiveIntegerField()
    credits_earned = models.PositiveIntegerField(default=0)
    
    # Status Flags
    is_repeat = models.BooleanField(default=False)
    is_transfer = models.BooleanField(default=False)
    is_audit = models.BooleanField(default=False)
    
    # Metadata
    instructor = models.CharField(max_length=200, blank=True)
    comments = models.TextField(blank=True)
    
    class Meta:
        verbose_name = _("Transcript Record")
        verbose_name_plural = _("Transcript Records")
        unique_together = ['student_record', 'term', 'course']
        ordering = ['-term__start_date', 'course__course_code']
        indexes = [
            models.Index(fields=['student_record', 'term']),
            models.Index(fields=['course', 'grade']),
        ]
    
    def __str__(self):
        return f"{self.student_record.student.get_full_name()} - {self.course.course_code} ({self.term.name})"
    
    def save(self, *args, **kwargs):
        """Automatically calculate grade points and credits earned."""
        if self.grade and not self.is_audit:
            try:
                grade_value = GradePointValue.objects.get(grade=self.grade)
                self.grade_points = grade_value.points
                # Credits earned only if passing grade
                if grade_value.points >= decimal.Decimal('1.0'):
                    self.credits_earned = self.credits_attempted
                else:
                    self.credits_earned = 0
            except GradePointValue.DoesNotExist:
                self.grade_points = None
                self.credits_earned = 0
        
        super().save(*args, **kwargs)


class CoursePrerequisite(BaseModel):
    """
    Enhanced prerequisite system with multiple types of requirements.
    """
    PREREQUISITE_TYPE_CHOICES = [
        ('COURSE', _('Course Prerequisite')),
        ('GPA', _('GPA Requirement')),
        ('CREDITS', _('Credit Hours Requirement')),
        ('STANDING', _('Academic Standing Requirement')),
        ('PERMISSION', _('Instructor Permission')),
        ('PLACEMENT', _('Placement Test')),
    ]
    
    course = models.ForeignKey(
        'courses.Course',
        on_delete=models.CASCADE,
        related_name='prerequisite_requirements'
    )
    prerequisite_type = models.CharField(max_length=20, choices=PREREQUISITE_TYPE_CHOICES)
    
    # Course prerequisite
    prerequisite_course = models.ForeignKey(
        'courses.Course',
        on_delete=models.CASCADE,
        related_name='course_prerequisites',
        null=True,
        blank=True,
        help_text="The course that must be completed as a prerequisite"
    )
    minimum_grade = models.CharField(
        max_length=2,
        default='D-',
        help_text=_("Minimum grade required in prerequisite course")
    )
    
    # GPA requirement
    minimum_gpa = models.DecimalField(
        max_digits=4,
        decimal_places=3,
        null=True,
        blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(4.0)]
    )
    
    # Credit hours requirement
    minimum_credits = models.PositiveIntegerField(null=True, blank=True)
    
    # Academic standing requirement
    required_standing = models.CharField(
        max_length=20,
        choices=StudentAcademicRecord.ACADEMIC_STANDING_CHOICES,
        null=True,
        blank=True
    )
    
    # Other requirements
    description = models.TextField(
        blank=True,
        help_text=_("Additional requirement description")
    )
    is_corequisite = models.BooleanField(
        default=False,
        help_text=_("Can be taken concurrently with this course")
    )
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = _("Course Prerequisite")
        verbose_name_plural = _("Course Prerequisites")
        indexes = [
            models.Index(fields=['course', 'prerequisite_type']),
            models.Index(fields=['prerequisite_course']),
        ]
    
    def __str__(self):
        if self.prerequisite_type == 'COURSE':
            return f"{self.course.course_code} requires {self.prerequisite_course.course_code}"
        return f"{self.course.course_code} requires {self.get_prerequisite_type_display()}"
    
    def check_prerequisite(self, student: User) -> Dict[str, Any]:
        """
        Check if student meets this prerequisite requirement.
        
        Returns:
            Dict with 'met' (bool) and 'details' (str) keys
        """
        try:
            academic_record = student.academic_record
        except StudentAcademicRecord.DoesNotExist:
            return {'met': False, 'details': 'No academic record found'}
        
        if self.prerequisite_type == 'COURSE':
            return self._check_course_prerequisite(student)
        elif self.prerequisite_type == 'GPA':
            return self._check_gpa_requirement(academic_record)
        elif self.prerequisite_type == 'CREDITS':
            return self._check_credits_requirement(academic_record)
        elif self.prerequisite_type == 'STANDING':
            return self._check_standing_requirement(academic_record)
        else:
            return {'met': False, 'details': 'Manual verification required'}
    
    def _check_course_prerequisite(self, student: User) -> Dict[str, Any]:
        """Check if student has completed prerequisite course with minimum grade."""
        if not self.prerequisite_course:
            return {'met': True, 'details': 'No course prerequisite specified'}
        
        # Check if student has completed the prerequisite course
        try:
            course_grade = CourseGrade.objects.get(
                user=student,
                course=self.prerequisite_course
            )
            
            # Get grade point values for comparison
            try:
                student_grade_points = GradePointValue.objects.get(grade=course_grade.grade)
                min_grade_points = GradePointValue.objects.get(grade=self.minimum_grade)
                
                if student_grade_points.points >= min_grade_points.points:
                    return {
                        'met': True,
                        'details': f'Completed {self.prerequisite_course.course_code} with grade {course_grade.grade}'
                    }
                else:
                    return {
                        'met': False,
                        'details': f'Grade {course_grade.grade} in {self.prerequisite_course.course_code} below minimum {self.minimum_grade}'
                    }
            except GradePointValue.DoesNotExist:
                return {'met': False, 'details': 'Unable to verify grade requirements'}
                
        except CourseGrade.DoesNotExist:
            return {
                'met': False,
                'details': f'Must complete {self.prerequisite_course.course_code} with minimum grade {self.minimum_grade}'
            }
    
    def _check_gpa_requirement(self, academic_record: StudentAcademicRecord) -> Dict[str, Any]:
        """Check if student meets GPA requirement."""
        if academic_record.cumulative_gpa >= self.minimum_gpa:
            return {
                'met': True,
                'details': f'GPA {academic_record.cumulative_gpa} meets minimum {self.minimum_gpa}'
            }
        else:
            return {
                'met': False,
                'details': f'GPA {academic_record.cumulative_gpa} below minimum {self.minimum_gpa}'
            }
    
    def _check_credits_requirement(self, academic_record: StudentAcademicRecord) -> Dict[str, Any]:
        """Check if student has earned minimum credit hours."""
        if academic_record.total_credits_earned >= self.minimum_credits:
            return {
                'met': True,
                'details': f'{academic_record.total_credits_earned} credits earned (minimum {self.minimum_credits})'
            }
        else:
            return {
                'met': False,
                'details': f'Need {self.minimum_credits - academic_record.total_credits_earned} more credits'
            }
    
    def _check_standing_requirement(self, academic_record: StudentAcademicRecord) -> Dict[str, Any]:
        """Check if student meets academic standing requirement."""
        if academic_record.current_standing == self.required_standing:
            return {
                'met': True,
                'details': f'Academic standing: {academic_record.get_current_standing_display()}'
            }
        else:
            return {
                'met': False,
                'details': f'Requires {self.get_required_standing_display()} standing'
            }


class CourseWaitlist(BaseModel):
    """
    Course waitlist and enrollment queue system.
    """
    PRIORITY_CHOICES = [
        (1, _('High Priority (Seniors)')),
        (2, _('Medium-High Priority (Juniors)')),
        (3, _('Medium Priority (Sophomores)')),
        (4, _('Low Priority (Freshmen)')),
        (5, _('Lowest Priority (Others)')),
    ]
    
    STATUS_CHOICES = [
        ('WAITING', _('Waiting')),
        ('NOTIFIED', _('Notified of Opening')),
        ('ENROLLED', _('Successfully Enrolled')),
        ('EXPIRED', _('Notification Expired')),
        ('REMOVED', _('Removed from Waitlist')),
    ]
    
    course = models.ForeignKey(
        'courses.Course',
        on_delete=models.CASCADE,
        related_name='waitlist_entries'
    )
    student = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='waitlist_entries'
    )
    term = models.ForeignKey(AcademicTerm, on_delete=models.CASCADE)
    
    # Waitlist Information
    position = models.PositiveIntegerField()
    priority = models.PositiveIntegerField(choices=PRIORITY_CHOICES, default=5)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='WAITING')
    
    # Timestamps
    added_at = models.DateTimeField(auto_now_add=True)
    notified_at = models.DateTimeField(null=True, blank=True)
    response_deadline = models.DateTimeField(null=True, blank=True)
    enrolled_at = models.DateTimeField(null=True, blank=True)
    
    # Metadata
    notification_method = models.CharField(
        max_length=20,
        choices=[('EMAIL', 'Email'), ('SMS', 'SMS'), ('BOTH', 'Both')],
        default='EMAIL'
    )
    auto_enroll = models.BooleanField(
        default=False,
        help_text=_("Automatically enroll when spot becomes available")
    )
    
    class Meta:
        verbose_name = _("Course Waitlist Entry")
        verbose_name_plural = _("Course Waitlist Entries")
        unique_together = ['course', 'student', 'term']
        ordering = ['priority', 'added_at']
        indexes = [
            models.Index(fields=['course', 'term', 'status']),
            models.Index(fields=['student', 'status']),
            models.Index(fields=['priority', 'added_at']),
        ]
    
    def __str__(self):
        return f"{self.student.get_full_name()} - {self.course.course_code} (Position {self.position})"
    
    def save(self, *args, **kwargs):
        """Auto-assign position if not set."""
        if not self.position:
            last_position = CourseWaitlist.objects.filter(
                course=self.course,
                term=self.term
            ).aggregate(Max('position'))['position__max'] or 0
            self.position = last_position + 1
        
        super().save(*args, **kwargs)
    
    def notify_of_opening(self, hours_to_respond: int = 24):
        """Notify student of course opening."""
        self.status = 'NOTIFIED'
        self.notified_at = timezone.now()
        self.response_deadline = timezone.now() + timedelta(hours=hours_to_respond)
        self.save()
        
        # TODO: Send actual notification (email/SMS)
        # This would integrate with notification system
        
    def enroll_student(self):
        """Enroll student and update status."""
        from courses.models import Enrollment
        
        # Create enrollment
        enrollment = Enrollment.objects.create(
            user=self.student,
            course=self.course,
            status='APPROVED',
            enrollment_type='REGULAR'
        )
        
        # Update waitlist status
        self.status = 'ENROLLED'
        self.enrolled_at = timezone.now()
        self.save()
        
        # Move up remaining waitlist positions
        CourseWaitlist.objects.filter(
            course=self.course,
            term=self.term,
            position__gt=self.position,
            status='WAITING'
        ).update(position=F('position') - 1)
        
        return enrollment
    
    def remove_from_waitlist(self):
        """Remove student from waitlist and adjust positions."""
        old_position = self.position
        self.status = 'REMOVED'
        self.save()
        
        # Move up remaining waitlist positions
        CourseWaitlist.objects.filter(
            course=self.course,
            term=self.term,
            position__gt=old_position,
            status='WAITING'
        ).update(position=F('position') - 1)
    
    @classmethod
    def process_expired_notifications(cls):
        """Process waitlist entries with expired notifications."""
        expired_entries = cls.objects.filter(
            status='NOTIFIED',
            response_deadline__lt=timezone.now()
        )
        
        for entry in expired_entries:
            entry.status = 'EXPIRED'
            entry.save()
            
            # Notify next person on waitlist
            next_entry = cls.objects.filter(
                course=entry.course,
                term=entry.term,
                status='WAITING',
                position__gt=entry.position
            ).first()
            
            if next_entry:
                next_entry.notify_of_opening()
    
    @classmethod
    def get_student_position(cls, course, student, term):
        """Get student's position on waitlist."""
        try:
            entry = cls.objects.get(
                course=course,
                student=student,
                term=term,
                status__in=['WAITING', 'NOTIFIED']
            )
            return entry.position
        except cls.DoesNotExist:
            return None


class EnrollmentHistory(BaseModel):
    """
    Track enrollment changes and history for auditing.
    """
    ACTION_CHOICES = [
        ('ENROLLED', _('Enrolled')),
        ('DROPPED', _('Dropped')),
        ('WITHDRAWN', _('Withdrawn')),
        ('WAITLISTED', _('Added to Waitlist')),
        ('REMOVED_WAITLIST', _('Removed from Waitlist')),
        ('GRADE_ASSIGNED', _('Grade Assigned')),
    ]
    
    student = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='enrollment_history'
    )
    course = models.ForeignKey('courses.Course', on_delete=models.CASCADE)
    term = models.ForeignKey(AcademicTerm, on_delete=models.CASCADE)
    
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    action_date = models.DateTimeField(auto_now_add=True)
    performed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='enrollment_actions_performed'
    )
    
    # Additional data
    reason = models.TextField(blank=True)
    metadata = models.JSONField(default=dict, blank=True)
    
    class Meta:
        verbose_name = _("Enrollment History")
        verbose_name_plural = _("Enrollment Histories")
        ordering = ['-action_date']
        indexes = [
            models.Index(fields=['student', 'action_date']),
            models.Index(fields=['course', 'action']),
        ]
    
    def __str__(self):
        return f"{self.student.get_full_name()} - {self.course.course_code} - {self.get_action_display()}"

"""UnifiedCourseService-EnsurescourseconsistencyacrossallappsThisservicemanagescoursedataintegrationbetween:1.courses.Course(maincoursemodel)2.course_generator.GeneratedCourseContent(AIcontent)Keyprinciples:-ONEcourserecordincourses.Courseisthesourceoftruth-Otherappsextendthiswithadditionalfeatures-Studentenrollmentisalwaystiedtothemaincourse-Allappsseethesamecoursedata(CS100isCS100everywhere)"""import loggingfromtypingimportDictListOptionalTuplefrom django.appsimportappsfrom django.dbimporttransactionfrom django.contrib.authimportget_user_modelfrom django.core.exceptionsimportObjectDoesNotExistlogger=logging.getLogger(__name__)User=get_user_model()classUnifiedCourseService:"""Servicetomanageunifiedcoursedataacrossallcourse-relatedapps"""def__init__(self):self.Course=apps.get_model('courses''Course')self.Enrollment=apps.get_model('courses''Enrollment')self.CourseProgress=apps.get_model('courses''CourseProgress')#Trytogetoptionalmodels(maynotexistinallconfigurations)try:self.GeneratedCourseContent=apps.get_model('course_generator''GeneratedCourseContent')exceptLookupError:self.GeneratedCourseContent=Nonedefget_unified_course_data(selfcourse_code:strsemester:str=Noneacademic_year:str=None)->Dict:"""GetcompletecoursedatafromallappsforagivencourseReturnsunifiedviewof:-Maincourseinfo-Interactivefeatures(ifavailable)-AI-generatedcontent(ifavailable)-Studentenrollmentdata"""try:#Findthemaincoursecourse_filter={'course_code':course_code}ifsemester:course_filter['semester']=semesterifacademic_year:course_filter['academic_year']=academic_yearcourse=self.Course.objects.filter(**course_filter).first()ifnotcourse:return{'error':f'Course{course_code}notfound'}#Buildunifiedcoursedataunified_data={'course_id':course.id'course_code':course.course_code'title':course.title'description':course.description'credits':course.credits'semester':course.semester'academic_year':course.academic_year'department':course.department.nameifcourse.departmentelseNone'instructor':course.instructor.get_full_name()ifcourse.instructorelseNone'required_level':course.required_level'recommended_level':course.recommended_level'capacity':course.capacity'max_students':course.max_students'is_active':course.is_active'is_published':course.is_published#Integrationflags'has_ai_content':getattr(course'has_ai_content'False)'has_assessment':getattr(course'has_assessment'False)#Extendedfeatures'ai_content':None'enrollment_stats':{}}#AddAI-generatedcontentdataifavailableifself.GeneratedCourseContentandhasattr(course'generated_content'):try:ai_content=course.generated_contentunified_data['ai_content']={'id':ai_content.id'generated_at':getattr(ai_content'generated_at'None)'ai_difficulty_override':getattr(ai_content'ai_difficulty_override'None)'provider_info':getattr(ai_content'provider_info''')'has_weekly_schedule':bool(getattr(ai_content'weekly_schedule'[]))'has_lesson_plans':bool(getattr(ai_content'lesson_plans'[]))'has_assessments':bool(getattr(ai_content'assessment_methods'[]))'skills_developed':len(getattr(ai_content'skills_developed'[]))}exceptExceptionase:logger.warning(f'ErrorloadingAIcontentfor{course_code}:{e}')#Addenrollmentstatisticstry:total_enrollments=course.enrollments.count()active_enrollments=course.enrollments.filter(status='APPROVED').count()completed_enrollments=course.enrollments.filter(status='COMPLETED').count()unified_data['enrollment_stats']={'total_enrollments':total_enrollments'active_enrollments':active_enrollments'completed_enrollments':completed_enrollments'completion_rate':(completed_enrollments/total_enrollments*100)iftotal_enrollments>0else0'available_spots':course.max_students-active_enrollments}exceptExceptionase:logger.warning(f'Errorcalculatingenrollmentstatsfor{course_code}:{e}')returnunified_dataexceptExceptionase:logger.error(f'Errorgettingunifiedcoursedatafor{course_code}:{e}')return{'error':str(e)}defenroll_student_unified(selfuser:Usercourse_code:strenrollment_type:str='REGULAR')->Dict:"""EnrollstudentincoursewithunifiedtrackingacrossallappsThisensures:1.Singleenrollmentrecordinmaincoursesapp2.Progresstrackinginallrelevantapps3.Consistentcoursedataacrossallfeatures"""try:withtransaction.atomic():#Getthemaincoursecourse=self.Course.objects.filter(course_code=course_code).first()ifnotcourse:return{'success':False'error':f'Course{course_code}notfound'}#Checkifalreadyenrolledexisting_enrollment=self.Enrollment.objects.filter(user=usercourse=course).first()ifexisting_enrollment:ifexisting_enrollment.status=='APPROVED':return{'success':False'error':'Studentalreadyenrolledinthiscourse'}elifexisting_enrollment.status=='PENDING':return{'success':False'error':'Enrollmentpendingapproval'}#Createmainenrollmentenrollment=self.Enrollment.objects.create(user=usercourse=courseenrollment_type=enrollment_typestatus='APPROVED'#Auto-approvefornow)#Createmaincourseprogresscourse_progress=self.CourseProgress.objects.create(user=usercourse=coursecompletion_percentage=0.0mastery_level='BEGINNER')#Createinteractiveprogressifinteractivefeaturesexistif(self.InteractiveCourseVersionandself.StudentInteractiveProgressandhasattr(course'interactive_version')):try:interactive_progress=self.StudentInteractiveProgress.objects.create(student=userinteractive_course=course.interactive_versionnormal_course_progress=course_progresssync_with_course_progress=True)logger.info(f'Createdinteractiveprogressfor{user.username}in{course_code}')exceptExceptionase:logger.warning(f'Couldnotcreateinteractiveprogress:{e}')#Updatecourseflagsself._update_course_integration_flags(course)return{'success':True'enrollment_id':enrollment.id'course_progress_id':course_progress.id'message':f'Successfullyenrolled{user.username}in{course_code}'}exceptExceptionase:logger.error(f'Errorenrollingstudent{user.username}in{course_code}:{e}')return{'success':False'error':str(e)}defget_student_unified_progress(selfuser:Usercourse_code:str)->Dict:"""Getstudent'sprogressacrossallcourseappsforaspecificcourseReturnsunifiedprogressdatafrom:-Maincourseprogress-Interactivelearningprogress-AIcontentinteraction"""try:course=self.Course.objects.filter(course_code=course_code).first()ifnotcourse:return{'error':f'Course{course_code}notfound'}#Checkenrollmentenrollment=self.Enrollment.objects.filter(user=usercourse=course).first()ifnotenrollment:return{'error':f'Studentnotenrolledin{course_code}'}progress_data={'course_code':course_code'course_title':course.title'enrollment_status':enrollment.status'enrollment_date':enrollment.enrollment_date'main_progress':{}'interactive_progress':None'ai_interaction':None}#Getmaincourseprogresstry:course_progress=self.CourseProgress.objects.filter(user=usercourse=course).first()ifcourse_progress:progress_data['main_progress']={'completion_percentage':course_progress.completion_percentage'current_grade':course_progress.current_grade'mastery_level':course_progress.mastery_level'attendance_rate':course_progress.attendance_rate'is_completed':course_progress.is_completed'last_activity':course_progress.last_activity_date'completed_materials':course_progress.completed_materials.count()}exceptExceptionase:logger.warning(f'Errorloadingmainprogress:{e}')#Getinteractiveprogressifavailableif(self.StudentInteractiveProgressandhasattr(course'interactive_version')):try:interactive_progress=self.StudentInteractiveProgress.objects.filter(student=userinteractive_course=course.interactive_version).first()ifinteractive_progress:progress_data['interactive_progress']={'current_streak':interactive_progress.current_streak'longest_streak':interactive_progress.longest_streak'total_points':interactive_progress.total_points'current_level':interactive_progress.current_level'accuracy_rate':interactive_progress.accuracy_rate'total_time_spent':interactive_progress.total_time_spent'completed_lessons':interactive_progress.completed_lessons.count()'achievements':len(interactive_progress.achievements)'badges':len(interactive_progress.badges)}exceptExceptionase:logger.warning(f'Errorloadinginteractiveprogress:{e}')returnprogress_dataexceptExceptionase:logger.error(f'Errorgettingstudentprogressfor{user.username}in{course_code}:{e}')return{'error':str(e)}defsync_course_data_across_apps(selfcourse_code:str)->Dict:"""SynchronizecoursedataacrossallappstoensureconsistencyThisfunction:1.Updatesintegrationflags2.Syncscoursemetadata3.Ensuresconsistentenrollmentdata"""try:withtransaction.atomic():course=self.Course.objects.filter(course_code=course_code).first()ifnotcourse:return{'success':False'error':f'Course{course_code}notfound'}updates_made=[]#Updateintegrationflagsbasedonactualcontentifself._update_course_integration_flags(course):updates_made.append('Updatedintegrationflags')#Syncinteractivecoursedataif(self.InteractiveCourseVersionandhasattr(course'interactive_version')):try:interactive=course.interactive_version#Ensurebase_courseisproperlysetifnothasattr(interactive'base_course')orinteractive.base_course!=course:interactive.base_course=courseinteractive.save(update_fields=['base_course'])updates_made.append('Syncedinteractivecourserelationship')exceptExceptionase:logger.warning(f'Errorsyncinginteractivecourse:{e}')#SyncAI-generatedcontentif(self.GeneratedCourseContentandhasattr(course'generated_content')):try:ai_content=course.generated_content#Ensurebase_courseisproperlysetifnothasattr(ai_content'base_course')orai_content.base_course!=course:ai_content.base_course=courseai_content.save(update_fields=['base_course'])updates_made.append('SyncedAIcontentrelationship')exceptExceptionase:logger.warning(f'ErrorsyncingAIcontent:{e}')#Syncstudentprogressacrossappssync_count=self._sync_student_progress_across_apps(course)ifsync_count>0:updates_made.append(f'Syncedprogressfor{sync_count}students')return{'success':True'course_code':course_code'updates_made':updates_made'message':f'Successfullysyncedcoursedatafor{course_code}'}exceptExceptionase:logger.error(f'Errorsyncingcoursedatafor{course_code}:{e}')return{'success':False'error':str(e)}def_update_course_integration_flags(selfcourse)->bool:"""Updatecourseintegrationflagsbasedonactualcontent"""updates_made=False#Checkforinteractivecontenthas_interactive=(self.InteractiveCourseVersionandhasattr(course'interactive_version'))ifgetattr(course'has_interactive_content'False)!=has_interactive:course.has_interactive_content=has_interactiveupdates_made=True#CheckforAIcontenthas_ai=(self.GeneratedCourseContentandhasattr(course'generated_content'))ifgetattr(course'has_ai_content'False)!=has_ai:course.has_ai_content=has_aiupdates_made=Trueifupdates_made:course.save(update_fields=['has_interactive_content''has_ai_content'])returnupdates_madedef_sync_student_progress_across_apps(selfcourse)->int:"""Syncstudentprogressacrossallcourseapps"""sync_count=0#Getallstudentsenrolledinthiscourseenrollments=self.Enrollment.objects.filter(course=coursestatus='APPROVED').select_related('user')forenrollmentinenrollments:try:#Getmaincourseprogresscourse_progress=self.CourseProgress.objects.filter(user=enrollment.usercourse=course).first()ifnotcourse_progress:continue#Syncwithinteractiveprogressifexistsif(self.StudentInteractiveProgressandhasattr(course'interactive_version')):try:interactive_progress=self.StudentInteractiveProgress.objects.filter(student=enrollment.userinteractive_course=course.interactive_version).first()ifinteractive_progressandinteractive_progress.sync_with_course_progress:#Synccompletionpercentageifabs(interactive_progress.completion_percentage-course_progress.completion_percentage)>1:max_completion=max(interactive_progress.completion_percentagecourse_progress.completion_percentage)course_progress.completion_percentage=max_completioninteractive_progress.completion_percentage=max_completioncourse_progress.save(update_fields=['completion_percentage'])interactive_progress.save(update_fields=['completion_percentage'])sync_count+=1exceptExceptionase:logger.warning(f'Errorsyncingprogressfor{enrollment.user.username}:{e}')exceptExceptionase:logger.warning(f'Errorprocessingenrollment{enrollment.id}:{e}')returnsync_countdefget_all_courses_unified(self)->List[Dict]:"""Getallcourseswiththeirintegrationstatus"""courses_data=[]forcourseinself.Course.objects.all().select_related('department''instructor'):course_data={'id':course.id'course_code':course.course_code'title':course.title'department':course.department.nameifcourse.departmentelseNone'instructor':course.instructor.get_full_name()ifcourse.instructorelseNone'semester':course.semester'academic_year':course.academic_year'is_active':course.is_active'has_interactive_content':getattr(course'has_interactive_content'False)'has_ai_content':getattr(course'has_ai_content'False)'total_enrollments':course.enrollments.count()'active_enrollments':course.enrollments.filter(status='APPROVED').count()}courses_data.append(course_data)returncourses_datadefcreate_or_link_interactive(selfcourse_code:str)->Dict:"""Createorlinkaninteractivecourseversionforagivencoursecode."""try:course=self.Course.objects.get(course_code=course_code)ifnotcourse:return{'success':False'error':f'Course{course_code}notfound'}interactive_coursecreated=self.InteractiveCourseVersion.objects.get_or_create(course=coursedefaults={'interactive_title':course.title'interactive_description':course.description})ifcreated:course.has_interactive_content=Truecourse.save()return{'success':True'message':'Interactivecoursecreatedandlinkedsuccessfully''interactive_course_id':interactive_course.id}else:return{'success':True'message':'Interactivecoursealreadyexists''interactive_course_id':interactive_course.id}exceptObjectDoesNotExist:return{'success':False'error':f'Course{course_code}notfound'}exceptExceptionase:logger.error(f"Errorincreate_or_link_interactive:{str(e)}")return{'success':False'error':'Anerroroccurredwhilecreatingorlinkinginteractivecourse'}#Singletoninstanceunified_course_service=UnifiedCourseService()
import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Grid,
  Chip,
  LinearProgress,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider,
  <PERSON>,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Error as <PERSON>rrorIcon,
  Psychology as BrainIcon,
} from '@mui/icons-material';
import multiAgentService, { AgentInfo } from '../services/multiAgentService';

// 📊 Agent Status Dashboard - Real-time monitoring of AI agents

interface AgentStatusData {
  agents: AgentInfo[];
  totalAgents: number;
  orchestratorStatus: string;
  activeSessions: number;
  studentModels: number;
}

const AgentStatusDashboard: React.FC = () => {
  const [agentStatus, setAgentStatus] = useState<AgentStatusData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchAgentStatus = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to fetch from backend, fallback to mock data
      try {
        const response = await multiAgentService.getAgentStatus();
        setAgentStatus(response.data);
      } catch (backendError) {
        // Fallback to mock data for demo
        const mockData: AgentStatusData = {
          agents: [
            {
              name: 'Math Tutor Agent',
              type: 'math_tutor',
              specialization: 'Mathematics and problem solving',
              status: 'active',
              icon: '🔢',
              keywords: ['math', 'equation', 'calculate'],
            },
            {
              name: 'Science Tutor Agent',
              type: 'science_tutor',
              specialization: 'Science concepts and experiments',
              status: 'active',
              icon: '🔬',
              keywords: ['science', 'biology', 'chemistry'],
            },
            {
              name: 'Language Tutor Agent',
              type: 'language_tutor',
              specialization: 'Writing and language arts',
              status: 'active',
              icon: '📝',
              keywords: ['write', 'essay', 'grammar'],
            },
            {
              name: 'Career Advisor Agent',
              type: 'advisor',
              specialization: 'Academic and career guidance',
              status: 'active',
              icon: '🎯',
              keywords: ['career', 'advice', 'guidance'],
            },
            {
              name: 'Assessor Agent',
              type: 'assessor',
              specialization: 'Assessment and quiz generation',
              status: 'active',
              icon: '📊',
              keywords: ['quiz', 'test', 'assessment'],
            },
            {
              name: 'Content Creator Agent',
              type: 'content_creator',
              specialization: 'Educational content creation',
              status: 'active',
              icon: '✨',
              keywords: ['create', 'content', 'lesson'],
            },
            {
              name: 'General Tutor Agent',
              type: 'tutor',
              specialization: 'General tutoring and education',
              status: 'active',
              icon: '🎓',
              keywords: ['help', 'explain', 'teach'],
            },
          ],
          totalAgents: 7,
          orchestratorStatus: 'active',
          activeSessions: 12,
          studentModels: 45,
        };
        setAgentStatus(mockData);
      }

      setLastUpdated(new Date());
    } catch (err) {
      setError('Failed to fetch agent status');
      console.error('Error fetching agent status:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAgentStatus();

    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchAgentStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'error';
      case 'warning':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return <CheckCircleIcon color='success' />;
      case 'inactive':
        return <ErrorIcon color='error' />;
      default:
        return <BrainIcon color='primary' />;
    }
  };

  if (loading && !agentStatus) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant='h5' gutterBottom>
          🤖 Agent Status Dashboard
        </Typography>
        <LinearProgress />
        <Typography variant='body2' sx={{ mt: 2 }}>
          Loading agent status...
        </Typography>
      </Box>
    );
  }

  if (error && !agentStatus) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          severity='error'
          action={
            <Button color='inherit' size='small' onClick={fetchAgentStatus}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
        }}
      >
        <Typography variant='h4' gutterBottom>
          🤖 Multi-Agent System Dashboard
        </Typography>
        <Button
          variant='outlined'
          startIcon={<RefreshIcon />}
          onClick={fetchAgentStatus}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {/* System Overview */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography color='textSecondary' gutterBottom>
                Total Agents
              </Typography>
              <Typography variant='h4' color='primary'>
                {agentStatus?.totalAgents || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography color='textSecondary' gutterBottom>
                Orchestrator Status
              </Typography>
              <Chip
                icon={getStatusIcon(
                  agentStatus?.orchestratorStatus || 'unknown'
                )}
                label={agentStatus?.orchestratorStatus || 'Unknown'}
                color={
                  getStatusColor(
                    agentStatus?.orchestratorStatus || 'unknown'
                  ) as any
                }
              />
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography color='textSecondary' gutterBottom>
                Active Sessions
              </Typography>
              <Typography variant='h4' color='secondary'>
                {agentStatus?.activeSessions || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography color='textSecondary' gutterBottom>
                Student Models
              </Typography>
              <Typography variant='h4' color='info'>
                {agentStatus?.studentModels || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Agent Details */}
      <Typography variant='h5' gutterBottom>
        🎯 Available AI Agents
      </Typography>

      <Grid container spacing={2}>
        {agentStatus?.agents.map((agent, index) => (
          <Grid item xs={12} md={6} lg={4} key={index}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant='h6' sx={{ mr: 1 }}>
                    {agent.icon}
                  </Typography>
                  <Typography variant='h6' component='div' sx={{ flexGrow: 1 }}>
                    {agent.name}
                  </Typography>
                  <Chip
                    size='small'
                    label={agent.status}
                    color={getStatusColor(agent.status) as any}
                    icon={getStatusIcon(agent.status)}
                  />
                </Box>

                <Typography
                  variant='body2'
                  color='text.secondary'
                  sx={{ mb: 2 }}
                >
                  {agent.specialization}
                </Typography>

                <Divider sx={{ my: 1 }} />

                <Typography variant='caption' color='text.secondary'>
                  Type: {agent.type}
                </Typography>

                {agent.keywords && agent.keywords.length > 0 && (
                  <Box sx={{ mt: 1 }}>
                    <Typography
                      variant='caption'
                      color='text.secondary'
                      display='block'
                    >
                      Keywords:
                    </Typography>
                    <Box
                      sx={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: 0.5,
                        mt: 0.5,
                      }}
                    >
                      {agent.keywords.slice(0, 3).map((keyword, idx) => (
                        <Chip
                          key={idx}
                          label={keyword}
                          size='small'
                          variant='outlined'
                          sx={{ fontSize: '0.7rem', height: '20px' }}
                        />
                      ))}
                    </Box>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Last Updated */}
      {lastUpdated && (
        <Paper sx={{ p: 2, mt: 3, bgcolor: 'grey.50' }}>
          <Typography variant='caption' color='text.secondary'>
            Last updated: {lastUpdated.toLocaleString()}
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default AgentStatusDashboard;

from django.conf import settings
from django.db import models
from django.utils import timezone


class ChatConversation(models.Model):
    USER_ROLE_CHOICES = [
        ("STUDENT", "Student"),
        ("PROFESSOR", "Professor"),
        ("<PERSON>MI<PERSON>", "Admin")
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="chat_conversations",
        on_delete=models.CASCADE
    )
    title = models.CharField(max_length=255, blank=True, default="New Conversation")
    user_role = models.CharField(max_length=20, choices=USER_ROLE_CHOICES, blank=True)
    context = models.J<PERSON><PERSON>ield(default=dict, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-updated_at"]

    def __str__(self):
        return f"{self.title or 'Untitled'} - {self.user.email}"

    def save(self, *args, **kwargs):
        if not self.user_role and hasattr(self.user, "role"):
            self.user_role = self.user.role
        
        if not self.title:
            first_message = self.messages.first()
            if first_message and first_message.role == "user":
                title = first_message.content[:50]
                if len(first_message.content) > 50:
                    title += "..."
                self.title = title
        
        super().save(*args, **kwargs)


class ChatMessage(models.Model):
    MESSAGE_ROLES = [
        ("user", "User"),
        ("assistant", "Assistant"),
        ("system", "System")
    ]
    
    MESSAGE_STATUS = [
        ("received", "Received"),
        ("completed", "Completed"),
        ("error", "Error")
    ]
    
    conversation = models.ForeignKey(
        "ChatConversation",
        related_name="messages",
        on_delete=models.CASCADE
    )
    role = models.CharField(max_length=20, choices=MESSAGE_ROLES)
    content = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text="Stores metadata about the message including learning style indicators"
    )
    status = models.CharField(max_length=20, choices=MESSAGE_STATUS, default="received")

    class Meta:
        ordering = ["timestamp"]

    def __str__(self):
        return f"{self.role} message at {self.timestamp}"

    def save(self, *args, **kwargs):
        # Update conversation title on first message
        if not self.pk and self.role == "user" and not self.conversation.title:
            title = self.content[:50]
            if len(self.content) > 50:
                title += "..."
            self.conversation.title = title
            self.conversation.save()
        
        super().save(*args, **kwargs)

    def update_learning_indicators(self):
        """Update learning style indicators based on message content and metadata"""
        from .services.learning_pattern_analyzer import LearningPatternAnalyzer
        
        if self.role == "user":
            analyzer = LearningPatternAnalyzer(self.conversation.user)
            patterns = analyzer.analyze_message_patterns(self.content)
            
            # Store learning indicators in the metadata field
            if "learning_indicators" not in self.metadata:
                self.metadata["learning_indicators"] = {}
            
            self.metadata["learning_indicators"] = patterns
            self.save()
            
            # Update the user's learning profile
            analyzer.update_learning_profile(patterns)


class ChatSession(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    started_at = models.DateTimeField(auto_now_add=True)
    last_message_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    context = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"Chat session for {self.user.username} at {self.started_at}"


class AIResponse(models.Model):
    message = models.ForeignKey(
        ChatMessage,
        on_delete=models.CASCADE,
        related_name="ai_responses"
    )
    content = models.TextField()
    suggested_questions = models.JSONField(default=list)
    dynamic_content = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"AI response at {self.created_at}"


class UserLearningProfile(models.Model):
    LEARNING_STYLES = [
        ("VISUAL", "Visual"),
        ("AUDITORY", "Auditory"),
        ("READ_WRITE", "Read/Write"),
        ("KINESTHETIC", "Kinesthetic")
    ]
    
    # Updated to match the standard 5-level system
    COMPREHENSION_LEVELS = [
        (1, "Beginner"),
        (2, "Elementary"),
        (3, "Intermediate"),
        (4, "Advanced"),
        (5, "Expert")
    ]
    
    INTERACTION_STYLES = [
        ("COLLABORATIVE", "Collaborative"),
        ("INDEPENDENT", "Independent"),
        ("STRUCTURED", "Structured"),
        ("FLEXIBLE", "Flexible")
    ]
    
    PACE_PREFERENCES = [
        ("SLOW", "Slow and thorough"),
        ("MODERATE", "Moderate pace"),
        ("FAST", "Fast-paced")
    ]
    
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="learning_profile"
    )
    primary_learning_style = models.CharField(
        max_length=20,
        choices=LEARNING_STYLES,
        null=True,
        blank=True
    )
    secondary_learning_style = models.CharField(
        max_length=20,
        choices=LEARNING_STYLES,
        null=True,
        blank=True
    )
    comprehension_level = models.IntegerField(choices=COMPREHENSION_LEVELS, default=1)
    interaction_style = models.CharField(
        max_length=20,
        choices=INTERACTION_STYLES,
        default="COLLABORATIVE"
    )
    prefers_step_by_step = models.BooleanField(default=True)
    prefers_examples = models.BooleanField(default=True)
    prefers_visual_aids = models.BooleanField(default=True)
    attention_span_minutes = models.IntegerField(default=30)
    pace_preference = models.CharField(
        max_length=20,
        choices=PACE_PREFERENCES,
        default="MODERATE"
    )
    last_updated = models.DateTimeField(auto_now=True)
    interaction_patterns = models.JSONField(default=dict)

    class Meta:
        verbose_name = "User Learning Profile"
        verbose_name_plural = "User Learning Profiles"

    def __str__(self):
        return f"{self.user.username}'s Learning Profile"

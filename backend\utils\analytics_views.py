"""
Analytics API Views
Provides RESTful endpoints for analytics data
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.response import Response
from rest_framework import status
from django.core.cache import cache
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
import json

from .analytics_service import analytics_service


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_dashboard_analytics(request):
    """
    Get comprehensive dashboard analytics
    
    Returns:
    - Overview statistics
    - Performance metrics
    - Usage trends
    - User distribution
    - Course analytics
    - Assessment analytics
    """
    try:
        user = request.user if not request.user.is_staff else None
        analytics_data = analytics_service.get_dashboard_analytics(user)
        
        return Response({
            'success': True,
            'data': analytics_data,
            'cached': 'error' not in analytics_data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e),
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_real_time_stats(request):
    """
    Get real-time statistics for live dashboard updates
    
    Returns:
    - Users currently online
    - Active sessions
    - Timestamp
    """
    try:
        stats = analytics_service.get_real_time_stats()
        
        return Response({
            'success': True,
            'data': stats
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e),
            'data': {
                'users_online': 0,
                'active_sessions': 0,
                'timestamp': timezone.now().isoformat(),
            }
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def track_user_activity(request):
    """
    Track user activity for analytics
    
    Expected payload:
    {
        "activity_type": "string",
        "metadata": {}  // optional
    }
    """
    try:
        activity_type = request.data.get('activity_type')
        metadata = request.data.get('metadata', {})
        
        if not activity_type:
            return Response({
                'success': False,
                'error': 'activity_type is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        analytics_service.track_user_activity(
            user=request.user,
            activity_type=activity_type,
            metadata=metadata
        )
        
        return Response({
            'success': True,
            'message': 'Activity tracked successfully'
        }, status=status.HTTP_201_CREATED)
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAdminUser])
def get_admin_analytics(request):
    """
    Get detailed analytics for administrators
    
    Returns comprehensive system analytics including:
    - System performance metrics
    - User behavior patterns
    - Course effectiveness data
    - Assessment analytics
    """
    try:
        # Extended admin analytics
        analytics_data = analytics_service.get_dashboard_analytics()
        
        # Add admin-specific metrics
        admin_metrics = {
            'system_health': _get_system_health(),
            'performance_metrics': _get_performance_metrics(),
            'security_metrics': _get_security_metrics(),
        }
        
        analytics_data['admin_metrics'] = admin_metrics
        
        return Response({
            'success': True,
            'data': analytics_data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e),
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_personal_analytics(request):
    """
    Get personalized analytics for the authenticated user
    
    Returns:
    - Personal learning progress
    - Course completion status
    - Assessment performance
    - Study patterns
    """
    try:
        user = request.user
        
        # Get user-specific analytics
        personal_analytics = {
            'learning_progress': _get_user_learning_progress(user),
            'course_completion': _get_user_course_completion(user),
            'assessment_performance': _get_user_assessment_performance(user),
            'activity_summary': _get_user_activity_summary(user),
            'recommendations': _get_user_recommendations(user),
        }
        
        return Response({
            'success': True,
            'data': personal_analytics
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e),
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def _get_system_health():
    """Get system health metrics"""
    try:
        from django.db import connection
        
        # Database connection test
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            db_healthy = True
    except:
        db_healthy = False
    
    # Cache test
    try:
        cache.set('health_check', 'ok', 30)
        cache_healthy = cache.get('health_check') == 'ok'
    except:
        cache_healthy = False
    
    return {
        'database': db_healthy,
        'cache': cache_healthy,
        'timestamp': timezone.now().isoformat(),
    }


def _get_performance_metrics():
    """Get performance metrics"""
    try:
        # This would ideally use a proper monitoring system
        # For now, we'll return basic metrics
        return {
            'response_time_avg': 0.5,  # Would be calculated from logs
            'error_rate': 0.01,  # 1% error rate
            'uptime': 99.9,  # Would be calculated from monitoring
            'memory_usage': 65.0,  # Would be from system monitoring
        }
    except:
        return {}


def _get_security_metrics():
    """Get security-related metrics"""
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        week_ago = timezone.now() - timedelta(days=7)
        
        # Failed login attempts (would require tracking)
        failed_logins = 0  # Would be tracked in a separate model
        
        # Recent new users
        new_users = User.objects.filter(date_joined__gte=week_ago).count()
        
        return {
            'failed_login_attempts': failed_logins,
            'new_users_week': new_users,
            'active_sessions': 0,  # Would be calculated from session store
        }
    except:
        return {}


def _get_user_learning_progress(user):
    """Get user's learning progress"""
    try:
        from courses.models import CourseProgress, Enrollment
        
        enrollments = Enrollment.objects.filter(student=user)
        total_courses = enrollments.count()
        
        progress_data = []
        for enrollment in enrollments:
            try:
                progress = CourseProgress.objects.get(
                    student=user, 
                    course=enrollment.course
                )
                progress_data.append({
                    'course_id': enrollment.course.id,
                    'course_name': enrollment.course.name,
                    'progress_percentage': progress.progress_percentage,
                    'is_completed': progress.is_completed,
                    'last_accessed': progress.last_accessed.isoformat() if progress.last_accessed else None,
                })
            except CourseProgress.DoesNotExist:
                progress_data.append({
                    'course_id': enrollment.course.id,
                    'course_name': enrollment.course.name,
                    'progress_percentage': 0,
                    'is_completed': False,
                    'last_accessed': None,
                })
        
        completed_courses = sum(1 for p in progress_data if p['is_completed'])
        avg_progress = sum(p['progress_percentage'] for p in progress_data) / len(progress_data) if progress_data else 0
        
        return {
            'total_courses': total_courses,
            'completed_courses': completed_courses,
            'average_progress': round(avg_progress, 2),
            'course_progress': progress_data,
        }
    except:
        return {}


def _get_user_course_completion(user):
    """Get user's course completion statistics"""
    try:
        from courses.models import CourseProgress
        
        progress_records = CourseProgress.objects.filter(student=user)
        completed = progress_records.filter(is_completed=True).count()
        in_progress = progress_records.filter(
            is_completed=False, 
            progress_percentage__gt=0
        ).count()
        not_started = progress_records.filter(progress_percentage=0).count()
        
        return {
            'completed': completed,
            'in_progress': in_progress,
            'not_started': not_started,
            'completion_rate': (completed / progress_records.count() * 100) if progress_records.count() > 0 else 0,
        }
    except:
        return {}


def _get_user_assessment_performance(user):
    """Get user's assessment performance"""
    try:
        from assessment.models import AssessmentResponse
        
        responses = AssessmentResponse.objects.filter(student=user)
        
        if not responses.exists():
            return {
                'total_assessments': 0,
                'average_score': 0,
                'best_score': 0,
                'recent_performance': [],
            }
        
        total_assessments = responses.count()
        average_score = responses.aggregate(models.Avg('score'))['score__avg'] or 0
        best_score = responses.aggregate(models.Max('score'))['score__max'] or 0
        
        # Recent performance (last 10 assessments)
        recent_responses = responses.order_by('-submitted_at')[:10]
        recent_performance = [
            {
                'assessment_id': resp.assessment.id,
                'assessment_name': resp.assessment.title,
                'score': resp.score,
                'submitted_at': resp.submitted_at.isoformat(),
            }
            for resp in recent_responses
        ]
        
        return {
            'total_assessments': total_assessments,
            'average_score': round(average_score, 2),
            'best_score': round(best_score, 2),
            'recent_performance': recent_performance,
        }
    except:
        return {}


def _get_user_activity_summary(user):
    """Get user's activity summary"""
    try:
        from utils.models import UserActivity
        
        week_ago = timezone.now() - timedelta(days=7)
        recent_activities = UserActivity.objects.filter(
            user=user,
            timestamp__gte=week_ago
        ).order_by('-timestamp')[:20]
        
        activity_counts = {}
        for activity in recent_activities:
            activity_type = activity.activity_type
            activity_counts[activity_type] = activity_counts.get(activity_type, 0) + 1
        
        return {
            'recent_activities': [
                {
                    'type': activity.activity_type,
                    'timestamp': activity.timestamp.isoformat(),
                    'metadata': activity.metadata,
                }
                for activity in recent_activities
            ],
            'activity_summary': activity_counts,
            'total_activities_week': recent_activities.count(),
        }
    except:
        return {}


def _get_user_recommendations(user):
    """Get personalized recommendations for the user"""
    try:
        recommendations = []
        
        # This would use AI/ML algorithms to generate recommendations
        # For now, we'll provide basic recommendations based on progress
        
        from courses.models import Course, CourseProgress, Enrollment
        
        # Recommend courses based on incomplete progress
        incomplete_courses = CourseProgress.objects.filter(
            student=user,
            is_completed=False,
            progress_percentage__gt=0
        ).order_by('-last_accessed')[:3]
        
        for progress in incomplete_courses:
            recommendations.append({
                'type': 'continue_course',
                'title': f'Continue {progress.course.name}',
                'description': f'You\'re {progress.progress_percentage}% complete',
                'action_url': f'/courses/{progress.course.id}',
                'priority': 'high',
            })
        
        # Recommend new courses based on department
        user_enrollments = Enrollment.objects.filter(student=user)
        enrolled_departments = user_enrollments.values_list('course__department', flat=True).distinct()
        
        for dept_id in enrolled_departments:
            similar_courses = Course.objects.filter(
                department_id=dept_id,
                is_active=True
            ).exclude(
                id__in=user_enrollments.values_list('course_id', flat=True)
            )[:2]
            
            for course in similar_courses:
                recommendations.append({
                    'type': 'new_course',
                    'title': course.name,
                    'description': f'Based on your interest in {course.department.name if course.department else "this field"}',
                    'action_url': f'/courses/{course.id}',
                    'priority': 'medium',
                })
        
        return recommendations[:5]  # Limit to 5 recommendations
    except:
        return []

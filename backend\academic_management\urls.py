from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from . import views

# Create a router and register our ViewSets
router = DefaultRouter()
router.register(r'academic-terms', views.AcademicTermViewSet, basename='academic-terms')
router.register(r'transcript-records', views.TranscriptRecordViewSet, basename='transcript-records')
router.register(r'prerequisites', views.CoursePrerequisiteViewSet, basename='prerequisites')
router.register(r'waitlists', views.CourseWaitlistViewSet, basename='waitlists')
router.register(r'enrollment-history', views.EnrollmentHistoryViewSet, basename='enrollment-history')
router.register(r'academic-standings', views.AcademicStandingViewSet, basename='academic-standings')
router.register(r'reports', views.AcademicReportsViewSet, basename='reports')

app_name = 'academic_management'

urlpatterns = [
    # Include all router URLs
    path('', include(router.urls)),
    
    # Student-specific endpoints
    path('standing/my-standing/', views.my_academic_standing, name='my-academic-standing'),
    
    # Additional custom endpoints if needed
    # These are already covered by the ViewSet actions above, but included for reference
    
    # Academic Terms
    # GET /api/academic-management/academic-terms/current/ - Get current term
    # GET /api/academic-management/academic-terms/upcoming/ - Get upcoming terms
    # POST /api/academic-management/academic-terms/{id}/activate/ - Activate term
    
    # Transcript Records
    # GET /api/academic-management/transcript-records/student_transcript/?student_id={id} - Get student transcript
    # GET /api/academic-management/transcript-records/gpa_calculation/?student_id={id}&term_id={id} - Calculate GPA
    
    # Prerequisites
    # POST /api/academic-management/prerequisites/check_prerequisites/ - Check prerequisites
    # GET /api/academic-management/prerequisites/course_prerequisites/?course_id={id} - Get course prerequisites
    
    # Waitlists
    # POST /api/academic-management/waitlists/add_to_waitlist/ - Add to waitlist
    # POST /api/academic-management/waitlists/{id}/remove_from_waitlist/ - Remove from waitlist
    # GET /api/academic-management/waitlists/student_position/?student_id={id}&course_id={id}&term_id={id} - Get position
    # POST /api/academic-management/waitlists/process_waitlist/ - Process waitlist
    
    # Enrollment History
    # GET /api/academic-management/enrollment-history/student_history/?student_id={id} - Get student history
    # GET /api/academic-management/enrollment-history/course_enrollment_stats/?course_id={id}&term_id={id} - Get stats
    
    # Academic Standings
    # POST /api/academic-management/academic-standings/calculate_standing/ - Calculate standing
    # GET /api/academic-management/academic-standings/at_risk_students/?term_id={id} - Get at-risk students
    # GET /api/academic-management/academic-standings/honors_students/?term_id={id} - Get honors students
    # POST /api/academic-management/academic-standings/bulk_calculate/ - Bulk calculate standings
    
    # Reports
    # GET /api/academic-management/reports/gpa_trends/?student_id={id} - Get GPA trends
    # GET /api/academic-management/reports/department_statistics/?term_id={id} - Get department stats
    # GET /api/academic-management/reports/graduation_progress/?student_id={id} - Get graduation progress
]

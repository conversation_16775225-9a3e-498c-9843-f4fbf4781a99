import React, { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Button,
  Alert,
  LinearProgress,
  Chip,
  Grid,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  CloudUpload,
  Delete,
  InsertDriveFile,
  Image,
  VideoFile,
  AudioFile,
  PictureAsPdf,
  Description,
  Error,
  CheckCircle,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { FileUploadConfig } from '../../types';

interface FileUploadProps {
  config: FileUploadConfig;
  onFilesChange: (files: File[]) => void;
  isSubmitting: boolean;
  value?: string;
}

interface UploadedFile {
  file: File;
  id: string;
  uploadProgress: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  config,
  onFilesChange,
  isSubmitting,
  value,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [uploadError, setUploadError] = useState<string | null>(null);

  useEffect(() => {
    // Parse existing value if provided
    if (value) {
      try {
        const parsed = JSON.parse(value);
        // In real implementation, you would reconstruct the file objects
        console.log('Existing files:', parsed);
      } catch {
        // Invalid JSON, ignore
      }
    }
  }, [value]);

  const validateFile = useCallback(
    (file: File): string | null => {
      // Check file size
      if (file.size > config.maxFileSize) {
        return t('assessment.fileUpload.errors.fileTooLarge', {
          maxSize: formatFileSize(config.maxFileSize),
        });
      }

      // Check file type
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      const mimeType = file.type;
      
      const isAllowedType = config.allowedTypes.some(type => 
        type === mimeType || 
        type === `.${fileExtension}` ||
        (type.includes('*') && mimeType.startsWith(type.replace('*', '')))
      );

      if (!isAllowedType) {
        return t('assessment.fileUpload.errors.invalidFileType', {
          allowedTypes: config.allowedTypes.join(', '),
        });
      }

      return null;
    },
    [config, t]
  );

  const onDrop = useCallback(
    (acceptedFiles: File[], rejectedFiles: any[]) => {
      setUploadError(null);

      // Handle rejected files
      if (rejectedFiles.length > 0) {
        const errors = rejectedFiles.map(rejection => 
          rejection.errors.map((error: any) => error.message).join(', ')
        );
        setUploadError(errors.join('; '));
        return;
      }

      // Check total file count
      if (uploadedFiles.length + acceptedFiles.length > config.maxFiles) {
        setUploadError(t('assessment.fileUpload.errors.tooManyFiles', {
          maxFiles: config.maxFiles,
        }));
        return;
      }

      // Validate and upload files
      const newFiles: UploadedFile[] = [];
      
      for (const file of acceptedFiles) {
        const validationError = validateFile(file);
        if (validationError) {
          setUploadError(validationError);
          continue;
        }

        const uploadedFile: UploadedFile = {
          file,
          id: `${Date.now()}-${Math.random()}`,
          uploadProgress: 0,
          status: 'uploading',
        };

        newFiles.push(uploadedFile);
      }

      if (newFiles.length > 0) {
        setUploadedFiles(prev => [...prev, ...newFiles]);
        
        // Simulate upload process
        newFiles.forEach(uploadedFile => {
          simulateUpload(uploadedFile);
        });
      }
    },
    [uploadedFiles, config, validateFile, t]
  );

  const simulateUpload = useCallback(
    (uploadedFile: UploadedFile) => {
      const interval = setInterval(() => {
        setUploadedFiles(prev => 
          prev.map(file => 
            file.id === uploadedFile.id
              ? {
                  ...file,
                  uploadProgress: Math.min(file.uploadProgress + 10, 100),
                  status: file.uploadProgress >= 90 ? 'completed' : 'uploading',
                }
              : file
          )
        );
      }, 200);

      setTimeout(() => {
        clearInterval(interval);
        setUploadedFiles(prev => 
          prev.map(file => 
            file.id === uploadedFile.id
              ? { ...file, uploadProgress: 100, status: 'completed' }
              : file
          )
        );
        
        // Update parent component
        const allFiles = uploadedFiles.map(f => f.file);
        onFilesChange(allFiles);
      }, 2000);
    },
    [uploadedFiles, onFilesChange]
  );

  const removeFile = useCallback(
    (fileId: string) => {
      setUploadedFiles(prev => {
        const updated = prev.filter(file => file.id !== fileId);
        onFilesChange(updated.map(f => f.file));
        return updated;
      });
    },
    [onFilesChange]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    disabled: isSubmitting || uploadedFiles.length >= config.maxFiles,
    accept: config.allowedTypes.reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>),
    maxSize: config.maxFileSize,
  });

  const getFileIcon = (file: File) => {
    const type = file.type;
    if (type.startsWith('image/')) return <Image />;
    if (type.startsWith('video/')) return <VideoFile />;
    if (type.startsWith('audio/')) return <AudioFile />;
    if (type === 'application/pdf') return <PictureAsPdf />;
    if (type.includes('document') || type.includes('text')) return <Description />;
    return <InsertDriveFile />;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Box sx={{ mt: 2 }}>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        {t('assessment.fileUpload.instruction')}
      </Typography>

      {/* Upload Configuration Info */}
      <Grid container spacing={1} sx={{ mb: 2 }}>
        <Grid item>
          <Chip
            label={t('assessment.fileUpload.maxFiles', { count: config.maxFiles })}
            size="small"
            variant="outlined"
          />
        </Grid>
        <Grid item>
          <Chip
            label={t('assessment.fileUpload.maxSize', { size: formatFileSize(config.maxFileSize) })}
            size="small"
            variant="outlined"
          />
        </Grid>
        <Grid item>
          <Chip
            label={t('assessment.fileUpload.allowedTypes', { types: config.allowedTypes.join(', ') })}
            size="small"
            variant="outlined"
          />
        </Grid>
      </Grid>

      {/* Dropzone */}
      <Paper
        {...getRootProps()}
        elevation={1}
        sx={{
          p: 3,
          border: `2px dashed ${
            isDragActive ? theme.palette.primary.main : theme.palette.divider
          }`,
          borderRadius: 2,
          backgroundColor: isDragActive
            ? theme.palette.primary.light
            : uploadedFiles.length >= config.maxFiles
            ? theme.palette.action.disabledBackground
            : theme.palette.background.paper,
          cursor: isSubmitting || uploadedFiles.length >= config.maxFiles ? 'default' : 'pointer',
          transition: 'all 0.2s ease',
          textAlign: 'center',
        }}
      >
        <input {...getInputProps()} />
        
        <CloudUpload
          sx={{
            fontSize: 48,
            color: isDragActive
              ? theme.palette.primary.main
              : theme.palette.text.secondary,
            mb: 1,
          }}
        />
        
        {uploadedFiles.length >= config.maxFiles ? (
          <Typography variant="body1" color="text.secondary">
            {t('assessment.fileUpload.maxFilesReached')}
          </Typography>
        ) : isDragActive ? (
          <Typography variant="body1" color="primary">
            {t('assessment.fileUpload.dropFiles')}
          </Typography>
        ) : (
          <>
            <Typography variant="body1" gutterBottom>
              {t('assessment.fileUpload.dragAndDrop')}
            </Typography>
            <Button variant="outlined" component="span" disabled={isSubmitting}>
              {t('assessment.fileUpload.selectFiles')}
            </Button>
          </>
        )}
      </Paper>

      {/* Upload Error */}
      {uploadError && (
        <Alert severity="error" sx={{ mt: 2 }} onClose={() => setUploadError(null)}>
          {uploadError}
        </Alert>
      )}

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <Paper elevation={1} sx={{ mt: 2 }}>
          <Typography variant="subtitle2" sx={{ p: 2, pb: 0 }}>
            {t('assessment.fileUpload.uploadedFiles')} ({uploadedFiles.length}/{config.maxFiles})
          </Typography>
          
          <List>
            {uploadedFiles.map((uploadedFile) => (
              <ListItem key={uploadedFile.id}>
                <ListItemIcon>
                  {uploadedFile.status === 'completed' ? (
                    <CheckCircle color="success" />
                  ) : uploadedFile.status === 'error' ? (
                    <Error color="error" />
                  ) : (
                    getFileIcon(uploadedFile.file)
                  )}
                </ListItemIcon>
                
                <ListItemText
                  primary={uploadedFile.file.name}
                  secondary={
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        {formatFileSize(uploadedFile.file.size)}
                      </Typography>
                      
                      {uploadedFile.status === 'uploading' && (
                        <Box sx={{ mt: 0.5 }}>
                          <LinearProgress
                            variant="determinate"
                            value={uploadedFile.uploadProgress}
                            sx={{ height: 6, borderRadius: 3 }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {uploadedFile.uploadProgress}%
                          </Typography>
                        </Box>
                      )}
                      
                      {uploadedFile.status === 'error' && (
                        <Typography variant="caption" color="error">
                          {uploadedFile.error}
                        </Typography>
                      )}
                    </Box>
                  }
                />
                
                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    onClick={() => removeFile(uploadedFile.id)}
                    disabled={isSubmitting || uploadedFile.status === 'uploading'}
                    size="small"
                  >
                    <Delete />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </Paper>
      )}

      {/* Peer Review Notice */}
      {config.requiresPeerReview && uploadedFiles.some(f => f.status === 'completed') && (
        <Alert severity="info" sx={{ mt: 2 }}>
          <Typography variant="body2">
            {t('assessment.fileUpload.peerReviewNotice')}
          </Typography>
        </Alert>
      )}
    </Box>
  );
};

export default FileUpload;

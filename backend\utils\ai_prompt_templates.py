"""AIPromptTemplatesThismoduleprovidesstandardizedprompttemplatesandJSONstructuresforconsistentAIserviceinteractionsacrosstheapplication."""fromdataclassesimportdataclassfromenumimportEnumfromtypingimportAnyDictListOptionalclassPromptType(Enum):"""TypesofAIprompts"""EDUCATIONAL_CONTENT="educational_content"ASSESSMENT="assessment"STUDY_PLAN="study_plan"QUESTION_GENERATION="question_generation"ANALYSIS="analysis"RECOMMENDATION="recommendation"TUTORING="tutoring"GENERAL="general"@dataclassclassPromptTemplate:"""TemplateforAIprompts"""template:strrequired_variables:List[str]optional_variables:List[str]=Noneexpected_response_type:str="text"json_structure:Dict[strAny]=None#CommonJSONstructuresCOMMON_JSON_STRUCTURES={"study_plan":{"weekly_schedule":[{"day":"string""sessions":[{"time":"string""focus":"string""activity":"string"}]}]"focus_areas":[{"skill":"string""topics":["string"]"resources":["string"]}]"study_techniques":[{"name":"string""description":"string""best_for":["string"]}]"goals":{"short_term":["string"]"long_term":["string"]}"review_topics":[{"name":"string""priority":"string""resources":["string"]}]}"assessment_questions":[{"question":"string""options":["string"]"correct_answer":"string""explanation":"string""difficulty":"string""topic":"string"}]"learning_analysis":{"strengths":["string"]"weaknesses":["string"]"recommendations":["string"]"learning_style":"string""progress_indicators":{"current_level":"string""target_level":"string""estimated_time":"string"}}"course_content":{"title":"string""description":"string""learning_objectives":["string"]"modules":[{"title":"string""content":"string""activities":["string"]"resources":["string"]}]"assessment_criteria":["string"]}"tutoring_response":{"explanation":"string""examples":["string"]"practice_questions":["string"]"additional_resources":["string"]"next_steps":["string"]}}#PrompttemplatesPROMPT_TEMPLATES={PromptType.STUDY_PLAN:PromptTemplate(template="""Generateapersonalizedstudyplanforastudentwiththefollowingprofile:StudentLevel:{student_level}WeakAreas:{weak_areas}CourseInformation:{course_info}AvailableTime:{available_time}LearningPreferences:{learning_preferences}Createacomprehensivestudyplanthatincludes:1.Weeklyschedulewithrecommendedstudytimes2.Focusareasbasedonthestudent'sweakskills3.Recommendedstudytechniques4.Short-termandlong-termgoals5.SpecifictopicstoreviewFormattheresponseasJSONwiththestructureprovided."""required_variables=["student_level""weak_areas"]optional_variables=["course_info""available_time""learning_preferences"]expected_response_type="json"json_structure=COMMON_JSON_STRUCTURES["study_plan"])PromptType.QUESTION_GENERATION:PromptTemplate(template="""Generate{num_questions}assessmentquestionsforthefollowingtopic:Topic:{topic}DifficultyLevel:{difficulty}QuestionType:{question_type}LearningObjectives:{learning_objectives}Eachquestionshould:1.Beclearandunambiguous2.Have4multiplechoiceoptions3.Includeadetailedexplanation4.AlignwiththespecifieddifficultylevelFormattheresponseasJSONwiththestructureprovided."""required_variables=["topic""num_questions""difficulty"]optional_variables=["question_type""learning_objectives"]expected_response_type="json"json_structure=COMMON_JSON_STRUCTURES["assessment_questions"])PromptType.ANALYSIS:PromptTemplate(template="""Analyzethefollowingstudentlearningdataandprovideinsights:StudentPerformanceData:{performance_data}AssessmentResults:{assessment_results}LearningHistory:{learning_history}CourseContext:{course_context}Provideacomprehensiveanalysisincluding:1.Identifiedstrengthsandweaknesses2.Learningpatternanalysis3.Personalizedrecommendations4.ProgressindicatorsFormattheresponseasJSONwiththestructureprovided."""required_variables=["performance_data"]optional_variables=["assessment_results""learning_history""course_context"]expected_response_type="json"json_structure=COMMON_JSON_STRUCTURES["learning_analysis"])PromptType.TUTORING:PromptTemplate(template="""Providetutoringassistanceforthefollowingquestion:StudentQuestion:{question}SubjectArea:{subject}StudentLevel:{level}Context:{context}Provideahelpfultutoringresponsethatincludes:1.Clearexplanationoftheconcept2.Relevantexamples3.Practicequestionsforreinforcement4.Additionallearningresources5.SuggestednextstepsFormattheresponseasJSONwiththestructureprovided."""required_variables=["question"]optional_variables=["subject""level""context"]expected_response_type="json"json_structure=COMMON_JSON_STRUCTURES["tutoring_response"])PromptType.EDUCATIONAL_CONTENT:PromptTemplate(template="""Createeducationalcontentforthefollowingspecifications:Topic:{topic}TargetAudience:{audience}LearningLevel:{level}ContentType:{content_type}Duration:{duration}Thecontentshould:1.Beengagingandappropriateforthetargetaudience2.Includeclearlearningobjectives3.Providestructuredmodulesorsections4.Includeassessmentcriteria5.BepedagogicallysoundFormattheresponseasJSONwiththestructureprovided."""required_variables=["topic""audience"]optional_variables=["level""content_type""duration"]expected_response_type="json"json_structure=COMMON_JSON_STRUCTURES["course_content"])}defget_prompt_template(prompt_type:PromptType)->PromptTemplate:"""Getaprompttemplatebytype"""returnPROMPT_TEMPLATES.get(prompt_type)defformat_prompt(prompt_type:PromptType**kwargs)->str:"""FormataprompttemplatewithprovidedvariablesArgs:prompt_type:Thetypeofprompttoformat**kwargs:VariablestosubstituteinthetemplateReturns:FormattedpromptstringRaises:ValueError:Ifrequiredvariablesaremissing"""template=get_prompt_template(prompt_type)ifnottemplate:raiseValueError(f"Unknownprompttype:{prompt_type}")#Checkforrequiredvariablesmissing_vars=[varforvarintemplate.required_variablesifvarnotinkwargs]ifmissing_vars:raiseValueError(f"Missingrequiredvariables:{missing_vars}")#Setdefaultvaluesforoptionalvariablesiftemplate.optional_variables:forvarintemplate.optional_variables:ifvarnotinkwargs:kwargs[var]=""#Formatthetemplatetry:returntemplate.template.format(**kwargs)exceptKeyErrorase:raiseValueError(f"Templateformattingfailed:{e}")defget_json_structure(structure_name:str)->Dict[strAny]:"""GetacommonJSONstructurebyname"""returnCOMMON_JSON_STRUCTURES.get(structure_name{})defvalidate_json_response(response:Dict[strAny]structure_name:str)->bool:"""ValidateaJSONresponseagainstacommonstructureArgs:response:Theresponsetovalidatestructure_name:NameofthestructuretovalidateagainstReturns:TrueifvalidFalseotherwise"""expected_structure=get_json_structure(structure_name)ifnotexpected_structure:returnFalsedefvalidate_structure(datastructure):ifisinstance(structuredict):ifnotisinstance(datadict):returnFalseforkeyvalueinstructure.items():ifkeynotindata:returnFalseifnotvalidate_structure(data[key]value):returnFalseelifisinstance(structurelist):ifnotisinstance(datalist):returnFalseiflen(structure)>0andlen(data)>0:returnvalidate_structure(data[0]structure[0])returnTruereturnvalidate_structure(responseexpected_structure)#CommonpromptfragmentsforreusePROMPT_FRAGMENTS={"json_instruction":"FormattheresponseasvalidJSONonlywithnoadditionaltextorformatting.""educational_context":"YouareanAIassistantforaneducationalplatform.Providehelpfulaccurateandpedagogicallysoundresponses.""student_context":"Considerthestudent'slearninglevelpreferencesandcurrentprogresswhenprovidingrecommendations.""assessment_context":"Ensurequestionsarefairunbiasedandalignedwithlearningobjectives.""quality_instruction":"Ensureallcontentishigh-qualityaccurateandappropriatefortheeducationalcontext."}defadd_prompt_fragment(prompt:strfragment_name:str)->str:"""Addacommonpromptfragmenttoaprompt"""fragment=PROMPT_FRAGMENTS.get(fragment_name"")iffragment:returnf"{prompt}\n\n{fragment}"returnprompt#Exportcommonlyusedfunctionsandclasses__all__=["PromptType""PromptTemplate""COMMON_JSON_STRUCTURES""PROMPT_TEMPLATES""get_prompt_template""format_prompt""get_json_structure""validate_json_response""add_prompt_fragment""PROMPT_FRAGMENTS"]
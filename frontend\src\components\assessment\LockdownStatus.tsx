/**
 * LockdownStatus Component
 * 
 * Displays the current lockdown and security monitoring status
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Chip,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Badge,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Security,
  Lock,
  Warning,
  CheckCircle,
  Error,
  ExpandMore,
  Visibility,
  Block,
  Computer,
  Shield
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

const StatusContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.background.paper,
  boxShadow: theme.shadows[1],
  border: `1px solid ${theme.palette.divider}`,
}));

const StatusRow = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  marginBottom: theme.spacing(1),
}));

interface ViolationLog {
  type: string;
  timestamp: number;
  data?: any;
}

interface LockdownEnvironment {
  is_seb: boolean;
  is_ldb: boolean;
  is_supported_browser: boolean;
  user_agent: string;
  handshake_token: string | null;
  lockdown_capable: boolean;
}

interface LockdownStatusProps {
  isLockdownCapable: boolean;
  isLockdownActive: boolean;
  environment: LockdownEnvironment | null;
  violations: ViolationLog[];
  isValidating: boolean;
  error: string | null;
}

export const LockdownStatus: React.FC<LockdownStatusProps> = ({
  isLockdownCapable,
  isLockdownActive,
  environment,
  violations,
  isValidating,
  error
}) => {
  const [showDetails, setShowDetails] = useState(false);
  
  // Get browser type
  const getBrowserType = (): string => {
    if (!environment) return 'Unknown';
    
    if (environment.is_seb) return 'Safe Exam Browser';
    if (environment.is_ldb) return 'Respondus LockDown Browser';
    
    const ua = environment.user_agent.toLowerCase();
    if (ua.includes('chrome')) return 'Chrome';
    if (ua.includes('firefox')) return 'Firefox';
    if (ua.includes('safari')) return 'Safari';
    if (ua.includes('edge')) return 'Edge';
    
    return 'Unknown Browser';
  };
  
  // Get recent violations (last 10 minutes)
  const getRecentViolations = (): ViolationLog[] => {
    const tenMinutesAgo = Date.now() - (10 * 60 * 1000);
    return violations.filter(v => v.timestamp > tenMinutesAgo);
  };
  
  // Get violation severity
  const getViolationSeverity = (type: string): 'low' | 'medium' | 'high' => {
    const highSeverity = ['DEVELOPER_TOOLS', 'SCREEN_CAPTURE', 'VIRTUAL_MACHINE', 'REMOTE_ACCESS'];
    const mediumSeverity = ['BROWSER_SWITCH', 'APP_SWITCH', 'FULLSCREEN_EXIT'];
    
    if (highSeverity.includes(type)) return 'high';
    if (mediumSeverity.includes(type)) return 'medium';
    return 'low';
  };
  
  // Count violations by severity
  const violationCounts = violations.reduce((acc, violation) => {
    const severity = getViolationSeverity(violation.type);
    acc[severity] = (acc[severity] || 0) + 1;
    return acc;
  }, {} as Record<'low' | 'medium' | 'high', number>);
  
  const recentViolations = getRecentViolations();
  const browserType = getBrowserType();
  
  // Main status determination
  const getMainStatus = () => {
    if (error) {
      return {
        icon: <Error color="error" />,
        label: 'Error',
        color: 'error' as const,
        message: error
      };
    }
    
    if (isValidating) {
      return {
        icon: <Security color="info" />,
        label: 'Validating',
        color: 'info' as const,
        message: 'Validating lockdown environment...'
      };
    }
    
    if (!isLockdownCapable) {
      return {
        icon: <Warning color="warning" />,
        label: 'Not Capable',
        color: 'warning' as const,
        message: 'Browser does not support lockdown mode'
      };
    }
    
    if (isLockdownActive) {
      return {
        icon: <Lock color="success" />,
        label: 'Active',
        color: 'success' as const,
        message: 'Lockdown mode is active and monitoring'
      };
    }
    
    return {
      icon: <CheckCircle color="success" />,
      label: 'Ready',
      color: 'success' as const,
      message: 'Environment is capable of lockdown mode'
    };
  };
  
  const mainStatus = getMainStatus();
  
  return (
    <StatusContainer>
      <StatusRow>
        <Box display="flex" alignItems="center" gap={1}>
          {mainStatus.icon}
          <Typography variant="h6">
            Exam Security
          </Typography>
        </Box>
        <Chip
          label={mainStatus.label}
          color={mainStatus.color}
          variant="filled"
          size="small"
        />
      </StatusRow>
      
      {mainStatus.message && (
        <Alert severity={mainStatus.color} size="small" sx={{ mb: 2 }}>
          {mainStatus.message}
        </Alert>
      )}
      
      {/* Browser and Environment Info */}
      <StatusRow>
        <Typography variant="body2" color="text.secondary">
          Browser:
        </Typography>
        <Box display="flex" alignItems="center" gap={1}>
          <Computer fontSize="small" />
          <Typography variant="body2">
            {browserType}
          </Typography>
          {environment?.handshake_token && (
            <Tooltip title="Authenticated with handshake token">
              <Shield fontSize="small" color="success" />
            </Tooltip>
          )}
        </Box>
      </StatusRow>
      
      {/* Violations Summary */}
      {violations.length > 0 && (
        <StatusRow>
          <Typography variant="body2" color="text.secondary">
            Security Events:
          </Typography>
          <Box display="flex" gap={0.5}>
            {violationCounts.high > 0 && (
              <Chip
                label={`${violationCounts.high} High`}
                color="error"
                size="small"
                variant="outlined"
              />
            )}
            {violationCounts.medium > 0 && (
              <Chip
                label={`${violationCounts.medium} Medium`}
                color="warning"
                size="small"
                variant="outlined"
              />
            )}
            {violationCounts.low > 0 && (
              <Chip
                label={`${violationCounts.low} Low`}
                color="info"
                size="small"
                variant="outlined"
              />
            )}
          </Box>
        </StatusRow>
      )}
      
      {/* Recent Activity */}
      {recentViolations.length > 0 && (
        <StatusRow>
          <Typography variant="body2" color="text.secondary">
            Recent Activity:
          </Typography>
          <Badge badgeContent={recentViolations.length} color="warning">
            <IconButton
              size="small"
              onClick={() => setShowDetails(!showDetails)}
            >
              <Visibility fontSize="small" />
            </IconButton>
          </Badge>
        </StatusRow>
      )}
      
      {/* Detailed Violations */}
      {violations.length > 0 && (
        <Accordion
          expanded={showDetails}
          onChange={() => setShowDetails(!showDetails)}
          sx={{ mt: 1 }}
        >
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="body2">
              Security Event Details ({violations.length} total)
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List dense>
              {recentViolations.slice(0, 10).map((violation, index) => {
                const severity = getViolationSeverity(violation.type);
                const severityColors = {
                  high: 'error',
                  medium: 'warning',
                  low: 'info'
                } as const;
                
                return (
                  <ListItem key={index} divider>
                    <ListItemIcon>
                      <Block color={severityColors[severity]} fontSize="small" />
                    </ListItemIcon>
                    <ListItemText
                      primary={violation.type.replace(/_/g, ' ')}
                      secondary={new Date(violation.timestamp).toLocaleTimeString()}
                    />
                    <Chip
                      label={severity}
                      color={severityColors[severity]}
                      size="small"
                      variant="outlined"
                    />
                  </ListItem>
                );
              })}
              {recentViolations.length > 10 && (
                <ListItem>
                  <ListItemText
                    primary={`... and ${recentViolations.length - 10} more recent events`}
                    sx={{ fontStyle: 'italic', color: 'text.secondary' }}
                  />
                </ListItem>
              )}
            </List>
          </AccordionDetails>
        </Accordion>
      )}
      
      {/* Security Features Status */}
      {isLockdownActive && (
        <Box mt={2}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Active Protections:
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={0.5}>
            <Chip label="Tab Monitoring" size="small" color="success" variant="outlined" />
            <Chip label="Copy/Paste Blocked" size="small" color="success" variant="outlined" />
            <Chip label="Right-Click Disabled" size="small" color="success" variant="outlined" />
            <Chip label="Print Prevention" size="small" color="success" variant="outlined" />
            <Chip label="Focus Tracking" size="small" color="success" variant="outlined" />
            <Chip label="Keyboard Shortcuts Blocked" size="small" color="success" variant="outlined" />
          </Box>
        </Box>
      )}
    </StatusContainer>
  );
};

export default LockdownStatus;

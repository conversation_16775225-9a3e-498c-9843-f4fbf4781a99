"""
Office hours views for the courses app.
Provides office hours management functionality.
"""
import logging
from django.apps import apps
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework import permissions, serializers, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

# Import models dynamically to avoid circular imports
try:
    OfficeHours = apps.get_model("courses", "OfficeHours")
except LookupError:
    OfficeHours = None

try:
    Course = apps.get_model("courses", "Course")
except LookupError:
    Course = None

try:
    Enrollment = apps.get_model("courses", "Enrollment")
except LookupError:
    Enrollment = None

# Import permission classes
try:
    from core.permissions import IsProfessorUser, IsStudentUser
except ImportError:
    from rest_framework.permissions import IsAuthenticated
    IsProfessorUser = IsAuthenticated
    IsStudentUser = IsAuthenticated

User = get_user_model()
logger = logging.getLogger(__name__)


class OfficeHoursSerializer(serializers.Serializer):
    """Serializer for office hours"""
    id = serializers.IntegerField(read_only=True)
    professor = serializers.IntegerField(read_only=True)
    professor_name = serializers.CharField(read_only=True)
    day = serializers.CharField(max_length=10)
    day_display = serializers.CharField(read_only=True)
    start_time = serializers.TimeField()
    end_time = serializers.TimeField()
    location = serializers.CharField(max_length=200, required=False)
    is_online = serializers.BooleanField(default=False)
    meeting_link = serializers.URLField(required=False)
    notes = serializers.CharField(required=False)
    
    def validate(self, data):
        """Validate the office hours data"""
        if data.get("end_time") and data.get("start_time"):
            if data.get("end_time") <= data.get("start_time"):
                raise serializers.ValidationError("End time must be after start time")
        
        if data.get("is_online") and not data.get("meeting_link"):
            raise serializers.ValidationError("Meeting link is required for online office hours")
        
        return data


class OfficeHoursViewSet(viewsets.ViewSet):
    """API endpoints for managing office hours"""
    permission_classes = [permissions.IsAuthenticated, IsProfessorUser]
    
    def list(self, request):
        """Get office hours for the requesting professor"""
        try:
            if not OfficeHours:
                return Response({
                    "status": "success",
                    "data": [],
                    "message": "OfficeHours model not available"
                })
            
            office_hours = OfficeHours.objects.filter(professor=request.user).order_by('day', 'start_time')
            office_hours_data = []
            
            for oh in office_hours:
                oh_data = {
                    'id': oh.id,
                    'professor': oh.professor.id,
                    'professor_name': f"{oh.professor.first_name} {oh.professor.last_name}".strip(),
                    'day': getattr(oh, 'day', ''),
                    'day_display': getattr(oh, 'day', ''),
                    'start_time': getattr(oh, 'start_time', None),
                    'end_time': getattr(oh, 'end_time', None),
                    'location': getattr(oh, 'location', ''),
                    'is_online': getattr(oh, 'is_online', False),
                    'meeting_link': getattr(oh, 'meeting_link', ''),
                    'notes': getattr(oh, 'notes', '')
                }
                office_hours_data.append(oh_data)
            
            return Response({
                "status": "success",
                "data": office_hours_data,
                "count": len(office_hours_data)
            })
        except Exception as e:
            logger.error(f"Error fetching office hours: {e}")
            return Response({
                "status": "error",
                "message": str(e),
                "data": []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def create(self, request):
        """Create new office hours"""
        try:
            if not OfficeHours:
                return Response({
                    "status": "error",
                    "message": "OfficeHours model not available"
                }, status=status.HTTP_400_BAD_REQUEST)
            
            serializer = OfficeHoursSerializer(data=request.data)
            if serializer.is_valid():
                # Create office hours (simplified - in real implementation, save to database)
                oh_data = {
                    "id": 1,  # Would be generated by database
                    **serializer.validated_data,
                    "professor": request.user.id,
                    "professor_name": f"{request.user.first_name} {request.user.last_name}".strip()
                }
                
                return Response({
                    "status": "success",
                    "data": oh_data,
                    "message": "Office hours created successfully"
                }, status=status.HTTP_201_CREATED)
            
            return Response({
                "status": "error",
                "errors": serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error creating office hours: {e}")
            return Response({
                "status": "error",
                "message": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def retrieve(self, request, pk=None):
        """Get specific office hours"""
        try:
            if not OfficeHours:
                return Response({
                    "status": "error",
                    "message": "OfficeHours model not available"
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Simplified response - in real implementation, get from database
            return Response({
                "status": "success",
                "data": {
                    "id": pk,
                    "professor": request.user.id,
                    "professor_name": f"{request.user.first_name} {request.user.last_name}".strip(),
                    "day": "MON",
                    "day_display": "Monday",
                    "start_time": "09:00:00",
                    "end_time": "11:00:00",
                    "location": "Office 101",
                    "is_online": False,
                    "meeting_link": "",
                    "notes": "Sample office hours"
                }
            })
        except Exception as e:
            logger.error(f"Error retrieving office hours: {e}")
            return Response({
                "status": "error",
                "message": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def update(self, request, pk=None):
        """Update office hours"""
        try:
            serializer = OfficeHoursSerializer(data=request.data)
            if serializer.is_valid():
                return Response({
                    "status": "success",
                    "data": {
                        "id": pk,
                        **serializer.validated_data,
                        "professor": request.user.id,
                        "professor_name": f"{request.user.first_name} {request.user.last_name}".strip()
                    },
                    "message": "Office hours updated successfully"
                })
            
            return Response({
                "status": "error",
                "errors": serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error updating office hours: {e}")
            return Response({
                "status": "error",
                "message": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def destroy(self, request, pk=None):
        """Delete office hours"""
        try:
            return Response({
                "status": "success",
                "message": "Office hours deleted successfully"
            }, status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            logger.error(f"Error deleting office hours: {e}")
            return Response({
                "status": "error",
                "message": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=["get"])
    def current(self, request):
        """Get current day's office hours"""
        try:
            today = timezone.localtime().strftime("%a").upper()[:3]
            
            if not OfficeHours:
                return Response({
                    "status": "success",
                    "data": [],
                    "message": "OfficeHours model not available"
                })
            
            # Get today's office hours for the professor
            office_hours = OfficeHours.objects.filter(
                professor=request.user,
                day=today
            ).order_by('start_time')
            
            office_hours_data = []
            for oh in office_hours:
                oh_data = {
                    'id': oh.id,
                    'professor': oh.professor.id,
                    'professor_name': f"{oh.professor.first_name} {oh.professor.last_name}".strip(),
                    'day': getattr(oh, 'day', ''),
                    'day_display': getattr(oh, 'day', ''),
                    'start_time': getattr(oh, 'start_time', None),
                    'end_time': getattr(oh, 'end_time', None),
                    'location': getattr(oh, 'location', ''),
                    'is_online': getattr(oh, 'is_online', False),
                    'meeting_link': getattr(oh, 'meeting_link', ''),
                    'notes': getattr(oh, 'notes', '')
                }
                office_hours_data.append(oh_data)
            
            return Response({
                "status": "success",
                "data": office_hours_data,
                "count": len(office_hours_data)
            })
        except Exception as e:
            logger.error(f"Error fetching current office hours: {e}")
            return Response({
                "status": "error",
                "message": str(e),
                "data": []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class StudentOfficeHoursViewSet(viewsets.ViewSet):
    """API endpoints for students to view office hours"""
    permission_classes = [permissions.IsAuthenticated, IsStudentUser]
    
    def list(self, request):
        """Get office hours for professors of courses the student is enrolled in"""
        try:
            if not OfficeHours or not Course or not Enrollment:
                return Response({
                    "status": "success",
                    "data": [],
                    "message": "Required models not available"
                })
            
            # Get courses where the student is enrolled
            enrolled_courses = Course.objects.filter(
                enrollments__user=request.user,
                enrollments__status__in=['APPROVED', 'active']
            )
            
            # Get professors of those courses
            professors = enrolled_courses.values_list('instructor', flat=True).distinct()
            
            # Get office hours for those professors
            office_hours = OfficeHours.objects.filter(
                professor__in=professors
            ).select_related('professor').order_by('day', 'start_time')
            
            office_hours_data = []
            for oh in office_hours:
                oh_data = {
                    'id': oh.id,
                    'professor': oh.professor.id,
                    'professor_name': f"{oh.professor.first_name} {oh.professor.last_name}".strip(),
                    'day': getattr(oh, 'day', ''),
                    'day_display': getattr(oh, 'day', ''),
                    'start_time': getattr(oh, 'start_time', None),
                    'end_time': getattr(oh, 'end_time', None),
                    'location': getattr(oh, 'location', ''),
                    'is_online': getattr(oh, 'is_online', False),
                    'meeting_link': getattr(oh, 'meeting_link', ''),
                    'notes': getattr(oh, 'notes', '')
                }
                office_hours_data.append(oh_data)
            
            return Response({
                "status": "success",
                "data": office_hours_data,
                "count": len(office_hours_data)
            })
        except Exception as e:
            logger.error(f"Error fetching student office hours: {e}")
            return Response({
                "status": "error",
                "message": str(e),
                "data": []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

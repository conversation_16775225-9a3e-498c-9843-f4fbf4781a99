import { lazy, ComponentType, memo } from 'react';
import { lazyRetry } from './lazyRetry';

// Types for skeleton components
export type SkeletonType = 'courseCard' | 'courseList' | 'courseGenerator' | 'materialCard' | 'dashboard' | 'assessment' | 'analytics' | 'generic';

/**
 * Enhanced lazy component loader with automatic retry and error handling
 * @param importFunc - Function that returns a promise resolving to a module with default export
 * @param fallbackType - Type of skeleton to show while loading
 * @param enableRetry - Whether to enable automatic retry on failed imports
 * @param retryAttempts - Number of retry attempts for failed imports
 * @returns Lazy-loaded component wrapped with error boundary and loading state
 */
export const createLazyComponent = <T = any>(
  importFunc: () => Promise<{ default: ComponentType<T> }>,
  fallbackType: SkeletonType = 'generic',
  enableRetry: boolean = true,
  retryAttempts: number = 3
) => {
  // Create lazy component with optional retry logic
  const LazyComponent = enableRetry 
    ? lazy(() => lazyRetry(importFunc, retryAttempts))
    : lazy(importFunc);
  
  // Return memoized component for performance
  return memo((props: T) => {
    // Dynamic import of LazyLoadWrapper to avoid circular dependencies
    const LazyLoadWrapper = lazy(() => import('../components/courses/LazyLoadWrapper'));
    
    return (
      <LazyLoadWrapper fallback={fallbackType} errorBoundary={true}>
        <LazyComponent {...props} />
      </LazyLoadWrapper>
    );
  });
};

/**
 * Optimized loader for course-related components
 */
export const createCourseComponent = <T = any>(
  importFunc: () => Promise<{ default: ComponentType<T> }>,
  fallbackType: SkeletonType = 'courseCard'
) => createLazyComponent(importFunc, fallbackType);

/**
 * Optimized loader for dashboard components
 */
export const createDashboardComponent = <T = any>(
  importFunc: () => Promise<{ default: ComponentType<T> }>
) => createLazyComponent(importFunc, 'dashboard');

/**
 * Optimized loader for assessment components
 */
export const createAssessmentComponent = <T = any>(
  importFunc: () => Promise<{ default: ComponentType<T> }>
) => createLazyComponent(importFunc, 'assessment');

/**
 * Optimized loader for analytics components
 */
export const createAnalyticsComponent = <T = any>(
  importFunc: () => Promise<{ default: ComponentType<T> }>
) => createLazyComponent(importFunc, 'analytics');

/**
 * Preloader utility to prefetch components for better UX
 * @param importFunc - Function that returns a promise resolving to a module
 */
export const preloadComponent = (
  importFunc: () => Promise<{ default: ComponentType<any> }>
) => {
  // Preload during idle time
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      importFunc().catch(() => {
        // Silently fail preloading - component will load normally when needed
      });
    });
  } else {
    // Fallback for browsers without requestIdleCallback
    setTimeout(() => {
      importFunc().catch(() => {
        // Silently fail preloading
      });
    }, 100);
  }
};

/**
 * Batch preloader for multiple components
 * @param importFuncs - Array of import functions to preload
 */
export const preloadComponents = (
  importFuncs: Array<() => Promise<{ default: ComponentType<any> }>>
) => {
  importFuncs.forEach((importFunc, index) => {
    // Stagger preloading to avoid overwhelming the network
    setTimeout(() => preloadComponent(importFunc), index * 50);
  });
};

/**
 * Route-based preloader - preloads components likely to be needed next
 * @param currentRoute - Current route path
 */
export const preloadRouteComponents = (currentRoute: string) => {
  const preloadMap: Record<string, Array<() => Promise<{ default: ComponentType<any> }>>> = {
    '/student': [
      () => import('../features/student/StudentCourses'),
      () => import('../features/student/LearningPathPage'),
    ],
    '/student/courses': [
      () => import('../features/student/CourseDetail'),
      () => import('../components/courses/UnifiedCourseList'),
    ],
    '/admin': [
      () => import('../features/admin/courses/AdminCourseList'),
      () => import('../features/admin/users/EnhancedUsersList'),
    ],
    '/professor': [
      () => import('../features/professor/ProfessorCoursesList'),
      () => import('../features/professor/ProfessorDashboardHome'),
    ],
  };

  const componentsToPreload = preloadMap[currentRoute];
  if (componentsToPreload) {
    preloadComponents(componentsToPreload);
  }
};

// Export convenience functions for common component types
export const lazy = {
  course: createCourseComponent,
  dashboard: createDashboardComponent,
  assessment: createAssessmentComponent,
  analytics: createAnalyticsComponent,
  generic: createLazyComponent,
};

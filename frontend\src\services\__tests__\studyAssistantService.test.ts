/**
 * Study Assistant Service Tests
 *
 * Comprehensive tests for the Study Assistant Service to ensure
 * it works correctly with study planning and spaced repetition.
 */

import studyAssistantService from '../studyAssistantService';
import { BaseAIService } from '../utils/BaseAIService';
import { AIServiceError } from '../utils/aiServiceUtils';
import axiosInstance from '../../config/axios';

import mockAxios from 'jest-mock-axios';

describe('Study Assistant Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Structure', () => {
    test('should be instance of BaseAIService', () => {
      expect(studyAssistantService).toBeInstanceOf(BaseAIService);
    });

    test('should have correct service info', () => {
      const info = studyAssistantService.getServiceInfo();
      expect(info.name).toBe('Study Assistant');
      expect(info.endpoint).toContain('study');
    });

    test('should have all required methods', () => {
      expect(typeof studyAssistantService.getStudySessions).toBe('function');
      expect(typeof studyAssistantService.startStudySession).toBe('function');
      expect(typeof studyAssistantService.endStudySession).toBe('function');
      expect(typeof studyAssistantService.getStudyTopics).toBe('function');
      expect(typeof studyAssistantService.generateStudyPlan).toBe('function');
      expect(typeof studyAssistantService.getRecommendations).toBe('function');
      expect(typeof studyAssistantService.markRecommendationImplemented).toBe(
        'function'
      );
      expect(typeof studyAssistantService.getDueItems).toBe('function');
      expect(typeof studyAssistantService.updateReview).toBe('function');
      expect(typeof studyAssistantService.createFlashcard).toBe('function');
      expect(typeof studyAssistantService.generateFlashcards).toBe('function');
      expect(typeof studyAssistantService.generatePracticeQuestions).toBe(
        'function'
      );
      expect(typeof studyAssistantService.checkAnswer).toBe('function');
      expect(typeof studyAssistantService.getMyQuestions).toBe('function');
    });
  });

  describe('Study Sessions', () => {
    test('should get study sessions successfully', async () => {
      const mockSessions = [
        {
          id: 1,
          start_time: '2024-01-01T10:00:00Z',
          end_time: '2024-01-01T11:30:00Z',
          duration: 90,
          topics_covered: ['algebra', 'geometry'],
          productivity_score: 0.85,
        },
        {
          id: 2,
          start_time: '2024-01-02T14:00:00Z',
          end_time: null,
          duration: null,
          topics_covered: ['calculus'],
          productivity_score: null,
        },
      ];

      mockAxios.get.mockResolvedValueOnce({
        data: mockSessions,
      });

      const result = await studyAssistantService.getStudySessions();

      expect(mockAxios.get).toHaveBeenCalledWith('sessions/');
      expect(result).toEqual(mockSessions);
    });

    test('should start study session successfully', async () => {
      const mockSession = {
        id: 3,
        start_time: '2024-01-03T09:00:00Z',
        status: 'active',
      };

      mockAxios.post.mockResolvedValueOnce({
        data: mockSession,
      });

      const result = await studyAssistantService.startStudySession();

      expect(mockAxios.post).toHaveBeenCalledWith('sessions/start_session/');
      expect(result).toEqual(mockSession);
    });

    test('should end study session successfully', async () => {
      const mockSession = {
        id: 3,
        start_time: '2024-01-03T09:00:00Z',
        end_time: '2024-01-03T10:30:00Z',
        duration: 90,
        status: 'completed',
      };

      mockAxios.post.mockResolvedValueOnce({
        data: mockSession,
      });

      const result = await studyAssistantService.endStudySession(3);

      expect(mockAxios.post).toHaveBeenCalledWith('sessions/3/end_session/');
      expect(result).toEqual(mockSession);
    });
  });

  describe('Study Planning', () => {
    test('should get study topics successfully', async () => {
      const mockTopics = [
        {
          id: 1,
          name: 'Algebra',
          difficulty: 'intermediate',
          estimated_hours: 20,
        },
        {
          id: 2,
          name: 'Calculus',
          difficulty: 'advanced',
          estimated_hours: 40,
        },
      ];

      mockAxios.get.mockResolvedValueOnce({
        data: mockTopics,
      });

      const result = await studyAssistantService.getStudyTopics();

      expect(mockAxios.get).toHaveBeenCalledWith('topics/');
      expect(result).toEqual(mockTopics);
    });

    test('should generate study plan successfully', async () => {
      const mockPlan = {
        id: 1,
        course_id: 123,
        total_duration: '8 weeks',
        weekly_schedule: [
          { week: 1, topics: ['intro_to_algebra'], hours: 5 },
          { week: 2, topics: ['linear_equations'], hours: 6 },
        ],
        milestones: [
          { week: 4, description: 'Complete algebra fundamentals' },
          { week: 8, description: 'Ready for advanced topics' },
        ],
      };

      mockAxios.post.mockResolvedValueOnce({
        data: mockPlan,
      });

      const result = await studyAssistantService.generateStudyPlan(123);

      expect(mockAxios.post).toHaveBeenCalledWith(
        'assistant/generate_study_plan/',
        {
          course_id: 123,
        }
      );
      expect(result).toEqual(mockPlan);
    });

    test('should generate study plan without course ID', async () => {
      const mockPlan = { id: 2, total_duration: '12 weeks' };

      mockAxios.post.mockResolvedValueOnce({
        data: mockPlan,
      });

      await studyAssistantService.generateStudyPlan();

      expect(mockAxios.post).toHaveBeenCalledWith(
        'assistant/generate_study_plan/',
        {
          course_id: undefined,
        }
      );
    });

    test('should get recommendations successfully', async () => {
      const mockRecommendations = [
        {
          id: 1,
          type: 'study_schedule',
          title: 'Optimize study timing',
          description: 'Study math in the morning for better retention',
          priority: 'high',
        },
        {
          id: 2,
          type: 'content',
          title: 'Review weak areas',
          description: 'Focus on quadratic equations',
          priority: 'medium',
        },
      ];

      mockAxios.get.mockResolvedValueOnce({
        data: mockRecommendations,
      });

      const result = await studyAssistantService.getRecommendations();

      expect(mockAxios.get).toHaveBeenCalledWith(
        'assistant/recommendations/'
      );
      expect(result).toEqual(mockRecommendations);
    });

    test('should mark recommendation as implemented', async () => {
      const mockResponse = {
        success: true,
        message: 'Recommendation marked as implemented',
      };

      mockAxios.post.mockResolvedValueOnce({
        data: mockResponse,
      });

      const result =
        await studyAssistantService.markRecommendationImplemented(1);

      expect(mockAxios.post).toHaveBeenCalledWith(
        'assistant/mark_recommendation_implemented/',
        {
          recommendation_id: 1,
        }
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Spaced Repetition', () => {
    test('should get due items successfully', async () => {
      const mockDueItems = [
        {
          id: 1,
          content: 'What is the derivative of x²?',
          type: 'flashcard',
          due_date: '2024-01-01T12:00:00Z',
          difficulty: 0.3,
        },
        {
          id: 2,
          content: 'Solve: 2x + 5 = 15',
          type: 'practice_question',
          due_date: '2024-01-01T14:00:00Z',
          difficulty: 0.2,
        },
      ];

      mockAxios.get.mockResolvedValueOnce({
        data: mockDueItems,
      });

      const result = await studyAssistantService.getDueItems(10);

      expect(mockAxios.get).toHaveBeenCalledWith(
        'spaced-repetition/due_items/',
        {
          limit: 10,
        }
      );
      expect(result).toEqual(mockDueItems);
    });

    test('should use default limit for due items', async () => {
      mockAxios.get.mockResolvedValueOnce({ data: [] });

      await studyAssistantService.getDueItems();

      expect(mockAxios.get).toHaveBeenCalledWith(
        'spaced-repetition/due_items/',
        {
          limit: 20,
        }
      );
    });

    test('should update review successfully', async () => {
      const mockUpdatedItem = {
        id: 1,
        next_review_date: '2024-01-05T12:00:00Z',
        difficulty: 0.25,
        interval: 4,
      };

      mockAxios.post.mockResolvedValueOnce({
        data: mockUpdatedItem,
      });

      const result = await studyAssistantService.updateReview(1, 4);

      expect(mockAxios.post).toHaveBeenCalledWith(
        'spaced-repetition/update_review/',
        {
          item_id: 1,
          performance_rating: 4,
        }
      );
      expect(result).toEqual(mockUpdatedItem);
    });
  });

  describe('Flashcards', () => {
    test('should create flashcard successfully', async () => {
      const mockFlashcard = {
        id: 1,
        topic_id: 5,
        front: 'What is the quadratic formula?',
        back: 'x = (-b ± √(b²-4ac)) / 2a',
        created_at: '2024-01-01T10:00:00Z',
      };

      mockAxios.post.mockResolvedValueOnce({
        data: mockFlashcard,
      });

      const result = await studyAssistantService.createFlashcard(
        5,
        'What is the quadratic formula?',
        'x = (-b ± √(b²-4ac)) / 2a'
      );

      expect(mockAxios.post).toHaveBeenCalledWith(
        'spaced-repetition/create_flashcard/',
        {
          topic_id: 5,
          front: 'What is the quadratic formula?',
          back: 'x = (-b ± √(b²-4ac)) / 2a',
        }
      );
      expect(result).toEqual(mockFlashcard);
    });

    test('should generate flashcards successfully', async () => {
      const mockFlashcards = [
        {
          id: 2,
          front: 'What is a derivative?',
          back: 'The rate of change of a function',
        },
        {
          id: 3,
          front: 'What is an integral?',
          back: 'The area under a curve',
        },
      ];

      mockAxios.post.mockResolvedValueOnce({
        data: mockFlashcards,
      });

      const result = await studyAssistantService.generateFlashcards(10, 3);

      expect(mockAxios.post).toHaveBeenCalledWith(
        'spaced-repetition/generate_flashcards/',
        {
          topic_id: 10,
          count: 3,
        }
      );
      expect(result).toEqual(mockFlashcards);
    });

    test('should use default count for generating flashcards', async () => {
      mockAxios.post.mockResolvedValueOnce({ data: [] });

      await studyAssistantService.generateFlashcards(10);

      expect(mockAxios.post).toHaveBeenCalledWith(
        'spaced-repetition/generate_flashcards/',
        {
          topic_id: 10,
          count: 5,
        }
      );
    });
  });

  describe('Practice Questions', () => {
    test('should generate practice questions successfully', async () => {
      const mockQuestions = [
        {
          id: 1,
          question: 'Solve for x: 3x + 7 = 22',
          type: 'multiple_choice',
          options: ['x = 5', 'x = 7', 'x = 3', 'x = 9'],
          correct_answer: 'x = 5',
        },
      ];

      mockAxios.post.mockResolvedValueOnce({
        data: mockQuestions,
      });

      const params = {
        topicId: 5,
        skillId: 10,
        count: 3,
        questionTypes: ['multiple_choice', 'short_answer'],
      };

      const result =
        await studyAssistantService.generatePracticeQuestions(params);

      expect(mockAxios.post).toHaveBeenCalledWith(
        'practice-questions/generate_questions/',
        {
          topic_id: 5,
          skill_id: 10,
          count: 3,
          question_types: ['multiple_choice', 'short_answer'],
        }
      );
      expect(result).toEqual(mockQuestions);
    });

    test('should use default values for practice questions', async () => {
      mockAxios.post.mockResolvedValueOnce({ data: [] });

      await studyAssistantService.generatePracticeQuestions({});

      expect(mockAxios.post).toHaveBeenCalledWith(
        'practice-questions/generate_questions/',
        {
          topic_id: undefined,
          skill_id: undefined,
          count: 5,
          question_types: [],
        }
      );
    });

    test('should check answer successfully', async () => {
      const mockResult = {
        correct: true,
        explanation: 'Correct! 3x + 7 = 22, so 3x = 15, therefore x = 5',
        score: 1.0,
      };

      mockAxios.post.mockResolvedValueOnce({
        data: mockResult,
      });

      const result = await studyAssistantService.checkAnswer(1, 'x = 5');

      expect(mockAxios.post).toHaveBeenCalledWith(
        'practice-questions/check_answer/',
        {
          question_id: 1,
          student_answer: 'x = 5',
        }
      );
      expect(result).toEqual(mockResult);
    });

    test('should get my questions successfully', async () => {
      const mockQuestions = [
        { id: 1, question: 'Question 1', status: 'completed' },
        { id: 2, question: 'Question 2', status: 'pending' },
      ];

      mockAxios.get.mockResolvedValueOnce({
        data: mockQuestions,
      });

      const result = await studyAssistantService.getMyQuestions();

      expect(mockAxios.get).toHaveBeenCalledWith(
        'practice-questions/my_questions/'
      );
      expect(result).toEqual(mockQuestions);
    });
  });

  describe('Configuration', () => {
    test('should have appropriate default configuration', () => {
      const config = studyAssistantService.getConfig();

      expect(config.timeout).toBe(45000); // 45 seconds
      expect(config.retries).toBe(2);
      expect(config.fallbackEnabled).toBe(true);
      expect(config.cacheEnabled).toBe(true);
      expect(config.cacheTTL).toBe(300000); // 5 minutes
    });
  });

  describe('Error Handling', () => {
    test('should handle AI generation errors gracefully', async () => {
      const aiError = new AIServiceError(
        'AI service unavailable',
        'SERVER_ERROR',
        503
      );
      mockAxios.post.mockRejectedValueOnce(aiError);

      // Should handle gracefully with fallback
      await expect(
        studyAssistantService.generateFlashcards(1)
      ).resolves.not.toThrow();
    });

    test('should handle validation errors appropriately', async () => {
      const validationError = new AIServiceError(
        'Invalid topic ID',
        'VALIDATION_ERROR',
        400
      );
      mockAxios.post.mockRejectedValueOnce(validationError);

      await expect(
        studyAssistantService.createFlashcard(-1, '', '')
      ).rejects.toThrow();
    });
  });
});

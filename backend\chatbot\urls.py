from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>
from .views import (
    ChatConversationViewSet,
    ChatMessageViewSet,
    ChatSessionViewSet,
    ChatView,
    PathAdvisorChatBotView,
    UserLearningProfileViewSet
)

router = DefaultRouter()
router.register(r"conversations", ChatConversationViewSet, basename="chat-conversation")
router.register(r"messages", ChatMessageViewSet, basename="chat-message")
router.register(r"sessions", ChatSessionViewSet, basename="chat-session")
router.register(r"learning-profile", UserLearningProfileViewSet, basename="learning-profile")

urlpatterns = [
    path("", include(router.urls)),
    path("chat/", ChatView.as_view(), name="chat"),
    path("path-advisor-chat/", PathAdvisorChatBotView.as_view(), name="path-advisor-chat"),
]
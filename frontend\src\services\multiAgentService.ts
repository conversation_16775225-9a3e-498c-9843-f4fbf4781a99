import axiosInstance from '../config/axios';
import { API_ENDPOINTS } from '../config/api';

// 🤖 Multi-Agent Service - Interface for role-based AI agents
// Provides access to specialized AI agents based on user role

export interface AgentInfo {
  name: string;
  type: string;
  specialization: string;
  status: string;
  icon: string;
  keywords: string[];
}

export interface AgentStatusResponse {
  status: string;
  data: {
    agents: AgentInfo[];
    total_agents: number;
    orchestrator_status: string;
    active_sessions: number;
    student_models: number;
  };
}

export interface RoutingTestRequest {
  message: string;
  context?: Record<string, any>;
}

export interface RoutingTestResponse {
  status: string;
  data: {
    message: string;
    predicted_agent: string;
    agent_info: {
      name: string;
      icon: string;
      reasoning: string;
    };
    is_tutoring_request: boolean;
    routing_logic: {
      tutoring_detected: boolean;
      keywords_found: string[];
    };
  };
}

export interface AgentTestRequest {
  agent_type: string;
  message: string;
  context?: Record<string, any>;
}

export interface AgentTestResponse {
  status: string;
  data: {
    agent_type: string;
    agent_name: string;
    message: string;
    response: {
      content: string;
      confidence: number;
      reasoning: string;
      recommendations: string[];
      next_actions: string[];
      metadata: Record<string, any>;
    };
  };
}

const multiAgentService = {
  /**
   * Get status of all available AI agents
   */
  getAgentStatus: async (): Promise<AgentStatusResponse> => {
    try {
      const response = await axiosInstance.get(
        API_ENDPOINTS.MULTI_AGENT.STATUS
      );
      return response.data;
    } catch (error: any) {
      console.error('Error getting agent status:', error);
      throw new Error(
        error.response?.data?.message || 'Failed to get agent status'
      );
    }
  },

  /**
   * Test which agent would handle a given message
   */
  testAgentRouting: async (
    request: RoutingTestRequest
  ): Promise<RoutingTestResponse> => {
    try {
      const response = await axiosInstance.post(
        API_ENDPOINTS.MULTI_AGENT.TEST_ROUTING,
        request
      );
      return response.data;
    } catch (error: any) {
      console.error('Error testing agent routing:', error);
      throw new Error(
        error.response?.data?.message || 'Failed to test agent routing'
      );
    }
  },

  /**
   * Test getting a response from a specific agent
   */
  testAgentResponse: async (
    request: AgentTestRequest
  ): Promise<AgentTestResponse> => {
    try {
      const response = await axiosInstance.post(
        API_ENDPOINTS.MULTI_AGENT.TEST_RESPONSE,
        request
      );
      return response.data;
    } catch (error: any) {
      console.error('Error testing agent response:', error);
      throw new Error(
        error.response?.data?.message || 'Failed to test agent response'
      );
    }
  },

  /**
   * Get role-specific agent recommendations
   */
  getRoleAgents: (role: string): AgentInfo[] => {
    const roleAgentMap: Record<string, Partial<AgentInfo>[]> = {
      STUDENT: [
        {
          name: 'General Tutor',
          icon: '🎓',
          specialization: 'General tutoring and education',
        },
        {
          name: 'Math Tutor',
          icon: '🔢',
          specialization: 'Mathematics and problem solving',
        },
        {
          name: 'Science Tutor',
          icon: '🔬',
          specialization: 'Science concepts and experiments',
        },
        {
          name: 'Language Tutor',
          icon: '📝',
          specialization: 'Writing and language arts',
        },
        {
          name: 'Career Advisor',
          icon: '🎯',
          specialization: 'Academic and career guidance',
        },
      ],
      PROFESSOR: [
        {
          name: 'Assessor',
          icon: '📊',
          specialization: 'Create quizzes and assessments',
        },
        {
          name: 'Content Creator',
          icon: '✨',
          specialization: 'Generate educational content',
        },
        {
          name: 'General Tutor',
          icon: '🎓',
          specialization: 'Understand student needs',
        },
        {
          name: 'Math Tutor',
          icon: '🔢',
          specialization: 'Math curriculum development',
        },
        {
          name: 'Science Tutor',
          icon: '🔬',
          specialization: 'Science lesson planning',
        },
      ],
      ADMIN: [
        {
          name: 'Assessor',
          icon: '📊',
          specialization: 'System-wide assessment analytics',
        },
        {
          name: 'Content Creator',
          icon: '✨',
          specialization: 'Institutional content creation',
        },
        {
          name: 'Advisor',
          icon: '🎯',
          specialization: 'Institutional guidance and planning',
        },
        {
          name: 'General Tutor',
          icon: '🎓',
          specialization: 'System oversight and monitoring',
        },
        {
          name: 'Math Tutor',
          icon: '🔢',
          specialization: 'Math program oversight',
        },
      ],
    };

    const agents = roleAgentMap[role] || roleAgentMap['STUDENT'];
    return agents.map(agent => ({
      name: agent.name || '',
      type: agent.name?.toLowerCase().replace(' ', '_') || '',
      specialization: agent.specialization || '',
      status: 'active',
      icon: agent.icon || '🤖',
      keywords: [],
    }));
  },

  /**
   * Get sample messages for a specific role
   */
  getRoleSampleMessages: (role: string): string[] => {
    const roleSampleMap: Record<string, string[]> = {
      STUDENT: [
        'Help me understand quadratic equations',
        'Explain photosynthesis process',
        'How do I write a better essay?',
        'What career path should I choose?',
        'I need help with my homework',
      ],
      PROFESSOR: [
        'Create a calculus quiz for intermediate students',
        'Generate lesson content on cellular biology',
        'Help me design an assessment for algebra',
        'Create practice problems for physics',
        'Develop a writing assignment rubric',
      ],
      ADMIN: [
        'Analyze system-wide learning patterns',
        'Create institutional assessment strategy',
        'Generate administrative reports',
        'Plan curriculum improvements',
        'Monitor student progress trends',
      ],
    };

    return roleSampleMap[role] || roleSampleMap['STUDENT'];
  },

  /**
   * Predict which agent would handle a message based on role and content
   */
  predictAgent: (
    message: string,
    role: string
  ): { name: string; icon: string; reasoning: string } => {
    const lowerMessage = message.toLowerCase();
    const roleAgents = multiAgentService.getRoleAgents(role);

    // Check for tutoring keywords first
    const hasTutoringKeywords = [
      'help',
      'explain',
      'teach',
      'learn',
      'understand',
      'how to',
      'what is',
    ].some(keyword => lowerMessage.includes(keyword));

    if (hasTutoringKeywords) {
      if (
        lowerMessage.includes('math') ||
        lowerMessage.includes('equation') ||
        lowerMessage.includes('calculate')
      ) {
        return {
          name: 'Math Tutor',
          icon: '🔢',
          reasoning: 'Contains mathematical keywords',
        };
      }
      if (
        lowerMessage.includes('science') ||
        lowerMessage.includes('biology') ||
        lowerMessage.includes('chemistry')
      ) {
        return {
          name: 'Science Tutor',
          icon: '🔬',
          reasoning: 'Contains science keywords',
        };
      }
      if (
        lowerMessage.includes('write') ||
        lowerMessage.includes('essay') ||
        lowerMessage.includes('grammar')
      ) {
        return {
          name: 'Language Tutor',
          icon: '📝',
          reasoning: 'Contains writing/language keywords',
        };
      }
      return {
        name: 'General Tutor',
        icon: '🎓',
        reasoning: 'Contains tutoring keywords but no specific subject',
      };
    }

    // Role-specific routing
    if (role === 'PROFESSOR') {
      if (
        lowerMessage.includes('quiz') ||
        lowerMessage.includes('test') ||
        lowerMessage.includes('assessment')
      ) {
        return {
          name: 'Assessor',
          icon: '📊',
          reasoning: 'Professor requesting assessment creation',
        };
      }
      if (
        lowerMessage.includes('create') ||
        lowerMessage.includes('content') ||
        lowerMessage.includes('lesson')
      ) {
        return {
          name: 'Content Creator',
          icon: '✨',
          reasoning: 'Professor requesting content creation',
        };
      }
    }

    if (role === 'ADMIN') {
      if (
        lowerMessage.includes('analytics') ||
        lowerMessage.includes('report') ||
        lowerMessage.includes('data')
      ) {
        return {
          name: 'Assessor',
          icon: '📊',
          reasoning: 'Admin requesting analytics/reporting',
        };
      }
      if (
        lowerMessage.includes('institutional') ||
        lowerMessage.includes('policy') ||
        lowerMessage.includes('planning')
      ) {
        return {
          name: 'Advisor',
          icon: '🎯',
          reasoning: 'Admin requesting institutional guidance',
        };
      }
    }

    // Career/guidance keywords
    if (
      lowerMessage.includes('career') ||
      lowerMessage.includes('advice') ||
      lowerMessage.includes('guidance')
    ) {
      return {
        name: 'Career Advisor',
        icon: '🎯',
        reasoning: 'Contains career/guidance keywords',
      };
    }

    // Default to first agent for the role
    const defaultAgent = roleAgents[0];
    return {
      name: defaultAgent.name,
      icon: defaultAgent.icon,
      reasoning: `Default ${role.toLowerCase()} agent for general queries`,
    };
  },
};

export default multiAgentService;

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  TextField,
  InputAdornment,
  Paper,
  Chip,
  Button,
  Alert,
  CircularProgress,
  alpha,
  useTheme,
} from '@mui/material';
import { motion } from 'framer-motion';
import { FiSearch as SearchIcon } from 'react-icons/fi';
import {
  FaGraduationCap as SchoolIcon,
  FaUserPlus as EnrollIcon,
} from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';

import { SuperCourseCard } from '../../components/courses';
import { courseService } from '../../services/courseService';

interface Course {
  id: number;
  title: string;
  course_code: string;
  description: string;
  credits: number;
  level?: number;
  required_level?: number;
  has_ai_content?: boolean;
  has_interactive_content?: boolean;
  course_type?: string;
  department?: string;
  is_full?: boolean;
  enrolled_count?: number;
  capacity?: number;
}

const SimpleCourseRegistration: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState<number | null>(null);
  const [enrolling, setEnrolling] = useState<number | null>(null);

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setLoading(true);
        const response = await courseService.getAvailableCourses();

        let coursesData = [];
        if (response && typeof response === 'object') {
          if (response.status === 'success' && Array.isArray(response.data)) {
            coursesData = response.data;
          } else if (Array.isArray(response)) {
            coursesData = response;
          } else if (response.data && Array.isArray(response.data)) {
            coursesData = response.data;
          }
        }

        setCourses(coursesData);
        setError(null);
      } catch (err: any) {
        console.error('Error fetching courses:', err);
        setError(err.message || 'Failed to fetch courses');
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, []);

  const handleEnroll = async (courseId: number) => {
    try {
      setEnrolling(courseId);
      await courseService.enrollInCourse(courseId);

      // Navigate to student courses after successful enrollment
      navigate('/student/courses', {
        state: { message: 'Successfully enrolled in course!' },
      });
    } catch (err: any) {
      console.error('Error enrolling in course:', err);
      setError(err.message || 'Failed to enroll in course');
    } finally {
      setEnrolling(null);
    }
  };

  const filteredCourses = courses.filter(course => {
    const matchesSearch =
      course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.course_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.description?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesLevel =
      selectedLevel === null ||
      (course.required_level || course.level || 1) === selectedLevel;

    return matchesSearch && matchesLevel && !course.is_full;
  });

  const levels = [1, 2, 3, 4, 5];

  if (loading) {
    return (
      <Box
        display='flex'
        justifyContent='center'
        alignItems='center'
        minHeight='400px'
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        p: 3,
        background:
          theme.palette.mode === 'dark'
            ? 'linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,229,255,0.05) 100%)'
            : 'linear-gradient(180deg, rgba(255,255,255,0) 0%, rgba(0,119,182,0.03) 100%)',
        minHeight: '100vh',
      }}
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 3,
          }}
        >
          <Typography
            variant='h4'
            sx={{
              fontWeight: 700,
              background:
                theme.palette.mode === 'dark'
                  ? 'linear-gradient(90deg, #ffffff 0%, #00e5ff 100%)'
                  : 'linear-gradient(90deg, #000000 0%, #0077b6 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            Course Registration
          </Typography>
          <Button
            variant='outlined'
            onClick={() => navigate('/student/courses')}
            sx={{ borderRadius: 3 }}
          >
            My Courses
          </Button>
        </Box>

        {/* Course Stats */}
        <Paper
          elevation={0}
          sx={{
            p: 3,
            mb: 3,
            borderRadius: 4,
            background: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)',
            color: 'white',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <SchoolIcon size={24} style={{ marginRight: 12 }} />
              <Typography variant='h6' fontWeight='600'>
                Available Courses
              </Typography>
            </Box>
            <Typography variant='h4' fontWeight='bold'>
              {filteredCourses.length}
            </Typography>
          </Box>
        </Paper>

        {/* Search and Filters */}
        <Paper
          elevation={0}
          sx={{
            p: 3,
            mb: 3,
            borderRadius: 4,
            background: alpha(theme.palette.background.paper, 0.8),
            backdropFilter: 'blur(10px)',
            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          }}
        >
          <Grid container spacing={3} alignItems='center'>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder='Search courses...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position='start'>
                      <SearchIcon />
                    </InputAdornment>
                  ),
                  sx: { borderRadius: 2 },
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  label='All Levels'
                  onClick={() => setSelectedLevel(null)}
                  color={selectedLevel === null ? 'primary' : 'default'}
                  variant={selectedLevel === null ? 'filled' : 'outlined'}
                />
                {levels.map(level => (
                  <Chip
                    key={level}
                    label={`Level ${level}`}
                    onClick={() => setSelectedLevel(level)}
                    color={selectedLevel === level ? 'primary' : 'default'}
                    variant={selectedLevel === level ? 'filled' : 'outlined'}
                  />
                ))}
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {error && (
          <Alert severity='error' sx={{ mb: 3, borderRadius: 2 }}>
            {error}
          </Alert>
        )}

        {/* Course Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Grid container spacing={3}>
            {filteredCourses.map((course, index) => (
              <Grid item xs={12} sm={6} md={4} key={course.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.05 * (index % 6) }}
                >
                  <Box sx={{ position: 'relative' }}>
                    <SuperCourseCard
                      course={course}
                      onClick={() =>
                        navigate(`/student/course-preview/${course.id}`)
                      }
                      showProgress={false}
                      variant="compact"
                    />
                    <Box
                      sx={{
                        position: 'absolute',
                        bottom: 16,
                        left: 16,
                        right: 16,
                      }}
                    >
                      <Button
                        variant='contained'
                        fullWidth
                        startIcon={<EnrollIcon />}
                        onClick={e => {
                          e.stopPropagation();
                          handleEnroll(course.id);
                        }}
                        disabled={enrolling === course.id}
                        sx={{
                          borderRadius: 2,
                          background:
                            'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)',
                          '&:hover': {
                            background:
                              'linear-gradient(135deg, #45a049 0%, #3d8b40 100%)',
                          },
                          textTransform: 'none',
                          fontWeight: 600,
                        }}
                      >
                        {enrolling === course.id ? (
                          <CircularProgress size={20} color='inherit' />
                        ) : (
                          'Enroll Now'
                        )}
                      </Button>
                    </Box>
                  </Box>
                </motion.div>
              </Grid>
            ))}
          </Grid>

          {filteredCourses.length === 0 && !loading && (
            <Paper
              elevation={0}
              sx={{
                p: 4,
                textAlign: 'center',
                borderRadius: 4,
                background: alpha(theme.palette.background.paper, 0.8),
              }}
            >
              <Typography variant='h6' gutterBottom>
                No courses found
              </Typography>
              <Typography variant='body2' color='text.secondary'>
                Try adjusting your search criteria or level filters.
              </Typography>
            </Paper>
          )}
        </motion.div>
      </motion.div>
    </Box>
  );
};

export default SimpleCourseRegistration;

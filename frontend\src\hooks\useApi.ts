import { useState, useCallback } from 'react';
import axiosInstance from '@config/axios';
import { handleApiError } from '@utils/errorHandling';
import { useToast } from './useToast';

interface ApiOptions {
  showSuccessToast?: boolean;
  showErrorToast?: boolean;
  successMessage?: string;
  errorMessage?: string;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
}

interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: any;
  validationErrors: Record<string, string>;
}

/**
 * Custom hook for making API requests with built-in error handling
 */
export function useApi<T = any>(defaultOptions: ApiOptions = {}) {
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
    validationErrors: {},
  });

  const { showSuccess, showError } = useToast();

  /**
   * Make a GET request
   */
  const get = useCallback(
    async (url: string, options: ApiOptions = {}) => {
      const opts = { ...defaultOptions, ...options };
      setState(prev => ({
        ...prev,
        loading: true,
        error: null,
        validationErrors: {},
      }));

      try {
        const response = await axiosInstance.get(url);
        const data = response.data;

        setState(prev => ({ ...prev, data, loading: false }));

        if (opts.showSuccessToast && opts.successMessage) {
          showSuccess(opts.successMessage);
        }

        if (opts.onSuccess) {
          opts.onSuccess(data);
        }

        return data;
      } catch (error) {
        const errorResult = handleApiError(error);

        setState(prev => ({
          ...prev,
          loading: false,
          error: errorResult.message,
          validationErrors:
            errorResult.type === 'validation' ? errorResult.details || {} : {},
        }));

        if (opts.showErrorToast) {
          showError(opts.errorMessage || errorResult.message);
        }

        if (opts.onError) {
          opts.onError(error);
        }

        throw error;
      }
    },
    [defaultOptions, showSuccess, showError]
  );

  /**
   * Make a POST request
   */
  const post = useCallback(
    async (url: string, data: any, options: ApiOptions = {}) => {
      const opts = { ...defaultOptions, ...options };
      setState(prev => ({
        ...prev,
        loading: true,
        error: null,
        validationErrors: {},
      }));

      try {
        const response = await axiosInstance.post(url, data);
        const responseData = response.data;

        setState(prev => ({ ...prev, data: responseData, loading: false }));

        if (opts.showSuccessToast && opts.successMessage) {
          showSuccess(opts.successMessage);
        }

        if (opts.onSuccess) {
          opts.onSuccess(responseData);
        }

        return responseData;
      } catch (error) {
        const errorResult = handleApiError(error);

        setState(prev => ({
          ...prev,
          loading: false,
          error: errorResult.message,
          validationErrors:
            errorResult.type === 'validation' ? errorResult.details || {} : {},
        }));

        if (opts.showErrorToast) {
          showError(opts.errorMessage || errorResult.message);
        }

        if (opts.onError) {
          opts.onError(error);
        }

        throw error;
      }
    },
    [defaultOptions, showSuccess, showError]
  );

  /**
   * Make a PUT request
   */
  const put = useCallback(
    async (url: string, data: any, options: ApiOptions = {}) => {
      const opts = { ...defaultOptions, ...options };
      setState(prev => ({
        ...prev,
        loading: true,
        error: null,
        validationErrors: {},
      }));

      try {
        const response = await axiosInstance.put(url, data);
        const responseData = response.data;

        setState(prev => ({ ...prev, data: responseData, loading: false }));

        if (opts.showSuccessToast && opts.successMessage) {
          showSuccess(opts.successMessage);
        }

        if (opts.onSuccess) {
          opts.onSuccess(responseData);
        }

        return responseData;
      } catch (error) {
        const errorResult = handleApiError(error);

        setState(prev => ({
          ...prev,
          loading: false,
          error: errorResult.message,
          validationErrors:
            errorResult.type === 'validation' ? errorResult.details || {} : {},
        }));

        if (opts.showErrorToast) {
          showError(opts.errorMessage || errorResult.message);
        }

        if (opts.onError) {
          opts.onError(error);
        }

        throw error;
      }
    },
    [defaultOptions, showSuccess, showError]
  );

  /**
   * Make a DELETE request
   */
  const del = useCallback(
    async (url: string, options: ApiOptions = {}) => {
      const opts = { ...defaultOptions, ...options };
      setState(prev => ({
        ...prev,
        loading: true,
        error: null,
        validationErrors: {},
      }));

      try {
        const response = await axiosInstance.delete(url);
        const data = response.data;

        setState(prev => ({ ...prev, data, loading: false }));

        if (opts.showSuccessToast && opts.successMessage) {
          showSuccess(opts.successMessage);
        }

        if (opts.onSuccess) {
          opts.onSuccess(data);
        }

        return data;
      } catch (error) {
        const errorResult = handleApiError(error);

        setState(prev => ({
          ...prev,
          loading: false,
          error: errorResult.message,
          validationErrors:
            errorResult.type === 'validation' ? errorResult.details || {} : {},
        }));

        if (opts.showErrorToast) {
          showError(opts.errorMessage || errorResult.message);
        }

        if (opts.onError) {
          opts.onError(error);
        }

        throw error;
      }
    },
    [defaultOptions, showSuccess, showError]
  );

  /**
   * Reset the state
   */
  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      validationErrors: {},
    });
  }, []);

  return {
    ...state,
    get,
    post,
    put,
    delete: del,
    reset,
  };
}

export default useApi;

"""
Enhanced User Admin Interface

This module provides comprehensive admin interfaces for managing users with:
- Role-specific views and permissions
- Enhanced student management with enrollment tracking
- Professor management with course assignment tracking
- Admin oversight with security controls
- Improved search and filtering
"""

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth import get_user_model
from django.db.models import Count, Q, Avg
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from django.urls import reverse, path
from django.shortcuts import render
from django.http import HttpResponseRedirect
from django.contrib import messages
from django.contrib.admin import SimpleListFilter
from django.core.exceptions import ValidationError

from .models import CustomUser, Profile, UserActivity
from courses.models import Enrollment, Course

User = get_user_model()


class RoleFilter(SimpleListFilter):
    """Filter users by role with additional validation"""
    title = _('User Role')
    parameter_name = 'role_filter'

    def lookups(self, request, model_admin):
        return (
            ('students', _('Students Only')),
            ('professors', _('Professors Only')),
            ('admins', _('Administrators Only')),
            ('active_students', _('Active Students')),
            ('active_professors', _('Active Professors')),
        )

    def queryset(self, request, queryset):
        if self.value() == 'students':
            return queryset.filter(role='STUDENT')
        
        if self.value() == 'professors':
            return queryset.filter(role='PROFESSOR')
        
        if self.value() == 'admins':
            return queryset.filter(role='ADMIN')
        
        if self.value() == 'active_students':
            return queryset.filter(role='STUDENT', is_active=True)
        
        if self.value() == 'active_professors':
            return queryset.filter(role='PROFESSOR', is_active=True)


class EnrollmentStatusFilter(SimpleListFilter):
    """Filter students by enrollment status"""
    title = _('Enrollment Status')
    parameter_name = 'enrollment_status'

    def lookups(self, request, model_admin):
        return (
            ('enrolled', _('Currently Enrolled')),
            ('not_enrolled', _('No Active Enrollments')),
            ('completed_courses', _('Has Completed Courses')),
        )

    def queryset(self, request, queryset):
        # Only apply to students
        student_queryset = queryset.filter(role='STUDENT')
        
        if self.value() == 'enrolled':
            return student_queryset.filter(
                enrollments__status__in=['PENDING', 'APPROVED']
            ).distinct()
        
        if self.value() == 'not_enrolled':
            return student_queryset.exclude(
                enrollments__status__in=['PENDING', 'APPROVED']
            ).distinct()
        
        if self.value() == 'completed_courses':
            return student_queryset.filter(
                enrollments__status='COMPLETED'
            ).distinct()


class ProfileInline(admin.StackedInline):
    """Inline for user profile management"""
    model = Profile
    extra = 0
    fields = [
        'bio', 'academic_status', 'graduation_year',
        'major', 'minor', 'research_interests',
        'office_location', 'office_hours'
    ]


class UserActivityInline(admin.TabularInline):
    """Inline for viewing recent user activities"""
    model = UserActivity
    extra = 0
    readonly_fields = ['type', 'message', 'timestamp', 'course', 'details']
    fields = ['type', 'message', 'course', 'timestamp']
    ordering = ['-timestamp']
    
    def has_add_permission(self, request, obj=None):
        return False  # Activities are created automatically


@admin.register(CustomUser)
class EnhancedCustomUserAdmin(UserAdmin):
    """
    Enhanced User Admin with role-specific management and improved features
    """
    
    # Display configuration
    list_display = [
        'username_link', 'full_name_display', 'email', 'role_badge',
        'status_indicator', 'activity_summary', 'enrollment_info',
        'last_activity_display', 'date_joined_short'
    ]
    
    list_display_links = ['username_link']
    
    list_filter = [
        RoleFilter, EnrollmentStatusFilter, 'is_active', 'is_staff',
        'date_joined', 'last_login', 'department'
    ]
    
    search_fields = [
        'username', 'email', 'first_name', 'last_name',
        'student_id', 'department', 'phone_number'
    ]
    
    readonly_fields = [
        'last_login', 'date_joined', 'last_activity',
        'user_statistics', 'enrollment_summary', 'course_teaching_summary'
    ]
    
    inlines = [ProfileInline, UserActivityInline]
    
    # Enhanced fieldsets based on role
    fieldsets = (
        (None, {
            'fields': ('username', 'password')
        }),
        
        (_('Personal Information'), {
            'fields': (
                'first_name', 'last_name', 'email', 'date_of_birth',
                'phone_number', 'address', 'profile_picture'
            )
        }),
        
        (_('Role & Academic Information'), {
            'fields': ('role', 'department', 'student_id'),
            'classes': ('wide',)
        }),
        
        (_('Permissions'), {
            'fields': (
                'is_active', 'is_staff', 'is_superuser',
                'groups', 'user_permissions'
            ),
            'classes': ('collapse',)
        }),
        
        (_('Activity & Statistics'), {
            'fields': ('user_statistics', 'enrollment_summary', 'course_teaching_summary'),
            'classes': ('collapse',)
        }),
        
        (_('Important Dates'), {
            'fields': ('last_login', 'date_joined', 'last_activity'),
            'classes': ('collapse',)
        }),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'email', 'password1', 'password2', 'role'),
        }),
    )
    
    # Actions
    actions = [
        'activate_users', 'deactivate_users', 'promote_to_professor',
        'generate_user_report', 'reset_user_passwords'
    ]
    
    # Custom display methods
    def username_link(self, obj):
        """Display username with link to user detail"""
        url = reverse('admin:users_customuser_change', args=[obj.pk])
        return format_html('<a href="{}" title="Edit user"><strong>{}</strong></a>', url, obj.username)
    username_link.short_description = _('Username')
    username_link.admin_order_field = 'username'
    
    def full_name_display(self, obj):
        """Display user's full name or username if not available"""
        full_name = obj.get_full_name()
        if full_name:
            return format_html('<span title="{}">{}</span>', obj.username, full_name)
        return format_html('<em>{}</em>', obj.username)
    full_name_display.short_description = _('Full Name')
    full_name_display.admin_order_field = 'last_name'
    
    def role_badge(self, obj):
        """Display role with appropriate styling and icon"""
        role_styles = {
            'STUDENT': ('👨‍🎓', 'blue', 'Student'),
            'PROFESSOR': ('👨‍🏫', 'green', 'Professor'),
            'ADMIN': ('🛡️', 'red', 'Admin')
        }
        
        icon, color, display = role_styles.get(obj.role, ('❓', 'gray', obj.role))
        
        return format_html(
            '<span style="color: {}; font-weight: bold;" title="{}">{} {}</span>',
            color, display, icon, display
        )
    role_badge.short_description = _('Role')
    role_badge.admin_order_field = 'role'
    
    def status_indicator(self, obj):
        """Display user status with visual indicators"""
        status_parts = []
        
        if obj.is_active:
            status_parts.append('<span style="color: green;">✅ Active</span>')
        else:
            status_parts.append('<span style="color: red;">❌ Inactive</span>')
        
        if obj.is_staff:
            status_parts.append('<span style="color: blue;" title="Staff">👮</span>')
        
        if obj.is_superuser:
            status_parts.append('<span style="color: purple;" title="Superuser">👑</span>')
        
        return format_html(' '.join(status_parts))
    status_indicator.short_description = _('Status')
    status_indicator.admin_order_field = 'is_active'
    
    def activity_summary(self, obj):
        """Display recent activity summary"""
        if obj.role == 'STUDENT':
            active_enrollments = obj.enrollments.filter(status='APPROVED').count()
            completed_courses = obj.enrollments.filter(status='COMPLETED').count()
            
            return format_html(
                '<div title="Active: {}, Completed: {}">📚 {} | ✅ {}</div>',
                active_enrollments, completed_courses, active_enrollments, completed_courses
            )
        
        elif obj.role == 'PROFESSOR':
            teaching_courses = obj.taught_courses.filter(is_active=True).count()
            return format_html(
                '<div title="Teaching {} courses">🏫 {}</div>',
                teaching_courses, teaching_courses
            )
        
        return format_html('<span style="color: #ccc;">-</span>')
    activity_summary.short_description = _('Activity')
    
    def enrollment_info(self, obj):
        """Display enrollment information for students"""
        if obj.role != 'STUDENT':
            return format_html('<span style="color: #ccc;">-</span>')
        
        try:
            pending = obj.enrollments.filter(status='PENDING').count()
            approved = obj.enrollments.filter(status='APPROVED').count()
            completed = obj.enrollments.filter(status='COMPLETED').count()
            
            info_parts = []
            if pending > 0:
                info_parts.append(f'🟡{pending}')
            if approved > 0:
                info_parts.append(f'🟢{approved}')
            if completed > 0:
                info_parts.append(f'✅{completed}')
            
            return format_html(
                '<span title="Pending: {}, Active: {}, Completed: {}">{}</span>',
                pending, approved, completed, ' '.join(info_parts) or '📄0'
            )
        except:
            return format_html('<span style="color: #ccc;">-</span>')
    enrollment_info.short_description = _('Enrollments')
    
    def last_activity_display(self, obj):
        """Display last activity time"""
        if obj.last_activity:
            return format_html(
                '<span title="{}">{}</span>',
                obj.last_activity.strftime('%Y-%m-%d %H:%M'),
                obj.last_activity.strftime('%m/%d')
            )
        elif obj.last_login:
            return format_html(
                '<span style="color: orange;" title="Last login: {}">{}</span>',
                obj.last_login.strftime('%Y-%m-%d %H:%M'),
                obj.last_login.strftime('%m/%d')
            )
        return format_html('<span style="color: #ccc;">Never</span>')
    last_activity_display.short_description = _('Last Active')
    last_activity_display.admin_order_field = 'last_activity'
    
    def date_joined_short(self, obj):
        """Display join date in short format"""
        return format_html(
            '<span title="{}">{}</span>',
            obj.date_joined.strftime('%Y-%m-%d %H:%M'),
            obj.date_joined.strftime('%Y-%m-%d')
        )
    date_joined_short.short_description = _('Joined')
    date_joined_short.admin_order_field = 'date_joined'
    
    # Detailed information methods for readonly fields
    def user_statistics(self, obj):
        """Display comprehensive user statistics"""
        if not obj.pk:
            return "Save the user first to see statistics"
        
        stats = []
        
        if obj.role == 'STUDENT':
            enrollments = obj.enrollments.all()
            total_enrollments = enrollments.count()
            active_enrollments = enrollments.filter(status='APPROVED').count()
            completed_courses = enrollments.filter(status='COMPLETED').count()
            
            # Calculate GPA if grades exist
            try:
                from grades.models import CourseGrade
                grades = CourseGrade.objects.filter(user=obj)
                avg_grade = grades.aggregate(avg=Avg('numeric_grade'))['avg']
                gpa_display = f"{avg_grade:.2f}" if avg_grade else "N/A"
            except:
                gpa_display = "N/A"
            
            stats.extend([
                f"Total Enrollments: {total_enrollments}",
                f"Active Enrollments: {active_enrollments}",
                f"Completed Courses: {completed_courses}",
                f"Average Grade: {gpa_display}"
            ])
        
        elif obj.role == 'PROFESSOR':
            teaching_courses = obj.taught_courses.filter(is_active=True).count()
            total_students = Enrollment.objects.filter(
                course__instructor=obj, status='APPROVED'
            ).count()
            
            stats.extend([
                f"Active Courses Teaching: {teaching_courses}",
                f"Total Students: {total_students}"
            ])
        
        return format_html(
            '<div style="background: #f0f0f0; padding: 10px; border-radius: 5px;">'
            '<strong>User Statistics:</strong><br>'
            '{}'
            '</div>',
            '<br>'.join(stats) if stats else 'No statistics available'
        )
    user_statistics.short_description = _('User Statistics')
    
    def enrollment_summary(self, obj):
        """Display detailed enrollment summary for students"""
        if obj.role != 'STUDENT' or not obj.pk:
            return "Not applicable or user not saved"
        
        try:
            enrollments = obj.enrollments.select_related('course').order_by('-enrollment_date')[:5]
            
            if not enrollments.exists():
                return "No enrollments found"
            
            enrollment_list = []
            for enrollment in enrollments:
                status_icon = {
                    'PENDING': '🟡',
                    'APPROVED': '🟢',
                    'COMPLETED': '✅',
                    'REJECTED': '🔴',
                    'DROPPED': '❌'
                }.get(enrollment.status, '❓')
                
                enrollment_list.append(
                    f"{status_icon} {enrollment.course.course_code} - {enrollment.get_status_display()}"
                )
            
            return format_html(
                '<div style="background: #f0f0f0; padding: 10px; border-radius: 5px;">'
                '<strong>Recent Enrollments:</strong><br>'
                '{}'
                '</div>',
                '<br>'.join(enrollment_list)
            )
        except Exception as e:
            return f"Error loading enrollments: {str(e)}"
    enrollment_summary.short_description = _('Enrollment Summary')
    
    def course_teaching_summary(self, obj):
        """Display teaching summary for professors"""
        if obj.role != 'PROFESSOR' or not obj.pk:
            return "Not applicable or user not saved"
        
        try:
            courses = obj.taught_courses.filter(is_active=True).order_by('course_code')[:5]
            
            if not courses.exists():
                return "No courses currently teaching"
            
            course_list = []
            for course in courses:
                enrolled_count = course.enrollments.filter(status='APPROVED').count()
                course_list.append(
                    f"📚 {course.course_code} - {course.title} ({enrolled_count} students)"
                )
            
            return format_html(
                '<div style="background: #f0f0f0; padding: 10px; border-radius: 5px;">'
                '<strong>Current Courses:</strong><br>'
                '{}'
                '</div>',
                '<br>'.join(course_list)
            )
        except Exception as e:
            return f"Error loading courses: {str(e)}"
    course_teaching_summary.short_description = _('Teaching Summary')
    
    # Admin actions
    def activate_users(self, request, queryset):
        """Bulk activate selected users"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} users activated successfully.')
    activate_users.short_description = _('Activate selected users')
    
    def deactivate_users(self, request, queryset):
        """Bulk deactivate selected users"""
        # Prevent deactivating superusers
        superuser_count = queryset.filter(is_superuser=True).count()
        if superuser_count > 0:
            self.message_user(
                request, 
                f'Cannot deactivate {superuser_count} superusers. Operation cancelled.',
                level=messages.ERROR
            )
            return
        
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} users deactivated successfully.')
    deactivate_users.short_description = _('Deactivate selected users')
    
    def promote_to_professor(self, request, queryset):
        """Promote selected students to professor role"""
        student_queryset = queryset.filter(role='STUDENT')
        updated = student_queryset.update(role='PROFESSOR')
        
        if updated > 0:
            self.message_user(request, f'{updated} students promoted to professor role.')
        else:
            self.message_user(request, 'No students selected for promotion.', level=messages.WARNING)
    promote_to_professor.short_description = _('Promote students to professor')
    
    # Query optimization
    def get_queryset(self, request):
        """Optimize queryset with select_related and prefetch_related"""
        queryset = super().get_queryset(request)
        return queryset.select_related('profile').prefetch_related(
            'enrollments__course',
            'taught_courses',
            'activities'
        )
    
    def save_model(self, request, obj, form, change):
        """Override save to add validation and logging"""
        # Validate role-specific fields
        if obj.role == 'STUDENT' and not obj.student_id:
            # Auto-generate student ID if not provided
            last_student = CustomUser.objects.filter(
                role='STUDENT', student_id__isnull=False
            ).order_by('student_id').last()
            
            if last_student and last_student.student_id:
                try:
                    last_id = int(last_student.student_id)
                    obj.student_id = str(last_id + 1).zfill(8)
                except:
                    obj.student_id = "20240001"  # Default starting ID
            else:
                obj.student_id = "20240001"
        
        super().save_model(request, obj, form, change)
        
        # Create profile if it doesn't exist
        if not hasattr(obj, 'profile'):
            Profile.objects.create(user=obj)
    
    # Custom admin views
    def get_urls(self):
        """Add custom URLs for admin views"""
        urls = super().get_urls()
        custom_urls = [
            path(
                '<int:user_id>/detailed-report/',
                self.admin_site.admin_view(self.detailed_report_view),
                name='user_detailed_report'
            ),
        ]
        return custom_urls + urls
    
    def detailed_report_view(self, request, user_id):
        """Custom view for detailed user report"""
        user = self.get_object(request, user_id)
        if user is None:
            return HttpResponseRedirect(reverse('admin:users_customuser_changelist'))
        
        context = {
            'user': user,
            'enrollments': user.enrollments.select_related('course').all() if user.role == 'STUDENT' else None,
            'taught_courses': user.taught_courses.all() if user.role == 'PROFESSOR' else None,
            'recent_activities': user.activities.order_by('-timestamp')[:20],
            'title': f'Detailed Report - {user.username}',
        }
        return render(request, 'admin/users/detailed_report.html', context)


@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    """Admin for user profiles"""
    
    list_display = [
        'user_display', 'role_display', 'academic_status',
        'graduation_year', 'major', 'updated_at'
    ]
    
    list_filter = [
        'user__role', 'academic_status', 'graduation_year',
        'created_at', 'updated_at'
    ]
    
    search_fields = [
        'user__username', 'user__first_name', 'user__last_name',
        'bio', 'major', 'minor', 'research_interests'
    ]
    
    readonly_fields = ['created_at', 'updated_at']
    
    def user_display(self, obj):
        """Display user with link"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:users_customuser_change', args=[obj.user.pk]),
            obj.user.get_full_name() or obj.user.username
        )
    user_display.short_description = _('User')
    user_display.admin_order_field = 'user__username'
    
    def role_display(self, obj):
        """Display user role"""
        role_icons = {
            'STUDENT': '👨‍🎓',
            'PROFESSOR': '👨‍🏫',
            'ADMIN': '🛡️'
        }
        icon = role_icons.get(obj.user.role, '❓')
        return format_html('{} {}', icon, obj.user.get_role_display())
    role_display.short_description = _('Role')
    role_display.admin_order_field = 'user__role'


@admin.register(UserActivity)
class UserActivityAdmin(admin.ModelAdmin):
    """Admin for user activities"""
    
    list_display = [
        'user_display', 'type_display', 'message_truncated',
        'course_display', 'timestamp'
    ]
    
    list_filter = [
        'type', 'timestamp', 'user__role', 'course__department'
    ]
    
    search_fields = [
        'user__username', 'message', 'course__course_code'
    ]
    
    readonly_fields = ['user', 'type', 'message', 'timestamp', 'course', 'details']
    
    date_hierarchy = 'timestamp'
    
    def has_add_permission(self, request):
        """Prevent manual creation of activities"""
        return False
    
    def user_display(self, obj):
        """Display user with role"""
        role_icons = {
            'STUDENT': '👨‍🎓',
            'PROFESSOR': '👨‍🏫',
            'ADMIN': '🛡️'
        }
        icon = role_icons.get(obj.user.role, '❓')
        return format_html(
            '<a href="{}" title="{}">{} {}</a>',
            reverse('admin:users_customuser_change', args=[obj.user.pk]),
            obj.user.get_role_display(),
            icon,
            obj.user.get_full_name() or obj.user.username
        )
    user_display.short_description = _('User')
    user_display.admin_order_field = 'user__username'
    
    def type_display(self, obj):
        """Display activity type with icon"""
        type_icons = {
            'material_view': '👀',
            'assignment_submission': '📝',
            'assessment_completion': '✅',
            'forum_post': '💬',
            'grade_received': '🎓',
            'course_enrolled': '📚',
            'quiz_completed': '❓',
        }
        icon = type_icons.get(obj.type, '📋')
        return format_html('{} {}', icon, obj.get_type_display())
    type_display.short_description = _('Activity Type')
    type_display.admin_order_field = 'type'
    
    def message_truncated(self, obj):
        """Display truncated message"""
        if len(obj.message) > 50:
            return format_html(
                '<span title="{}">{}</span>',
                obj.message,
                obj.message[:50] + '...'
            )
        return obj.message
    message_truncated.short_description = _('Message')
    
    def course_display(self, obj):
        """Display course if associated"""
        if obj.course:
            return format_html(
                '<a href="{}" title="{}">{}</a>',
                reverse('admin:courses_course_change', args=[obj.course.pk]),
                obj.course.title,
                obj.course.course_code
            )
        return format_html('<span style="color: #ccc;">-</span>')
    course_display.short_description = _('Course')
    course_display.admin_order_field = 'course__course_code'
    
    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related('user', 'course')


# Unregister the default CustomUser admin if it exists
try:
    admin.site.unregister(CustomUser)
except admin.sites.NotRegistered:
    pass

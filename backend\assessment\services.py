"""Assessmentservicesmodule."""import loggingimport randomfromtypingimportTYPE_CHECKINGAnyDictListfrom django.appsimportappsfrom django.confimportsettingsfrom django.dbimporttransactionfrom django.db.modelsimportAvgCountModelfrom django.utilsimport timezone#ImportstandardizedAIserviceutilitiesanderrorsfrom utils.ai_service_utilsimport(StandardizedAIServicecreate_fallback_responseget_ai_loggerget_error_messagestandardized_json_parser)from utils.consolidated_ai_serviceimportConsolidatedAIErrorget_ai_service#CustomexceptionsthatinheritfromstandardizedAIerrorsclassAssessmentError(ConsolidatedAIError):"""Baseexceptionforassessment-relatederrors"""passclassAssessmentNotFoundError(AssessmentError):"""Raisedwhenanassessmentisnotfound"""passclassQuestionNotFoundError(AssessmentError):"""Raisedwhenaquestionisnotfound"""passifTYPE_CHECKING:from courses.modelsimportCoursefrom.modelsimport(AIAssessmentControllerAssessmentAssessmentQuestionAssessmentResponseStudentLevel)#Usestandardizedloggerlogger=get_ai_logger("assessment_service")classUnifiedAssessmentService:"""Unifiedservicehandlingalltypesofassessments"""LEVELS={"BEGINNER":(025)"INTERMEDIATE":(2650)"ADVANCED":(5175)"EXPERT":(76100)}def__init__(selfuser=None):self.user=userself.ai_service=get_ai_service()self.cached_settings=Nonedef_get_models(self):"""Getmodelclassestouse"""AssessmentQuestion=apps.get_model("assessment""AssessmentQuestion")Assessment=apps.get_model("assessment""Assessment")AssessmentResponse=apps.get_model("assessment""AssessmentResponse")AIAssessmentController=apps.get_model("assessment""AIAssessmentController")StudentLevel=apps.get_model("assessment""StudentLevel")return(AssessmentQuestionAssessmentAssessmentResponseAIAssessmentControllerStudentLevel)defget_initial_assessment(selfassessment_type="GENERAL")->List[Model]:"""Getquestionsforinitialassessmentbasedontype"""AssessmentQuestion____=self._get_models()questions=[]categories=["GENERAL"]forcategoryincategories:category_questions=AssessmentQuestion.objects.filter(category=categorydifficulty_level=1assessment_type=assessment_type).order_by("?")[:3]questions.extend(category_questions)returnquestions[:10]defcreate_assessment(selfassessment_typecourse=None):"""Createsanewassessmentofspecifiedtype"""_Assessment__StudentLevel=self._get_models()#Getorcreatestudentleveltodetermineinitiallevelstudent_level_=StudentLevel.objects.get_or_create(student=self.userdefaults={"current_level":1"current_level_display":"Beginner"})returnAssessment.objects.create(student=self.usercourse=courseassessment_type=assessment_typestatus="IN_PROGRESS"start_time=timezone.now()initial_level=student_level.current_level#Setinitiallevelfromstudentlevel)defget_student_progress(self):"""Getstudent'sassessmentprogress"""_Assessment___=self._get_models()returnAssessment.objects.filter(student=self.user).order_by("-start_time")defsubmit_assessment(selfassessment_idresponses):"""Submitanassessmentwithresponses"""_Assessment___=self._get_models()assessment=Assessment.objects.get(id=assessment_id)#Addvalidationhereassessment.submit_responses(responses)returnassessmentdefget_recommendations(selfstudent):"""Getpersonalizedrecommendationsbasedonassessmenthistory"""_Assessment___=self._get_models()#RegularassessmentrecommendationslogicreturnAssessment.objects.filter(student=studentstatus="COMPLETED").order_by("-end_time")defanalyze_assessment(selfassessment)->Dict[strAny]:"""Comprehensiveanalysisofassessmentresults"""strengths_weaknesses=self.analyze_strengths_weaknesses(assessment)level=self.calculate_level(assessment.score)#GetAI-poweredinsightsai_analysis=self._get_ai_analysis(assessment)return{"score":assessment.score"level":level"strengths":strengths_weaknesses["strengths"]"weaknesses":strengths_weaknesses["weaknesses"]"detailed_analysis":strengths_weaknesses["detailed_analysis"]"ai_insights":ai_analysis"recommendations":self.get_course_recommendations(assessment)}defanalyze_strengths_weaknesses(selfassessment)->Dict[strList[str]]:"""Analyzestudent'sstrengthsandweaknesses"""__AssessmentResponse__=self._get_models()responses=AssessmentResponse.objects.filter(assessment=assessment)analysis={}categories=["GENERAL"]forcategoryincategories:category_responses=responses.filter(question__category=category)ifcategory_responses.exists():correct_count=category_responses.filter(is_correct=True).count()total_count=category_responses.count()score=(correct_count/total_count)*100iftotal_count>0else0analysis[category]={"score":score"correct":correct_count"total":total_count}ai_analysis=self._get_ai_analysis(assessment)strengths=[catforcatstatsinanalysis.items()ifstats["score"]>=70]weaknesses=[catforcatstatsinanalysis.items()ifstats["score"]<=40]ifai_analysis:strengths.extend(ai_analysis.get("strengths"[]))weaknesses.extend(ai_analysis.get("areas_for_improvement"[]))return{"strengths":list(set(strengths))"weaknesses":list(set(weaknesses))"detailed_analysis":analysis"ai_insights":ai_analysis}defget_course_recommendations(selfassessment)->List[Dict[strAny]]:"""Getpersonalizedcourserecommendations"""analysis=self.analyze_strengths_weaknesses(assessment)current_level=self.calculate_level(assessment.score)#GetAIrecommendationsai_recommendations=self._get_ai_recommendations(assessmentanalysis)#Getcourserecommendationsfromdatabaserecommended_courses=self._get_db_course_recommendations(current_level=current_levelassessment_type=assessment.assessment_typestrengths=analysis["strengths"]weaknesses=analysis["weaknesses"])returnself._combine_and_sort_recommendations(ai_recommendations=ai_recommendationsdb_recommendations=recommended_courses)defcalculate_level(selfscore:int)->str:"""Calculatelevelbasedonscore"""forlevel(min_scoremax_score)inself.LEVELS.items():ifmin_score<=score<=max_score:returnlevelreturn"BEGINNER"def_get_next_level(selfcurrent_level:str)->str:"""Getthenextlevelupfromcurrentlevel"""levels=["BEGINNER""INTERMEDIATE""ADVANCED""EXPERT"]try:current_index=levels.index(current_level)ifcurrent_index<len(levels)-1:returnlevels[current_index+1]exceptValueError:passreturncurrent_leveldef_generate_ai_questions(selfcategory:strdifficulty_level:strcount:intassessment_type:str="PLACEMENT")->List[Dict]:"""GeneratequestionsusingAI"""try:prompt=self._build_question_generation_prompt(category=categorydifficulty=difficulty_levelcount=countassessment_type=assessment_type)returnself.get_ai_service().generate_questions(prompt)exceptExceptionase:logger.error(f"ErrorinAIquestiongeneration:{str(e)}")return[]def_create_questions_from_ai(selfai_questions:List[Dict]assessment_type:str)->List["AssessmentQuestion"]:"""CreateAssessmentQuestionobjectsfromAI-generatedquestions"""AssessmentQuestion____=self._get_models()questions=[]forq_datainai_questions:question=AssessmentQuestion.objects.create(question_text=q_data["text"]question_type="MULTIPLE_CHOICE"category=q_data.get("category""GENERAL")options=q_data["options"]correct_answer=q_data["correct"]difficulty_level=1assessment_type=assessment_type)questions.append(question)returnquestionsdef_get_ai_analysis(selfassessment)->Dict:"""GetAIanalysisofassessmentperformance"""try:responses=assessment.responses.all().select_related("question")prompt=self._build_analysis_prompt(assessmentresponses)returnself.get_ai_service().analyze_responses(prompt)exceptException:return{}def_get_ai_recommendations(selfassessmentanalysis:Dict)->List[Dict]:"""GetAI-poweredcourserecommendations"""try:returnself.get_ai_service().suggest_course_recommendations(assessment_results={"score":assessment.score"level":self.calculate_level(assessment.score)"strengths":analysis["strengths"]"weaknesses":analysis["weaknesses"]}student_level=self.calculate_level(assessment.score)assessment_type=assessment.assessment_type)exceptException:return[]def_get_db_course_recommendations(selfcurrent_level:strassessment_type:strstrengths:List[str]weaknesses:List[str])->List[Model]:"""Getcourserecommendationsfromdatabase"""Course=apps.get_model("courses""Course")recommended_courses=[]#Addfoundationalcoursesforweakareasforweaknessinweaknesses:foundational_courses=Course.objects.filter(category=weaknesslevel__in=["BEGINNER"current_level]is_active=Truecourse_type=assessment_type)[:2]recommended_courses.extend(foundational_courses)#Addadvancedcoursesforstrongareasforstrengthinstrengths:advanced_courses=Course.objects.filter(category=strengthlevel__in=[current_levelself._get_next_level(current_level)]is_active=Truecourse_type=assessment_type)[:2]recommended_courses.extend(advanced_courses)returnrecommended_coursesdef_combine_and_sort_recommendations(selfai_recommendations:List[Dict]db_recommendations:List[Model])->List[Dict[strAny]]:"""Combineandsortallcourserecommendations"""final_recommendations=[]seen_courses=set()#AddAI-recommendedcoursesifai_recommendationsand"courses"inai_recommendations:forcourse_datainai_recommendations["courses"]:ifcourse_data["title"]notinseen_courses:final_recommendations.append({"title":course_data["title"]"description":course_data["description"]"level":course_data["level"]"relevance_score":course_data.get("relevance_score"0.5)"ai_recommended":True})seen_courses.add(course_data["title"])#AdddatabasecoursesCourse=apps.get_model("courses""Course")forcourseindb_recommendations:ifcourse.titlenotinseen_courses:final_recommendations.append({"title":course.title"description":course.description"level":course.level"relevance_score":0.8"ai_recommended":False})seen_courses.add(course.title)#Sortbyrelevancescorefinal_recommendations.sort(key=lambdax:x["relevance_score"]reverse=True)returnfinal_recommendations[:5]#Returntop5recommendations#Initializetheservicelazily_assessment_ai=Nonedefget_assessment_ai():global_assessment_aiif_assessment_aiisNone:_assessment_ai=UnifiedAssessmentService(None)return_assessment_aiassessment_ai=get_assessment_ai()classAssessmentAdminService:@staticmethoddefget_question_statistics():"""Getstatisticsaboutquestionperformance."""AssessmentQuestion=apps.get_model("assessment""AssessmentQuestion")returnAssessmentQuestion.objects.annotate(times_used=Count("responses")correct_rate=Avg("responses__is_correct"))#Methodremoved:review_ai_suggestionswasemptyandunused@staticmethoddefoverride_student_level(assessmentnew_leveladmin_userreason):"""Overrideautomaticallyassignedstudentlevel."""assessment.override_level=new_levelassessment.level_override_by=admin_userassessment.level_override_reason=reasonassessment.save()returnassessmentimport loggingfrom django.appsimportappsfrom django.confimportsettingsfrom django.core.cacheimportcachefrom django.dbimporttransactionfrom django.utilsimport timezone#UsestandardizedloggerforassessmentAIservicelogger=get_ai_logger("assessment_ai_service")classAssessmentService:"""Serviceclassforhandlingassessmentbusinesslogic"""defanalyze_strengths_weaknesses(selfassessment):"""Analyzestudent'sstrengthsandweaknessesbasedonassessmentresponses"""try:#ImportmodelsAssessmentResponse=apps.get_model("assessment""AssessmentResponse")#Getallresponsesforthisassessmentresponses=AssessmentResponse.objects.filter(assessment=assessment)#Initializeanalysisdictionaryanalysis={"strengths":[]"weaknesses":[]"detailed_analysis":{}}#Groupresponsesbycategorycategories={}forresponseinresponses:ifnotresponse.question:continuecategory=response.question.categoryifcategorynotincategories:categories[category]={"correct":0"total":0}categories[category]["total"]+=1ifresponse.is_correct:categories[category]["correct"]+=1#Calculatescoresanddeterminestrengths/weaknessesforcategorystatsincategories.items():ifstats["total"]==0:continuescore=(stats["correct"]/stats["total"])*100#Addtodetailedanalysisanalysis["detailed_analysis"][category]={"score":score"correct":stats["correct"]"total":stats["total"]}#Determineifthisisastrengthorweaknessifscore>=70:analysis["strengths"].append(category)elifscore<=40:analysis["weaknesses"].append(category)returnanalysisexceptExceptionase:logger.error(f"Erroranalyzingstrengthsandweaknesses:{str(e)}")#Returnemptyanalysisifthere'sanerrorreturn{"strengths":[]"weaknesses":[]"detailed_analysis":{}}defget_course_recommendations(selfassessment):"""Getcourserecommendationsbasedonassessmentresults"""try:#Getstrengthsandweaknessesanalysis=self.analyze_strengths_weaknesses(assessment)#ImportCoursemodelCourse=apps.get_model("courses""Course")#GetstudentlevelStudentLevel=apps.get_model("assessment""StudentLevel")student_level=StudentLevel.objects.filter(student_id=assessment.student_id).first()current_level=student_level.current_levelifstudent_levelelse1#Getcoursesbasedonweaknesses(focusonimprovementareas)recommended_courses=[]#CheckiftheCoursemodelhastheexpectedfieldscourse_fields=[field.nameforfieldinCourse._meta.get_fields()]#Usethecorrectfieldnamefordifficultyleveldifficulty_field=("difficulty_level"if"difficulty_level"incourse_fieldselse"level")#Addcoursesforweaknessesforweaknessinanalysis["weaknesses"]:try:#Createafilterdictionarydynamicallyfilter_kwargs={"is_active":True}#Addcategoryfilterifitexistsif"category"incourse_fields:filter_kwargs["category__icontains"]=weakness#Adddifficultylevelfilterifitexistsifdifficulty_fieldincourse_fields:filter_kwargs[f"{difficulty_field}__lte"]=current_level+1#Getcoursesmatchingthefiltersweakness_courses=Course.objects.filter(**filter_kwargs)[:2]recommended_courses.extend(weakness_courses)exceptExceptionasfilter_error:logger.error(f"Errorfilteringcoursesforweakness{weakness}:{str(filter_error)}")#Addcoursesforcurrentlevelifwedon'thaveenoughiflen(recommended_courses)<3:try:#Createafilterdictionarydynamicallyfilter_kwargs={"is_active":True}#Adddifficultylevelfilterifitexistsifdifficulty_fieldincourse_fields:filter_kwargs[difficulty_field]=current_level#Getcoursesmatchingthefilterslevel_courses=Course.objects.filter(**filter_kwargs)#Excludealreadyrecommendedcoursesifrecommended_courses:level_courses=level_courses.exclude(id__in=[c.idforcinrecommended_courses])#Addupto3morecoursesrecommended_courses.extend(level_courses[:3])exceptExceptionaslevel_error:logger.error(f"Errorgettinglevelcourses:{str(level_error)}")#SerializethecoursestomakethemJSONserializableserialized_courses=[]forcourseinrecommended_courses[:5]:try:#Createadictionarywithbasiccourseinformationcourse_data={"id":course.id"title":(course.titleifhasattr(course"title")elsef"Course{course.id}")"course_code":(course.course_codeifhasattr(course"course_code")else"")"description":(course.descriptionifhasattr(course"description")else"")"category":(course.categoryifhasattr(course"category")else"")"difficulty_level":getattr(coursedifficulty_field1)"url":(f"/courses/{course.id}/"ifhasattr(course"id")else"#")}serialized_courses.append(course_data)exceptExceptionasserialize_error:logger.error(f"Errorserializingcourse{course.id}:{str(serialize_error)}")#Returnserializedcoursesreturnserialized_coursesexceptExceptionase:logger.error(f"Errorgettingcourserecommendations:{str(e)}")return[]defcreate_assessment(selfstudentassessment_typenum_questions=Nonelearning_path=None):"""Createanewassessment"""try:AssessmentSettings=apps.get_model("assessment""AssessmentSettings")settings=AssessmentSettings.objects.get(assessment_type=assessment_type)ifnum_questionsisNone:num_questions=settings.questions_per_assessment#Getquestionsbasedonsettingsandstudentlevelquestions=self._select_questions(assessment_typenum_questionsstudentlearning_path)ifnotquestions:raiseAssessmentError("Nosuitablequestionsavailable")#GetorcreatestudentleveltodetermineinitiallevelStudentLevel=apps.get_model("assessment""StudentLevel")student_level_=StudentLevel.objects.get_or_create(student=studentdefaults={"current_level":1"current_level_display":"Beginner"})Assessment=apps.get_model("assessment""Assessment")assessment=Assessment.objects.create(student=studentassessment_type=assessment_typestart_time=timezone.now()initial_level=student_level.current_level#Setinitiallevelfromstudentlevellearning_path=learning_pathor"general"#Setlearningpath)assessment.questions.set(questions)returnassessmentexceptExceptionase:logger.error(f"Errorcreatingassessment:{str(e)}")raiseAssessmentError(f"Failedtocreateassessment:{str(e)}")def_select_questions(selfassessment_typenum_questionsstudentlearning_path=None):"""Selectappropriatequestionsfortheassessment"""try:StudentLevel=apps.get_model("assessment""StudentLevel")AssessmentQuestion=apps.get_model("assessment""AssessmentQuestion")#Getstudent'scurrentlevelstudent_level=StudentLevel.objects.get(student=student)current_level=student_level.current_level#Getbasequerysetquestions=AssessmentQuestion.objects.filter(assessment_type=assessment_typeis_public=True#Useis_publicinsteadofis_active)#Filterbylearningpathifspecifiediflearning_pathandlearning_path!="general":#Firsttrytogetpath-specificquestionspath_questions=questions.filter(category=learning_path)#Ifwehaveenoughpath-specificquestionsusethemifpath_questions.count()>=num_questions:questions=path_questionselse:#Otherwiseprioritizepath-specificquestionsbutincludegeneralonesgeneral_questions=questions.filter(category="general")#Combinepath-specificandgeneralquestionsquestions=list(path_questions)+list(general_questions)#ApplyadaptivedifficultyifenabledAssessmentSettings=apps.get_model("assessment""AssessmentSettings")settings=AssessmentSettings.objects.get(assessment_type=assessment_type)ifsettings.adaptive_difficulty:questions=self._apply_adaptive_difficulty(questionsstudent_level)#Randomizeandreturnrequirednumberofquestionsreturnquestions.order_by("?")[:num_questions]exceptStudentLevel.DoesNotExist:#IfnolevelexistsstartwithbasicquestionsAssessmentQuestion=apps.get_model("assessment""AssessmentQuestion")returnAssessmentQuestion.objects.filter(assessment_type=assessment_typeis_public=True#Useis_publicinsteadofis_activedifficulty_level=1).order_by("?")[:num_questions]def_apply_adaptive_difficulty(selfquestionsstudent_levelassessment=Noneprevious_response=None):"""Applyadaptivedifficultybasedonstudentlevelandperformance"""current_level=student_level.current_levelAssessmentSettings=apps.get_model("assessment""AssessmentSettings")#Getassessmentsettingstry:ifassessment:settings=AssessmentSettings.objects.get(assessment_type=assessment.assessment_type)else:#Defaulttoplacementassessmentsettingsifnoassessmentprovidedsettings=AssessmentSettings.objects.get(assessment_type="PLACEMENT")exceptAssessmentSettings.DoesNotExist:#Createdefaultsettingsifnoneexistsettings=AssessmentSettings.objects.create(assessment_type="PLACEMENT")#Getadaptivesettingswithdefaultsadaptive_settings=settings.get_adaptive_settings()#Ifthisisanongoingassessmentwithapreviousresponseadjustdifficultybasedonperformanceif(assessmentandprevious_responseandassessment.responses.count()>=adaptive_settings["min_questions_before_adapt"]):returnself._adjust_difficulty_based_on_performance(questionsassessmentstudent_leveladaptive_settings)#Initialquestionselectionbasedonstudentleveldifficulty_distribution=adaptive_settings["difficulty_distribution"]#Calculatenumberofquestionsateachdifficultylevelbelow_level_count=int(questions.count()*difficulty_distribution["below_level"])at_level_count=int(questions.count()*difficulty_distribution["at_level"])above_level_count=int(questions.count()*difficulty_distribution["above_level"])#Ensurewehaveatleastonequestionateachlevelifpossiblebelow_level_count=max(1below_level_count)at_level_count=max(1at_level_count)above_level_count=max(1above_level_count)#Getquestionsatdifferentdifficultylevelsbelow_level_questions=questions.filter(difficulty_level__lt=current_level).order_by("?")[:below_level_count]at_level_questions=questions.filter(difficulty_level=current_level).order_by("?")[:at_level_count]above_level_questions=questions.filter(difficulty_level__gt=current_level).order_by("?")[:above_level_count]#Combinequestionsselected_questions=(list(below_level_questions)+list(at_level_questions)+list(above_level_questions))#Ifwedon'thaveenoughquestionsaddmorefromanydifficultyleveliflen(selected_questions)<questions.count():remaining_count=questions.count()-len(selected_questions)excluded_ids=[q.idforqinselected_questions]remaining_questions=questions.exclude(id__in=excluded_ids).order_by("?")[:remaining_count]selected_questions.extend(list(remaining_questions))#Iffocusingonweakskillsisenabledprioritizequestionsinweakareasif(adaptive_settings["skill_focus"]andhasattr(student_level"skill_weaknesses")andstudent_level.skill_weaknesses):#Getquestionsrelatedtoweakskillsweak_skill_questions=[]forquestioninselected_questions:if(hasattr(question"skills_assessed")andquestion.skills_assessed.filter(name__in=student_level.skill_weaknesses).exists()):weak_skill_questions.append(question)#Moveweakskillquestionstothefrontforquestioninweak_skill_questions:selected_questions.remove(question)selected_questions.insert(0question)#Ifthisisanewassessmentinitializeadaptiveprogressiontrackingifassessmentandnotassessment.adaptive_progression:assessment.adaptive_progression={"difficulty_levels":[current_level]"performance_history":[]"question_difficulty_sequence":[]}assessment.current_difficulty_level=current_levelassessment.save(update_fields=["adaptive_progression""current_difficulty_level"])returnselected_questionsdef_adjust_difficulty_based_on_performance(selfquestionsassessmentstudent_leveladaptive_settings):"""Adjustquestiondifficultybasedonstudent'sperformanceinthecurrentassessment"""#Getrecentperformancerecent_responses=assessment.responses.order_by("-created_at")[:adaptive_settings["min_questions_before_adapt"]]correct_count=sum(1forrinrecent_responsesifr.is_correct)performance_rate=correct_count/len(recent_responses)#Getcurrentdifficultylevelcurrent_difficulty=assessment.current_difficulty_level#Determineifdifficultyshouldchangenew_difficulty=current_difficultyif(performance_rate>=adaptive_settings["performance_threshold"]["increase_level"]):#Studentisdoingwellincreasedifficultynew_difficulty=min(5current_difficulty+adaptive_settings["max_difficulty_jump"])elif(performance_rate<=adaptive_settings["performance_threshold"]["decrease_level"]):#Studentisstrugglingdecreasedifficultynew_difficulty=max(1current_difficulty-adaptive_settings["max_difficulty_jump"])#Updateassessmenttrackingif"difficulty_levels"notinassessment.adaptive_progression:assessment.adaptive_progression["difficulty_levels"]=[]if"performance_history"notinassessment.adaptive_progression:assessment.adaptive_progression["performance_history"]=[]if"question_difficulty_sequence"notinassessment.adaptive_progression:assessment.adaptive_progression["question_difficulty_sequence"]=[]assessment.adaptive_progression["difficulty_levels"].append(new_difficulty)assessment.adaptive_progression["performance_history"].append({"question_index":assessment.responses.count()"performance_rate":performance_rate"difficulty_before":current_difficulty"difficulty_after":new_difficulty})#Updatecurrentdifficultylevelassessment.current_difficulty_level=new_difficultyassessment.save(update_fields=["adaptive_progression""current_difficulty_level"])#Selectquestionsatthenewdifficultylevelselected_questions=questions.filter(difficulty_level=new_difficulty).order_by("?")#Ifnotenoughquestionsatthislevelexpandtherangeifselected_questions.count()<1:#Expanddifficultyrangeby1inbothdirectionsmin_difficulty=max(1new_difficulty-1)max_difficulty=min(5new_difficulty+1)selected_questions=questions.filter(difficulty_level__range=(min_difficultymax_difficulty)).order_by("?")#Trackthedifficultyofthenextquestionifselected_questions.exists():next_question_difficulty=selected_questions.first().difficulty_levelassessment.adaptive_progression["question_difficulty_sequence"].append(next_question_difficulty)assessment.save(update_fields=["adaptive_progression"])returnselected_questionsdefcomplete_assessment(selfassessment):"""Completeanassessmentandprocessresults"""ifassessment.completed:raiseAssessmentError("Assessmentalreadycompleted")try:withtransaction.atomic():#Calculatefinalscoretotal_points=sum(q.pointsforqinassessment.questions.all())earned_points=sum(r.points_earnedforrinassessment.responses.all())final_score=((earned_points/total_points*100)iftotal_points>0else0)#Updateassessmentassessment.score=final_scoreassessment.end_time=timezone.now()assessment.completed=Trueassessment.status="COMPLETED"#Generatedetailedresultsassessment.detailed_results=self._generate_detailed_results(assessment)assessment.save()#Updatestudentlevelself._update_student_level(assessment)returnassessmentexceptExceptionase:logger.error(f"Errorcompletingassessment:{str(e)}")raiseAssessmentError(f"Failedtocompleteassessment:{str(e)}")def_generate_detailed_results(selfassessment):"""Generatedetailedanalysisofassessmentresults"""AssessmentResponse=apps.get_model("assessment""AssessmentResponse")responses=assessment.responses.select_related("question").all()#Analyzebycategorycategory_analysis={}forresponseinresponses:category=response.question.categoryifcategorynotincategory_analysis:category_analysis[category]={"total_questions":0"correct_answers":0"points_earned":0"total_points":0"average_time":0}analysis=category_analysis[category]analysis["total_questions"]+=1analysis["total_points"]+=response.question.pointsifresponse.is_correct:analysis["correct_answers"]+=1analysis["points_earned"]+=response.points_earnedifresponse.time_taken:analysis["average_time"]=(analysis["average_time"]*(analysis["total_questions"]-1)+response.time_taken)/analysis["total_questions"]#Calculatestrengthsandweaknessesstrengths=[]weaknesses=[]forcategoryanalysisincategory_analysis.items():score=(analysis["points_earned"]/analysis["total_points"]*100ifanalysis["total_points"]>0else0)ifscore>=70:strengths.append(category)elifscore<=40:weaknesses.append(category)return{"category_analysis":category_analysis"strengths":strengths"weaknesses":weaknesses"time_analysis":{"average_response_time":(sum(r.time_takenforrinresponsesifr.time_taken)/len(responses)ifresponseselse0)"by_category":{cat:analysis["average_time"]forcatanalysisincategory_analysis.items()}}}def_update_student_level(selfassessment):"""Updatestudentlevelbasedonassessmentperformance"""try:StudentLevel=apps.get_model("assessment""StudentLevel")student_level_=StudentLevel.objects.get_or_create(student=assessment.student)#Calculatenewlevelbasedonassessmentscorecurrent_level=student_level.current_levelnew_level=current_level#Forplacementassessmentsuseamoredetailedleveldeterminationifassessment.assessment_type=="PLACEMENT":ifassessment.score>=90:new_level=5#Expertelifassessment.score>=75:new_level=4#Advancedelifassessment.score>=60:new_level=3#Intermediateelifassessment.score>=40:new_level=2#Elementaryelse:new_level=1#Beginnerelse:#Forregularassessmentsadjustlevelupordownifassessment.score>=90:new_level=min(5current_level+1)elifassessment.score<=40:new_level=max(1current_level-1)#Updatestudentlevelifchangedifnew_level!=current_level:student_level.update_level(new_levelf"{assessment.assessment_type}assessmentscore:{assessment.score}")#Updateskillsifdetailedresultsareavailableifhasattr(assessment"detailed_results")andassessment.detailed_results:student_level.skill_strengths=assessment.detailed_results.get("strengths"{})student_level.skill_weaknesses=assessment.detailed_results.get("weaknesses"{})student_level.last_assessment_date=timezone.now()student_level.save()returnstudent_levelexceptExceptionase:logger.error(f"Errorupdatingstudentlevel:{str(e)}")raiseAssessmentError(f"Failedtoupdatestudentlevel:{str(e)}")classAssessmentAIService:"""ServiceclassforAI-poweredassessmentfeatures"""def__init__(self):self.ai_settings=getattr(settings"ASSESSMENT_AI_SETTINGS"{})self.auto_approval_threshold=self.ai_settings.get("auto_approval_threshold"0.9)self.review_threshold=self.ai_settings.get("review_threshold"0.7)defgenerate_questions(selfcategory=Nonedifficulty_level=Nonecount=5):"""GenerateassessmentquestionsusingAI"""try:#GetexistingquestionsifAIgenerationfailsAssessmentQuestion=apps.get_model("assessment""AssessmentQuestion")#TrytogeneratequestionswithAIai_questions=self._generate_ai_questions(categorydifficulty_levelcount)ifai_questions:returnai_questions#Fallbacktodatabasequestionsreturnlist(AssessmentQuestion.objects.filter(category=categoryifcategoryelse"GENERAL"difficulty_level=difficulty_levelifdifficulty_levelelse1is_public=True#Useis_publicinsteadofis_active).order_by("?")[:count])exceptExceptionase:logger.error(f"Errorgeneratingquestions:{str(e)}")return[]defvalidate_question(selfquestion_data):"""ValidategeneratedquestionusingAI"""try:#AIvalidationlogichereconfidence_score=0.0requires_review=confidence_score<self.auto_approval_thresholdreturn{"is_valid":confidence_score>=self.review_threshold"confidence_score":confidence_score"requires_review":requires_review"flags":[]"suggestions":[]}exceptExceptionase:logger.error(f"Errorvalidatingquestion:{str(e)}")returnNonedef_generate_ai_questions(selfcategory=Nonedifficulty_level=Nonecount=5):"""GeneratequestionsusingAI"""try:from utils.consolidated_ai_serviceimportget_ai_servicefrom.modelsimportAssessmentQuestion#Defaultvaluescategory=categoryifcategoryelse"general"difficulty_level=int(difficulty_level)ifdifficulty_levelelse1#Createamoredetailedpromptforbetterresultsprompt=f"""Generate{count}assessmentquestionsforaplacementassessment.Category:{category}DifficultyLevel:{difficulty_level}(onascaleof1-5where1isbeginnerand5isexpert)Foreachquestionprovide:1.Thequestiontext2.Thequestiontype(MULTIPLE_CHOICETRUE_FALSESHORT_ANSWER)3.Answeroptions(formultiplechoice)4.Thecorrectanswer5.AnexplanationofwhytheansweriscorrectIMPORTANT:YourresponsemustbeavalidJSONarraycontainingquestionobjects.DonotincludeanyexplanatorytextbeforeoraftertheJSONarray.FormateachquestionasaJSONobjectwiththefollowingstructure:{{"text":"Questiontexthere""question_type":"MULTIPLE_CHOICE""options":["OptionA""OptionB""OptionC""OptionD"]"correct_answer":"OptionA""explanation":"Explanationhere""category":"{category}""difficulty_level":{difficulty_level}}}ReturnONLYavalidJSONarrayofquestionobjects.Exampleformat:[{{"text":"Whatisavariableinprogramming?""question_type":"MULTIPLE_CHOICE""options":["OptionA""OptionB""OptionC""OptionD"]"correct_answer":"OptionA""explanation":"Explanationhere""category":"{category}""difficulty_level":{difficulty_level}}}{{"text":"IsPythonacompiledlanguage?""question_type":"TRUE_FALSE""options":["True""False"]"correct_answer":"False""explanation":"Explanationhere""category":"{category}""difficulty_level":{difficulty_level}}}]"""#GeneratecontentusingAIserviceresponse=get_ai_service().generate_content(prompt)response_text=(response.textifhasattr(response"text")elsestr(response))#Logtheresponsefordebugginglogger.info(f"AIresponsereceived:{response_text[:100]}...")#TrytoparsetheJSONresponsetry:#FindJSONarrayintheresponseimport jsonimport re#FirsttrytoparsetheentireresponseasJSONtry:questions_data=json.loads(response_text)exceptjson.JSONDecodeError:#IfthatfailslookforJSONarraypatternjson_match=re.search(r"\[\s*{.*}\s*\]"response_textre.DOTALL)ifjson_match:json_str=json_match.group(0)questions_data=json.loads(json_str)else:#TrytofindindividualJSONobjectsjson_objects=re.findall(r"{[^{}]*}"response_text)ifjson_objects:questions_data=[]forobj_strinjson_objects:try:obj=json.loads(obj_str)questions_data.append(obj)exceptjson.JSONDecodeError:continueelse:#NovalidJSONfoundreturn[]#Ensurewehavealistifnotisinstance(questions_datalist):ifisinstance(questions_datadict):questions_data=[questions_data]else:return[]#CreateAssessmentQuestionobjectsquestions=[]forq_datainquestions_data:#Validaterequiredfieldsif"text"notinq_dataor"question_type"notinq_data:continue#Mapfieldstomodelfields#Handlecorrect_answerproperly-ensureit'savalidJSONobjectcorrect_answer=q_data.get("correct_answer")orq_data.get("correct""")ifisinstance(correct_answerstr):#ConvertstringtoJSONobjectifneededcorrect_answer={"answer":correct_answer}question=AssessmentQuestion(text=q_data.get("text")question_type=q_data.get("question_type")options=q_data.get("options"[])correct_answer=correct_answerdifficulty_level=int(q_data.get("difficulty_level"difficulty_level))category=q_data.get("category"category)ai_suggested=Trueai_reviewed=False)questions.append(question)returnquestionsexceptExceptionasparse_error:logger.error(f"ErrorparsingAIresponse:{str(parse_error)}")logger.error(f"Responsewas:{response_text[:500]}...")return[]exceptExceptionase:logger.error(f"ErrorinAIquestiongeneration:{str(e)}")return[]defanalyze_response(selfresponse):"""AnalyzestudentresponseusingAI"""try:#AIanalysislogicherereturn{}exceptExceptionase:logger.error(f"Erroranalyzingresponse:{str(e)}")returnNonedefgenerate_feedback(selfassessment):"""GeneratepersonalizedfeedbackusingAI"""try:#AIfeedbackgenerationlogicherereturn{}exceptExceptionase:logger.error(f"Errorgeneratingfeedback:{str(e)}")returnNonedefget_recommendations(selfstudentassessment=None):"""GetAI-poweredlearningrecommendations"""try:#AIrecommendationlogicherereturn[]exceptExceptionase:logger.error(f"Errorgettingrecommendations:{str(e)}")return[]defpredict_student_performance(selfstudentcourse=None):"""PredictstudentperformanceusingAI"""try:#AIpredictionlogicherereturnNoneexceptExceptionase:logger.error(f"Errorpredictingperformance:{str(e)}")returnNonedefget_recommended_courses(selflevel):"""Getrecommendedcoursesforastudentlevel"""Course=apps.get_model("courses""Course")#Getcoursesmatchingthestudent'slevellevel_courses=Course.objects.filter(is_active=Truedifficulty_level=level)#Ifnotenoughcoursesatthislevelincludecoursesfromadjacentlevelsiflevel_courses.count()<3:iflevel>1:#Includesomecoursesfromthelevelbelowlower_level_courses=Course.objects.filter(is_active=Truedifficulty_level=level-1)level_courses=list(level_courses)+list(lower_level_courses[:2])iflevel<5:#Includesomeintroductorycoursesfromthelevelabovehigher_level_courses=Course.objects.filter(is_active=Truedifficulty_level=level+1is_introductory=True)level_courses=list(level_courses)+list(higher_level_courses[:1])#Returnatmost5coursesrandomizedreturnsorted(level_courses[:5]key=lambda_:random.random())
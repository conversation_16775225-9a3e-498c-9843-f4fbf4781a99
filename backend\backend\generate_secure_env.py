#!/usr/bin/env python3
"""
Generate secure environment variables for production deployment.
This script creates cryptographically secure keys and tokens.
"""

import secrets
import string
from django.core.management.utils import get_random_secret_key

def generate_secret_key():
    """Generate a secure Django secret key."""
    return get_random_secret_key()

def generate_api_encryption_key():
    """Generate a Fernet encryption key for API key storage."""
    from cryptography.fernet import Fernet
    return Fernet.generate_key().decode()

def generate_random_password(length=32):
    """Generate a cryptographically secure password."""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def generate_jwt_secret():
    """Generate a secure JWT secret."""
    return secrets.token_urlsafe(64)

def main():
    print("🔐 Generating Secure Environment Variables")
    print("=" * 50)
    
    print("\n# Django Settings")
    print(f"DJANGO_SECRET_KEY={generate_secret_key()}")
    print("DJANGO_DEBUG=False")
    print("DJANGO_ALLOWED_HOSTS=your-domain.com,localhost")
    
    print("\n# Database Configuration")
    print("# DATABASE_URL=postgresql://username:password@localhost:5432/dbname")
    
    print("\n# API Key Encryption")
    try:
        print(f"API_ENCRYPTION_KEY={generate_api_encryption_key()}")
    except ImportError:
        print("# API_ENCRYPTION_KEY=install-cryptography-package-first")
    
    print("\n# JWT Configuration")
    print(f"JWT_SECRET_KEY={generate_jwt_secret()}")
    
    print("\n# Email Configuration")
    print("EMAIL_HOST=smtp.your-provider.com")
    print("EMAIL_PORT=587")
    print("EMAIL_HOST_USER=<EMAIL>")
    print(f"EMAIL_HOST_PASSWORD={generate_random_password(24)}")
    print("EMAIL_USE_TLS=True")
    
    print("\n# Redis Configuration")
    print("REDIS_URL=redis://localhost:6379/0")
    
    print("\n# Security Settings")
    print("SECURE_SSL_REDIRECT=True")
    print("SECURE_HSTS_SECONDS=31536000")
    print("SECURE_HSTS_INCLUDE_SUBDOMAINS=True")
    print("SECURE_CONTENT_TYPE_NOSNIFF=True")
    print("SECURE_BROWSER_XSS_FILTER=True")
    print("SESSION_COOKIE_SECURE=True")
    print("CSRF_COOKIE_SECURE=True")
    
    print("\n# AI Service Configuration")
    print("# GEMINI_API_KEY=your-gemini-api-key-here")
    print("# OPENAI_API_KEY=your-openai-api-key-here")
    print("# ANTHROPIC_API_KEY=your-anthropic-api-key-here")
    
    print("\n⚠️  IMPORTANT SECURITY NOTES:")
    print("1. Never commit these values to version control")
    print("2. Store them securely in your production environment")
    print("3. Rotate keys regularly")
    print("4. Use a secrets management service in production")

if __name__ == "__main__":
    main()

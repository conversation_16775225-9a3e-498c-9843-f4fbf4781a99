"""Tests for Material model"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from courses.models import Course, Material, Department

User = get_user_model()

class MaterialModelTest(TestCase):
    """Test cases for Material model"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User"
        )
        self.department = Department.objects.create(
            name="Computer Science",
            code="CS",
            description="Computer Science Department"
        )
        self.course = Course.objects.create(
            title="Test Course",
            course_code="CS101",
            description="A test course",
            instructor=self.user,
            department=self.department,
            credits=3,
            semester="FALL",
            academic_year="2023-2024"
        )

    def test_material_created_at_ordering(self):
        """Test that Material objects can be ordered by -created_at"""
        # Create materials at different times
        material1 = Material.objects.create(
            title="First Material",
            description="First material description",
            course=self.course,
            created_by=self.user
        )
        
        # Sleep briefly to ensure different timestamps
        import time
        time.sleep(0.01)
        
        material2 = Material.objects.create(
            title="Second Material",
            description="Second material description",
            course=self.course,
            created_by=self.user
        )
        
        time.sleep(0.01)
        
        material3 = Material.objects.create(
            title="Third Material",
            description="Third material description",
            course=self.course,
            created_by=self.user
        )
        
        # Test ordering by -created_at (newest first)
        materials_desc = Material.objects.order_by('-created_at')
        self.assertEqual(list(materials_desc), [material3, material2, material1])
        
        # Test ordering by created_at (oldest first)
        materials_asc = Material.objects.order_by('created_at')
        self.assertEqual(list(materials_asc), [material1, material2, material3])

    def test_material_has_created_at_field(self):
        """Test that Material model has created_at field"""
        material = Material.objects.create(
            title="Test Material",
            description="Test material description",
            course=self.course,
            created_by=self.user
        )
        
        # Check that created_at field exists and is set
        self.assertTrue(hasattr(material, 'created_at'))
        self.assertIsNotNone(material.created_at)
        self.assertIsInstance(material.created_at, timezone.datetime)

    def test_material_no_uploaded_at_field(self):
        """Test that Material model does not have uploaded_at field"""
        material = Material.objects.create(
            title="Test Material",
            description="Test material description",
            course=self.course,
            created_by=self.user
        )
        
        # Check that uploaded_at field does not exist
        self.assertFalse(hasattr(material, 'uploaded_at'))

    def test_material_ordering_filter_combination(self):
        """Test ordering works with filters"""
        # Create materials with different titles
        material1 = Material.objects.create(
            title="Python Tutorial",
            description="Python basics",
            course=self.course,
            created_by=self.user
        )
        
        import time
        time.sleep(0.01)
        
        material2 = Material.objects.create(
            title="Java Tutorial",
            description="Java basics",
            course=self.course,
            created_by=self.user
        )
        
        time.sleep(0.01)
        
        material3 = Material.objects.create(
            title="Python Advanced",
            description="Advanced Python concepts",
            course=self.course,
            created_by=self.user
        )
        
        # Test filtering and ordering together
        python_materials = Material.objects.filter(title__contains="Python").order_by('-created_at')
        expected_python_materials = [material3, material1]
        self.assertEqual(list(python_materials), expected_python_materials)

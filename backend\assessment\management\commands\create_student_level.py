fromdjango.contrib.authimportget_user_modelfromdjango.core.management.baseimportBaseCommandfromdjango.utilsimporttimezonefromassessment.modelsimportStudentLevelUser=get_user_model()classCommand(BaseCommand):help="CreateaStudentLevelrecordforastudent"defadd_arguments(selfparser):parser.add_argument("--student_id"type=inthelp="IDofthestudent")parser.add_argument("--username"type=strhelp="Usernameofthestudent")parser.add_argument("--all"action="store_true"help="CreateStudentLevelrecordsforallstudentswithoutone")parser.add_argument("--level"type=intdefault=1help="Initialleveltoset(default:1)")defhandle(self*args**options):student_id=options.get("student_id")username=options.get("username")create_all=options.get("all")level=options.get("level"1)ifcreate_all:#GetallstudentswithoutaStudentLevelrecordstudents_without_level=User.objects.filter(level_profile__isnull=True).exclude(is_staff=True)count=0forstudentinstudents_without_level:self._create_student_level(studentlevel)count+=1self.stdout.write(self.style.SUCCESS(f"Created{count}StudentLevelrecords"))returnifstudent_id:try:student=User.objects.get(id=student_id)self._create_student_level(studentlevel)returnexceptUser.DoesNotExist:self.stdout.write(self.style.ERROR(f"StudentwithID{student_id}doesnotexist"))returnifusername:try:student=User.objects.get(username=username)self._create_student_level(studentlevel)returnexceptUser.DoesNotExist:self.stdout.write(self.style.ERROR(f"Studentwithusername{username}doesnotexist"))returnself.stdout.write(self.style.ERROR("Pleaseprovideeither--student_id--usernameor--all"))def_create_student_level(selfstudentlevel):#CheckifStudentLevelalreadyexistsifhasattr(student"level_profile"):self.stdout.write(self.style.WARNING(f"StudentLevelalreadyexistsfor{student.username}"))return#CreateStudentLevellevel_display=dict(StudentLevel.LEVEL_CHOICES).get(level"Beginner")student_level=StudentLevel.objects.create(student=studentcurrent_level=levelcurrent_level_display=level_displaylast_assessment_date=timezone.now())self.stdout.write(self.style.SUCCESS(f"CreatedStudentLevelfor{student.username}withlevel{level}({level_display})"))
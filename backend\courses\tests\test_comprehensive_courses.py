"""
Comprehensive tests for courses system.
"""

import pytest
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from unittest.mock import patch, Mock
from django.core.files.uploadedfile import SimpleUploadedFile

from courses.models import Course, Department, Enrollment, Material, CourseSection
from assessment.models import Assessment

User = get_user_model()


@pytest.mark.unit
class TestCourseModels:
    """Test course model functionality."""
    
    def test_course_creation(self, sample_course_data):
        """Test course creation."""
        course = Course.objects.create(**sample_course_data)
        
        assert course.title == sample_course_data['title']
        assert course.code == sample_course_data['code']
        assert course.credits == sample_course_data['credits']
        assert course.level == sample_course_data['level']
    
    def test_department_creation(self):
        """Test department creation."""
        department = Department.objects.create(
            name='Computer Science',
            code='CS',
            description='Computer Science Department'
        )
        
        assert department.name == 'Computer Science'
        assert department.code == 'CS'
        assert str(department) == 'Computer Science (CS)'
    
    def test_enrollment_creation(self, test_student, sample_course_data):
        """Test enrollment creation."""
        course = Course.objects.create(**sample_course_data)
        
        enrollment = Enrollment.objects.create(
            student=test_student,
            course=course,
            enrollment_status='ENROLLED'
        )
        
        assert enrollment.student == test_student
        assert enrollment.course == course
        assert enrollment.enrollment_status == 'ENROLLED'
    
    def test_course_material_creation(self, test_professor, sample_course_data, temp_media_root):
        """Test course material creation."""
        course = Course.objects.create(**sample_course_data)
        
        # Create a test file
        test_file = SimpleUploadedFile(
            "test_material.pdf",
            b"file_content",
            content_type="application/pdf"
        )
        
        material = Material.objects.create(
            course=course,
            title='Test Material',
            description='Test material description',
            file=test_file,
            uploaded_by=test_professor
        )
        
        assert material.course == course
        assert material.title == 'Test Material'
        assert material.uploaded_by == test_professor
    
    def test_course_section_creation(self, test_professor, sample_course_data):
        """Test course section creation."""
        course = Course.objects.create(**sample_course_data)
        
        section = CourseSection.objects.create(
            course=course,
            section_number='001',
            instructor=test_professor,
            max_students=30,
            schedule='MWF 10:00-11:00'
        )
        
        assert section.course == course
        assert section.instructor == test_professor
        assert section.max_students == 30


@pytest.mark.api
class TestCourseAPI:
    """Test course API endpoints."""
    
    def test_list_courses(self, api_client, sample_course_data):
        """Test listing courses."""
        # Create test courses
        Course.objects.create(**sample_course_data)
        Course.objects.create(
            title='Course 2',
            code='TEST102',
            description='Second test course',
            credits=4,
            level='INTERMEDIATE'
        )
        
        url = reverse('courses:course-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 2
    
    def test_get_course_detail(self, api_client, sample_course_data):
        """Test getting course details."""
        course = Course.objects.create(**sample_course_data)
        
        url = reverse('courses:course-detail', kwargs={'pk': course.pk})
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['title'] == sample_course_data['title']
    
    def test_create_course_as_professor(self, authenticated_client, test_professor, sample_course_data):
        """Test creating a course as professor."""
        # Set user as professor
        test_professor.role = 'PROFESSOR'
        test_professor.save()
        
        # Authenticate as professor
        from rest_framework_simplejwt.tokens import RefreshToken
        refresh = RefreshToken.for_user(test_professor)
        authenticated_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        url = reverse('courses:course-list')
        response = authenticated_client.post(url, sample_course_data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert Course.objects.filter(title=sample_course_data['title']).exists()
    
    def test_enroll_in_course(self, authenticated_client, test_student, sample_course_data):
        """Test enrolling in a course."""
        course = Course.objects.create(**sample_course_data)
        
        url = reverse('courses:course-enroll', kwargs={'pk': course.pk})
        response = authenticated_client.post(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert Enrollment.objects.filter(student=test_student, course=course).exists()
    
    def test_unenroll_from_course(self, authenticated_client, test_student, sample_course_data):
        """Test unenrolling from a course."""
        course = Course.objects.create(**sample_course_data)
        
        # First enroll
        Enrollment.objects.create(
            student=test_student,
            course=course,
            enrollment_status='ENROLLED'
        )
        
        url = reverse('courses:course-unenroll', kwargs={'pk': course.pk})
        response = authenticated_client.post(url)
        
        assert response.status_code == status.HTTP_200_OK
        enrollment = Enrollment.objects.get(student=test_student, course=course)
        assert enrollment.enrollment_status == 'DROPPED'
    
    def test_get_course_materials(self, authenticated_client, test_student, test_professor, sample_course_data):
        """Test getting course materials."""
        course = Course.objects.create(**sample_course_data)
        
        # Enroll student in course
        Enrollment.objects.create(
            student=test_student,
            course=course,
            enrollment_status='ENROLLED'
        )
        
        # Create material
        Material.objects.create(
            course=course,
            title='Test Material',
            description='Test description',
            uploaded_by=test_professor
        )
        
        url = reverse('courses:course-materials', kwargs={'pk': course.pk})
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['title'] == 'Test Material'


@pytest.mark.integration
class TestCourseIntegration:
    """Integration tests for course system."""
    
    def test_complete_course_workflow(self, authenticated_client, test_student, test_professor):
        """Test complete course workflow."""
        # 1. Create department
        department = Department.objects.create(
            name='Test Department',
            code='TD',
            description='Test department'
        )
        
        # 2. Create course
        course_data = {
            'title': 'Integration Test Course',
            'code': 'ITC101',
            'description': 'Course for integration testing',
            'credits': 3,
            'level': 'BEGINNER',
            'department': department.id
        }
        
        # Authenticate as professor
        from rest_framework_simplejwt.tokens import RefreshToken
        refresh = RefreshToken.for_user(test_professor)
        authenticated_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        create_url = reverse('courses:course-list')
        create_response = authenticated_client.post(create_url, course_data, format='json')
        assert create_response.status_code == status.HTTP_201_CREATED
        
        course_id = create_response.data['id']
        
        # 3. Switch to student and enroll
        refresh = RefreshToken.for_user(test_student)
        authenticated_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        enroll_url = reverse('courses:course-enroll', kwargs={'pk': course_id})
        enroll_response = authenticated_client.post(enroll_url)
        assert enroll_response.status_code == status.HTTP_200_OK
        
        # 4. Get enrolled courses
        enrolled_url = reverse('courses:student-courses')
        enrolled_response = authenticated_client.get(enrolled_url)
        assert enrolled_response.status_code == status.HTTP_200_OK
        assert len(enrolled_response.data['results']) == 1
        
        # 5. Access course content
        detail_url = reverse('courses:course-detail', kwargs={'pk': course_id})
        detail_response = authenticated_client.get(detail_url)
        assert detail_response.status_code == status.HTTP_200_OK


@pytest.mark.ai
class TestAICourseFeatures:
    """Test AI-powered course features."""
    
    @patch('courses.services.AIService')
    def test_ai_course_recommendation(self, mock_ai_service, authenticated_client, test_student):
        """Test AI-powered course recommendations."""
        mock_ai_service.return_value.recommend_courses.return_value = {
            'recommendations': [
                {
                    'course_id': 1,
                    'title': 'Recommended Course',
                    'confidence': 0.95,
                    'reason': 'Based on your interests'
                }
            ]
        }
        
        url = reverse('courses:ai-recommendations')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'recommendations' in response.data
    
    @patch('courses.services.AIService')
    def test_ai_content_generation(self, mock_ai_service, authenticated_client, test_professor, sample_course_data):
        """Test AI-powered content generation."""
        mock_ai_service.return_value.generate_content.return_value = {
            'content': 'AI generated course content',
            'topics': ['Topic 1', 'Topic 2', 'Topic 3']
        }
        
        course = Course.objects.create(**sample_course_data)
        
        # Authenticate as professor
        from rest_framework_simplejwt.tokens import RefreshToken
        refresh = RefreshToken.for_user(test_professor)
        authenticated_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        
        data = {
            'topic': 'Introduction to Programming',
            'difficulty': 'BEGINNER',
            'duration': 60
        }
        
        url = reverse('courses:ai-generate-content', kwargs={'pk': course.pk})
        response = authenticated_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'content' in response.data


@pytest.mark.performance
class TestCoursePerformance:
    """Test course system performance."""
    
    def test_bulk_course_creation(self, performance_timer):
        """Test performance of bulk course creation."""
        performance_timer.start()
        
        courses = []
        for i in range(100):
            courses.append(Course(
                title=f'Course {i}',
                code=f'TEST{i:03d}',
                description=f'Test course {i}',
                credits=3,
                level='BEGINNER'
            ))
        
        Course.objects.bulk_create(courses)
        performance_timer.stop()
        
        assert performance_timer.elapsed < 1.0  # Should complete in under 1 second
        assert Course.objects.count() == 100
    
    def test_course_query_optimization(self, test_student, sample_course_data):
        """Test optimized queries for course retrieval."""
        # Create test data
        course = Course.objects.create(**sample_course_data)
        
        Enrollment.objects.create(
            student=test_student,
            course=course,
            enrollment_status='ENROLLED'
        )
        
        # Test optimized query
        with pytest.assertNumQueries(2):  # Should use minimal queries
            courses_with_enrollments = Course.objects.select_related(
                'department'
            ).prefetch_related(
                'enrollments',
                'materials'
            ).filter(enrollments__student=test_student)
            
            # Access related data
            for course in courses_with_enrollments:
                _ = course.department
                _ = list(course.enrollments.all())
                _ = list(course.materials.all())


@pytest.mark.database
class TestCourseDatabase:
    """Test course database operations."""
    
    def test_course_cascade_delete(self, test_student, test_professor, sample_course_data):
        """Test cascade deletion of course data."""
        course = Course.objects.create(**sample_course_data)
        
        enrollment = Enrollment.objects.create(
            student=test_student,
            course=course,
            enrollment_status='ENROLLED'
        )
        
        material = Material.objects.create(
            course=course,
            title='Test Material',
            uploaded_by=test_professor
        )
        
        course_id = course.id
        enrollment_id = enrollment.id
        material_id = material.id
        
        # Delete course
        course.delete()
        
        # Verify cascade deletion
        assert not Course.objects.filter(id=course_id).exists()
        assert not Enrollment.objects.filter(id=enrollment_id).exists()
        assert not Material.objects.filter(id=material_id).exists()
    
    def test_enrollment_constraints(self, test_student, sample_course_data):
        """Test enrollment constraints."""
        course = Course.objects.create(**sample_course_data)
        
        # Create first enrollment
        Enrollment.objects.create(
            student=test_student,
            course=course,
            enrollment_status='ENROLLED'
        )
        
        # Try to create duplicate enrollment (should be prevented by unique constraint)
        with pytest.raises(Exception):
            Enrollment.objects.create(
                student=test_student,
                course=course,
                enrollment_status='ENROLLED'
            )

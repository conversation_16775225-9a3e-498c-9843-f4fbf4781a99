"""
API URL configuration for the University Management System.
All APIs are standardized to v1.
"""

from django.urls import include, path

# Import multi-agent API views
from utils.multi_agent_api_views import (
    multi_agent_features,
    multi_agent_status,
    test_agent_response,
    test_agent_routing
)

# Real API imports - no more mocks



# Helper function to safely include URLs
def safe_include(module_urls):
    try:
        from django.urls import include
        return include(module_urls)
    except ImportError:
        # Return empty patterns if module doesn't exist
        return []

# API v1 URL patterns - using real APIs only
v1_urlpatterns = [
    # Authentication and user management (core - always available)
    path("auth/", include("auth_api.urls")),
    path("users/", include("users.urls")),
    
    # Multi-Agent API Endpoints (always available)
    path("multi-agent/status/", multi_agent_status, name="multi-agent-status"),
    path("multi-agent/features/", multi_agent_features, name="multi-agent-features"),
    path("multi-agent/test-routing/", test_agent_routing, name="multi-agent-test-routing"),
    path("multi-agent/test-response/", test_agent_response, name="multi-agent-test-response"),
]

# Additional modules - conditionally loaded
try:
    from django.urls import include
    # Add only the most essential modules that are known to work
    essential_modules = [
        ("utils/", "utils.urls"),
        ("assessment/", "assessment.urls"),
        ("courses/", "courses.urls"),
        ("grades/", "grades.urls"),
        ("notifications/", "notifications.urls"),
    ]

    # Temporarily disabled modules with import issues
    disabled_modules = [
    ]
    
    # Optional modules that may have issues - skip silently
    optional_modules = [
        ("chatbot/", "chatbot.urls"),
        ("ai-assistant/", "ai_assistant.urls"),
        ("blockchain/", "blockchain_credentials.urls"),
        ("course-generator/", "course_generator.urls"),
        ("academic/", "academic_management.urls"),
        ("interactive-learning/", "interactive_learning.urls"),

        ("study-assistant/", "study_assistant.urls"),
    ]
    
    # Add essential modules first
    for url_path, module_name in essential_modules:
        try:
            print(f"Attempting to load module: {module_name} at path: {url_path}")
            # Test import first
            import importlib
            module = importlib.import_module(module_name)
            print(f"Module {module_name} imported successfully")
            # Now include the URLs
            v1_urlpatterns.append(path(url_path, include(module_name)))
            print(f"Successfully loaded module: {module_name}")
        except (ImportError, Exception) as e:
            # Print the error for debugging
            print(f"Failed to load module {module_name}: {e}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            continue
    
    # Add optional modules
    for url_path, module_name in optional_modules:
        try:
            v1_urlpatterns.append(path(url_path, include(module_name)))
        except (ImportError, Exception):
            # Skip modules that have import issues
            continue
except Exception:
    # If there are any issues, just use the core patterns
    pass

# Main API URL patterns - all requests go to v1
api_urlpatterns = [
    path("", include((v1_urlpatterns, "v1"))),
]


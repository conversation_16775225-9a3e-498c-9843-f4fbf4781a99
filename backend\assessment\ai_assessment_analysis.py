"""AI-poweredassessmentanalysisserviceforadmindashboard.ThismoduleprovidesAI-driveninsightsandrecommendationsforstudentassessments."""import json
import loggingimport refromtypingimportDictListfrom django.utilsimport timezonefrom utils.ai_service_utilsimportget_ai_loggerfrom utils.ai_service_improvedimportimproved_ai_servicefrom.modelsimportAIAssessmentControllerAssessmentAssessmentResponseStudentLevel#UseimprovedAIserviceinstanceai_service=improved_ai_service#Usestandardizedloggerlogger=get_ai_logger("ai_assessment_analysis")classAIAssessmentAnalysis:"""ServiceforAI-poweredassessmentanalysis"""def__init__(self):"""InitializetheAIassessmentanalysisservice"""self.ai_service=ai_servicedefanalyze_student_assessment(selfassessment_id:int)->Dict:"""Analyzeastudent'sassessmentusingAIArgs:assessment_id:TheIDoftheassessmenttoanalyzeReturns:AdictionarycontainingAIanalysisresults"""try:#Gettheassessmentassessment=Assessment.objects.get(id=assessment_id)#Getallresponsesforthisassessmentresponses=AssessmentResponse.objects.filter(assessment=assessment).select_related("question")#BuildthepromptforAIanalysisprompt=self._build_assessment_analysis_prompt(assessmentresponses)#GetAIanalysisanalysis_result=self.ai_service.generate_structured_text(promptoutput_format="json")#Parsetheresulttry:#FirstcheckiftheresultisalreadyanerrorresponsefromtheAIservicetry:error_check=json.loads(analysis_result)if"error"inerror_check:logger.error(f"AIservicereturnedanerror:{error_check['error']}")return{"error":error_check.get("error""Failedtogeneratestructuredcontent")"message":error_check.get("message""Unknownerror")"raw_response":analysis_result[:500]}exceptjson.JSONDecodeError:#NotavalidJSONerrorresponsecontinuewithnormalprocessingpass#Cleanuptheresponsetohandlepotentialformattingissuescleaned_result=analysis_result#Handlemarkdowncodeblockswithjsonif"```json"incleaned_result:cleaned_result=(cleaned_result.split("```json")[1].split("```")[0].strip())#Handlemarkdowncodeblockswithoutlanguagespecificationelif"```"incleaned_result:cleaned_result=(cleaned_result.split("```")[1].split("```")[0].strip())#TrytofindJSONobjectinthetextjson_match=re.search(r"\{[\s\S]*\}"cleaned_result)ifjson_match:cleaned_result=json_match.group(0)else:#Ifwecan'tfindvalidJSONmarkersraiseanerrorraisejson.JSONDecodeError("NovalidJSONobjectfound"cleaned_result0)try:analysis_data=json.loads(cleaned_result)exceptjson.JSONDecodeErrorasjson_err:logger.error(f"JSONdecodeerror:{str(json_err)}.AttemptingtofixmalformedJSON.")#TrytofixcommonJSONformattingissuesfixed_json=cleaned_result.replace("'\n""\n")fixed_json=fixed_json.replace("'"'"')analysis_data=json.loads(fixed_json)#Validatethestructureoftheanalysisdatarequired_fields=["overall_analysis""strengths""weaknesses""recommended_level""course_recommendations""learning_path"]forfieldinrequired_fields:iffieldnotinanalysis_data:logger.error(f"Missingrequiredfield'{field}'inassessmentanalysisdata")return{"error":f"Missingrequiredfield'{field}'inassessmentanalysisdata""raw_response":analysis_result[:500]}#CreateanAIassessmentdecisionrecordself._create_ai_decision(assessmentanalysis_data)returnanalysis_dataexceptjson.JSONDecodeErrorase:logger.error(f"ErrorparsingAIanalysisresult:{str(e)}")return{"error":"FailedtoparseAIanalysis""raw_response":analysis_result[:500]#Includepartoftherawresponsefordebugging}exceptExceptionase:logger.error(f"Unexpectederrorinassessmentanalysis:{str(e)}")return{"error":"Unexpectederrorinassessmentanalysis""message":str(e)"raw_response":analysis_result[:500]}exceptAssessment.DoesNotExist:logger.error(f"AssessmentwithID{assessment_id}notfound")return{"error":f"AssessmentwithID{assessment_id}notfound"}exceptExceptionase:logger.error(f"Erroranalyzingassessment:{str(e)}")return{"error":f"Erroranalyzingassessment:{str(e)}"}def_build_assessment_analysis_prompt(selfassessment:Assessmentresponses:List[AssessmentResponse])->str:"""BuildapromptforAIanalysisofanassessmentArgs:assessment:Theassessmenttoanalyzeresponses:Thestudent'sresponsestotheassessmentquestionsReturns:ApromptstringfortheAIservice"""#Getstudentinfostudent=assessment.studentstudent_level=StudentLevel.objects.get_or_create(student=studentdefaults={"current_level":1"current_level_display":"Beginner"})[0]#Formatresponsesforthepromptresponse_details=[]forresponseinresponses:question=response.questionresponse_details.append({"question_id":question.id"question_text":question.text"question_type":question.question_type"category":question.category"difficulty_level":question.difficulty_level"student_answer":response.answer_text"correct_answer":question.correct_answer"is_correct":response.is_correct"points_earned":response.points_earned"points_possible":question.points})#Buildtheprompt#Getfinallevelsafelyfinal_level=getattr(assessment"final_level"None)final_level_text=(str(final_level)iffinal_levelisnotNoneelse"Notdetermined")prompt=f"""Analyzethefollowingstudentassessmentresultsandprovidedetailedinsights:STUDENTINFORMATION:-StudentID:{student.id}-CurrentLevel:{student_level.current_level}({student_level.current_level_display})ASSESSMENTINFORMATION:-AssessmentID:{assessment.id}-AssessmentType:{assessment.assessment_type}-Score:{assessment.score}-InitialLevel:{assessment.initial_level}-FinalLevel:{final_level_text}RESPONSEDETAILS:{json.dumps(response_detailsindent=2)}PleaseprovideacomprehensiveanalysiswiththefollowinginformationinJSONformat:1.Overallperformanceassessment2.Identifiedstrengths(specificskillsorknowledgeareas)3.Identifiedweaknesses(specificskillsorknowledgeareas)4.Recommendedlevel(1-5)withjustification5.Specificcourserecommendationsbasedonperformance6.Learningpathsuggestions7.Confidencescoreforthisanalysis(0.0-1.0)FormatyourresponseasavalidJSONobjectwiththesefields:-overall_analysis:stringwithoverallassessment-strengths:arrayofstringsidentifyingstrengths-weaknesses:arrayofstringsidentifyingweaknesses-recommended_level:integer(1-5)-level_justification:stringexplainingthelevelrecommendation-course_recommendations:arrayofstringswithcoursesuggestions-learning_path:arrayofstringswithlearningpathsteps-confidence_score:floatbetween0.0and1.0IMPORTANT:ReturnONLYavalidJSONobjectwithnoadditionaltext."""returnpromptdef_create_ai_decision(selfassessment:Assessmentanalysis_data:Dict)->None:"""CreateanAIassessmentdecisionrecordArgs:assessment:Theassessmentbeinganalyzedanalysis_data:TheAIanalysisresultsReturns:None"""try:#Extracttherecommendedlevelfromtheanalysisrecommended_level=analysis_data.get("recommended_level"assessment.initial_level)#Ensurerecommended_levelisanintegerbetween1-5try:recommended_level=int(recommended_level)ifrecommended_level<1orrecommended_level>5:logger.warning(f"Invalidrecommendedlevel{recommended_level}defaultingtoinitiallevel{assessment.initial_level}")recommended_level=assessment.initial_levelexcept(ValueErrorTypeError):logger.warning(f"Non-integerrecommendedlevel{recommended_level}defaultingtoinitiallevel{assessment.initial_level}")recommended_level=assessment.initial_level#CreatetheAIdecisionrecordAIAssessmentController.objects.create(decision_type="LEVEL_CHANGE"student=assessment.studentassessment=assessmentsuggestion={"recommended_level":recommended_level"justification":analysis_data.get("level_justification""")"strengths":analysis_data.get("strengths"[])"weaknesses":analysis_data.get("weaknesses"[])"course_recommendations":analysis_data.get("course_recommendations"[])"learning_path":analysis_data.get("learning_path"[])}confidence_score=analysis_data.get("confidence_score"0.7)requires_review=Trueoriginal_decision={"initial_level":assessment.initial_level"final_level":(assessment.final_levelifhasattr(assessment"final_level")elseassessment.initial_level)"score":assessment.score})logger.info(f"CreatedAIdecisionrecordforassessment{assessment.id}")exceptExceptionase:logger.error(f"ErrorcreatingAIdecisionrecord:{str(e)}")defgenerate_skill_gap_analysis(selfstudent_id:int)->Dict:"""GenerateaskillgapanalysisforastudentArgs:student_id:TheIDofthestudenttoanalyzeReturns:Adictionarycontainingskillgapanalysisresults"""try:#Getthestudent'sassessmentsassessments=Assessment.objects.filter(student_id=student_idcompleted=True).order_by("-created_at")ifnotassessments.exists():return{"error":"Nocompletedassessmentsfoundforthisstudent"}#Getthestudent'scurrentlevelstudent_level=StudentLevel.objects.get_or_create(student_id=student_iddefaults={"current_level":1"current_level_display":"Beginner"})[0]#Getthestudent'sresponsesacrossallassessmentsresponses=AssessmentResponse.objects.filter(assessment__in=assessments).select_related("question""assessment")#BuildthepromptforAIanalysisprompt=self._build_skill_gap_analysis_prompt(student_levelassessmentsresponses)#GetAIanalysisanalysis_result=self.ai_service.generate_structured_text(promptoutput_format="json")#Parsetheresulttry:#FirstcheckiftheresultisalreadyanerrorresponsefromtheAIservicetry:error_check=json.loads(analysis_result)if"error"inerror_check:logger.error(f"AIservicereturnedanerror:{error_check['error']}")return{"error":error_check.get("error""Failedtogeneratestructuredcontent")"message":error_check.get("message""Unknownerror")"raw_response":analysis_result[:500]}exceptjson.JSONDecodeError:#NotavalidJSONerrorresponsecontinuewithnormalprocessingpass#Cleanuptheresponsetohandlepotentialformattingissuescleaned_result=analysis_result#Handlemarkdowncodeblockswithjsonif"```json"incleaned_result:cleaned_result=(cleaned_result.split("```json")[1].split("```")[0].strip())#Handlemarkdowncodeblockswithoutlanguagespecificationelif"```"incleaned_result:cleaned_result=(cleaned_result.split("```")[1].split("```")[0].strip())#TrytofindJSONobjectinthetextjson_match=re.search(r"\{[\s\S]*\}"cleaned_result)ifjson_match:cleaned_result=json_match.group(0)else:#Ifwecan'tfindvalidJSONmarkersraiseanerrorraisejson.JSONDecodeError("NovalidJSONobjectfound"cleaned_result0)try:analysis_data=json.loads(cleaned_result)exceptjson.JSONDecodeErrorasjson_err:logger.error(f"JSONdecodeerror:{str(json_err)}.AttemptingtofixmalformedJSON.")#TrytofixcommonJSONformattingissuesfixed_json=cleaned_result.replace("'\n""\n")fixed_json=fixed_json.replace("'"'"')analysis_data=json.loads(fixed_json)#Updatethestudent'sskillstrengthsandweaknessesstrengths=analysis_data.get("strengths"{})weaknesses=analysis_data.get("weaknesses"{})#Ensurestrengthsandweaknessesaredictionariesifnotisinstance(strengthsdict):ifisinstance(strengthslist):#Convertlisttodictionarywithdefaultvaluesstrengths_dict={}foriiteminenumerate(strengths):ifisinstance(itemstr):strengths_dict[item]=80#Defaultproficiencylevelstrengths=strengths_dictelse:strengths={}ifnotisinstance(weaknessesdict):ifisinstance(weaknesseslist):#Convertlisttodictionarywithdefaultvaluesweaknesses_dict={}foriiteminenumerate(weaknesses):ifisinstance(itemstr):weaknesses_dict[item]=30#Defaultproficiencylevelweaknesses=weaknesses_dictelse:weaknesses={}#Validatethestructureoftheanalysisdatarequired_fields=["strengths""weaknesses""focus_areas"]forfieldinrequired_fields:iffieldnotinanalysis_data:logger.error(f"Missingrequiredfield'{field}'inskillgapanalysisdata")return{"error":f"Missingrequiredfield'{field}'inskillgapanalysisdata""raw_response":analysis_result[:500]}student_level.skill_strengths=strengthsstudent_level.skill_weaknesses=weaknessesstudent_level.save(update_fields=["skill_strengths""skill_weaknesses"])returnanalysis_dataexceptjson.JSONDecodeErrorase:logger.error(f"Errorparsingskillgapanalysisresult:{str(e)}")return{"error":"Failedtoparseskillgapanalysis""raw_response":analysis_result[:500]#Includepartoftherawresponsefordebugging}exceptExceptionase:logger.error(f"Unexpectederrorinskillgapanalysis:{str(e)}")return{"error":"Unexpectederrorinskillgapanalysis""message":str(e)"raw_response":analysis_result[:500]}exceptExceptionase:logger.error(f"Errorgeneratingskillgapanalysis:{str(e)}")return{"error":f"Errorgeneratingskillgapanalysis:{str(e)}"}def_build_skill_gap_analysis_prompt(selfstudent_level:StudentLevelassessments:List[Assessment]responses:List[AssessmentResponse])->str:"""BuildapromptforAIskillgapanalysisArgs:student_level:Thestudent'slevelprofileassessments:Thestudent'sassessmentsresponses:Thestudent'sresponsestoassessmentquestionsReturns:ApromptstringfortheAIservice"""#Formatassessmentdataforthepromptassessment_data=[]forassessmentinassessments[:5]:#Limittothe5mostrecentassessmentsassessment_data.append({"assessment_id":assessment.id"assessment_type":assessment.assessment_type"score":assessment.score"created_at":assessment.created_at.isoformat()})#Formatresponsedataforthepromptresponse_data=[]forresponseinresponses[:50]:#Limitto50responsestoavoidtokenlimitsresponse_data.append({"question_category":response.question.category"question_difficulty":response.question.difficulty_level"is_correct":response.is_correct"points_earned":response.points_earned"points_possible":response.question.points})#Buildthepromptprompt=f"""Performacomprehensiveskillgapanalysisforthefollowingstudent:STUDENTINFORMATION:-CurrentLevel:{student_level.current_level}({student_level.current_level_display})ASSESSMENTHISTORY:{json.dumps(assessment_dataindent=2)}RESPONSEDATA:{json.dumps(response_dataindent=2)}PleaseprovideadetailedskillgapanalysiswiththefollowinginformationinJSONformat:1.Strengths:Skillsandknowledgeareaswherethestudentexcels2.Weaknesses:Skillsandknowledgeareaswherethestudentneedsimprovement3.Recommendedfocusareasforimprovement4.Suggestedlearningresourcesandactivities5.EstimatedtimetoreachthenextlevelFormatyourresponseasavalidJSONobjectwiththesefields:-strengths:objectwithskillcategoriesaskeysandproficiencylevels(0-100)asvalues-weaknesses:objectwithskillcategoriesaskeysandproficiencylevels(0-100)asvalues-focus_areas:arrayofstringswithrecommendedfocusareas-learning_resources:arrayofobjectswith"type""description"and"benefit"fields-estimated_time_to_next_level:stringdescribingestimatedtime-personalized_advice:stringwithpersonalizedadviceforthestudentIMPORTANT:ReturnONLYavalidJSONobjectwithnoadditionaltext."""returnpromptdefgenerate_course_recommendations(selfstudent_id:int)->Dict:"""GeneratepersonalizedcourserecommendationsforastudentArgs:student_id:TheIDofthestudenttogeneraterecommendationsforReturns:Adictionarycontainingcourserecommendations"""try:#Getthestudent'slevelstudent_level=StudentLevel.objects.get_or_create(student_id=student_iddefaults={"current_level":1"current_level_display":"Beginner"})[0]#Getavailablecourses#Usestringreferencetoavoidcircularimportsfrom django.appsimportappsCourse=apps.get_model("courses""Course")available_courses=Course.objects.all()[:20]#Limitto20coursestoavoidtokenlimits#Formatcoursedataforthepromptcourse_data=[]forcourseinavailable_courses:course_data.append({"course_id":course.id"title":course.title"description":course.description"level":course.levelifhasattr(course"level")else1"category":(course.categoryifhasattr(course"category")else"General")})#BuildthepromptforAIrecommendationsprompt=f"""Generatepersonalizedcourserecommendationsforthefollowingstudent:STUDENTINFORMATION:-CurrentLevel:{student_level.current_level}({student_level.current_level_display})-Strengths:{json.dumps(student_level.skill_strengths)}-Weaknesses:{json.dumps(student_level.skill_weaknesses)}AVAILABLECOURSES:{json.dumps(course_dataindent=2)}PleaseprovidepersonalizedcourserecommendationswiththefollowinginformationinJSONformat:1.Toprecommendedcourseswithjustification2.Learningpathsuggestion(sequenceofcourses)3.ExpectedoutcomesfromfollowingtheserecommendationsFormatyourresponseasavalidJSONobjectwiththesefields:-recommended_courses:arrayofobjectswith"course_id""title"and"justification"fields-learning_path:arrayofobjectswith"step""course_id""title"and"goal"fields-expected_outcomes:arrayofstringsdescribingexpectedoutcomes-personalized_message:stringwithapersonalizedmessageforthestudentIMPORTANT:ReturnONLYavalidJSONobjectwithnoadditionaltext."""#GetAIrecommendationsrecommendations_result=self.ai_service.generate_structured_text(promptoutput_format="json")#Parsetheresulttry:#FirstcheckiftheresultisalreadyanerrorresponsefromtheAIservicetry:error_check=json.loads(recommendations_result)if"error"inerror_check:logger.error(f"AIservicereturnedanerror:{error_check['error']}")return{"error":error_check.get("error""Failedtogeneratestructuredcontent")"message":error_check.get("message""Unknownerror")"raw_response":recommendations_result[:500]}exceptjson.JSONDecodeError:#NotavalidJSONerrorresponsecontinuewithnormalprocessingpass#Cleanuptheresponsetohandlepotentialformattingissuescleaned_result=recommendations_result#Handlemarkdowncodeblockswithjsonif"```json"incleaned_result:cleaned_result=(cleaned_result.split("```json")[1].split("```")[0].strip())#Handlemarkdowncodeblockswithoutlanguagespecificationelif"```"incleaned_result:cleaned_result=(cleaned_result.split("```")[1].split("```")[0].strip())#TrytofindJSONobjectinthetextimport rejson_match=re.search(r"\{[\s\S]*\}"cleaned_result)ifjson_match:cleaned_result=json_match.group(0)else:#Ifwecan'tfindvalidJSONmarkersraiseanerrorraisejson.JSONDecodeError("NovalidJSONobjectfound"cleaned_result0)try:recommendations_data=json.loads(cleaned_result)exceptjson.JSONDecodeErrorasjson_err:logger.error(f"JSONdecodeerror:{str(json_err)}.AttemptingtofixmalformedJSON.")#TrytofixcommonJSONformattingissuesfixed_json=cleaned_result.replace("'\n""\n")fixed_json=fixed_json.replace("'"'"')recommendations_data=json.loads(fixed_json)#Validatethestructureoftherecommendationsdatarequired_fields=["recommended_courses""learning_path""expected_outcomes"]forfieldinrequired_fields:iffieldnotinrecommendations_data:logger.error(f"Missingrequiredfield'{field}'inrecommendationsdata")return{"error":f"Missingrequiredfield'{field}'inrecommendationsdata""raw_response":recommendations_result[:500]}#Updatethestudent'srecommendedcoursesrecommended_course_ids=[rec.get("course_id")forrecinrecommendations_data.get("recommended_courses"[])ifrec.get("course_id")]#Clearexistingrecommendationsandaddnewonesstudent_level.recommended_courses.clear()forcourse_idinrecommended_course_ids:try:#Usestringreferencetoavoidcircularimportsfrom django.appsimportappsCourse=apps.get_model("courses""Course")course=Course.objects.get(id=course_id)student_level.recommended_courses.add(course)exceptExceptionascourse_err:logger.error(f"Erroraddingcourse{course_id}:{str(course_err)}")continuereturnrecommendations_dataexceptjson.JSONDecodeErrorase:logger.error(f"Errorparsingcourserecommendationsresult:{str(e)}")return{"error":"Failedtoparsecourserecommendations""raw_response":recommendations_result[:500]#Includepartoftherawresponsefordebugging}exceptExceptionase:logger.error(f"Unexpectederrorincourserecommendations:{str(e)}")return{"error":"Unexpectederrorincourserecommendations""message":str(e)"raw_response":recommendations_result[:500]}exceptExceptionase:logger.error(f"Errorgeneratingcourserecommendations:{str(e)}")return{"error":f"Errorgeneratingcourserecommendations:{str(e)}"}#Createasingletoninstanceai_assessment_analysis=AIAssessmentAnalysis()
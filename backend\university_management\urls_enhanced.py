from django.contrib import admin
from django.urls import path, include
from django.http import JsonResponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

def api_status(request):
    return JsonResponse({
        "status": "ok", 
        "message": "University Management System API is running",
        "features": ["authentication", "user_management"]
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def student_level(request):
    """Temporary endpoint for student level - returns mock data"""
    return Response({
        "status": "success",
        "data": {
            "level": 1,
            "experience": 0,
            "next_level_exp": 100,
            "progress_percentage": 0
        },
        "message": "Student level retrieved successfully"
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def auth_me(request):
    """Get current user profile - alternative endpoint"""
    user = request.user
    return Response({
        "status": "success",
        "data": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "role": getattr(user, 'role', 'STUDENT'),
            "first_name": user.first_name,
            "last_name": user.last_name
        },
        "message": "User profile retrieved successfully"
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def professor_courses(request):
    """Mock endpoint for professor courses"""
    return Response({
        "status": "success",
        "data": [],
        "message": "Professor courses retrieved successfully"
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def courses_v1(request):
    """Mock endpoint for courses v1 API"""
    return Response({
        "status": "success",
        "data": [],
        "message": "Courses retrieved successfully"
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def attendance_stats(request):
    """Mock endpoint for attendance statistics"""
    return Response({
        "status": "success",
        "data": {
            "total_classes": 0,
            "attended": 0,
            "percentage": 0
        },
        "message": "Attendance stats retrieved successfully"
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def professor_notifications(request):
    """Mock endpoint for professor notifications"""
    return Response({
        "status": "success",
        "data": [],
        "message": "Notifications retrieved successfully"
    })

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def chat_conversations(request):
    """Mock endpoint for AI chat conversations"""
    if request.method == 'GET':
        return Response({
            "status": "success",
            "data": [],
            "message": "Chat conversations retrieved successfully"
        })
    elif request.method == 'POST':
        return Response({
            "status": "success",
            "data": {
                "id": 1,
                "title": request.data.get('title', 'New Conversation'),
                "created_at": "2025-07-02T18:22:00Z",
                "updated_at": "2025-07-02T18:22:00Z",
                "messages": []
            },
            "message": "Conversation created successfully"
        })

@api_view(['GET', 'POST', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def office_hours(request):
    """Mock endpoint for office hours management"""
    if request.method == 'GET':
        return Response({
            "status": "success",
            "data": [],
            "message": "Office hours retrieved successfully"
        })
    elif request.method == 'POST':
        return Response({
            "status": "success",
            "data": {
                "id": 1,
                "day": request.data.get('day', 'Monday'),
                "start_time": request.data.get('start_time', '09:00'),
                "end_time": request.data.get('end_time', '10:00'),
                "location": request.data.get('location', 'Office 101')
            },
            "message": "Office hours created successfully"
        })
    elif request.method == 'PUT':
        return Response({
            "status": "success",
            "data": {
                "id": 1,
                "day": request.data.get('day', 'Monday'),
                "start_time": request.data.get('start_time', '09:00'),
                "end_time": request.data.get('end_time', '10:00'),
                "location": request.data.get('location', 'Office 101')
            },
            "message": "Office hours updated successfully"
        })
    elif request.method == 'DELETE':
        return Response({
            "status": "success",
            "message": "Office hours deleted successfully"
        })

urlpatterns = [
    path("admin/", admin.site.urls),
    path("api/status/", api_status, name="api-status"),
    
    # Authentication endpoints
    path("api/auth/", include("auth_api.urls")),
    path("api/auth/me/", auth_me, name="auth-me"),
    
    # User management endpoints  
    path("api/users/", include("users.urls")),
    
    # Assessment endpoints (mock)
    path("api/assessment/student/level/", student_level, name="student-level"),
    
    # Grades endpoints (mock)
    path("api/grades/course-grades/professor_courses/", professor_courses, name="professor-courses"),
    
    # Courses endpoints (mock) - using standard API structure
    path("api/courses/", courses_v1, name="courses-standard"),
    path("api/courses/professor/attendance/stats/", attendance_stats, name="attendance-stats"),
    
    # Notifications endpoints (mock)
    path("api/notifications/professor/notifications/", professor_notifications, name="professor-notifications"),
    
    # AI Chat endpoints (mock)
    path("api/utils/ai/chat/conversations/", chat_conversations, name="chat-conversations"),
    # Office hours endpoint
    path("api/courses/office-hours/", office_hours, name="office-hours"),
]

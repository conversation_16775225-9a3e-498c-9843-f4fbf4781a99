"""DjangoManagementCommand:FixCircularImportDependenciesThiscommandidentifiesandfixescircularimportdependenciesbetweenDjangoappsbyimplementingproperimportpatternsanddependencyinjection."""importastimport osimport refromcollectionsimportdefaultdictdequefrompathlibimportPathfrom django.core.management.baseimportBaseCommandclassCommand(BaseCommand):help="FixcircularimportdependenciesbetweenDjangoapps"defadd_arguments(selfparser):parser.add_argument("--dry-run"action="store_true"help="Showwhatwouldbechangedwithoutmakingchanges")parser.add_argument("--analyze-only"action="store_true"help="Onlyanalyzeandreportcirculardependencies")defhandle(self*args**options):"""Maincommandhandler"""self.stdout.write(self.style.SUCCESS("🔄FixingCircularImportDependencies"))dry_run=options["dry_run"]analyze_only=options["analyze_only"]#Analyzecurrentdependenciesdependencies=self._analyze_dependencies()circular_deps=self._find_circular_dependencies(dependencies)ifcircular_deps:self.stdout.write(self.style.WARNING(f"⚠️Found{len(circular_deps)}circulardependencies"))self._report_circular_dependencies(circular_deps)ifnotanalyze_only:self._fix_circular_dependencies(circular_depsdry_run)else:self.stdout.write(self.style.SUCCESS("✅Nocirculardependenciesfound!"))def_analyze_dependencies(self):"""Analyzeimportdependenciesbetweenapps"""self.stdout.write("🔍Analyzingimportdependencies...")dependencies=defaultdict(set)#Djangoappstoanalyzeapps=["users""courses""grades""assessment""auth_api""chatbot""course_generator""study_assistant""ai_assistant""notifications""utils"]forappinapps:app_path=Path(app)ifapp_path.exists():app_deps=self._get_app_dependencies(app_path)dependencies[app]=app_depsreturndependenciesdef_get_app_dependencies(selfapp_path):"""Getdependenciesforaspecificapp"""dependencies=set()#FindallPythonfilesintheapppython_files=list(app_path.rglob("*.py"))forfile_pathinpython_files:try:withopen(file_path"r"encoding="utf-8")asf:content=f.read()#Parseimportsfile_deps=self._parse_imports(content)dependencies.update(file_deps)exceptExceptionase:self.stdout.write(self.style.WARNING(f"⚠️Couldnotparse{file_path}:{e}"))returndependenciesdef_parse_imports(selfcontent):"""Parseimportstatementstofindappdependencies"""dependencies=set()#Patternstomatchappimportsimport_patterns=[r"from\s+(users|courses|grades|assessment|auth_api|chatbot|course_generator|interactive_learning|study_assistant|ai_assistant|notifications|utils)\..*?import"r"import\s+(users|courses|grades|assessment|auth_api|chatbot|course_generator|interactive_learning|study_assistant|ai_assistant|notifications|utils)\."]forpatterninimport_patterns:matches=re.findall(patterncontent)dependencies.update(matches)returndependenciesdef_find_circular_dependencies(selfdependencies):"""FindcirculardependenciesusingDFS"""circular_deps=[]defdfs(nodepathvisited):ifnodeinpath:#Foundacyclecycle_start=path.index(node)cycle=path[cycle_start:]+[node]circular_deps.append(cycle)returnifnodeinvisited:returnvisited.add(node)path.append(node)forneighborindependencies.get(node[]):dfs(neighborpathvisited)path.pop()visited=set()forappindependencies:ifappnotinvisited:dfs(app[]visited)returncircular_depsdef_report_circular_dependencies(selfcircular_deps):"""Reportfoundcirculardependencies"""self.stdout.write("\n📊CircularDependenciesReport:")self.stdout.write("="*50)foricycleinenumerate(circular_deps1):cycle_str="→".join(cycle)self.stdout.write(f"{i}.{cycle_str}")def_fix_circular_dependencies(selfcircular_depsdry_run):"""Fixcirculardependenciesbyimplementingproperpatterns"""self.stdout.write("\n🔧Fixingcirculardependencies...")#Commonfixesforcirculardependenciesfixes=[self._fix_model_importsself._fix_serializer_importsself._fix_view_importsself._implement_lazy_importsself._create_shared_interfaces]forfix_functioninfixes:try:fix_function(dry_run)exceptExceptionase:self.stdout.write(self.style.ERROR(f"❌Errorin{fix_function.__name__}:{e}"))def_fix_model_imports(selfdry_run):"""Fixcircularimportsinmodelfiles"""self.stdout.write("🗃️Fixingmodelimports...")#Createacentralizedmodelregistrymodel_registry_content='''"""CentralizedModelRegistryThismoduleprovidesacentralizedwaytoaccessmodelsacrossappswithoutcreatingcircularimportdependencies."""from django.appsimportappsfromtypingimportOptionalTypefrom django.dbimportmodelsclassModelRegistry:"""Centralizedregistryforaccessingmodelsacrossapps"""@staticmethoddefget_user_model():"""GettheUsermodel"""from django.contrib.authimportget_user_modelreturnget_user_model()@staticmethoddefget_course_model():"""GettheCoursemodel"""try:returnapps.get_model('courses''Course')exceptLookupError:returnNone@staticmethoddefget_enrollment_model():"""GettheEnrollmentmodel"""try:returnapps.get_model('courses''Enrollment')exceptLookupError:returnNone@staticmethoddefget_course_grade_model():"""GettheCourseGrademodel"""try:returnapps.get_model('grades''CourseGrade')exceptLookupError:returnNone@staticmethoddefget_assessment_model():"""GettheAssessmentmodel"""try:returnapps.get_model('assessment''Assessment')exceptLookupError:returnNone@staticmethoddefget_model(app_label:strmodel_name:str)->Optional[Type[models.Model]]:"""Genericmethodtogetanymodel"""try:returnapps.get_model(app_labelmodel_name)exceptLookupError:returnNone#Createsingletoninstancemodel_registry=ModelRegistry()#Conveniencefunctionsget_user_model=model_registry.get_user_modelget_course_model=model_registry.get_course_modelget_enrollment_model=model_registry.get_enrollment_modelget_course_grade_model=model_registry.get_course_grade_modelget_assessment_model=model_registry.get_assessment_model'''ifdry_run:self.stdout.write("[DRYRUN]Wouldcreatecore/model_registry.py")else:#Createcoredirectoryifitdoesn'texistcore_dir=Path("core")core_dir.mkdir(exist_ok=True)#Writemodelregistryregistry_path=core_dir/"model_registry.py"withopen(registry_path"w"encoding="utf-8")asf:f.write(model_registry_content)self.stdout.write(f"✅Created:{registry_path}")def_fix_serializer_imports(selfdry_run):"""Fixcircularimportsinserializerfiles"""self.stdout.write("📝Fixingserializerimports...")#Examplefixforusers/serializers.pyusers_serializer_fix="""#BEFORE:Directimportcausingcirculardependency#from courses.modelsimportCourse#from grades.modelsimportCourseGrade#AFTER:Usemodelregistryfrom core.model_registryimportget_course_modelget_course_grade_modelclassUserSerializer(serializers.ModelSerializer):defget_courses(selfobj):Course=get_course_model()ifCourse:#Usethemodelsafelypassreturn[]"""ifdry_run:self.stdout.write("[DRYRUN]Wouldfixserializerimports")else:#Applyfixestospecificfilesself._apply_serializer_fixes()def_fix_view_imports(selfdry_run):"""Fixcircularimportsinviewfiles"""self.stdout.write("👁️Fixingviewimports...")ifdry_run:self.stdout.write("[DRYRUN]Wouldfixviewimports")else:#Applyfixestoviewfilesself._apply_view_fixes()def_implement_lazy_imports(selfdry_run):"""Implementlazyimportsforbreakingcirculardependencies"""self.stdout.write("⏰Implementinglazyimports...")lazy_import_utility='''"""LazyImportUtilityThismoduleprovidesutilitiesforlazyimportingtobreakcirculardependencies."""importimportlibfromtypingimportAnyCallableOptionalclassLazyImport:"""Lazyimportwrapperthatimportsmodulesonlywhenaccessed"""def__init__(selfmodule_name:strattribute_name:Optional[str]=None):self.module_name=module_nameself.attribute_name=attribute_nameself._cached_object=Nonedef__call__(self)->Any:"""Gettheimportedobject"""ifself._cached_objectisNone:try:module=importlib.import_module(self.module_name)ifself.attribute_name:self._cached_object=getattr(moduleself.attribute_name)else:self._cached_object=moduleexcept(ImportErrorAttributeError)ase:raiseImportError(f"Couldnotlazyimport{self.module_name}.{self.attribute_name}:{e}")returnself._cached_objectdeflazy_import(module_name:strattribute_name:Optional[str]=None)->LazyImport:"""Createalazyimport"""returnLazyImport(module_nameattribute_name)#Exampleusage:#CourseModel=lazy_import('courses.models''Course')#course_model=CourseModel()#Onlyimportswhencalled'''ifdry_run:self.stdout.write("[DRYRUN]Wouldcreatelazyimportutility")else:utils_dir=Path("utils")lazy_import_path=utils_dir/"lazy_imports.py"withopen(lazy_import_path"w"encoding="utf-8")asf:f.write(lazy_import_utility)self.stdout.write(f"✅Created:{lazy_import_path}")def_create_shared_interfaces(selfdry_run):"""Createsharedinterfacestoreducecoupling"""self.stdout.write("🔗Creatingsharedinterfaces...")interfaces_content='''"""SharedInterfacesThismoduledefinesinterfacesthatcanbeusedacrossappstoreducecouplingandcirculardependencies."""fromabcimportABCabstractmethodfromtypingimportAnyDictListOptionalclassCourseInterface(ABC):"""Interfaceforcourse-relatedoperations"""@abstractmethoddefget_course_by_id(selfcourse_id:int)->Optional[Any]:"""GetcoursebyID"""pass@abstractmethoddefget_user_courses(selfuser_id:int)->List[Any]:"""Getcoursesforauser"""passclassGradeInterface(ABC):"""Interfaceforgrade-relatedoperations"""@abstractmethoddefget_user_grades(selfuser_id:int)->List[Any]:"""Getgradesforauser"""pass@abstractmethoddefcalculate_gpa(selfuser_id:int)->float:"""CalculateGPAforauser"""passclassAssessmentInterface(ABC):"""Interfaceforassessment-relatedoperations"""@abstractmethoddefget_user_assessments(selfuser_id:int)->List[Any]:"""Getassessmentsforauser"""pass@abstractmethoddefsubmit_assessment(selfassessment_id:intuser_id:intanswers:Dict)->Any:"""Submitassessmentanswers"""pass'''ifdry_run:self.stdout.write("[DRYRUN]Wouldcreatesharedinterfaces")else:core_dir=Path("core")interfaces_path=core_dir/"interfaces.py"withopen(interfaces_path"w"encoding="utf-8")asf:f.write(interfaces_content)self.stdout.write(f"✅Created:{interfaces_path}")def_apply_serializer_fixes(self):"""Applyspecificfixestoserializerfiles"""#Fixusers/serializers.pyusers_serializer_path=Path("users/serializers.py")ifusers_serializer_path.exists():self._fix_file_imports(users_serializer_path[("from courses.modelsimportCourse""from core.model_registryimportget_course_model")("from grades.modelsimportCourseGrade""from core.model_registryimportget_course_grade_model")])def_apply_view_fixes(self):"""Applyspecificfixestoviewfiles"""#Fixassessment/views.pyassessment_views_path=Path("assessment/views.py")ifassessment_views_path.exists():self._fix_file_imports(assessment_views_path[("from courses.modelsimportCourse""from core.model_registryimportget_course_model")])def_fix_file_imports(selffile_pathreplacements):"""Applyimport replacementstoafile"""try:withopen(file_path"r"encoding="utf-8")asf:content=f.read()original_content=contentforold_importnew_importinreplacements:content=content.replace(old_importnew_import)ifcontent!=original_content:withopen(file_path"w"encoding="utf-8")asf:f.write(content)self.stdout.write(f"✅Fixedimportsin:{file_path}")exceptExceptionase:self.stdout.write(self.style.ERROR(f"❌Errorfixing{file_path}:{e}"))
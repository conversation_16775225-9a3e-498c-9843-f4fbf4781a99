# This file makes the serializers directory a Python package

# Import serializers from the new files
# Temporarily disabled to fix import issues
# try:
#     from .relationship_serializers import *
# except ImportError:
#     pass

# try:
#     from .section_serializers import *
# except ImportError:
#     pass

# try:
#     from .skill_serializers import *
# except ImportError:
#     pass

# try:
#     from .direct_serializers import *
# except ImportError:
#     pass

# from .course_serializers import *  # Commented out to avoid circular imports

# try:
#     from .content_preferences_serializers import *
# except ImportError:
#     pass

# OfficeHoursSerializer is defined directly in office_hours_views.py to avoid circular imports

# Define a basic CourseSerializer to prevent import errors
from rest_framework import serializers

class CourseSerializer(serializers.Serializer):
    """Basic course serializer to prevent import errors"""
    id = serializers.IntegerField(read_only=True)
    title = serializers.Char<PERSON><PERSON>(max_length=200)
    course_code = serializers.CharField(max_length=10)
    description = serializers.Char<PERSON>ield(required=False)
# Generated by Django 4.2.7 on 2025-07-04 15:54

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Assessment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.Char<PERSON>ield(blank=True, max_length=255)),
                ("description", models.TextField(blank=True)),
                (
                    "assessment_type",
                    models.CharField(
                        choices=[
                            ("PLACEMENT", "Placement Assessment"),
                            ("COURSE", "Course Assessment"),
                            ("MILESTONE", "Milestone Assessment"),
                            ("QUIZ", "Quiz"),
                            ("EXAM", "Exam"),
                            ("PRACTICE", "Practice"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("IN_PROGRESS", "In Progress"),
                            ("COMPLETED", "Completed"),
                            ("EXPIRED", "Expired"),
                        ],
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                ("score", models.FloatField(blank=True, null=True)),
                ("start_time", models.DateTimeField(blank=True, null=True)),
                ("end_time", models.DateTimeField(blank=True, null=True)),
                ("completed", models.BooleanField(default=False)),
                (
                    "skill_scores",
                    models.JSONField(
                        default=dict, help_text="Scores by skill category"
                    ),
                ),
                (
                    "feedback",
                    models.JSONField(default=dict, help_text="Structured feedback"),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "is_adaptive",
                    models.BooleanField(
                        default=True, help_text="Whether this is an adaptive assessment"
                    ),
                ),
                (
                    "adaptive_progression",
                    models.JSONField(
                        default=dict,
                        help_text="Tracks the progression of difficulty during adaptive assessment",
                    ),
                ),
                (
                    "current_difficulty_level",
                    models.PositiveSmallIntegerField(
                        default=1,
                        help_text="Current difficulty level in adaptive assessment",
                    ),
                ),
                (
                    "learning_path",
                    models.CharField(
                        choices=[
                            ("programming", "Programming"),
                            ("cybersecurity", "Cybersecurity"),
                            ("finance", "Finance"),
                            ("marketing", "Marketing"),
                            ("general", "General"),
                        ],
                        default="general",
                        help_text="The learning path this assessment is for",
                        max_length=50,
                    ),
                ),
                (
                    "detailed_results",
                    models.JSONField(
                        default=dict, help_text="Detailed assessment results"
                    ),
                ),
                (
                    "initial_level",
                    models.IntegerField(
                        blank=True,
                        help_text="Student's level before assessment",
                        null=True,
                    ),
                ),
                (
                    "final_level",
                    models.IntegerField(
                        blank=True,
                        help_text="Student's level after assessment",
                        null=True,
                    ),
                ),
                (
                    "level_changed",
                    models.BooleanField(
                        default=False, help_text="Whether the student's level changed"
                    ),
                ),
                (
                    "time_spent",
                    models.IntegerField(
                        blank=True, help_text="Time spent in seconds", null=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="AssessmentQuestion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("question_text", models.TextField(verbose_name="Question Text")),
                (
                    "question_type",
                    models.CharField(
                        choices=[
                            ("MULTIPLE_CHOICE", "Multiple Choice"),
                            ("TRUE_FALSE", "True/False"),
                            ("MATCHING", "Matching"),
                            ("SHORT_ANSWER", "Short Answer"),
                            ("ESSAY", "Essay"),
                            ("FILL_IN_BLANK", "Fill in the Blank"),
                            ("CODING", "Coding Question"),
                            ("ORDERING", "Ordering/Sequencing"),
                            ("FLASHCARD", "Flashcard"),
                            ("DICTATION", "Dictation"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "options",
                    models.JSONField(
                        default=list, help_text="Answer options or content elements"
                    ),
                ),
                ("correct_answer", models.JSONField(help_text="Correct answer data")),
                (
                    "explanation",
                    models.TextField(
                        blank=True, help_text="Explanation of the correct answer"
                    ),
                ),
                (
                    "difficulty_level",
                    models.IntegerField(
                        default=1, help_text="Difficulty level of the question (1-5)"
                    ),
                ),
                (
                    "points",
                    models.PositiveIntegerField(
                        default=1, help_text="Points awarded for correct answer"
                    ),
                ),
                (
                    "ai_generated",
                    models.BooleanField(
                        default=False,
                        help_text="Flag to indicate if this was generated by AI",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("programming", "Programming"),
                            ("cybersecurity", "Cybersecurity"),
                            ("finance", "Finance"),
                            ("marketing", "Marketing"),
                            ("general", "General"),
                        ],
                        default="general",
                        max_length=50,
                    ),
                ),
                (
                    "learning_path",
                    models.CharField(
                        choices=[
                            ("programming", "Programming"),
                            ("cybersecurity", "Cybersecurity"),
                            ("finance", "Finance"),
                            ("marketing", "Marketing"),
                            ("general", "General"),
                        ],
                        default="general",
                        help_text="The learning path this question belongs to",
                        max_length=50,
                    ),
                ),
                (
                    "is_public",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this question can be shown to students",
                    ),
                ),
                (
                    "is_placement",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this question is for placement assessments",
                    ),
                ),
                (
                    "ai_reviewed",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this question was reviewed by AI",
                    ),
                ),
                ("ai_suggested", models.BooleanField(default=False)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="AssessmentResponse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "answer_text",
                    models.TextField(
                        blank=True,
                        help_text="Text answer provided by student",
                        null=True,
                    ),
                ),
                ("is_correct", models.BooleanField(null=True)),
                ("points_earned", models.FloatField(default=0)),
                (
                    "started_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When student started this question",
                        null=True,
                    ),
                ),
                (
                    "submitted_at",
                    models.DateTimeField(
                        blank=True, help_text="When student submitted answer", null=True
                    ),
                ),
                (
                    "time_spent",
                    models.IntegerField(
                        blank=True, help_text="Time spent in seconds", null=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "student_answer",
                    models.JSONField(
                        blank=True, help_text="JSON format answer (legacy)", null=True
                    ),
                ),
                (
                    "answer",
                    models.TextField(
                        blank=True, help_text="Text answer (legacy)", null=True
                    ),
                ),
                ("feedback", models.TextField(blank=True)),
            ],
            options={
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="AssessmentSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "assessment_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("PLACEMENT", "Placement Assessment"),
                            ("COURSE", "Course Assessment"),
                            ("MILESTONE", "Milestone Assessment"),
                            ("QUIZ", "Quiz"),
                            ("EXAM", "Exam"),
                            ("PRACTICE", "Practice"),
                        ],
                        help_text="Assessment type these settings apply to. Leave blank for global settings.",
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "max_attempts",
                    models.PositiveIntegerField(
                        default=3, help_text="Maximum number of attempts allowed"
                    ),
                ),
                (
                    "time_limit",
                    models.PositiveIntegerField(
                        default=60, help_text="Time limit in minutes"
                    ),
                ),
                (
                    "passing_score",
                    models.FloatField(
                        default=70.0,
                        help_text="Minimum score required to pass (percentage)",
                    ),
                ),
                (
                    "questions_per_assessment",
                    models.PositiveIntegerField(
                        default=10, help_text="Number of questions per assessment"
                    ),
                ),
                (
                    "allow_retakes",
                    models.BooleanField(
                        default=True,
                        help_text="Whether students can retake the assessment",
                    ),
                ),
                (
                    "retake_cooldown_days",
                    models.PositiveIntegerField(
                        default=1, help_text="Days to wait before allowing retake"
                    ),
                ),
                (
                    "adaptive_difficulty",
                    models.BooleanField(
                        default=True, help_text="Enable adaptive difficulty adjustment"
                    ),
                ),
                (
                    "difficulty_adjustment_threshold",
                    models.FloatField(
                        default=0.7,
                        help_text="Threshold for adjusting difficulty (0.0-1.0)",
                    ),
                ),
                (
                    "randomize_questions",
                    models.BooleanField(
                        default=True, help_text="Randomize question order"
                    ),
                ),
                (
                    "randomize_options",
                    models.BooleanField(
                        default=True, help_text="Randomize answer options"
                    ),
                ),
                (
                    "prevent_backtracking",
                    models.BooleanField(
                        default=False,
                        help_text="Prevent students from going back to previous questions",
                    ),
                ),
                (
                    "show_correct_answers",
                    models.BooleanField(
                        default=True, help_text="Show correct answers after completion"
                    ),
                ),
                (
                    "show_explanations",
                    models.BooleanField(
                        default=True, help_text="Show explanations for answers"
                    ),
                ),
                (
                    "immediate_feedback",
                    models.BooleanField(
                        default=False,
                        help_text="Show feedback immediately after each question",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Assessment Settings",
                "verbose_name_plural": "Assessment Settings",
                "ordering": ["assessment_type"],
            },
        ),
        migrations.CreateModel(
            name="LevelRequirement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "level",
                    models.IntegerField(
                        choices=[
                            (1, "Beginner"),
                            (2, "Elementary"),
                            (3, "Intermediate"),
                            (4, "Advanced"),
                            (5, "Expert"),
                        ],
                        unique=True,
                    ),
                ),
                ("min_assessment_score", models.FloatField(default=60.0)),
                ("required_skills", models.JSONField(default=list)),
                ("min_completed_courses", models.IntegerField(default=0)),
                ("description", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Level Requirement",
                "verbose_name_plural": "Level Requirements",
                "ordering": ["level"],
            },
        ),
        migrations.CreateModel(
            name="StudentLevel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "current_level",
                    models.IntegerField(
                        choices=[
                            (1, "Beginner"),
                            (2, "Elementary"),
                            (3, "Intermediate"),
                            (4, "Advanced"),
                            (5, "Expert"),
                        ],
                        default=1,
                    ),
                ),
                (
                    "current_level_display",
                    models.CharField(default="Beginner", max_length=50),
                ),
                ("last_assessment_date", models.DateTimeField(blank=True, null=True)),
                (
                    "skill_strengths",
                    models.JSONField(
                        default=dict, help_text="Areas where student shows strength"
                    ),
                ),
                (
                    "skill_weaknesses",
                    models.JSONField(
                        default=dict, help_text="Areas where student needs improvement"
                    ),
                ),
                (
                    "progression_history",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="History of level changes",
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
    ]

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface AppError {
  id: string;
  message: string;
  code?: string;
  timestamp: string;
  action?: string;
  component?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  stack?: string;
  metadata?: Record<string, any>;
}

interface ErrorState {
  errors: AppError[];
  lastError: AppError | null;
  errorCount: number;
  hasGlobalError: boolean;
  dismissedErrors: string[];
}

const initialState: ErrorState = {
  errors: [],
  lastError: null,
  errorCount: 0,
  hasGlobalError: false,
  dismissedErrors: [],
};

const errorStateSlice = createSlice({
  name: 'errorState',
  initialState,
  reducers: {
    setError: (state, action: PayloadAction<Omit<AppError, 'id' | 'timestamp'>>) => {
      const error: AppError = {
        ...action.payload,
        id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
      };

      state.errors.push(error);
      state.lastError = error;
      state.errorCount += 1;

      // Set global error flag for critical errors
      if (error.severity === 'critical') {
        state.hasGlobalError = true;
      }

      // Keep only last 50 errors to prevent memory issues
      if (state.errors.length > 50) {
        state.errors = state.errors.slice(-50);
      }
    },

    clearError: (state, action: PayloadAction<string>) => {
      const errorId = action.payload;
      state.errors = state.errors.filter(error => error.id !== errorId);
      state.dismissedErrors.push(errorId);

      // Update last error if the cleared error was the last one
      if (state.lastError?.id === errorId) {
        state.lastError = state.errors.length > 0 ? state.errors[state.errors.length - 1] : null;
      }

      // Check if we still have critical errors
      const hasCriticalErrors = state.errors.some(error => error.severity === 'critical');
      if (!hasCriticalErrors) {
        state.hasGlobalError = false;
      }
    },

    clearAllErrors: (state) => {
      state.dismissedErrors.push(...state.errors.map(error => error.id));
      state.errors = [];
      state.lastError = null;
      state.hasGlobalError = false;
    },

    clearErrorsByComponent: (state, action: PayloadAction<string>) => {
      const component = action.payload;
      const componentErrors = state.errors.filter(error => error.component === component);
      state.dismissedErrors.push(...componentErrors.map(error => error.id));
      state.errors = state.errors.filter(error => error.component !== component);

      // Update last error and global flag
      if (state.errors.length === 0) {
        state.lastError = null;
        state.hasGlobalError = false;
      } else {
        state.lastError = state.errors[state.errors.length - 1];
        const hasCriticalErrors = state.errors.some(error => error.severity === 'critical');
        state.hasGlobalError = hasCriticalErrors;
      }
    },

    clearErrorsBySeverity: (state, action: PayloadAction<AppError['severity']>) => {
      const severity = action.payload;
      const severityErrors = state.errors.filter(error => error.severity === severity);
      state.dismissedErrors.push(...severityErrors.map(error => error.id));
      state.errors = state.errors.filter(error => error.severity !== severity);

      // Update global flag if clearing critical errors
      if (severity === 'critical') {
        state.hasGlobalError = false;
      }

      // Update last error
      if (state.errors.length === 0) {
        state.lastError = null;
      } else {
        state.lastError = state.errors[state.errors.length - 1];
      }
    },

    markErrorAsDismissed: (state, action: PayloadAction<string>) => {
      const errorId = action.payload;
      if (!state.dismissedErrors.includes(errorId)) {
        state.dismissedErrors.push(errorId);
      }
    },

    resetErrorState: () => initialState,

    updateErrorMetadata: (state, action: PayloadAction<{ errorId: string; metadata: Record<string, any> }>) => {
      const { errorId, metadata } = action.payload;
      const errorIndex = state.errors.findIndex(error => error.id === errorId);
      if (errorIndex !== -1) {
        state.errors[errorIndex].metadata = {
          ...state.errors[errorIndex].metadata,
          ...metadata,
        };
      }
    },
  },
});

export const {
  setError,
  clearError,
  clearAllErrors,
  clearErrorsByComponent,
  clearErrorsBySeverity,
  markErrorAsDismissed,
  resetErrorState,
  updateErrorMetadata,
} = errorStateSlice.actions;

// Selectors
export const selectErrorState = (state: { errorState: ErrorState }) => state.errorState;
export const selectErrors = (state: { errorState: ErrorState }) => state.errorState.errors;
export const selectLastError = (state: { errorState: ErrorState }) => state.errorState.lastError;
export const selectErrorCount = (state: { errorState: ErrorState }) => state.errorState.errorCount;
export const selectHasGlobalError = (state: { errorState: ErrorState }) => state.errorState.hasGlobalError;

export const selectErrorsBySeverity = (severity: AppError['severity']) => 
  (state: { errorState: ErrorState }) => 
    state.errorState.errors.filter(error => error.severity === severity);

export const selectErrorsByComponent = (component: string) => 
  (state: { errorState: ErrorState }) => 
    state.errorState.errors.filter(error => error.component === component);

export const selectRecentErrors = (minutes: number = 5) => 
  (state: { errorState: ErrorState }) => {
    const cutoff = new Date(Date.now() - minutes * 60 * 1000).toISOString();
    return state.errorState.errors.filter(error => error.timestamp > cutoff);
  };

export const selectUndismissedErrors = (state: { errorState: ErrorState }) =>
  state.errorState.errors.filter(error => 
    !state.errorState.dismissedErrors.includes(error.id)
  );

export default errorStateSlice.reducer;

from django.test import TestCase, override_settings
from assessment.prevent_mock_data import (
    is_mock_question_text,
    prevent_mock_question_creation,
    prevent_mock_response_creation,
    sanitize_assessment_data
)


class PreventMockDataTests(TestCase):
    def test_is_mock_question_text(self):
        """Test the is_mock_question_text function"""
        # Test mock indicators
        self.assertTrue(is_mock_question_text("This is a sample question"))
        self.assertTrue(is_mock_question_text("Test question for assessment"))
        self.assertTrue(is_mock_question_text("Mock question about programming"))
        self.assertTrue(is_mock_question_text("Dummy question for testing"))
        self.assertTrue(is_mock_question_text("Example question about Python"))

        # Test "Sample question" prefix
        self.assertTrue(is_mock_question_text("Sample question 1"))

        # Test legitimate questions
        self.assertFalse(is_mock_question_text("What is the capital of France?"))
        self.assertFalse(is_mock_question_text("Explain the concept of inheritance in OOP"))
        self.assertFalse(is_mock_question_text("Calculate the area of a circle with radius 5"))

        # Test edge cases
        self.assertFalse(is_mock_question_text(""))
        self.assertFalse(is_mock_question_text(None))

    def test_prevent_mock_question_creation(self):
        """Test the prevent_mock_question_creation function"""
        # Test mock questions
        self.assertTrue(prevent_mock_question_creation({"question_text": "This is a sample question"}))
        self.assertTrue(prevent_mock_question_creation({"text": "Test question for assessment"}))

        # Test legitimate questions
        self.assertFalse(prevent_mock_question_creation({"question_text": "What is the capital of France?"}))
        self.assertFalse(prevent_mock_question_creation({"text": "Explain the concept of inheritance in OOP"}))

        # Test edge cases
        self.assertFalse(prevent_mock_question_creation({}))
        self.assertFalse(prevent_mock_question_creation({"question_text": ""}))
        self.assertFalse(prevent_mock_question_creation({"text": ""}))

    def test_prevent_mock_response_creation(self):
        """Test the prevent_mock_response_creation function"""
        # Test mock responses
        self.assertTrue(prevent_mock_response_creation({"feedback": "This is a sample feedback"}))
        self.assertTrue(prevent_mock_response_creation({"feedback": "Test feedback for assessment"}))

        # Test legitimate responses
        self.assertFalse(prevent_mock_response_creation({"feedback": "Good job! Your answer is correct."}))
        self.assertFalse(prevent_mock_response_creation({"feedback": "Incorrect. The correct answer is Paris."}))

        # Test edge cases
        self.assertFalse(prevent_mock_response_creation({}))
        self.assertFalse(prevent_mock_response_creation({"feedback": ""}))

    def test_sanitize_assessment_data(self):
        """Test the sanitize_assessment_data function"""
        # Test sanitizing title and description
        sanitized_data = sanitize_assessment_data({
            "title": "Sample Assessment for Programming",
            "description": "This is a test assessment for programming skills"
        })
        self.assertEqual(sanitized_data["title"], "Assessment Assessment for Programming")
        self.assertEqual(sanitized_data["description"], "This is a assessment assessment for programming skills")

        # Test legitimate data
        original_data = {
            "title": "Programming Skills Assessment",
            "description": "This assessment evaluates your programming skills"
        }
        sanitized_data = sanitize_assessment_data(original_data)
        self.assertEqual(sanitized_data, original_data)

        # Test edge cases
        self.assertEqual(sanitize_assessment_data({}), {})
        self.assertEqual(sanitize_assessment_data(None), {})

    @override_settings(DEBUG=True, ALLOW_MOCK_DATA_IN_DEV=True)
    def test_allow_mock_data_in_dev(self):
        """Test that mock data is allowed in development mode if configured"""
        # Mock data should be allowed in development mode
        self.assertFalse(prevent_mock_question_creation({"question_text": "This is a sample question"}))
        self.assertFalse(prevent_mock_response_creation({"feedback": "This is a sample feedback"}))
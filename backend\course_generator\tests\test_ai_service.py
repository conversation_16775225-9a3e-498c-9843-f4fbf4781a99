"""Tests for the course_generator AI service."""
import json
from unittest.mock import MagicMock, patch

from django.contrib.auth import get_user_model
from django.test import TestCase

from course_generator.components.course_generation_service import (
    CourseGenerationService
)
from course_generator.models import CourseGenerationRequest, GeneratedCourseContent

# Import models from the courses app
from courses.models import Course
from courses.models.department import Department
from utils.consolidated_ai_service import ConsolidatedAIError, get_ai_service

User = get_user_model()


class CourseGenerationServiceTestCase(TestCase):
    """Test case for the CourseGenerationService"""

    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.user = User.objects.create_user(
            username="testprofessor",
            email="<EMAIL>",
            password="password123",
            first_name="Test",
            last_name="Professor",
            role="PROFESSOR"
        )

        # Create a test department
        self.department = Department.objects.create(
            name="Test Department",
            code="TEST"
        )

        # Create a test course
        self.course = Course.objects.create(
            course_code="TEST101",
            title="Test Course",
            description="A test course for unit testing",
            department=self.department,
            professor=self.user,
            credits=3,
            capacity=30,
            required_level=1
        )

        # Create a test generation request
        self.request = CourseGenerationRequest.objects.create(
            course=self.course,
            keywords="test, unit testing, example",
            description_prompt="Create a test course",
            requested_by=self.user,
            status="PENDING"
        )

        # Initialize the service
        self.service = CourseGenerationService()

        # Sample AI response
        self.sample_ai_response = json.dumps({
            "weekly_schedule": [{
                "week": 1,
                "topic": "Introduction",
                "activities": ["Activity 1"]
            }],
            "lesson_plans": [{
                "title": "Lesson 1",
                "objectives": ["Objective 1"]
            }],
            "assessment_methods": [{
                "name": "Quiz 1",
                "weight": 10
            }],
            "recommended_readings": [{
                "title": "Book 1",
                "author": "Author 1"
            }],
            "sample_quizzes": [{
                "question": "Question 1",
                "options": ["A", "B", "C"],
                "answer": "A"
            }],
            "project_ideas": [{
                "title": "Project 1",
                "description": "Description 1"
            }],
            "teaching_tips": [{
                "title": "Tip 1",
                "description": "Description 1"
            }],
            "skills_gained": ["Skill 1", "Skill 2"]
        })

    @patch("utils.consolidated_ai_service.ConsolidatedAIService.generate_content")
    def test_generate_course_outline_success(self, mock_generate_content):
        """Test successful course outline generation"""
        # Mock the AI response
        mock_generate_content.return_value = self.sample_ai_response

        # Call the method
        result = self.service.generate_course_outline(self.request.id)

        # Check that the request status was updated
        self.request.refresh_from_db()
        self.assertEqual(self.request.status, "COMPLETED")

        # Check that generated content was created
        self.assertTrue(
            GeneratedCourseContent.objects.filter(request=self.request).exists()
        )

        # Check the result
        self.assertIn("weekly_schedule", result)
        self.assertIn("lesson_plans", result)
        self.assertIn("assessment_methods", result)
        self.assertIn("skills_gained", result)

    @patch("utils.consolidated_ai_service.ConsolidatedAIService.generate_content")
    def test_generate_course_outline_ai_error(self, mock_generate_content):
        """Test course outline generation with AI error"""
        # Mock an AI error
        mock_generate_content.side_effect = ConsolidatedAIError(
            "AI service unavailable"
        )

        # Call the method and expect an exception
        with self.assertRaises(ConsolidatedAIError):
            self.service.generate_course_outline(self.request.id)

        # Check that the request status was updated to FAILED
        self.request.refresh_from_db()
        self.assertEqual(self.request.status, "FAILED")
        self.assertIn("AI service error", self.request.error_message)

    def test_parse_json_response_valid_json(self):
        """Test parsing a valid JSON response"""
        # Call the method with valid JSON
        result = self.service.parse_json_response(self.sample_ai_response)

        # Check the result
        self.assertIsInstance(result, dict)
        self.assertIn("weekly_schedule", result)
        self.assertIn("skills_gained", result)

    def test_parse_json_response_invalid_json(self):
        """Test parsing an invalid JSON response"""
        # Call the method with invalid JSON
        result = self.service.parse_json_response(
            'This is not JSON but contains {"weekly_schedule": []}'
        )

        # Check that it extracted the JSON
        self.assertIsInstance(result, dict)
        self.assertIn("weekly_schedule", result)

    def test_ensure_required_fields(self):
        """Test ensuring required fields are present"""
        # Test with missing fields
        data = {"weekly_schedule": []}
        result = self.service._ensure_required_fields(data)

        # Check that all required fields are present
        self.assertIn("weekly_schedule", result)
        self.assertIn("lesson_plans", result)
        self.assertIn("assessment_methods", result)
        self.assertIn("recommended_readings", result)
        self.assertIn("sample_quizzes", result)
        self.assertIn("project_ideas", result)
        self.assertIn("teaching_tips", result)
        self.assertIn("skills_gained", result)

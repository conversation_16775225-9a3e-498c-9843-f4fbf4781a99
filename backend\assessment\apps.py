from django.apps import AppConfig


class AssessmentConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "assessment"
    
    def ready(self):
        """
        Initialize app components after Django is ready.
        This method is called by Django after all apps are loaded.
        """
        # Import signals and services only after apps are ready
        try:
            # Import signal handlers
            from . import signals
            
            # Initialize service lazily
            def get_assessment_service():
                from .services import UnifiedAssessmentService
                return UnifiedAssessmentService(None)
            
            self.get_assessment_service = get_assessment_service
            
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Error initializing assessment app: {str(e)}")
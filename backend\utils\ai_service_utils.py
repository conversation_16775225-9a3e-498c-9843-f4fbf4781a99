"""AIServiceUtilitiesThismoduleprovidescommonutilitiesforAIservicestoensureconsistencyandreducecodeduplicationacrossdifferentAIserviceimplementations."""import loggingimport jsonfromtypingimportAnyDictOptionalListfromdatetimeimport datetime#ConfigurestandardAIserviceloggerdefget_ai_logger(service_name:str)->logging.Logger:"""GetastandardizedloggerforAIservices."""logger=logging.getLogger(f"ai_service.{service_name}")ifnotlogger.handlers:#Configureloggerifnotalreadyconfiguredlogger.setLevel(logging.INFO)handler=logging.StreamHandler()formatter=logging.Formatter(f'[AI:{service_name}]%(asctime)s-%(levelname)s-%(message)s')handler.setFormatter(formatter)logger.addHandler(handler)returnloggerdefget_error_message(error:Exception)->str:"""Getastandardizederrormessagefromanexception."""returnf"{type(error).__name__}:{str(error)}"defstandardized_json_parser(response_text:str)->Dict[strAny]:"""ParseJSONresponsewithstandardizederrorhandling."""try:#TrytoparseasJSONreturnjson.loads(response_text)exceptjson.JSONDecodeError:#Ifit'snotvalidJSONtrytoextractJSONfromthetextimport rejson_match=re.search(r'\{.*\}'response_textre.DOTALL)ifjson_match:try:returnjson.loads(json_match.group())exceptjson.JSONDecodeError:pass#IfnoJSONfoundreturnastructuredresponsereturn{"error":"InvalidJSONresponse""raw_response":response_text"parsed":False}classStandardizedAIService:"""BaseclassforstandardizedAIservices.ThisclassprovidescommonfunctionalityandensuresconsistencyacrossdifferentAIserviceimplementations."""def__init__(selfservice_name:str):"""InitializethestandardizedAIservice."""self.service_name=service_nameself.logger=get_ai_logger(service_name)self.config={}self._initialize_service()defregister_with_config_manager(self):"""RegisterthisservicewiththeAIconfigmanager."""try:from utils.ai_config_managerimport register_ai_serviceregister_ai_service(self.service_nameself'refresh_configuration')self.logger.info(f"Registered{self.service_name}withAIconfigurationmanager")exceptExceptionase:self.logger.warning(f"CouldnotregisterwithAIconfigmanager:{e}")def_initialize_service(self):"""Initializeservice-specificcomponents.Overrideinsubclasses."""try:#GettheunifiedAIservicefrom utils.ai.servicesimportget_ai_serviceself.ai_service=get_ai_service()self.logger.info(f"Initialized{self.service_name}withconsolidatedAIservice")exceptExceptionase:self.logger.warning(f"CouldnotinitializeAIserviceconnection:{e}")self.ai_service=Nonedefrefresh_configuration(self):"""Refreshtheserviceconfiguration.Overrideinsubclasses."""self.logger.info(f"Refreshingconfigurationfor{self.service_name}")try:#ReinitializetheAIserviceconnectionself._initialize_service()self.logger.info(f"Configurationrefreshedfor{self.service_name}")exceptExceptionase:self.logger.error(f"Failedtorefreshconfigurationfor{self.service_name}:{e}")defget_config(selfkey:str=Nonedefault:Any=None)->Any:"""Getconfigurationvalue."""ifkeyisNone:returnself.configreturnself.config.get(keydefault)defset_config(selfkey:strvalue:Any):"""Setconfigurationvalue."""self.config[key]=valuedeflog_error(selferror:Exceptioncontext:str=""):"""Loganerrorwithstandardizedformat."""error_msg=get_error_message(error)ifcontext:self.logger.error(f"{context}:{error_msg}")else:self.logger.error(error_msg)deflog_info(selfmessage:str):"""Loganinfomessage."""self.logger.info(message)deflog_warning(selfmessage:str):"""Logawarningmessage."""self.logger.warning(message)classAIServiceError(Exception):"""BaseexceptionforAIserviceerrors."""passclassConfigurationError(AIServiceError):"""Raisedwhenthereareconfigurationissues."""passclassAPIConnectionError(AIServiceError):"""RaisedwhenthereareconnectionissueswiththeAPI."""passclassResponseGenerationError(AIServiceError):"""Raisedwhenthereareissuesgeneratingresponses."""passdefcreate_standardized_response(data:Anystatus:str="success"message:str="")->Dict[strAny]:"""Createastandardizedresponseformat."""return{"status":status"data":data"message":message"timestamp":datetime.now().isoformat()}defcreate_fallback_response(message:str="AIserviceunavailable"data:Any=None)->Dict[strAny]:"""CreateafallbackresponsewhenAIservicesareunavailable."""return{"status":"fallback""data":dataor{"message":message"fallback_used":True}"message":message"timestamp":datetime.now().isoformat()}defvalidate_ai_response(response:Dict[strAny])->bool:"""ValidateifanAIresponsehastheexpectedstructure."""required_keys=["status""data"]returnall(keyinresponseforkeyinrequired_keys)#Exportcommonlyuseditems__all__=['StandardizedAIService''get_ai_logger''get_error_message''standardized_json_parser''AIServiceError''ConfigurationError''APIConnectionError''ResponseGenerationError''create_standardized_response''create_fallback_response''validate_ai_response']
/**
 * Comprehensive tests for QuestionCard component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { ThemeProvider } from '@mui/material/styles';
import { configureStore } from '@reduxjs/toolkit';

import QuestionCard from '../../assessment/QuestionCard';
import { theme } from '../../../theme';
import assessmentSlice from '../../../store/slices/assessmentSlice';

// Mock question data
const mockMultipleChoiceQuestion = {
  id: 1,
  question_text: 'What is the capital of France?',
  question_type: 'MULTIPLE_CHOICE',
  points: 10,
  options: [
    { id: 1, text: 'London', is_correct: false },
    { id: 2, text: 'Paris', is_correct: true },
    { id: 3, text: 'Berlin', is_correct: false },
    { id: 4, text: 'Madrid', is_correct: false },
  ],
  time_limit: 60,
  difficulty: 'EASY',
  explanation: 'Paris is the capital and largest city of France.',
};

const mockEssayQuestion = {
  id: 2,
  question_text: 'Explain the concept of photosynthesis.',
  question_type: 'ESSAY',
  points: 20,
  time_limit: 300,
  difficulty: 'MEDIUM',
  word_limit: 500,
  explanation: 'Photosynthesis is the process by which plants convert light energy into chemical energy.',
};

const mockTrueFalseQuestion = {
  id: 3,
  question_text: 'The Earth is flat.',
  question_type: 'TRUE_FALSE',
  points: 5,
  correct_answer: false,
  difficulty: 'EASY',
  explanation: 'The Earth is approximately spherical in shape.',
};

// Create test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      assessment: assessmentSlice,
    },
    preloadedState: {
      assessment: {
        currentAssessment: null,
        questions: [],
        responses: {},
        loading: false,
        error: null,
        ...initialState,
      },
    },
  });
};

// Test wrapper
const TestWrapper: React.FC<{ children: React.ReactNode; store?: any }> = ({
  children,
  store = createTestStore(),
}) => (
  <Provider store={store}>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </Provider>
);

describe('QuestionCard Component', () => {
  describe('Multiple Choice Questions', () => {
    it('renders multiple choice question correctly', () => {
      render(
        <TestWrapper>
          <QuestionCard question={mockMultipleChoiceQuestion} questionNumber={1} />
        </TestWrapper>
      );

      expect(screen.getByText('Question 1')).toBeInTheDocument();
      expect(screen.getByText('What is the capital of France?')).toBeInTheDocument();
      expect(screen.getByText('10 points')).toBeInTheDocument();
      expect(screen.getByText('London')).toBeInTheDocument();
      expect(screen.getByText('Paris')).toBeInTheDocument();
      expect(screen.getByText('Berlin')).toBeInTheDocument();
      expect(screen.getByText('Madrid')).toBeInTheDocument();
    });

    it('handles option selection', async () => {
      const user = userEvent.setup();
      const mockOnAnswer = jest.fn();

      render(
        <TestWrapper>
          <QuestionCard 
            question={mockMultipleChoiceQuestion} 
            questionNumber={1}
            onAnswer={mockOnAnswer}
          />
        </TestWrapper>
      );

      const parisOption = screen.getByLabelText('Paris');
      await user.click(parisOption);

      expect(mockOnAnswer).toHaveBeenCalledWith(1, 2); // question id, option id
    });

    it('shows selected option visually', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <QuestionCard 
            question={mockMultipleChoiceQuestion} 
            questionNumber={1}
            selectedAnswer={2}
          />
        </TestWrapper>
      );

      const parisOption = screen.getByLabelText('Paris');
      expect(parisOption).toBeChecked();
    });

    it('prevents multiple selections', async () => {
      const user = userEvent.setup();
      const mockOnAnswer = jest.fn();

      render(
        <TestWrapper>
          <QuestionCard 
            question={mockMultipleChoiceQuestion} 
            questionNumber={1}
            onAnswer={mockOnAnswer}
          />
        </TestWrapper>
      );

      const londonOption = screen.getByLabelText('London');
      const parisOption = screen.getByLabelText('Paris');

      await user.click(londonOption);
      await user.click(parisOption);

      // Should only have Paris selected
      expect(londonOption).not.toBeChecked();
      expect(parisOption).toBeChecked();
    });
  });

  describe('Essay Questions', () => {
    it('renders essay question correctly', () => {
      render(
        <TestWrapper>
          <QuestionCard question={mockEssayQuestion} questionNumber={2} />
        </TestWrapper>
      );

      expect(screen.getByText('Question 2')).toBeInTheDocument();
      expect(screen.getByText('Explain the concept of photosynthesis.')).toBeInTheDocument();
      expect(screen.getByText('20 points')).toBeInTheDocument();
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('handles text input', async () => {
      const user = userEvent.setup();
      const mockOnAnswer = jest.fn();

      render(
        <TestWrapper>
          <QuestionCard 
            question={mockEssayQuestion} 
            questionNumber={2}
            onAnswer={mockOnAnswer}
          />
        </TestWrapper>
      );

      const textArea = screen.getByRole('textbox');
      const testAnswer = 'Photosynthesis is a process...';
      
      await user.type(textArea, testAnswer);

      expect(mockOnAnswer).toHaveBeenCalledWith(2, testAnswer);
    });

    it('shows word count', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <QuestionCard question={mockEssayQuestion} questionNumber={2} />
        </TestWrapper>
      );

      const textArea = screen.getByRole('textbox');
      await user.type(textArea, 'This is a test answer with multiple words.');

      expect(screen.getByText(/8 \/ 500 words/)).toBeInTheDocument();
    });

    it('warns when approaching word limit', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <QuestionCard question={mockEssayQuestion} questionNumber={2} />
        </TestWrapper>
      );

      const textArea = screen.getByRole('textbox');
      const longText = 'word '.repeat(450); // 450 words
      
      await user.type(textArea, longText);

      expect(screen.getByText(/approaching word limit/i)).toBeInTheDocument();
    });

    it('prevents input beyond word limit', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <QuestionCard question={mockEssayQuestion} questionNumber={2} />
        </TestWrapper>
      );

      const textArea = screen.getByRole('textbox');
      const maxText = 'word '.repeat(500); // Exactly 500 words
      const extraText = ' extra';
      
      await user.type(textArea, maxText);
      await user.type(textArea, extraText);

      expect(textArea.value.split(' ').length).toBeLessThanOrEqual(500);
    });
  });

  describe('True/False Questions', () => {
    it('renders true/false question correctly', () => {
      render(
        <TestWrapper>
          <QuestionCard question={mockTrueFalseQuestion} questionNumber={3} />
        </TestWrapper>
      );

      expect(screen.getByText('Question 3')).toBeInTheDocument();
      expect(screen.getByText('The Earth is flat.')).toBeInTheDocument();
      expect(screen.getByText('5 points')).toBeInTheDocument();
      expect(screen.getByLabelText('True')).toBeInTheDocument();
      expect(screen.getByLabelText('False')).toBeInTheDocument();
    });

    it('handles true/false selection', async () => {
      const user = userEvent.setup();
      const mockOnAnswer = jest.fn();

      render(
        <TestWrapper>
          <QuestionCard 
            question={mockTrueFalseQuestion} 
            questionNumber={3}
            onAnswer={mockOnAnswer}
          />
        </TestWrapper>
      );

      const falseOption = screen.getByLabelText('False');
      await user.click(falseOption);

      expect(mockOnAnswer).toHaveBeenCalledWith(3, false);
    });
  });

  describe('Common Features', () => {
    it('displays question difficulty', () => {
      render(
        <TestWrapper>
          <QuestionCard question={mockMultipleChoiceQuestion} questionNumber={1} />
        </TestWrapper>
      );

      expect(screen.getByText('EASY')).toBeInTheDocument();
    });

    it('shows time limit when provided', () => {
      render(
        <TestWrapper>
          <QuestionCard question={mockMultipleChoiceQuestion} questionNumber={1} />
        </TestWrapper>
      );

      expect(screen.getByText('60 seconds')).toBeInTheDocument();
    });

    it('displays explanation in review mode', () => {
      render(
        <TestWrapper>
          <QuestionCard 
            question={mockMultipleChoiceQuestion} 
            questionNumber={1}
            showExplanation={true}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Paris is the capital and largest city of France.')).toBeInTheDocument();
    });

    it('shows correct answer in review mode', () => {
      render(
        <TestWrapper>
          <QuestionCard 
            question={mockMultipleChoiceQuestion} 
            questionNumber={1}
            showCorrectAnswer={true}
          />
        </TestWrapper>
      );

      const parisOption = screen.getByLabelText('Paris');
      expect(parisOption.closest('div')).toHaveClass('correct-answer');
    });

    it('indicates incorrect answer in review mode', () => {
      render(
        <TestWrapper>
          <QuestionCard 
            question={mockMultipleChoiceQuestion} 
            questionNumber={1}
            selectedAnswer={1} // London (incorrect)
            showCorrectAnswer={true}
          />
        </TestWrapper>
      );

      const londonOption = screen.getByLabelText('London');
      expect(londonOption.closest('div')).toHaveClass('incorrect-answer');
    });

    it('is disabled in read-only mode', () => {
      render(
        <TestWrapper>
          <QuestionCard 
            question={mockMultipleChoiceQuestion} 
            questionNumber={1}
            readOnly={true}
          />
        </TestWrapper>
      );

      const options = screen.getAllByRole('radio');
      options.forEach(option => {
        expect(option).toBeDisabled();
      });
    });

    it('handles keyboard navigation', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <QuestionCard question={mockMultipleChoiceQuestion} questionNumber={1} />
        </TestWrapper>
      );

      const firstOption = screen.getByLabelText('London');
      
      // Focus first option
      firstOption.focus();
      expect(firstOption).toHaveFocus();

      // Navigate with arrow keys
      await user.keyboard('{ArrowDown}');
      expect(screen.getByLabelText('Paris')).toHaveFocus();

      await user.keyboard('{ArrowDown}');
      expect(screen.getByLabelText('Berlin')).toHaveFocus();
    });

    it('supports flagging for review', async () => {
      const user = userEvent.setup();
      const mockOnFlag = jest.fn();

      render(
        <TestWrapper>
          <QuestionCard 
            question={mockMultipleChoiceQuestion} 
            questionNumber={1}
            onFlag={mockOnFlag}
          />
        </TestWrapper>
      );

      const flagButton = screen.getByRole('button', { name: /flag for review/i });
      await user.click(flagButton);

      expect(mockOnFlag).toHaveBeenCalledWith(1, true);
    });

    it('shows flagged state visually', () => {
      render(
        <TestWrapper>
          <QuestionCard 
            question={mockMultipleChoiceQuestion} 
            questionNumber={1}
            isFlagged={true}
          />
        </TestWrapper>
      );

      expect(screen.getByTestId('flag-icon')).toHaveClass('flagged');
    });

    it('is accessible', () => {
      render(
        <TestWrapper>
          <QuestionCard question={mockMultipleChoiceQuestion} questionNumber={1} />
        </TestWrapper>
      );

      // Check for proper ARIA labels
      expect(screen.getByRole('group')).toBeInTheDocument();
      expect(screen.getByRole('group')).toHaveAttribute('aria-labelledby');

      // Check for proper form structure
      const radioButtons = screen.getAllByRole('radio');
      radioButtons.forEach(radio => {
        expect(radio).toHaveAttribute('name', 'question-1');
      });
    });

    it('handles loading state', () => {
      render(
        <TestWrapper>
          <QuestionCard 
            question={mockMultipleChoiceQuestion} 
            questionNumber={1}
            loading={true}
          />
        </TestWrapper>
      );

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
      expect(screen.getByText(/loading question/i)).toBeInTheDocument();
    });
  });
});

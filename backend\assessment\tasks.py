import loggingfrom django.dbimporttransactionfrom django.utilsimport timezonefromceleryimportshared_taskfrom.modelsimportAssessmentAssessmentQuestionAssessmentResponsefrom.servicesimportassessment_ailogger=logging.getLogger(__name__)@shared_taskdefgenerate_ai_questions_task(category:strdifficulty_level:strcount:int=5)->list:"""GenerateAI-poweredassessmentquestionsasynchronously"""try:questions=assessment_ai.generate_questions(category=categorydifficulty_level=difficulty_levelcount=count)created_questions=[]forquestion_datainquestions:question=AssessmentQuestion.objects.create(question_text=question_data["text"]options=question_data["options"]correct_answer=question_data["correct"]points=question_data.get("points"10)difficulty_level=difficulty_levelcategory=categorycreated_at=timezone.now())created_questions.append(question.id)returncreated_questionsexceptExceptionase:return{"error":str(e)}@shared_taskdefvalidate_ai_questions_task(question_ids:list)->dict:"""ValidateAI-generatedquestionsasynchronously"""try:validation_results={}questions=AssessmentQuestion.objects.filter(id__in=question_ids)forquestioninquestions:validation=assessment_ai.validate_question({"question_text":question.question_text"options":question.options"correct_answer":question.correct_answer"difficulty_level":question.difficulty_level"category":question.category})validation_results[question.id]=validation#Updatequestionifvalidationsuggestsimprovementsifvalidation.get("improvements"):question.question_text=validation["improvements"].get("text"question.question_text)question.options=validation["improvements"].get("options"question.options)question.save()returnvalidation_resultsexceptExceptionase:return{"error":str(e)}@shared_taskdefanalyze_assessment_responses_task(assessment_id:int)->dict:"""Analyzestudentassessmentresponsesasynchronously"""try:assessment=Assessment.objects.get(id=assessment_id)responses=assessment.results.all()#Checkifthereareanyresponsestoanalyzeifnotresponses.exists():logger.warning(f"Noresponsesfoundforassessment{assessment_id}")return{"warning":f"Noresponsesfoundforassessment{assessment_id}"}try:response_data=[{"question":response.question.question_text"answer":response.student_answer"is_correct":response.is_correct"time_spent_seconds":response.time_spent_seconds}forresponseinresponses]#Trytoanalyzeresponseswitherrorhandlingtry:analysis=assessment_ai.analyze_responses(responses=response_datastudent_level=assessment.score)exceptConnectionErrorasconn_err:logger.error(f"Connectionerrorduringanalysis:{str(conn_err)}")return{"error":"Connectionerror""details":str(conn_err)}exceptExceptionasanalysis_err:logger.error(f"Erroranalyzingresponses:{str(analysis_err)}")#Returnadefaultanalysistoavoidbreakingtheflowanalysis={"strengths":{}"weaknesses":{}"detailed_analysis":{}"ai_insights":{}"suggestions":[]}#Updateassessmentwithanalysisresultswithtransaction.atomic():#Getorcreatestudentlevelprofiletry:student_level=assessment.student.level_profilestudent_level.skill_strengths=analysis.get("strengths"{})student_level.skill_weaknesses=analysis.get("weaknesses"{})student_level.save()exceptExceptionaslevel_err:logger.error(f"Errorupdatingstudentlevel:{str(level_err)}")#Continuewithassessmentupdateevenifstudentlevelupdatefails#Updateassessmentdetailedresultsassessment.detailed_results.update({"detailed_analysis":analysis.get("detailed_analysis"{})"ai_insights":analysis.get("ai_insights"{})"improvement_suggestions":analysis.get("suggestions"[])})assessment.save()returnanalysisexceptExceptionasinner_err:logger.error(f"Errorprocessingresponses:{str(inner_err)}")return{"error":str(inner_err)}exceptAssessment.DoesNotExist:logger.error(f"AssessmentwithID{assessment_id}notfound")return{"error":f"AssessmentwithID{assessment_id}notfound"}exceptExceptionase:logger.error(f"Unexpectederrorinanalyze_assessment_responses_task:{str(e)}")return{"error":str(e)}@shared_taskdefschedule_assessment_generation(category:strdifficulty_level:strcount:int=5)->None:"""Scheduleperiodicgenerationofnewassessmentquestions"""generate_ai_questions_task.delay(categorydifficulty_levelcount)@shared_taskdefbulk_analyze_student_progress(student_ids:list)->dict:"""Analyzeprogressformultiplestudentsasynchronously"""try:results={}forstudent_idinstudent_ids:assessments=Assessment.objects.filter(student_id=student_idstatus="COMPLETED").order_by("-end_time")ifassessments.exists():latest_assessment=assessments.first()analysis=analyze_assessment_responses_task.delay(latest_assessment.id)results[student_id]=analysis.get()returnresultsexceptExceptionase:return{"error":str(e)}@shared_taskdefgenerate_course_recommendations_task(assessment_id):"""Asynctasktogeneratecourserecommendations"""try:assessment=Assessment.objects.get(id=assessment_id)#Createasimplelearningpathwithdefaultrecommendationsdefault_recommendations=["Completetheintroductorycoursesinyourfield""Practicewithhands-onexercises""Joinstudygroupsforcollaborativelearning"]learning_path=[{"title":"Introductiontothesubject""level":1}{"title":"Coreconcepts""level":2}{"title":"Advancedtopics""level":3}]#TrytogenerateAIrecommendationsbutusedefaultsifitfailstry:#GeneratebasicrecommendationsusingAIserviceai_recommendations=assessment_ai.generate_content(f"Generatecourserecommendationsforastudentwithassessmentscore{assessment.score}")ifai_recommendations:recommendations=ai_recommendationselse:recommendations=default_recommendationsexceptConnectionErrorasconn_err:logger.error(f"Connectionerrorduringrecommendationgeneration:{str(conn_err)}")recommendations=default_recommendationsexceptExceptionasrec_err:logger.error(f"ErrorgeneratingAIrecommendations:{str(rec_err)}")recommendations=default_recommendationswithtransaction.atomic():#Updateassessmentwithrecommendationsassessment.detailed_results.update({"recommendations":recommendations"learning_path":learning_path})assessment.save()return{"status":"success""assessment_id":assessment_id"recommendations":recommendations"learning_path":learning_path}exceptAssessment.DoesNotExist:logger.error(f"AssessmentwithID{assessment_id}notfound")return{"status":"error""message":f"AssessmentwithID{assessment_id}notfound"}exceptExceptionase:logger.error(f"Errorgeneratingcourserecommendations:{str(e)}")return{"status":"error""message":str(e)}
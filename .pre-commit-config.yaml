# Pre-commit configuration for North Star University Full-Stack Project
# Handles both Django backend and React frontend code quality

repos:
  # ===== GENERAL CODE QUALITY =====
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: debug-statements
      - id: check-docstring-first
        files: \.py$

  # ===== BACKEND (Python/Django) =====
  
  # Python code formatting
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=88]
        files: ^backend/.*\.py$

  # Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]
        files: ^backend/.*\.py$

  # Python linting
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,W503]
        files: ^backend/.*\.py$
        additional_dependencies: [flake8-docstrings]

  # Security checks
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, backend/, -f, json, -o, backend/bandit-report.json]
        files: ^backend/.*\.py$
        exclude: ^backend/.*/(tests|migrations)/.*\.py$

  # ===== FRONTEND (JavaScript/TypeScript/React) =====
  
  # JavaScript/TypeScript formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0
    hooks:
      - id: prettier
        files: ^frontend/.*\.(js|jsx|ts|tsx|json|css|scss|md)$
        args: [--write]

  # ESLint for JavaScript/TypeScript
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.44.0
    hooks:
      - id: eslint
        files: ^frontend/.*\.(js|jsx|ts|tsx)$
        args: [--fix]
        additional_dependencies:
          - eslint@8.44.0
          - '@typescript-eslint/eslint-plugin@5.60.0'
          - '@typescript-eslint/parser@5.60.0'
          - 'eslint-plugin-react@7.32.2'
          - 'eslint-plugin-react-hooks@4.6.0'

  # ===== DJANGO SPECIFIC CHECKS =====
  
  # Django system checks
  - repo: local
    hooks:
      - id: django-check
        name: Django Check
        entry: bash -c 'cd backend && python manage.py check'
        language: system
        pass_filenames: false
        files: ^backend/.*\.py$

      - id: django-migrations-check
        name: Django Migrations Check
        entry: bash -c 'cd backend && python manage.py makemigrations --check --dry-run'
        language: system
        pass_filenames: false
        files: ^backend/.*\.py$

  # ===== CUSTOM CODE QUALITY CHECKS =====
  
  # Our custom code quality tools
  - repo: local
    hooks:
      - id: code-quality-analysis
        name: Code Quality Analysis
        entry: bash -c 'cd backend && python analyze_code_quality.py'
        language: system
        pass_filenames: false
        files: ^backend/.*\.py$

      - id: naming-conventions
        name: Check Naming Conventions
        entry: bash -c 'cd backend && python manage.py fix_naming_conventions --dry-run'
        language: system
        pass_filenames: false
        files: ^backend/.*\.py$

      - id: circular-imports
        name: Check Circular Imports
        entry: bash -c 'cd backend && python manage.py fix_circular_imports --analyze-only'
        language: system
        pass_filenames: false
        files: ^backend/.*\.py$

  # ===== DOCUMENTATION =====
  
  # Markdown linting
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.35.0
    hooks:
      - id: markdownlint
        args: [--fix]

# Configuration for specific tools
default_language_version:
  python: python3.9
  node: 18.17.0

# Files to exclude from all hooks
exclude: |
  (?x)^(
      backend/migrations/.*|
      backend/__pycache__/.*|
      backend/\.venv/.*|
      backend/build/.*|
      backend/dist/.*|
      backend/backups/.*|
      frontend/node_modules/.*|
      frontend/build/.*|
      frontend/dist/.*|
      \.git/.*
  )$

# Fail fast - stop on first failure
fail_fast: false

# Minimum pre-commit version
minimum_pre_commit_version: 3.0.0

"""This file defines the Course model to make it available through the models package."""
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django.db.models import Case, Count, Q, When
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# Import base models from core app
from core.models import (
    BaseContent,
    BaseModel,
    Course as CourseBase,
    CourseContentBase,
    CourseRelationshipBase,
    ProgressTrackable,
    Skill
)

# Import the Department model
from .department import Department


# Define a helper function for academic year
def get_current_academic_year():
    """Return current academic year in format '2023-2024'"""
    today = timezone.now()
    if today.month < 9:  # If before September
        return f"{today.year - 1}-{today.year}"
    return f"{today.year}-{today.year + 1}"


# Define the Course model
class Course(CourseBase):
    """Model for university courses
    
    This is the main course model that inherits from CourseBase.
    It adds university-specific fields and relationships.
    """
    
    # Relationships
    department = models.ForeignKey(
        Department,
        on_delete=models.CASCADE,
        related_name="courses",
        null=True,
        blank=True
    )
    
    instructor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="courses_taught",
        null=True,
        blank=True
    )
    
    prerequisites = models.ManyToManyField(
        "self",
        symmetrical=False,
        blank=True,
        related_name="course_unlocks",
        help_text="Courses that must be completed before taking this course"
    )
    
    next_level_courses = models.ManyToManyField(
        "self",
        symmetrical=False,
        blank=True,
        related_name="previous_level_courses",
        help_text="Recommended courses to take after completing this course"
    )
    
    # Skills relationships
    skills_required = models.ManyToManyField(
        "core.Skill",
        related_name="required_by_courses",
        blank=True,
        help_text="Skills needed to take this course"
    )
    
    skills_developed = models.ManyToManyField(
        "core.Skill",
        related_name="developed_in_courses",
        blank=True,
        help_text="Skills that will be developed in this course"
    )
    
    # Note: Core course fields inherited from CourseBase
    
    # Add has_assessment field
    has_assessment = models.BooleanField(
        default=False,
        help_text="Whether this course has an assessment"
    )
    
    # Primary course type (for consolidation)
    COURSE_TYPE_CHOICES = [
        ('STANDARD', 'Standard Course'),
        ('INTERACTIVE', 'Interactive Course'),
        ('AI_GENERATED', 'AI-Generated Course'),
        ('HYBRID', 'Hybrid Course')
    ]
    
    primary_type = models.CharField(
        max_length=20,
        choices=COURSE_TYPE_CHOICES,
        default='STANDARD',
        help_text="Primary type of this course for unified management"
    )
    
    class Meta:
        ordering = ["-created_at", "title"]
        app_label = "courses"  # Explicitly set the app label
    
    def __str__(self):
        return f"{self.course_code} - {self.title}"
    
    @property
    def has_interactive_version(self):
        """Check if this course has an interactive version"""
        # Interactive learning removed - app no longer exists
        return False
    
    def get_interactive_version(self):
        """Get the interactive version of this course if it exists"""
        # Interactive learning removed - app no longer exists
        return None
    
    def create_interactive_version(self, **kwargs):
        """Create an interactive version of this course"""
        # Interactive learning removed - app no longer exists
        raise NotImplementedError("Interactive learning feature has been removed")
    
    def to_dict_with_interactive(self):
        """Convert course to dictionary including interactive version info"""
        data = {
            'id': self.id,
            'course_code': self.course_code,
            'title': self.title,
            'description': self.description,
            'credits': self.credits,
            'instructor': self.instructor.get_full_name() if self.instructor else None,
            'department': self.department.name if self.department else None,
            'is_active': self.is_active,
            'has_interactive_content': self.has_interactive_content,
            'primary_type': self.primary_type,
            'has_interactive_version': self.has_interactive_version
        }
        
        # Interactive learning removed - app no longer exists
        # No interactive version data to add
        
        return data

import React, { useState, memo, useCallback, useMemo } from 'react';
// Optimized imports for better tree shaking
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardActions from '@mui/material/CardActions';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Chip from '@mui/material/Chip';
import Avatar from '@mui/material/Avatar';
import LinearProgress from '@mui/material/LinearProgress';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import Badge from '@mui/material/Badge';
import Divider from '@mui/material/Divider';
import Collapse from '@mui/material/Collapse';
import Fade from '@mui/material/Fade';
import Zoom from '@mui/material/Zoom';
import { useTheme } from '@mui/material/styles';
// Keep lightweight layout components as named imports
import { Box, Stack } from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import { ICourse } from '../../services/courseService';
import {
  BookmarkBorder,
  Bookmark,
  PlayArrow,
  Assessment,
  School,
  AccessTime,
  Star,
  Group,
  ExpandMore,
  ExpandLess,
  Person,
  Schedule,
  Devices,
  AutoAwesome,
  Psychology,
} from '../icons';
import { formatDistanceToNow } from 'date-fns';

// Enhanced keyframe animations
const shimmer = keyframes`
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
`;

const glow = keyframes`
  0%, 100% { box-shadow: 0 0 5px rgba(33, 150, 243, 0.3); }
  50% { box-shadow: 0 0 20px rgba(33, 150, 243, 0.6), 0 0 30px rgba(33, 150, 243, 0.4); }
`;

const float = keyframes`
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
`;

const slideIn = keyframes`
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
`;

// Enhanced styled components
const StyledCard = styled(Card, {
  shouldForwardProp: (prop) => !['variant', 'userRole'].includes(prop as string),
})<{ variant?: string; userRole?: string }>(({ theme, variant }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  borderRadius: variant === 'compact' ? '16px' : '24px',
  overflow: 'hidden',
  cursor: 'pointer',
  
  // Base styles
  background: variant === 'featured' 
    ? 'linear-gradient(145deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.08) 100%)'
    : 'linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
  backdropFilter: 'blur(20px)',
  border: variant === 'featured' 
    ? '2px solid rgba(255,215,0,0.3)'
    : '1px solid rgba(255,255,255,0.2)',

  '&:hover': {
    transform: variant === 'compact' 
      ? 'translateY(-6px) scale(1.01)' 
      : 'translateY(-12px) scale(1.02)',
    boxShadow: variant === 'featured'
      ? `0 30px 60px rgba(255,215,0,0.2), 0 0 0 1px rgba(255,215,0,0.1)`
      : `0 25px 50px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1)`,
    
    '& .card-header': {
      background: variant === 'featured'
        ? 'linear-gradient(135deg, rgba(255,215,0,0.2), rgba(255,193,7,0.2))'
        : 'linear-gradient(135deg, rgba(33,150,243,0.2), rgba(156,39,176,0.2))',
    },
    '& .floating-element': {
      animation: `${float} 3s ease-in-out infinite`,
    },
    '& .glow-effect': {
      animation: `${glow} 2s ease-in-out infinite`,
    },
  },

  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
    transition: 'left 0.5s',
    zIndex: 1,
  },
  '&:hover::before': {
    left: '100%',
  },
}));

const CardHeader = styled(Box)(({ theme }) => ({
  padding: '24px 24px 20px',
  background: 'rgba(255,255,255,0.08)',
  borderBottom: '1px solid rgba(255,255,255,0.1)',
  position: 'relative',
  overflow: 'hidden',
  zIndex: 2,
  
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '2px',
    background: 'linear-gradient(90deg, transparent, rgba(33,150,243,0.8), transparent)',
  },
}));

const CourseTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  fontSize: '1.25rem',
  lineHeight: 1.3,
  marginBottom: theme.spacing(1),
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
  overflow: 'hidden',
  background: 'linear-gradient(45deg, #2196F3, #21CBF3)',
  backgroundClip: 'text',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
}));

const CourseDescription = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  fontSize: '0.875rem',
  lineHeight: 1.6,
  marginBottom: theme.spacing(2),
  flexGrow: 1,
}));

const InstructorSection = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1.5),
  marginBottom: theme.spacing(2),
  padding: theme.spacing(1.5),
  borderRadius: '12px',
  background: 'rgba(255,255,255,0.05)',
  border: '1px solid rgba(255,255,255,0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    background: 'rgba(255,255,255,0.1)',
    transform: 'scale(1.02)',
  },
}));

const GlowingChip = styled(Chip)(({ theme }) => ({
  borderRadius: '12px',
  fontWeight: 600,
  fontSize: '0.75rem',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'scale(1.05)',
    boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
  },
}));

const ProgressContainer = styled(Box)(({ theme }) => ({
  padding: '16px',
  borderRadius: '16px',
  background: 'linear-gradient(135deg, rgba(33,150,243,0.1), rgba(156,39,176,0.1))',
  border: '1px solid rgba(33,150,243,0.3)',
  position: 'relative',
  overflow: 'hidden',
  marginBottom: theme.spacing(2),
  
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent)',
    animation: `${shimmer} 2s infinite`,
  },
}));

const ActionButton = styled(Button)(({ theme }) => ({
  borderRadius: '14px',
  textTransform: 'none',
  fontWeight: 600,
  padding: '10px 20px',
  position: 'relative',
  overflow: 'hidden',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
    transition: 'left 0.5s',
  },
  '&:hover::before': {
    left: '100%',
  },
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 25px rgba(0,0,0,0.3)',
  },
}));

const MetricItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(0.5),
  padding: theme.spacing(1),
  borderRadius: '8px',
  background: 'rgba(255,255,255,0.05)',
  transition: 'all 0.3s ease',
  '&:hover': {
    background: 'rgba(255,255,255,0.1)',
    transform: 'scale(1.02)',
  },
}));

const AnimatedStack = styled(Stack)(({ theme }) => ({
  animation: `${slideIn} 0.5s ease-out`,
}));

// Enhanced prop interface
interface SuperCourseCardProps {
  course: ICourse;
  
  // Callback functions
  onEnroll?: (courseId: number) => void;
  onViewDetails?: (courseId: number) => void;
  onStartLearning?: (courseId: number) => void;
  onTakeAssessment?: (courseId: number) => void;
  onEdit?: (courseId: number) => void;
  onManage?: (courseId: number) => void;
  onBookmark?: (courseId: number, isBookmarked?: boolean) => void;
  
  // Display options
  variant?: 'default' | 'compact' | 'featured' | 'detailed';
  userRole?: 'STUDENT' | 'PROFESSOR' | 'ADMIN';
  showProgress?: boolean;
  showActions?: boolean;
  showInteractiveFeatures?: boolean;
  showSkills?: boolean;
  showEnrollmentInfo?: boolean;
  showLastUpdated?: boolean;
  
  // State
  progress?: number;
  isEnrolled?: boolean;
  isBookmarked?: boolean;
  enrollmentCount?: number;
  rating?: number;
  
  // Animation options
  animationDelay?: number;
  enableAnimations?: boolean;
}

const SuperCourseCard: React.FC<SuperCourseCardProps> = ({
  course,
  onEnroll,
  onViewDetails,
  onStartLearning,
  onTakeAssessment,
  onEdit,
  onManage,
  onBookmark,
  variant = 'default',
  userRole = 'STUDENT',
  showProgress = false,
  showActions = true,
  showInteractiveFeatures = true,
  showSkills = true,
  showEnrollmentInfo = true,
  showLastUpdated = true,
  progress = 0,
  isEnrolled = false,
  isBookmarked = false,
  enrollmentCount,
  rating,
  animationDelay = 0,
  enableAnimations = true,
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(false);
  const [hovering, setHovering] = useState(false);

  // Helper functions - memoized for performance
  const getCourseTypeDisplay = useCallback((type: string) => {
    const typeMap = {
      'INTERACTIVE': { icon: <Devices />, color: 'primary' as const, label: 'Interactive' },
      'AI_GENERATED': { icon: <AutoAwesome />, color: 'secondary' as const, label: 'AI Enhanced' },
      'HYBRID': { icon: <Devices />, color: 'warning' as const, label: 'Hybrid' },
      'STANDARD': { icon: <School />, color: 'default' as const, label: 'Standard' },
    };
    return typeMap[type as keyof typeof typeMap] || typeMap.STANDARD;
  }, []);

  const getLevelColor = useCallback((level: string | number) => {
    const levelStr = String(level).toLowerCase();
    if (levelStr.includes('beginner') || levelStr === '1') return 'success';
    if (levelStr.includes('elementary') || levelStr === '2') return 'info';
    if (levelStr.includes('intermediate') || levelStr === '3') return 'warning';
    if (levelStr.includes('advanced') || levelStr === '4') return 'error';
    if (levelStr.includes('expert') || levelStr === '5') return 'secondary';
    return 'primary';
  }, []);

  const getLevelDisplay = useCallback((level: number) => {
    const levels = {
      1: { label: 'Beginner', color: 'success' as const },
      2: { label: 'Elementary', color: 'info' as const },
      3: { label: 'Intermediate', color: 'warning' as const },
      4: { label: 'Advanced', color: 'error' as const },
      5: { label: 'Expert', color: 'secondary' as const }
    };
    return levels[level as keyof typeof levels] || { label: `Level ${level}`, color: 'default' as const };
  }, []);

  const getInstructorName = useCallback(() => {
    if (typeof course.instructor === 'object' && course.instructor) {
      const { first_name, last_name, username } = course.instructor;
      if (first_name || last_name) {
        return `${first_name || ''} ${last_name || ''}`.trim();
      }
      return username || 'Unknown Instructor';
    }
    return course.instructor || 'No Instructor Assigned';
  }, [course.instructor]);

  const getInstructorInitials = useCallback(() => {
    if (typeof course.instructor === 'object' && course.instructor) {
      const name = getInstructorName();
      const parts = name.split(' ');
      return parts.length > 1 ? `${parts[0][0]}${parts[1][0]}` : name[0] || 'I';
    }
    return course.instructor?.[0] || 'I';
  }, [course.instructor, getInstructorName]);

  const getDepartmentName = useCallback(() => {
    if (typeof course.department === 'object' && course.department) {
      return course.department.name || course.department.code;
    }
    return course.department || 'No Department';
  }, [course.department]);

  const getStatusChip = useCallback(() => {
    if (isEnrolled) {
      return { label: 'Enrolled', color: 'success' as const, variant: 'filled' as const };
    }
    if (course.is_active) {
      return { label: 'Available', color: 'primary' as const, variant: 'outlined' as const };
    }
    return { label: 'Inactive', color: 'default' as const, variant: 'outlined' as const };
  }, [isEnrolled, course.is_active]);

  const getProgressMessage = useCallback((progress: number) => {
    if (progress < 25) return '🚀 Just getting started!';
    if (progress < 50) return '💪 Making good progress!';
    if (progress < 75) return '🔥 More than halfway there!';
    if (progress < 100) return '⭐ Almost finished!';
    return '🎉 Course completed!';
  }, []);

  // Memoized computed values for performance
  const enrollmentPercentage = useMemo(() => {
    return course.max_students ? ((course.student_count || 0) / course.max_students) * 100 : 0;
  }, [course.max_students, course.student_count]);

  const getEnrollmentStatusColor = useCallback(() => {
    if (enrollmentPercentage >= 100) return 'error';
    if (enrollmentPercentage >= 80) return 'warning';
    return 'success';
  }, [enrollmentPercentage]);

  const typeDisplay = useMemo(() => getCourseTypeDisplay(course.primary_type || 'STANDARD'), [course.primary_type, getCourseTypeDisplay]);
  const levelDisplay = useMemo(() => getLevelDisplay(course.required_level || 1), [course.required_level, getLevelDisplay]);
  const statusChip = useMemo(() => getStatusChip(), [getStatusChip]);

  const cardContent = (
    <StyledCard
      variant={variant}
      userRole={userRole}
      onMouseEnter={() => setHovering(true)}
      onMouseLeave={() => setHovering(false)}
    >
      {/* Featured Badge */}
      {variant === 'featured' && (
        <Box
          className="floating-element"
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            zIndex: 3,
          }}
        >
          <GlowingChip
            className="glow-effect"
            label="Featured"
            color="warning"
            variant="filled"
            size="small"
            icon={<Star />}
          />
        </Box>
      )}

      {/* Header */}
      <CardHeader className="card-header">
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box flex={1}>
            <CourseTitle>
              {course.title}
            </CourseTitle>
            
            <Box display="flex" alignItems="center" gap={1} mb={1} flexWrap="wrap">
              {course.course_code && (
                <GlowingChip
                  label={course.course_code || course.code}
                  size="small"
                  variant="outlined"
                  sx={{ fontSize: '0.7rem', height: '20px' }}
                />
              )}
              <GlowingChip
                label={levelDisplay.label}
                size="small"
                color={levelDisplay.color}
                variant="filled"
              />
              <GlowingChip
                icon={typeDisplay.icon}
                label={typeDisplay.label}
                color={typeDisplay.color}
                size="small"
                variant="outlined"
              />
            </Box>
          </Box>

          <Box display="flex" alignItems="center" gap={1}>
            {rating && (
              <Fade in={hovering || variant === 'featured'} timeout={300}>
                <Box display="flex" alignItems="center" gap={0.5}>
                  <Star sx={{ fontSize: '1rem', color: 'warning.main' }} />
                  <Typography variant="caption" fontWeight={700}>
                    {rating.toFixed(1)}
                  </Typography>
                </Box>
              </Fade>
            )}
            
            {onBookmark && (
              <Tooltip title={isBookmarked ? 'Remove bookmark' : 'Bookmark course'}>
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    onBookmark(course.id, isBookmarked);
                  }}
                  sx={{
                    color: isBookmarked ? 'warning.main' : 'text.secondary',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 193, 7, 0.1)',
                      transform: 'scale(1.1)',
                    },
                    transition: 'all 0.3s ease',
                  }}
                >
                  {isBookmarked ? <Bookmark /> : <BookmarkBorder />}
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>
      </CardHeader>

      {/* Content */}
      <CardContent sx={{ flexGrow: 1, p: variant === 'compact' ? 2 : 3 }}>
        <CourseDescription
          sx={{
            display: '-webkit-box',
            WebkitLineClamp: variant === 'compact' ? 2 : (expanded ? 'none' : 3),
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
          }}
        >
          {course.description}
        </CourseDescription>

        {/* Expand/Collapse for long descriptions */}
        {course.description && course.description.length > 150 && variant !== 'compact' && (
          <Button
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              setExpanded(!expanded);
            }}
            endIcon={expanded ? <ExpandLess /> : <ExpandMore />}
            sx={{ mb: 2, textTransform: 'none' }}
          >
            {expanded ? 'Show less' : 'Show more'}
          </Button>
        )}

        {/* Instructor Info */}
        {course.instructor && variant !== 'compact' && (
          <Fade in timeout={400}>
            <InstructorSection>
              <Avatar
                sx={{
                  width: 36,
                  height: 36,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  fontSize: '0.875rem',
                }}
              >
                {getInstructorInitials()}
              </Avatar>
              <Box flex={1}>
                <Typography variant="body2" fontWeight={600}>
                  {getInstructorName()}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Course Instructor
                </Typography>
              </Box>
            </InstructorSection>
          </Fade>
        )}

        {/* Course Metrics */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box display="flex" gap={1} flexWrap="wrap">
            {course.credits && (
              <MetricItem>
                <School sx={{ fontSize: '1rem', color: 'primary.main' }} />
                <Typography variant="caption" fontWeight={500}>
                  {course.credits} Credits
                </Typography>
              </MetricItem>
            )}
            
            {showEnrollmentInfo && (enrollmentCount || course.student_count) && (
              <MetricItem>
                <Group sx={{ fontSize: '1rem', color: 'secondary.main' }} />
                <Typography variant="caption" fontWeight={500}>
                  {enrollmentCount || course.student_count} Students
                </Typography>
              </MetricItem>
            )}
          </Box>

          <GlowingChip
            {...statusChip}
            size="small"
            sx={{ fontSize: '0.7rem', height: '24px' }}
          />
        </Box>

        {/* Schedule Info for detailed variants */}
        {variant === 'detailed' && (
          <AnimatedStack direction="row" alignItems="center" spacing={1} mb={2}>
            <Schedule fontSize="small" color="action" />
            <Typography variant="body2" color="text.secondary">
              {course.semester} {course.academic_year}
            </Typography>
          </AnimatedStack>
        )}

        {/* Department and Features */}
        <Box display="flex" gap={1} flexWrap="wrap" mb={2}>
          {getDepartmentName() && getDepartmentName() !== 'No Department' && (
            <GlowingChip
              label={getDepartmentName()}
              size="small"
              variant="outlined"
              color="secondary"
            />
          )}
          
          {showInteractiveFeatures && course.has_interactive_content && (
            <GlowingChip
              label="Interactive"
              size="small"
              color="info"
              variant="filled"
              icon={<Devices />}
            />
          )}
          
          {showInteractiveFeatures && course.has_ai_content && (
            <GlowingChip
              label="AI Enhanced"
              size="small"
              color="secondary"
              variant="filled"
              icon={<Psychology />}
            />
          )}

          {course.has_assessment && variant !== 'compact' && (
            <GlowingChip
              icon={<Assessment />}
              label="Assessment"
              color="info"
              size="small"
              variant="outlined"
            />
          )}
        </Box>

        {/* Enrollment Progress Bar */}
        {showEnrollmentInfo && course.max_students && variant !== 'compact' && (
          <Box mb={2}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <Typography variant="caption" color="text.secondary">
                Enrollment: {course.student_count || 0} / {course.max_students}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={enrollmentPercentage}
                color={getEnrollmentStatusColor()}
                sx={{ flexGrow: 1, height: 4, borderRadius: 2 }}
              />
            </Stack>
          </Box>
        )}

        {/* Skills Section */}
        {showSkills && variant === 'detailed' && (course.skills_required?.length > 0 || course.skills_developed?.length > 0) && (
          <Box>
            <Divider sx={{ my: 1 }} />
            <Stack spacing={1}>
              {course.skills_required?.length > 0 && (
                <Box>
                  <Typography variant="caption" color="text.secondary" gutterBottom>
                    Required Skills:
                  </Typography>
                  <Stack direction="row" spacing={0.5} flexWrap="wrap">
                    {course.skills_required.slice(0, 3).map((skill) => (
                      <GlowingChip
                        key={skill.id}
                        label={skill.name}
                        size="small"
                        variant="outlined"
                        color="warning"
                      />
                    ))}
                    {course.skills_required.length > 3 && (
                      <GlowingChip 
                        label={`+${course.skills_required.length - 3} more`} 
                        size="small" 
                      />
                    )}
                  </Stack>
                </Box>
              )}

              {course.skills_developed?.length > 0 && (
                <Box>
                  <Typography variant="caption" color="text.secondary" gutterBottom>
                    Skills Developed:
                  </Typography>
                  <Stack direction="row" spacing={0.5} flexWrap="wrap">
                    {course.skills_developed.slice(0, 3).map((skill) => (
                      <GlowingChip
                        key={skill.id}
                        label={skill.name}
                        size="small"
                        variant="outlined"
                        color="success"
                      />
                    ))}
                    {course.skills_developed.length > 3 && (
                      <GlowingChip 
                        label={`+${course.skills_developed.length - 3} more`} 
                        size="small" 
                      />
                    )}
                  </Stack>
                </Box>
              )}
            </Stack>
          </Box>
        )}

        {/* Progress Section for Enrolled Students */}
        {showProgress && isEnrolled && (
          <Collapse in timeout={500}>
            <ProgressContainer>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                <Typography variant="body2" fontWeight={600}>
                  Course Progress
                </Typography>
                <Typography
                  variant="body2"
                  color="primary.main"
                  fontWeight={700}
                  sx={{ textShadow: variant === 'featured' ? '0 0 10px rgba(33,150,243,0.5)' : 'none' }}
                >
                  {progress}%
                </Typography>
              </Box>
              
              <LinearProgress
                variant="determinate"
                value={progress}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 4,
                    background: 'linear-gradient(90deg, #2196F3, #21CBF3)',
                    boxShadow: variant === 'featured' ? '0 0 10px rgba(33,150,243,0.5)' : 'none',
                  },
                }}
              />
              
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{
                  mt: 0.5,
                  display: 'block',
                  textAlign: 'center',
                  fontStyle: 'italic',
                }}
              >
                {getProgressMessage(progress)}
              </Typography>
            </ProgressContainer>
          </Collapse>
        )}

        {/* Last Updated */}
        {showLastUpdated && variant !== 'compact' && course.updated_at && (
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            Updated {formatDistanceToNow(new Date(course.updated_at), { addSuffix: true })}
          </Typography>
        )}
      </CardContent>

      {/* Actions */}
      {showActions && (
        <CardActions
          sx={{
            justifyContent: 'space-between',
            p: variant === 'compact' ? 2 : 3,
            pt: 0,
            borderTop: '1px solid rgba(255,255,255,0.1)',
            background: 'rgba(255,255,255,0.02)',
          }}
        >
          <Box display="flex" gap={1}>
            {onViewDetails && (
              <ActionButton
                size="small"
                variant="outlined"
                onClick={(e) => {
                  e.stopPropagation();
                  onViewDetails(course.id);
                }}
              >
                View Details
              </ActionButton>
            )}
          </Box>

          <Box display="flex" gap={1} alignItems="center">
            {/* Assessment Button */}
            {isEnrolled && onTakeAssessment && (
              <Tooltip title="Take Assessment">
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    onTakeAssessment(course.id);
                  }}
                  sx={{
                    color: 'warning.main',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 193, 7, 0.1)',
                      transform: 'scale(1.1)',
                    },
                    transition: 'all 0.3s ease',
                  }}
                >
                  <Assessment />
                </IconButton>
              </Tooltip>
            )}

            {/* Primary Action Button */}
            {isEnrolled && onStartLearning ? (
              <ActionButton
                size="small"
                variant="contained"
                startIcon={<PlayArrow />}
                onClick={(e) => {
                  e.stopPropagation();
                  onStartLearning(course.id);
                }}
                sx={{
                  background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                  '&:hover': {
                    background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',
                  },
                }}
              >
                Continue Learning
              </ActionButton>
            ) : (
              // Role-based actions for non-enrolled users
              <>
                {userRole === 'STUDENT' && onEnroll && !isEnrolled && course.is_active && (
                  <ActionButton
                    size="small"
                    variant="contained"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEnroll(course.id);
                    }}
                    disabled={enrollmentPercentage >= 100}
                    sx={{
                      background: enrollmentPercentage >= 100 
                        ? undefined
                        : 'linear-gradient(45deg, #4CAF50 30%, #8BC34A 90%)',
                      '&:hover': {
                        background: enrollmentPercentage >= 100 
                          ? undefined
                          : 'linear-gradient(45deg, #388E3C 30%, #689F38 90%)',
                      },
                    }}
                  >
                    {enrollmentPercentage >= 100 ? 'Full' : 'Enroll Now'}
                  </ActionButton>
                )}

                {userRole === 'PROFESSOR' && onManage && (
                  <ActionButton
                    size="small"
                    variant="contained"
                    color="primary"
                    onClick={(e) => {
                      e.stopPropagation();
                      onManage(course.id);
                    }}
                  >
                    Manage
                  </ActionButton>
                )}

                {userRole === 'ADMIN' && onEdit && (
                  <ActionButton
                    size="small"
                    variant="contained"
                    color="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit(course.id);
                    }}
                  >
                    Edit
                  </ActionButton>
                )}
              </>
            )}
          </Box>
        </CardActions>
      )}
    </StyledCard>
  );

  // Wrap with animations if enabled
  if (enableAnimations) {
    return (
      <Zoom in timeout={600} style={{ transitionDelay: `${animationDelay}ms` }}>
        <Box>
          {cardContent}
        </Box>
      </Zoom>
    );
  }

  return cardContent;
};

export default memo(SuperCourseCard);

import loggingfrom django.core.cacheimportcachefrom django.dbimporttransactionfrom django.db.modelsimportFfrom django.shortcutsimportget_object_or_404from django.utilsimport timezonefrom django.utils.decoratorsimportmethod_decoratorfrom django.views.decorators.csrfimportcsrf_exemptfromasgiref.syncimportasync_to_syncfromchannels.layersimportget_channel_layerfromrest_frameworkimportpermissionsstatusviewsetsfromrest_framework.decoratorsimportactionauthentication_classespermission_classesfromrest_framework.responseimportResponsefromrest_framework.viewsimportAPIViewfromrest_framework_simplejwt.authenticationimportJWTAuthenticationfrom utils.path_advisor_chatbotimportPathAdvisorChatBotfrom.modelsimportChatConversationChatMessageChatSessionUserLearningProfilefrom.serializersimport(ChatConversationSerializerChatInputSerializerChatMessageSerializerChatSessionSerializerUserLearningProfileSerializer)from.services.chat_serviceimport(APIConnectionErrorChatServiceErrorConfigurationErrorGeminiChatServiceResponseGenerationError)from.services.learning_pattern_analyzerimportLearningPatternAnalyzerlogger=logging.getLogger(__name__)chat_service=GeminiChatService()path_advisor_chatbot=PathAdvisorChatBot()classChatConversationViewSet(viewsets.ModelViewSet):serializer_class=ChatConversationSerializerpermission_classes=[permissions.IsAuthenticated]defget_queryset(self):try:returnChatConversation.objects.filter(user=self.request.user)exceptExceptionase:logger.error(f"ErrorinChatConversationViewSet.get_queryset:{str(e)}")returnChatConversation.objects.none()defperform_create(selfserializer):try:serializer.save(user=self.request.useruser_role=self.request.user.role)exceptExceptionase:logger.error(f"ErrorinChatConversationViewSet.perform_create:{str(e)}")raisedeflist(selfrequest*args**kwargs):try:returnsuper().list(request*args**kwargs)exceptExceptionase:logger.error(f"ErrorinChatConversationViewSet.list:{str(e)}")returnResponse({"detail":f"Failedtoretrieveconversations:{str(e)}"}status=status.HTTP_500_INTERNAL_SERVER_ERROR)defretrieve(selfrequest*args**kwargs):try:returnsuper().retrieve(request*args**kwargs)exceptExceptionase:logger.error(f"ErrorinChatConversationViewSet.retrieve:{str(e)}")returnResponse({"detail":f"Failedtoretrieveconversation:{str(e)}"}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@method_decorator(csrf_exemptname="dispatch")classChatView(APIView):permission_classes=[permissions.IsAuthenticated]CACHE_TIMEOUT=300#5minutesdef__init__(self*args**kwargs):super().__init__(*args**kwargs)try:self.chat_service=GeminiChatService()exceptConfigurationErrorase:logger.error(f"Failedtoinitializechatservice:{str(e)}")self.chat_service=Nonedefpost(selfrequest):try:ifnotself.chat_service:raiseChatServiceError("Chatserviceisnotproperlyinitialized")serializer=ChatInputSerializer(data=request.data)ifnotserializer.is_valid():returnResponse({"error":"Invalidinput""details":serializer.errors}status=status.HTTP_400_BAD_REQUEST)user_message=serializer.validated_data["message"]conversation_id=serializer.validated_data.get("conversation_id")#Processthechatrequesttry:response=self.process_chat_request(requestuser_messageconversation_id)returnResponse(response)exceptChatConversation.DoesNotExist:#Createnewconversationifspecifiedonedoesn'texistreturnResponse(self.process_chat_request(requestuser_messageNone))exceptChatServiceErrorase:logger.error(f"Chatserviceerror:{str(e)}")returnResponse({"error":"Chatserviceerror""message":str(e)}status=status.HTTP_503_SERVICE_UNAVAILABLE)exceptResponseGenerationErrorase:logger.error(f"Responsegenerationerror:{str(e)}")returnResponse({"error":"Failedtogenerateresponse""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)exceptExceptionase:logger.error(f"Unexpectederrorinchatview:{str(e)}")returnResponse({"error":"Internalservererror""message":"Anunexpectederroroccurred"}status=status.HTTP_500_INTERNAL_SERVER_ERROR)def_notify_websocket(selfuser_idmessage_typedata):try:channel_layer=get_channel_layer()async_to_sync(channel_layer.group_send)(f"chat_{user_id}"{"type":message_type"message":data})exceptExceptionase:logger.error(f"FailedtosendWebSocketnotification:{str(e)}")@transaction.atomicdefprocess_chat_request(selfrequestuser_message:strconversation_id=None):"""Processchatrequestwitherrorhandlingandreal-timeupdates"""try:#Getorcreateconversationuser=request.userconversation=Noneifconversation_id:conversation=ChatConversation.objects.select_for_update().get(id=conversation_iduser=user)else:conversation=ChatConversation.objects.create(user=usertitle=f"Conversation{timezone.now().strftime('%Y-%m-%d%H:%M')}"user_role=user.role)#Createusermessageuser_chat_message=ChatMessage.objects.create(conversation=conversationrole="user"content=user_messagestatus="received")#Notifyaboutreceivedmessageself._notify_websocket(user.id"message_status"{"message_id":user_chat_message.id"status":"received"})#Getcontextforchatservicecontext=self._get_cached_context(user)#GenerateAIresponsetry:ai_response=self.chat_service.get_response(user_messagecontext)#CreateAImessageai_chat_message=ChatMessage.objects.create(conversation=conversationrole="assistant"content=ai_response["content"]status="completed"metadata=ai_response.get("metadata"{}))#Updateconversationcontextandlastactivityconversation.context={**(conversation.contextor{})**ai_response.get("metadata"{})}conversation.last_activity=timezone.now()conversation.save()#Trackuseractivityself._track_user_activity(user)return{"conversation_id":conversation.id"message_id":ai_chat_message.id"content":ai_response["content"]"metadata":ai_response.get("metadata"{})"suggested_questions":ai_response.get("suggested_questions"[])}exceptExceptionase:error_message=f"FailedtogenerateAIresponse:{str(e)}"logger.error(error_message)#CreateerrormessageChatMessage.objects.create(conversation=conversationrole="system"content="SorryIencounteredanerrorgeneratingaresponse.Pleasetryagain."status="error"metadata={"error":str(e)})raiseResponseGenerationError(error_message)exceptChatConversation.DoesNotExist:raiseexceptExceptionase:logger.error(f"Errorprocessingchatrequest:{str(e)}")raisedefget_or_create_conversation(selfuserconversation_id=None):"""Getexistingconversationorcreatenewone"""ifconversation_id:returnChatConversation.objects.select_for_update().get(id=conversation_iduser=user)returnChatConversation.objects.create(user=usertitle=f"Conversation{timezone.now().strftime('%Y-%m-%d%H:%M')}"user_role=user.role)def_get_cached_context(selfuser)->dict:"""Getcachedusercontextorgeneratenew"""cache_key=f"user_context_{user.id}"context=cache.get(cache_key)ifnotcontext:context=self.chat_service._build_context(user)cache.set(cache_keycontextself.CACHE_TIMEOUT)returncontextdef_invalidate_context_cache(selfuser):"""Invalidateusercontextcache"""cache_key=f"user_context_{user.id}"cache.delete(cache_key)def_track_user_activity(selfuser):"""Trackuserchatactivity"""try:user.last_activity=timezone.now()user.chat_activity_count=F("chat_activity_count")+1user.save(update_fields=["last_activity""chat_activity_count"])self._invalidate_context_cache(user)exceptExceptionase:logger.error(f"Errortrackinguseractivity:{str(e)}")classChatMessageViewSet(viewsets.ModelViewSet):serializer_class=ChatMessageSerializerpermission_classes=[permissions.IsAuthenticated]defget_queryset(self):returnChatMessage.objects.filter(conversation__user=self.request.user)defperform_create(selfserializer):message=serializer.save()ifmessage.role=="user":message.update_learning_indicators()classChatSessionViewSet(viewsets.ModelViewSet):serializer_class=ChatSessionSerializerpermission_classes=[permissions.IsAuthenticated]defget_queryset(self):returnChatSession.objects.filter(user=self.request.user)@action(detail=Truemethods=["post"])defend_session(selfrequestpk=None):"""Endachatsession"""session=self.get_object()session.is_active=Falsesession.save()returnResponse({"status":"success"})@action(detail=Falsemethods=["get"])defactive_session(selfrequest):"""Getorcreateanactivechatsession"""session=ChatSession.objects.filter(user=request.useris_active=True).first()ifnotsession:session=ChatSession.objects.create(user=request.usercontext={"role":request.user.role"courses":list(request.user.courses.values("id""code""name"))})serializer=self.get_serializer(session)returnResponse(serializer.data)@authentication_classes([JWTAuthentication])@permission_classes([permissions.IsAuthenticated])classUserLearningProfileViewSet(viewsets.ModelViewSet):serializer_class=UserLearningProfileSerializerpermission_classes=[permissions.IsAuthenticated]authentication_classes=[JWTAuthentication]defget_queryset(self):returnUserLearningProfile.objects.filter(user=self.request.user)defget_object(self):profilecreated=UserLearningProfile.objects.get_or_create(user=self.request.user)returnprofile@action(detail=Falsemethods=["get"])deflearning_stats(selfrequest):"""Getlearningstylestatisticsandrecommendations"""profile=self.get_object()analyzer=LearningPatternAnalyzer(request.user)stats={"learning_style":{"primary":profile.primary_learning_style"secondary":profile.secondary_learning_style}"preferences":{"step_by_step":profile.prefers_step_by_step"examples":profile.prefers_examples"visual_aids":profile.prefers_visual_aids}"comprehension_level":profile.comprehension_level"interaction_patterns":profile.interaction_patternsor{}"recommendations":{"suggested_learning_methods":self._get_learning_recommendations(profile)"study_tips":self._get_study_tips(profile)}}returnResponse(stats)def_get_learning_recommendations(selfprofile):"""Generatepersonalizedlearningmethodrecommendations"""recommendations=[]#Learningstylebasedrecommendationsifprofile.primary_learning_style=="VISUAL":recommendations.extend(["Usemindmapsanddiagrams""Watcheducationalvideos""Createvisualsummaries""Usecolor-codinginnotes"])elifprofile.primary_learning_style=="AUDITORY":recommendations.extend(["Recordandlistentolectures""Participateingroupdiscussions""Useverbalrepetition""Explainconceptstoothers"])elifprofile.primary_learning_style=="READ_WRITE":recommendations.extend(["Takedetailednotes""Rewritekeypointsinyourownwords""Createwrittensummaries""Usetext-basedresources"])elifprofile.primary_learning_style=="KINESTHETIC":recommendations.extend(["Practicehands-onexercises""Usereal-worldexamples""Createphysicalmodels""Takebreaksformovement"])#Addrecommendationsbasedonpreferencesifprofile.prefers_step_by_step:recommendations.append("Breakdowncomplextopicsintosmallersteps")ifprofile.prefers_examples:recommendations.append("Workthroughmultipleexamplesbeforemovingon")ifprofile.prefers_visual_aids:recommendations.append("Createorfindvisualrepresentationsofconcepts")returnrecommendations[:5]#Returntop5recommendationsdef_get_study_tips(selfprofile):"""Generatepersonalizedstudytips"""tips=[]#Basetipsoncomprehensionlevelifprofile.comprehension_level=="BASIC":tips.extend(["Focusonfoundationalconceptsfirst""Takemorefrequentshorterstudysessions""Usesimplifiedexplanations""Askforclarificationwhenneeded"])elifprofile.comprehension_level=="INTERMEDIATE":tips.extend(["Connectnewconceptswithexistingknowledge""Practiceactiverecalltechniques""Varyyourstudymethods""Seekoutadditionalresources"])else:#ADVANCEDtips.extend(["Challengeyourselfwithcomplexproblems""Teachconceptstoothers""Exploreadvancedtopicsindependently""Lookforconnectionsbetweendifferentsubjects"])#Addtipsbasedoninteractionpatternspatterns=profile.interaction_patternsor{}ifpatterns.get("followup_questions"0)>0.7:tips.append("Continueaskingdeeperquestionsabouttopics")ifpatterns.get("detail_level"0)>0.7:tips.append("Breakdowncomplextopicsintodetailedcomponents")returntips[:5]#Returntop5tips@method_decorator(csrf_exemptname="dispatch")classPathAdvisorChatBotView(APIView):"""APIendpointforthePathAdvisorChatBot"""permission_classes=[permissions.AllowAny]#AllowunauthenticatedaccessforassessmentCACHE_TIMEOUT=300#5minutesdef__init__(self*args**kwargs):super().__init__(*args**kwargs)try:self.path_advisor_chatbot=path_advisor_chatbotexceptExceptionase:logger.error(f"Failedtoinitializepathadvisorchatbot:{str(e)}")self.path_advisor_chatbot=Nonedefpost(selfrequest):try:ifnotself.path_advisor_chatbot:raiseChatServiceError("Pathadvisorchatbotisnotproperlyinitialized")#Validateinputif"message"notinrequest.data:returnResponse({"error":"Messageisrequired"}status=status.HTTP_400_BAD_REQUEST)user_message=request.data["message"]conversation_id=request.data.get("conversation_id")assessment_data=request.data.get("assessment_data")#Getlanguagefromrequestlanguage=request.data.get("language""en")#Buildcontextcontext={"user_role":(getattr(request.user"role""STUDENT")ifrequest.user.is_authenticatedelse"STUDENT")"language":language}#Addassessmentdatatocontextifprovidedifassessment_data:context["assessment_data"]=assessment_data#Addpathrecommendationtocontextifavailableifconversation_idandrequest.user.is_authenticated:try:conversation=ChatConversation.objects.get(id=conversation_iduser=request.user)if(conversation.contextand"path_recommendation"inconversation.context):context["path_recommendation"]=conversation.context["path_recommendation"]exceptChatConversation.DoesNotExist:pass#Processthechatrequesttry:#Generateresponsefrompathadvisorchatbotresponse=self.path_advisor_chatbot.get_chat_response(user_messagecontext)#Ifuserisauthenticatedsavetheconversationifrequest.user.is_authenticated:conversation=self._save_conversation(request.useruser_messageresponseconversation_id)returnResponse({"conversation_id":conversation.id"content":response["content"]"metadata":response.get("metadata"{})"suggested_questions":response.get("suggested_questions"[])})else:#ForunauthenticatedusersjustreturntheresponsereturnResponse({"content":response["content"]"metadata":response.get("metadata"{})"suggested_questions":response.get("suggested_questions"[])})exceptExceptionase:logger.error(f"Errorgeneratingpathadvisorresponse:{str(e)}")returnResponse({"error":"Failedtogenerateresponse""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)exceptExceptionase:logger.error(f"Unexpectederrorinpathadvisorchatbotview:{str(e)}")returnResponse({"error":"Internalservererror""message":"Anunexpectederroroccurred"}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@transaction.atomicdef_save_conversation(selfuseruser_messageai_responseconversation_id=None):"""Savetheconversationforauthenticatedusers"""#Getorcreateconversationifconversation_id:try:conversation=ChatConversation.objects.select_for_update().get(id=conversation_iduser=user)exceptChatConversation.DoesNotExist:conversation=ChatConversation.objects.create(user=usertitle="PathAdvisorChat"user_role=user.role)else:conversation=ChatConversation.objects.create(user=usertitle="PathAdvisorChat"user_role=user.role)#CreateusermessageChatMessage.objects.create(conversation=conversationrole="user"content=user_messagestatus="received")#CreateAImessageChatMessage.objects.create(conversation=conversationrole="assistant"content=ai_response["content"]status="completed"metadata=ai_response.get("metadata"{}))#Updateconversationcontextconversation.context={**(conversation.contextor{})**ai_response.get("metadata"{})}conversation.save()returnconversation
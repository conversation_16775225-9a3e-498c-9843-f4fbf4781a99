from django.dbimpor t modelsfrom django.contrib.authimpor t get_user_modelUser = get_user_model()class OfficeHours(models.Model):
    """professor = models.For eignKey(Useron_delete = models.CASCADErelated_name ="office_hours")day = models.CharField(max_length =3)#Useabbreviationslike'Mon''Tue''Wed'etc.start_time = models.TimeField()end_time = models.TimeField()location = models.CharField(max_length =255)is_online = models.BooleanField(def ault = False)meeting_link = models.URLField(blank = Truenull = True)notes = models.TextField(blank = True)class Meta:
    """or dering =['professor''day''start_time']def __str__(self):
        """return f"{self.professor.get_full_name()}-{self.day}{self.start_time}-{self.end_time}"
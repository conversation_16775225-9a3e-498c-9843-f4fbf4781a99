"""Multi-AgentCourseRecommendationSystemUsesspecializedAIagentstoprovidepersonalizedcourserecommendations"""import loggingfromtypingimportAnyDictListOptionalfrom django.appsimportappsfrom django.contrib.authimportget_user_modellogger=logging.getLogger(__name__)classMultiAgentCourseRecommender:"""Enhancedcourserecommendationsystemusingmulti-agentAI"""def__init__(self):self.agents_available=Falsetry:from utils.enhanced_ai_agent_systemimport(advisor_agentassessor_agentcontent_creator_agentlanguage_tutor_agentmath_tutor_agentscience_tutor_agenttutor_agent)self.advisor_agent=advisor_agentself.assessor_agent=assessor_agentself.content_creator_agent=content_creator_agentself.tutor_agent=tutor_agentself.math_tutor_agent=math_tutor_agentself.science_tutor_agent=science_tutor_agentself.language_tutor_agent=language_tutor_agentself.agents_available=Truelogger.info("Multi-agentsystemloadedsuccessfullyforcourserecommendations")exceptImportErrorase:logger.warning(f"Multi-agentsystemnotavailable:{e}")self.agents_available=Falsedefget_personalized_recommendations(selfusercontext:Dict[strAny]=None)->Dict[strAny]:"""Getpersonalizedcourserecommendationsusingmulti-agentsystemArgs:user:Userobjectcontext:Additionalcontext(assessmentresultspreferencesetc.)Returns:Dictcontainingrecommendationsfromdifferentagents"""ifcontextisNone:context={}#Prepareusercontextuser_context=self._prepare_user_context(usercontext)recommendations={"status":"success""user_role":getattr(user"role""STUDENT")"recommendations":{}"agents_used":[]"fallback_used":notself.agents_available}ifself.agents_available:#Getrecommendationsfromdifferentagentsbasedonuserroleandneedsrecommendations["recommendations"]=self._get_multi_agent_recommendations(user_context)recommendations["agents_used"]=self._get_agents_used(user_context)else:#Fallbacktotraditionalrecommendationsystemrecommendations["recommendations"]=self._get_fallback_recommendations(usercontext)returnrecommendationsdef_prepare_user_context(selfusercontext:Dict[strAny])->Dict[strAny]:"""Preparecomprehensiveusercontextforagents"""user_context={"user_id":user.id"user_role":getattr(user"role""STUDENT")"username":user.username"email":user.email**context}#Addassessmentdataifavailabletry:Assessment=apps.get_model("assessment""Assessment")latest_assessment=(Assessment.objects.filter(student=user).order_by("-created_at").first())iflatest_assessment:user_context["latest_assessment"]={"score":latest_assessment.score"level":getattr(latest_assessment"level""beginner")"assessment_type":getattr(latest_assessment"assessment_type""general")}exceptExceptionase:logger.warning(f"Couldnotfetchassessmentdata:{e}")#Addenrolledcoursestry:Course=apps.get_model("courses""Course")enrolled_courses=Course.objects.filter(enrollments__student=user)user_context["enrolled_courses"]=[{"id":course.id"name":course.name"category":getattr(course"category""general")"level":getattr(course"level"1)}forcourseinenrolled_courses[:5]#Limittorecent5]exceptExceptionase:logger.warning(f"Couldnotfetchenrolledcourses:{e}")user_context["enrolled_courses"]=[]returnuser_contextdef_get_multi_agent_recommendations(selfuser_context:Dict[strAny])->Dict[strAny]:"""GetrecommendationsfrommultipleAIagents"""recommendations={}user_role=user_context.get("user_role""STUDENT")#1.Career/AcademicPathRecommendations(AdvisorAgent)try:career_request={"type":"course_recommendation""user_profile":user_context"focus":"career_alignment"}career_response=self.advisor_agent.process_request(career_requestuser_context)recommendations["career_aligned"]={"agent":"advisor_agent""recommendations":self._parse_agent_recommendations(career_response.content)"reasoning":career_response.reasoning"confidence":career_response.confidence}exceptExceptionase:logger.error(f"Errorgettingcareerrecommendations:{e}")#2.Assessment-BasedRecommendations(AssessorAgent)ifuser_context.get("latest_assessment"):try:assessment_request={"type":"course_recommendation""assessment_data":user_context["latest_assessment"]"focus":"skill_improvement"}assessment_response=self.assessor_agent.process_request(assessment_requestuser_context)recommendations["skill_improvement"]={"agent":"assessor_agent""recommendations":self._parse_agent_recommendations(assessment_response.content)"reasoning":assessment_response.reasoning"confidence":assessment_response.confidence}exceptExceptionase:logger.error(f"Errorgettingassessment-basedrecommendations:{e}")#3.Subject-SpecificRecommendationsifuser_role=="STUDENT":#Mathcourses(MathTutorAgent)try:math_request={"type":"course_recommendation""subject":"mathematics""user_profile":user_context}math_response=self.math_tutor_agent.process_request(math_requestuser_context)recommendations["mathematics"]={"agent":"math_tutor_agent""recommendations":self._parse_agent_recommendations(math_response.content)"reasoning":math_response.reasoning"confidence":math_response.confidence}exceptExceptionase:logger.error(f"Errorgettingmathrecommendations:{e}")#Sciencecourses(ScienceTutorAgent)try:science_request={"type":"course_recommendation""subject":"science""user_profile":user_context}science_response=self.science_tutor_agent.process_request(science_requestuser_context)recommendations["science"]={"agent":"science_tutor_agent""recommendations":self._parse_agent_recommendations(science_response.content)"reasoning":science_response.reasoning"confidence":science_response.confidence}exceptExceptionase:logger.error(f"Errorgettingsciencerecommendations:{e}")#4.ContentCreationRecommendations(forProfessors/Admins)ifuser_rolein["PROFESSOR""ADMIN"]:try:content_request={"type":"course_recommendation""focus":"content_creation""user_profile":user_context}content_response=self.content_creator_agent.process_request(content_requestuser_context)recommendations["content_creation"]={"agent":"content_creator_agent""recommendations":self._parse_agent_recommendations(content_response.content)"reasoning":content_response.reasoning"confidence":content_response.confidence}exceptExceptionase:logger.error(f"Errorgettingcontentcreationrecommendations:{e}")returnrecommendationsdef_parse_agent_recommendations(selfagent_content:str)->List[Dict[strAny]]:"""Parseagentresponsecontentintostructuredrecommendations"""#Thisisasimplifiedparser-inproductionyou'dwantmoresophisticatedparsingrecommendations=[]try:#Lookforcoursenamessubjectsorstructureddataintheresponselines=agent_content.split("\n")forlineinlines:line=line.strip()iflineand("course"inline.lower()or"recommend"inline.lower()):recommendations.append({"title":line"description":line"priority":"medium""source":"ai_agent"})exceptExceptionase:logger.error(f"Errorparsingagentrecommendations:{e}")#Ifnostructuredrecommendationsfoundcreateageneraloneifnotrecommendations:recommendations.append({"title":"AI-GeneratedRecommendation""description":(agent_content[:200]+"..."iflen(agent_content)>200elseagent_content)"priority":"medium""source":"ai_agent"})returnrecommendationsdef_get_agents_used(selfuser_context:Dict[strAny])->List[str]:"""Getlistofagentsusedforrecommendations"""agents_used=["advisor_agent"]ifuser_context.get("latest_assessment"):agents_used.append("assessor_agent")user_role=user_context.get("user_role""STUDENT")ifuser_role=="STUDENT":agents_used.extend(["math_tutor_agent""science_tutor_agent"])elifuser_rolein["PROFESSOR""ADMIN"]:agents_used.append("content_creator_agent")returnagents_useddef_get_fallback_recommendations(selfusercontext:Dict[strAny])->Dict[strAny]:"""Fallbackrecommendationsystemwhenagentsarenotavailable"""try:Course=apps.get_model("courses""Course")#Getpopularcoursespopular_courses=Course.objects.filter(is_active=True).order_by("-id")[:5]fallback_recommendations={"general":{"agent":"fallback_system""recommendations":[{"title":course.name"description":getattr(course"description""Coursedescription")"priority":"medium""source":"database""course_id":course.id}forcourseinpopular_courses]"reasoning":"Fallbacksystemprovidingpopularcourses""confidence":0.5}}returnfallback_recommendationsexceptExceptionase:logger.error(f"Errorinfallbackrecommendations:{e}")return{"general":{"agent":"fallback_system""recommendations":[{"title":"ExploreAvailableCourses""description":"Browseourcoursecatalogtofindcoursesthatmatchyourinterests""priority":"low""source":"system"}]"reasoning":"Systemfallbackmessage""confidence":0.3}}#Globalinstancemulti_agent_course_recommender=MultiAgentCourseRecommender()
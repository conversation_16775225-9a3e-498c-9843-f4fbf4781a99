# North Star University - Full Stack .gitignore

# ===== BACKEND (Django/Python) =====

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/
static/

# Environment variables
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Django migrations (optional - uncomment if you want to ignore)
# */migrations/*.py
# !*/migrations/__init__.py

# ===== FRONTEND (React/Node.js) =====

# Dependencies
frontend/node_modules/
frontend/.pnp
frontend/.pnp.js

# Production builds
frontend/build/
frontend/dist/

# Environment variables
frontend/.env
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

# Logs
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*
frontend/lerna-debug.log*
frontend/.pnpm-debug.log*

# Runtime data
frontend/pids
frontend/*.pid
frontend/*.seed
frontend/*.pid.lock

# Coverage directory used by tools like istanbul
frontend/coverage/
frontend/*.lcov

# ESLint cache
frontend/.eslintcache

# Optional npm cache directory
frontend/.npm

# Optional eslint cache
frontend/.eslintcache

# Microbundle cache
frontend/.rpt2_cache/
frontend/.rts2_cache_cjs/
frontend/.rts2_cache_es/
frontend/.rts2_cache_umd/

# Optional REPL history
frontend/.node_repl_history

# Output of 'npm pack'
frontend/*.tgz

# Yarn Integrity file
frontend/.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
frontend/.cache
frontend/.parcel-cache

# Next.js build output
frontend/.next
frontend/out

# Nuxt.js build / generate output
frontend/.nuxt
frontend/dist

# Gatsby files
frontend/.cache/
frontend/public

# Storybook build outputs
frontend/.out
frontend/.storybook-out

# Temporary folders
frontend/tmp/
frontend/temp/

# ===== DEVELOPMENT TOOLS =====

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ===== PROJECT SPECIFIC =====

# Backups created by our code quality tools
backend/backups/

# Test coverage reports
backend/htmlcov/
backend/.coverage
backend/.pytest_cache/

# Jupyter Notebook
*.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# ===== SECURITY =====

# API keys and secrets
*.key
*.pem
*.p12
secrets.json
.secrets/

# Database dumps
*.sql
*.dump

# ===== TEMPORARY FILES =====

# Temporary files created during development
*.tmp
*.temp
*.bak
*.backup
*.orig

# Log files
*.log

# ===== DOCUMENTATION =====

# Generated documentation
docs/_build/
docs/build/

# ===== TESTING =====

# Test results
test-results/
coverage/
.nyc_output/

# ===== DEPLOYMENT =====

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes
*.yaml.local
*.yml.local

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# ===== MISC =====

# Compressed files
*.zip
*.tar.gz
*.rar

# Large files that shouldn't be in git
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm
*.mkv

# Audio files
*.mp3
*.wav
*.flac
*.aac

# Large images
*.psd
*.ai
*.eps

# Archives
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

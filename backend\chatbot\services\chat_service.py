import json
import loggingimport osimport refromdatetimeimport datetimetimedeltafromtypingimportAnyDictListOptionalfrom django.appsimportappsfrom django.db.modelsimportAvgCountQfrom django.db.models.functionsimportRoundfrom django.utilsimport timezonefrom grades.modelsimportAssignmentCourseGradeGradeSubmissionfrom users.modelsimportCustomUser#ImportstandardizedAIserviceutilitiesfrom utils.ai_service_utilsimportStandardizedAIServiceget_ai_loggerAIServiceErrorResponseGenerationErrorfrom utils.ai.servicesimportget_ai_service#ImportimprovedAIservicefrom utils.ai_service_improvedimportimproved_ai_servicefrom..modelsimportChatConversationChatMessageUserLearningProfilefrom.learning_pattern_analyzerimportLearningPatternAnalyzer#Getmodelsdynamicallytoavoidcircularimportstry:Course=apps.get_model("courses""Course")exceptLookupError:Course=Nonetry:Enrollment=apps.get_model("courses""Enrollment")exceptLookupError:Enrollment=Nonetry:CourseProgress=apps.get_model("courses""CourseProgress")exceptLookupError:CourseProgress=Nonetry:Attendance=apps.get_model("courses""Attendance")exceptLookupError:Attendance=None#Usestandardizedloggerlogger=get_ai_logger("chatbot_service")#UsestandardizederrorclassesfromconsolidatedAIserviceclassChatServiceError(AIServiceError):"""Baseexceptionforchatserviceerrors"""passclassConfigurationError(ChatServiceError):"""Raisedwhenthereareconfigurationissues"""passclassAPIConnectionError(ChatServiceError):"""RaisedwhenthereareconnectionissueswiththeAPI"""passclassGeminiChatService(StandardizedAIService):"""ServiceforhandlingchatinteractionsusingGeminiAPI"""def__init__(self):"""Initializethechatservicewithstandardizedconfiguration"""super().__init__("gemini_chat_service")logger.info(f"GeminiChatServiceinitializedwithconsolidatedAIservice")#Registerwithimprovedconfigurationmanagertry:from utils.ai_config_simplifiedimportconfig_manager#Note:Improvedconfigmanagerhandlesregistrationautomaticallylogger.info("ChatserviceusingimprovedAIconfigurationmanager")exceptExceptionase:logger.warning(f"CouldnotaccessimprovedAIconfigmanager:{e}")defget_response(selfmessage:strcontext:Dict)->Dict:"""GenerateAIresponsewithlearning-styleadaptationsusingconsolidatedAIagents"""try:#Setdefaultformatpreferencesformat_prefs={"detail_level":"standard""step_by_step":True"include_examples":True"use_visual_aids":True"chunk_size":3"interaction_style":"collaborative"}#Determineifthisisatutoringrequesttutoring_keywords=["help""explain""teach""learn""understand""howto""whatis"]is_tutoring_request=any(keywordinmessage.lower()forkeywordintutoring_keywords)ifis_tutoring_request:#Usetheconsolidatedtutoringsystemtry:#UsetheconsolidatedAIservice'stutoringresponsefunctiontutoring_prompt=f"""Youareanexperttutor.Pleaseprovideacomprehensiveanswertothisstudentquestion:Question:{message}Subject:{context.get("subject""General")}StudentLevel:{context.get("learning_profile"{}).get("comprehension_level""intermediate")}Pleasetailoryourresponsetothestudent'slevelandprovideclearexplanations."""response_text=self.ai_service.generate_content(tutoring_prompt)suggested_questions=self._generate_suggested_questions(messageresponse_textcontext)exceptExceptionase:logger.warning(f"TutoringservicefailedfallingbacktogeneralAI:{e}")#FallbacktoimprovedAIservicesystem_prompt=self._build_system_prompt(contextformat_prefs)full_prompt=f"{system_prompt}\n\nUser:{message}"response_text=improved_ai_service.generate_content(prompt=full_promptservice_type="chatbot"user_id=context.get("user_id"))suggested_questions=self._generate_suggested_questions(messageresponse_textcontext)else:#UseimprovedAIserviceforgeneralchatsystem_prompt=self._build_system_prompt(contextformat_prefs)full_prompt=f"{system_prompt}\n\nUser:{message}"response_text=improved_ai_service.generate_content(prompt=full_promptservice_type="chatbot"user_id=context.get("user_id"))suggested_questions=self._generate_suggested_questions(messageresponse_textcontext)#Formattheresponseformatted={"main_content":response_text"dynamic_content":{}}return{"content":formatted["main_content"]"metadata":{"format_recommendations":format_prefs"dynamic_content":formatted["dynamic_content"]"learning_indicators":self._analyze_learning_indicators(messagecontext)"ai_agent_used":("tutoring_agent"ifis_tutoring_requestelse"unified_ai")}"suggested_questions":suggested_questions}exceptExceptionase:logger.error(f"Erroringet_response:{str(e)}")raiseResponseGenerationError(str(e))def_build_system_prompt(selfcontext:Dictformat_prefs:Dict)->str:"""BuildasystempromptwithcontextfortheAI"""learning_profile=context.get("learning_profile"{})#Createasystempromptthatincludescontextandformattingpreferencessystem_prompt="""YouareanAIeducationalassistantforauniversityplatform.Yourgoalistoprovidehelpfulaccurateandeducationalresponsestostudentquestions."""#Addlearningprofileinformationifavailableiflearning_profile:system_prompt+=f"""Learningprofile:-Primarylearningstyle:{learning_profile.get('primary_learning_style''VISUAL')}-Comprehensionlevel:{learning_profile.get('comprehension_level''INTERMEDIATE')}-Pacepreference:{learning_profile.get('pace_preference''MODERATE')}-Interactionstyle:{learning_profile.get('interaction_style''COLLABORATIVE')}"""#Addformattingpreferencessystem_prompt+=f"""Formatyourresponseswith:-Detaillevel:{format_prefs.get('detail_level''standard')}-Stepbystepexplanations:{'Yes'ifformat_prefs.get('step_by_step'True)else'No'}-Includeexamples:{'Yes'ifformat_prefs.get('include_examples'True)else'No'}-Usevisualdescriptions:{'Yes'ifformat_prefs.get('use_visual_aids'True)else'No'}-Interactionstyle:{format_prefs.get('interaction_style''collaborative')}"""returnsystem_promptdef_build_prompt(selfmessage:strcontext:Dictformat_prefs:Dict)->str:"""Buildacontext-awareprompt"""learning_profile=context.get("learning_profile"{})prompt=f"""AsanAIeducationalassistantpleasehelpexplainthistopic:"{message}"Considerthefollowingcontext:-Learningstyle:{learning_profile.get('primary_learning_style''VISUAL')}-Comprehensionlevel:{learning_profile.get('comprehension_level''INTERMEDIATE')}-Interactionstyle:{learning_profile.get('interaction_style''COLLABORATIVE')}-Pacepreference:{learning_profile.get('pace_preference''MODERATE')}Pleaseformatyourresponse:-Use{'detailed'ifformat_prefs['detail_level']=='detailed'else'concise'}explanations-{'Breakdownconceptsstepbystep'ifformat_prefs['step_by_step']else'Provideaflowingnarrative'}-{'Includepracticalexamples'ifformat_prefs['include_examples']else'Focusontheory'}-{'Usebulletpointsandlistsforclarity'ifformat_prefs['use_visual_aids']else'Useparagraphform'}Aimtopromoteactivelearningandcriticalthinking."""returnpromptdef_format_response_for_learning_style(selfresponse_text:strformat_prefs:Dictcontext:Dict)->Dict[strAny]:"""Formatresponsebasedonlearningstylepreferences"""learning_profile=context.get("learning_profile"{})#Applybasicformattingparagraphs=response_text.split("\n\n")formatted_content=[]#Processeachparagraphbasedonpreferencesforpinparagraphs:ifp.strip():#Addvisualmarkersforstepsifneededifformat_prefs["step_by_step"]andnotp.startswith(("•""-""*""1.")):p="•"+pformatted_content.append(p)main_content="\n\n".join(formatted_content)#Adddynamiccontentbasedonlearningstyledynamic_content={"visual_aids":([]iflearning_profile.get("primary_learning_style")=="VISUAL"elseNone)"practice_exercises":[]ifformat_prefs["include_examples"]elseNone"key_points":(self._extract_key_points(main_content)ifformat_prefs["use_visual_aids"]elseNone)}return{"main_content":main_content"dynamic_content":dynamic_content}def_extract_key_points(selfcontent:str)->List[str]:"""Extractkeypointsfromcontent"""points=[]lines=content.split("\n")forlineinlines:line=line.strip()ifline.startswith(("•""-""*""1."))orany(phraseinline.lower()forphrasein["keypoint""important""note:""remember:"]):points.append(line.lstrip("•-*1234567890."))returnpoints[:5]#Returntop5keypointsdef_analyze_learning_indicators(selfmessage:strcontext:Dict)->Dict[strfloat]:"""Analyzemessageforlearningstyleindicators"""#Getlearningprofilefromcontextlearning_profile=context.get("learning_profile"{})#Convertlearningstylepreferencestonumericvaluesstyle_to_value={"VISUAL":{"visual_preference":0.8"example_preference":0.6}"AUDITORY":{"interaction_style":0.7"step_preference":0.6}"KINESTHETIC":{"example_preference":0.8"step_preference":0.7}"READING":{"detail_level":0.8"followup_questions":0.6}}#Getdefaultvaluesbasedonprimarylearningstyleprimary_style=learning_profile.get("primary_learning_style""VISUAL")indicators=style_to_value.get(primary_style{})#Setdefaultvaluesforallindicatorsreturn{"visual_preference":indicators.get("visual_preference"0.5)"example_preference":indicators.get("example_preference"0.5)"step_preference":indicators.get("step_preference"0.5)"detail_level":indicators.get("detail_level"0.5)"interaction_style":indicators.get("interaction_style"0.5)"topic_complexity":0.5#Defaultvalue"followup_questions":indicators.get("followup_questions"0.5)"pace_preference":learning_profile.get("pace_preference"0.5)}def_generate_suggested_questions(selforiginal_message:strresponse:strcontext:Dict)->List[str]:"""Generatecontextuallyrelevantfollow-upquestions"""prompt=f"""Basedonthisconversation:Originalquestion:{original_message}Responsesummary:{response[:200]}...Generate3relevantfollow-upquestionsthatwouldhelptheuserbetterunderstandthetopic.Considertheirlearningstyle({context.get('learning_profile'{}).get('primary_learning_style''Notset')})andcurrentcomprehensionlevel({context.get('learning_profile'{}).get('comprehension_level''INTERMEDIATE')}).Formateachquestiononanewlinewithoutnumbering."""try:#UsetheimprovedAIservicecontent=improved_ai_service.generate_content(prompt=promptservice_type="chatbot"user_id=context.get("user_id"))questions=[q.strip()forqincontent.split("\n")ifq.strip()and"?"inq]returnquestions[:3]#Limitto3questionsexceptExceptionase:logger.error(f"Errorgeneratingsuggestedquestions:{str(e)}")return[]def_build_context(selfuser)->dict:"""Buildcontextforthechatbasedonuserinformation"""context={"user_role":user.role"learning_profile":self._get_learning_profile(user)"courses":self._get_user_courses(user)"preferences":self._get_user_preferences(user)}returncontextdef_get_learning_profile(selfuser):"""Getuser'slearningprofile"""try:profile=UserLearningProfile.objects.get(user=user)return{"primary_learning_style":profile.primary_learning_style"secondary_learning_style":profile.secondary_learning_style"comprehension_level":profile.comprehension_level"pace_preference":profile.pace_preference"interaction_style":profile.interaction_style}exceptUserLearningProfile.DoesNotExist:return{"primary_learning_style":"VISUAL""comprehension_level":"INTERMEDIATE""pace_preference":"MODERATE""interaction_style":"COLLABORATIVE"}def_get_user_courses(selfuser):"""Getuser'scoursesbasedontheirrole"""try:ifuser.role=="STUDENT":enrollments=Enrollment.objects.filter(student=useractive=True)return[{"id":e.course.id"name":e.course.name"progress":CourseProgress.objects.get(student=usercourse=e.course).progress}foreinenrollments]elifuser.rolein["PROFESSOR""ADMIN"]:courses=Course.objects.filter(instructor=user)return[{"id":c.id"name":c.name}forcincourses]return[]exceptExceptionase:logger.error(f"Errorgettingusercourses:{str(e)}")return[]def_get_user_preferences(selfuser=None):"""Getuser'schatpreferences"""#Thismethoddoesn'tactuallyusetheuserparameteryet#butit'skeptforfuturepersonalizationreturn{"detail_level":"detailed""example_preference":True"step_by_step":True"language_style":"formal"}defget_student_course_performance(selfuser):"""Getdetailedperformancestatisticsforastudentacrossallcourses"""ifuser.role!="STUDENT":returnNoneperformance_data=[]enrollments=Enrollment.objects.filter(user=user)forenrollmentinenrollments:course=enrollment.coursecourse_grade=CourseGrade.objects.filter(user=usercourse=course).first()assignments=Assignment.objects.filter(course=course)submissions=Submission.objects.filter(user=userassignment__course=course)attendance_records=Attendance.objects.filter(enrollment=enrollment)progress=CourseProgress.objects.filter(user=usercourse=course).first()attendance_rate=((attendance_records.filter(is_present=True).count()/attendance_records.count()*100)ifattendance_records.exists()else100)performance_data.append({"course_code":course.course_code"course_title":course.title"grade":course_grade.gradeifcourse_gradeelseNone"numeric_grade":(course_grade.numeric_gradeifcourse_gradeelseNone)"assignments_completed":submissions.count()"total_assignments":assignments.count()"attendance_rate":round(attendance_rate1)"attendance_streak":progress.attendance_streakifprogresselse1"absences_remaining":(progress.absences_remainingifprogresselse3)"last_access":(progress.last_accessed.strftime("%Y-%m-%d")ifprogressandprogress.last_accessedelseNone)"improvement_needed":bool(course_gradeandcourse_grade.numeric_grade<70)"professor_name":f"{course.instructor.first_name}{course.instructor.last_name}""next_assignment":self._get_next_assignment(courseuser)})returnperformance_datadefget_course_statistics(selfuser):"""Getdetailedstatisticsforallcoursesofaprofessor"""courses_data=[]total_students=0forcourseinuser.courses_taught.all():enrolled_count=Enrollment.objects.filter(course=course).count()total_students+=enrolled_countattendance_records=Attendance.objects.filter(course=course)ifattendance_records.exists():attendance_rate=(attendance_records.filter(is_present=True).count()/attendance_records.count())*100else:attendance_rate=0courses_data.append({"course_code":course.course_code"title":course.title"enrolled_count":enrolled_count"attendance_rate":round(attendance_rate1)})return{"courses":courses_data"total_students":total_students"average_class_size":(total_students/len(courses_data)ifcourses_dataelse0)}def_get_next_assignment(selfcourseuser):"""Getthenextupcomingassignmentforacourse"""next_assignment=(Assignment.objects.filter(course=coursedue_date__gt=timezone.now()).order_by("due_date").first())ifnext_assignment:return{"title":next_assignment.title"due_date":next_assignment.due_date.strftime("%Y-%m-%d")"days_left":(next_assignment.due_date-timezone.now()).days"submitted":Submission.objects.filter(user=userassignment=next_assignment).exists()}returnNonedef_calculate_overall_gpa(selfperformance_data):"""CalculateoverallGPAfromperformancedata"""grades=[p["numeric_grade"]forpinperformance_dataifp["numeric_grade"]]returnround(sum(grades)/len(grades)2)ifgradeselseNonedef_get_attendance_summary(selfuser):"""Getoverallattendancestatistics"""all_attendance=Attendance.objects.filter(enrollment__user=user)ifall_attendance.exists():present_count=all_attendance.filter(is_present=True).count()total_count=all_attendance.count()return{"overall_rate":round((present_count/total_count)*1001)"total_classes":total_count"classes_attended":present_count"classes_missed":total_count-present_count}returnNonedef_determine_academic_standing(selfperformance_data):"""Determinestudent'sacademicstandingbasedongradesandattendance"""ifnotperformance_data:return"Unknown"grades=[p["numeric_grade"]forpinperformance_dataifp["numeric_grade"]]attendance_rates=[p["attendance_rate"]forpinperformance_data]ifnotgrades:return"PendingGrades"avg_grade=sum(grades)/len(grades)avg_attendance=sum(attendance_rates)/len(attendance_rates)ifavg_grade>=90andavg_attendance>=90:return"Excellent"elifavg_grade>=80andavg_attendance>=80:return"Good"elifavg_grade>=70andavg_attendance>=70:return"Satisfactory"else:return"NeedsImprovement"defrefresh_configuration(self):"""Refreshthechatserviceconfiguration(calledbyconfigmanager)."""try:logger.info("Refreshingchatserviceconfiguration")#TheserviceinheritsfromStandardizedAIServicewhichwillautomatically#getthelatestconfigurationfromtheconsolidatedAIservicelogger.info("Chatserviceconfigurationrefreshedsuccessfully")exceptExceptionase:logger.error(f"Failedtorefreshchatserviceconfiguration:{e}")defrefresh_chat_service():"""RefreshthechatserviceAIconfiguration"""try:#TheGeminiChatServiceinheritsfromStandardizedAIService#whichautomaticallyusestheupdatedAIservicelogger.info("ChatservicenotifiedtouseupdatedAIconfiguration")exceptExceptionase:logger.error(f"Failedtorefreshchatservice:{e}")#The_generate_suggested_questionsmethodisalreadydefinedabove(lines246-277)#Thisisaduplicatemethodwithaslightlydifferentimplementation#Usingthecentralizedconfigurationfromthefirstimplementation
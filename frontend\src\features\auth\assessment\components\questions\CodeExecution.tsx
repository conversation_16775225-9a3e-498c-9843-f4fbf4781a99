import React, { useState, useEffect, useCallback, useRef } from 'react';
import Editor from '@monaco-editor/react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Chip,
  LinearProgress,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  PlayArrow,
  Stop,
  CheckCircle,
  Error,
  ExpandMore,
  Refresh,
  BugReport,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { TestCase } from '../../types';

interface CodeExecutionProps {
  codeTemplate: string;
  language: string;
  testCases: TestCase[];
  onCodeChange: (code: string) => void;
  isSubmitting: boolean;
  value?: string;
}

interface TestResult {
  testCase: TestCase;
  passed: boolean;
  output: string;
  error?: string;
  executionTime?: number;
}

interface ExecutionResult {
  success: boolean;
  results: TestResult[];
  overallScore: number;
  totalExecutionTime: number;
}

const CodeExecution: React.FC<CodeExecutionProps> = ({
  codeTemplate,
  language,
  testCases,
  onCodeChange,
  isSubmitting,
  value,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [code, setCode] = useState(value || codeTemplate);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionResult, setExecutionResult] = useState<ExecutionResult | null>(null);
  const [expandedTest, setExpandedTest] = useState<string | false>(false);
  const editorRef = useRef<any>(null);

  useEffect(() => {
    if (value && value !== code) {
      setCode(value);
    }
  }, [value]);

  const handleEditorChange = useCallback(
    (newCode: string | undefined) => {
      if (newCode !== undefined) {
        setCode(newCode);
        onCodeChange(newCode);
      }
    },
    [onCodeChange]
  );

  const handleEditorDidMount = useCallback((editor: any) => {
    editorRef.current = editor;
    
    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
      executeCode();
    });
  }, []);

  const resetCode = useCallback(() => {
    setCode(codeTemplate);
    onCodeChange(codeTemplate);
    setExecutionResult(null);
  }, [codeTemplate, onCodeChange]);

  // Mock code execution function - in real implementation, this would call a backend service
  const executeCode = useCallback(async () => {
    if (isExecuting || isSubmitting) return;

    setIsExecuting(true);
    setExecutionResult(null);

    try {
      // Simulate API call to Docker sandbox
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock execution results
      const results: TestResult[] = testCases.map((testCase, index) => {
        // Simple mock logic - in reality this would come from the backend
        const mockPassed = Math.random() > 0.3; // 70% pass rate for demo
        const mockOutput = mockPassed ? testCase.expectedOutput : 'Incorrect output';
        const mockExecutionTime = Math.random() * 100 + 10; // 10-110ms

        return {
          testCase,
          passed: mockPassed,
          output: mockOutput,
          executionTime: mockExecutionTime,
          error: mockPassed ? undefined : 'Logic error in implementation',
        };
      });

      const passedTests = results.filter(r => r.passed).length;
      const overallScore = (passedTests / results.length) * 100;
      const totalExecutionTime = results.reduce((sum, r) => sum + (r.executionTime || 0), 0);

      setExecutionResult({
        success: true,
        results,
        overallScore,
        totalExecutionTime,
      });
    } catch (error) {
      setExecutionResult({
        success: false,
        results: [],
        overallScore: 0,
        totalExecutionTime: 0,
      });
    } finally {
      setIsExecuting(false);
    }
  }, [code, testCases, isExecuting, isSubmitting]);

  const handleTestExpand = useCallback(
    (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpandedTest(isExpanded ? panel : false);
    },
    []
  );

  const getLanguageDisplayName = (lang: string) => {
    const langMap: Record<string, string> = {
      javascript: 'JavaScript',
      python: 'Python',
      java: 'Java',
      cpp: 'C++',
      c: 'C',
      csharp: 'C#',
      typescript: 'TypeScript',
    };
    return langMap[lang] || lang.toUpperCase();
  };

  return (
    <Box sx={{ mt: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="body2" color="text.secondary">
          {t('assessment.codeExecution.instruction', {
            language: getLanguageDisplayName(language),
          })}
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            startIcon={<Refresh />}
            onClick={resetCode}
            disabled={isExecuting || isSubmitting}
            size="small"
          >
            {t('assessment.codeExecution.reset')}
          </Button>
          
          <Button
            startIcon={isExecuting ? <Stop /> : <PlayArrow />}
            onClick={executeCode}
            disabled={isExecuting || isSubmitting}
            variant="contained"
            color={isExecuting ? 'secondary' : 'primary'}
            size="small"
          >
            {isExecuting
              ? t('assessment.codeExecution.executing')
              : t('assessment.codeExecution.run')}
          </Button>
        </Box>
      </Box>

      <Grid container spacing={2}>
        {/* Code Editor */}
        <Grid item xs={12} md={8}>
          <Paper elevation={1} sx={{ height: isMobile ? 300 : 400, overflow: 'hidden' }}>
            <Editor
              height="100%"
              language={language}
              value={code}
              onChange={handleEditorChange}
              onMount={handleEditorDidMount}
              theme={theme.palette.mode === 'dark' ? 'vs-dark' : 'light'}
              options={{
                minimap: { enabled: !isMobile },
                fontSize: isMobile ? 12 : 14,
                wordWrap: 'on',
                lineNumbers: 'on',
                folding: true,
                readOnly: isSubmitting,
                automaticLayout: true,
                scrollBeyondLastLine: false,
                accessibilitySupport: 'on',
              }}
            />
          </Paper>
          
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            {t('assessment.codeExecution.shortcut')}
          </Typography>
        </Grid>

        {/* Test Cases and Results */}
        <Grid item xs={12} md={4}>
          <Typography variant="subtitle2" gutterBottom>
            {t('assessment.codeExecution.testCases')}
          </Typography>

          {isExecuting && (
            <Box sx={{ mb: 2 }}>
              <LinearProgress />
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                {t('assessment.codeExecution.executingTests')}
              </Typography>
            </Box>
          )}

          {executionResult && (
            <Box sx={{ mb: 2 }}>
              <Alert
                severity={executionResult.overallScore >= 70 ? 'success' : 'warning'}
                icon={executionResult.overallScore >= 70 ? <CheckCircle /> : <BugReport />}
              >
                <Typography variant="body2">
                  {t('assessment.codeExecution.score', {
                    score: Math.round(executionResult.overallScore),
                    passed: executionResult.results.filter(r => r.passed).length,
                    total: executionResult.results.length,
                  })}
                </Typography>
                <Typography variant="caption">
                  {t('assessment.codeExecution.executionTime', {
                    time: Math.round(executionResult.totalExecutionTime),
                  })}
                </Typography>
              </Alert>
            </Box>
          )}

          {/* Test Case Details */}
          {testCases.map((testCase, index) => {
            const result = executionResult?.results.find(r => r.testCase.id === testCase.id);
            const panelId = `test-${testCase.id}`;
            
            return (
              <Accordion
                key={testCase.id}
                expanded={expandedTest === panelId}
                onChange={handleTestExpand(panelId)}
                sx={{ mb: 1 }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMore />}
                  sx={{
                    backgroundColor: result
                      ? result.passed
                        ? theme.palette.success.light
                        : theme.palette.error.light
                      : theme.palette.grey[100],
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                    {result && (
                      <Chip
                        icon={result.passed ? <CheckCircle /> : <Error />}
                        label={result.passed ? t('assessment.codeExecution.passed') : t('assessment.codeExecution.failed')}
                        size="small"
                        color={result.passed ? 'success' : 'error'}
                      />
                    )}
                    <Typography variant="body2">
                      {testCase.description || t('assessment.codeExecution.testCase', { number: index + 1 })}
                    </Typography>
                  </Box>
                </AccordionSummary>
                
                <AccordionDetails>
                  <Box sx={{ space: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      {t('assessment.codeExecution.input')}
                    </Typography>
                    <Paper variant="outlined" sx={{ p: 1, mb: 1, fontFamily: 'monospace', fontSize: '0.875rem' }}>
                      {testCase.input}
                    </Paper>
                    
                    <Typography variant="caption" color="text.secondary">
                      {t('assessment.codeExecution.expectedOutput')}
                    </Typography>
                    <Paper variant="outlined" sx={{ p: 1, mb: 1, fontFamily: 'monospace', fontSize: '0.875rem' }}>
                      {testCase.expectedOutput}
                    </Paper>
                    
                    {result && (
                      <>
                        <Typography variant="caption" color="text.secondary">
                          {t('assessment.codeExecution.actualOutput')}
                        </Typography>
                        <Paper 
                          variant="outlined" 
                          sx={{ 
                            p: 1, 
                            mb: 1, 
                            fontFamily: 'monospace', 
                            fontSize: '0.875rem',
                            backgroundColor: result.passed ? theme.palette.success.light : theme.palette.error.light,
                          }}
                        >
                          {result.output}
                        </Paper>
                        
                        {result.error && (
                          <>
                            <Typography variant="caption" color="error">
                              {t('assessment.codeExecution.error')}
                            </Typography>
                            <Paper variant="outlined" sx={{ p: 1, fontFamily: 'monospace', fontSize: '0.875rem', color: 'error.main' }}>
                              {result.error}
                            </Paper>
                          </>
                        )}
                      </>
                    )}
                  </Box>
                </AccordionDetails>
              </Accordion>
            );
          })}
        </Grid>
      </Grid>
    </Box>
  );
};

export default CodeExecution;

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tabs,
  Tab,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField
} from '@mui/material';
import {
  TrendingUp,
  Warning,
  CheckCircle,
  Error as ErrorIcon,
  School,
  People,
  Assessment,
  Timeline,
  PieChart,
  BarChart,
  Refresh,
  FileDownload,
  Insights
} from '@mui/icons-material';
import { predictiveAnalyticsService } from '../../services';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analytics-tabpanel-${index}`}
      aria-labelledby={`analytics-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const PredictiveAnalyticsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);

  // Dashboard State
  const [dashboard, setDashboard] = useState<any>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState('30_days');

  // Student Risk State
  const [riskAnalysis, setRiskAnalysis] = useState<any[]>([]);
  const [selectedStudent, setSelectedStudent] = useState('');
  const [studentRisk, setStudentRisk] = useState<any>(null);

  // Learning Patterns State
  const [learningPatterns, setLearningPatterns] = useState<any>(null);

  // Predictions State
  const [predictions, setPredictions] = useState<any[]>([]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const loadDashboard = async () => {
    setLoading(true);
    try {
      const dashboardData = await predictiveAnalyticsService.getInstitutionDashboard();
      setDashboard(dashboardData);
    } catch (error) {
      console.error('Error loading dashboard:', error);
      setMessage({ type: 'error', text: 'Failed to load dashboard data' });
    }
    setLoading(false);
  };

  const loadRiskAnalysis = async () => {
    setLoading(true);
    try {
      // Simulate loading risk analysis for multiple students
      const mockRiskData = [
        {
          studentId: 'student1',
          studentName: 'John Doe',
          riskLevel: 'high',
          riskFactors: {
            academicPerformance: 0.3,
            engagementLevel: 0.2,
            attendanceRate: 0.4,
            submissionTimeliness: 0.1,
            socialInteraction: 0.6
          },
          predictions: {
            dropoutProbability: 0.75,
            expectedGrade: 'D',
            completionTimeframe: 18
          },
          confidenceScore: 0.85
        },
        {
          studentId: 'student2',
          studentName: 'Jane Smith',
          riskLevel: 'low',
          riskFactors: {
            academicPerformance: 0.9,
            engagementLevel: 0.8,
            attendanceRate: 0.95,
            submissionTimeliness: 0.9,
            socialInteraction: 0.7
          },
          predictions: {
            dropoutProbability: 0.05,
            expectedGrade: 'A',
            completionTimeframe: 12
          },
          confidenceScore: 0.92
        },
        {
          studentId: 'student3',
          studentName: 'Mike Johnson',
          riskLevel: 'medium',
          riskFactors: {
            academicPerformance: 0.6,
            engagementLevel: 0.5,
            attendanceRate: 0.7,
            submissionTimeliness: 0.6,
            socialInteraction: 0.4
          },
          predictions: {
            dropoutProbability: 0.35,
            expectedGrade: 'C',
            completionTimeframe: 15
          },
          confidenceScore: 0.78
        }
      ];
      setRiskAnalysis(mockRiskData);
    } catch (error) {
      console.error('Error loading risk analysis:', error);
      setMessage({ type: 'error', text: 'Failed to load risk analysis' });
    }
    setLoading(false);
  };

  const loadLearningPatterns = async () => {
    setLoading(true);
    try {
      const patterns = await predictiveAnalyticsService.analyzeLearningPatterns('demo-student');
      setLearningPatterns(patterns);
    } catch (error) {
      console.error('Error loading learning patterns:', error);
      setMessage({ type: 'error', text: 'Failed to load learning patterns' });
    }
    setLoading(false);
  };

  const loadPredictions = async () => {
    setLoading(true);
    try {
      // Simulate predictions data
      const mockPredictions = [
        {
          type: 'enrollment',
          target: 'Fall 2024',
          prediction: 1250,
          confidence: 0.87,
          factors: ['Historical trends', 'Economic indicators', 'Program popularity']
        },
        {
          type: 'graduation_rate',
          target: 'Class of 2024',
          prediction: 0.78,
          confidence: 0.92,
          factors: ['Academic performance', 'Engagement metrics', 'Support utilization']
        },
        {
          type: 'course_completion',
          target: 'Advanced Mathematics',
          prediction: 0.85,
          confidence: 0.89,
          factors: ['Prerequisites', 'Instructor effectiveness', 'Student preparation']
        }
      ];
      setPredictions(mockPredictions);
    } catch (error) {
      console.error('Error loading predictions:', error);
      setMessage({ type: 'error', text: 'Failed to load predictions' });
    }
    setLoading(false);
  };

  const analyzeSpecificStudent = async (studentId: string) => {
    setLoading(true);
    try {
      const risk = await predictiveAnalyticsService.analyzeStudentRisk(studentId);
      setStudentRisk(risk);
      setMessage({ type: 'success', text: 'Student analysis completed' });
    } catch (error) {
      console.error('Error analyzing student:', error);
      setMessage({ type: 'error', text: 'Failed to analyze student' });
    }
    setLoading(false);
  };

  const exportReport = async (reportType: string) => {
    try {
      const report = await predictiveAnalyticsService.exportAnalyticsData(
        reportType,
        'pdf'
      );
      window.open(report.downloadUrl, '_blank');
      setMessage({ type: 'success', text: 'Report exported successfully' });
    } catch (error) {
      console.error('Error exporting report:', error);
      setMessage({ type: 'error', text: 'Failed to export report' });
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'success';
      case 'medium': return 'warning';
      case 'high': return 'error';
      case 'critical': return 'error';
      default: return 'default';
    }
  };

  const getRiskIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return <CheckCircle color="success" />;
      case 'medium': return <Warning color="warning" />;
      case 'high': return <ErrorIcon color="error" />;
      case 'critical': return <ErrorIcon color="error" />;
      default: return <CheckCircle />;
    }
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  useEffect(() => {
    if (activeTab === 0) {
      loadDashboard();
    } else if (activeTab === 1) {
      loadRiskAnalysis();
    } else if (activeTab === 2) {
      loadLearningPatterns();
    } else if (activeTab === 3) {
      loadPredictions();
    }
  }, [activeTab]);

  return (
    <Box sx={{ width: '100%', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Predictive Analytics Dashboard
      </Typography>

      {message && (
        <Alert 
          severity={message.type} 
          onClose={() => setMessage(null)}
          sx={{ mb: 2 }}
        >
          {message.text}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab icon={<Insights />} label="Institution Overview" />
          <Tab icon={<Warning />} label="Risk Analysis" />
          <Tab icon={<Timeline />} label="Learning Patterns" />
          <Tab icon={<TrendingUp />} label="Predictions" />
        </Tabs>
      </Box>

      {/* Institution Overview Tab */}
      <TabPanel value={activeTab} index={0}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">Institution Analytics Overview</Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Time Range</InputLabel>
              <Select
                value={selectedTimeRange}
                label="Time Range"
                onChange={(e) => setSelectedTimeRange(e.target.value)}
              >
                <MenuItem value="7_days">Last 7 Days</MenuItem>
                <MenuItem value="30_days">Last 30 Days</MenuItem>
                <MenuItem value="90_days">Last 90 Days</MenuItem>
                <MenuItem value="1_year">Last Year</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={loadDashboard}
              disabled={loading}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<FileDownload />}
              onClick={() => exportReport('overview')}
            >
              Export Report
            </Button>
          </Box>
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Total Students
                </Typography>
                <Typography variant="h3" color="primary">
                  {dashboard?.overview?.totalStudents || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Active: {dashboard?.overview?.activeStudents || 0}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  At Risk Students
                </Typography>
                <Typography variant="h3" color="error.main">
                  {dashboard?.overview?.atRiskStudents || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {dashboard?.overview?.totalStudents ? 
                    formatPercentage(dashboard.overview.atRiskStudents / dashboard.overview.totalStudents) : '0%'} of total
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Avg Performance
                </Typography>
                <Typography variant="h3" color="success.main">
                  {dashboard?.overview?.averagePerformance || 0}%
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={dashboard?.overview?.averagePerformance || 0} 
                  sx={{ mt: 1 }}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Critical Alerts
                </Typography>
                <Typography variant="h3" color="warning.main">
                  {dashboard?.alerts?.criticalAlerts?.length || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Require immediate attention
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Enrollment Trends
                </Typography>
                <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <BarChart sx={{ fontSize: 60, color: 'primary.main' }} />
                  <Typography variant="body2" sx={{ ml: 2 }}>
                    Chart visualization would be implemented here
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Performance Distribution
                </Typography>
                <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <PieChart sx={{ fontSize: 60, color: 'success.main' }} />
                  <Typography variant="body2" sx={{ ml: 2 }}>
                    Chart visualization would be implemented here
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Risk Analysis Tab */}
      <TabPanel value={activeTab} index={1}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">Student Risk Analysis</Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <TextField
              size="small"
              label="Student ID"
              value={selectedStudent}
              onChange={(e) => setSelectedStudent(e.target.value)}
              placeholder="Enter student ID"
            />
            <Button
              variant="contained"
              onClick={() => analyzeSpecificStudent(selectedStudent)}
              disabled={!selectedStudent || loading}
            >
              Analyze Student
            </Button>
          </Box>
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Risk Assessment Overview
                </Typography>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Student</TableCell>
                        <TableCell>Risk Level</TableCell>
                        <TableCell>Dropout Probability</TableCell>
                        <TableCell>Expected Grade</TableCell>
                        <TableCell>Confidence</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {riskAnalysis.map((student) => (
                        <TableRow key={student.studentId}>
                          <TableCell>{student.studentName}</TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {getRiskIcon(student.riskLevel)}
                              <Chip 
                                label={student.riskLevel.toUpperCase()}
                                color={getRiskColor(student.riskLevel)}
                                size="small"
                              />
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography color={student.predictions.dropoutProbability > 0.5 ? 'error' : 'success'}>
                              {formatPercentage(student.predictions.dropoutProbability)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip 
                              label={student.predictions.expectedGrade}
                              color={student.predictions.expectedGrade === 'A' ? 'success' : 
                                     student.predictions.expectedGrade === 'B' ? 'info' :
                                     student.predictions.expectedGrade === 'C' ? 'warning' : 'error'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <LinearProgress 
                              variant="determinate" 
                              value={student.confidenceScore * 100}
                              sx={{ width: 60 }}
                            />
                            <Typography variant="caption">
                              {formatPercentage(student.confidenceScore)}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            {studentRisk && (
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Individual Risk Profile
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle1">Risk Level</Typography>
                    <Chip 
                      label={studentRisk.riskLevel.toUpperCase()}
                      color={getRiskColor(studentRisk.riskLevel)}
                      sx={{ mt: 1 }}
                    />
                  </Box>
                  
                  <Typography variant="subtitle1" gutterBottom>
                    Risk Factors
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText 
                        primary="Academic Performance"
                        secondary={formatPercentage(studentRisk.riskFactors?.academicPerformance || 0)}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="Engagement Level"
                        secondary={formatPercentage(studentRisk.riskFactors?.engagementLevel || 0)}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="Attendance Rate"
                        secondary={formatPercentage(studentRisk.riskFactors?.attendanceRate || 0)}
                      />
                    </ListItem>
                  </List>

                  <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                    Interventions
                  </Typography>
                  <List dense>
                    {studentRisk.predictions?.interventionRecommendations?.map((intervention: string, index: number) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <School />
                        </ListItemIcon>
                        <ListItemText primary={intervention} />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            )}
          </Grid>
        </Grid>
      </TabPanel>

      {/* Learning Patterns Tab */}
      <TabPanel value={activeTab} index={2}>
        <Typography variant="h6" gutterBottom>
          Learning Pattern Analysis
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Learning Preferences
                </Typography>
                {learningPatterns && (
                  <List>
                    <ListItem>
                      <ListItemText 
                        primary="Preferred Learning Times"
                        secondary={learningPatterns.patterns?.preferredLearningTimes?.join(', ') || 'N/A'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="Average Session Duration"
                        secondary={`${learningPatterns.patterns?.sessionDuration || 0} minutes`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="Content Preferences"
                        secondary={learningPatterns.patterns?.contentPreferences?.join(', ') || 'N/A'}
                      />
                    </ListItem>
                  </List>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Optimization Insights
                </Typography>
                {learningPatterns && (
                  <List>
                    {learningPatterns.insights?.personalizedStrategies?.map((strategy: string, index: number) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <TrendingUp color="primary" />
                        </ListItemIcon>
                        <ListItemText primary={strategy} />
                      </ListItem>
                    ))}
                  </List>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Learning Progression Visualization
                </Typography>
                <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Timeline sx={{ fontSize: 80, color: 'primary.main' }} />
                  <Typography variant="body1" sx={{ ml: 2 }}>
                    Interactive learning progression chart would be implemented here
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Predictions Tab */}
      <TabPanel value={activeTab} index={3}>
        <Typography variant="h6" gutterBottom>
          Predictive Models & Forecasts
        </Typography>

        <Grid container spacing={3}>
          {predictions.map((prediction, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {prediction.type.replace('_', ' ').toUpperCase()}
                  </Typography>
                  <Typography variant="h4" color="primary" gutterBottom>
                    {typeof prediction.prediction === 'number' && prediction.prediction < 1 
                      ? formatPercentage(prediction.prediction)
                      : prediction.prediction}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Target: {prediction.target}
                  </Typography>
                  
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2">Confidence Level</Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={prediction.confidence * 100}
                      sx={{ mt: 1, mb: 2 }}
                    />
                    <Typography variant="caption">
                      {formatPercentage(prediction.confidence)}
                    </Typography>
                  </Box>

                  <Typography variant="subtitle2" gutterBottom>
                    Key Factors:
                  </Typography>
                  <List dense>
                    {prediction.factors.map((factor: string, factorIndex: number) => (
                      <ListItem key={factorIndex} sx={{ py: 0 }}>
                        <ListItemIcon sx={{ minWidth: 20 }}>
                          <Assessment fontSize="small" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={factor}
                          primaryTypographyProps={{ variant: 'caption' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>
    </Box>
  );
};

export default PredictiveAnalyticsPage;

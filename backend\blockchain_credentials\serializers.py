"""
Blockchain Credentials Serializers

This module provides DRF serializers for blockchain credentials,
NFT achievements, and related models.
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    BlockchainCredential, 
    CredentialTemplate, 
    NFTAchievement,
    BlockchainNetwork,
    CredentialVerification,
    WalletAddress,
    SmartContract
)

User = get_user_model()


class BlockchainNetworkSerializer(serializers.ModelSerializer):
    """Serializer for blockchain network information"""
    
    class Meta:
        model = BlockchainNetwork
        fields = [
            'id', 'name', 'network_type', 'chain_id', 'explorer_url',
            'is_testnet', 'is_active', 'gas_fee_estimate', 'created_at'
        ]
        read_only_fields = ['created_at']


class CredentialTemplateSerializer(serializers.ModelSerializer):
    """Serializer for credential templates"""
    
    credential_type_display = serializers.CharField(source='get_credential_type_display', read_only=True)
    default_network_name = serializers.Char<PERSON>ield(source='default_network.name', read_only=True)
    
    class Meta:
        model = CredentialTemplate
        fields = [
            'id', 'name', 'credential_type', 'credential_type_display',
            'description', 'template_design', 'image_template',
            'required_fields', 'verification_requirements',
            'blockchain_enabled', 'default_network', 'default_network_name',
            'default_validity_period', 'is_renewable', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class StudentBasicSerializer(serializers.ModelSerializer):
    """Basic serializer for student information in credentials"""
    
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'email']
        read_only_fields = ['id', 'username', 'email']


class BlockchainCredentialListSerializer(serializers.ModelSerializer):
    """Serializer for listing blockchain credentials"""
    
    student = StudentBasicSerializer(read_only=True)
    template_name = serializers.CharField(source='template.name', read_only=True)
    credential_type = serializers.CharField(source='template.credential_type', read_only=True)
    network_name = serializers.CharField(source='blockchain_network.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    competency_level_display = serializers.CharField(source='get_competency_level_display', read_only=True)
    is_valid_credential = serializers.SerializerMethodField()
    verification_url_public = serializers.SerializerMethodField()
    
    class Meta:
        model = BlockchainCredential
        fields = [
            'credential_id', 'title', 'description', 'student', 'template_name',
            'credential_type', 'final_grade', 'grade_percentage', 'skills_acquired',
            'competency_level', 'competency_level_display', 'issue_date', 'completion_date',
            'expiry_date', 'network_name', 'status', 'status_display',
            'is_public', 'verification_count', 'is_valid_credential',
            'verification_url_public'
        ]
        read_only_fields = [
            'credential_id', 'issue_date', 'verification_count', 'is_valid_credential'
        ]
    
    def get_is_valid_credential(self, obj):
        return obj.is_valid()
    
    def get_verification_url_public(self, obj):
        if obj.is_public:
            return obj.verification_url
        return None


class BlockchainCredentialDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for blockchain credentials"""
    
    student = StudentBasicSerializer(read_only=True)
    template = CredentialTemplateSerializer(read_only=True)
    blockchain_network = BlockchainNetworkSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    competency_level_display = serializers.CharField(source='get_competency_level_display', read_only=True)
    is_valid_credential = serializers.SerializerMethodField()
    is_expired_credential = serializers.SerializerMethodField()
    blockchain_verification = serializers.SerializerMethodField()
    course_title = serializers.CharField(source='course.title', read_only=True)
    
    class Meta:
        model = BlockchainCredential
        fields = [
            'credential_id', 'student', 'template', 'title', 'description',
            'achievement_data', 'course', 'course_title', 'assessment',
            'final_grade', 'grade_percentage', 'skills_acquired',
            'competency_level', 'competency_level_display', 'issue_date',
            'completion_date', 'expiry_date', 'blockchain_network',
            'transaction_hash', 'smart_contract_address', 'token_id',
            'blockchain_hash', 'verification_url', 'ipfs_hash',
            'status', 'status_display', 'is_public', 'is_transferable',
            'issued_by', 'metadata', 'verification_count', 'last_verified',
            'is_valid_credential', 'is_expired_credential', 'blockchain_verification',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'credential_id', 'blockchain_hash', 'verification_url',
            'verification_count', 'last_verified', 'created_at', 'updated_at'
        ]
    
    def get_is_valid_credential(self, obj):
        return obj.is_valid()
    
    def get_is_expired_credential(self, obj):
        return obj.is_expired()
    
    def get_blockchain_verification(self, obj):
        return obj.verify_on_blockchain()


class BlockchainCredentialCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating blockchain credentials"""
    
    class Meta:
        model = BlockchainCredential
        fields = [
            'student', 'template', 'title', 'description', 'achievement_data',
            'course', 'assessment', 'final_grade', 'grade_percentage',
            'skills_acquired', 'competency_level', 'completion_date',
            'expiry_date', 'blockchain_network', 'is_public', 'is_transferable',
            'metadata'
        ]
    
    def validate(self, data):
        """Validate credential data before creation"""
        template = data.get('template')
        if template and not template.is_active:
            raise serializers.ValidationError("Selected template is not active")
        
        blockchain_network = data.get('blockchain_network')
        if blockchain_network and not blockchain_network.is_active:
            raise serializers.ValidationError("Selected blockchain network is not active")
        
        # Validate required fields based on template
        if template and template.required_fields:
            for field in template.required_fields:
                if field not in data or not data[field]:
                    raise serializers.ValidationError(f"Field '{field}' is required for this credential type")
        
        return data


class NFTAchievementListSerializer(serializers.ModelSerializer):
    """Serializer for listing NFT achievements"""
    
    student = StudentBasicSerializer(read_only=True)
    achievement_type_display = serializers.CharField(source='get_achievement_type_display', read_only=True)
    rarity_display = serializers.CharField(source='get_rarity_display', read_only=True)
    network_name = serializers.CharField(source='blockchain_network.name', read_only=True)
    course_title = serializers.CharField(source='related_course.title', read_only=True)
    
    class Meta:
        model = NFTAchievement
        fields = [
            'achievement_id', 'title', 'description', 'student', 'achievement_type',
            'achievement_type_display', 'rarity', 'rarity_display', 'image',
            'earned_date', 'course_title', 'network_name', 'is_minted',
            'xp_reward', 'badge_level'
        ]
        read_only_fields = ['achievement_id', 'earned_date']


class NFTAchievementDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for NFT achievements"""
    
    student = StudentBasicSerializer(read_only=True)
    blockchain_network = BlockchainNetworkSerializer(read_only=True)
    achievement_type_display = serializers.CharField(source='get_achievement_type_display', read_only=True)
    rarity_display = serializers.CharField(source='get_rarity_display', read_only=True)
    nft_metadata = serializers.SerializerMethodField()
    course_title = serializers.CharField(source='related_course.title', read_only=True)
    
    class Meta:
        model = NFTAchievement
        fields = [
            'achievement_id', 'student', 'title', 'description', 'achievement_type',
            'achievement_type_display', 'rarity', 'rarity_display', 'image',
            'animation_url', 'criteria_met', 'earned_date', 'related_course',
            'course_title', 'related_assessment', 'blockchain_network',
            'nft_contract_address', 'token_id', 'transaction_hash',
            'ipfs_metadata_hash', 'opensea_url', 'is_minted', 'is_transferable',
            'mint_price', 'xp_reward', 'badge_level', 'nft_metadata',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'achievement_id', 'earned_date', 'created_at', 'updated_at'
        ]
    
    def get_nft_metadata(self, obj):
        return obj.get_nft_metadata()


class CredentialVerificationSerializer(serializers.ModelSerializer):
    """Serializer for credential verification records"""
    
    credential_title = serializers.CharField(source='credential.title', read_only=True)
    student_name = serializers.CharField(source='credential.student.username', read_only=True)
    verification_type_display = serializers.CharField(source='get_verification_type_display', read_only=True)
    
    class Meta:
        model = CredentialVerification
        fields = [
            'id', 'credential', 'credential_title', 'student_name',
            'verification_type', 'verification_type_display', 'verifier_email',
            'verifier_organization', 'is_verified', 'verification_data',
            'verified_at', 'verification_duration'
        ]
        read_only_fields = ['verified_at']


class PublicCredentialVerificationSerializer(serializers.ModelSerializer):
    """Public serializer for credential verification (limited fields)"""
    
    student_name = serializers.CharField(source='student.username', read_only=True)
    template_name = serializers.CharField(source='template.name', read_only=True)
    network_name = serializers.CharField(source='blockchain_network.name', read_only=True)
    is_valid_credential = serializers.SerializerMethodField()
    blockchain_verification = serializers.SerializerMethodField()
    
    class Meta:
        model = BlockchainCredential
        fields = [
            'credential_id', 'title', 'description', 'student_name',
            'template_name', 'final_grade', 'skills_acquired',
            'completion_date', 'issue_date', 'network_name',
            'blockchain_hash', 'transaction_hash', 'is_valid_credential',
            'blockchain_verification'
        ]
    
    def get_is_valid_credential(self, obj):
        return obj.is_valid()
    
    def get_blockchain_verification(self, obj):
        return obj.verify_on_blockchain()


class WalletAddressSerializer(serializers.ModelSerializer):
    """Serializer for user wallet addresses"""
    
    network_name = serializers.CharField(source='blockchain_network.name', read_only=True)
    wallet_type_display = serializers.CharField(source='get_wallet_type_display', read_only=True)
    
    class Meta:
        model = WalletAddress
        fields = [
            'id', 'blockchain_network', 'network_name', 'address',
            'wallet_type', 'wallet_type_display', 'label', 'is_verified',
            'verified_at', 'is_primary', 'is_active', 'created_at'
        ]
        read_only_fields = ['verified_at', 'created_at']
    
    def validate_address(self, value):
        """Basic validation for wallet address format"""
        if not value:
            raise serializers.ValidationError("Wallet address is required")
        
        # Basic Ethereum address validation (can be extended for other networks)
        if len(value) == 42 and value.startswith('0x'):
            return value
        elif len(value) >= 26 and len(value) <= 62:  # Bitcoin/other formats
            return value
        else:
            raise serializers.ValidationError("Invalid wallet address format")


class SmartContractSerializer(serializers.ModelSerializer):
    """Serializer for smart contract information"""
    
    network_name = serializers.CharField(source='blockchain_network.name', read_only=True)
    contract_type_display = serializers.CharField(source='get_contract_type_display', read_only=True)
    
    class Meta:
        model = SmartContract
        fields = [
            'id', 'name', 'contract_type', 'contract_type_display',
            'blockchain_network', 'network_name', 'contract_address',
            'deployment_transaction', 'version', 'is_active',
            'deployment_date', 'deployment_gas_used', 'deployment_cost',
            'owner_address'
        ]
        read_only_fields = ['deployment_date']


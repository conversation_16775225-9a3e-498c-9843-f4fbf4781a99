{"name": "university-frontend", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "dev:fix-hmr": "vite --force --host localhost", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "fix-modules": "tsx fix-module-issues.ts", "validate-translations": "tsx scripts/validate-translations.ts", "organize-translations": "tsx scripts/organize-translations.ts", "restart": "tsx restart.ts", "clear-restart": "tsx clear-cache-and-restart.ts", "test": "jest"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.11.0", "@monaco-editor/react": "^4.6.0", "@mui/icons-material": "^5.16.14", "@mui/lab": "^5.0.0-alpha.129", "@mui/material": "^5.16.14", "@mui/x-data-grid": "^6.3.0", "@mui/x-date-pickers": "^6.3.0", "@reduxjs/toolkit": "^1.9.7", "@types/chart.js": "^2.9.41", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "@uidotdev/usehooks": "^2.4.1", "antd": "^5.15.0", "axios": "^1.6.2", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "dayjs": "^1.11.10", "framer-motion": "^12.7.4", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "katex": "^0.16.8", "mathquill": "^0.10.1", "monaco-editor": "^0.44.0", "notistack": "^3.0.2", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-redux": "^8.1.3", "react-router-dom": "^6.20.0", "react-toastify": "^10.0.4", "react-type-animation": "^3.2.0", "react-window": "^1.8.11", "recharts": "^2.15.2", "remark-gfm": "^4.0.1", "rtl-detect": "^1.1.2", "socket.io-client": "^4.8.1", "stylis": "^4.3.1", "stylis-plugin-rtl": "^2.1.1", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.1", "@eslint/js": "^8.56.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/crypto-js": "^4.2.2", "@types/node": "^22.15.3", "@types/react": "^18.2.37", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-chartjs-2": "^2.0.2", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.21", "babel-jest": "^29.7.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "globals": "^13.24.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.2", "jest-mock-axios": "^4.8.0", "postcss": "^8.5.3", "prop-types": "^15.8.1", "terser": "^5.39.0", "ts-jest": "^29.3.2", "tsx": "^4.19.3", "typescript": "^5.2.2", "typescript-eslint": "^0.0.1-alpha.0", "vite": "^5.0.0", "vite-plugin-pwa": "^1.0.0"}}
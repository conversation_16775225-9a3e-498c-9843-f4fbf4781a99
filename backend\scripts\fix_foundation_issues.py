#!/usr/bin/env python3
"""
Critical Foundation Issues Fix Script
This script addresses the most critical foundation issues in the North Star University project.
"""

import os
import re
import shutil
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FoundationIssuesFixer:
    def __init__(self, base_dir):
        self.base_dir = Path(base_dir)
        self.issues_found = []
        self.fixes_applied = []
        
    def log_issue(self, issue_type, description, severity="HIGH"):
        """Log an issue found during analysis"""
        issue = {
            "type": issue_type,
            "description": description,
            "severity": severity
        }
        self.issues_found.append(issue)
        logger.warning(f"[{severity}] {issue_type}: {description}")
    
    def log_fix(self, fix_type, description):
        """Log a fix that was applied"""
        fix = {
            "type": fix_type,
            "description": description
        }
        self.fixes_applied.append(fix)
        logger.info(f"[FIXED] {fix_type}: {description}")
    
    def fix_corrupted_files(self):
        """Fix files with formatting corruption (no spaces, all on one line)"""
        logger.info("Scanning for corrupted Python files...")
        
        corrupted_files = [
            "utils/api_key_service.py",
            "course_generator/api_key_views.py", 
            "settings/production.py",
            "assessment/prevent_mock_data.py"
        ]
        
        for file_path in corrupted_files:
            full_path = self.base_dir / file_path
            if full_path.exists():
                self.log_issue("FILE_CORRUPTION", f"File {file_path} has formatting corruption", "CRITICAL")
                self._fix_python_file_formatting(full_path)
    
    def _fix_python_file_formatting(self, file_path):
        """Fix Python file formatting by adding proper spaces and newlines"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Create backup
            backup_path = f"{file_path}.backup"
            shutil.copy2(file_path, backup_path)
            
            # Apply basic Python formatting fixes
            formatted_content = self._apply_python_formatting(content)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(formatted_content)
            
            self.log_fix("FILE_FORMATTING", f"Fixed formatting in {file_path}")
            
        except Exception as e:
            self.log_issue("FILE_FIX_ERROR", f"Failed to fix {file_path}: {e}", "HIGH")
    
    def _apply_python_formatting(self, content):
        """Apply basic Python formatting rules"""
        # Add spaces around common keywords and operators
        content = re.sub(r'from([a-zA-Z])', r'from \1', content)
        content = re.sub(r'import([a-zA-Z])', r'import \1', content)
        content = re.sub(r'class([A-Z])', r'class \1', content)
        content = re.sub(r'def([a-z_])', r'def \1', content)
        content = re.sub(r'if([a-zA-Z])', r'if \1', content)
        content = re.sub(r'else([a-zA-Z])', r'else \1', content)
        content = re.sub(r'elif([a-zA-Z])', r'elif \1', content)
        content = re.sub(r'try([a-zA-Z:])', r'try\1', content)
        content = re.sub(r'except([A-Z])', r'except \1', content)
        content = re.sub(r'return([a-zA-Z])', r'return \1', content)
        
        # Add newlines after certain patterns
        content = re.sub(r'"""([^"]+)"""([a-zA-Z])', r'"""\1"""\n\2', content)
        content = re.sub(r'}([a-zA-Z])', r'}\n\1', content)
        content = re.sub(r':([a-zA-Z])', r':\n\1', content)
        
        return content
    
    def fix_settings_issues(self):
        """Fix critical issues in Django settings"""
        logger.info("Fixing Django settings issues...")
        
        settings_base = self.base_dir / "settings" / "base.py"
        if settings_base.exists():
            self._fix_installed_apps(settings_base)
            self._fix_security_settings(settings_base)
    
    def _fix_installed_apps(self, settings_file):
        """Re-enable essential apps that were commented out"""
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Track changes
            original_content = content
            
            # Re-enable essential apps that are commented out
            essential_apps = [
                "courses",
                "grades", 
                "notifications",
                "assessment.apps.AssessmentConfig",
                "course_generator.apps.CourseGeneratorConfig",
                "chatbot",
                "utils"
            ]
            
            for app in essential_apps:
                # Find commented out app and uncomment it
                pattern = rf'#\s*["\']({re.escape(app)})["\']'
                if re.search(pattern, content):
                    content = re.sub(pattern, rf'"\1"', content)
                    self.log_fix("SETTINGS_APPS", f"Re-enabled app: {app}")
            
            # Write back only if changes were made
            if content != original_content:
                with open(settings_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.log_fix("SETTINGS_FIXED", f"Updated INSTALLED_APPS in {settings_file}")
            
        except Exception as e:
            self.log_issue("SETTINGS_ERROR", f"Failed to fix settings: {e}", "HIGH")
    
    def _fix_security_settings(self, settings_file):
        """Fix security-related settings issues"""
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for hardcoded secrets
            if 'django-insecure-default-key-for-development-only' in content:
                self.log_issue("SECURITY_ISSUE", "Hardcoded development secret key found", "HIGH")
            
            # Check for debug settings
            if 'DEBUG = True' in content and 'production' in str(settings_file).lower():
                self.log_issue("SECURITY_ISSUE", "DEBUG=True in production settings", "CRITICAL")
                
        except Exception as e:
            self.log_issue("SECURITY_CHECK_ERROR", f"Failed to check security settings: {e}", "MEDIUM")
    
    def fix_import_issues(self):
        """Fix common import issues"""
        logger.info("Fixing import issues...")
        
        # Check for circular imports or missing imports
        python_files = list(self.base_dir.rglob("*.py"))
        
        for file_path in python_files:
            if any(skip in str(file_path) for skip in ['migrations', '__pycache__', 'venv', '.venv']):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for common import issues
                if 'from .models import' in content and 'models.py' in str(file_path):
                    self.log_issue("CIRCULAR_IMPORT", f"Potential circular import in {file_path}", "MEDIUM")
                
            except Exception as e:
                continue  # Skip files that can't be read
    
    def create_missing_files(self):
        """Create missing essential files"""
        logger.info("Creating missing essential files...")
        
        # Create __init__.py files where missing
        essential_dirs = [
            "utils",
            "core",
            "courses",
            "assessment"
        ]
        
        for dir_name in essential_dirs:
            dir_path = self.base_dir / dir_name
            if dir_path.exists() and dir_path.is_dir():
                init_file = dir_path / "__init__.py"
                if not init_file.exists():
                    init_file.touch()
                    self.log_fix("MISSING_FILES", f"Created __init__.py in {dir_name}")
    
    def check_database_issues(self):
        """Check for database-related issues"""
        logger.info("Checking database configuration...")
        
        # Check if migrations are up to date
        try:
            os.system("python manage.py showmigrations --list > migration_status.txt 2>&1")
            with open("migration_status.txt", 'r') as f:
                migration_output = f.read()
            
            if "[ ]" in migration_output:  # Unapplied migrations
                self.log_issue("DATABASE_MIGRATIONS", "Unapplied migrations found", "HIGH")
            
            os.remove("migration_status.txt")
            
        except Exception as e:
            self.log_issue("DATABASE_CHECK_ERROR", f"Failed to check migrations: {e}", "MEDIUM")
    
    def generate_report(self):
        """Generate a comprehensive report of issues and fixes"""
        report = []
        report.append("=" * 80)
        report.append("NORTH STAR UNIVERSITY - FOUNDATION ISSUES REPORT")
        report.append("=" * 80)
        report.append("")
        
        # Issues Summary
        report.append("🚨 CRITICAL ISSUES FOUND:")
        report.append("-" * 40)
        critical_issues = [i for i in self.issues_found if i['severity'] == 'CRITICAL']
        high_issues = [i for i in self.issues_found if i['severity'] == 'HIGH']
        medium_issues = [i for i in self.issues_found if i['severity'] == 'MEDIUM']
        
        report.append(f"Critical Issues: {len(critical_issues)}")
        report.append(f"High Priority Issues: {len(high_issues)}")
        report.append(f"Medium Priority Issues: {len(medium_issues)}")
        report.append("")
        
        # Detailed Issues
        if critical_issues:
            report.append("CRITICAL ISSUES (Immediate Action Required):")
            for issue in critical_issues:
                report.append(f"  • {issue['type']}: {issue['description']}")
            report.append("")
        
        if high_issues:
            report.append("HIGH PRIORITY ISSUES:")
            for issue in high_issues:
                report.append(f"  • {issue['type']}: {issue['description']}")
            report.append("")
        
        # Fixes Applied
        report.append("✅ FIXES APPLIED:")
        report.append("-" * 40)
        for fix in self.fixes_applied:
            report.append(f"  • {fix['type']}: {fix['description']}")
        report.append("")
        
        # Recommendations
        report.append("📋 IMMEDIATE NEXT STEPS:")
        report.append("-" * 40)
        report.append("1. Review and test all applied fixes")
        report.append("2. Run: python manage.py check --verbosity=2")
        report.append("3. Run: python manage.py makemigrations")
        report.append("4. Run: python manage.py migrate")
        report.append("5. Test application startup: python manage.py runserver")
        report.append("6. Address any remaining critical/high priority issues")
        report.append("")
        
        return "\n".join(report)
    
    def run_comprehensive_fix(self):
        """Run all foundation fixes"""
        logger.info("Starting comprehensive foundation fixes...")
        
        # 1. Fix corrupted files (highest priority)
        self.fix_corrupted_files()
        
        # 2. Fix settings issues
        self.fix_settings_issues()
        
        # 3. Create missing files
        self.create_missing_files()
        
        # 4. Check for import issues
        self.fix_import_issues()
        
        # 5. Check database issues
        self.check_database_issues()
        
        # 6. Generate report
        report = self.generate_report()
        
        # Save report
        with open("foundation_fixes_report.txt", "w") as f:
            f.write(report)
        
        logger.info("Foundation fixes completed. Report saved to foundation_fixes_report.txt")
        print(report)

def main():
    """Main function to run foundation fixes"""
    # Get the backend directory (current directory)
    backend_dir = Path(__file__).parent.parent
    
    fixer = FoundationIssuesFixer(backend_dir)
    fixer.run_comprehensive_fix()

if __name__ == "__main__":
    main()

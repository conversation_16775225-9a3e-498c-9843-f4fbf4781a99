#!/bin/bash

# North Star University Deployment Script for <PERSON><PERSON>
# This script automates the deployment process

set -e  # Exit on any error

# Configuration
PROJECT_NAME="north-star-university"
PROJECT_DIR="/var/www/$PROJECT_NAME"
BACKEND_DIR="$PROJECT_DIR/backend"
FRONTEND_DIR="$PROJECT_DIR/frontend"
VENV_DIR="$PROJECT_DIR/venv"
LOG_DIR="/var/log/$PROJECT_NAME"
RUN_DIR="/var/run/$PROJECT_NAME"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   error "This script should not be run as root for security reasons"
fi

# Create necessary directories
log "Creating necessary directories..."
sudo mkdir -p $PROJECT_DIR
sudo mkdir -p $LOG_DIR
sudo mkdir -p $RUN_DIR
sudo chown -R $USER:www-data $PROJECT_DIR
sudo chown -R www-data:www-data $LOG_DIR
sudo chown -R www-data:www-data $RUN_DIR

# Navigate to project directory
cd $PROJECT_DIR

# Update system packages
log "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install required system packages
log "Installing system dependencies..."
sudo apt install -y python3 python3-pip python3-venv nodejs npm nginx postgresql postgresql-contrib redis-server supervisor

# Create virtual environment if it doesn't exist
if [ ! -d "$VENV_DIR" ]; then
    log "Creating Python virtual environment..."
    python3 -m venv $VENV_DIR
fi

# Activate virtual environment
log "Activating virtual environment..."
source $VENV_DIR/bin/activate

# Install Python dependencies
log "Installing Python dependencies..."
cd $BACKEND_DIR
pip install --upgrade pip
pip install -r requirements.txt

# Install Node.js dependencies and build frontend
log "Building frontend..."
cd $FRONTEND_DIR
npm install
npm run build

# Copy built frontend to nginx directory
log "Copying frontend build to web directory..."
sudo rm -rf $PROJECT_DIR/static_frontend
sudo cp -r $FRONTEND_DIR/dist $PROJECT_DIR/static_frontend
sudo chown -R www-data:www-data $PROJECT_DIR/static_frontend

# Collect Django static files
log "Collecting Django static files..."
cd $BACKEND_DIR
python manage.py collectstatic --noinput --settings=settings.production

# Run database migrations
log "Running database migrations..."
python manage.py migrate --settings=settings.production

# Create superuser if it doesn't exist
log "Creating Django superuser (if needed)..."
python manage.py shell --settings=settings.production << EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'change_this_password')
    print('Superuser created')
else:
    print('Superuser already exists')
EOF

# Set proper permissions
log "Setting file permissions..."
sudo chown -R www-data:www-data $PROJECT_DIR/media
sudo chown -R www-data:www-data $PROJECT_DIR/static
sudo chmod -R 755 $PROJECT_DIR

# Copy nginx configuration
log "Configuring nginx..."
sudo cp $PROJECT_DIR/deployment/nginx/$PROJECT_NAME.conf /etc/nginx/sites-available/
sudo ln -sf /etc/nginx/sites-available/$PROJECT_NAME.conf /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test nginx configuration
sudo nginx -t || error "Nginx configuration test failed"

# Restart services
log "Restarting services..."
sudo systemctl restart nginx
sudo systemctl enable nginx

log "Deployment completed successfully!"
log "Don't forget to:"
log "1. Update your domain DNS to point to this server"
log "2. Configure SSL certificates"
log "3. Update the .env.production file with your actual values"
log "4. Start the Django application with: sudo systemctl start $PROJECT_NAME"
log "5. Enable auto-start: sudo systemctl enable $PROJECT_NAME"

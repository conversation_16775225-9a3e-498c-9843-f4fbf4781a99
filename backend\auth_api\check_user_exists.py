﻿import random
import string
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

User = get_user_model()

@api_view(["POST"])
@permission_classes([AllowAny])
def check_user_exists(request):
    """
    Check if a username or email already exists in the database.
    Returns suggestions for usernames if the requested username is taken.
    """
    username = request.data.get("username")
    email = request.data.get("email")
    
    response_data = {
        "username_exists": False,
        "email_exists": False,
        "suggestions": []
    }
    
    # Check if username exists
    if username:
        username_exists = User.objects.filter(username=username).exists()
        response_data["username_exists"] = username_exists
        
        # If username exists, generate suggestions
        if username_exists:
            suggestions = generate_username_suggestions(username)
            response_data["suggestions"] = suggestions
    
    # Check if email exists
    if email:
        email_exists = User.objects.filter(email=email).exists()
        response_data["email_exists"] = email_exists
    
    return Response(response_data)

def generate_username_suggestions(username, count=5):
    """Generate alternative username suggestions"""
    suggestions = []
    base_username = username
    
    # Try adding numbers
    for i in range(1, count + 1):
        suggestion = f"{base_username}{i}"
        if not User.objects.filter(username=suggestion).exists():
            suggestions.append(suggestion)
        if len(suggestions) >= count:
            break
    
    # Try adding random characters
    while len(suggestions) < count:
        random_suffix = "".join(random.choices(string.ascii_lowercase + string.digits, k=3))
        suggestion = f"{base_username}_{random_suffix}"
        if not User.objects.filter(username=suggestion).exists():
            suggestions.append(suggestion)
    
    return suggestions

import React from 'react';
import {
  Card,
  CardContent,
  CardActions,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  Box,
  Avatar,
  Tooltip,
  <PERSON>ack,
  Divider,
  Badge
} from '@mui/material';
import {
  Person as PersonIcon,
  School as SchoolIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  CalendarToday as CalendarIcon,
  LocationOn as LocationIcon,
  WorkOutline as WorkIcon,
  Edit as EditIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  AdminPanelSettings as AdminIcon,
  Psychology as PsychologyIcon
} from '@mui/icons-material';
import { IEnhancedUser } from '../../services/userService';
import { formatDistanceToNow } from 'date-fns';

interface EnhancedUserCardProps {
  user: IEnhancedUser;
  currentUserRole?: 'STUDENT' | 'PROFESSOR' | 'ADMIN';
  onEdit?: (userId: number) => void;
  onView?: (userId: number) => void;
  onActivate?: (userId: number) => void;
  onDeactivate?: (userId: number) => void;
  onResetPassword?: (userId: number) => void;
  showActions?: boolean;
  showStatistics?: boolean;
  compact?: boolean;
}

const EnhancedUserCard: React.FC<EnhancedUserCardProps> = ({
  user,
  currentUserRole = 'ADMIN',
  onEdit,
  onView,
  onActivate,
  onDeactivate,
  onResetPassword,
  showActions = true,
  showStatistics = true,
  compact = false
}) => {
  // Helper function to get role display with icon and color
  const getRoleDisplay = (role: string) => {
    switch (role) {
      case 'STUDENT':
        return { 
          icon: <SchoolIcon />, 
          color: 'primary' as const, 
          label: 'Student',
          bgColor: '#e3f2fd'
        };
      case 'PROFESSOR':
        return { 
          icon: <WorkIcon />, 
          color: 'secondary' as const, 
          label: 'Professor',
          bgColor: '#f3e5f5'
        };
      case 'ADMIN':
        return { 
          icon: <AdminIcon />, 
          color: 'error' as const, 
          label: 'Administrator',
          bgColor: '#ffebee'
        };
      default:
        return { 
          icon: <PersonIcon />, 
          color: 'default' as const, 
          label: role,
          bgColor: '#f5f5f5'
        };
    }
  };

  // Helper function to format user display name
  const getDisplayName = () => {
    if (user.first_name || user.last_name) {
      return `${user.first_name || ''} ${user.last_name || ''}`.trim();
    }
    return user.username;
  };

  // Helper function to get avatar initials
  const getAvatarInitials = () => {
    if (user.first_name || user.last_name) {
      const first = user.first_name?.charAt(0) || '';
      const last = user.last_name?.charAt(0) || '';
      return (first + last).toUpperCase();
    }
    return user.username.substring(0, 2).toUpperCase();
  };

  // Helper function to get last activity display
  const getLastActivityDisplay = () => {
    if (user.last_activity) {
      return formatDistanceToNow(new Date(user.last_activity), { addSuffix: true });
    } else if (user.last_login) {
      return `Login ${formatDistanceToNow(new Date(user.last_login), { addSuffix: true })}`;
    }
    return 'Never active';
  };

  // Helper function to get user status
  const getUserStatusColor = () => {
    if (!user.is_active) return 'error';
    if (user.last_activity) {
      const lastActivity = new Date(user.last_activity);
      const daysSinceActivity = (Date.now() - lastActivity.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceActivity > 30) return 'warning';
      if (daysSinceActivity > 7) return 'info';
    }
    return 'success';
  };

  const roleDisplay = getRoleDisplay(user.role);
  const displayName = getDisplayName();
  const avatarInitials = getAvatarInitials();

  return (
    <Card 
      sx={{ 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        transition: 'all 0.3s ease-in-out',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: 3
        }
      }}
    >
      {/* Header with user avatar and role */}
      <Box sx={{ p: 2, pb: compact ? 1 : 2, backgroundColor: roleDisplay.bgColor }}>
        <Stack direction="row" spacing={2} alignItems="center">
          <Badge
            badgeContent={
              user.is_superuser ? <AdminIcon fontSize="small" /> : 
              user.is_staff ? <PsychologyIcon fontSize="small" /> : null
            }
            color={user.is_superuser ? 'error' : 'secondary'}
          >
            <Avatar
              src={user.profile_picture}
              sx={{ 
                width: compact ? 40 : 56, 
                height: compact ? 40 : 56,
                bgcolor: roleDisplay.color === 'default' ? 'grey.500' : `${roleDisplay.color}.main`
              }}
            >
              {avatarInitials}
            </Avatar>
          </Badge>
          
          <Box sx={{ flexGrow: 1, minWidth: 0 }}>
            <Typography variant={compact ? "subtitle1" : "h6"} component="h3" noWrap>
              {displayName}
            </Typography>
            <Typography variant="body2" color="text.secondary" noWrap>
              @{user.username}
            </Typography>
            <Chip
              icon={roleDisplay.icon}
              label={roleDisplay.label}
              color={roleDisplay.color}
              size="small"
              sx={{ mt: 0.5 }}
            />
          </Box>

          {/* Status indicator */}
          <Tooltip title={user.is_active ? 'Active User' : 'Inactive User'}>
            <Box
              sx={{
                width: 12,
                height: 12,
                borderRadius: '50%',
                bgcolor: getUserStatusColor() === 'success' ? 'success.main' : 
                         getUserStatusColor() === 'warning' ? 'warning.main' :
                         getUserStatusColor() === 'error' ? 'error.main' : 'info.main'
              }}
            />
          </Tooltip>
        </Stack>
      </Box>

      <CardContent sx={{ flexGrow: 1, pt: compact ? 1 : 2 }}>
        {/* Contact Information */}
        <Stack spacing={1}>
          {user.email && (
            <Stack direction="row" alignItems="center" spacing={1}>
              <EmailIcon fontSize="small" color="action" />
              <Typography variant="body2" color="text.secondary" noWrap>
                {user.email}
              </Typography>
            </Stack>
          )}

          {user.phone_number && (
            <Stack direction="row" alignItems="center" spacing={1}>
              <PhoneIcon fontSize="small" color="action" />
              <Typography variant="body2" color="text.secondary">
                {user.phone_number}
              </Typography>
            </Stack>
          )}

          {user.department && (
            <Stack direction="row" alignItems="center" spacing={1}>
              <SchoolIcon fontSize="small" color="action" />
              <Typography variant="body2" color="text.secondary">
                {user.department}
              </Typography>
            </Stack>
          )}

          {user.student_id && (
            <Stack direction="row" alignItems="center" spacing={1}>
              <PersonIcon fontSize="small" color="action" />
              <Typography variant="body2" color="text.secondary">
                ID: {user.student_id}
              </Typography>
            </Stack>
          )}
        </Stack>

        {/* Profile Information */}
        {!compact && user.profile && (
          <>
            <Divider sx={{ my: 2 }} />
            <Stack spacing={1}>
              {user.profile.major && (
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    Major: <strong>{user.profile.major}</strong>
                  </Typography>
                </Box>
              )}

              {user.profile.academic_status && (
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    Status: <strong>{user.profile.academic_status}</strong>
                  </Typography>
                </Box>
              )}

              {user.profile.graduation_year && (
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    Graduation: <strong>{user.profile.graduation_year}</strong>
                  </Typography>
                </Box>
              )}

              {user.profile.office_location && (
                <Stack direction="row" alignItems="center" spacing={1}>
                  <LocationIcon fontSize="small" color="action" />
                  <Typography variant="caption" color="text.secondary">
                    {user.profile.office_location}
                  </Typography>
                </Stack>
              )}
            </Stack>
          </>
        )}

        {/* Statistics */}
        {showStatistics && !compact && (
          <>
            <Divider sx={{ my: 2 }} />
            <Stack spacing={1}>
              {user.role === 'STUDENT' && (
                <>
                  {user.active_enrollments !== undefined && (
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="caption" color="text.secondary">
                        Active Enrollments:
                      </Typography>
                      <Typography variant="caption" fontWeight="bold">
                        {user.active_enrollments}
                      </Typography>
                    </Stack>
                  )}
                  {user.completed_courses !== undefined && (
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="caption" color="text.secondary">
                        Completed Courses:
                      </Typography>
                      <Typography variant="caption" fontWeight="bold">
                        {user.completed_courses}
                      </Typography>
                    </Stack>
                  )}
                </>
              )}

              {user.role === 'PROFESSOR' && (
                <>
                  {user.teaching_courses !== undefined && (
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="caption" color="text.secondary">
                        Teaching Courses:
                      </Typography>
                      <Typography variant="caption" fontWeight="bold">
                        {user.teaching_courses}
                      </Typography>
                    </Stack>
                  )}
                </>
              )}
            </Stack>
          </>
        )}

        {/* Status badges */}
        <Stack direction="row" spacing={1} sx={{ mt: 2 }} flexWrap="wrap">
          {!user.is_active && (
            <Chip label="Inactive" color="error" size="small" />
          )}
          {user.is_staff && (
            <Chip label="Staff" color="info" size="small" />
          )}
          {user.is_superuser && (
            <Chip label="Superuser" color="error" size="small" />
          )}
        </Stack>

        {/* Last activity */}
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
          {getLastActivityDisplay()}
        </Typography>
      </CardContent>

      {/* Actions */}
      {showActions && currentUserRole === 'ADMIN' && (
        <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
          <Button 
            size="small" 
            onClick={() => onView?.(user.id)}
            variant="outlined"
          >
            View Profile
          </Button>

          <Stack direction="row" spacing={1}>
            <Tooltip title="Edit User">
              <Button 
                size="small" 
                onClick={() => onEdit?.(user.id)}
                color="primary"
              >
                <EditIcon fontSize="small" />
              </Button>
            </Tooltip>

            {user.is_active ? (
              <Tooltip title="Deactivate User">
                <Button 
                  size="small" 
                  onClick={() => onDeactivate?.(user.id)}
                  color="warning"
                  disabled={user.is_superuser}
                >
                  <BlockIcon fontSize="small" />
                </Button>
              </Tooltip>
            ) : (
              <Tooltip title="Activate User">
                <Button 
                  size="small" 
                  onClick={() => onActivate?.(user.id)}
                  color="success"
                >
                  <CheckCircleIcon fontSize="small" />
                </Button>
              </Tooltip>
            )}
          </Stack>
        </CardActions>
      )}
    </Card>
  );
};

export default EnhancedUserCard;

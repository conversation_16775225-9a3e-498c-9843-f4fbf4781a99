"""
Consolidated AI Service Compatibility Module

This module provides backward compatibility for the old consolidated_ai_service imports
while redirecting them to the new unified AI system.

This is a compatibility layer to prevent breaking existing code while transitioning
to the unified AI configuration system.
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

# Define base exceptions first
class AIServiceError(Exception):
    """Base exception for AI service errors."""
    pass


class ConfigurationError(AIServiceError):
    """Raised when there are configuration issues."""
    pass


class APIConnectionError(AIServiceError):
    """Raised when there are connection issues with the API."""
    pass


class ResponseGenerationError(AIServiceError):
    """Raised when there are issues generating responses."""
    pass


# Backward compatibility aliases
ConsolidatedAIError = AIServiceError
ContentGenerationError = ResponseGenerationError


def ai_service_improved():
    """Legacy function for getting AI service. Redirects to the new unified AI service."""
    logger.warning("ai_service_improved() is deprecated. Use get_ai_service instead.")
    return None


def get_ai_service():
    """Get the unified AI service instance."""
    try:
        from utils.ai.services import get_ai_service as unified_get_ai_service
        logger.info("Successfully imported unified AI service")
        return unified_get_ai_service()
    except Exception as e:
        logger.warning(f"Failed to initialize unified AI service: {e}")
        return None


def provide_tutoring_response(question: str, subject: str = "General", **kwargs) -> str:
    """Legacy tutoring response function."""
    logger.warning("provide_tutoring_response() is deprecated.")
    return "AI service unavailable. Please check configuration."


def refresh_ai_service():
    """Legacy function to refresh AI service."""
    logger.warning("refresh_ai_service() is deprecated.")
    pass


# Export all items for backward compatibility
__all__ = [
    # Main service
    'get_ai_service',
    'ai_service_improved',
    
    # Tutoring
    'provide_tutoring_response',
    
    # Exceptions (with aliases for backward compatibility)
    'AIServiceError',
    'ConsolidatedAIError',  # Alias
    'ContentGenerationError',  # Alias
    'ConfigurationError',
    'APIConnectionError',
    'ResponseGenerationError',
    
    # Utilities
    'refresh_ai_service',
]

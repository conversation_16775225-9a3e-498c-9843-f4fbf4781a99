"""Managementcommandtofixcoursemodelrelationshipsbetweencoursesinteractive_learningandcourse_generatorapps.Thiscommandconsolidatestheredundantrelationshipsandfixesmodelconflicts."""fromdjango.core.management.baseimportBaseCommandfromdjango.dbimporttransactionfromdjango.appsimportappsclassCommand(BaseCommand):help='Fixcoursemodelrelationshipsandresolveconflictsbetweenapps'defadd_arguments(selfparser):parser.add_argument('--dry-run'action='store_true'help='Showwhatwouldbedonewithoutmakingchanges')defhandle(self*args**options):dry_run=options['dry_run']ifdry_run:self.stdout.write(self.style.WARNING('DRYRUNMODE-Nochangeswillbemade'))self.stdout.write('Startingcourserelationshipfixes...')try:withtransaction.atomic():#Fixinteractivelearningrelationshipsself.fix_interactive_relationships(dry_run)#Fixcoursegeneratorrelationshipsself.fix_generator_relationships(dry_run)#Updatecourseflagsself.update_course_flags(dry_run)ifdry_run:#Rollbackthetransactionindryrunmodetransaction.set_rollback(True)self.stdout.write(self.style.SUCCESS('DRYRUNCOMPLETED-Nochangesmade'))else:self.stdout.write(self.style.SUCCESS('Courserelationshipsfixedsuccessfully'))exceptExceptionase:self.stdout.write(self.style.ERROR(f'Errorfixingrelationships:{str(e)}'))raisedeffix_interactive_relationships(selfdry_run):"""FixInteractiveCourseVersionrelationships"""self.stdout.write('Fixinginteractivelearningrelationships...')try:InteractiveCourseVersion=apps.get_model('InteractiveCourseVersion')Course=apps.get_model('courses''Course')#Countexistingrecordstotal_interactive=InteractiveCourseVersion.objects.count()self.stdout.write(f'Found{total_interactive}interactivecourseversions')iftotal_interactive>0:forinteractiveinInteractiveCourseVersion.objects.all():course_to_update=None#Checkforbase_course(newfield)orcourse(oldfield)ifhasattr(interactive'base_course')andinteractive.base_course:course_to_update=interactive.base_courseelifhasattr(interactive'course')andinteractive.course:course_to_update=interactive.course#Migrateoldcoursefieldtobase_coursefieldifbase_courseisemptyifhasattr(interactive'base_course')andnotinteractive.base_course:ifnotdry_run:interactive.base_course=interactive.courseinteractive.save(update_fields=['base_course'])self.stdout.write(f'-Migratedcourserelationshiptobase_coursefor{interactive}')ifcourse_to_update:ifnotdry_run:#Updatethecourse'shas_interactive_contentflagcourse_to_update.has_interactive_content=Truecourse_to_update.save(update_fields=['has_interactive_content'])self.stdout.write(f'-Updatedcourse{course_to_update.course_code}interactiveflag')exceptExceptionase:self.stdout.write(self.style.WARNING(f'Interactivelearningmodelsnotfoundorerror:{str(e)}'))deffix_generator_relationships(selfdry_run):"""FixGeneratedCourseContentrelationships"""self.stdout.write('Fixingcoursegeneratorrelationships...')try:GeneratedCourseContent=apps.get_model('course_generator''GeneratedCourseContent')Course=apps.get_model('courses''Course')#Countexistingrecordstotal_generated=GeneratedCourseContent.objects.count()self.stdout.write(f'Found{total_generated}generatedcoursecontents')iftotal_generated>0:forgeneratedinGeneratedCourseContent.objects.all():ifhasattr(generated'request')andgenerated.requestandgenerated.request.course:course=generated.request.courseifnotdry_run:#Updatethecourse'shas_ai_contentflagcourse.has_ai_content=Truecourse.save(update_fields=['has_ai_content'])self.stdout.write(f'-Updatedcourse{course.course_code}AIcontentflag')exceptExceptionase:self.stdout.write(self.style.WARNING(f'Coursegeneratormodelsnotfoundorerror:{str(e)}'))defupdate_course_flags(selfdry_run):"""Updatecourseflagsbasedonexistingrelationships"""self.stdout.write('Updatingcourseintegrationflags...')try:Course=apps.get_model('courses''Course')updated_count=0forcourseinCourse.objects.all():needs_update=Falseupdate_fields=[]#Checkforinteractivecontentifhasattr(course'interactive_version')andnotcourse.has_interactive_content:ifnotdry_run:course.has_interactive_content=Trueupdate_fields.append('has_interactive_content')needs_update=True#CheckforAIcontentifhasattr(course'generated_content')andnotcourse.has_ai_content:ifnotdry_run:course.has_ai_content=Trueupdate_fields.append('has_ai_content')needs_update=Trueifneeds_update:ifnotdry_runandupdate_fields:course.save(update_fields=update_fields)updated_count+=1self.stdout.write(f'-Updatedflagsforcourse{course.course_code}')self.stdout.write(f'Updated{updated_count}courses')exceptExceptionase:self.stdout.write(self.style.ERROR(f'Errorupdatingcourseflags:{str(e)}'))defvalidate_relationships(self):"""Validatethatrelationshipsarecorrectlysetup"""self.stdout.write('Validatingrelationships...')try:Course=apps.get_model('courses''Course')forcourseinCourse.objects.all():#Checkinteractivecontentconsistencyhas_interactive=hasattr(course'interactive_version')flag_interactive=getattr(course'has_interactive_content'False)ifhas_interactive!=flag_interactive:self.stdout.write(self.style.WARNING(f'Course{course.course_code}:interactivecontentmismatch'f'(has_version={has_interactive}flag={flag_interactive})'))#CheckAIcontentconsistencyhas_ai=hasattr(course'generated_content')flag_ai=getattr(course'has_ai_content'False)ifhas_ai!=flag_ai:self.stdout.write(self.style.WARNING(f'Course{course.course_code}:AIcontentmismatch'f'(has_content={has_ai}flag={flag_ai})'))self.stdout.write('Validationcompleted')exceptExceptionase:self.stdout.write(self.style.ERROR(f'Errorduringvalidation:{str(e)}'))
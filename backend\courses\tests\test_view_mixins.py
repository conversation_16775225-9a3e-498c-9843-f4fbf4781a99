"""
Tests for course view mixins to ensure proper permission handling,
enrollment, content access, and standardized responses.
"""
import pytest
from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework.exceptions import PermissionDenied, NotFound

from core.models import StudentLevel
from courses.models import Course, CourseEnrollment, CourseContent
from courses.models.department import Department
from courses.utils.view_mixins import (
    CoursePermissionMixin,
    CourseEnrollmentMixin,
    CourseContentMixin,
    StandardizedResponseMixin
)

User = get_user_model()


class TestCoursePermissionMixin(TestCase):
    """Test cases for CoursePermissionMixin"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create test users
        self.student = User.objects.create_user(
            username='teststudent',
            email='<EMAIL>',
            password='testpass123',
            user_type='STUDENT'
        )
        
        self.professor = User.objects.create_user(
            username='testprof',
            email='<EMAIL>',
            password='testpass123',
            user_type='PROFESSOR'
        )
        
        self.admin = User.objects.create_user(
            username='testadmin',
            email='<EMAIL>',
            password='testpass123',
            user_type='ADMIN',
            is_staff=True
        )
        
        # Create test department
        self.department = Department.objects.create(
            name='Computer Science',
            code='CS'
        )
        
        # Create test course
        self.course = Course.objects.create(
            title='Test Course',
            course_code='CS101',
            credits=3,
            semester='FALL',
            academic_year='2024-2025',
            department=self.department,
            professor=self.professor,
            is_published=True,
            is_active=True
        )
        
        # Create StudentLevel for student
        self.student_level = StudentLevel.objects.create(
            student=self.student,
            current_level=2
        )
    
    def test_student_can_view_published_course(self):
        """Test that students can view published courses"""
        self.client.force_authenticate(user=self.student)
        url = reverse('courses:course-detail', kwargs={'pk': self.course.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_student_cannot_view_unpublished_course(self):
        """Test that students cannot view unpublished courses"""
        self.course.is_published = False
        self.course.save()
        
        self.client.force_authenticate(user=self.student)
        url = reverse('courses:course-detail', kwargs={'pk': self.course.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_professor_can_view_own_course(self):
        """Test that professors can view their own courses"""
        self.client.force_authenticate(user=self.professor)
        url = reverse('courses:course-detail', kwargs={'pk': self.course.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_admin_can_view_any_course(self):
        """Test that admins can view any course"""
        self.course.is_published = False
        self.course.save()
        
        self.client.force_authenticate(user=self.admin)
        url = reverse('courses:course-detail', kwargs={'pk': self.course.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)


class TestCourseEnrollmentMixin(TestCase):
    """Test cases for CourseEnrollmentMixin"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create test users
        self.student = User.objects.create_user(
            username='teststudent',
            email='<EMAIL>',
            password='testpass123',
            user_type='STUDENT'
        )
        
        self.professor = User.objects.create_user(
            username='testprof',
            email='<EMAIL>',
            password='testpass123',
            user_type='PROFESSOR'
        )
        
        # Create test department
        self.department = Department.objects.create(
            name='Computer Science',
            code='CS'
        )
        
        # Create test courses
        self.course1 = Course.objects.create(
            title='Test Course 1',
            course_code='CS101',
            credits=3,
            semester='FALL',
            academic_year='2024-2025',
            department=self.department,
            professor=self.professor,
            is_published=True,
            is_active=True,
            capacity=30
        )
        
        self.course2 = Course.objects.create(
            title='Test Course 2',
            course_code='CS201',
            credits=3,
            semester='FALL',
            academic_year='2024-2025',
            department=self.department,
            professor=self.professor,
            is_published=True,
            is_active=True,
            capacity=25
        )
        
        # Create StudentLevel for student
        self.student_level = StudentLevel.objects.create(
            student=self.student,
            current_level=2
        )
    
    def test_enroll_student_success(self):
        """Test successful student enrollment"""
        self.client.force_authenticate(user=self.student)
        url = reverse('courses:course-enroll', kwargs={'pk': self.course1.pk})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check enrollment was created
        enrollment = CourseEnrollment.objects.filter(
            student=self.student,
            course=self.course1
        ).first()
        self.assertIsNotNone(enrollment)
        self.assertEqual(enrollment.status, 'ENROLLED')
    
    def test_enroll_already_enrolled(self):
        """Test enrolling when already enrolled"""
        # Create existing enrollment
        CourseEnrollment.objects.create(
            student=self.student,
            course=self.course1,
            status='ENROLLED'
        )
        
        self.client.force_authenticate(user=self.student)
        url = reverse('courses:course-enroll', kwargs={'pk': self.course1.pk})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('already enrolled', response.data['error'].lower())
    
    def test_unenroll_student_success(self):
        """Test successful student unenrollment"""
        # Create enrollment first
        enrollment = CourseEnrollment.objects.create(
            student=self.student,
            course=self.course1,
            status='ENROLLED'
        )
        
        self.client.force_authenticate(user=self.student)
        url = reverse('courses:course-unenroll', kwargs={'pk': self.course1.pk})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check enrollment was removed or status changed
        enrollment.refresh_from_db()
        self.assertEqual(enrollment.status, 'DROPPED')
    
    def test_unenroll_not_enrolled(self):
        """Test unenrolling when not enrolled"""
        self.client.force_authenticate(user=self.student)
        url = reverse('courses:course-unenroll', kwargs={'pk': self.course1.pk})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('not enrolled', response.data['error'].lower())


class TestCourseContentMixin(TestCase):
    """Test cases for CourseContentMixin"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create test users
        self.student = User.objects.create_user(
            username='teststudent',
            email='<EMAIL>',
            password='testpass123',
            user_type='STUDENT'
        )
        
        self.professor = User.objects.create_user(
            username='testprof',
            email='<EMAIL>',
            password='testpass123',
            user_type='PROFESSOR'
        )
        
        # Create test department
        self.department = Department.objects.create(
            name='Computer Science',
            code='CS'
        )
        
        # Create test course
        self.course = Course.objects.create(
            title='Test Course',
            course_code='CS101',
            credits=3,
            semester='FALL',
            academic_year='2024-2025',
            department=self.department,
            professor=self.professor,
            is_published=True,
            is_active=True
        )
        
        # Create test content
        self.content = CourseContent.objects.create(
            course=self.course,
            title='Test Content',
            content_type='LECTURE',
            description='Test content description',
            is_published=True
        )
        
        # Create StudentLevel for student
        self.student_level = StudentLevel.objects.create(
            student=self.student,
            current_level=2
        )
    
    def test_get_course_content_enrolled_student(self):
        """Test that enrolled students can access course content"""
        # Enroll student
        CourseEnrollment.objects.create(
            student=self.student,
            course=self.course,
            status='ENROLLED'
        )
        
        self.client.force_authenticate(user=self.student)
        url = reverse('courses:course-content', kwargs={'pk': self.course.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']), 1)
        self.assertEqual(response.data['data'][0]['title'], 'Test Content')
    
    def test_get_course_content_not_enrolled_student(self):
        """Test that non-enrolled students cannot access course content"""
        self.client.force_authenticate(user=self.student)
        url = reverse('courses:course-content', kwargs={'pk': self.course.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_get_course_content_professor(self):
        """Test that professors can access their course content"""
        self.client.force_authenticate(user=self.professor)
        url = reverse('courses:course-content', kwargs={'pk': self.course.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']), 1)


class TestStandardizedResponseMixin(TestCase):
    """Test cases for StandardizedResponseMixin"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test department
        self.department = Department.objects.create(
            name='Computer Science',
            code='CS'
        )
        
        # Create test course
        self.course = Course.objects.create(
            title='Test Course',
            course_code='CS101',
            credits=3,
            semester='FALL',
            academic_year='2024-2025',
            department=self.department,
            professor=self.user,
            is_published=True,
            is_active=True
        )
    
    def test_standardized_success_response(self):
        """Test that responses follow standardized format"""
        self.client.force_authenticate(user=self.user)
        url = reverse('courses:course-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check response structure
        self.assertIn('success', response.data)
        self.assertIn('data', response.data)
        self.assertIn('message', response.data)
        self.assertIn('pagination', response.data)
        
        self.assertTrue(response.data['success'])
        self.assertIsInstance(response.data['data'], list)
    
    def test_standardized_error_response(self):
        """Test that error responses follow standardized format"""
        # Test with unauthenticated request
        url = reverse('courses:course-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Check error response structure
        self.assertIn('success', response.data)
        self.assertIn('error', response.data)
        
        self.assertFalse(response.data['success'])


@pytest.mark.django_db
class TestViewMixinsIntegration:
    """Integration tests for view mixins working together"""
    
    @pytest.fixture
    def setup_data(self):
        """Set up test data for pytest"""
        # Create test users
        student = User.objects.create_user(
            username='teststudent_integration',
            email='<EMAIL>',
            password='testpass123',
            user_type='STUDENT'
        )
        
        professor = User.objects.create_user(
            username='testprof_integration',
            email='<EMAIL>',
            password='testpass123',
            user_type='PROFESSOR'
        )
        
        # Create test department
        department = Department.objects.create(
            name='Computer Science Integration',
            code='CSI'
        )
        
        # Create test course
        course = Course.objects.create(
            title='Integration Test Course',
            course_code='CSI101',
            credits=3,
            semester='FALL',
            academic_year='2024-2025',
            department=department,
            professor=professor,
            is_published=True,
            is_active=True,
            capacity=30
        )
        
        # Create StudentLevel for student
        student_level = StudentLevel.objects.create(
            student=student,
            current_level=2
        )
        
        return {
            'student': student,
            'professor': professor,
            'department': department,
            'course': course,
            'student_level': student_level
        }
    
    def test_complete_enrollment_flow(self, setup_data):
        """Test complete enrollment flow with all mixins"""
        client = APIClient()
        client.force_authenticate(user=setup_data['student'])
        
        # 1. First, check course is visible (permission mixin)
        course_url = reverse('courses:course-detail', kwargs={'pk': setup_data['course'].pk})
        response = client.get(course_url)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True  # Standardized response
        
        # 2. Enroll in course (enrollment mixin)
        enroll_url = reverse('courses:course-enroll', kwargs={'pk': setup_data['course'].pk})
        response = client.post(enroll_url)
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['success'] is True  # Standardized response
        
        # 3. Access course content (content mixin)
        content_url = reverse('courses:course-content', kwargs={'pk': setup_data['course'].pk})
        response = client.get(content_url)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True  # Standardized response
        
        # 4. Unenroll from course
        unenroll_url = reverse('courses:course-unenroll', kwargs={'pk': setup_data['course'].pk})
        response = client.post(unenroll_url)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['success'] is True  # Standardized response
    
    def test_permission_denied_flow(self, setup_data):
        """Test permission denied scenarios with standardized responses"""
        client = APIClient()
        client.force_authenticate(user=setup_data['student'])
        
        # Make course unpublished
        setup_data['course'].is_published = False
        setup_data['course'].save()
        
        # Try to access unpublished course
        course_url = reverse('courses:course-detail', kwargs={'pk': setup_data['course'].pk})
        response = client.get(course_url)
        assert response.status_code == status.HTTP_404_NOT_FOUND
        
        # Try to access content without enrollment
        setup_data['course'].is_published = True
        setup_data['course'].save()
        
        content_url = reverse('courses:course-content', kwargs={'pk': setup_data['course'].pk})
        response = client.get(content_url)
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert response.data['success'] is False  # Standardized error response

"""
Consolidated AI Services

This module consolidates all AI service functionality into a 
well-organized service class.
"""

import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class AIServiceError(Exception):
    """Custom exception for AI service errors"""
    pass


class AIService:
    """Unified AI service that consolidates functionality"""
    
    def __init__(self):
        self.config = {'default_model': 'gemini-2.0-flash', 'temperature': 0.7, 'max_tokens': 2048}
        self.gemini_model = None
        
    def generate_content(self, prompt: str, **kwargs) -> str:
        """Generate content using the configured AI model"""
        logger.warning("AI service not fully configured. Returning fallback response.")
        return "AI service is currently unavailable. Please try again later."
    
    def is_service_available(self) -> bool:
        """Check if the AI service is properly configured and available"""
        return False
    
    def refresh_configuration(self):
        """Refresh the service configuration"""
        logger.info("Refreshing AI service configuration")
        pass


# Global AI service instance
_ai_service_instance = None


def get_ai_service():
    """Get or create the global AI service instance"""
    global _ai_service_instance
    if _ai_service_instance is None:
        _ai_service_instance = AIService()
    return _ai_service_instance


def refresh_ai_service():
    """Force refresh of the AI service instance"""
    global _ai_service_instance
    if _ai_service_instance is not None:
        _ai_service_instance.refresh_configuration()
        logger.info("AI service instance refreshed")


def reset_ai_service():
    """Reset the AI service instance (will be recreated on next access)"""
    global _ai_service_instance
    _ai_service_instance = None
    logger.info("AI service instance reset")

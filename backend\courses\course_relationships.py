from django.db import models
from django.utils.translation import gettext_lazy as _

# Import base models from core app
try:
    from core.models import CourseRelationshipBase
except ImportError:
    # Create a placeholder base class if it doesn't exist
    class CourseRelationshipBase(models.Model):
        class Meta:
            abstract = True


class CourseRelationship(CourseRelationshipBase):
    """
    Model to track relationships between courses

    This model inherits from CourseRelationshipBase and adds specific fields
    for course-to-course relationships.
    """
    from_course = models.ForeignKey(
        "Course",
        on_delete=models.CASCADE,
        related_name="next_courses_relationships"
    )
    to_course = models.ForeignKey(
        "Course",
        on_delete=models.CASCADE,
        related_name="previous_courses_relationships"
    )

    class Meta:
        unique_together = ["from_course", "to_course", "relationship_type"]

    def __str__(self):
        return f"{self.from_course.course_code} → {self.to_course.course_code} ({self.get_relationship_type_display()})"
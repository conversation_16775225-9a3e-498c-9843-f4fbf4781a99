import React from 'react';
import { Box, Typography, Container, Paper } from '@mui/material';
import { Construction as ConstructionIcon } from '@mui/icons-material';

interface PlaceholderPageProps {
  title?: string;
  message?: string;
}

const PlaceholderPage: React.FC<PlaceholderPageProps> = ({
  title = 'Feature Under Development',
  message = 'This feature is currently being developed and will be available soon.',
}) => {
  return (
    <Container maxWidth='md'>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '60vh',
          textAlign: 'center',
          gap: 3,
        }}
      >
        <Paper
          elevation={3}
          sx={{
            p: 4,
            borderRadius: 2,
            maxWidth: '500px',
            width: '100%',
          }}
        >
          <ConstructionIcon
            sx={{
              fontSize: '4rem',
              color: 'warning.main',
              mb: 2,
            }}
          />

          <Typography
            variant='h4'
            sx={{
              fontWeight: 600,
              color: 'text.primary',
              mb: 2,
            }}
          >
            {title}
          </Typography>

          <Typography
            variant='body1'
            sx={{
              color: 'text.secondary',
              lineHeight: 1.6,
            }}
          >
            {message}
          </Typography>
        </Paper>
      </Box>
    </Container>
  );
};

export default PlaceholderPage;

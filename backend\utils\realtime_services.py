"""
Real-time Services for North Star University
Provides WebSocket support and real-time notifications
"""

import json
import asyncio
from datetime import timed<PERSON><PERSON>
from django.utils import timezone
from django.core.cache import cache
from typing import Dict, List, Any

class RealTimeService:
    """Service for managing real-time features"""
    
    def __init__(self):
        self.active_connections = {}
    
    def send_notification_to_user(self, user_id: int, notification_data: Dict[str, Any]):
        """Send notification to a specific user"""
        try:
            from channels.layers import get_channel_layer
            from asgiref.sync import async_to_sync
            
            channel_layer = get_channel_layer()
            
            if channel_layer:
                async_to_sync(channel_layer.group_send)(
                    f"user_{user_id}",
                    {
                        "type": "notification_message",
                        "data": notification_data,
                        "timestamp": timezone.now().isoformat(),
                    }
                )
                return True
        except Exception as e:
            print(f"Error sending notification: {e}")
            return False
    
    def send_analytics_update(self, user_id: int = None, data: Dict[str, Any] = None):
        """Send analytics update to user(s)"""
        try:
            from channels.layers import get_channel_layer
            from asgiref.sync import async_to_sync
            
            channel_layer = get_channel_layer()
            
            if not channel_layer:
                return False
            
            message = {
                "type": "analytics_update",
                "data": data or {},
                "timestamp": timezone.now().isoformat(),
            }
            
            if user_id:
                # Send to specific user
                async_to_sync(channel_layer.group_send)(
                    f"user_{user_id}", message
                )
            else:
                # Send to all admin users
                async_to_sync(channel_layer.group_send)(
                    "role_admin", message
                )
            
            return True
        except Exception as e:
            print(f"Error sending analytics update: {e}")
            return False
    
    def send_course_update(self, course_id: int, update_data: Dict[str, Any]):
        """Send course update to all enrolled students"""
        try:
            from channels.layers import get_channel_layer
            from asgiref.sync import async_to_sync
            
            channel_layer = get_channel_layer()
            
            if channel_layer:
                async_to_sync(channel_layer.group_send)(
                    f"course_{course_id}",
                    {
                        "type": "course_update",
                        "data": update_data,
                        "timestamp": timezone.now().isoformat(),
                    }
                )
                return True
        except Exception as e:
            print(f"Error sending course update: {e}")
            return False
    
    def send_university_announcement(self, announcement_data: Dict[str, Any]):
        """Send university-wide announcement"""
        try:
            from channels.layers import get_channel_layer
            from asgiref.sync import async_to_sync
            
            channel_layer = get_channel_layer()
            
            if channel_layer:
                async_to_sync(channel_layer.group_send)(
                    "university_wide",
                    {
                        "type": "university_announcement",
                        "data": announcement_data,
                        "timestamp": timezone.now().isoformat(),
                    }
                )
                return True
        except Exception as e:
            print(f"Error sending university announcement: {e}")
            return False
    
    def get_active_users_count(self) -> int:
        """Get count of currently active users"""
        try:
            # This would be implemented with proper connection tracking
            # For now, return a mock value
            cached_count = cache.get('active_users_count', 0)
            return cached_count
        except:
            return 0
    
    def track_user_connection(self, user_id: int, channel_name: str):
        """Track user connection"""
        try:
            connections = cache.get('active_connections', {})
            connections[str(user_id)] = {
                'channel_name': channel_name,
                'connected_at': timezone.now().isoformat(),
            }
            cache.set('active_connections', connections, 3600)  # 1 hour
            
            # Update active users count
            count = len(connections)
            cache.set('active_users_count', count, 3600)
            
        except Exception as e:
            print(f"Error tracking user connection: {e}")
    
    def remove_user_connection(self, user_id: int):
        """Remove user connection"""
        try:
            connections = cache.get('active_connections', {})
            if str(user_id) in connections:
                del connections[str(user_id)]
                cache.set('active_connections', connections, 3600)
                
                # Update active users count
                count = len(connections)
                cache.set('active_users_count', count, 3600)
                
        except Exception as e:
            print(f"Error removing user connection: {e}")


class OfflineAnalyticsService:
    """Service for handling analytics when offline"""
    
    def __init__(self):
        self.cache_key_prefix = 'offline_analytics_'
    
    def cache_analytics_data(self, data: Dict[str, Any], cache_duration: int = 3600):
        """Cache analytics data for offline access"""
        try:
            cache_key = f"{self.cache_key_prefix}dashboard"
            cache.set(cache_key, data, cache_duration)
            return True
        except Exception as e:
            print(f"Error caching analytics data: {e}")
            return False
    
    def get_cached_analytics(self) -> Dict[str, Any]:
        """Get cached analytics data"""
        try:
            cache_key = f"{self.cache_key_prefix}dashboard"
            return cache.get(cache_key, {})
        except Exception as e:
            print(f"Error getting cached analytics: {e}")
            return {}
    
    def store_offline_activity(self, user_id: int, activity_data: Dict[str, Any]):
        """Store user activity for offline sync"""
        try:
            cache_key = f"{self.cache_key_prefix}activities_{user_id}"
            activities = cache.get(cache_key, [])
            
            activity_data['timestamp'] = timezone.now().isoformat()
            activities.append(activity_data)
            
            # Keep only last 100 activities
            if len(activities) > 100:
                activities = activities[-100:]
            
            cache.set(cache_key, activities, 86400)  # 24 hours
            return True
        except Exception as e:
            print(f"Error storing offline activity: {e}")
            return False
    
    def sync_offline_activities(self, user_id: int) -> bool:
        """Sync offline activities when connection is restored"""
        try:
            from .analytics_service import analytics_service
            from django.contrib.auth import get_user_model
            
            User = get_user_model()
            cache_key = f"{self.cache_key_prefix}activities_{user_id}"
            activities = cache.get(cache_key, [])
            
            if not activities:
                return True
            
            user = User.objects.get(id=user_id)
            
            # Sync all cached activities
            for activity in activities:
                analytics_service.track_user_activity(
                    user=user,
                    activity_type=activity.get('activity_type', 'unknown'),
                    metadata=activity.get('metadata', {})
                )
            
            # Clear cache after successful sync
            cache.delete(cache_key)
            return True
            
        except Exception as e:
            print(f"Error syncing offline activities: {e}")
            return False


class PWANotificationService:
    """Service for PWA push notifications"""
    
    def __init__(self):
        self.vapid_private_key = None  # Would be set from settings
        self.vapid_public_key = None   # Would be set from settings
    
    def send_push_notification(self, user_id: int, notification_data: Dict[str, Any]):
        """Send push notification to PWA"""
        try:
            # This would integrate with a push notification service
            # For now, we'll store the notification for future delivery
            cache_key = f"pending_push_notifications_{user_id}"
            notifications = cache.get(cache_key, [])
            
            notifications.append({
                'data': notification_data,
                'timestamp': timezone.now().isoformat(),
            })
            
            cache.set(cache_key, notifications, 86400)  # 24 hours
            return True
            
        except Exception as e:
            print(f"Error sending push notification: {e}")
            return False
    
    def get_pending_notifications(self, user_id: int) -> List[Dict[str, Any]]:
        """Get pending push notifications for user"""
        try:
            cache_key = f"pending_push_notifications_{user_id}"
            notifications = cache.get(cache_key, [])
            
            # Clear notifications after retrieval
            if notifications:
                cache.delete(cache_key)
            
            return notifications
        except Exception as e:
            print(f"Error getting pending notifications: {e}")
            return []


# Singleton instances
realtime_service = RealTimeService()
offline_analytics_service = OfflineAnalyticsService()
pwa_notification_service = PWANotificationService()

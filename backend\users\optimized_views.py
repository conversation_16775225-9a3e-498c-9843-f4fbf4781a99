"""OptimizedUserViewsThismoduleprovidesoptimizedversionsofuserviewswithbetterqueryperformancetoreplacetheN+1queryproblemsintheoriginalviews."""import loggingfrom django.contrib.authimportget_user_modelfrom django.dbimportmodelsfrom django.db.modelsimportAvgCountPrefetchQfromrest_frameworkimportstatusfromrest_framework.decoratorsimportactionfromrest_framework.permissionsimportIsAuthenticatedfromrest_framework.responseimportResponsefrom core.utils.api_responseimporterror_responsesuccess_responsefrom.serializersimportUserSerializerfrom.viewsimportUserViewSet#Importtheoriginalviewsetlogger=logging.getLogger(__name__)User=get_user_model()classOptimizedUserViewSet(UserViewSet):"""OptimizedversionofUserViewSetwithbetterqueryperformanceThisviewsetaddressestheN+1queryproblemsidentifiedinthecodereviewbyusingproperprefetchingandselect_relatedoptimizations."""defget_queryset(self):"""OptimizedquerysetwithproperprefetchingtoavoidN+1queries"""#Importmodelsdynamicallytoavoidcircularimportstry:from django.appsimportappsEnrollment=apps.get_model("courses""Enrollment")CourseGrade=apps.get_model("grades""CourseGrade")Course=apps.get_model("courses""Course")Attendance=apps.get_model("courses""Attendance")exceptExceptionase:logger.warning(f"Couldnotimportmodelsforoptimization:{e}")returnsuper().get_queryset()#Buildoptimizedquerysetqueryset=(User.objects.select_related("department")#Optimizeforeignkeyaccess.prefetch_related(#OptimizeenrollmentswithrelatedcoursedataPrefetch("enrollments"queryset=Enrollment.objects.select_related("course").prefetch_related("attendances").filter(is_active=True))#OptimizecoursegradesPrefetch("course_grades"queryset=CourseGrade.objects.select_related("course"))#Optimizecoursestaught(forprofessors)Prefetch("courses_taught"queryset=Course.objects.annotate(students_count=Count("enrollments")))).annotate(#Addcomputedfieldstoreduceadditionalqueriestotal_courses=Count("enrollments"filter=Q(enrollments__is_active=True))avg_grade=Avg("course_grades__numeric_grade")total_taught_courses=Count("courses_taught")))#Applyrolefilteringrole=self.request.query_params.get("role"None)ifrole:queryset=queryset.filter(role=role.upper())#Filterbasedonviewsetbasenameifhasattr(self"basename"):ifself.basename=="professor":queryset=queryset.filter(role="PROFESSOR")elifself.basename=="student":queryset=queryset.filter(role="STUDENT")returnqueryset@action(detail=Truemethods=["get"])defcourses(selfrequestpk=None):"""OptimizedcoursesendpointwithminimaldatabasequeriesThismethodfixestheN+1queryproblembyusingprefetcheddatainsteadofmakingseparatequeriesforeachenrollment."""user=self.get_object()ifuser.role=="STUDENT":#Useprefetchedenrollmentsdata(alreadyoptimizedinget_queryset)enrollments=user.enrollments.all()#Thisusesprefetcheddatacourses_data=[]forenrollmentinenrollments:course=enrollment.course#Alreadyprefetched#Getgradeusingprefetcheddatacourse_grade=Noneforgradeinuser.course_grades.all():#Prefetchedifgrade.course_id==course.id:course_grade=gradebreak#Calculateattendanceusingprefetcheddataattendances=enrollment.attendances.all()#Prefetchedattendance_rate=Noneifattendances:present_count=sum(1forainattendancesifa.is_present)attendance_rate=round((present_count/len(attendances))*1001)course_data={"id":course.id"code":getattr(course"code"getattr(course"course_code"""))"title":course.title"credits":course.credits"enrollment_date":enrollment.date_enrolled"grade":course_grade.gradeifcourse_gradeelseNone"numeric_grade":(course_grade.numeric_gradeifcourse_gradeelseNone)"attendance_rate":attendance_rate"professor":({"id":course.professor.idifcourse.professorelseNone"name":(f"{course.professor.first_name}{course.professor.last_name}"ifcourse.professorelseNone)}ifhasattr(course"professor")andcourse.professorelseNone)}courses_data.append(course_data)returnsuccess_response(data=courses_datamessage="Studentcoursesretrievedsuccessfully"meta={"total_courses":len(courses_data)"avg_grade":user.avg_grade#Fromannotation"performance_summary":self._get_performance_summary(courses_data)})elifuser.role=="PROFESSOR":#Useprefetchedcoursestaughtdatacourses=user.courses_taught.all()#Alreadyannotatedwithstudents_countcourses_data=[]forcourseincourses:course_data={"id":course.id"code":getattr(course"code"getattr(course"course_code"""))"title":course.title"credits":course.credits"students_count":course.students_count#Fromannotation"capacity":getattr(course"capacity"None)"department":({"id":(course.department.idifhasattr(course"department")andcourse.departmentelseNone)"name":(course.department.nameifhasattr(course"department")andcourse.departmentelseNone)}ifhasattr(course"department")elseNone)}courses_data.append(course_data)returnsuccess_response(data=courses_datamessage="Professorcoursesretrievedsuccessfully"meta={"total_courses":user.total_taught_courses#Fromannotation"total_students":sum(c["students_count"]forcincourses_data)})else:returnerror_response(message="Userisneitherastudentnoraprofessor"status_code=status.HTTP_400_BAD_REQUEST)@action(detail=Truemethods=["get"])defgrades(selfrequestpk=None):"""Optimizedgradesendpointusingprefetcheddata"""user=self.get_object()ifuser.role!="STUDENT":returnerror_response(message="Userisnotastudent"status_code=status.HTTP_400_BAD_REQUEST)#Useprefetchedcoursegradescourse_grades=user.course_grades.all()#Alreadyprefetchedwithcoursedatagrades_data=[]total_points=0total_credits=0forgradeincourse_grades:course=grade.course#Alreadyprefetchedgrade_data={"course_code":getattr(course"code"getattr(course"course_code"""))"course_title":course.title"credits":course.credits"grade":grade.grade"numeric_grade":grade.numeric_grade"semester":getattr(grade"semester"None)"year":getattr(grade"year"None)}grades_data.append(grade_data)#CalculateGPAifgrade.numeric_gradeandcourse.credits:total_points+=grade.numeric_grade*course.creditstotal_credits+=course.creditsgpa=round(total_points/total_credits2)iftotal_credits>0elseNonereturnsuccess_response(data={"grades":grades_data"gpa":gpa"total_credits":total_credits"grade_distribution":self._get_grade_distribution(grades_data)}message="Studentgradesretrievedsuccessfully")def_get_performance_summary(selfcourses_data):"""Generateperformancesummaryforstudentcourses"""ifnotcourses_data:returnNonegrades=[c["numeric_grade"]forcincourses_dataifc["numeric_grade"]isnotNone]attendances=[c["attendance_rate"]forcincourses_dataifc["attendance_rate"]isnotNone]return{"total_courses":len(courses_data)"courses_with_grades":len(grades)"average_grade":round(sum(grades)/len(grades)2)ifgradeselseNone"average_attendance":(round(sum(attendances)/len(attendances)2)ifattendanceselseNone)"highest_grade":max(grades)ifgradeselseNone"lowest_grade":min(grades)ifgradeselseNone}def_get_grade_distribution(selfgrades_data):"""Generategradedistributionstatistics"""ifnotgrades_data:returnNonegrades=[g["numeric_grade"]forgingrades_dataifg["numeric_grade"]isnotNone]ifnotgrades:returnNonedistribution={"A":len([gforgingradesifg>=90])"B":len([gforgingradesif80<=g<90])"C":len([gforgingradesif70<=g<80])"D":len([gforgingradesif60<=g<70])"F":len([gforgingradesifg<60])}returndistributiondeflist(selfrequest*args**kwargs):"""Optimizedlistmethodwithperformancemonitoring"""from django.confimportsettingsfrom django.dbimportconnection#Trackquerycountforperformancemonitoringinitial_query_count=len(connection.queries)ifsettings.DEBUGelse0#Callparentlistmethodresponse=super().list(request*args**kwargs)#Addperformancemetadataindebugmodeifsettings.DEBUG:final_query_count=len(connection.queries)query_count=final_query_count-initial_query_countifhasattr(response"data")andisinstance(response.datadict):response.data["_performance"]={"query_count":query_count"optimized":True}#Logperformancewarningiftoomanyqueriesifquery_count>10:logger.warning(f"Highquerycountinuserlist:{query_count}queries")returnresponse#Utilityfunctiontoreplacetheoriginalviewsetdefget_optimized_user_viewset():"""FactoryfunctiontogettheoptimizeduserviewsetThiscanbeusedinURLstoreplacetheoriginalUserViewSet"""returnOptimizedUserViewSet
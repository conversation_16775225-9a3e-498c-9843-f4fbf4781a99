/**
 * Utility Functions Tests
 *
 * Comprehensive tests for all utility functions used by AI services
 * including error handling, caching, and service utilities.
 */

import {
  AIServiceError,
  AIServiceConnectionError,
  AIServiceTimeoutError,
  AIServiceValidationError,
  createFallbackResponse,
  getCacheStats,
  clearAIServiceCache,
  parseAIResponse,
  validateServiceConfig,
  createServiceInstance,
} from '../utils/aiServiceUtils';

import {
  ERROR_MESSAGES,
  ErrorSeverity,
  ErrorCategory,
  getErrorInfo,
  logError,
  createUserErrorMessage,
  shouldUseFallback,
  getRetryDelay,
} from '../utils/errorHandling';

import { BaseAIService } from '../utils/BaseAIService';

// Mock console methods
const originalConsole = console;
beforeAll(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
  console.group = jest.fn();
  console.groupEnd = jest.fn();
});

afterAll(() => {
  console.log = originalConsole.log;
  console.warn = originalConsole.warn;
  console.error = originalConsole.error;
  console.group = originalConsole.group;
  console.groupEnd = originalConsole.groupEnd;
});

describe('AI Service Utilities', () => {
  describe('Error Classes', () => {
    test('AIServiceError should have correct properties', () => {
      const error = new AIServiceError('Test message', 'TEST_CODE', 500, {
        original: 'error',
      });

      expect(error.name).toBe('AIServiceError');
      expect(error.message).toBe('Test message');
      expect(error.code).toBe('TEST_CODE');
      expect(error.statusCode).toBe(500);
      expect(error.originalError).toEqual({ original: 'error' });
      expect(error instanceof Error).toBe(true);
    });

    test('AIServiceConnectionError should have correct defaults', () => {
      const error = new AIServiceConnectionError('Connection failed');

      expect(error.code).toBe('CONNECTION_ERROR');
      expect(error.message).toBe('Connection failed');
      expect(error instanceof AIServiceError).toBe(true);
    });

    test('AIServiceTimeoutError should have correct defaults', () => {
      const error = new AIServiceTimeoutError('Request timed out');

      expect(error.code).toBe('TIMEOUT_ERROR');
      expect(error.message).toBe('Request timed out');
      expect(error instanceof AIServiceError).toBe(true);
    });

    test('AIServiceValidationError should have correct defaults', () => {
      const error = new AIServiceValidationError('Invalid input');

      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.message).toBe('Invalid input');
      expect(error instanceof AIServiceError).toBe(true);
    });
  });

  describe('Fallback Response Generation', () => {
    test('should create general fallback response', () => {
      const fallback = createFallbackResponse('general');

      expect(fallback.status).toBe('fallback');
      expect(fallback.message).toBeDefined();
      expect(typeof fallback.message).toBe('string');
    });

    test('should create suggestions fallback response', () => {
      const fallback = createFallbackResponse('suggestions');

      expect(fallback.status).toBe('fallback');
      expect(fallback.suggestions).toBeDefined();
      expect(Array.isArray(fallback.suggestions)).toBe(true);
      expect(fallback.suggestions.length).toBeGreaterThan(0);
    });

    test('should create content fallback response', () => {
      const fallback = createFallbackResponse('content');

      expect(fallback.status).toBe('fallback');
      expect(fallback.content).toBeDefined();
      expect(typeof fallback.content).toBe('string');
    });

    test('should create analysis fallback response', () => {
      const fallback = createFallbackResponse('analysis');

      expect(fallback.status).toBe('fallback');
      expect(fallback.analysis).toBeDefined();
      expect(typeof fallback.analysis).toBe('object');
    });

    test('should merge custom data into fallback response', () => {
      const customData = { customField: 'test value', customNumber: 42 };
      const fallback = createFallbackResponse('general', customData);

      expect(fallback.status).toBe('fallback');
      expect(fallback.customField).toBe('test value');
      expect(fallback.customNumber).toBe(42);
    });

    test('should handle unknown fallback types', () => {
      const fallback = createFallbackResponse('unknown_type' as any);

      expect(fallback.status).toBe('fallback');
      expect(fallback.message).toBeDefined();
    });
  });

  describe('Cache Management', () => {
    test('should provide cache statistics', () => {
      const stats = getCacheStats();

      expect(stats).toBeDefined();
      expect(typeof stats.totalEntries).toBe('number');
      expect(typeof stats.hitRate).toBe('number');
      expect(typeof stats.missRate).toBe('number');
      expect(Array.isArray(stats.services)).toBe(true);
      expect(stats.hitRate + stats.missRate).toBeCloseTo(1, 2);
    });

    test('should clear all cache', () => {
      clearAIServiceCache();
      const stats = getCacheStats();
      expect(stats.totalEntries).toBe(0);
    });

    test('should clear specific service cache', () => {
      clearAIServiceCache('test_service');
      // Should not throw
      expect(true).toBe(true);
    });
  });

  describe('Response Parsing', () => {
    test('should parse valid AI response', () => {
      const validResponse = {
        data: { result: 'success', content: 'test content' },
      };

      const parsed = parseAIResponse(validResponse);
      expect(parsed).toEqual({ result: 'success', content: 'test content' });
    });

    test('should handle response without data property', () => {
      const directResponse = { result: 'success', content: 'test content' };

      const parsed = parseAIResponse(directResponse);
      expect(parsed).toEqual({ result: 'success', content: 'test content' });
    });

    test('should handle null/undefined responses', () => {
      expect(parseAIResponse(null)).toBeNull();
      expect(parseAIResponse(undefined)).toBeUndefined();
    });
  });

  describe('Service Configuration Validation', () => {
    test('should validate correct service configuration', () => {
      const validConfig = {
        timeout: 30000,
        retries: 2,
        fallbackEnabled: true,
        cacheEnabled: true,
        cacheTTL: 300000,
      };

      expect(() => validateServiceConfig(validConfig)).not.toThrow();
    });

    test('should reject invalid timeout values', () => {
      const invalidConfig = {
        timeout: -1000,
        retries: 2,
        fallbackEnabled: true,
      };

      expect(() => validateServiceConfig(invalidConfig)).toThrow();
    });

    test('should reject invalid retry values', () => {
      const invalidConfig = {
        timeout: 30000,
        retries: -1,
        fallbackEnabled: true,
      };

      expect(() => validateServiceConfig(invalidConfig)).toThrow();
    });

    test('should provide default values for missing properties', () => {
      const partialConfig = {
        timeout: 30000,
      };

      const validated = validateServiceConfig(partialConfig);
      expect(validated.retries).toBeDefined();
      expect(validated.fallbackEnabled).toBeDefined();
      expect(validated.cacheEnabled).toBeDefined();
    });
  });

  describe('Service Instance Creation', () => {
    test('should create service instance with valid configuration', () => {
      const config = {
        serviceName: 'Test Service',
        baseEndpoint: '/api/v1/test',
        config: {
          timeout: 30000,
          retries: 2,
          fallbackEnabled: true,
        },
      };

      const instance = createServiceInstance(config);
      expect(instance).toBeInstanceOf(BaseAIService);
      expect(instance.getServiceInfo().name).toBe('Test Service');
    });

    test('should handle missing configuration gracefully', () => {
      const minimalConfig = {
        serviceName: 'Minimal Service',
        baseEndpoint: '/api/v1/minimal',
      };

      const instance = createServiceInstance(minimalConfig);
      expect(instance).toBeInstanceOf(BaseAIService);
      expect(instance.getConfig()).toBeDefined();
    });
  });
});

describe('Error Handling Utilities', () => {
  describe('Error Information Extraction', () => {
    test('should extract error info from AIServiceError', () => {
      const error = new AIServiceError('Server error', 'SERVER_ERROR', 500);
      const info = getErrorInfo(error);

      expect(info.message).toBe('Server error');
      expect(info.category).toBe(ErrorCategory.SERVER);
      expect(info.severity).toBe(ErrorSeverity.HIGH);
      expect(info.retryable).toBe(true);
      expect(info.reportable).toBe(true);
    });

    test('should extract error info from HTTP errors', () => {
      const httpError = {
        response: {
          status: 404,
          data: { message: 'Not found' },
        },
      };

      const info = getErrorInfo(httpError);
      expect(info.category).toBe(ErrorCategory.CLIENT);
      expect(info.severity).toBe(ErrorSeverity.MEDIUM);
      expect(info.retryable).toBe(false);
    });

    test('should extract error info from network errors', () => {
      const networkError = {
        request: {},
        message: 'Network Error',
      };

      const info = getErrorInfo(networkError);
      expect(info.category).toBe(ErrorCategory.NETWORK);
      expect(info.severity).toBe(ErrorSeverity.HIGH);
      expect(info.retryable).toBe(true);
    });

    test('should handle unknown errors', () => {
      const unknownError = new Error('Unknown error');
      const info = getErrorInfo(unknownError);

      expect(info.category).toBe(ErrorCategory.UNKNOWN);
      expect(info.severity).toBe(ErrorSeverity.MEDIUM);
      expect(info.retryable).toBe(true);
    });
  });

  describe('User-Friendly Error Messages', () => {
    test('should create user-friendly messages', () => {
      const error = new AIServiceError(
        'Internal server error',
        'SERVER_ERROR',
        500
      );
      const userMessage = createUserErrorMessage(error, 'Loading data');

      expect(userMessage).toContain('Loading data');
      expect(userMessage).toContain('try again');
      expect(userMessage).not.toContain('Internal server error');
    });

    test('should handle errors without context', () => {
      const error = new AIServiceError('Server error', 'SERVER_ERROR', 500);
      const userMessage = createUserErrorMessage(error);

      expect(userMessage).toBeDefined();
      expect(typeof userMessage).toBe('string');
      expect(userMessage.length).toBeGreaterThan(0);
    });
  });

  describe('Fallback Decision Logic', () => {
    test('should recommend fallback for server errors', () => {
      const serverError = new AIServiceError(
        'Server error',
        'SERVER_ERROR',
        500
      );
      expect(shouldUseFallback(serverError)).toBe(true);
    });

    test('should not recommend fallback for validation errors', () => {
      const validationError = new AIServiceError(
        'Invalid input',
        'VALIDATION_ERROR',
        400
      );
      expect(shouldUseFallback(validationError)).toBe(false);
    });

    test('should recommend fallback for connection errors', () => {
      const connectionError = new AIServiceConnectionError('Connection failed');
      expect(shouldUseFallback(connectionError)).toBe(true);
    });

    test('should recommend fallback for timeout errors', () => {
      const timeoutError = new AIServiceTimeoutError('Request timed out');
      expect(shouldUseFallback(timeoutError)).toBe(true);
    });
  });

  describe('Retry Delay Calculation', () => {
    test('should calculate retry delays for retryable errors', () => {
      const serverError = new AIServiceError(
        'Server error',
        'SERVER_ERROR',
        500
      );

      const delay1 = getRetryDelay(serverError, 1);
      const delay2 = getRetryDelay(serverError, 2);
      const delay3 = getRetryDelay(serverError, 3);

      expect(delay1).toBeGreaterThan(0);
      expect(delay2).toBeGreaterThan(delay1);
      expect(delay3).toBeGreaterThan(delay2);
    });

    test('should return zero delay for non-retryable errors', () => {
      const validationError = new AIServiceError(
        'Invalid input',
        'VALIDATION_ERROR',
        400
      );
      const delay = getRetryDelay(validationError, 1);

      expect(delay).toBe(0);
    });

    test('should cap maximum delay', () => {
      const serverError = new AIServiceError(
        'Server error',
        'SERVER_ERROR',
        500
      );
      const delay = getRetryDelay(serverError, 10); // High attempt number

      expect(delay).toBeLessThanOrEqual(30000); // Should be capped at 30 seconds
    });

    test('should handle rate limit errors with longer delays', () => {
      const rateLimitError = new AIServiceError(
        'Rate limited',
        'RATE_LIMIT',
        429
      );
      const delay = getRetryDelay(rateLimitError, 1);

      expect(delay).toBeGreaterThanOrEqual(5000); // Should be at least 5 seconds for rate limits
    });
  });

  describe('Error Logging', () => {
    test('should log errors with context', () => {
      const error = new AIServiceError('Test error', 'TEST_ERROR', 500);
      const additionalInfo = { userId: 123, action: 'test' };

      // Should not throw
      expect(() =>
        logError(error, 'Test context', additionalInfo)
      ).not.toThrow();
    });

    test('should log errors without additional info', () => {
      const error = new Error('Simple error');

      // Should not throw
      expect(() => logError(error, 'Simple context')).not.toThrow();
    });
  });

  describe('Error Message Constants', () => {
    test('should have all required error messages', () => {
      expect(ERROR_MESSAGES.CONNECTION_ERROR).toBeDefined();
      expect(ERROR_MESSAGES.TIMEOUT_ERROR).toBeDefined();
      expect(ERROR_MESSAGES.AUTH_ERROR).toBeDefined();
      expect(ERROR_MESSAGES.VALIDATION_ERROR).toBeDefined();
      expect(ERROR_MESSAGES.SERVER_ERROR).toBeDefined();
      expect(ERROR_MESSAGES.RATE_LIMIT).toBeDefined();
      expect(ERROR_MESSAGES.UNKNOWN_ERROR).toBeDefined();
    });

    test('should have user-friendly error messages', () => {
      Object.values(ERROR_MESSAGES).forEach(message => {
        expect(typeof message).toBe('string');
        expect(message.length).toBeGreaterThan(0);
        expect(message).not.toContain('500');
        expect(message).not.toContain('404');
        expect(message).not.toContain('error code');
      });
    });
  });
});

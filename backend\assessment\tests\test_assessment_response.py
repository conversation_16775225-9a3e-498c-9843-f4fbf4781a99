"""Tests for the AssessmentResponse model."""
from django.contrib.auth import get_user_model
from django.test import TestCase
from django.utils import timezone
from assessment.models import Assessment, AssessmentQuestion, AssessmentResponse

User = get_user_model()


class AssessmentResponseTestCase(TestCase):
    """Test case for the AssessmentResponse model"""
    
    def setUp(self):
        """Set up test data"""
        # Create users
        self.admin_user = User.objects.create_superuser(
            username="admin",
            email="<EMAIL>",
            password="password123"
        )
        self.student_user = User.objects.create_user(
            username="student",
            email="<EMAIL>",
            password="password123"
        )
        
        # Create a question
        self.question = AssessmentQuestion.objects.create(
            question_text="What is the capital of France?",
            question_type="MULTIPLE_CHOICE",
            options=["Paris", "London", "Berlin", "Madrid"],
            correct_answer={"answer": "Paris", "explanation": "Paris is the capital of France"},
            category="general",
            difficulty_level=1,
            points=10,
            created_by=self.admin_user
        )
        
        # Create an assessment
        self.assessment = Assessment.objects.create(
            title="General Knowledge Test",
            description="Test your general knowledge",
            assessment_type="QUIZ",
            student=self.student_user,
            status="IN_PROGRESS",
            start_time=timezone.now()
        )
        
        # Create a response
        self.response = AssessmentResponse.objects.create(
            assessment=self.assessment,
            question=self.question,
            student=self.student_user,
            answer_text="Paris",
            started_at=timezone.now(),
            submitted_at=timezone.now() + timezone.timedelta(minutes=1)
        )
    
    def test_evaluate_correct_answer(self):
        """Test evaluation of a correct answer"""
        self.response.evaluate()
        self.assertTrue(self.response.is_correct)
        self.assertEqual(self.response.points_earned, self.question.points)
    
    def test_evaluate_incorrect_answer(self):
        """Test evaluation of an incorrect answer"""
        self.response.answer_text = "London"
        self.response.save()
        self.response.evaluate()
        self.assertFalse(self.response.is_correct)
        self.assertEqual(self.response.points_earned, 0)
    
    def test_evaluate_with_legacy_fields(self):
        """Test evaluation with legacy answer fields"""
        # Create a response with only the legacy answer field
        response = AssessmentResponse.objects.create(
            assessment=self.assessment,
            question=self.question,
            student=self.student_user,
            answer="Paris",  # Using legacy field
            answer_text=None,  # Primary field is empty
            started_at=timezone.now(),
            submitted_at=timezone.now() + timezone.timedelta(minutes=1)
        )
        response.evaluate()
        self.assertTrue(response.is_correct)
        self.assertEqual(response.points_earned, self.question.points)
        
        # Create a response with only the legacy student_answer field
        response = AssessmentResponse.objects.create(
            assessment=self.assessment,
            question=self.question,
            student=self.student_user,
            student_answer={"answer": "Paris"},  # Using legacy JSON field
            answer_text=None,  # Primary field is empty
            answer=None,  # Legacy text field is empty
            started_at=timezone.now(),
            submitted_at=timezone.now() + timezone.timedelta(minutes=1)
        )
        response.evaluate()
        self.assertTrue(response.is_correct)
        self.assertEqual(response.points_earned, self.question.points)

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Alert,
  Grid,
  IconButton,
  Tooltip,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Functions,
  Clear,
  CheckCircle,
  Error,
  Help,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { MathValidationRule } from '../../types';

// Note: In a real implementation, you would import MathQuill properly
// For now, we'll create a simplified version
declare global {
  interface Window {
    MQ?: any;
  }
}

interface MathInputProps {
  mathExpression?: string;
  validation?: MathValidationRule;
  onExpressionChange: (expression: string) => void;
  isSubmitting: boolean;
  value?: string;
}

interface ValidationResult {
  isValid: boolean;
  message: string;
  formattedExpression?: string;
}

const MathInput: React.FC<MathInputProps> = ({
  mathExpression,
  validation,
  onExpressionChange,
  isSubmitting,
  value,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [expression, setExpression] = useState(value || '');
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [showHelp, setShowHelp] = useState(false);
  const mathFieldRef = useRef<HTMLDivElement>(null);
  const mathQuillRef = useRef<any>(null);

  useEffect(() => {
    // Initialize MathQuill when component mounts
    initializeMathQuill();
    
    return () => {
      // Cleanup MathQuill instance
      if (mathQuillRef.current) {
        mathQuillRef.current.destroy();
      }
    };
  }, []);

  useEffect(() => {
    if (value && value !== expression) {
      setExpression(value);
      if (mathQuillRef.current) {
        mathQuillRef.current.latex(value);
      }
    }
  }, [value]);

  const initializeMathQuill = useCallback(() => {
    // Mock MathQuill initialization - in real implementation, load from CDN or npm
    if (typeof window !== 'undefined' && mathFieldRef.current) {
      try {
        // This would be the actual MathQuill initialization
        // const MQ = window.MQ = window.MathQuill.getInterface(2);
        // mathQuillRef.current = MQ.MathField(mathFieldRef.current, {
        //   spaceBehavesLikeTab: true,
        //   leftRightIntoCmdGoes: 'up',
        //   restrictMismatchedBrackets: true,
        //   sumStartsWithNEquals: true,
        //   supSubsRequireOperand: true,
        //   charsThatBreakOutOfSupSub: '+-=<>',
        //   autoSubscriptNumerals: true,
        //   autoCommands: 'pi theta sqrt sum prod alpha beta gamma delta',
        //   autoOperatorNames: 'sin cos tan ln log',
        //   handlers: {
        //     edit: () => {
        //       const latex = mathQuillRef.current.latex();
        //       setExpression(latex);
        //       onExpressionChange(latex);
        //       validateExpression(latex);
        //     }
        //   }
        // });
        
        // For demo purposes, we'll use a simple text input
        console.log('MathQuill would be initialized here');
      } catch (error) {
        console.warn('MathQuill not available, falling back to text input');
      }
    }
  }, [onExpressionChange]);

  const validateExpression = useCallback(
    (expr: string) => {
      if (!validation || !expr.trim()) {
        setValidationResult(null);
        return;
      }

      try {
        switch (validation.type) {
          case 'latex':
            // Basic LaTeX validation
            const hasValidBrackets = checkBrackets(expr);
            if (!hasValidBrackets) {
              setValidationResult({
                isValid: false,
                message: t('assessment.mathInput.validation.invalidBrackets'),
              });
              return;
            }
            break;
            
          case 'numeric':
            // Check if expression evaluates to a number
            const numericValue = evaluateNumericExpression(expr);
            if (isNaN(numericValue)) {
              setValidationResult({
                isValid: false,
                message: t('assessment.mathInput.validation.notNumeric'),
              });
              return;
            }
            break;
            
          case 'algebraic':
            // Check for valid algebraic expression
            if (!isValidAlgebraicExpression(expr)) {
              setValidationResult({
                isValid: false,
                message: t('assessment.mathInput.validation.invalidAlgebraic'),
              });
              return;
            }
            break;
        }

        setValidationResult({
          isValid: true,
          message: t('assessment.mathInput.validation.valid'),
          formattedExpression: expr,
        });
      } catch (error) {
        setValidationResult({
          isValid: false,
          message: t('assessment.mathInput.validation.error'),
        });
      }
    },
    [validation, t]
  );

  const checkBrackets = (expr: string): boolean => {
    const brackets = { '(': ')', '[': ']', '{': '}' };
    const stack: string[] = [];
    
    for (const char of expr) {
      if (Object.keys(brackets).includes(char)) {
        stack.push(char);
      } else if (Object.values(brackets).includes(char)) {
        const last = stack.pop();
        if (!last || brackets[last as keyof typeof brackets] !== char) {
          return false;
        }
      }
    }
    
    return stack.length === 0;
  };

  const evaluateNumericExpression = (expr: string): number => {
    // Simple numeric evaluation - in real implementation, use a math parser
    try {
      // Remove LaTeX commands and convert to JS-evaluable expression
      const cleaned = expr
        .replace(/\\frac{([^}]*)}{([^}]*)}/g, '($1)/($2)')
        .replace(/\\sqrt{([^}]*)}/g, 'Math.sqrt($1)')
        .replace(/\\pi/g, 'Math.PI')
        .replace(/\\e/g, 'Math.E')
        .replace(/[^0-9+\-*/().\s]/g, '');
      
      return eval(cleaned);
    } catch {
      return NaN;
    }
  };

  const isValidAlgebraicExpression = (expr: string): boolean => {
    // Basic algebraic validation
    const algebraicPattern = /^[a-zA-Z0-9+\-*/^(){}[\]\\._\s=<>]+$/;
    return algebraicPattern.test(expr);
  };

  const clearExpression = useCallback(() => {
    setExpression('');
    onExpressionChange('');
    setValidationResult(null);
    if (mathQuillRef.current) {
      mathQuillRef.current.latex('');
    }
  }, [onExpressionChange]);

  const insertSymbol = useCallback(
    (symbol: string) => {
      const newExpression = expression + symbol;
      setExpression(newExpression);
      onExpressionChange(newExpression);
      if (mathQuillRef.current) {
        mathQuillRef.current.write(symbol);
      }
    },
    [expression, onExpressionChange]
  );

  const handleTextChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value;
      setExpression(newValue);
      onExpressionChange(newValue);
      validateExpression(newValue);
    },
    [onExpressionChange, validateExpression]
  );

  const mathSymbols = [
    { symbol: '\\frac{a}{b}', display: '½', tooltip: 'Fraction' },
    { symbol: '\\sqrt{x}', display: '√', tooltip: 'Square root' },
    { symbol: 'x^{2}', display: 'x²', tooltip: 'Exponent' },
    { symbol: '\\pi', display: 'π', tooltip: 'Pi' },
    { symbol: '\\theta', display: 'θ', tooltip: 'Theta' },
    { symbol: '\\alpha', display: 'α', tooltip: 'Alpha' },
    { symbol: '\\beta', display: 'β', tooltip: 'Beta' },
    { symbol: '\\sum', display: 'Σ', tooltip: 'Sum' },
    { symbol: '\\int', display: '∫', tooltip: 'Integral' },
    { symbol: '\\lim', display: 'lim', tooltip: 'Limit' },
    { symbol: '\\sin', display: 'sin', tooltip: 'Sine' },
    { symbol: '\\cos', display: 'cos', tooltip: 'Cosine' },
    { symbol: '\\tan', display: 'tan', tooltip: 'Tangent' },
    { symbol: '\\log', display: 'log', tooltip: 'Logarithm' },
    { symbol: '\\ln', display: 'ln', tooltip: 'Natural log' },
    { symbol: '\\pm', display: '±', tooltip: 'Plus minus' },
  ];

  return (
    <Box sx={{ mt: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="body2" color="text.secondary">
          {t('assessment.mathInput.instruction')}
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title={t('assessment.mathInput.help')}>
            <IconButton
              size="small"
              onClick={() => setShowHelp(!showHelp)}
              color={showHelp ? 'primary' : 'default'}
            >
              <Help />
            </IconButton>
          </Tooltip>
          
          <Button
            startIcon={<Clear />}
            onClick={clearExpression}
            disabled={isSubmitting}
            size="small"
          >
            {t('assessment.mathInput.clear')}
          </Button>
        </Box>
      </Box>

      {showHelp && (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            {t('assessment.mathInput.helpText')}
          </Typography>
        </Alert>
      )}

      <Grid container spacing={2}>
        {/* Math Input Field */}
        <Grid item xs={12} md={8}>
          <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
            {/* MathQuill container - fallback to text input for demo */}
            <Box
              ref={mathFieldRef}
              sx={{
                minHeight: 60,
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                p: 1,
                backgroundColor: isSubmitting ? theme.palette.action.disabledBackground : 'background.paper',
                display: 'flex',
                alignItems: 'center',
                fontSize: '1.2rem',
              }}
            >
              {/* Fallback text input for demo */}
              <input
                type="text"
                value={expression}
                onChange={handleTextChange}
                disabled={isSubmitting}
                placeholder={t('assessment.mathInput.placeholder')}
                style={{
                  width: '100%',
                  border: 'none',
                  outline: 'none',
                  backgroundColor: 'transparent',
                  fontSize: '1.1rem',
                  fontFamily: 'serif',
                }}
              />
            </Box>
            
            {expression && (
              <Box sx={{ mt: 1, p: 1, backgroundColor: theme.palette.grey[50], borderRadius: 1 }}>
                <Typography variant="caption" color="text.secondary">
                  {t('assessment.mathInput.preview')}:
                </Typography>
                <Typography variant="body1" sx={{ fontFamily: 'serif', fontSize: '1.1rem' }}>
                  {expression}
                </Typography>
              </Box>
            )}
          </Paper>

          {/* Validation Result */}
          {validationResult && (
            <Alert
              severity={validationResult.isValid ? 'success' : 'error'}
              icon={validationResult.isValid ? <CheckCircle /> : <Error />}
              sx={{ mb: 2 }}
            >
              {validationResult.message}
            </Alert>
          )}
        </Grid>

        {/* Math Symbol Palette */}
        <Grid item xs={12} md={4}>
          <Typography variant="subtitle2" gutterBottom>
            {t('assessment.mathInput.symbols')}
          </Typography>
          
          <Paper elevation={1} sx={{ p: 1 }}>
            <Grid container spacing={0.5}>
              {mathSymbols.map((item, index) => (
                <Grid item key={index}>
                  <Tooltip title={item.tooltip}>
                    <IconButton
                      size="small"
                      onClick={() => insertSymbol(item.symbol)}
                      disabled={isSubmitting}
                      sx={{
                        minWidth: 40,
                        minHeight: 40,
                        fontSize: '1.1rem',
                        fontFamily: 'serif',
                      }}
                    >
                      {item.display}
                    </IconButton>
                  </Tooltip>
                </Grid>
              ))}
            </Grid>
          </Paper>
          
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            {t('assessment.mathInput.symbolsHelp')}
          </Typography>
        </Grid>
      </Grid>

      {/* LaTeX Output for Reference */}
      {expression && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="caption" color="text.secondary">
            LaTeX: 
          </Typography>
          <Paper variant="outlined" sx={{ p: 1, fontFamily: 'monospace', fontSize: '0.875rem' }}>
            {expression}
          </Paper>
        </Box>
      )}
    </Box>
  );
};

export default MathInput;

"""
API views for managing content type preferences.
"""
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

User = get_user_model()


class CourseContentTypePreferenceViewSet(viewsets.ViewSet):
    """API endpoint for managing course content type preferences."""
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """List course content type preferences"""
        return Response({
            "status": "success",
            "data": [],
            "message": "Course content type preferences not yet implemented"
        })

    @action(detail=False, methods=["get"])
    def by_course(self, request):
        """Get content type preferences for a specific course"""
        course_id = request.query_params.get("course_id")
        if not course_id:
            return Response({
                "error": "course_id parameter is required"
            }, status=status.HTTP_400_BAD_REQUEST)

        # Return default preferences
        default_preferences = {
            "id": None,
            "course": course_id,
            "show_standard_content": True,
            "show_interactive_content": True,
            "show_ai_content": True,
            "default_content_type": "STANDARD",
            "created_by": request.user.id,
            "created_at": None,
            "updated_at": None
        }
        return Response(default_preferences)


class StudentContentTypePreferenceViewSet(viewsets.ViewSet):
    """API endpoint for managing student content type preferences."""
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """List student content type preferences"""
        return Response({
            "status": "success",
            "data": [],
            "message": "Student content type preferences not yet implemented"
        })

    @action(detail=False, methods=["get"])
    def by_student_course(self, request):
        """Get content type preferences for a specific student and course"""
        student_id = request.query_params.get("student_id")
        course_id = request.query_params.get("course_id")

        if not student_id or not course_id:
            return Response({
                "error": "student_id and course_id parameters are required"
            }, status=status.HTTP_400_BAD_REQUEST)

        # Return default preferences
        default_preferences = {
            "id": None,
            "student": student_id,
            "course": course_id,
            "show_standard_content": True,
            "show_interactive_content": True,
            "show_ai_content": True,
            "default_content_type": "STANDARD",
            "created_by": request.user.id,
            "created_at": None,
            "updated_at": None,
            "override_reason": "",
            "has_override": False
        }
        return Response(default_preferences)

    @action(detail=False, methods=["get"])
    def by_course(self, request):
        """Get all student preferences for a specific course"""
        course_id = request.query_params.get("course_id")
        if not course_id:
            return Response({
                "error": "course_id parameter is required"
            }, status=status.HTTP_400_BAD_REQUEST)

        # Return empty list for now
        return Response([])
"""Tests for the AssessmentQuestion model."""
from django.contrib.auth import get_user_model
from django.test import TestCase
from assessment.models import AssessmentQuestion

User = get_user_model()


class AssessmentQuestionTestCase(TestCase):
    """Test case for the AssessmentQuestion model"""
    
    def setUp(self):
        """Set up test data"""
        self.admin_user = User.objects.create_superuser(
            username="admin",
            email="<EMAIL>",
            password="password123"
        )
        
        # Create a multiple choice question
        self.multiple_choice_question = AssessmentQuestion.objects.create(
            question_text="What is the capital of France?",
            question_type="MULTIPLE_CHOICE",
            options=["Paris", "London", "Berlin", "Madrid"],
            correct_answer={"answer": "Paris", "explanation": "Paris is the capital of France"},
            category="general",
            difficulty_level=1,
            points=10,
            created_by=self.admin_user
        )
        
        # Create a true/false question
        self.true_false_question = AssessmentQuestion.objects.create(
            question_text="Python is a programming language.",
            question_type="TRUE_FALSE",
            options=["True", "False"],
            correct_answer={"answer": "True", "explanation": "Python is indeed a programming language"},
            category="computer_science",
            difficulty_level=1,
            points=5,
            created_by=self.admin_user
        )
    
    def test_question_text_property(self):
        """Test that the text property works correctly"""
        # Test getting text via property
        self.assertEqual(self.multiple_choice_question.text, "What is the capital of France?")
        
        # Test setting text via property
        self.multiple_choice_question.text = "What is the capital of Spain?"
        self.assertEqual(self.multiple_choice_question.question_text, "What is the capital of Spain?")
        
        # Save and reload from database
        self.multiple_choice_question.save()
        refreshed_question = AssessmentQuestion.objects.get(id=self.multiple_choice_question.id)
        self.assertEqual(refreshed_question.question_text, "What is the capital of Spain?")
        self.assertEqual(refreshed_question.text, "What is the capital of Spain?")
    
    def test_check_answer(self):
        """Test the check_answer method"""
        # Test correct answer for multiple choice
        self.assertTrue(self.multiple_choice_question.check_answer("Paris"))
        self.assertFalse(self.multiple_choice_question.check_answer("London"))
        
        # Test correct answer for true/false
        self.assertTrue(self.true_false_question.check_answer("True"))
        self.assertFalse(self.true_false_question.check_answer("False"))
        
        # Test case insensitivity
        self.assertTrue(self.multiple_choice_question.check_answer("paris"))
        self.assertTrue(self.true_false_question.check_answer("true"))
    
    def test_str_method(self):
        """Test the __str__ method"""
        self.assertEqual(str(self.multiple_choice_question), "What is the capital of France?...")
        
        # Test with a long question text
        long_text = "This is a very long question text that should be truncated in the string representation of the question model instance."
        self.multiple_choice_question.question_text = long_text
        self.assertEqual(str(self.multiple_choice_question), long_text[:50] + "...")

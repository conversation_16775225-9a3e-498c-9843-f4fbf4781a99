from django.apps import AppConfig


class BlockchainCredentialsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'blockchain_credentials'
    verbose_name = 'Blockchain Credentials'
    
    def ready(self):
        """Initialize blockchain credentials signals and services"""
        try:
            import blockchain_credentials.signals
        except ImportError:
            pass

"""
URL Configuration for Improved AI Services
"""

from django.urls import path
from . import ai_views

app_name = 'ai_improved'

urlpatterns = [
    # Content generation
    path('generate/', ai_views.generate_content, name='generate_content'),
    path('generate-async/', ai_views.generate_content_async, name='generate_content_async'),

    # Async request management
    path('async-status/<str:request_id>/', ai_views.async_status, name='async_status'),
    path('async-result/<str:request_id>/', ai_views.async_result, name='async_result'),

    # Assessment
    path('analyze-answer/', ai_views.analyze_answer, name='analyze_answer'),

    # Monitoring and health
    path('health/', ai_views.health_status, name='health_status'),
    path('analytics/', ai_views.usage_analytics, name='usage_analytics'),
    path('metrics/', ai_views.service_metrics, name='service_metrics'),

    # Configuration (admin only)
    path('config/', ai_views.update_configuration, name='update_configuration'),
    path('api-key/', ai_views.update_api_key, name='update_api_key'),
]
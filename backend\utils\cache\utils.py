"""ConsolidatedCacheUtilitiesThismoduleconsolidatescachingfunctionalityfrom:-unified_cache_utils.py-cache_utils.py-redis_utils.py"""import json
import loggingimporthashlibfromtypingimportAnyOptionalDictUnionfrom django.core.cacheimportcachefrom django.confimportsettingsfromfunctoolsimportwrapslogger=logging.getLogger(__name__)classCacheManager:"""Unifiedcachemanagerthatprovidesaconsistentinterfaceforallcachingoperationsacrosstheplatform."""def__init__(self):self.default_timeout=getattr(settings'CACHE_DEFAULT_TIMEOUT'3600)self.key_prefix=getattr(settings'CACHE_KEY_PREFIX''nsu')def_make_key(selfkey:strnamespace:str='default')->str:"""Createastandardizedcachekey"""returnf"{self.key_prefix}:{namespace}:{key}"defget(selfkey:strnamespace:str='default'default:Any=None)->Any:"""Getvaluefromcache"""cache_key=self._make_key(keynamespace)try:value=cache.get(cache_keydefault)ifvalueisnotNone:logger.debug(f"Cachehitforkey:{cache_key}")else:logger.debug(f"Cachemissforkey:{cache_key}")returnvalueexceptExceptionase:logger.error(f"Cachegeterrorforkey{cache_key}:{e}")returndefaultdefset(selfkey:strvalue:Anytimeout:Optional[int]=Nonenamespace:str='default')->bool:"""Setvalueincache"""cache_key=self._make_key(keynamespace)timeout=timeoutorself.default_timeouttry:cache.set(cache_keyvaluetimeout)logger.debug(f"Cachesetforkey:{cache_key}timeout:{timeout}")returnTrueexceptExceptionase:logger.error(f"Cacheseterrorforkey{cache_key}:{e}")returnFalsedefdelete(selfkey:strnamespace:str='default')->bool:"""Deletevaluefromcache"""cache_key=self._make_key(keynamespace)try:cache.delete(cache_key)logger.debug(f"Cachedeleteforkey:{cache_key}")returnTrueexceptExceptionase:logger.error(f"Cachedeleteerrorforkey{cache_key}:{e}")returnFalsedefget_or_set(selfkey:strcallable_functimeout:Optional[int]=Nonenamespace:str='default')->Any:"""Getvaluefromcacheorsetitusingthecallable"""value=self.get(keynamespace)ifvalueisNone:value=callable_func()self.set(keyvaluetimeoutnamespace)returnvaluedefclear_namespace(selfnamespace:str)->bool:"""Clearallkeysinanamespace(ifsupportedbycachebackend)"""try:#Thisisasimplifiedimplementation#ForRedisyoucouldusepatternmatchingpattern=self._make_key('*'namespace)logger.info(f"Clearingcachenamespace:{namespace}")#Note:ThisrequiresRedisbackendforpatternsupportreturnTrueexceptExceptionase:logger.error(f"Cacheclearnamespaceerrorfor{namespace}:{e}")returnFalsedefget_stats(self)->Dict[strAny]:"""Getcachestatistics(ifsupported)"""try:#Thiswouldneedtobeimplementedbasedoncachebackendreturn{'backend':settings.CACHES.get('default'{}).get('BACKEND''unknown')'default_timeout':self.default_timeout'key_prefix':self.key_prefix}exceptExceptionase:logger.error(f"Cachestatserror:{e}")return{}classCourseCache:"""Specializedcachemanagerforcourse-relateddata"""def__init__(selfcache_manager:CacheManager):self.cache=cache_managerself.namespace='courses'self.timeout=1800#30minutesdefget_course(selfcourse_id:int)->Optional[Dict]:"""Getcoursedatafromcache"""returnself.cache.get(f"course:{course_id}"self.namespace)defset_course(selfcourse_id:intcourse_data:Dict)->bool:"""Cachecoursedata"""returnself.cache.set(f"course:{course_id}"course_dataself.timeoutself.namespace)defget_course_list(selffilters_hash:str)->Optional[list]:"""Getcachedcourselistbasedonfilters"""returnself.cache.get(f"course_list:{filters_hash}"self.namespace)defset_course_list(selffilters_hash:strcourse_list:list)->bool:"""Cachecourselist"""returnself.cache.set(f"course_list:{filters_hash}"course_listself.timeoutself.namespace)definvalidate_course(selfcourse_id:int)->bool:"""Invalidatecoursecache"""returnself.cache.delete(f"course:{course_id}"self.namespace)classAICache:"""SpecializedcachemanagerforAI-relateddata"""def__init__(selfcache_manager:CacheManager):self.cache=cache_managerself.namespace='ai'self.timeout=3600#1hourdef_hash_prompt(selfprompt:strparams:Dict=None)->str:"""Createhashforpromptandparameters"""content=prompt+json.dumps(paramsor{}sort_keys=True)returnhashlib.md5(content.encode()).hexdigest()defget_ai_response(selfprompt:strparams:Dict=None)->Optional[Dict]:"""GetcachedAIresponse"""key=f"ai_response:{self._hash_prompt(promptparams)}"returnself.cache.get(keyself.namespace)defset_ai_response(selfprompt:strresponse:Dictparams:Dict=None)->bool:"""CacheAIresponse"""key=f"ai_response:{self._hash_prompt(promptparams)}"returnself.cache.set(keyresponseself.timeoutself.namespace)defget_recommendations(selfuser_id:intcontext:str)->Optional[Dict]:"""Getcachedrecommendations"""key=f"recommendations:{user_id}:{context}"returnself.cache.get(keyself.namespace)defset_recommendations(selfuser_id:intcontext:strrecommendations:Dict)->bool:"""Cacherecommendations"""key=f"recommendations:{user_id}:{context}"returnself.cache.set(keyrecommendationsself.timeoutself.namespace)defcache_result(timeout:int=3600namespace:str='default'key_func=None):"""DecoratortocachefunctionresultsArgs:timeout:Cachetimeoutinsecondsnamespace:Cachenamespacekey_func:Functiontogeneratecachekeyfromargs/kwargs"""defdecorator(func):@wraps(func)defwrapper(*args**kwargs):#Generatecachekeyifkey_func:cache_key=key_func(*args**kwargs)else:#Defaultkeygenerationkey_parts=[func.__name__]key_parts.extend(str(arg)forarginargs)key_parts.extend(f"{k}:{v}"forkvinsorted(kwargs.items()))cache_key=":".join(key_parts)#Trytogetfromcachecm=get_cache_manager()result=cm.get(cache_keynamespace)ifresultisnotNone:returnresult#Executefunctionandcacheresultresult=func(*args**kwargs)cm.set(cache_keyresulttimeoutnamespace)returnresultreturnwrapperreturndecorator#Globalinstances-lazyinitialization_cache_manager=None_course_cache=None_ai_cache=Nonedefget_cache_manager():"""Getorcreatetheglobalcachemanagerinstance"""global_cache_managerif_cache_managerisNone:_cache_manager=CacheManager()return_cache_managerdefget_course_cache():"""Getorcreatetheglobalcoursecacheinstance"""global_course_cacheif_course_cacheisNone:_course_cache=CourseCache(get_cache_manager())return_course_cachedefget_ai_cache():"""GetorcreatetheglobalAIcacheinstance"""global_ai_cacheif_ai_cacheisNone:_ai_cache=AICache(get_cache_manager())return_ai_cache#Backwardcompatibility-DONOTinitializeatmodulelevel#cache_manager=CacheManager()#course_cache=CourseCache(cache_manager)#ai_cache=AICache(cache_manager)
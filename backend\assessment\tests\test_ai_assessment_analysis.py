"""Tests for the AI assessment analysis module."""
from unittest.mock import MagicMock, patch
from django.test import TestCase
from assessment.ai_assessment_analysis import AIAssessmentAnalysis


class AIAssessmentAnalysisTest(TestCase):
    """Test the AI assessment analysis module"""
    
    def setUp(self):
        """Set up test data"""
        # Initialize the AI assessment analysis service
        self.ai_assessment_analysis = AIAssessmentAnalysis()
    
    @patch("assessment.ai_assessment_analysis.ai_service")
    @patch("assessment.ai_assessment_analysis.Assessment")
    @patch("assessment.ai_assessment_analysis.StudentLevel")
    @patch("assessment.ai_assessment_analysis.AIAssessmentController")
    def test_analyze_student_assessment(self, mock_ai_controller, mock_student_level, mock_assessment, mock_ai_service):
        """Test analyzing a student assessment"""
        # Mock the Assessment model
        assessment_instance = MagicMock()
        assessment_instance.id = 1
        assessment_instance.student.id = 1
        assessment_instance.student.username = "testuser"
        assessment_instance.assessment_type = "PLACEMENT"
        assessment_instance.score = 75.0
        assessment_instance.initial_level = 2
        assessment_instance.final_level = None
        assessment_instance.completed = True
        mock_assessment.objects.get.return_value = assessment_instance
        
        # Mock the StudentLevel model
        student_level_instance = MagicMock()
        student_level_instance.student = assessment_instance.student
        student_level_instance.current_level = 2
        student_level_instance.current_level_display = "Elementary"
        student_level_instance.skill_strengths = {}
        student_level_instance.skill_weaknesses = {}
        mock_student_level.objects.get.return_value = student_level_instance
        
        # Mock the AI service response
        mock_ai_service.generate_structured_text.return_value = """{
            "overall_analysis": "Good performance",
            "strengths": ["Math", "Science"],
            "weaknesses": ["History"],
            "recommended_level": 3,
            "level_justification": "Student shows good understanding",
            "course_recommendations": ["Math101", "Science101"],
            "learning_path": ["Step1", "Step2"],
            "confidence_score": 0.8
        }"""
        
        # Mock the AssessmentResponse.objects.filter().values()
        assessment_instance.responses.filter.return_value.values.return_value = [
            {
                "question__text": "Question1",
                "answer_text": "Answer1",
                "is_correct": True
            },
            {
                "question__text": "Question2",
                "answer_text": "Answer2",
                "is_correct": False
            }
        ]
        
        # Call the method
        result = self.ai_assessment_analysis.analyze_student_assessment(assessment_instance.id)
        
        # Check the result
        self.assertEqual(result["overall_analysis"], "Good performance")
        self.assertEqual(result["recommended_level"], 3)
        self.assertEqual(len(result["strengths"]), 2)
        self.assertEqual(len(result["weaknesses"]), 1)
        
        # Check that the AI decision was created
        mock_ai_controller.objects.create.assert_called_once()
    
    @patch("assessment.ai_assessment_analysis.ai_service")
    @patch("assessment.ai_assessment_analysis.get_user_model")
    @patch("assessment.ai_assessment_analysis.StudentLevel")
    def test_generate_skill_gap_analysis(self, mock_student_level, mock_user_model, mock_ai_service):
        """Test generating a skill gap analysis"""
        # Mock the User model
        user_instance = MagicMock()
        user_instance.id = 1
        user_instance.username = "testuser"
        mock_user_model.return_value.objects.get.return_value = user_instance
        
        # Mock the StudentLevel model
        student_level_instance = MagicMock()
        student_level_instance.student = user_instance
        student_level_instance.current_level = 2
        student_level_instance.current_level_display = "Elementary"
        student_level_instance.skill_strengths = {}
        student_level_instance.skill_weaknesses = {}
        mock_student_level.objects.get.return_value = student_level_instance
        
        # Mock the AI service response
        mock_ai_service.generate_structured_text.return_value = """{
            "strengths": {"Math": 80, "Science": 85},
            "weaknesses": {"History": 40, "Geography": 35},
            "focus_areas": ["History", "Geography"],
            "learning_resources": [
                {
                    "type": "Book",
                    "description": "History101",
                    "benefit": "Improve history knowledge"
                }
            ],
            "estimated_time_to_next_level": "2 months",
            "personalized_advice": "Focus on history and geography"
        }"""
        
        # Call the method
        result = self.ai_assessment_analysis.generate_skill_gap_analysis(user_instance.id)
        
        # Check the result
        self.assertEqual(result["strengths"]["Math"], 80)
        self.assertEqual(result["weaknesses"]["History"], 40)
        self.assertEqual(len(result["focus_areas"]), 2)
        
        # Check that the student level was updated
        student_level_instance.save.assert_called_once()
    
    @patch("assessment.ai_assessment_analysis.ai_service")
    @patch("assessment.ai_assessment_analysis.get_user_model")
    @patch("assessment.ai_assessment_analysis.StudentLevel")
    @patch("assessment.ai_assessment_analysis.Course")
    def test_generate_course_recommendations(self, mock_course, mock_student_level, mock_user_model, mock_ai_service):
        """Test generating course recommendations"""
        # Mock the User model
        user_instance = MagicMock()
        user_instance.id = 1
        user_instance.username = "testuser"
        mock_user_model.return_value.objects.get.return_value = user_instance
        
        # Mock the StudentLevel model
        student_level_instance = MagicMock()
        student_level_instance.student = user_instance
        student_level_instance.current_level = 2
        student_level_instance.current_level_display = "Elementary"
        student_level_instance.recommended_courses = MagicMock()
        mock_student_level.objects.get.return_value = student_level_instance
        
        # Mock the Course model
        course1 = MagicMock()
        course1.id = 1
        course1.title = "Math101"
        course2 = MagicMock()
        course2.id = 2
        course2.title = "Science101"
        mock_course.objects.get.side_effect = [course1, course2]
        
        # Mock the AI service response
        mock_ai_service.generate_structured_text.return_value = '{
            "recommended_courses": [
                {
                    "course_id": 1,
                    "title": "Math101",
                    "justification": "Good fit for your level"
                },
                {
                    "course_id": 2,
                    "title": "Science101",
                    "justification": "Builds on your strengths"
                }
            ],
            "learning_path": [
                {
                    "step": 1,
                    "course_id": 1,
                    "title": "Math101",
                    "goal": "Learn basic math"
                },
                {
                    "step": 2,
                    "course_id": 2,
                    "title": "Science101",
                    "goal": "Learn basic science"
                }
            ],
            "expected_outcomes": ["Improved math skills", "Better science understanding"],
            "personalized_message": "These courses will help you advance"
        }'
        
        # Call the method
        result = self.ai_assessment_analysis.generate_course_recommendations(user_instance.id)
        
        # Check the result
        self.assertEqual(len(result["recommended_courses"]), 2)
        self.assertEqual(result["recommended_courses"][0]["course_id"], 1)
        self.assertEqual(result["recommended_courses"][1]["course_id"], 2)
        
        # Check that the student level was updated with recommended courses
        student_level_instance.recommended_courses.clear.assert_called_once()
        self.assertEqual(student_level_instance.recommended_courses.add.call_count, 2)

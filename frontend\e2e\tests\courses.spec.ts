import { test, expect } from '@playwright/test';

test.describe('Course Management Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login as student
    await page.goto('/login');
    const username = process.env.TEST_STUDENT_USERNAME || 'testuser';
    const password = process.env.TEST_STUDENT_PASSWORD || 'testpass123';
    
    await page.fill('input[name="username"]', username);
    await page.fill('input[name="password"]', password);
    await page.click('button[type="submit"]');
    
    await expect(page).toHaveURL(/\/dashboard/);
  });

  test('should display course catalog correctly', async ({ page }) => {
    await page.goto('/courses');
    
    await expect(page.locator('h1')).toContainText('Course Catalog');
    await expect(page.locator('[data-testid="course-card"]')).toHaveCount.greaterThan(0);
    
    // Check course card elements
    const firstCourse = page.locator('[data-testid="course-card"]').first();
    await expect(firstCourse.locator('[data-testid="course-title"]')).toBeVisible();
    await expect(firstCourse.locator('[data-testid="course-description"]')).toBeVisible();
    await expect(firstCourse.locator('[data-testid="course-instructor"]')).toBeVisible();
    await expect(firstCourse.locator('[data-testid="course-credits"]')).toBeVisible();
  });

  test('should filter courses by category', async ({ page }) => {
    await page.goto('/courses');
    
    // Get initial course count
    const initialCount = await page.locator('[data-testid="course-card"]').count();
    
    // Apply filter
    await page.selectOption('[data-testid="category-filter"]', 'Computer Science');
    
    // Wait for filter to apply
    await page.waitForTimeout(1000);
    
    // Check filtered results
    const filteredCount = await page.locator('[data-testid="course-card"]').count();
    expect(filteredCount).toBeLessThanOrEqual(initialCount);
    
    // Verify all visible courses are from selected category
    const courseCards = page.locator('[data-testid="course-card"]');
    const count = await courseCards.count();
    
    for (let i = 0; i < count; i++) {
      const category = await courseCards.nth(i).locator('[data-testid="course-category"]').textContent();
      expect(category).toContain('Computer Science');
    }
  });

  test('should search courses by title', async ({ page }) => {
    await page.goto('/courses');
    
    // Search for specific course
    await page.fill('[data-testid="course-search"]', 'Programming');
    await page.keyboard.press('Enter');
    
    // Wait for search results
    await page.waitForTimeout(1000);
    
    // Verify search results
    const courseCards = page.locator('[data-testid="course-card"]');
    const count = await courseCards.count();
    
    for (let i = 0; i < count; i++) {
      const title = await courseCards.nth(i).locator('[data-testid="course-title"]').textContent();
      expect(title?.toLowerCase()).toContain('programming');
    }
  });

  test('should view course details', async ({ page }) => {
    await page.goto('/courses');
    
    // Click on first course
    await page.locator('[data-testid="course-card"]').first().click();
    
    // Should navigate to course detail page
    await expect(page).toHaveURL(/\/courses\/\d+/);
    
    // Check course detail elements
    await expect(page.locator('[data-testid="course-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="course-description"]')).toBeVisible();
    await expect(page.locator('[data-testid="course-instructor"]')).toBeVisible();
    await expect(page.locator('[data-testid="course-syllabus"]')).toBeVisible();
    await expect(page.locator('[data-testid="enroll-button"]')).toBeVisible();
  });

  test('should enroll in a course', async ({ page }) => {
    await page.goto('/courses');
    
    // Find a course that's not enrolled
    const courseCard = page.locator('[data-testid="course-card"]').first();
    await courseCard.click();
    
    // Check if already enrolled
    const enrollButton = page.locator('[data-testid="enroll-button"]');
    const buttonText = await enrollButton.textContent();
    
    if (buttonText?.includes('Enroll')) {
      // Enroll in course
      await enrollButton.click();
      
      // Wait for enrollment confirmation
      await expect(page.locator('text=Successfully enrolled')).toBeVisible();
      
      // Button should change to "Continue" or "View Course"
      await expect(enrollButton).toContainText(/Continue|View Course/);
    }
  });

  test('should view enrolled courses', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Navigate to my courses
    await page.click('[data-testid="my-courses-link"]');
    
    await expect(page).toHaveURL(/\/my-courses/);
    await expect(page.locator('h1')).toContainText('My Courses');
    
    // Check enrolled courses
    const enrolledCourses = page.locator('[data-testid="enrolled-course"]');
    await expect(enrolledCourses).toHaveCount.greaterThan(0);
    
    // Check course progress elements
    const firstCourse = enrolledCourses.first();
    await expect(firstCourse.locator('[data-testid="course-progress"]')).toBeVisible();
    await expect(firstCourse.locator('[data-testid="continue-button"]')).toBeVisible();
  });

  test('should access course materials', async ({ page }) => {
    await page.goto('/my-courses');
    
    // Click on first enrolled course
    await page.locator('[data-testid="enrolled-course"]').first().click();
    
    // Should navigate to course content
    await expect(page).toHaveURL(/\/courses\/\d+\/content/);
    
    // Check course materials
    await expect(page.locator('[data-testid="course-materials"]')).toBeVisible();
    await expect(page.locator('[data-testid="material-item"]')).toHaveCount.greaterThan(0);
    
    // Click on first material
    await page.locator('[data-testid="material-item"]').first().click();
    
    // Should open material viewer
    await expect(page.locator('[data-testid="material-viewer"]')).toBeVisible();
  });

  test('should track course progress', async ({ page }) => {
    await page.goto('/my-courses');
    
    const firstCourse = page.locator('[data-testid="enrolled-course"]').first();
    
    // Check progress bar
    const progressBar = firstCourse.locator('[data-testid="progress-bar"]');
    await expect(progressBar).toBeVisible();
    
    // Check progress percentage
    const progressText = firstCourse.locator('[data-testid="progress-text"]');
    await expect(progressText).toBeVisible();
    
    const progressValue = await progressText.textContent();
    expect(progressValue).toMatch(/\d+%/);
  });

  test('should handle course ratings and reviews', async ({ page }) => {
    await page.goto('/courses');
    
    // Go to course detail
    await page.locator('[data-testid="course-card"]').first().click();
    
    // Scroll to reviews section
    await page.locator('[data-testid="reviews-section"]').scrollIntoViewIfNeeded();
    
    // Check existing reviews
    await expect(page.locator('[data-testid="course-rating"]')).toBeVisible();
    await expect(page.locator('[data-testid="review-item"]')).toHaveCount.greaterThan(0);
    
    // Add a review (if enrolled)
    const reviewButton = page.locator('[data-testid="add-review-button"]');
    if (await reviewButton.isVisible()) {
      await reviewButton.click();
      
      // Fill review form
      await page.selectOption('[data-testid="rating-select"]', '5');
      await page.fill('[data-testid="review-text"]', 'Great course! Highly recommended.');
      await page.click('[data-testid="submit-review"]');
      
      // Check success message
      await expect(page.locator('text=Review submitted successfully')).toBeVisible();
    }
  });

  test('should handle course prerequisites', async ({ page }) => {
    await page.goto('/courses');
    
    // Find course with prerequisites
    const courseWithPrereqs = page.locator('[data-testid="course-card"]').filter({
      has: page.locator('[data-testid="prerequisites-indicator"]')
    }).first();
    
    if (await courseWithPrereqs.isVisible()) {
      await courseWithPrereqs.click();
      
      // Check prerequisites section
      await expect(page.locator('[data-testid="prerequisites-section"]')).toBeVisible();
      await expect(page.locator('[data-testid="prerequisite-item"]')).toHaveCount.greaterThan(0);
      
      // Check if enrollment is blocked
      const enrollButton = page.locator('[data-testid="enroll-button"]');
      const isDisabled = await enrollButton.isDisabled();
      
      if (isDisabled) {
        await expect(page.locator('text=Prerequisites not met')).toBeVisible();
      }
    }
  });

  test('should sort courses by different criteria', async ({ page }) => {
    await page.goto('/courses');
    
    // Sort by rating
    await page.selectOption('[data-testid="sort-select"]', 'rating');
    await page.waitForTimeout(1000);
    
    // Verify sorting (check first few courses have high ratings)
    const firstCourseRating = await page.locator('[data-testid="course-card"]').first()
      .locator('[data-testid="course-rating"]').textContent();
    
    // Sort by newest
    await page.selectOption('[data-testid="sort-select"]', 'newest');
    await page.waitForTimeout(1000);
    
    // Verify URL contains sort parameter
    expect(page.url()).toContain('sort=newest');
  });

  test('should handle course wishlist', async ({ page }) => {
    await page.goto('/courses');
    
    // Add course to wishlist
    const firstCourse = page.locator('[data-testid="course-card"]').first();
    const wishlistButton = firstCourse.locator('[data-testid="wishlist-button"]');
    
    await wishlistButton.click();
    
    // Check success message
    await expect(page.locator('text=Added to wishlist')).toBeVisible();
    
    // Button should change state
    await expect(wishlistButton).toHaveClass(/wishlisted/);
    
    // Navigate to wishlist
    await page.goto('/wishlist');
    
    // Check course appears in wishlist
    await expect(page.locator('[data-testid="wishlist-item"]')).toHaveCount.greaterThan(0);
  });

  test('should work on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/courses');
    
    // Check mobile layout
    await expect(page.locator('[data-testid="mobile-course-grid"]')).toBeVisible();
    
    // Test mobile navigation
    await page.click('[data-testid="mobile-menu-button"]');
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
    
    // Test course card on mobile
    const courseCard = page.locator('[data-testid="course-card"]').first();
    await expect(courseCard).toBeVisible();
    
    await courseCard.click();
    await expect(page).toHaveURL(/\/courses\/\d+/);
  });

  test('should be accessible', async ({ page }) => {
    await page.goto('/courses');
    
    // Check for proper ARIA labels
    await expect(page.locator('[aria-label="Course search"]')).toBeVisible();
    await expect(page.locator('[aria-label="Filter courses"]')).toBeVisible();
    
    // Check keyboard navigation
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid="course-search"]')).toBeFocused();
    
    // Check heading hierarchy
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('h2')).toHaveCount.greaterThan(0);
    
    // Check alt text for images
    const courseImages = page.locator('[data-testid="course-image"]');
    const count = await courseImages.count();
    
    for (let i = 0; i < count; i++) {
      await expect(courseImages.nth(i)).toHaveAttribute('alt');
    }
  });
});

#!/usr/bin/env python
"""Test Django startup without migration checks"""
import os
import sys
import django
from django.conf import settings
from django.core.wsgi import get_wsgi_application

def main():
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings.development")
    django.setup()
    print("Django setup completed successfully!")
    print(f"Django version: {django.get_version()}")
    print(f"Database engine: {settings.DATABASES['default']['ENGINE']}")
    print("Available apps:")
    for app in settings.INSTALLED_APPS:
        print(f"  - {app}")

if __name__ == "__main__":
    main()

import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Box, Tabs, Tab } from '@mui/material';
import GlobalErrorBoundary from '../../../../components/errors/GlobalErrorBoundary';

// Decomposed components
import StudentLevelTable from './components/StudentLevelTable';
import StudentLevelFilters from './components/StudentLevelFilters';
import StudentLevelStats from './components/StudentLevelStats';
import LevelHistoryDialog from './components/LevelHistoryDialog';
import EditLevelDialog from './components/EditLevelDialog';
import AssessmentResponsesDialog from './components/AssessmentResponsesDialog';

// Hooks and services
import { useStudentLevel } from './hooks/useStudentLevel';
import { useStudentLevelActions } from './hooks/useStudentLevelActions';

// Types
import { Student, TabValue } from './types';

const StudentLevelManagementContainer: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Tab state
  const [tabValue, setTabValue] = useState<TabValue>(0);
  
  // Custom hooks for data and actions
  const {
    students,
    filteredStudents,
    loading,
    error,
    levelDistribution,
    fetchStudents,
    fetchLevelHistory,
  } = useStudentLevel();

  const {
    editDialogOpen,
    historyDialogOpen,
    responsesDialogOpen,
    selectedStudent,
    openEditDialog,
    closeEditDialog,
    openHistoryDialog,
    closeHistoryDialog,
    openResponsesDialog,
    closeResponsesDialog,
    handleLevelUpdate,
  } = useStudentLevelActions();

  // Initialize data and handle URL parameters
  useEffect(() => {
    fetchStudents();
    
    // Handle studentId in URL
    const searchParams = new URLSearchParams(location.search);
    const studentIdParam = searchParams.get('studentId');
    if (studentIdParam) {
      const studentId = parseInt(studentIdParam);
      if (!isNaN(studentId)) {
        fetchLevelHistory(studentId);
      }
    }
  }, [location, fetchStudents, fetchLevelHistory]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: TabValue) => {
    setTabValue(newValue);
  };

  const renderTabContent = () => {
    switch (tabValue) {
      case 0:
        return (
          <GlobalErrorBoundary level="section">
            <StudentLevelTable
              students={filteredStudents}
              loading={loading}
              onEditLevel={openEditDialog}
              onViewHistory={openHistoryDialog}
              onViewResponses={openResponsesDialog}
            />
          </GlobalErrorBoundary>
        );
      case 1:
        return (
          <GlobalErrorBoundary level="section">
            <StudentLevelStats 
              distribution={levelDistribution}
              totalStudents={students.length}
            />
          </GlobalErrorBoundary>
        );
      default:
        return null;
    }
  };

  if (error) {
    return (
      <GlobalErrorBoundary level="page">
        <Box p={3}>Error loading student level management</Box>
      </GlobalErrorBoundary>
    );
  }

  return (
    <GlobalErrorBoundary level="page">
      <Box sx={{ width: '100%' }}>
        {/* Filters Section */}
        <GlobalErrorBoundary level="section">
          <StudentLevelFilters />
        </GlobalErrorBoundary>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Student Levels" />
            <Tab label="Statistics" />
          </Tabs>
        </Box>

        {/* Tab Content */}
        {renderTabContent()}

        {/* Dialogs */}
        <EditLevelDialog
          open={editDialogOpen}
          student={selectedStudent}
          onClose={closeEditDialog}
          onSave={handleLevelUpdate}
        />

        <LevelHistoryDialog
          open={historyDialogOpen}
          student={selectedStudent}
          onClose={closeHistoryDialog}
        />

        <AssessmentResponsesDialog
          open={responsesDialogOpen}
          student={selectedStudent}
          onClose={closeResponsesDialog}
        />
      </Box>
    </GlobalErrorBoundary>
  );
};

export default StudentLevelManagementContainer;

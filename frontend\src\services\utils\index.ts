/**
 * Utils Index - Barrel Export
 * 
 * Centralized exports for all utility functions, classes, and interfaces
 * used by AI services and their tests.
 */

// Export everything from BaseAIService
export { BaseAIService } from './BaseAIService';
export type { BaseAIServiceOptions } from './BaseAIService';

// Export everything from aiServiceUtils
export {
  AIServiceError,
  AIServiceConnectionError,
  AIServiceTimeoutError,
  AIServiceValidationError,
  makeAIRequest,
  handleAIServiceError,
  parseAIResponse,
  createStandardizedResponse,
  createFallbackResponse,
  clearAIServiceCache,
  getCacheStats,
} from './aiServiceUtils';

export type {
  StandardizedAIResponse,
  AIServiceConfig,
} from './aiServiceUtils';

// Export everything from errorHandling
export {
  ERROR_MESSAGES,
  ErrorSeverity,
  ErrorCategory,
  getErrorInfo,
  logError,
  createUserErrorMessage,
  shouldUseFallback,
  getRetryDelay,
} from './errorHandling';

export type { ErrorInfo } from './errorHandling';

// Export everything from serviceRegistry
export {
  serviceRegistry,
} from './serviceRegistry';

export type {
  ServiceInfo,
  ServiceRegistryConfig,
} from './serviceRegistry';

// Helper function for creating service instances (used in tests)
export const createServiceInstance = (options: {
  serviceName: string;
  baseEndpoint: string;
  config?: any;
}) => {
  class TestAIService extends BaseAIService {
    constructor(serviceOptions: any) {
      super(serviceOptions);
    }

    // Expose protected methods for testing
    public async testGet<T = any>(endpoint: string, params?: any, config?: any): Promise<T> {
      return this.get<T>(endpoint, params, config);
    }

    public async testPost<T = any>(endpoint: string, data?: any, config?: any): Promise<T> {
      return this.post<T>(endpoint, data, config);
    }
  }

  return new TestAIService(options);
};

// Validation function for service configuration (used in tests)
export const validateServiceConfig = (config: any): boolean => {
  if (!config) return false;
  
  const requiredFields = ['timeout', 'retries', 'fallbackEnabled'];
  return requiredFields.every(field => config.hasOwnProperty(field));
};

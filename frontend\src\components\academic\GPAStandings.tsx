/**
 * 📊 GPA & Academic Standing Component
 * 
 * Features:
 * - Update student GPAs
 * - Track academic standing history
 * - Identify at-risk students
 * - Academic standing management
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  CircularProgress,
  Chip,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Warning,
  Star,
  Refresh,
  Person,
  Grade,
  School,
  Timeline,
  Assessment
} from '@mui/icons-material';
import { academicService, AcademicStanding, GpaUpdate, AtRiskStudent } from '../../services/academicService';

interface GPAStandingsProps {
  studentId?: number;
  isAdmin?: boolean;
}

const GPAStandings: React.FC<GPAStandingsProps> = ({ studentId, isAdmin = false }) => {
  const [standings, setStandings] = useState<AcademicStanding[]>([]);
  const [atRiskStudents, setAtRiskStudents] = useState<AtRiskStudent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);
  const [openGpaDialog, setOpenGpaDialog] = useState(false);
  const [gpaStudentId, setGpaStudentId] = useState<number>(studentId || 0);
  const [gpaTermId, setGpaTermId] = useState<number>(0);

  useEffect(() => {
    loadData();
  }, [studentId]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [standingsData, atRiskData] = await Promise.all([
        academicService.getAcademicStanding(studentId),
        isAdmin ? academicService.getAtRiskStudents() : Promise.resolve([])
      ]);
      
      setStandings(standingsData);
      setAtRiskStudents(atRiskData);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to load academic data');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateGPA = async () => {
    if (!gpaStudentId) {
      setError('Please enter a student ID');
      return;
    }

    try {
      setUpdating(true);
      await academicService.updateGpa(gpaStudentId, gpaTermId || undefined);
      setOpenGpaDialog(false);
      loadData();
      setGpaStudentId(0);
      setGpaTermId(0);
    } catch (err: any) {
      setError(err.message || 'Failed to update GPA');
    } finally {
      setUpdating(false);
    }
  };

  const handleUpdateAcademicStanding = async (studentId: number, termId: number) => {
    try {
      setUpdating(true);
      await academicService.updateAcademicStanding(studentId, termId);
      loadData();
    } catch (err: any) {
      setError(err.message || 'Failed to update academic standing');
    } finally {
      setUpdating(false);
    }
  };

  const getStandingColor = (standing: string) => {
    return academicService.getStandingColor(standing);
  };

  const getStandingIcon = (standing: string) => {
    switch (standing) {
      case 'GOOD':
        return <Star color="success" />;
      case 'PROBATION':
        return <Warning color="warning" />;
      case 'SUSPENSION':
      case 'DISMISSAL':
        return <Warning color="error" />;
      default:
        return <Grade />;
    }
  };

  const getGPAColor = (gpa: number) => {
    if (gpa >= 3.5) return 'success';
    if (gpa >= 3.0) return 'info';
    if (gpa >= 2.5) return 'warning';
    return 'error';
  };

  const getGPATrend = (currentGpa: number, previousGpa?: number) => {
    if (!previousGpa) return null;
    
    if (currentGpa > previousGpa) {
      return <TrendingUp color="success" />;
    } else if (currentGpa < previousGpa) {
      return <TrendingDown color="error" />;
    }
    return null;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Assessment color="primary" />
          GPA & Academic Standing
        </Typography>
        {isAdmin && (
          <Box display="flex" gap={2}>
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={() => setOpenGpaDialog(true)}
            >
              Update GPA
            </Button>
            <Button
              variant="contained"
              startIcon={<Timeline />}
              onClick={loadData}
              disabled={loading}
            >
              Refresh Data
            </Button>
          </Box>
        )}
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Current Standing Summary */}
      {standings.length > 0 && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="primary" gutterBottom>
                  Current Academic Standing
                </Typography>
                <Box display="flex" alignItems="center" gap={2}>
                  {getStandingIcon(standings[0].standing)}
                  <Box>
                    <Typography variant="h5">
                      {standings[0].standing.replace('_', ' ')}
                    </Typography>
                    <Typography color="text.secondary">
                      Current GPA: {academicService.formatGPA(standings[0].gpa_at_time)}
                    </Typography>
                  </Box>
                  <Chip
                    label={standings[0].standing}
                    color={getStandingColor(standings[0].standing)}
                    sx={{ ml: 'auto' }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="primary" gutterBottom>
                  Academic Progress
                </Typography>
                <Box>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2">GPA Progress</Typography>
                    <Typography variant="h6" color={getGPAColor(standings[0].gpa_at_time)}>
                      {academicService.formatGPA(standings[0].gpa_at_time)}/4.0
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={(standings[0].gpa_at_time / 4.0) * 100}
                    color={getGPAColor(standings[0].gpa_at_time)}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Credits Earned: {standings[0].credits_at_time}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Academic Standing History */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Timeline />
            Academic Standing History
          </Typography>
          
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Term</TableCell>
                  <TableCell align="center">Standing</TableCell>
                  <TableCell align="center">GPA</TableCell>
                  <TableCell align="center">Credits</TableCell>
                  <TableCell align="center">Trend</TableCell>
                  <TableCell>Effective Date</TableCell>
                  {isAdmin && <TableCell>Actions</TableCell>}
                </TableRow>
              </TableHead>
              <TableBody>
                {standings.map((standing, index) => (
                  <TableRow key={standing.id}>
                    <TableCell>{standing.term.name}</TableCell>
                    <TableCell align="center">
                      <Chip
                        icon={getStandingIcon(standing.standing)}
                        label={standing.standing}
                        color={getStandingColor(standing.standing)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Chip
                        label={academicService.formatGPA(standing.gpa_at_time)}
                        color={getGPAColor(standing.gpa_at_time)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="center">{standing.credits_at_time}</TableCell>
                    <TableCell align="center">
                      {getGPATrend(
                        standing.gpa_at_time,
                        standings[index + 1]?.gpa_at_time
                      )}
                    </TableCell>
                    <TableCell>
                      {new Date(standing.effective_date).toLocaleDateString()}
                    </TableCell>
                    {isAdmin && (
                      <TableCell>
                        <Button
                          size="small"
                          onClick={() => handleUpdateAcademicStanding(standing.student, standing.term.id)}
                          disabled={updating}
                        >
                          Update
                        </Button>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* At-Risk Students (Admin only) */}
      {isAdmin && atRiskStudents.length > 0 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Warning color="warning" />
              At-Risk Students
            </Typography>
            
            <List>
              {atRiskStudents.map((student, index) => (
                <React.Fragment key={index}>
                  <ListItem>
                    <ListItemIcon>
                      <Person />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={2}>
                          <Typography>
                            {student.student.first_name} {student.student.last_name}
                          </Typography>
                          <Chip
                            label={`GPA: ${academicService.formatGPA(student.current_gpa)}`}
                            color="error"
                            size="small"
                          />
                        </Box>
                      }
                      secondary={
                        <Box sx={{ mt: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            Risk Factors:
                          </Typography>
                          <Box display="flex" flexWrap="wrap" gap={1} sx={{ mt: 0.5 }}>
                            {student.risk_factors.map((factor, i) => (
                              <Chip
                                key={i}
                                label={factor}
                                color="warning"
                                size="small"
                                variant="outlined"
                              />
                            ))}
                          </Box>
                          {student.recommended_actions.length > 0 && (
                            <Box sx={{ mt: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Recommended Actions:
                              </Typography>
                              <List dense>
                                {student.recommended_actions.map((action, i) => (
                                  <ListItem key={i} sx={{ py: 0, pl: 2 }}>
                                    <Typography variant="body2">• {action}</Typography>
                                  </ListItem>
                                ))}
                              </List>
                            </Box>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < atRiskStudents.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      )}

      {/* Update GPA Dialog */}
      <Dialog open={openGpaDialog} onClose={() => setOpenGpaDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Update Student GPA</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Student ID"
                type="number"
                value={gpaStudentId || ''}
                onChange={(e) => setGpaStudentId(parseInt(e.target.value) || 0)}
                placeholder="Enter student ID"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Term ID (optional)"
                type="number"
                value={gpaTermId || ''}
                onChange={(e) => setGpaTermId(parseInt(e.target.value) || 0)}
                placeholder="Leave empty for cumulative GPA update"
                helperText="Leave empty to update cumulative GPA, or specify a term ID for term-specific update"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenGpaDialog(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            onClick={handleUpdateGPA}
            disabled={updating}
          >
            {updating ? <CircularProgress size={20} /> : 'Update GPA'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GPAStandings;

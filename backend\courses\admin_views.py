"""Adminviewsforthecoursesapp.Providesadmin-specificfunctionalityandAPIendpoints."""import loggingfromdatetimeimport timedeltafromtypingimportAnyDictfrom django.appsimportappsfrom django.contrib.authimportget_user_modelfrom django.db.modelsimportCountQfrom django.httpimportJsonResponsefrom django.shortcutsimportget_object_or_404from django.utilsimport timezonefromrest_frameworkimportpermissionsstatusviewsetsfromrest_framework.decoratorsimportactionfromrest_framework.permissionsimportIsAdminUserfromrest_framework.responseimportResponsefromrest_framework.viewsimportAPIView#ImportunifiedAIserviceandconfigurationutilitiesfrom utils.ai.servicesimportget_ai_servicefrom utils.api_key_serviceimportApiKeyServicefrom utils.monitoringimportmonitoring_service#Getmodelsdynamicallytoavoidcircularimportstry:Course=apps.get_model("courses""Course")exceptLookupError:Course=Nonetry:Department=apps.get_model("courses""Department")exceptLookupError:Department=Nonetry:Enrollment=apps.get_model("courses""Enrollment")exceptLookupError:Enrollment=None#Importexistingviewsandserializersfrom.views.course_viewsimportCourseViewSetCourseDetailSerializer#Importmodelsweneedfortheadminendpointstry:from.models.materialimportMaterialexceptImportError:Material=Nonetry:from grades.modelsimportAssignmentexceptImportError:Assignment=Nonetry:from.models.enrollmentimportEnrollmentexceptImportError:Enrollment=NoneUser=get_user_model()logger=logging.getLogger(__name__)classAdminAIConfigurationView(APIView):"""AdminviewforAIconfigurationmanagement.HandlesdynamicAPIkeyupdatesandconfigurationfromthefrontend."""permission_classes=[IsAdminUser]defget(selfrequest):"""GetcurrentAIconfiguration"""try:#Usethecentralizedconfigurationmanagerfrom utils.ai_config_managerimportget_ai_configunified_config=get_ai_config()#Formatresponsetomatchexpectedstructureconfig={'api_key_set':unified_config.get('api_key_configured'False)'current_model':unified_config.get('default_model''gemini-2.0-flash')'temperature':unified_config.get('temperature'0.7)'max_tokens':unified_config.get('max_tokens'2048)'status':'active'ifunified_config.get('api_key_configured'False)else'inactive''monitoring_enabled':unified_config.get('enable_monitoring'True)'configured_from':'centralized_manager''version':unified_config.get('version'1)'last_updated':unified_config.get('updated_at''Never')}returnResponse({'success':True'config':config'message':'AIconfigurationretrievedsuccessfully'})exceptExceptionase:logger.error(f"ErrorgettingAIconfiguration:{e}")returnResponse({'success':False'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)defpost(selfrequest):"""UpdateAIconfigurationfromfrontend"""try:config_data=request.data.get('config'{})ifnotconfig_data:returnResponse({'success':False'error':'Noconfigurationdataprovided'}status=status.HTTP_400_BAD_REQUEST)#Usethecentralizedconfigurationmanagerfrom utils.ai_config_managerimportupdate_ai_configsuccess=update_ai_config(config_datasource="admin_frontend")ifsuccess:logger.info("AIconfigurationupdatedsuccessfullyfromadminfrontend")returnResponse({'success':True'message':'AIconfigurationupdatedsuccessfully''config':config_data})else:logger.error("FailedtoupdateAIconfigurationfromadminfrontend")returnResponse({'success':False'error':'Failedtoupdateconfiguration'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)exceptExceptionase:logger.error(f"ErrorupdatingAIconfiguration:{e}")returnResponse({'success':False'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAdminCourseViewSet(CourseViewSet):"""Admin-specificcourseviewsetwithextendedpermissionsandfeatures"""permission_classes=[IsAdminUser]defretrieve(selfrequest*args**kwargs):"""Getcoursedetailswithcomprehensiveenrollmentinformation"""try:instance=self.get_object()serializer=self.get_serializer(instance)data=serializer.data#Addcomprehensiveenrollmentinformationenrollment_data=self._get_enrollment_data(instance)data.update(enrollment_data)returnResponse({'success':True'course':data'message':'Coursedetailsretrievedsuccessfully'})exceptExceptionase:logger.error(f"Errorgettingcoursedetails:{e}")returnResponse({'success':False'error':str(e)'message':'Failedtoretrievecoursedetails'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)def_get_enrollment_data(selfcourse):"""Getcomprehensiveenrollmentdataforacourse"""enrollment_data={'enrolled_students':[]'enrollment_summary':{'total_enrolled':0'active_enrollments':0'pending_enrollments':0'completed_enrollments':0'dropped_enrollments':0'capacity':getattr(course'capacity'0)'available_spots':0'enrollment_rate':0.0}'student_demographics':{'age_distribution':{}'average_age':0'gender_distribution':{}}}ifnotEnrollment:returnenrollment_data#Getallenrollmentsforthiscourseall_enrollments=Enrollment.objects.filter(course=course).select_related('user').order_by('-enrollment_date')#Categorizeenrollmentsbystatusactive_statuses=['PENDING''APPROVED''active''enrolled''ongoing']pending_statuses=['PENDING''pending']completed_statuses=['COMPLETED''completed''finished''graduated']dropped_statuses=['DROPPED''dropped''withdrawn']active_enrollments=[]pending_enrollments=[]completed_enrollments=[]dropped_enrollments=[]ages=[]forenrollmentinall_enrollments:user=enrollment.user#Calculateageifdate_of_birthisavailableage=Noneifhasattr(user'date_of_birth')anduser.date_of_birth:fromdatetimeimportdatetoday=date.today()age=today.year-user.date_of_birth.year-((today.monthtoday.day)<(user.date_of_birth.monthuser.date_of_birth.day))ages.append(age)#Getstudentlevelinfoifavailablestudent_level=Nonetry:from assessment.modelsimportStudentLevellevel_obj=StudentLevel.objects.filter(student=user).first()iflevel_obj:student_level={'current_level':level_obj.current_level'level_display':level_obj.get_current_level_display()'last_updated':level_obj.level_updated_at}except:pass#Getattendanceinfoifavailableattendance_info=Nonetry:#Trytogetattendancerecords(thiswouldneedtobeimplementedbasedonyourattendancemodel)attendance_info={'attendance_rate':0#Placeholder'total_classes':0'present_count':0}except:passstudent_data={'id':user.id'first_name':getattr(user'first_name''')'last_name':getattr(user'last_name''')'full_name':f"{getattr(user'first_name''')}{getattr(user'last_name''')}".strip()'username':user.username'email':getattr(user'email''')'date_of_birth':getattr(user'date_of_birth'None)'age':age'phone_number':getattr(user'phone_number''')'student_id':getattr(user'student_id''')'department':getattr(user'department''')'role':getattr(user'role''STUDENT')#Enrollmentspecificdata'enrollment_id':enrollment.id'enrollment_date':enrollment.enrollment_date'enrollment_status':enrollment.status'enrollment_type':getattr(enrollment'enrollment_type''REGULAR')'completion_date':getattr(enrollment'completion_date'None)#Additionalstudentinfo'student_level':student_level'attendance_info':attendance_info'last_activity':getattr(user'last_activity'None)'is_active':getattr(user'is_active'True)}#Categorizebyenrollmentstatusifenrollment.statusinactive_statuses:active_enrollments.append(student_data)elifenrollment.statusinpending_statuses:pending_enrollments.append(student_data)elifenrollment.statusincompleted_statuses:completed_enrollments.append(student_data)elifenrollment.statusindropped_statuses:dropped_enrollments.append(student_data)#Calculatesummarystatisticstotal_enrolled=len(all_enrollments)active_count=len(active_enrollments)capacity=getattr(course'capacity'0)available_spots=max(0capacity-active_count)ifcapacity>0else0enrollment_rate=(active_count/capacity*100)ifcapacity>0else0#Calculatedemographicsaverage_age=sum(ages)/len(ages)ifageselse0age_ranges={'18-20':len([aforainagesif18<=a<=20])'21-25':len([aforainagesif21<=a<=25])'26-30':len([aforainagesif26<=a<=30])'31-35':len([aforainagesif31<=a<=35])'36+':len([aforainagesifa>35])}#Updateenrollmentdataenrollment_data.update({'enrolled_students':active_enrollments#Forbackwardcompatibility'students':active_enrollments#Primarystudentlist'pending_students':pending_enrollments'completed_students':completed_enrollments'dropped_students':dropped_enrollments'all_enrollments':active_enrollments+pending_enrollments+completed_enrollments+dropped_enrollments'enrollment_summary':{'total_enrolled':total_enrolled'active_enrollments':active_count'pending_enrollments':len(pending_enrollments)'completed_enrollments':len(completed_enrollments)'dropped_enrollments':len(dropped_enrollments)'capacity':capacity'available_spots':available_spots'enrollment_rate':round(enrollment_rate2)'is_full':available_spots==0andcapacity>0'has_waitlist':False#Wouldneedtocheckwaitlistmodel}'student_demographics':{'total_students_with_age':len(ages)'average_age':round(average_age1)ifaverage_age>0elseNone'age_distribution':age_ranges'youngest_student':min(ages)ifageselseNone'oldest_student':max(ages)ifageselseNone}})returnenrollment_data@action(detail=Truemethods=['get'])defdetailed_stats(selfrequestpk=None):"""Getdetailedstatisticsforacourse(adminonly)"""try:course=self.get_object()#Getenrollmentstatsenrollment_stats={}ifEnrollment:#Usestatusfieldinsteadofis_active(whichdoesn'texist)active_statuses=['active''enrolled''ongoing']enrollment_stats={'total_enrollments':Enrollment.objects.filter(course=course).count()'active_enrollments':Enrollment.objects.filter(course=coursestatus__in=active_statuses).count()'completed_enrollments':Enrollment.objects.filter(course=coursecompletion_date__isnull=False).count()}stats={'course_id':course.id'course_title':course.title'enrollment_stats':enrollment_stats'created_at':course.created_at'updated_at':course.updated_at}returnResponse({'success':True'stats':stats})exceptExceptionase:logger.error(f"Errorgettingcoursestats:{e}")returnResponse({'success':False'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@action(detail=Truemethods=['get''post'])defmaterials(selfrequestpk=None):"""Getallmaterialsforacourseoruploadnewmaterial(adminonly)"""ifrequest.method=='GET':try:course=self.get_object()materials_data=[]ifMaterial:materials=Material.objects.filter(course=course)materials_data=[{'id':material.id'title':material.title'description':getattr(material'description''')'content_type':getattr(material'content_type''MATERIAL')'file_url':material.file.urlifhasattr(material'file')andmaterial.fileelseNone'publish_date':getattr(material'publish_date'None)'created_at':getattr(material'created_at'None)'order':getattr(material'order'0)}formaterialinmaterials]returnResponse({'status':'success''data':materials_data'count':len(materials_data)})exceptExceptionase:logger.error(f"Errorgettingcoursematerials:{e}")returnResponse({'status':'error''error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)elifrequest.method=='POST':try:course=self.get_object()#Getdatafromrequesttitle=request.data.get('title')description=request.data.get('description''')content_type=request.data.get('type''READING')file=request.FILES.get('file')#Validaterequiredfieldsifnottitle:returnResponse({'status':'error''error':'Titleisrequired'}status=status.HTTP_400_BAD_REQUEST)#CreatenewmaterialifMaterial:material=Material.objects.create(course=coursetitle=titledescription=descriptioncontent_type=content_typefile=filecreated_by=request.userprovider='STANDARD')material_data={'id':material.id'title':material.title'description':material.description'content_type':material.content_type'file_url':material.file.urlifmaterial.fileelseNone'publish_date':getattr(material'publish_date'None)'created_at':material.created_at'order':material.order}returnResponse({'status':'success''message':'Materialuploadedsuccessfully''data':material_data}status=status.HTTP_201_CREATED)else:returnResponse({'status':'error''error':'Materialmodelnotavailable'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)exceptExceptionase:logger.error(f"Erroruploadingcoursematerial:{e}")returnResponse({'status':'error''error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@action(detail=Truemethods=['get'])defassignments(selfrequestpk=None):"""Getallassignmentsforacourse(adminonly)"""try:course=self.get_object()assignments_data=[]ifAssignment:assignments=Assignment.objects.filter(course=course)assignments_data=[{'id':assignment.id'title':assignment.title'description':assignment.description'due_date':assignment.due_date'total_points':getattr(assignment'total_points'100)'points':getattr(assignment'total_points'100)#Aliasforfrontendcompatibility'weight':getattr(assignment'weight'10.0)'is_graded':getattr(assignment'is_graded'False)'submission_count':getattr(assignment'submission_count'0)'created_at':assignment.created_at'file_attachment':assignment.file_attachment.urlifassignment.file_attachmentelseNone}forassignmentinassignments]returnResponse({'success':True'assignments':assignments_data'count':len(assignments_data)})exceptExceptionase:logger.error(f"Errorgettingcourseassignments:{e}")returnResponse({'success':False'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@action(detail=Truemethods=['get'])defenrollments(selfrequestpk=None):"""Getallenrollmentsforacourse(adminonly)"""try:course=self.get_object()enrollments_data=[]ifEnrollment:enrollments=Enrollment.objects.filter(course=course).select_related('user').order_by('-enrollment_date')forenrollmentinenrollments:user=enrollment.userenrollment_data={'id':enrollment.id'user_id':user.id'username':user.username'first_name':getattr(user'first_name''')'last_name':getattr(user'last_name''')'email':getattr(user'email''')'enrollment_date':enrollment.enrollment_date'status':enrollment.status'enrollment_type':getattr(enrollment'enrollment_type''REGULAR')'completion_date':getattr(enrollment'completion_date'None)}enrollments_data.append(enrollment_data)returnResponse({'success':True'enrollments':enrollments_data'count':len(enrollments_data)'message':f"Found{len(enrollments_data)}enrollments"})exceptExceptionase:logger.error(f"Errorgettingcourseenrollments:{e}")returnResponse({'success':False'error':str(e)'message':'Failedtoretrievecourseenrollments'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@action(detail=Truemethods=['get'])defstudents(selfrequestpk=None):"""Getallenrolledstudentsforacoursefromdatabaseonly(adminonly)"""try:course=self.get_object()students_data=[]ifEnrollment:#Getonlyactiveenrollmentsfromdatabaseactive_statuses=['PENDING''APPROVED''active''enrolled''ongoing']enrollments=Enrollment.objects.filter(course=coursestatus__in=active_statuses).select_related('user').order_by('enrollment_date')forenrollmentinenrollments:user=enrollment.user#Calculateageifdate_of_birthisavailableage=Noneifhasattr(user'date_of_birth')anduser.date_of_birth:fromdatetimeimportdatetoday=date.today()age=today.year-user.date_of_birth.year-((today.monthtoday.day)<(user.date_of_birth.monthuser.date_of_birth.day))student_data={'id':user.id'first_name':getattr(user'first_name''')'last_name':getattr(user'last_name''')'full_name':f"{getattr(user'first_name''')}{getattr(user'last_name''')}".strip()'username':user.username'email':getattr(user'email''')'date_of_birth':getattr(user'date_of_birth'None)'age':age'phone_number':getattr(user'phone_number''')'student_id':getattr(user'student_id''')'department':getattr(user'department''')'enrollment_date':enrollment.enrollment_date'enrollment_status':enrollment.status'enrollment_type':getattr(enrollment'enrollment_type''REGULAR')}students_data.append(student_data)#Returnresponsewithmessageindicatingifnostudentsfoundmessage=f"Found{len(students_data)}enrolledstudents"ifstudents_dataelse"Thiscoursehasnoenrolledstudents"returnResponse({'success':True'students':students_data'count':len(students_data)'message':message})exceptExceptionase:logger.error(f"Errorgettingcoursestudents:{e}")returnResponse({'success':False'error':str(e)'message':'Failedtoretrievecoursestudents'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAdminCourseAssignmentViewSet(viewsets.ModelViewSet):"""Adminviewsetforcourseassignments"""permission_classes=[IsAdminUser]defget_queryset(self):"""Returnallassignmentsforadminusers"""#ThiswouldneedtobeimplementedbasedonyourAssignmentmodel#Fornowreturnemptyquerysetreturn[]deflist(selfrequest):"""Listallcourseassignments"""returnResponse({'success':True'assignments':[]'message':'Assignmentfunctionalitynotyetimplemented'})classAdminDepartmentListView(APIView):"""Adminviewforlistingdepartments"""permission_classes=[IsAdminUser]defget(selfrequest):"""Getlistofalldepartments"""try:departments=[]ifDepartment:departments_qs=Department.objects.all()departments=[{'id':dept.id'name':dept.name'code':getattr(dept'code''')'course_count':getattr(dept'courses'[]).count()ifhasattr(dept'courses')else0}fordeptindepartments_qs]returnResponse({'success':True'departments':departments'count':len(departments)})exceptExceptionase:logger.error(f"Errorgettingdepartments:{e}")returnResponse({'success':False'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAdminDepartmentDetailView(APIView):"""Adminviewfordepartmentdetails"""permission_classes=[IsAdminUser]defget(selfrequestpk):"""Getdepartmentdetails"""try:ifnotDepartment:returnResponse({'success':False'error':'Departmentmodelnotavailable'}status=status.HTTP_404_NOT_FOUND)department=get_object_or_404(Departmentpk=pk)dept_data={'id':department.id'name':department.name'code':getattr(department'code''')'description':getattr(department'description''')'course_count':getattr(department'courses'[]).count()ifhasattr(department'courses')else0}returnResponse({'success':True'department':dept_data})exceptExceptionase:logger.error(f"Errorgettingdepartmentdetails:{e}")returnResponse({'success':False'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAdminDashboardStatsView(APIView):"""Admindashboardstatisticsview"""permission_classes=[IsAdminUser]defget(selfrequest):"""Getdashboardstatisticsforadmin"""try:stats={}#CoursestatisticsifCourse:course_filter=Q()ifhasattr(Course'is_active'):active_courses=Course.objects.filter(is_active=True).count()else:active_courses=Course.objects.count()#Fallbackifnois_activefieldstats['courses']={'total_courses':Course.objects.count()'active_courses':active_courses'recent_courses':Course.objects.filter(created_at__gte=timezone.now()-timedelta(days=30)).count()ifhasattr(Course'created_at')else0}#Userstatisticsstats['users']={'total_users':User.objects.count()'active_users':User.objects.filter(is_active=True).count()'admin_users':User.objects.filter(is_staff=True).count()}#EnrollmentstatisticsifEnrollment:#Usestatusfieldinsteadofis_active(whichdoesn'texist)active_statuses=['active''enrolled''ongoing']#Commonactivestatusvaluescompleted_statuses=['completed''finished''graduated']stats['enrollments']={'total_enrollments':Enrollment.objects.count()'active_enrollments':Enrollment.objects.filter(status__in=active_statuses).count()'completed_enrollments':Enrollment.objects.filter(completion_date__isnull=False).count()}#AIservicestatisticsai_service=get_ai_service()stats['ai_service']={'api_key_configured':bool(ApiKeyService.get_api_key())'current_model':ai_service.config.get('default_model''gemini-2.0-flash')'service_active':True}returnResponse({'success':True'stats':stats'timestamp':timezone.now().isoformat()})exceptExceptionase:logger.error(f"Errorgettingdashboardstats:{e}")returnResponse({'success':False'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)#ForbackwardcompatibilityimportCourseViewSetasAdminCourseViewSetisneeded#inurls.pysowecreateanaliasCourseViewSet=AdminCourseViewSet
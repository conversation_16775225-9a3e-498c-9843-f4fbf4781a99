"""
Academic Management Services

This module provides comprehensive business logic for academic management including:
- Student Transcript System
- Academic Calendar Management
- Prerequisites Enforcement
- GPA Tracking and Academic Standing
- Course Waitlist and Enrollment Queue System
"""

import decimal
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import transaction, models
from django.db.models import Q, F, Count, Avg, Sum
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from core.services.base import (
    BaseService, 
    BaseRepository, 
    CRUDService,
    ServiceError,
    ValidationServiceError,
    NotFoundServiceError,
    PermissionServiceError,
    validate_required_fields,
    sanitize_data
)

from .models import (
    AcademicTerm,
    GradePointValue,
    StudentAcademicRecord,
    TranscriptRecord,
    CoursePrerequisite,
    CourseWaitlist,
    EnrollmentHistory
)

from courses.models import Course, Enrollment
from grades.models import CourseGrade

User = get_user_model()


class AcademicTermRepository(BaseRepository[AcademicTerm]):
    """Repository for AcademicTerm model."""
    
    def __init__(self):
        super().__init__(AcademicTerm)
    
    def get_current_term(self) -> Optional[AcademicTerm]:
        """Get the current active academic term."""
        return self.model.objects.filter(
            status='CURRENT', 
            is_active=True
        ).first()
    
    def get_registration_term(self) -> Optional[AcademicTerm]:
        """Get the term currently open for registration."""
        return self.model.objects.filter(
            status='REGISTRATION_OPEN', 
            is_active=True
        ).first()
    
    def get_by_academic_year(self, academic_year: str) -> models.QuerySet[AcademicTerm]:
        """Get terms by academic year."""
        return self.filter(academic_year=academic_year, is_active=True)
    
    def get_future_terms(self) -> models.QuerySet[AcademicTerm]:
        """Get future terms available for planning."""
        return self.filter(
            status__in=['FUTURE', 'REGISTRATION_OPEN'],
            is_active=True
        ).order_by('start_date')


class StudentAcademicRecordRepository(BaseRepository[StudentAcademicRecord]):
    """Repository for StudentAcademicRecord model."""
    
    def __init__(self):
        super().__init__(StudentAcademicRecord)
    
    def get_by_student(self, student: User) -> Optional[StudentAcademicRecord]:
        """Get academic record by student."""
        try:
            return self.model.objects.get(student=student)
        except self.model.DoesNotExist:
            return None
    
    def get_by_student_id(self, student_id: str) -> Optional[StudentAcademicRecord]:
        """Get academic record by student ID."""
        try:
            return self.model.objects.get(student_id=student_id)
        except self.model.DoesNotExist:
            return None
    
    def get_students_by_standing(self, standing: str) -> models.QuerySet[StudentAcademicRecord]:
        """Get students by academic standing."""
        return self.filter(current_standing=standing)
    
    def get_probation_students(self) -> models.QuerySet[StudentAcademicRecord]:
        """Get students currently on academic probation."""
        return self.filter(current_standing='PROBATION')
    
    def get_honors_students(self) -> models.QuerySet[StudentAcademicRecord]:
        """Get students with honors standing."""
        return self.filter(current_standing__in=['HONORS', 'HIGH_HONORS'])


class TranscriptRepository(BaseRepository[TranscriptRecord]):
    """Repository for TranscriptRecord model."""
    
    def __init__(self):
        super().__init__(TranscriptRecord)
    
    def get_student_transcript(self, student_record: StudentAcademicRecord) -> models.QuerySet[TranscriptRecord]:
        """Get complete transcript for a student."""
        return self.filter(
            student_record=student_record
        ).select_related('term', 'course').order_by('-term__start_date', 'course__course_code')
    
    def get_transcript_by_term(self, student_record: StudentAcademicRecord, term: AcademicTerm) -> models.QuerySet[TranscriptRecord]:
        """Get transcript records for a specific term."""
        return self.filter(student_record=student_record, term=term)
    
    def get_completed_courses(self, student_record: StudentAcademicRecord) -> models.QuerySet[TranscriptRecord]:
        """Get all completed courses (with passing grades)."""
        return self.filter(
            student_record=student_record,
            credits_earned__gt=0
        ).exclude(grade__in=['W', 'I', 'IP'])


class CourseWaitlistRepository(BaseRepository[CourseWaitlist]):
    """Repository for CourseWaitlist model."""
    
    def __init__(self):
        super().__init__(CourseWaitlist)
    
    def get_course_waitlist(self, course: Course, term: AcademicTerm) -> models.QuerySet[CourseWaitlist]:
        """Get waitlist for a specific course and term."""
        return self.filter(
            course=course, 
            term=term, 
            status='WAITING'
        ).order_by('priority', 'added_at')
    
    def get_student_waitlists(self, student: User) -> models.QuerySet[CourseWaitlist]:
        """Get all waitlist entries for a student."""
        return self.filter(student=student, status__in=['WAITING', 'NOTIFIED'])
    
    def get_next_in_line(self, course: Course, term: AcademicTerm) -> Optional[CourseWaitlist]:
        """Get next student in line for a course."""
        return self.filter(
            course=course,
            term=term,
            status='WAITING'
        ).order_by('priority', 'added_at').first()


class AcademicTermService(CRUDService[AcademicTerm]):
    """Service for academic term management."""
    
    def __init__(self):
        super().__init__(AcademicTermRepository())
    
    def get_current_term(self) -> Optional[AcademicTerm]:
        """Get the current active term."""
        return self.repository.get_current_term()
    
    def get_registration_term(self) -> Optional[AcademicTerm]:
        """Get the term open for registration."""
        return self.repository.get_registration_term()
    
    def create_academic_term(
        self, 
        name: str,
        term_type: str,
        academic_year: str,
        start_date: datetime.date,
        end_date: datetime.date,
        registration_start: datetime,
        registration_end: datetime,
        add_drop_deadline: datetime.date,
        withdrawal_deadline: datetime.date,
        final_grades_due: datetime,
        **kwargs
    ) -> AcademicTerm:
        """Create a new academic term with validation."""
        
        # Validate required fields
        validate_required_fields({
            'name': name,
            'term_type': term_type,
            'academic_year': academic_year,
            'start_date': start_date,
            'end_date': end_date,
            'registration_start': registration_start,
            'registration_end': registration_end,
            'add_drop_deadline': add_drop_deadline,
            'withdrawal_deadline': withdrawal_deadline,
            'final_grades_due': final_grades_due,
        }, ['name', 'term_type', 'academic_year', 'start_date', 'end_date', 
            'registration_start', 'registration_end', 'add_drop_deadline', 
            'withdrawal_deadline', 'final_grades_due'])
        
        # Additional validation
        if start_date >= end_date:
            raise ValidationServiceError("End date must be after start date")
        
        if registration_start >= registration_end:
            raise ValidationServiceError("Registration end must be after registration start")
        
        data = {
            'name': name,
            'term_type': term_type,
            'academic_year': academic_year,
            'start_date': start_date,
            'end_date': end_date,
            'registration_start': registration_start,
            'registration_end': registration_end,
            'add_drop_deadline': add_drop_deadline,
            'withdrawal_deadline': withdrawal_deadline,
            'final_grades_due': final_grades_due,
            **kwargs
        }
        
        return self.repository.create(**data)
    
    def update_term_status(self, term: AcademicTerm, new_status: str) -> AcademicTerm:
        """Update term status with validation."""
        valid_statuses = ['FUTURE', 'CURRENT', 'PAST', 'REGISTRATION_OPEN', 'REGISTRATION_CLOSED']
        
        if new_status not in valid_statuses:
            raise ValidationServiceError(f"Invalid status: {new_status}")
        
        return self.repository.update(term, status=new_status)
    
    def get_terms_for_planning(self) -> List[AcademicTerm]:
        """Get terms available for academic planning."""
        return list(self.repository.get_future_terms())


class TranscriptService(BaseService):
    """Service for student transcript management."""
    
    def __init__(self):
        super().__init__()
        self.transcript_repository = TranscriptRepository()
        self.student_repository = StudentAcademicRecordRepository()
        self.term_repository = AcademicTermRepository()
    
    def generate_official_transcript(self, student: User) -> Dict[str, Any]:
        """Generate an official transcript for a student."""
        
        # Get student academic record
        academic_record = self.student_repository.get_by_student(student)
        if not academic_record:
            raise NotFoundServiceError("Student academic record not found")
        
        # Get all transcript records
        transcript_records = self.transcript_repository.get_student_transcript(academic_record)
        
        # Organize by terms
        terms_data = {}
        total_credits_attempted = 0
        total_credits_earned = 0
        
        for record in transcript_records:
            term_key = record.term.id
            if term_key not in terms_data:
                terms_data[term_key] = {
                    'term': record.term,
                    'courses': [],
                    'term_credits_attempted': 0,
                    'term_credits_earned': 0,
                    'term_gpa': decimal.Decimal('0.000')
                }
            
            terms_data[term_key]['courses'].append(record)
            terms_data[term_key]['term_credits_attempted'] += record.credits_attempted
            terms_data[term_key]['term_credits_earned'] += record.credits_earned
            
            total_credits_attempted += record.credits_attempted
            total_credits_earned += record.credits_earned
        
        # Calculate term GPAs
        for term_data in terms_data.values():
            term_data['term_gpa'] = self._calculate_term_gpa(term_data['courses'])
        
        return {
            'student': student,
            'academic_record': academic_record,
            'terms': list(terms_data.values()),
            'summary': {
                'total_credits_attempted': total_credits_attempted,
                'total_credits_earned': total_credits_earned,
                'cumulative_gpa': academic_record.cumulative_gpa,
                'current_standing': academic_record.get_current_standing_display(),
                'enrollment_status': academic_record.get_enrollment_status_display(),
            },
            'generated_at': timezone.now()
        }
    
    def get_term_transcript(self, student: User, term: AcademicTerm) -> Dict[str, Any]:
        """Get transcript for a specific term."""
        
        academic_record = self.student_repository.get_by_student(student)
        if not academic_record:
            raise NotFoundServiceError("Student academic record not found")
        
        records = self.transcript_repository.get_transcript_by_term(academic_record, term)
        
        term_credits_attempted = sum(r.credits_attempted for r in records)
        term_credits_earned = sum(r.credits_earned for r in records)
        term_gpa = self._calculate_term_gpa(records)
        
        return {
            'student': student,
            'term': term,
            'courses': list(records),
            'term_credits_attempted': term_credits_attempted,
            'term_credits_earned': term_credits_earned,
            'term_gpa': term_gpa
        }
    
    def add_transcript_record(
        self, 
        student_record: StudentAcademicRecord,
        term: AcademicTerm,
        course: Course,
        grade: str = '',
        is_repeat: bool = False,
        is_transfer: bool = False,
        is_audit: bool = False,
        instructor: str = '',
        comments: str = ''
    ) -> TranscriptRecord:
        """Add a new transcript record."""
        
        # Check for existing record
        existing = self.transcript_repository.filter(
            student_record=student_record,
            term=term,
            course=course
        ).first()
        
        if existing:
            raise ValidationServiceError("Transcript record already exists for this course and term")
        
        return self.transcript_repository.create(
            student_record=student_record,
            term=term,
            course=course,
            grade=grade,
            credits_attempted=course.credits,
            is_repeat=is_repeat,
            is_transfer=is_transfer,
            is_audit=is_audit,
            instructor=instructor,
            comments=comments
        )
    
    def update_transcript_grade(
        self, 
        transcript_record: TranscriptRecord, 
        grade: str
    ) -> TranscriptRecord:
        """Update grade for a transcript record."""
        
        # Validate grade exists in grade point values
        try:
            GradePointValue.objects.get(grade=grade)
        except GradePointValue.DoesNotExist:
            if grade not in ['W', 'I', 'IP']:  # Allow non-graded statuses
                raise ValidationServiceError(f"Invalid grade: {grade}")
        
        return self.transcript_repository.update(transcript_record, grade=grade)
    
    def _calculate_term_gpa(self, records: List[TranscriptRecord]) -> decimal.Decimal:
        """Calculate GPA for a list of transcript records."""
        total_points = decimal.Decimal('0.0')
        total_credits = 0
        
        for record in records:
            if record.grade_points is not None and not record.is_audit:
                total_points += record.grade_points * record.credits_attempted
                total_credits += record.credits_attempted
        
        if total_credits == 0:
            return decimal.Decimal('0.000')
        
        return round(total_points / total_credits, 3)


class PrerequisiteService(BaseService):
    """Service for course prerequisite management and validation."""
    
    def __init__(self):
        super().__init__()
        self.prerequisite_repository = BaseRepository(CoursePrerequisite)
        self.student_repository = StudentAcademicRecordRepository()
    
    def check_enrollment_eligibility(
        self, 
        student: User, 
        course: Course, 
        term: AcademicTerm = None
    ) -> Dict[str, Any]:
        """
        Check if student is eligible to enroll in a course.
        
        Returns:
            Dict with 'eligible' (bool), 'requirements' (list), and 'messages' (list)
        """
        
        # Get student academic record
        academic_record = self.student_repository.get_by_student(student)
        if not academic_record:
            return {
                'eligible': False,
                'requirements': [],
                'messages': ['Student academic record not found']
            }
        
        # Check if student is eligible for enrollment (not suspended/dismissed)
        if not academic_record.is_eligible_for_enrollment():
            return {
                'eligible': False,
                'requirements': [],
                'messages': [f'Student is not eligible for enrollment due to academic standing: {academic_record.get_current_standing_display()}']
            }
        
        # Get course prerequisites
        prerequisites = self.prerequisite_repository.filter(
            course=course, 
            is_active=True
        )
        
        requirements_check = []
        all_met = True
        messages = []
        
        for prereq in prerequisites:
            check_result = prereq.check_prerequisite(student)
            requirements_check.append({
                'prerequisite': prereq,
                'type': prereq.get_prerequisite_type_display(),
                'met': check_result['met'],
                'details': check_result['details']
            })
            
            if not check_result['met']:
                all_met = False
                messages.append(check_result['details'])
        
        # Check credit limit for the term
        if term and all_met:
            current_credits = self._get_student_term_credits(student, term)
            credit_limit = academic_record.get_credit_limit(term)
            
            if current_credits + course.credits > credit_limit:
                all_met = False
                messages.append(f'Enrollment would exceed credit limit of {credit_limit} credits')
        
        return {
            'eligible': all_met,
            'requirements': requirements_check,
            'messages': messages
        }
    
    def get_completed_prerequisites(self, student: User, course: Course) -> List[CoursePrerequisite]:
        """Get list of prerequisites that student has completed."""
        prerequisites = self.prerequisite_repository.filter(course=course, is_active=True)
        completed = []
        
        for prereq in prerequisites:
            check_result = prereq.check_prerequisite(student)
            if check_result['met']:
                completed.append(prereq)
        
        return completed
    
    def get_missing_prerequisites(self, student: User, course: Course) -> List[CoursePrerequisite]:
        """Get list of prerequisites that student has not met."""
        prerequisites = self.prerequisite_repository.filter(course=course, is_active=True)
        missing = []
        
        for prereq in prerequisites:
            check_result = prereq.check_prerequisite(student)
            if not check_result['met']:
                missing.append(prereq)
        
        return missing
    
    def create_course_prerequisite(
        self,
        course: Course,
        prerequisite_type: str,
        prerequisite_course: Course = None,
        minimum_grade: str = 'D-',
        minimum_gpa: decimal.Decimal = None,
        minimum_credits: int = None,
        required_standing: str = None,
        description: str = '',
        is_corequisite: bool = False
    ) -> CoursePrerequisite:
        """Create a new course prerequisite."""
        
        # Validate prerequisite type
        valid_types = ['COURSE', 'GPA', 'CREDITS', 'STANDING', 'PERMISSION', 'PLACEMENT']
        if prerequisite_type not in valid_types:
            raise ValidationServiceError(f"Invalid prerequisite type: {prerequisite_type}")
        
        # Type-specific validation
        if prerequisite_type == 'COURSE' and not prerequisite_course:
            raise ValidationServiceError("Prerequisite course is required for COURSE type")
        
        if prerequisite_type == 'GPA' and minimum_gpa is None:
            raise ValidationServiceError("Minimum GPA is required for GPA type")
        
        if prerequisite_type == 'CREDITS' and minimum_credits is None:
            raise ValidationServiceError("Minimum credits is required for CREDITS type")
        
        if prerequisite_type == 'STANDING' and not required_standing:
            raise ValidationServiceError("Required standing is required for STANDING type")
        
        return self.prerequisite_repository.create(
            course=course,
            prerequisite_type=prerequisite_type,
            prerequisite_course=prerequisite_course,
            minimum_grade=minimum_grade,
            minimum_gpa=minimum_gpa,
            minimum_credits=minimum_credits,
            required_standing=required_standing,
            description=description,
            is_corequisite=is_corequisite
        )
    
    def _get_student_term_credits(self, student: User, term: AcademicTerm) -> int:
        """Get total credits student is enrolled in for a term."""
        enrollments = Enrollment.objects.filter(
            user=student,
            course__term=term,
            status='APPROVED'
        ).select_related('course')
        
        return sum(enrollment.course.credits for enrollment in enrollments)


class AcademicStandingService(BaseService):
    """Service for GPA tracking and academic standing management."""
    
    def __init__(self):
        super().__init__()
        self.student_repository = StudentAcademicRecordRepository()
        self.transcript_repository = TranscriptRepository()
        self.term_repository = AcademicTermRepository()
    
    @transaction.atomic
    def update_student_gpa(self, student: User, term: AcademicTerm = None) -> StudentAcademicRecord:
        """Update student's GPA and academic standing."""
        
        academic_record = self.student_repository.get_by_student(student)
        if not academic_record:
            raise NotFoundServiceError("Student academic record not found")
        
        # Update GPA calculations
        academic_record.update_gpa(term)
        
        # Log the GPA update
        self.log_action('gpa_update', obj=academic_record, term=term.name if term else 'cumulative')
        
        return academic_record
    
    def bulk_update_gpa_for_term(self, term: AcademicTerm) -> Dict[str, int]:
        """Update GPA for all students who took courses in a term."""
        
        # Get all students who have courses in this term
        student_records = StudentAcademicRecord.objects.filter(
            transcript_records__term=term
        ).distinct()
        
        updated_count = 0
        probation_count = 0
        honors_count = 0
        
        for record in student_records:
            old_standing = record.current_standing
            record.update_gpa(term)
            updated_count += 1
            
            # Track standing changes
            if record.current_standing in ['PROBATION', 'SUSPENSION'] and old_standing not in ['PROBATION', 'SUSPENSION']:
                probation_count += 1
            elif record.current_standing in ['HONORS', 'HIGH_HONORS'] and old_standing not in ['HONORS', 'HIGH_HONORS']:
                honors_count += 1
        
        return {
            'students_updated': updated_count,
            'new_probation': probation_count,
            'new_honors': honors_count
        }
    
    def get_academic_standing_report(self, term: AcademicTerm = None) -> Dict[str, Any]:
        """Generate academic standing report."""
        
        # Get all active students
        if term:
            # Students who took courses in specific term
            students = StudentAcademicRecord.objects.filter(
                transcript_records__term=term
            ).distinct()
        else:
            # All active students
            students = StudentAcademicRecord.objects.filter(
                enrollment_status__in=['FULL_TIME', 'PART_TIME']
            )
        
        # Count by standing
        standing_counts = students.values('current_standing').annotate(
            count=Count('id')
        ).order_by('current_standing')
        
        # Get averages
        avg_gpa = students.aggregate(avg_gpa=Avg('cumulative_gpa'))['avg_gpa'] or 0
        
        # Get probation details
        probation_students = students.filter(current_standing='PROBATION')
        probation_details = []
        
        for student in probation_students:
            probation_details.append({
                'student': student.student,
                'gpa': student.cumulative_gpa,
                'consecutive_terms': student.consecutive_probation_terms,
                'probation_start': student.probation_start_term
            })
        
        return {
            'term': term,
            'total_students': students.count(),
            'standing_distribution': {item['current_standing']: item['count'] for item in standing_counts},
            'average_gpa': round(avg_gpa, 3),
            'probation_students': probation_details,
            'generated_at': timezone.now()
        }
    
    def identify_at_risk_students(self) -> List[Dict[str, Any]]:
        """Identify students at risk of academic probation or suspension."""
        
        current_term = self.term_repository.get_current_term()
        if not current_term:
            return []
        
        at_risk_students = []
        
        # Students with low GPA
        low_gpa_students = self.student_repository.filter(
            cumulative_gpa__lt=decimal.Decimal('2.5'),
            enrollment_status__in=['FULL_TIME', 'PART_TIME']
        )
        
        for record in low_gpa_students:
            # Calculate current term GPA
            current_term_gpa = record.calculate_gpa(current_term)
            
            risk_factors = []
            if record.cumulative_gpa < decimal.Decimal('2.0'):
                risk_factors.append('Low cumulative GPA')
            if current_term_gpa < decimal.Decimal('2.0'):
                risk_factors.append('Low current term GPA')
            if record.consecutive_probation_terms > 0:
                risk_factors.append(f'On probation for {record.consecutive_probation_terms} terms')
            
            if risk_factors:
                at_risk_students.append({
                    'student': record.student,
                    'cumulative_gpa': record.cumulative_gpa,
                    'current_term_gpa': current_term_gpa,
                    'current_standing': record.current_standing,
                    'risk_factors': risk_factors
                })
        
        return sorted(at_risk_students, key=lambda x: x['cumulative_gpa'])


class WaitlistService(BaseService):
    """Service for course waitlist management."""
    
    def __init__(self):
        super().__init__()
        self.waitlist_repository = CourseWaitlistRepository()
        self.student_repository = StudentAcademicRecordRepository()
        self.prerequisite_service = PrerequisiteService()
    
    def add_to_waitlist(
        self, 
        student: User, 
        course: Course, 
        term: AcademicTerm,
        auto_enroll: bool = False,
        notification_method: str = 'EMAIL'
    ) -> CourseWaitlist:
        """Add student to course waitlist."""
        
        # Check if student already on waitlist
        existing = self.waitlist_repository.filter(
            student=student,
            course=course,
            term=term,
            status__in=['WAITING', 'NOTIFIED']
        ).first()
        
        if existing:
            raise ValidationServiceError("Student is already on the waitlist for this course")
        
        # Check if student is already enrolled
        existing_enrollment = Enrollment.objects.filter(
            user=student,
            course=course,
            status='APPROVED'
        ).exists()
        
        if existing_enrollment:
            raise ValidationServiceError("Student is already enrolled in this course")
        
        # Check basic eligibility (prerequisites, standing, etc.)
        eligibility = self.prerequisite_service.check_enrollment_eligibility(student, course, term)
        if not eligibility['eligible']:
            raise ValidationServiceError(f"Student not eligible: {'; '.join(eligibility['messages'])}")
        
        # Determine priority based on student level
        academic_record = self.student_repository.get_by_student(student)
        priority = self._calculate_waitlist_priority(academic_record)
        
        # Create waitlist entry
        waitlist_entry = self.waitlist_repository.create(
            student=student,
            course=course,
            term=term,
            priority=priority,
            auto_enroll=auto_enroll,
            notification_method=notification_method
        )
        
        self.log_action('waitlist_add', student, waitlist_entry)
        return waitlist_entry
    
    def remove_from_waitlist(self, waitlist_entry: CourseWaitlist) -> bool:
        """Remove student from waitlist."""
        
        if waitlist_entry.status not in ['WAITING', 'NOTIFIED']:
            raise ValidationServiceError("Cannot remove student from waitlist with current status")
        
        waitlist_entry.remove_from_waitlist()
        self.log_action('waitlist_remove', waitlist_entry.student, waitlist_entry)
        return True
    
    def process_course_opening(self, course: Course, term: AcademicTerm) -> Optional[CourseWaitlist]:
        """Process a course opening by notifying next student on waitlist."""
        
        next_student = self.waitlist_repository.get_next_in_line(course, term)
        if not next_student:
            return None
        
        # Check if student is still eligible
        eligibility = self.prerequisite_service.check_enrollment_eligibility(
            next_student.student, course, term
        )
        
        if not eligibility['eligible']:
            # Remove ineligible student and try next
            next_student.remove_from_waitlist()
            return self.process_course_opening(course, term)
        
        # Notify student or auto-enroll
        if next_student.auto_enroll:
            enrollment = next_student.enroll_student()
            self.log_action('auto_enroll', next_student.student, enrollment)
            return next_student
        else:
            next_student.notify_of_opening()
            self.log_action('waitlist_notify', next_student.student, next_student)
            return next_student
    
    def process_waitlist_response(self, waitlist_entry: CourseWaitlist, accept: bool) -> Optional[Enrollment]:
        """Process student response to waitlist notification."""
        
        if waitlist_entry.status != 'NOTIFIED':
            raise ValidationServiceError("Waitlist entry is not in notified status")
        
        if timezone.now() > waitlist_entry.response_deadline:
            waitlist_entry.status = 'EXPIRED'
            waitlist_entry.save()
            raise ValidationServiceError("Response deadline has passed")
        
        if accept:
            # Enroll student
            enrollment = waitlist_entry.enroll_student()
            self.log_action('waitlist_accept', waitlist_entry.student, enrollment)
            
            # Check for next student
            self.process_course_opening(waitlist_entry.course, waitlist_entry.term)
            
            return enrollment
        else:
            # Remove from waitlist
            waitlist_entry.remove_from_waitlist()
            self.log_action('waitlist_decline', waitlist_entry.student, waitlist_entry)
            
            # Process next student
            self.process_course_opening(waitlist_entry.course, waitlist_entry.term)
            
            return None
    
    def get_student_waitlist_status(self, student: User) -> List[Dict[str, Any]]:
        """Get waitlist status for all courses for a student."""
        
        waitlists = self.waitlist_repository.get_student_waitlists(student)
        
        status_list = []
        for waitlist in waitlists:
            # Get current position
            current_position = CourseWaitlist.get_student_position(
                waitlist.course, student, waitlist.term
            )
            
            status_list.append({
                'course': waitlist.course,
                'term': waitlist.term,
                'position': current_position,
                'status': waitlist.get_status_display(),
                'added_at': waitlist.added_at,
                'notified_at': waitlist.notified_at,
                'response_deadline': waitlist.response_deadline,
                'auto_enroll': waitlist.auto_enroll
            })
        
        return status_list
    
    def get_course_waitlist_summary(self, course: Course, term: AcademicTerm) -> Dict[str, Any]:
        """Get summary of waitlist for a course."""
        
        waitlist_entries = self.waitlist_repository.get_course_waitlist(course, term)
        
        return {
            'course': course,
            'term': term,
            'total_waiting': waitlist_entries.count(),
            'entries': [{
                'position': entry.position,
                'student': entry.student,
                'priority': entry.get_priority_display(),
                'added_at': entry.added_at,
                'auto_enroll': entry.auto_enroll
            } for entry in waitlist_entries],
            'last_updated': timezone.now()
        }
    
    @transaction.atomic
    def process_expired_notifications(self) -> Dict[str, int]:
        """Process all expired waitlist notifications."""
        
        expired_count = 0
        processed_count = 0
        
        # Get expired notifications
        expired_entries = CourseWaitlist.objects.filter(
            status='NOTIFIED',
            response_deadline__lt=timezone.now()
        )
        
        for entry in expired_entries:
            entry.status = 'EXPIRED'
            entry.save()
            expired_count += 1
            
            # Process next student in line
            next_entry = self.process_course_opening(entry.course, entry.term)
            if next_entry:
                processed_count += 1
        
        return {
            'expired_notifications': expired_count,
            'new_notifications': processed_count
        }
    
    def _calculate_waitlist_priority(self, academic_record: Optional[StudentAcademicRecord]) -> int:
        """Calculate waitlist priority based on student level and standing."""
        
        if not academic_record:
            return 5  # Lowest priority
        
        # Base priority on credit hours (student level)
        credits = academic_record.total_credits_earned
        
        if credits >= 90:  # Senior
            priority = 1
        elif credits >= 60:  # Junior
            priority = 2
        elif credits >= 30:  # Sophomore
            priority = 3
        else:  # Freshman
            priority = 4
        
        # Adjust for academic standing
        if academic_record.current_standing in ['PROBATION', 'SUSPENSION']:
            priority += 1  # Lower priority for students on probation
        elif academic_record.current_standing in ['HONORS', 'HIGH_HONORS']:
            priority = max(1, priority - 1)  # Higher priority for honors students
        
        return min(5, priority)  # Cap at lowest priority


class EnrollmentHistoryService(BaseService):
    """Service for tracking enrollment history and audit trail."""
    
    def __init__(self):
        super().__init__()
        self.history_repository = BaseRepository(EnrollmentHistory)
    
    def log_enrollment_action(
        self,
        student: User,
        course: Course,
        term: AcademicTerm,
        action: str,
        performed_by: User = None,
        reason: str = '',
        metadata: Dict[str, Any] = None
    ) -> EnrollmentHistory:
        """Log an enrollment action."""
        
        return self.history_repository.create(
            student=student,
            course=course,
            term=term,
            action=action,
            performed_by=performed_by or student,
            reason=reason,
            metadata=metadata or {}
        )
    
    def get_student_enrollment_history(
        self, 
        student: User, 
        term: AcademicTerm = None
    ) -> models.QuerySet[EnrollmentHistory]:
        """Get enrollment history for a student."""
        
        filters = {'student': student}
        if term:
            filters['term'] = term
        
        return self.history_repository.filter(**filters).select_related(
            'course', 'term', 'performed_by'
        ).order_by('-action_date')
    
    def get_course_enrollment_history(
        self, 
        course: Course, 
        term: AcademicTerm = None
    ) -> models.QuerySet[EnrollmentHistory]:
        """Get enrollment history for a course."""
        
        filters = {'course': course}
        if term:
            filters['term'] = term
        
        return self.history_repository.filter(**filters).select_related(
            'student', 'term', 'performed_by'
        ).order_by('-action_date')

# Generated by Django 4.2.7 on 2025-07-04 15:54

import courses.models.course_enhanced
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("core", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Announcement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("content", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="AssessmentResult",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("score", models.FloatField()),
                ("completed_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="Course",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True)),
                ("course_code", models.CharField(max_length=20)),
                ("credits", models.IntegerField(default=3)),
                (
                    "semester",
                    models.CharField(
                        choices=[
                            ("FALL", "Fall"),
                            ("SPRING", "Spring"),
                            ("SUMMER", "Summer"),
                        ],
                        default="FALL",
                        max_length=6,
                    ),
                ),
                (
                    "academic_year",
                    models.CharField(
                        help_text="Academic year in format YYYY-YYYY", max_length=9
                    ),
                ),
                (
                    "required_level",
                    models.IntegerField(
                        choices=[
                            (1, "Beginner"),
                            (2, "Elementary"),
                            (3, "Intermediate"),
                            (4, "Advanced"),
                            (5, "Expert"),
                        ],
                        default=1,
                        help_text="Minimum student level required to take this course",
                    ),
                ),
                (
                    "recommended_level",
                    models.IntegerField(
                        choices=[
                            (1, "Beginner"),
                            (2, "Elementary"),
                            (3, "Intermediate"),
                            (4, "Advanced"),
                            (5, "Expert"),
                        ],
                        default=1,
                        help_text="Recommended student level for optimal learning",
                    ),
                ),
                ("learning_objectives", models.TextField(blank=True)),
                (
                    "syllabus",
                    models.FileField(blank=True, null=True, upload_to="syllabi/"),
                ),
                ("start_date", models.DateField(blank=True, null=True)),
                ("end_date", models.DateField(blank=True, null=True)),
                ("capacity", models.IntegerField(default=30)),
                ("waitlist_capacity", models.IntegerField(default=10)),
                ("is_published", models.BooleanField(default=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "has_interactive_content",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this course has an interactive version",
                    ),
                ),
                (
                    "has_ai_content",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this course has AI-generated content",
                    ),
                ),
                (
                    "max_students",
                    models.IntegerField(
                        default=30,
                        help_text="Maximum number of students allowed in this course",
                    ),
                ),
                (
                    "prerequisites_text",
                    models.TextField(
                        blank=True,
                        default="",
                        help_text="Text description of prerequisites",
                    ),
                ),
                (
                    "course_type",
                    models.CharField(
                        choices=[
                            ("STANDARD", "Standard Course"),
                            ("GENERATED", "AI Generated"),
                            ("INTERACTIVE", "Interactive"),
                            ("HYBRID", "Hybrid (Multiple Types)"),
                        ],
                        default="STANDARD",
                        help_text="Type of course content",
                        max_length=20,
                    ),
                ),
                (
                    "content_providers",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Configuration for different content providers",
                    ),
                ),
                (
                    "enhanced_metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Extended metadata for different course types",
                    ),
                ),
                (
                    "ai_generated_content",
                    models.JSONField(
                        blank=True, default=dict, help_text="AI-generated content data"
                    ),
                ),
                (
                    "interactive_config",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Interactive course configuration",
                    ),
                ),
                (
                    "gamification_enabled",
                    models.BooleanField(
                        default=False,
                        help_text="Whether gamification features are enabled",
                    ),
                ),
                (
                    "gamification_config",
                    models.JSONField(
                        blank=True, default=dict, help_text="Gamification configuration"
                    ),
                ),
                (
                    "has_assessment",
                    models.BooleanField(
                        default=False, help_text="Whether this course has an assessment"
                    ),
                ),
                (
                    "primary_type",
                    models.CharField(
                        choices=[
                            ("STANDARD", "Standard Course"),
                            ("INTERACTIVE", "Interactive Course"),
                            ("AI_GENERATED", "AI-Generated Course"),
                            ("HYBRID", "Hybrid Course"),
                        ],
                        default="STANDARD",
                        help_text="Primary type of this course for unified management",
                        max_length=20,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at", "title"],
            },
        ),
        migrations.CreateModel(
            name="CourseEnhanced",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True)),
                (
                    "semester",
                    models.CharField(
                        choices=[
                            ("FALL", "Fall"),
                            ("SPRING", "Spring"),
                            ("SUMMER", "Summer"),
                        ],
                        default="FALL",
                        max_length=6,
                    ),
                ),
                (
                    "required_level",
                    models.IntegerField(
                        choices=[
                            (1, "Beginner"),
                            (2, "Elementary"),
                            (3, "Intermediate"),
                            (4, "Advanced"),
                            (5, "Expert"),
                        ],
                        default=1,
                        help_text="Minimum student level required to take this course",
                    ),
                ),
                (
                    "recommended_level",
                    models.IntegerField(
                        choices=[
                            (1, "Beginner"),
                            (2, "Elementary"),
                            (3, "Intermediate"),
                            (4, "Advanced"),
                            (5, "Expert"),
                        ],
                        default=1,
                        help_text="Recommended student level for optimal learning",
                    ),
                ),
                ("learning_objectives", models.TextField(blank=True)),
                (
                    "syllabus",
                    models.FileField(blank=True, null=True, upload_to="syllabi/"),
                ),
                ("start_date", models.DateField(blank=True, null=True)),
                ("end_date", models.DateField(blank=True, null=True)),
                ("capacity", models.IntegerField(default=30)),
                ("waitlist_capacity", models.IntegerField(default=10)),
                ("is_published", models.BooleanField(default=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "has_interactive_content",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this course has an interactive version",
                    ),
                ),
                (
                    "has_ai_content",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this course has AI-generated content",
                    ),
                ),
                (
                    "prerequisites_text",
                    models.TextField(
                        blank=True,
                        default="",
                        help_text="Text description of prerequisites",
                    ),
                ),
                (
                    "course_type",
                    models.CharField(
                        choices=[
                            ("STANDARD", "Standard Course"),
                            ("GENERATED", "AI Generated"),
                            ("INTERACTIVE", "Interactive"),
                            ("HYBRID", "Hybrid (Multiple Types)"),
                        ],
                        default="STANDARD",
                        help_text="Type of course content",
                        max_length=20,
                    ),
                ),
                (
                    "content_providers",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Configuration for different content providers",
                    ),
                ),
                (
                    "enhanced_metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Extended metadata for different course types",
                    ),
                ),
                (
                    "ai_generated_content",
                    models.JSONField(
                        blank=True, default=dict, help_text="AI-generated content data"
                    ),
                ),
                (
                    "interactive_config",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Interactive course configuration",
                    ),
                ),
                (
                    "gamification_enabled",
                    models.BooleanField(
                        default=False,
                        help_text="Whether gamification features are enabled",
                    ),
                ),
                (
                    "gamification_config",
                    models.JSONField(
                        blank=True, default=dict, help_text="Gamification configuration"
                    ),
                ),
                (
                    "course_code",
                    models.CharField(
                        help_text="Course code in format like CS101, MATH200",
                        max_length=20,
                        validators=[
                            courses.models.course_enhanced.validate_course_code
                        ],
                    ),
                ),
                (
                    "credits",
                    models.IntegerField(
                        default=3,
                        help_text="Credit hours (0-12)",
                        validators=[
                            courses.models.course_enhanced.validate_credits,
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(12),
                        ],
                    ),
                ),
                (
                    "max_students",
                    models.IntegerField(
                        default=30,
                        help_text="Maximum number of students (1-500)",
                        validators=[
                            courses.models.course_enhanced.validate_capacity,
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(500),
                        ],
                    ),
                ),
                (
                    "academic_year",
                    models.CharField(
                        default=courses.models.course_enhanced.get_current_academic_year,
                        help_text="Academic year in format YYYY-YYYY",
                        max_length=9,
                        validators=[
                            courses.models.course_enhanced.validate_academic_year
                        ],
                    ),
                ),
                (
                    "has_assessment",
                    models.BooleanField(
                        default=False, help_text="Whether this course has an assessment"
                    ),
                ),
                (
                    "primary_type",
                    models.CharField(
                        choices=[
                            ("STANDARD", "Standard Course"),
                            ("INTERACTIVE", "Interactive Course"),
                            ("AI_GENERATED", "AI-Generated Course"),
                            ("HYBRID", "Hybrid Course"),
                        ],
                        default="STANDARD",
                        help_text="Primary type of this course for unified management",
                        max_length=20,
                    ),
                ),
                (
                    "typical_session_duration",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Typical session duration in minutes",
                        null=True,
                        validators=[
                            courses.models.course_enhanced.validate_duration_minutes
                        ],
                    ),
                ),
            ],
            options={
                "verbose_name": "Course",
                "verbose_name_plural": "Courses",
                "ordering": ["-created_at", "title"],
            },
        ),
        migrations.CreateModel(
            name="CoursePrerequisite",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "relationship_type",
                    models.CharField(
                        choices=[
                            ("NEXT_LEVEL", "Next Level Course"),
                            ("ALTERNATIVE", "Alternative Course"),
                            ("COMPLEMENTARY", "Complementary Course"),
                            ("PREREQUISITE", "Prerequisite"),
                        ],
                        default="NEXT_LEVEL",
                        max_length=20,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Description of how these courses are related",
                    ),
                ),
                (
                    "recommended",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this relationship is strongly recommended",
                    ),
                ),
                (
                    "minimum_grade",
                    models.CharField(
                        choices=[
                            ("A", "A"),
                            ("B", "B"),
                            ("C", "C"),
                            ("D", "D"),
                            ("F", "F"),
                        ],
                        default="C",
                        help_text="Minimum grade required in prerequisite course",
                        max_length=2,
                    ),
                ),
                (
                    "is_mandatory",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this prerequisite is mandatory or just recommended",
                    ),
                ),
            ],
            options={
                "verbose_name": "Course Prerequisite",
                "verbose_name_plural": "Course Prerequisites",
            },
        ),
        migrations.CreateModel(
            name="CourseSection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "section_number",
                    models.CharField(
                        help_text="Section identifier (e.g., A, B, 001, 002)",
                        max_length=10,
                    ),
                ),
                (
                    "meeting_times",
                    models.JSONField(
                        default=dict,
                        help_text="Meeting times and locations in JSON format",
                    ),
                ),
                (
                    "max_enrollment",
                    models.PositiveIntegerField(
                        default=30,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(200),
                        ],
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
            ],
            options={
                "verbose_name": "Course Section",
                "verbose_name_plural": "Course Sections",
            },
        ),
        migrations.CreateModel(
            name="Department",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Full name of the department", max_length=255
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        help_text="Short department code (e.g. CS, EE, MATH)",
                        max_length=10,
                        unique=True,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="Detailed description of the department"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, help_text="Department email address", max_length=254
                    ),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True, help_text="Department phone number", max_length=20
                    ),
                ),
                (
                    "website",
                    models.URLField(blank=True, help_text="Department website"),
                ),
                (
                    "building",
                    models.CharField(
                        blank=True,
                        help_text="Building where department is located",
                        max_length=100,
                    ),
                ),
                (
                    "room",
                    models.CharField(
                        blank=True,
                        help_text="Room number or department office",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Enrollment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("APPROVED", "Approved"),
                            ("REJECTED", "Rejected"),
                            ("COMPLETED", "Completed"),
                            ("DROPPED", "Dropped"),
                        ],
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                (
                    "enrollment_type",
                    models.CharField(
                        choices=[
                            ("REGULAR", "Regular"),
                            ("AUDIT", "Audit"),
                            ("HONORS", "Honors"),
                        ],
                        default="REGULAR",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "enrollment_date",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                ("completion_date", models.DateTimeField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
            ],
            options={
                "verbose_name": "Enrollment",
                "verbose_name_plural": "Enrollments",
                "ordering": ["-enrollment_date"],
            },
        ),
        migrations.CreateModel(
            name="SkillAssessment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("score", models.FloatField()),
                ("assessment_date", models.DateTimeField(auto_now_add=True)),
                (
                    "skill",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.skill"
                    ),
                ),
            ],
        ),
    ]

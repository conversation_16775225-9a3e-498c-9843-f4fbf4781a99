from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework import serializers
from .models import (
    Assessment,
    AssessmentQuestion,
    AssessmentResponse,
    AssessmentSettings,
    StudentLevel,
    LevelRequirement
)

# Import Skill from core app where it's actually defined
from core.models import Skill

# Import Course model directly
try:
    from courses.models import Course
except ImportError:
    # Fallback to importing from models module
    from django.apps import apps
    try:
        Course = apps.get_model("courses", "Course")
    except LookupError:
        # Define a placeholder Course model for development
        from django.db import models
        class Course(models.Model):
            title = models.CharField(max_length=200)
            course_code = models.CharField(max_length=10)

User = get_user_model()


class AssessmentResponseSerializer(serializers.ModelSerializer):
    """Serializer for assessment responses"""
    question_text = serializers.CharField(source="question.question_text", read_only=True)
    points_possible = serializers.IntegerField(source="question.points", read_only=True)
    
    # Add a combined field for the answer that prioritizes answer_text
    combined_answer = serializers.SerializerMethodField()
    
    def get_combined_answer(self, obj):
        """Get the answer from the primary field first, then fallbacks"""
        # First try the primary field
        if obj.answer_text:
            return obj.answer_text
        # Then try the legacy fields
        elif obj.answer:
            return obj.answer
        elif obj.student_answer:
            # If it's a dict or JSON, return as is
            return obj.student_answer
        # Default to empty string
        return ""
    
    class Meta:
        model = AssessmentResponse
        fields = [
            "id", "question", "question_text", "answer_text", "answer", 
            "student_answer", "combined_answer", "is_correct", "points_earned", 
            "points_possible", "time_spent", "feedback", "created_at"
        ]
        read_only_fields = ["is_correct", "points_earned", "created_at", "combined_answer"]


class AssessmentSkillSerializer(serializers.ModelSerializer):
    """Serializer for assessment skills"""
    skill_name = serializers.CharField(source="name", read_only=True)
    
    class Meta:
        model = Skill
        fields = ["id", "name", "skill_name", "category", "description"]


class AssessmentQuestionSerializer(serializers.ModelSerializer):
    """Serializer for assessment questions"""
    # Include text field for backward compatibility - make it required
    text = serializers.CharField(source="question_text", required=True)
    
    class Meta:
        model = AssessmentQuestion
        fields = [
            "id", "text", "question_type", "category", "learning_path", 
            "difficulty_level", "options", "points", "skills_assessed", 
            "correct_answer", "ai_suggested", "ai_reviewed", "is_public", 
            "is_placement", "ai_generated"
        ]
        read_only_fields = ["created_at", "updated_at"]
    
    def validate_correct_answer(self, value):
        """Validate that correct_answer is properly formatted"""
        if isinstance(value, str):
            # Convert string to object format
            return {"answer": value, "explanation": ""}
        elif not isinstance(value, dict):
            raise serializers.ValidationError(
                "Correct answer must be a string or an object with 'answer' field"
            )
        elif "answer" not in value:
            raise serializers.ValidationError(
                "Correct answer object must contain 'answer' field"
            )
        return value
    
    def to_representation(self, instance):
        """Format correct answer in representation"""
        data = super().to_representation(instance)
        
        # Format correct_answer if needed and it's included
        if "correct_answer" in data:
            correct_answer = data["correct_answer"]
            if isinstance(correct_answer, str):
                data["correct_answer"] = {"answer": correct_answer, "explanation": ""}
            elif isinstance(correct_answer, dict) and "answer" not in correct_answer:
                # Try to convert to proper format
                data["correct_answer"] = {"answer": str(correct_answer), "explanation": ""}
            elif isinstance(correct_answer, dict) and "answer" in correct_answer:
                # Ensure explanation field exists
                if "explanation" not in correct_answer:
                    data["correct_answer"]["explanation"] = ""
        
        # Only include correct answer if explicitly requested
        if not self.context.get("show_correct_answer", False):
            data.pop("correct_answer", None)
        
        return data


class AssessmentSerializer(serializers.ModelSerializer):
    """Serializer for Assessment model"""
    questions = AssessmentQuestionSerializer(many=True, read_only=True)
    responses = AssessmentResponseSerializer(many=True, read_only=True)
    time_remaining = serializers.SerializerMethodField()
    skills_assessed = AssessmentSkillSerializer(many=True, read_only=True)
    student_level = serializers.SerializerMethodField()
    student_name = serializers.SerializerMethodField()
    student_id = serializers.SerializerMethodField()
    
    def get_student_id(self, obj):
        """Get student ID ensuring it's always available"""
        if obj.student:
            return obj.student.id
        return None
    
    def get_student_name(self, obj):
        """Get student name ensuring it's not 'undefined undefined'"""
        if not obj.student:
            return "Unknown Student"
        
        # Try to get full name using get_full_name method
        full_name = (
            obj.student.get_full_name() 
            if callable(obj.student.get_full_name) 
            else obj.student.get_full_name
        )
        
        # If full name is empty or contains 'undefined', build from first and last name
        if not full_name or "undefined" in full_name:
            first_name = getattr(obj.student, "first_name", "")
            last_name = getattr(obj.student, "last_name", "")
            
            # If both first and last name are empty, use username or email
            if not first_name and not last_name:
                return (
                    getattr(obj.student, "username", "") or 
                    getattr(obj.student, "email", "") or 
                    f"User ID: {obj.student.id}"
                )
            
            # Otherwise combine first and last name
            return f"{first_name} {last_name}".strip()
        
        return full_name
    
    class Meta:
        model = Assessment
        fields = [
            "id", "title", "description", "assessment_type", "questions", 
            "responses", "skills_assessed", "start_time", "end_time", 
            "status", "score", "skill_scores", "completed", "time_remaining", 
            "created_at", "updated_at", "student_level", "student_id", "student_name"
        ]
        read_only_fields = [
            "start_time", "end_time", "status", "score", "skill_scores", 
            "completed", "created_at", "updated_at", "student_level"
        ]
    
    def get_time_remaining(self, obj):
        """Calculate remaining time for assessment"""
        if not obj.start_time or obj.completed:
            return None
        
        settings = AssessmentSettings.objects.filter(assessment_type=obj.assessment_type).first()
        if not settings:
            return None
        
        elapsed = timezone.now() - obj.start_time
        remaining = settings.time_limit * 60 - elapsed.total_seconds()
        return max(0, int(remaining))
    
    def get_student_level(self, obj):
        """Get student level information"""
        try:
            if not obj.student:
                return None
            
            student_level = StudentLevel.objects.filter(student=obj.student).first()
            if not student_level:
                return {"current_level": 1, "current_level_display": "Beginner"}
            
            return {
                "current_level": student_level.current_level,
                "current_level_display": student_level.current_level_display,
                "last_assessment_date": (
                    student_level.last_assessment_date.isoformat() 
                    if student_level.last_assessment_date 
                    else None
                )
            }
        except Exception as e:
            print(f"Error getting student level: {e}")
            return None


class AssessmentSettingsSerializer(serializers.ModelSerializer):
    """Serializer for assessment settings"""
    
    class Meta:
        model = AssessmentSettings
        fields = [
            "id", "assessment_type", "max_attempts", "time_limit", 
            "passing_score", "questions_per_assessment", "allow_retakes", 
            "retake_cooldown_days", "adaptive_difficulty", 
            "difficulty_adjustment_threshold", "randomize_questions", 
            "randomize_options", "prevent_backtracking", 
            "show_correct_answers", "show_explanations", "immediate_feedback"
        ]


class StudentLevelSerializer(serializers.ModelSerializer):
    """Serializer for student level information"""
    level_name = serializers.CharField(source="get_current_level_display", read_only=True)
    student_name = serializers.CharField(source="student.get_full_name", read_only=True)
    student_email = serializers.CharField(source="student.email", read_only=True)
    
    class Meta:
        model = StudentLevel
        fields = [
            "id", "student", "student_name", "student_email", "current_level", 
            "level_name", "last_assessment_date", "skill_strengths", 
            "skill_weaknesses", "progression_history", "created_at", "updated_at"
        ]
        read_only_fields = ["level_name", "created_at", "updated_at"]


class LevelRequirementSerializer(serializers.ModelSerializer):
    """Serializer for level requirements"""
    
    class Meta:
        model = LevelRequirement
        fields = "__all__"


class SkillSerializer(serializers.ModelSerializer):
    """Serializer for the Skill model"""
    
    class Meta:
        model = Skill
        fields = ["id", "name", "description", "category", "created_at"]


# Simple serializers for API requests
class AssessmentSubmissionSerializer(serializers.Serializer):
    question_id = serializers.IntegerField()
    answer = serializers.CharField()
    time_spent_seconds = serializers.IntegerField(required=False)


class InitialAssessmentSerializer(serializers.Serializer):
    category = serializers.CharField(required=False)
    difficulty_level = serializers.IntegerField(required=False, min_value=1, max_value=5)


class AIQuestionSuggestionSerializer(serializers.Serializer):
    category = serializers.CharField(required=False)
    difficulty_level = serializers.IntegerField(min_value=1, max_value=5)
    count = serializers.IntegerField(required=False, min_value=1, max_value=10)
    topic = serializers.CharField(required=False, allow_blank=True)


class AssessmentDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for assessment with all related data"""
    questions = AssessmentQuestionSerializer(many=True, read_only=True)
    responses = AssessmentResponseSerializer(many=True, read_only=True)
    student_name = serializers.SerializerMethodField()
    student_email = serializers.CharField(source="student.email", read_only=True)
    student_id = serializers.IntegerField(source="student.id", read_only=True)
    assessment_type_display = serializers.CharField(source="get_assessment_type_display", read_only=True)
    score_display = serializers.SerializerMethodField()

    def get_student_name(self, obj):
        """Get student name ensuring it's not 'undefined undefined'"""
        if not obj.student:
            return "Unknown Student"

        # Try to get full name using get_full_name method
        full_name = (
            obj.student.get_full_name()
            if callable(obj.student.get_full_name)
            else obj.student.get_full_name
        )

        # If full name is empty or contains 'undefined', build from first and last name
        if not full_name or "undefined" in full_name:
            first_name = getattr(obj.student, "first_name", "")
            last_name = getattr(obj.student, "last_name", "")

            # If both first and last name are empty, use username or email
            if not first_name and not last_name:
                return getattr(obj.student, "username", "") or getattr(obj.student, "email", "Unknown Student")

            # Otherwise combine first and last name
            return f"{first_name} {last_name}".strip()

        return full_name

    def get_score_display(self, obj):
        """Format score as percentage with % symbol"""
        if obj.score is not None:
            return f"{obj.score}%"
        return None

    class Meta:
        model = Assessment
        fields = [
            "id", "student", "questions", "responses", "start_time", "end_time",
            "status", "score", "score_display", "assessment_type", "assessment_type_display",
            "detailed_results", "student_name", "student_email", "student_id",
            "time_spent", "created_at", "updated_at"
        ]
        read_only_fields = [
            "start_time", "end_time", "score", "score_display", "detailed_results",
            "assessment_type_display", "student_email", "responses", "time_spent",
            "created_at", "updated_at"
        ]


class AdminQuestionListSerializer(serializers.ModelSerializer):
    class Meta:
        model = AssessmentQuestion
        fields = "__all__"

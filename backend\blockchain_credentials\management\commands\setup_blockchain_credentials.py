"""
Django management command to set up initial blockchain credentials system.

This command creates:
- Default blockchain networks
- Credential templates
- Sample smart contracts
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from datetime import timedelta

from blockchain_credentials.models import (
    BlockchainNetwork,
    CredentialTemplate,
    SmartContract
)


class Command(BaseCommand):
    help = 'Set up initial blockchain credentials system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset existing data before setup',
        )

    @transaction.atomic
    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Resetting existing blockchain credentials data...')
            self.reset_data()

        self.stdout.write('Setting up blockchain networks...')
        self.setup_networks()

        self.stdout.write('Setting up credential templates...')
        self.setup_templates()

        self.stdout.write('Setting up smart contracts...')
        self.setup_smart_contracts()

        self.stdout.write(
            self.style.SUCCESS('✅ Blockchain credentials system setup completed!')
        )

    def reset_data(self):
        """Reset existing blockchain credentials data"""
        SmartContract.objects.all().delete()
        CredentialTemplate.objects.all().delete()
        BlockchainNetwork.objects.all().delete()
        self.stdout.write(self.style.WARNING('Reset completed.'))

    def setup_networks(self):
        """Set up default blockchain networks"""
        networks = [
            {
                'name': 'Polygon Mumbai Testnet',
                'network_type': 'POLYGON',
                'chain_id': 80001,
                'rpc_url': 'https://rpc-mumbai.maticvigil.com',
                'explorer_url': 'https://mumbai.polygonscan.com',
                'is_testnet': True,
                'is_active': True,
                'gas_fee_estimate': 0.001,
            },
            {
                'name': 'Ethereum Sepolia Testnet',
                'network_type': 'ETHEREUM',
                'chain_id': 11155111,
                'rpc_url': 'https://sepolia.infura.io/v3/',
                'explorer_url': 'https://sepolia.etherscan.io',
                'is_testnet': True,
                'is_active': True,
                'gas_fee_estimate': 0.01,
            },
            {
                'name': 'Binance Smart Chain Testnet',
                'network_type': 'BINANCE',
                'chain_id': 97,
                'rpc_url': 'https://data-seed-prebsc-1-s1.binance.org:8545',
                'explorer_url': 'https://testnet.bscscan.com',
                'is_testnet': True,
                'is_active': True,
                'gas_fee_estimate': 0.005,
            },
            {
                'name': 'Avalanche Fuji Testnet',
                'network_type': 'AVALANCHE',
                'chain_id': 43113,
                'rpc_url': 'https://api.avax-test.network/ext/bc/C/rpc',
                'explorer_url': 'https://testnet.snowtrace.io',
                'is_testnet': True,
                'is_active': True,
                'gas_fee_estimate': 0.01,
            }
        ]

        for network_data in networks:
            network, created = BlockchainNetwork.objects.get_or_create(
                name=network_data['name'],
                defaults=network_data
            )
            if created:
                self.stdout.write(f'  ✓ Created network: {network.name}')
            else:
                self.stdout.write(f'  → Network already exists: {network.name}')

    def setup_templates(self):
        """Set up default credential templates"""
        # Get default network
        default_network = BlockchainNetwork.objects.filter(
            is_active=True, 
            is_testnet=True
        ).first()

        templates = [
            {
                'name': 'Course Completion Certificate',
                'credential_type': 'COURSE_COMPLETION',
                'description': 'Certificate awarded upon successful completion of a course',
                'template_design': {
                    'background_color': '#1e3a8a',
                    'text_color': '#ffffff',
                    'border_style': 'elegant',
                    'logo_position': 'top-center'
                },
                'required_fields': ['course', 'final_grade', 'completion_date'],
                'verification_requirements': {
                    'min_grade': 'D',
                    'attendance_required': True
                },
                'default_validity_period': timedelta(days=365*10),  # 10 years
                'blockchain_enabled': True,
                'is_renewable': False,
                'default_network': default_network
            },
            {
                'name': 'Professional Certification',
                'credential_type': 'CERTIFICATION',
                'description': 'Professional certification for specialized skills',
                'template_design': {
                    'background_color': '#059669',
                    'text_color': '#ffffff',
                    'border_style': 'professional',
                    'logo_position': 'top-left'
                },
                'required_fields': ['skills_acquired', 'competency_level', 'assessment'],
                'verification_requirements': {
                    'min_score': 80,
                    'practical_assessment': True
                },
                'default_validity_period': timedelta(days=365*3),  # 3 years
                'blockchain_enabled': True,
                'is_renewable': True,
                'default_network': default_network
            },
            {
                'name': 'Skill Badge',
                'credential_type': 'SKILL_BADGE',
                'description': 'Digital badge for specific skill achievements',
                'template_design': {
                    'background_color': '#7c2d12',
                    'text_color': '#ffffff',
                    'border_style': 'modern',
                    'logo_position': 'center'
                },
                'required_fields': ['skills_acquired', 'competency_level'],
                'verification_requirements': {
                    'demonstration_required': True
                },
                'blockchain_enabled': True,
                'is_renewable': False,
                'default_network': default_network
            },
            {
                'name': 'Academic Achievement',
                'credential_type': 'ACHIEVEMENT',
                'description': 'Recognition for outstanding academic performance',
                'template_design': {
                    'background_color': '#7c2d12',
                    'text_color': '#fbbf24',
                    'border_style': 'decorative',
                    'logo_position': 'top-center'
                },
                'required_fields': ['achievement_data', 'completion_date'],
                'verification_requirements': {
                    'peer_review': True,
                    'faculty_approval': True
                },
                'blockchain_enabled': True,
                'is_renewable': False,
                'default_network': default_network
            },
            {
                'name': 'Micro-Credential',
                'credential_type': 'MICRO_CREDENTIAL',
                'description': 'Micro-credential for specific learning outcomes',
                'template_design': {
                    'background_color': '#4338ca',
                    'text_color': '#ffffff',
                    'border_style': 'minimal',
                    'logo_position': 'bottom-right'
                },
                'required_fields': ['skills_acquired', 'course', 'completion_date'],
                'verification_requirements': {
                    'portfolio_submission': True
                },
                'default_validity_period': timedelta(days=365*2),  # 2 years
                'blockchain_enabled': True,
                'is_renewable': True,
                'default_network': default_network
            }
        ]

        for template_data in templates:
            template, created = CredentialTemplate.objects.get_or_create(
                name=template_data['name'],
                credential_type=template_data['credential_type'],
                defaults=template_data
            )
            if created:
                self.stdout.write(f'  ✓ Created template: {template.name}')
            else:
                self.stdout.write(f'  → Template already exists: {template.name}')

    def setup_smart_contracts(self):
        """Set up sample smart contracts for each network"""
        networks = BlockchainNetwork.objects.filter(is_active=True)
        
        # Sample ABI for educational purposes
        sample_abi = [
            {
                "inputs": [
                    {"name": "to", "type": "address"},
                    {"name": "tokenId", "type": "uint256"},
                    {"name": "metadataURI", "type": "string"}
                ],
                "name": "mintCredential",
                "outputs": [],
                "type": "function"
            },
            {
                "inputs": [{"name": "tokenId", "type": "uint256"}],
                "name": "verifyCredential",
                "outputs": [{"name": "", "type": "bool"}],
                "type": "function"
            }
        ]

        for network in networks:
            # Credential Contract
            credential_contract, created = SmartContract.objects.get_or_create(
                name=f'North Star Credentials',
                contract_type='CREDENTIAL',
                blockchain_network=network,
                defaults={
                    'contract_address': f'0x{network.chain_id:040x}' + '1' * 36,  # Mock address
                    'deployment_transaction': f'0x{network.chain_id:040x}' + 'a' * 36,
                    'abi': sample_abi,
                    'version': '1.0.0',
                    'owner_address': '0x' + '1' * 40,  # Mock owner address
                    'admin_addresses': ['0x' + '2' * 40],
                    'deployment_gas_used': 2500000,
                    'deployment_cost': network.gas_fee_estimate * 2500000
                }
            )
            
            if created:
                # Update network with contract address
                network.credential_contract_address = credential_contract.contract_address
                self.stdout.write(f'  ✓ Created credential contract for {network.name}')
            
            # NFT Achievement Contract
            nft_contract, created = SmartContract.objects.get_or_create(
                name=f'North Star NFT Achievements',
                contract_type='NFT',
                blockchain_network=network,
                defaults={
                    'contract_address': f'0x{network.chain_id:040x}' + '2' * 36,  # Mock address
                    'deployment_transaction': f'0x{network.chain_id:040x}' + 'b' * 36,
                    'abi': sample_abi,
                    'version': '1.0.0',
                    'owner_address': '0x' + '1' * 40,  # Mock owner address
                    'admin_addresses': ['0x' + '2' * 40],
                    'deployment_gas_used': 3000000,
                    'deployment_cost': network.gas_fee_estimate * 3000000
                }
            )
            
            if created:
                # Update network with NFT contract address
                network.nft_contract_address = nft_contract.contract_address
                network.save()
                self.stdout.write(f'  ✓ Created NFT contract for {network.name}')

        self.stdout.write(f'Smart contracts setup completed for {networks.count()} networks.')

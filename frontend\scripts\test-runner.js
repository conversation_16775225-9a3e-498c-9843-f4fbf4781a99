#!/usr/bin/env node

/**
 * Comprehensive test runner for North Star University frontend
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const yargs = require('yargs');

class FrontendTestRunner {
  constructor() {
    this.startTime = Date.now();
    this.results = {
      lint: null,
      typeCheck: null,
      unit: null,
      integration: null,
      e2e: null,
      build: null,
      coverage: null,
      accessibility: null,
      performance: null
    };
  }

  runCommand(command, description, options = {}) {
    console.log(`\n${'='.repeat(60)}`);
    console.log(`Running: ${description}`);
    console.log(`Command: ${command}`);
    console.log(`${'='.repeat(60)}`);

    try {
      const result = execSync(command, {
        cwd: process.cwd(),
        stdio: options.silent ? 'pipe' : 'inherit',
        encoding: 'utf8',
        timeout: options.timeout || 300000, // 5 minutes default
        ...options
      });

      console.log(`✅ ${description} - PASSED`);
      return true;
    } catch (error) {
      console.log(`❌ ${description} - FAILED`);
      if (options.silent && error.stdout) {
        console.log('STDOUT:', error.stdout);
      }
      if (options.silent && error.stderr) {
        console.log('STDERR:', error.stderr);
      }
      return false;
    }
  }

  async runLinting() {
    console.log('\n🧹 Running Code Linting...');
    
    // ESLint
    const eslintResult = this.runCommand(
      'npm run lint',
      'ESLint Code Linting'
    );

    // Prettier check
    const prettierResult = this.runCommand(
      'npm run format:check',
      'Prettier Code Formatting'
    );

    // Stylelint (if configured)
    let stylelintResult = true;
    if (fs.existsSync('.stylelintrc.json')) {
      stylelintResult = this.runCommand(
        'npm run lint:styles',
        'Stylelint CSS Linting'
      );
    }

    this.results.lint = eslintResult && prettierResult && stylelintResult;
    return this.results.lint;
  }

  async runTypeCheck() {
    console.log('\n🔍 Running TypeScript Type Check...');
    
    this.results.typeCheck = this.runCommand(
      'npm run type-check',
      'TypeScript Type Check'
    );
    
    return this.results.typeCheck;
  }

  async runUnitTests() {
    console.log('\n🧪 Running Unit Tests...');
    
    this.results.unit = this.runCommand(
      'npm run test:unit -- --coverage --watchAll=false',
      'Jest Unit Tests'
    );
    
    return this.results.unit;
  }

  async runIntegrationTests() {
    console.log('\n🔗 Running Integration Tests...');
    
    this.results.integration = this.runCommand(
      'npm run test:integration -- --watchAll=false',
      'Integration Tests'
    );
    
    return this.results.integration;
  }

  async runE2ETests() {
    console.log('\n🌐 Running End-to-End Tests...');
    
    // Check if Playwright is configured
    if (fs.existsSync('playwright.config.ts') || fs.existsSync('e2e/playwright.config.ts')) {
      this.results.e2e = this.runCommand(
        'npx playwright test',
        'Playwright E2E Tests',
        { timeout: 600000 } // 10 minutes for E2E tests
      );
    } else if (fs.existsSync('cypress.config.js')) {
      this.results.e2e = this.runCommand(
        'npm run test:e2e',
        'Cypress E2E Tests',
        { timeout: 600000 }
      );
    } else {
      console.log('⏭️ No E2E test configuration found, skipping...');
      this.results.e2e = null;
    }
    
    return this.results.e2e;
  }

  async runBuild() {
    console.log('\n🏗️ Running Production Build...');
    
    this.results.build = this.runCommand(
      'npm run build',
      'Production Build'
    );
    
    return this.results.build;
  }

  async checkCoverage() {
    console.log('\n📊 Checking Test Coverage...');
    
    // Generate coverage report
    const coverageResult = this.runCommand(
      'npm run test:coverage -- --watchAll=false --silent',
      'Coverage Report Generation'
    );

    if (coverageResult && fs.existsSync('coverage/lcov-report/index.html')) {
      console.log('📋 Coverage report generated at: coverage/lcov-report/index.html');
      
      // Check coverage thresholds
      try {
        const coverageData = JSON.parse(
          fs.readFileSync('coverage/coverage-summary.json', 'utf8')
        );
        
        const total = coverageData.total;
        const thresholds = {
          statements: 90,
          branches: 90,
          functions: 90,
          lines: 90
        };

        let coveragePassed = true;
        for (const [metric, threshold] of Object.entries(thresholds)) {
          const actual = total[metric].pct;
          if (actual < threshold) {
            console.log(`❌ ${metric} coverage ${actual}% is below threshold ${threshold}%`);
            coveragePassed = false;
          } else {
            console.log(`✅ ${metric} coverage ${actual}% meets threshold ${threshold}%`);
          }
        }

        this.results.coverage = coveragePassed;
      } catch (error) {
        console.log('⚠️ Could not parse coverage data');
        this.results.coverage = coverageResult;
      }
    } else {
      this.results.coverage = false;
    }
    
    return this.results.coverage;
  }

  async runAccessibilityTests() {
    console.log('\n♿ Running Accessibility Tests...');
    
    // Check if axe-core tests are configured
    if (fs.existsSync('src/tests/accessibility')) {
      this.results.accessibility = this.runCommand(
        'npm run test:a11y',
        'Accessibility Tests'
      );
    } else {
      console.log('⏭️ No accessibility tests configured, skipping...');
      this.results.accessibility = null;
    }
    
    return this.results.accessibility;
  }

  async runPerformanceTests() {
    console.log('\n⚡ Running Performance Tests...');
    
    // Build first if not already built
    if (!fs.existsSync('dist') && !fs.existsSync('build')) {
      console.log('📦 Building application for performance testing...');
      await this.runBuild();
    }

    // Run Lighthouse CI if configured
    if (fs.existsSync('lighthouserc.js')) {
      this.results.performance = this.runCommand(
        'npx lhci autorun',
        'Lighthouse Performance Tests',
        { timeout: 300000 }
      );
    } else {
      console.log('⏭️ No Lighthouse CI configuration found, skipping...');
      this.results.performance = null;
    }
    
    return this.results.performance;
  }

  async runSecurityAudit() {
    console.log('\n🔒 Running Security Audit...');
    
    const auditResult = this.runCommand(
      'npm audit --audit-level=moderate',
      'NPM Security Audit'
    );

    // Run additional security checks if configured
    let additionalSecurityResult = true;
    if (fs.existsSync('.eslintrc.js') || fs.existsSync('.eslintrc.json')) {
      // Check if security plugins are configured
      additionalSecurityResult = this.runCommand(
        'npm run lint:security',
        'ESLint Security Rules'
      );
    }

    return auditResult && additionalSecurityResult;
  }

  generateReport() {
    const endTime = Date.now();
    const duration = (endTime - this.startTime) / 1000;

    console.log(`\n${'='.repeat(80)}`);
    console.log('📋 COMPREHENSIVE TEST REPORT');
    console.log(`${'='.repeat(80)}`);
    console.log(`Total Duration: ${duration.toFixed(2)} seconds`);
    console.log(`Start Time: ${new Date(this.startTime).toISOString()}`);
    console.log(`End Time: ${new Date(endTime).toISOString()}`);
    console.log(`${'='.repeat(80)}`);

    // Test results summary
    const totalTests = Object.keys(this.results).length;
    const passedTests = Object.values(this.results).filter(r => r === true).length;
    const failedTests = Object.values(this.results).filter(r => r === false).length;
    const skippedTests = Object.values(this.results).filter(r => r === null).length;

    console.log(`Test Categories: ${totalTests}`);
    console.log(`Passed: ${passedTests} ✅`);
    console.log(`Failed: ${failedTests} ❌`);
    console.log(`Skipped: ${skippedTests} ⏭️`);
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log(`${'='.repeat(80)}`);

    // Detailed results
    for (const [testType, result] of Object.entries(this.results)) {
      const status = result === true ? '✅ PASSED' : 
                    result === false ? '❌ FAILED' : '⏭️ SKIPPED';
      console.log(`${testType.toUpperCase().padEnd(15)} | ${status}`);
    }

    console.log(`${'='.repeat(80)}`);

    // Overall result
    const overallSuccess = Object.values(this.results).every(result => result !== false);
    if (overallSuccess) {
      console.log('🎉 ALL TESTS PASSED! 🎉');
      return 0;
    } else {
      console.log('💥 SOME TESTS FAILED! 💥');
      return 1;
    }
  }
}

async function main() {
  const argv = yargs
    .option('lint', { type: 'boolean', description: 'Run linting' })
    .option('type-check', { type: 'boolean', description: 'Run TypeScript type check' })
    .option('unit', { type: 'boolean', description: 'Run unit tests' })
    .option('integration', { type: 'boolean', description: 'Run integration tests' })
    .option('e2e', { type: 'boolean', description: 'Run end-to-end tests' })
    .option('build', { type: 'boolean', description: 'Run production build' })
    .option('coverage', { type: 'boolean', description: 'Check test coverage' })
    .option('accessibility', { type: 'boolean', description: 'Run accessibility tests' })
    .option('performance', { type: 'boolean', description: 'Run performance tests' })
    .option('security', { type: 'boolean', description: 'Run security audit' })
    .option('all', { type: 'boolean', description: 'Run all tests' })
    .option('quick', { type: 'boolean', description: 'Run quick test suite (lint + unit)' })
    .help()
    .argv;

  const runner = new FrontendTestRunner();

  // Determine which tests to run
  let testsToRun = [];
  
  if (argv.all) {
    testsToRun = ['lint', 'typeCheck', 'unit', 'integration', 'build', 'coverage', 'accessibility', 'performance', 'security'];
  } else if (argv.quick) {
    testsToRun = ['lint', 'typeCheck', 'unit'];
  } else {
    if (argv.lint) testsToRun.push('lint');
    if (argv['type-check']) testsToRun.push('typeCheck');
    if (argv.unit) testsToRun.push('unit');
    if (argv.integration) testsToRun.push('integration');
    if (argv.e2e) testsToRun.push('e2e');
    if (argv.build) testsToRun.push('build');
    if (argv.coverage) testsToRun.push('coverage');
    if (argv.accessibility) testsToRun.push('accessibility');
    if (argv.performance) testsToRun.push('performance');
    if (argv.security) testsToRun.push('security');
  }

  // If no specific tests specified, run quick suite
  if (testsToRun.length === 0) {
    testsToRun = ['lint', 'typeCheck', 'unit'];
  }

  // Run selected tests
  for (const testType of testsToRun) {
    switch (testType) {
      case 'lint':
        await runner.runLinting();
        break;
      case 'typeCheck':
        await runner.runTypeCheck();
        break;
      case 'unit':
        await runner.runUnitTests();
        break;
      case 'integration':
        await runner.runIntegrationTests();
        break;
      case 'e2e':
        await runner.runE2ETests();
        break;
      case 'build':
        await runner.runBuild();
        break;
      case 'coverage':
        await runner.checkCoverage();
        break;
      case 'accessibility':
        await runner.runAccessibilityTests();
        break;
      case 'performance':
        await runner.runPerformanceTests();
        break;
      case 'security':
        await runner.runSecurityAudit();
        break;
    }
  }

  // Generate final report
  const exitCode = runner.generateReport();
  process.exit(exitCode);
}

if (require.main === module) {
  main().catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = FrontendTestRunner;

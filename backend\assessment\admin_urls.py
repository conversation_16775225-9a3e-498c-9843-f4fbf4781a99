from django.urlsimportincludepathfromrest_framework.routersimportDefaultRouterfrom.importadmin_viewsfrom.admin_viewsimport(AdminAssessmentStatsViewSetAIAssessmentAnalysisViewSetCompetencyBadgeViewSetLearningPathwayViewSetProgressionMilestoneViewSetQuestionManagementViewSet#SkillViewSet#Commentedout-modeldoesn'texistStudentAssessmentAdminViewSet)from.ai_decision_viewsimportAIDecisionViewSetapp_name="assessment-admin"router=DefaultRouter()#Coreadminroutesrouter.register(r"stats"AdminAssessmentStatsViewSetbasename="stats")router.register(r"questions"QuestionManagementViewSetbasename="questions")router.register(r"student-assessments"StudentAssessmentAdminViewSetbasename="student-assessments")#Progressionmanagement#router.register(r"skills"SkillViewSetbasename="skill")#Commentedout-modeldoesn'texistrouter.register(r"learning-paths"LearningPathwayViewSetbasename="learning-path")router.register(r"milestones"ProgressionMilestoneViewSetbasename="milestone")router.register(r"badges"CompetencyBadgeViewSetbasename="badge")#Admin-specificroutesrouter.register(r"admin-questions"admin_views.AdminQuestionViewSetbasename="admin-question")router.register(r"assessments"admin_views.AdminAssessmentViewSetbasename="admin-assessment")router.register(r"settings"admin_views.AssessmentSettingsViewSetbasename="admin-settings")#AIassessmentanalysisroutesrouter.register(r"ai-analysis"AIAssessmentAnalysisViewSetbasename="ai-analysis")router.register(r"ai-decisions"AIDecisionViewSetbasename="ai-decisions")urlpatterns=[path(""include((router.urlsapp_name)))#Assessmentdataendpointspath("recent-assessments/"admin_views.AdminAssessmentViewSet.as_view({"get":"recent_assessments"})name="recent-assessments")path("score-distribution/"admin_views.AdminAssessmentViewSet.as_view({"get":"score_distribution"})name="score-distribution")path("level-requirements/"admin_views.AdminAssessmentViewSet.as_view({"get":"level_requirements"})name="level-requirements")#Questionperformanceendpointpath("questions/performance/"admin_views.AdminQuestionViewSet.as_view({"get":"performance"})name="question-performance")#AI-poweredfeaturespath("admin-questions/generate-ai-questions/"admin_views.AdminQuestionGenerationView.as_view()name="generate-ai-questions")path("generate-questions/"admin_views.AdminQuestionGenerationView.as_view()name="generate-questions")path("ai-suggestions/"admin_views.AdminQuestionViewSet.as_view({"post":"ai_suggestions"})name="ai-suggestions")path("ai-variations/"admin_views.AdminQuestionViewSet.as_view({"post":"ai_variations"})name="ai-variations")path("ai-recommendations/"admin_views.AdminAssessmentViewSet.as_view({"post":"ai_recommendations"})name="ai-recommendations")path("ai/validate-question/<int:pk>/"admin_views.AdminQuestionViewSet.as_view({"post":"validate_question"})name="validate-question")#AIdecisionsmanagementpath("ai-decisions/"admin_views.AdminAIDecisionsView.as_view()name="ai-decisions")path("ai-decisions/process/"admin_views.AdminAIDecisionsView.as_view()name="process-ai-decision")path("ai-decisions/<int:decision_id>/"admin_views.AdminAIDecisionsView.as_view()name="ai-decision-detail")#Analyticsandreportingpath("analytics/questions/"admin_views.AdminQuestionViewSet.as_view({"get":"stats"})name="question-analytics")path("analytics/assessments/"admin_views.AdminAssessmentViewSet.as_view({"get":"statistics"})name="assessment-analytics")path("analytics/score-distribution/"admin_views.AdminAssessmentViewSet.as_view({"get":"score_distribution"})name="score-distribution-analytics")path("analytics/question-performance/"admin_views.AdminQuestionViewSet.as_view({"get":"performance"})name="question-performance-analytics")path("level-requirements/"admin_views.AdminAssessmentViewSet.as_view({"get":"level_requirements"})name="level-requirements")#Bulkoperationspath("questions/bulk-update/"admin_views.AdminQuestionViewSet.as_view({"post":"bulk_update"})name="bulk-update-questions")#Assessmentmanagementpath("assessments/<int:pk>/invalidate/"admin_views.AdminAssessmentViewSet.as_view({"post":"invalidate"})name="invalidate-assessment")#Keeponlyonerecent-assessmentsendpointforconsistencypath("assessments/<int:pk>/"admin_views.AdminAssessmentViewSet.as_view({"get":"retrieve"})name="assessment-detail")#Studentassessments-bothendpointsforcompatibilitypath("students/<int:student_id>/assessments/"admin_views.AdminAssessmentViewSet.as_view({"get":"student_assessments"})name="student-assessments")#Additionalendpointfordirectaccesstostudentassessmentspath("student-assessments/<int:pk>/"admin_views.StudentAssessmentAdminViewSet.as_view({"get":"retrieve"})name="student-assessment-detail")path("student-assessments/<int:pk>/results/"admin_views.StudentAssessmentAdminViewSet.as_view({"get":"get_results"})name="student-assessment-results")#Settingsmanagementpath("settings/difficulty/"admin_views.AssessmentSettingsViewSet.as_view({"patch":"update_difficulty"})name="update-difficulty")path("settings/bulk-create/"admin_views.AssessmentSettingsViewSet.as_view({"post":"bulk_create"})name="bulk-create-settings")]
import React, { Suspense } from 'react';
import { Box, Skeleton, Card, CardContent } from '@mui/material';

// Loading skeleton for course cards
const CourseCardSkeleton = () => (
  <Card sx={{ height: '100%', borderRadius: 3 }}>
    <CardContent>
      <Skeleton variant="text" width="60%" height={32} sx={{ mb: 1 }} />
      <Skeleton variant="text" width="40%" height={20} sx={{ mb: 2 }} />
      <Skeleton variant="rectangular" width="100%" height={80} sx={{ mb: 2, borderRadius: 2 }} />
      <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
        <Skeleton variant="rounded" width={80} height={24} />
        <Skeleton variant="rounded" width={60} height={24} />
      </Box>
      <Skeleton variant="rectangular" width="100%" height={36} sx={{ borderRadius: 2 }} />
    </CardContent>
  </Card>
);

// Loading skeleton for course lists
const CourseListSkeleton = () => (
  <Box sx={{ display: 'grid', gap: 3, gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))' }}>
    {Array.from({ length: 6 }).map((_, index) => (
      <CourseCardSkeleton key={index} />
    ))}
  </Box>
);

// Loading skeleton for course generators
const CourseGeneratorSkeleton = () => (
  <Card sx={{ p: 3 }}>
    <Skeleton variant="text" width="50%" height={40} sx={{ mb: 3 }} />
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Skeleton variant="rectangular" width="100%" height={56} sx={{ borderRadius: 2 }} />
      <Skeleton variant="rectangular" width="100%" height={120} sx={{ borderRadius: 2 }} />
      <Box sx={{ display: 'flex', gap: 2 }}>
        <Skeleton variant="rectangular" width="48%" height={56} sx={{ borderRadius: 2 }} />
        <Skeleton variant="rectangular" width="48%" height={56} sx={{ borderRadius: 2 }} />
      </Box>
      <Skeleton variant="rectangular" width="30%" height={40} sx={{ borderRadius: 2, alignSelf: 'flex-end' }} />
    </Box>
  </Card>
);

// Loading skeleton for material cards
const MaterialCardSkeleton = () => (
  <Card sx={{ mb: 2 }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
        <Skeleton variant="circular" width={40} height={40} />
        <Box sx={{ flex: 1 }}>
          <Skeleton variant="text" width="60%" height={24} />
          <Skeleton variant="text" width="40%" height={20} />
        </Box>
      </Box>
      <Skeleton variant="rectangular" width="100%" height={4} sx={{ borderRadius: 2 }} />
    </CardContent>
  </Card>
);

// Generic loading skeleton
const GenericSkeleton = () => (
  <Box sx={{ p: 3 }}>
    <Skeleton variant="text" width="40%" height={32} sx={{ mb: 2 }} />
    <Skeleton variant="rectangular" width="100%" height={200} sx={{ mb: 2, borderRadius: 2 }} />
    <Box sx={{ display: 'flex', gap: 1 }}>
      <Skeleton variant="rounded" width={100} height={36} />
      <Skeleton variant="rounded" width={80} height={36} />
    </Box>
  </Box>
);

// Loading skeleton for dashboards
const DashboardSkeleton = () => (
  <Box sx={{ p: 3 }}>
    <Skeleton variant="text" width="40%" height={40} sx={{ mb: 3 }} />
    <Box sx={{ display: 'grid', gap: 3, gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', mb: 3 }}>
      {Array.from({ length: 4 }).map((_, index) => (
        <Card key={index} sx={{ p: 2 }}>
          <Skeleton variant="text" width="60%" height={24} sx={{ mb: 1 }} />
          <Skeleton variant="rectangular" width="100%" height={120} sx={{ mb: 2 }} />
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Skeleton variant="rounded" width={80} height={24} />
            <Skeleton variant="rounded" width={60} height={24} />
          </Box>
        </Card>
      ))}
    </Box>
    <Box sx={{ display: 'flex', gap: 3 }}>
      <Skeleton variant="rectangular" width="70%" height={300} sx={{ borderRadius: 2 }} />
      <Skeleton variant="rectangular" width="30%" height={300} sx={{ borderRadius: 2 }} />
    </Box>
  </Box>
);

// Loading skeleton for assessments
const AssessmentSkeleton = () => (
  <Card sx={{ p: 3, mb: 3 }}>
    <Skeleton variant="text" width="60%" height={32} sx={{ mb: 2 }} />
    <Skeleton variant="text" width="40%" height={20} sx={{ mb: 3 }} />
    <Box sx={{ mb: 3 }}>
      <Skeleton variant="rectangular" width="100%" height={150} sx={{ borderRadius: 2, mb: 2 }} />
      <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
        {Array.from({ length: 4 }).map((_, index) => (
          <Skeleton key={index} variant="rounded" width="23%" height={40} />
        ))}
      </Box>
    </Box>
    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <Skeleton variant="rounded" width={120} height={36} />
      <Skeleton variant="rounded" width={80} height={36} />
    </Box>
  </Card>
);

// Loading skeleton for analytics
const AnalyticsSkeleton = () => (
  <Box sx={{ p: 3 }}>
    <Skeleton variant="text" width="50%" height={32} sx={{ mb: 3 }} />
    <Box sx={{ display: 'grid', gap: 3, gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', mb: 3 }}>
      {Array.from({ length: 3 }).map((_, index) => (
        <Card key={index} sx={{ p: 2, textAlign: 'center' }}>
          <Skeleton variant="circular" width={60} height={60} sx={{ mx: 'auto', mb: 1 }} />
          <Skeleton variant="text" width="80%" height={24} sx={{ mx: 'auto', mb: 1 }} />
          <Skeleton variant="text" width="60%" height={32} sx={{ mx: 'auto' }} />
        </Card>
      ))}
    </Box>
    <Box sx={{ display: 'grid', gap: 3, gridTemplateColumns: '1fr 1fr', mb: 3 }}>
      <Skeleton variant="rectangular" width="100%" height={250} sx={{ borderRadius: 2 }} />
      <Skeleton variant="rectangular" width="100%" height={250} sx={{ borderRadius: 2 }} />
    </Box>
    <Skeleton variant="rectangular" width="100%" height={300} sx={{ borderRadius: 2 }} />
  </Box>
);

// Mapping of component types to their loading skeletons
const skeletonMap = {
  courseCard: CourseCardSkeleton,
  courseList: CourseListSkeleton,
  courseGenerator: CourseGeneratorSkeleton,
  materialCard: MaterialCardSkeleton,
  dashboard: DashboardSkeleton,
  assessment: AssessmentSkeleton,
  analytics: AnalyticsSkeleton,
  generic: GenericSkeleton,
};

interface LazyLoadWrapperProps {
  children: React.ReactNode;
  fallback?: 'courseCard' | 'courseList' | 'courseGenerator' | 'materialCard' | 'dashboard' | 'assessment' | 'analytics' | 'generic';
  errorBoundary?: boolean;
}

// Error boundary for lazy-loaded components
class LazyLoadErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback: React.ComponentType },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; fallback: React.ComponentType }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy component loading error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback;
      return <FallbackComponent />;
    }

    return this.props.children;
  }
}

const LazyLoadWrapper: React.FC<LazyLoadWrapperProps> = ({
  children,
  fallback = 'generic',
  errorBoundary = true,
}) => {
  const SkeletonComponent = skeletonMap[fallback];

  const content = (
    <Suspense fallback={<SkeletonComponent />}>
      {children}
    </Suspense>
  );

  if (errorBoundary) {
    return (
      <LazyLoadErrorBoundary fallback={SkeletonComponent}>
        {content}
      </LazyLoadErrorBoundary>
    );
  }

  return content;
};

export default LazyLoadWrapper;
export { CourseCardSkeleton, CourseListSkeleton, CourseGeneratorSkeleton, MaterialCardSkeleton };

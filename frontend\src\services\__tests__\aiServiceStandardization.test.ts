/**
 * Frontend AI Service Standardization Tests
 *
 * Comprehensive tests to validate that all AI services follow standardized patterns
 * and work correctly with the new standardized approach.
 */

import {
  aiAssistantService,
  chatbotService,
  unifiedAiService,
  studyAssistantService,
  serviceRegistry,
  initializeAIServices,
  SERVICE_KEYS,
} from '../index';

import {
  AIServiceError,
  AIServiceConnectionError,
  AIServiceTimeoutError,
  AIServiceValidationError,
  createFallbackResponse,
  getErrorInfo,
  shouldUseFallback,
  getCacheStats,
  clearAIServiceCache,
} from '../utils/aiServiceUtils';

import { BaseAIService } from '../utils/BaseAIService';
import {
  ERROR_MESSAGES,
  ErrorSeverity,
  ErrorCategory,
  createUserErrorMessage,
  getRetryDelay,
} from '../utils/errorHandling';

import mockAxios from 'jest-mock-axios';

// Global axios mock is configured in setupTests.ts

// Mock console methods to avoid noise in tests
const originalConsole = console;
beforeAll(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
  console.group = jest.fn();
  console.groupEnd = jest.fn();
});

afterAll(() => {
  console.log = originalConsole.log;
  console.warn = originalConsole.warn;
  console.error = originalConsole.error;
  console.group = originalConsole.group;
  console.groupEnd = originalConsole.groupEnd;
});

describe('Frontend AI Service Standardization', () => {
  describe('Service Structure Validation', () => {
    test('All AI services should be instances of BaseAIService', () => {
      expect(aiAssistantService).toBeInstanceOf(BaseAIService);
      expect(chatbotService).toBeInstanceOf(BaseAIService);
      expect(unifiedAiService).toBeInstanceOf(BaseAIService);
      expect(studyAssistantService).toBeInstanceOf(BaseAIService);
    });

    test('All services should have required methods', () => {
      const services = [
        aiAssistantService,
        chatbotService,
        unifiedAiService,
        studyAssistantService,
      ];

      services.forEach(service => {
        expect(typeof service.getHealthStatus).toBe('function');
        expect(typeof service.updateConfig).toBe('function');
        expect(typeof service.getConfig).toBe('function');
        expect(typeof service.setFallbackEnabled).toBe('function');
        expect(typeof service.getServiceInfo).toBe('function');
      });
    });

    test('All services should have proper service info', () => {
      const services = [
        { service: aiAssistantService, expectedName: 'AI Assistant' },
        { service: chatbotService, expectedName: 'Chatbot' },
        { service: unifiedAiService, expectedName: 'Unified AI' },
        { service: studyAssistantService, expectedName: 'Study Assistant' },
      ];

      services.forEach(({ service, expectedName }) => {
        const info = service.getServiceInfo();
        expect(info.name).toBe(expectedName);
        expect(info.endpoint).toBeDefined();
        expect(info.config).toBeDefined();
      });
    });
  });

  describe('Error Handling Standardization', () => {
    test('AIServiceError should have proper structure', () => {
      const error = new AIServiceError('Test error', 'TEST_CODE', 500);

      expect(error.name).toBe('AIServiceError');
      expect(error.message).toBe('Test error');
      expect(error.code).toBe('TEST_CODE');
      expect(error.statusCode).toBe(500);
      expect(error instanceof Error).toBe(true);
    });

    test('AIServiceConnectionError should extend AIServiceError', () => {
      const error = new AIServiceConnectionError('Connection failed');

      expect(error.name).toBe('AIServiceError');
      expect(error.code).toBe('CONNECTION_ERROR');
      expect(error instanceof AIServiceError).toBe(true);
    });

    test('AIServiceTimeoutError should extend AIServiceError', () => {
      const error = new AIServiceTimeoutError('Request timed out');

      expect(error.name).toBe('AIServiceError');
      expect(error.code).toBe('TIMEOUT_ERROR');
      expect(error instanceof AIServiceError).toBe(true);
    });

    test('AIServiceValidationError should extend AIServiceError', () => {
      const error = new AIServiceValidationError('Invalid input');

      expect(error.name).toBe('AIServiceError');
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error instanceof AIServiceError).toBe(true);
    });

    test('getErrorInfo should return proper error information', () => {
      const error = new AIServiceError('Test error', 'SERVER_ERROR', 500);
      const errorInfo = getErrorInfo(error);

      expect(errorInfo.message).toBe('Test error');
      expect(errorInfo.category).toBe('server');
      expect(errorInfo.severity).toBe('high');
      expect(errorInfo.retryable).toBe(true);
      expect(errorInfo.reportable).toBe(true);
    });

    test('getErrorInfo should handle axios errors', () => {
      const axiosError = {
        response: {
          status: 404,
          data: { message: 'Not found' },
        },
      };

      const errorInfo = getErrorInfo(axiosError);
      expect(errorInfo.category).toBe('client');
      expect(errorInfo.severity).toBe('medium');
      expect(errorInfo.userMessage).toContain('not found');
    });

    test('getErrorInfo should handle network errors', () => {
      const networkError = {
        request: {},
        message: 'Network Error',
      };

      const errorInfo = getErrorInfo(networkError);
      expect(errorInfo.category).toBe('network');
      expect(errorInfo.severity).toBe('high');
      expect(errorInfo.retryable).toBe(true);
    });

    test('shouldUseFallback should work correctly', () => {
      const serverError = new AIServiceError(
        'Server error',
        'SERVER_ERROR',
        500
      );
      const validationError = new AIServiceError(
        'Validation error',
        'VALIDATION_ERROR',
        400
      );
      const networkError = new AIServiceConnectionError('Connection failed');

      expect(shouldUseFallback(serverError)).toBe(true);
      expect(shouldUseFallback(validationError)).toBe(false);
      expect(shouldUseFallback(networkError)).toBe(true);
    });

    test('createUserErrorMessage should generate user-friendly messages', () => {
      const error = new AIServiceError(
        'Internal server error',
        'SERVER_ERROR',
        500
      );
      const userMessage = createUserErrorMessage(error, 'Loading data');

      expect(userMessage).toContain('Loading data');
      expect(userMessage).toContain('try again');
      expect(userMessage).not.toContain('Internal server error');
    });

    test('getRetryDelay should calculate appropriate delays', () => {
      const serverError = new AIServiceError(
        'Server error',
        'SERVER_ERROR',
        500
      );
      const validationError = new AIServiceError(
        'Validation error',
        'VALIDATION_ERROR',
        400
      );

      const serverDelay = getRetryDelay(serverError, 1);
      const validationDelay = getRetryDelay(validationError, 1);

      expect(serverDelay).toBeGreaterThan(0);
      expect(validationDelay).toBe(0); // Non-retryable

      // Test exponential backoff
      const delay1 = getRetryDelay(serverError, 1);
      const delay2 = getRetryDelay(serverError, 2);
      expect(delay2).toBeGreaterThan(delay1);
    });
  });

  describe('Fallback Response Generation', () => {
    test('createFallbackResponse should generate proper fallback responses', () => {
      const generalFallback = createFallbackResponse('general');
      expect(generalFallback.status).toBe('fallback');
      expect(generalFallback.message).toBeDefined();

      const suggestionsFallback = createFallbackResponse('suggestions');
      expect(suggestionsFallback.status).toBe('fallback');
      expect(suggestionsFallback.suggestions).toBeDefined();
      expect(Array.isArray(suggestionsFallback.suggestions)).toBe(true);

      const contentFallback = createFallbackResponse('content');
      expect(contentFallback.status).toBe('fallback');
      expect(contentFallback.content).toBeDefined();

      const analysisFallback = createFallbackResponse('analysis');
      expect(analysisFallback.status).toBe('fallback');
      expect(analysisFallback.analysis).toBeDefined();
    });

    test('createFallbackResponse should merge custom data', () => {
      const customFallback = createFallbackResponse('general', {
        customField: 'test',
      });
      expect(customFallback.customField).toBe('test');
      expect(customFallback.status).toBe('fallback');
    });
  });

  describe('Caching Functionality', () => {
    test('getCacheStats should return cache statistics', () => {
      const stats = getCacheStats();
      expect(stats).toBeDefined();
      expect(typeof stats.totalEntries).toBe('number');
      expect(typeof stats.hitRate).toBe('number');
      expect(typeof stats.missRate).toBe('number');
      expect(Array.isArray(stats.services)).toBe(true);
    });

    test('clearAIServiceCache should clear cache', () => {
      // Clear all cache
      clearAIServiceCache();
      const stats = getCacheStats();
      expect(stats.totalEntries).toBe(0);
    });

    test('clearAIServiceCache should clear specific service cache', () => {
      clearAIServiceCache('ai_assistant');
      // Should not throw and should work silently
      expect(true).toBe(true);
    });
  });

  describe('Service Registry', () => {
    test('Service registry should be properly initialized', () => {
      expect(serviceRegistry).toBeDefined();
      expect(typeof serviceRegistry.register).toBe('function');
      expect(typeof serviceRegistry.get).toBe('function');
      expect(typeof serviceRegistry.getSystemHealth).toBe('function');
      expect(typeof serviceRegistry.checkServiceHealth).toBe('function');
      expect(typeof serviceRegistry.updateServiceConfig).toBe('function');
    });

    test('SERVICE_KEYS should be properly defined', () => {
      expect(SERVICE_KEYS.AI_ASSISTANT).toBe('ai_assistant');
      expect(SERVICE_KEYS.CHATBOT).toBe('chatbot');
      expect(SERVICE_KEYS.UNIFIED_AI).toBe('unified_ai');
      expect(SERVICE_KEYS.STUDY_ASSISTANT).toBe('study_assistant');
    });

    test('Service registry should track system health', () => {
      const systemHealth = serviceRegistry.getSystemHealth();

      expect(systemHealth.status).toBeDefined();
      expect(systemHealth.services).toBeDefined();
      expect(systemHealth.healthyCount).toBeDefined();
      expect(systemHealth.totalCount).toBeDefined();
      expect(
        ['healthy', 'degraded', 'unhealthy'].includes(systemHealth.status)
      ).toBe(true);
      expect(typeof systemHealth.healthyCount).toBe('number');
      expect(typeof systemHealth.totalCount).toBe('number');
    });

    test('Service registry should allow service registration', () => {
      const testService = new BaseAIService({
        serviceName: 'Test Registry Service',
        baseEndpoint: '/api/v1/test-registry',
      });

      serviceRegistry.register('test_service', testService);
      const retrievedService = serviceRegistry.get('test_service');

      expect(retrievedService).toBe(testService);
    });

    test('Service registry should provide service statistics', () => {
      const stats = serviceRegistry.getAllServiceStats();
      expect(typeof stats).toBe('object');

      // Should have stats for registered services
      Object.values(stats).forEach(serviceStat => {
        expect(serviceStat).toHaveProperty('name');
        expect(serviceStat).toHaveProperty('status');
        expect(serviceStat).toHaveProperty('errorCount');
        expect(serviceStat).toHaveProperty('config');
      });
    });

    test('initializeAIServices should not throw errors', async () => {
      await expect(initializeAIServices()).resolves.not.toThrow();
    });

    test('Service registry should handle configuration updates', () => {
      const newConfig = { timeout: 15000, retries: 4 };
      serviceRegistry.updateAllServicesConfig(newConfig);

      // Verify that services got updated
      const services = [
        aiAssistantService,
        chatbotService,
        unifiedAiService,
        studyAssistantService,
      ];
      services.forEach(service => {
        const config = service.getConfig();
        expect(config.timeout).toBe(15000);
        expect(config.retries).toBe(4);
      });
    });

    test('Service registry should handle fallback toggling', () => {
      serviceRegistry.setServiceFallback(SERVICE_KEYS.AI_ASSISTANT, false);
      expect(aiAssistantService.getConfig().fallbackEnabled).toBe(false);

      serviceRegistry.setServiceFallback(SERVICE_KEYS.AI_ASSISTANT, true);
      expect(aiAssistantService.getConfig().fallbackEnabled).toBe(true);
    });
  });

  describe('BaseAIService Functionality', () => {
    let testService: BaseAIService;

    beforeEach(() => {
      testService = new BaseAIService({
        serviceName: 'Test Service',
        baseEndpoint: '/api/v1/test',
        config: {
          timeout: 5000,
          retries: 2,
          fallbackEnabled: true,
          cacheEnabled: true,
          cacheTTL: 60000,
        },
      });
    });

    test('BaseAIService should initialize with correct configuration', () => {
      const config = testService.getConfig();
      expect(config.timeout).toBe(5000);
      expect(config.retries).toBe(2);
      expect(config.fallbackEnabled).toBe(true);
      expect(config.cacheEnabled).toBe(true);
      expect(config.cacheTTL).toBe(60000);
    });

    test('BaseAIService should provide service info', () => {
      const info = testService.getServiceInfo();
      expect(info.name).toBe('Test Service');
      expect(info.endpoint).toBe('/api/v1/test');
      expect(info.config).toBeDefined();
    });

    test('BaseAIService should allow configuration updates', () => {
      const newConfig = { timeout: 10000, retries: 5 };
      testService.updateConfig(newConfig);

      const updatedConfig = testService.getConfig();
      expect(updatedConfig.timeout).toBe(10000);
      expect(updatedConfig.retries).toBe(5);
      expect(updatedConfig.fallbackEnabled).toBe(true); // Should preserve other settings
    });

    test('BaseAIService should allow fallback toggle', () => {
      testService.setFallbackEnabled(false);
      expect(testService.getConfig().fallbackEnabled).toBe(false);

      testService.setFallbackEnabled(true);
      expect(testService.getConfig().fallbackEnabled).toBe(true);
    });

    test('BaseAIService should provide health status', async () => {
      const healthStatus = await testService.getHealthStatus();

      expect(healthStatus.service).toBe('Test Service');
      expect(healthStatus.timestamp).toBeDefined();
      expect(['healthy', 'unhealthy'].includes(healthStatus.status)).toBe(true);
    });
  });

  describe('Service Configuration', () => {
    test('All services should have default configuration', () => {
      const services = [
        aiAssistantService,
        chatbotService,
        unifiedAiService,
        studyAssistantService,
      ];

      services.forEach(service => {
        const config = service.getConfig();
        expect(config.timeout).toBeDefined();
        expect(config.retries).toBeDefined();
        expect(config.fallbackEnabled).toBeDefined();
        expect(typeof config.timeout).toBe('number');
        expect(typeof config.retries).toBe('number');
        expect(typeof config.fallbackEnabled).toBe('boolean');
      });
    });

    test('Service configuration should be updatable', () => {
      const testConfig = {
        timeout: 60000,
        retries: 3,
        fallbackEnabled: false,
      };

      aiAssistantService.updateConfig(testConfig);
      const updatedConfig = aiAssistantService.getConfig();

      expect(updatedConfig.timeout).toBe(60000);
      expect(updatedConfig.retries).toBe(3);
      expect(updatedConfig.fallbackEnabled).toBe(false);
    });

    test('Service configuration should have reasonable defaults', () => {
      const services = [
        { service: aiAssistantService, name: 'AI Assistant' },
        { service: chatbotService, name: 'Chatbot' },
        { service: unifiedAiService, name: 'Unified AI' },
        { service: studyAssistantService, name: 'Study Assistant' },
      ];

      services.forEach(({ service, name }) => {
        const config = service.getConfig();

        // Timeout should be reasonable (between 5s and 2 minutes)
        expect(config.timeout).toBeGreaterThanOrEqual(5000);
        expect(config.timeout).toBeLessThanOrEqual(120000);

        // Retries should be reasonable (0-5)
        expect(config.retries).toBeGreaterThanOrEqual(0);
        expect(config.retries).toBeLessThanOrEqual(5);

        // Fallback should be enabled by default for reliability
        expect(config.fallbackEnabled).toBe(true);
      });
    });
  });

  describe('Service Health Monitoring', () => {
    test('Services should have health status methods', async () => {
      const services = [
        aiAssistantService,
        chatbotService,
        unifiedAiService,
        studyAssistantService,
      ];

      for (const service of services) {
        const healthStatus = await service.getHealthStatus();
        expect(healthStatus).toBeDefined();
        expect(healthStatus.service).toBeDefined();
        expect(healthStatus.timestamp).toBeDefined();
        expect(['healthy', 'unhealthy'].includes(healthStatus.status)).toBe(
          true
        );
      }
    });

    test('Service registry should track system health', () => {
      const systemHealth = serviceRegistry.getSystemHealth();

      expect(systemHealth.status).toBeDefined();
      expect(systemHealth.services).toBeDefined();
      expect(systemHealth.healthyCount).toBeDefined();
      expect(systemHealth.totalCount).toBeDefined();
      expect(
        ['healthy', 'degraded', 'unhealthy'].includes(systemHealth.status)
      ).toBe(true);
    });
  });

  describe('Backward Compatibility', () => {
    test('Services should maintain their original interfaces', () => {
      // AI Assistant Service
      expect(typeof aiAssistantService.getSuggestions).toBe('function');
      expect(typeof aiAssistantService.askQuestion).toBe('function');
      expect(typeof aiAssistantService.getAnalytics).toBe('function');

      // Chatbot Service
      expect(typeof chatbotService.sendMessage).toBe('function');
      expect(typeof chatbotService.getConversations).toBe('function');
      expect(typeof chatbotService.getConversation).toBe('function');

      // Unified AI Service
      expect(typeof unifiedAiService.getCapabilities).toBe('function');
      expect(typeof unifiedAiService.getMLAnalytics).toBe('function');
      expect(typeof unifiedAiService.executeWorkflow).toBe('function');

      // Study Assistant Service
      expect(typeof studyAssistantService.getStudySessions).toBe('function');
      expect(typeof studyAssistantService.generateStudyPlan).toBe('function');
      expect(typeof studyAssistantService.generateFlashcards).toBe('function');
    });
  });

  describe('Code Duplication Elimination', () => {
    test('Services should not have duplicate error handling code', () => {
      // This test ensures that error handling is centralized
      // We check that services use the standardized base class
      const services = [
        aiAssistantService,
        chatbotService,
        unifiedAiService,
        studyAssistantService,
      ];

      services.forEach(service => {
        // All services should inherit from BaseAIService
        expect(service.constructor.name).not.toBe('Object');
        expect(service).toBeInstanceOf(BaseAIService);
      });
    });

    test('Common utilities should be available', () => {
      // Test that common utilities are properly exported and available
      expect(AIServiceError).toBeDefined();
      expect(createFallbackResponse).toBeDefined();
      expect(getErrorInfo).toBeDefined();
      expect(shouldUseFallback).toBeDefined();
    });
  });
});

describe('Integration Tests', () => {
  test('Service initialization should complete successfully', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

    await initializeAIServices();

    expect(consoleSpy).toHaveBeenCalledWith(
      expect.stringContaining('🚀 Initializing AI Services...')
    );

    consoleSpy.mockRestore();
  });

  test('All services should be accessible through registry', () => {
    Object.values(SERVICE_KEYS).forEach(key => {
      const service = serviceRegistry.get(key);
      expect(service).toBeDefined();
    });
  });
});

import React from 'react';
import { Box, useTheme, alpha, SxProps, Theme } from '@mui/material';
import { useRtl } from '../../../utils/rtlUtils';

interface ArabicPatternsProps {
  type?: 'zellige' | 'arabesque' | 'geometric' | 'floral' | 'stars' | 'custom';
  color?: string;
  opacity?: number;
  size?: number | string;
  position?:
    | 'top'
    | 'bottom'
    | 'left'
    | 'right'
    | 'top-left'
    | 'top-right'
    | 'bottom-left'
    | 'bottom-right'
    | 'center';
  rotate?: number;
  scale?: number;
  animate?: boolean;
  animationDuration?: number;
  animationDelay?: number;
  animationType?: 'rotate' | 'pulse' | 'float' | 'none';
  sx?: SxProps<Theme>;
}

const ArabicPatterns: React.FC<ArabicPatternsProps> = ({
  type = 'geometric',
  color,
  opacity = 0.1,
  size = 100,
  position = 'top-right',
  rotate = 0,
  scale = 1,
  animate = false,
  animationDuration = 10,
  animationDelay = 0,
  animationType = 'none',
  sx = {},
}) => {
  const theme = useTheme();
  const { isRtl } = useRtl();

  // Only apply patterns in RTL mode
  if (!isRtl) {
    return null;
  }

  // Set default color based on theme
  const patternColor = color || theme.palette.primary.main;

  // Get pattern SVG based on type
  const getPatternSvg = () => {
    switch (type) {
      case 'zellige':
        return `
          <svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
            <g fill="${patternColor}" fill-opacity="${opacity}">
              <path d="M50 0L0 50L50 100L100 50L50 0ZM50 25L75 50L50 75L25 50L50 25Z" />
              <path d="M50 33.33L66.67 50L50 66.67L33.33 50L50 33.33Z" />
            </g>
          </svg>
        `;

      case 'arabesque':
        return `
          <svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
            <g fill="${patternColor}" fill-opacity="${opacity}">
              <path d="M50 0C22.4 0 0 22.4 0 50C0 77.6 22.4 100 50 100C77.6 100 100 77.6 100 50C100 22.4 77.6 0 50 0ZM50 20C63.2 20 74 30.8 74 44C74 57.2 63.2 68 50 68C36.8 68 26 57.2 26 44C26 30.8 36.8 20 50 20Z" />
              <path d="M50 28C41.2 28 34 35.2 34 44C34 52.8 41.2 60 50 60C58.8 60 66 52.8 66 44C66 35.2 58.8 28 50 28ZM50 36C54.4 36 58 39.6 58 44C58 48.4 54.4 52 50 52C45.6 52 42 48.4 42 44C42 39.6 45.6 36 50 36Z" />
            </g>
          </svg>
        `;

      case 'geometric':
        return `
          <svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
            <g fill="${patternColor}" fill-opacity="${opacity}">
              <path d="M0 0H33.33V33.33H0V0ZM33.33 33.33H66.67V66.67H33.33V33.33ZM66.67 66.67H100V100H66.67V66.67ZM0 66.67H33.33V100H0V66.67ZM66.67 0H100V33.33H66.67V0Z" />
              <path d="M33.33 0V33.33H66.67V0H33.33ZM50 25C47.24 25 45 22.76 45 20C45 17.24 47.24 15 50 15C52.76 15 55 17.24 55 20C55 22.76 52.76 25 50 25Z" />
              <path d="M0 33.33V66.67H33.33V33.33H0ZM16.67 58.33C13.91 58.33 11.67 56.09 11.67 53.33C11.67 50.57 13.91 48.33 16.67 48.33C19.43 48.33 21.67 50.57 21.67 53.33C21.67 56.09 19.43 58.33 16.67 58.33Z" />
              <path d="M66.67 33.33V66.67H100V33.33H66.67ZM83.33 58.33C80.57 58.33 78.33 56.09 78.33 53.33C78.33 50.57 80.57 48.33 83.33 48.33C86.09 48.33 88.33 50.57 88.33 53.33C88.33 56.09 86.09 58.33 83.33 58.33Z" />
              <path d="M33.33 66.67V100H66.67V66.67H33.33ZM50 91.67C47.24 91.67 45 89.43 45 86.67C45 83.91 47.24 81.67 50 81.67C52.76 81.67 55 83.91 55 86.67C55 89.43 52.76 91.67 50 91.67Z" />
            </g>
          </svg>
        `;

      case 'floral':
        return `
          <svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
            <g fill="${patternColor}" fill-opacity="${opacity}">
              <path d="M50 0C47.5 15 40 25 25 27.5C10 30 0 40 0 50C0 60 10 70 25 72.5C40 75 47.5 85 50 100C52.5 85 60 75 75 72.5C90 70 100 60 100 50C100 40 90 30 75 27.5C60 25 52.5 15 50 0Z" />
              <circle cx="50" cy="50" r="10" />
              <path d="M25 0C23.75 7.5 20 12.5 12.5 13.75C5 15 0 20 0 25C0 30 5 35 12.5 36.25C20 37.5 23.75 42.5 25 50C26.25 42.5 30 37.5 37.5 36.25C45 35 50 30 50 25C50 20 45 15 37.5 13.75C30 12.5 26.25 7.5 25 0Z" />
              <path d="M75 0C73.75 7.5 70 12.5 62.5 13.75C55 15 50 20 50 25C50 30 55 35 62.5 36.25C70 37.5 73.75 42.5 75 50C76.25 42.5 80 37.5 87.5 36.25C95 35 100 30 100 25C100 20 95 15 87.5 13.75C80 12.5 76.25 7.5 75 0Z" />
              <path d="M25 50C23.75 57.5 20 62.5 12.5 63.75C5 65 0 70 0 75C0 80 5 85 12.5 86.25C20 87.5 23.75 92.5 25 100C26.25 92.5 30 87.5 37.5 86.25C45 85 50 80 50 75C50 70 45 65 37.5 63.75C30 62.5 26.25 57.5 25 50Z" />
              <path d="M75 50C73.75 57.5 70 62.5 62.5 63.75C55 65 50 70 50 75C50 80 55 85 62.5 86.25C70 87.5 73.75 92.5 75 100C76.25 92.5 80 87.5 87.5 86.25C95 85 100 80 100 75C100 70 95 65 87.5 63.75C80 62.5 76.25 57.5 75 50Z" />
            </g>
          </svg>
        `;

      case 'stars':
        return `
          <svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
            <g fill="${patternColor}" fill-opacity="${opacity}">
              <path d="M50 0L58.78 34.55H94.39L65.8 55.9L74.58 90.45L50 69.1L25.42 90.45L34.2 55.9L5.61 34.55H41.22L50 0Z" />
              <path d="M25 50L29.39 67.27H47.2L32.9 77.95L37.3 95.23L25 84.55L12.7 95.23L17.1 77.95L2.8 67.27H20.61L25 50Z" />
              <path d="M75 50L79.39 67.27H97.2L82.9 77.95L87.3 95.23L75 84.55L62.7 95.23L67.1 77.95L52.8 67.27H70.61L75 50Z" />
              <path d="M25 0L29.39 17.27H47.2L32.9 27.95L37.3 45.23L25 34.55L12.7 45.23L17.1 27.95L2.8 17.27H20.61L25 0Z" />
              <path d="M75 0L79.39 17.27H97.2L82.9 27.95L87.3 45.23L75 34.55L62.7 45.23L67.1 27.95L52.8 17.27H70.61L75 0Z" />
            </g>
          </svg>
        `;

      case 'custom':
      default:
        return `
          <svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
            <g fill="${patternColor}" fill-opacity="${opacity}">
              <circle cx="50" cy="50" r="50" />
            </g>
          </svg>
        `;
    }
  };

  // Convert SVG to data URL
  const svgToDataUrl = (svg: string) => {
    return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svg)}`;
  };

  // Get position styles
  const getPositionStyles = () => {
    switch (position) {
      case 'top':
        return { top: 0, left: '50%', transform: 'translateX(-50%)' };
      case 'bottom':
        return { bottom: 0, left: '50%', transform: 'translateX(-50%)' };
      case 'left':
        return { left: 0, top: '50%', transform: 'translateY(-50%)' };
      case 'right':
        return { right: 0, top: '50%', transform: 'translateY(-50%)' };
      case 'top-left':
        return { top: 0, left: 0 };
      case 'top-right':
        return { top: 0, right: 0 };
      case 'bottom-left':
        return { bottom: 0, left: 0 };
      case 'bottom-right':
        return { bottom: 0, right: 0 };
      case 'center':
      default:
        return { top: '50%', left: '50%', transform: 'translate(-50%, -50%)' };
    }
  };

  // Get animation styles
  const getAnimationStyles = () => {
    if (!animate || animationType === 'none') return {};

    const keyframes = {
      rotate: {
        from: { transform: `rotate(0deg) scale(${scale})` },
        to: { transform: `rotate(360deg) scale(${scale})` },
      },
      pulse: {
        '0%': { transform: `rotate(${rotate}deg) scale(${scale})` },
        '50%': { transform: `rotate(${rotate}deg) scale(${scale * 1.1})` },
        '100%': { transform: `rotate(${rotate}deg) scale(${scale})` },
      },
      float: {
        '0%': {
          transform: `rotate(${rotate}deg) scale(${scale}) translateY(0)`,
        },
        '50%': {
          transform: `rotate(${rotate}deg) scale(${scale}) translateY(-10px)`,
        },
        '100%': {
          transform: `rotate(${rotate}deg) scale(${scale}) translateY(0)`,
        },
      },
    };

    // Simplified animation without @keyframes to avoid Stylis parser issues
    return {
      transition: 'all 0.3s ease',
      // Remove complex animations to prevent Stylis parser errors
    };
  };

  // Combine all styles
  const combinedStyles = {
    position: 'absolute',
    width: size,
    height: size,
    backgroundImage: `url(${svgToDataUrl(getPatternSvg())})`,
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center',
    ...getPositionStyles(),
    ...(animate ? {} : { transform: `rotate(${rotate}deg) scale(${scale})` }),
    ...getAnimationStyles(),
    pointerEvents: 'none',
    zIndex: 0,
    ...sx,
  };

  return <Box sx={combinedStyles} />;
};

export default ArabicPatterns;

from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import (
    # admin_views,  # Temporarily disabled due to syntax errors
    level_requirement_views,
    level_views,
    score_distribution_views,
    views
)

app_name = "assessment"

router = DefaultRouter()
router.register(r"assessments", views.AssessmentViewSet, basename="assessment")
router.register(r"responses", views.AssessmentResponseViewSet, basename="assessment-response")

urlpatterns = [
    # Core assessment routes
    path("", include(router.urls)),
    
    # Student level routes
    path("student/level/", level_views.StudentLevelView.as_view(), name="student-level"),
    path("student/level/<int:student_id>/", level_views.StudentLevelView.as_view(), name="student-level-detail"),
    path("student/level/update/", level_views.StudentLevelView.as_view(), name="student-level-update"),
    path("student/level/<int:student_id>/update/", level_views.StudentLevelView.as_view(), name="student-level-update-with-id"),
    
    # Student progress routes
    path("student/progress/", views.StudentProgressView.as_view(), name="student-progress"),
    
    # Level requirements routes
    path("level-requirements/", level_requirement_views.LevelRequirementView.as_view(), name="level-requirements"),
    
    # Score distribution routes
    path("score-distribution/", score_distribution_views.ScoreDistributionView.as_view(), name="score-distribution"),
    
    # Admin routes - temporarily disabled
    # path("admin/", include("assessment.admin_urls")),
    
    # Public routes (no authentication required)
    # path("public/", include("assessment.public_urls")),
]

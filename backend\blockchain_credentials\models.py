"""
Blockchain Credentials Models

This module provides models for managing blockchain-based credentials,
certificates, and NFT-based achievements for the North Star University platform.
"""

import hashlib
import json
import uuid
from datetime import datetime, timedelta
from decimal import Decimal

from django.conf import settings
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError

from core.models import BaseModel


class BlockchainNetwork(BaseModel):
    """Model to represent different blockchain networks"""
    
    NETWORK_TYPES = [
        ('ETHEREUM', 'Ethereum'),
        ('POLYGON', 'Polygon'),
        ('BINANCE', 'Binance Smart Chain'),
        ('AVALANCHE', 'Avalanche'),
        ('SOLANA', 'Solana'),
        ('CARDANO', 'Cardano'),
        ('HYPERLEDGER', 'Hyperledger Fabric'),
        ('CUSTOM', 'Custom Network'),
    ]
    
    name = models.CharField(max_length=100, unique=True)
    network_type = models.CharField(max_length=20, choices=NETWORK_TYPES)
    chain_id = models.IntegerField(null=True, blank=True, help_text="Blockchain chain ID")
    rpc_url = models.URLField(help_text="RPC endpoint for blockchain interaction")
    explorer_url = models.URLField(help_text="Block explorer URL")
    
    # Network configuration
    is_testnet = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    gas_fee_estimate = models.DecimalField(
        max_digits=20, 
        decimal_places=8, 
        default=0.001,
        help_text="Estimated gas fee in native token"
    )
    
    # Smart contract addresses
    credential_contract_address = models.CharField(
        max_length=100, 
        blank=True,
        help_text="Address of the credential smart contract"
    )
    nft_contract_address = models.CharField(
        max_length=100, 
        blank=True,
        help_text="Address of the NFT achievement contract"
    )
    
    # API configuration
    api_key = models.CharField(max_length=200, blank=True, help_text="API key for network access")
    api_secret = models.CharField(max_length=200, blank=True, help_text="API secret (encrypted)")
    
    class Meta:
        verbose_name = "Blockchain Network"
        verbose_name_plural = "Blockchain Networks"
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({'Testnet' if self.is_testnet else 'Mainnet'})"


class CredentialTemplate(BaseModel):
    """Template for different types of credentials"""
    
    CREDENTIAL_TYPES = [
        ('COURSE_COMPLETION', 'Course Completion'),
        ('CERTIFICATION', 'Professional Certification'),
        ('SKILL_BADGE', 'Skill Badge'),
        ('ACHIEVEMENT', 'Achievement'),
        ('DEGREE', 'Degree'),
        ('DIPLOMA', 'Diploma'),
        ('MICRO_CREDENTIAL', 'Micro-Credential'),
        ('CONTINUING_EDUCATION', 'Continuing Education'),
    ]
    
    name = models.CharField(max_length=200)
    credential_type = models.CharField(max_length=30, choices=CREDENTIAL_TYPES)
    description = models.TextField()
    
    # Visual design
    template_design = models.JSONField(
        default=dict,
        help_text="JSON configuration for credential visual design"
    )
    image_template = models.ImageField(
        upload_to='credential_templates/',
        null=True,
        blank=True,
        help_text="Background image or template"
    )
    
    # Validation requirements
    required_fields = models.JSONField(
        default=list,
        help_text="List of required fields for this credential type"
    )
    verification_requirements = models.JSONField(
        default=dict,
        help_text="Requirements for credential verification"
    )
    
    # Blockchain configuration
    blockchain_enabled = models.BooleanField(default=True)
    default_network = models.ForeignKey(
        BlockchainNetwork,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    
    # Validity and expiration
    default_validity_period = models.DurationField(
        null=True,
        blank=True,
        help_text="Default validity period for this credential type"
    )
    is_renewable = models.BooleanField(default=False)
    
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "Credential Template"
        verbose_name_plural = "Credential Templates"
        ordering = ['credential_type', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.get_credential_type_display()})"


class BlockchainCredential(BaseModel):
    """Main model for blockchain-based credentials"""
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('MINTING', 'Minting on Blockchain'),
        ('ACTIVE', 'Active'),
        ('EXPIRED', 'Expired'),
        ('REVOKED', 'Revoked'),
        ('SUSPENDED', 'Suspended'),
        ('FAILED', 'Failed to Mint'),
    ]
    
    # Core identification
    credential_id = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    student = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='blockchain_credentials'
    )
    template = models.ForeignKey(CredentialTemplate, on_delete=models.CASCADE)
    
    # Credential content
    title = models.CharField(max_length=300)
    description = models.TextField()
    achievement_data = models.JSONField(
        default=dict,
        help_text="Structured data about the achievement"
    )
    
    # Academic references
    course = models.ForeignKey(
        'courses.Course',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='blockchain_credentials'
    )
    assessment = models.ForeignKey(
        'assessment.Assessment',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='blockchain_credentials'
    )
    final_grade = models.CharField(max_length=10, blank=True)
    grade_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    
    # Skills and competencies
    skills_acquired = models.JSONField(
        default=list,
        help_text="List of skills demonstrated"
    )
    competency_level = models.IntegerField(
        choices=[
            (1, 'Beginner'),
            (2, 'Elementary'),
            (3, 'Intermediate'),
            (4, 'Advanced'),
            (5, 'Expert')
        ],
        default=1
    )
    
    # Dates and validity
    issue_date = models.DateTimeField(auto_now_add=True)
    completion_date = models.DateTimeField()
    expiry_date = models.DateTimeField(null=True, blank=True)
    
    # Blockchain information
    blockchain_network = models.ForeignKey(
        BlockchainNetwork,
        on_delete=models.CASCADE,
        related_name='credentials'
    )
    transaction_hash = models.CharField(max_length=200, blank=True, unique=True)
    smart_contract_address = models.CharField(max_length=100, blank=True)
    token_id = models.CharField(max_length=100, blank=True)
    blockchain_hash = models.CharField(max_length=200, blank=True, unique=True)
    
    # Verification
    verification_url = models.URLField(blank=True)
    ipfs_hash = models.CharField(
        max_length=100, 
        blank=True,
        help_text="IPFS hash for decentralized metadata storage"
    )
    
    # Status and control
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    is_public = models.BooleanField(
        default=True,
        help_text="Whether this credential can be publicly verified"
    )
    is_transferable = models.BooleanField(
        default=False,
        help_text="Whether this credential can be transferred"
    )
    
    # Issuance information
    issued_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='issued_credentials'
    )
    issuer_signature = models.TextField(blank=True)
    
    # Additional metadata
    metadata = models.JSONField(
        default=dict,
        help_text="Additional credential metadata"
    )
    
    # Verification tracking
    verification_count = models.PositiveIntegerField(default=0)
    last_verified = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        verbose_name = "Blockchain Credential"
        verbose_name_plural = "Blockchain Credentials"
        ordering = ['-issue_date']
        indexes = [
            models.Index(fields=['student', 'status']),
            models.Index(fields=['blockchain_hash']),
            models.Index(fields=['transaction_hash']),
            models.Index(fields=['credential_id']),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.student.username} ({self.status})"
    
    def save(self, *args, **kwargs):
        # Generate blockchain hash if not exists
        if not self.blockchain_hash:
            self.blockchain_hash = self.generate_blockchain_hash()
        
        # Set verification URL
        if not self.verification_url and self.credential_id:
            self.verification_url = f"{settings.SITE_URL}/verify/{self.credential_id}"
        
        super().save(*args, **kwargs)
    
    def generate_blockchain_hash(self):
        """Generate a unique hash for blockchain verification"""
        data = {
            'credential_id': str(self.credential_id),
            'student_id': self.student.id,
            'course_id': self.course.id if self.course else None,
            'completion_date': self.completion_date.isoformat() if self.completion_date else None,
            'title': self.title,
            'skills': self.skills_acquired,
            'grade': self.final_grade
        }
        
        json_data = json.dumps(data, sort_keys=True)
        return hashlib.sha256(json_data.encode()).hexdigest()
    
    def is_valid(self):
        """Check if credential is currently valid"""
        if self.status != 'ACTIVE':
            return False
        
        if self.expiry_date and timezone.now() > self.expiry_date:
            return False
        
        return True
    
    def is_expired(self):
        """Check if credential has expired"""
        return self.expiry_date and timezone.now() > self.expiry_date
    
    def verify_on_blockchain(self):
        """Verify credential authenticity on blockchain"""
        # This would integrate with actual blockchain verification
        # For now, return mock verification
        if self.transaction_hash and self.blockchain_hash:
            return {
                'verified': True,
                'transaction_hash': self.transaction_hash,
                'blockchain_hash': self.blockchain_hash,
                'network': self.blockchain_network.name,
                'verification_time': timezone.now().isoformat()
            }
        return {'verified': False, 'reason': 'No blockchain data found'}
    
    def get_metadata_for_blockchain(self):
        """Get metadata formatted for blockchain storage"""
        return {
            'name': self.title,
            'description': self.description,
            'image': f"{settings.SITE_URL}/media/credentials/{self.credential_id}.png",
            'external_url': self.verification_url,
            'attributes': [
                {'trait_type': 'Institution', 'value': 'North Star University'},
                {'trait_type': 'Student', 'value': self.student.username},
                {'trait_type': 'Course', 'value': self.course.title if self.course else 'N/A'},
                {'trait_type': 'Grade', 'value': self.final_grade or 'N/A'},
                {'trait_type': 'Completion Date', 'value': self.completion_date.strftime('%Y-%m-%d')},
                {'trait_type': 'Competency Level', 'value': self.get_competency_level_display()},
            ],
            'skills': self.skills_acquired,
            'issue_date': self.issue_date.isoformat(),
            'credential_type': self.template.credential_type,
        }


class NFTAchievement(BaseModel):
    """Model for NFT-based achievements and badges"""
    
    ACHIEVEMENT_TYPES = [
        ('MILESTONE', 'Learning Milestone'),
        ('EXCELLENCE', 'Academic Excellence'),
        ('PARTICIPATION', 'Participation Award'),
        ('LEADERSHIP', 'Leadership Recognition'),
        ('INNOVATION', 'Innovation Award'),
        ('COMMUNITY', 'Community Contribution'),
        ('SKILL_MASTERY', 'Skill Mastery'),
        ('PERFECT_SCORE', 'Perfect Score'),
        ('STREAK', 'Learning Streak'),
        ('EARLY_BIRD', 'Early Adopter'),
    ]
    
    RARITY_LEVELS = [
        ('COMMON', 'Common'),
        ('UNCOMMON', 'Uncommon'),
        ('RARE', 'Rare'),
        ('EPIC', 'Epic'),
        ('LEGENDARY', 'Legendary'),
    ]
    
    achievement_id = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    student = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='nft_achievements'
    )
    
    # Achievement details
    title = models.CharField(max_length=200)
    description = models.TextField()
    achievement_type = models.CharField(max_length=20, choices=ACHIEVEMENT_TYPES)
    rarity = models.CharField(max_length=15, choices=RARITY_LEVELS, default='COMMON')
    
    # Visual representation
    image = models.ImageField(upload_to='nft_achievements/', null=True, blank=True)
    animation_url = models.URLField(blank=True, help_text="URL to animated version")
    
    # Achievement criteria
    criteria_met = models.JSONField(
        default=dict,
        help_text="Specific criteria that were met to earn this achievement"
    )
    earned_date = models.DateTimeField(auto_now_add=True)
    
    # Related academic data
    related_course = models.ForeignKey(
        'courses.Course',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    related_assessment = models.ForeignKey(
        'assessment.Assessment',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    
    # Blockchain NFT information
    blockchain_network = models.ForeignKey(
        BlockchainNetwork,
        on_delete=models.CASCADE,
        related_name='nft_achievements'
    )
    nft_contract_address = models.CharField(max_length=100, blank=True)
    token_id = models.CharField(max_length=100, blank=True, unique=True)
    transaction_hash = models.CharField(max_length=200, blank=True)
    
    # NFT metadata
    ipfs_metadata_hash = models.CharField(max_length=100, blank=True)
    opensea_url = models.URLField(blank=True)
    
    # Properties
    is_minted = models.BooleanField(default=False)
    is_transferable = models.BooleanField(default=False)
    mint_price = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        null=True,
        blank=True,
        help_text="Cost to mint this NFT"
    )
    
    # Gamification
    xp_reward = models.PositiveIntegerField(default=0, help_text="XP points awarded")
    badge_level = models.PositiveIntegerField(default=1)
    
    class Meta:
        verbose_name = "NFT Achievement"
        verbose_name_plural = "NFT Achievements"
        ordering = ['-earned_date']
        indexes = [
            models.Index(fields=['student', 'achievement_type']),
            models.Index(fields=['token_id']),
            models.Index(fields=['rarity', 'achievement_type']),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.student.username} ({self.rarity})"
    
    def get_nft_metadata(self):
        """Get NFT metadata in OpenSea standard format"""
        return {
            'name': self.title,
            'description': self.description,
            'image': f"{settings.SITE_URL}{self.image.url}" if self.image else '',
            'animation_url': self.animation_url,
            'external_url': f"{settings.SITE_URL}/achievements/{self.achievement_id}",
            'attributes': [
                {'trait_type': 'Achievement Type', 'value': self.get_achievement_type_display()},
                {'trait_type': 'Rarity', 'value': self.get_rarity_display()},
                {'trait_type': 'Earned Date', 'value': self.earned_date.strftime('%Y-%m-%d')},
                {'trait_type': 'Institution', 'value': 'North Star University'},
                {'trait_type': 'Badge Level', 'value': self.badge_level},
                {'trait_type': 'XP Reward', 'value': self.xp_reward},
            ],
            'properties': {
                'student': self.student.username,
                'course': self.related_course.title if self.related_course else None,
                'criteria': self.criteria_met,
            }
        }


class CredentialVerification(BaseModel):
    """Model to track credential verification attempts"""
    
    VERIFICATION_TYPES = [
        ('PUBLIC', 'Public Verification'),
        ('EMPLOYER', 'Employer Verification'),
        ('INSTITUTION', 'Institution Verification'),
        ('API', 'API Verification'),
        ('QR_CODE', 'QR Code Scan'),
    ]
    
    credential = models.ForeignKey(
        BlockchainCredential,
        on_delete=models.CASCADE,
        related_name='verifications'
    )
    verification_type = models.CharField(max_length=20, choices=VERIFICATION_TYPES)
    
    # Verifier information
    verifier_ip = models.GenericIPAddressField(null=True, blank=True)
    verifier_email = models.EmailField(blank=True)
    verifier_organization = models.CharField(max_length=200, blank=True)
    
    # Verification result
    is_verified = models.BooleanField(default=False)
    verification_data = models.JSONField(
        default=dict,
        help_text="Detailed verification response"
    )
    
    # Tracking
    verified_at = models.DateTimeField(auto_now_add=True)
    verification_duration = models.DurationField(null=True, blank=True)
    
    class Meta:
        verbose_name = "Credential Verification"
        verbose_name_plural = "Credential Verifications"
        ordering = ['-verified_at']
        indexes = [
            models.Index(fields=['credential', 'verification_type']),
            models.Index(fields=['verified_at']),
        ]
    
    def __str__(self):
        return f"Verification of {self.credential.title} ({self.verification_type})"


class SmartContract(BaseModel):
    """Model to manage smart contract deployments"""
    
    CONTRACT_TYPES = [
        ('CREDENTIAL', 'Credential Contract'),
        ('NFT', 'NFT Achievement Contract'),
        ('REGISTRY', 'Credential Registry'),
        ('VERIFICATION', 'Verification Contract'),
    ]
    
    name = models.CharField(max_length=100)
    contract_type = models.CharField(max_length=20, choices=CONTRACT_TYPES)
    blockchain_network = models.ForeignKey(BlockchainNetwork, on_delete=models.CASCADE)
    
    # Contract details
    contract_address = models.CharField(max_length=100, unique=True)
    deployment_transaction = models.CharField(max_length=200, blank=True)
    abi = models.JSONField(help_text="Contract Application Binary Interface")
    bytecode = models.TextField(blank=True)
    
    # Version and status
    version = models.CharField(max_length=20, default='1.0.0')
    is_active = models.BooleanField(default=True)
    deployment_date = models.DateTimeField(auto_now_add=True)
    
    # Gas and fees
    deployment_gas_used = models.BigIntegerField(null=True, blank=True)
    deployment_cost = models.DecimalField(
        max_digits=20,
        decimal_places=8,
        null=True,
        blank=True
    )
    
    # Permissions
    owner_address = models.CharField(max_length=100)
    admin_addresses = models.JSONField(
        default=list,
        help_text="List of admin wallet addresses"
    )
    
    class Meta:
        verbose_name = "Smart Contract"
        verbose_name_plural = "Smart Contracts"
        ordering = ['-deployment_date']
        unique_together = ['name', 'blockchain_network']
    
    def __str__(self):
        return f"{self.name} on {self.blockchain_network.name}"


class WalletAddress(BaseModel):
    """Model to store user wallet addresses for credential delivery"""
    
    WALLET_TYPES = [
        ('METAMASK', 'MetaMask'),
        ('WALLET_CONNECT', 'WalletConnect'),
        ('COINBASE', 'Coinbase Wallet'),
        ('TRUST', 'Trust Wallet'),
        ('LEDGER', 'Ledger Hardware'),
        ('TREZOR', 'Trezor Hardware'),
        ('CUSTODIAL', 'University Custodial'),
        ('OTHER', 'Other'),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='wallet_addresses'
    )
    blockchain_network = models.ForeignKey(BlockchainNetwork, on_delete=models.CASCADE)
    
    # Wallet information
    address = models.CharField(max_length=100)
    wallet_type = models.CharField(max_length=20, choices=WALLET_TYPES)
    label = models.CharField(max_length=100, blank=True, help_text="User-defined label")
    
    # Verification
    is_verified = models.BooleanField(default=False)
    verification_signature = models.TextField(blank=True)
    verified_at = models.DateTimeField(null=True, blank=True)
    
    # Status
    is_primary = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "Wallet Address"
        verbose_name_plural = "Wallet Addresses"
        unique_together = ['user', 'address', 'blockchain_network']
        ordering = ['-is_primary', '-created_at']
    
    def __str__(self):
        return f"{self.user.username} - {self.address[:10]}...{self.address[-6:]}"
    
    def save(self, *args, **kwargs):
        # Ensure only one primary wallet per network per user
        if self.is_primary:
            WalletAddress.objects.filter(
                user=self.user,
                blockchain_network=self.blockchain_network,
                is_primary=True
            ).exclude(pk=self.pk).update(is_primary=False)
        
        super().save(*args, **kwargs)

{"updateTitle": "لوحة تحكم إدارة مفتاح API", "explanation": "إدارة مفتاح Google Gemini API ومراقبة إحصائيات الاستخدام.", "modelConfigNote": "التكوين الحالي: استخدام", "model": "النموذج", "modelConfigExplanation": "جميع الصفحات ومكونات الخادم مكونة لاستخدام", "modelPerformance": "نموذج للأداء الأمثل. النموذج الاحتياطي:", "lastUpdated": "آخر تحديث:", "adminInfo": "معلومات المسؤول", "refresh": "تحديث", "currentStatus": "حالة مفتاح API الحالية", "monitor": "مراقبة وتنبيه", "fallbackMode": "النظام يعمل في الوضع الاحتياطي", "fallbackExplanation": "مفتاح API غير صالح أو منتهي الصلاحية، لكن النظام مكون للاستمرار في العمل في الوضع الاحتياطي. هذه ميزة تطوير ولا يجب استخدامها في الإنتاج. يرجى تحديث مفتاح API الخاص بك.", "status": "الحالة", "invalid": "غير صالح", "fallbackEnabled": "الوضع الاحتياطي مفعل", "keyPreview": "معاينة المفتاح", "message": "الرسالة", "errorType": "نوع الخطأ", "lastChecked": "آخر فحص", "tokenUsage": "استخدام الرموز (آخر 7 أيام)", "modelNote": "جميع الصفحات التي تستخدم", "modelSuffix": "نموذج", "noUsageData": "لا توجد بيانات استخدام الرموز متاحة", "modelDistribution": "توزيع استخدام النموذج", "primaryModel": "النموذج الأساسي:", "updateKey": "تحديث مفتاح API", "instructions": "كيفية الحصول على مفتاح API جديد", "instructionsDetail": "يمكنك الحصول على مفتاح API جديد من موقع Google AI Studio. اتبع هذه الخطوات:", "step1": "انتقل إلى Google AI Studio", "step2": "سجل الدخول بحساب Google الخاص بك", "step3": "انتقل إلى قسم مفاتيح API", "step4": "أنشئ مفتاح API جديد أو استخدم مفتاحاً موجوداً", "getNewKey": "احصل على مفتاح API جديد", "keyLabel": "مفتاح Gemini API", "keyPlaceholder": "أدخل مفتاح Gemini API الخاص بك", "keyHelperText": "سيتم تخزين مفتاح API بشكل آمن على الخادم", "updateButton": "تحديث مفتاح API", "recentRequests": "طلبات API الأخيرة", "noRequestsData": "لا توجد طلبات API حديثة متاحة", "monitoringTitle": "مراقبة مفتاح API", "monitoringNoResult": "لا توجد نتائج مراقبة متاحة.", "forceAlert": "فرض التنبيه", "close": "إغلاق", "errorTitle": "خطأ في مفتاح API", "errorMessage": "انتهت صلاحية مفتاح Google Gemini API أو أنه غير صالح. تحتاج إلى تحديثه لمواصلة استخدام ميزات الذكاء الاصطناعي.", "updateNow": "تحديث مفتاح API", "emptyKeyError": "ير<PERSON>ى إدخال مفتاح API صالح", "unexpectedError": "حد<PERSON> خطأ غير متوقع", "statusFetchFailed": "فشل في جلب حالة مفتاح API", "usageFetchFailed": "فشل في جلب بيانات استخدام الرموز", "modelFetchFailed": "فشل في جلب بيانات توزيع النموذج", "adminFetchFailed": "فشل في جلب معلومات المسؤول", "requestsFetchFailed": "فشل في جلب الطلبات الأخيرة", "configFetchFailed": "فشل في جلب تكوين النموذج", "monitoringSuccess": "تم إكمال مراقبة مفتاح API بنجاح", "monitoringError": "واجهت مراقبة مفتاح API خطأ", "monitoringFailed": "فشل في مراقبة مفتاح API", "updateSuccess": "تم تحديث مفتاح API بنجاح", "updateFailed": "فشل في تحديث مفتاح API", "monitoringDescription": "نتائج مراقبة مفتاح API:", "success": "نجح", "error": "خطأ", "alertSent": "تم إرسال التنبيه", "yes": "نعم", "no": "لا", "alertSentDescription": "تم إرسال بريد إلكتروني تنبيهي للمسؤولين.", "loadingConfig": "جاري تحميل تكوين النموذج...", "noConfigNote": "تكوين النموذج غير متاح", "noConfigExplanation": "<PERSON>ير قادر على جلب تكوين النموذج الحالي من الخادم.", "username": "اسم المستخدم", "role": "الدور", "lastLogin": "آخر تسجيل دخول", "totalRequests": "إجمالي طلبات API", "totalTokens": "إجمالي الرموز المستخدمة", "noAdminInfo": "لا توجد معلومات مسؤول متاحة", "valid": "صالح", "noStatus": "لا توجد معلومات حالة متاحة", "tokens": "الرموز", "requests": "الطلبات", "primaryModelDefault": "جاري التحميل...", "noModelData": "لا توجد بيانات توزيع النموذج متاحة", "date": "التاريخ", "modelName": "النموذج", "requestType": "نوع الطلب", "management": "إدارة مفتاح API"}
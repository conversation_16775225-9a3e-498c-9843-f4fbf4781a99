/**
 * API Response Types
 *
 * This file defines the standardized API response format used throughout the application.
 */

/**
 * Base API response structure
 */
export interface ApiResponse<T = any> {
  /** Whether the request was successful */
  success: boolean;
  /** Response data (present on success) */
  data?: T;
  /** Error message (present on failure) */
  message?: string;
  /** Additional error details (present on failure) */
  error?: any;
  /** HTTP status code */
  status?: number;
  /** Response timestamp */
  timestamp?: string;
}

/**
 * Successful API response
 */
export interface SuccessResponse<T = any> extends ApiResponse<T> {
  success: true;
  data: T;
  message?: string;
}

/**
 * Error API response
 */
export interface ErrorResponse extends ApiResponse {
  success: false;
  message: string;
  error?: any;
  data?: never;
}

/**
 * Paginated response data
 */
export interface PaginatedData<T = any> {
  /** Array of items */
  items: T[];
  /** Total number of items */
  total: number;
  /** Current page number (1-based) */
  page: number;
  /** Number of items per page */
  limit: number;
  /** Total number of pages */
  totalPages: number;
  /** Whether there is a next page */
  hasNext: boolean;
  /** Whether there is a previous page */
  hasPrev: boolean;
}

/**
 * Paginated API response
 */
export interface PaginatedResponse<T = any> extends SuccessResponse<PaginatedData<T>> {
  data: PaginatedData<T>;
}

/**
 * Type guard to check if response is successful
 */
export function isSuccessResponse<T = any>(
  response: ApiResponse<T>
): response is SuccessResponse<T> {
  return response.success === true && response.data !== undefined;
}

/**
 * Type guard to check if response is an error
 */
export function isErrorResponse(
  response: ApiResponse
): response is ErrorResponse {
  return response.success === false;
}

/**
 * Type guard to check if response is paginated
 */
export function isPaginatedResponse<T = any>(
  response: ApiResponse
): response is PaginatedResponse<T> {
  return (
    isSuccessResponse(response) &&
    typeof response.data === 'object' &&
    response.data !== null &&
    'items' in response.data &&
    'total' in response.data &&
    'page' in response.data &&
    'limit' in response.data
  );
}

/**
 * API request options
 */
export interface ApiRequestOptions {
  /** Request timeout in milliseconds */
  timeout?: number;
  /** Whether to include credentials */
  withCredentials?: boolean;
  /** Additional headers */
  headers?: Record<string, string>;
  /** Request parameters */
  params?: Record<string, any>;
  /** Whether to retry on failure */
  retry?: boolean;
  /** Number of retry attempts */
  retryAttempts?: number;
  /** Delay between retries in milliseconds */
  retryDelay?: number;
}

/**
 * API error types
 */
export enum ApiErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * Structured API error
 */
export interface ApiError {
  type: ApiErrorType;
  message: string;
  status?: number;
  code?: string;
  details?: any;
  timestamp: string;
}

/**
 * Validation error details
 */
export interface ValidationError {
  field: string;
  message: string;
  code?: string;
  value?: any;
}

/**
 * Validation error response
 */
export interface ValidationErrorResponse extends ErrorResponse {
  error: {
    type: 'VALIDATION_ERROR';
    details: ValidationError[];
  };
}

/**
 * File upload response
 */
export interface FileUploadResponse {
  /** Uploaded file URL */
  url: string;
  /** File name */
  filename: string;
  /** File size in bytes */
  size: number;
  /** MIME type */
  mimeType: string;
  /** Upload timestamp */
  uploadedAt: string;
}

/**
 * Bulk operation response
 */
export interface BulkOperationResponse<T = any> {
  /** Number of successful operations */
  successCount: number;
  /** Number of failed operations */
  failureCount: number;
  /** Total number of operations */
  totalCount: number;
  /** Successfully processed items */
  successes: T[];
  /** Failed items with error details */
  failures: Array<{
    item: T;
    error: string;
  }>;
}

/**
 * Health check response
 */
export interface HealthCheckResponse {
  /** Service status */
  status: 'healthy' | 'unhealthy' | 'degraded';
  /** Service version */
  version: string;
  /** Timestamp */
  timestamp: string;
  /** Additional service details */
  details?: Record<string, any>;
}

/**
 * API endpoint configuration
 */
export interface ApiEndpoint {
  /** HTTP method */
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  /** Endpoint path */
  path: string;
  /** Whether authentication is required */
  requiresAuth?: boolean;
  /** Request timeout override */
  timeout?: number;
  /** Whether to cache the response */
  cache?: boolean;
  /** Cache duration in milliseconds */
  cacheDuration?: number;
}

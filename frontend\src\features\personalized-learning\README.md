# Advanced Personalization System

## Overview

The Advanced Personalization System provides comprehensive, AI-powered personalization for the North Star University learning platform. It adapts content, interface, and interactions based on individual student needs, emotional states, learning preferences, and contextual factors.

## Features

### 🧠 Emotional Intelligence
- **Real-time emotion detection** from typing patterns and interaction behavior
- **Stress level monitoring** with adaptive content simplification
- **Confidence assessment** with targeted encouragement
- **Engagement tracking** with dynamic content gamification
- **Mood-based adaptations** for optimal learning states

### 🎨 Visual Personalization
- **Color scheme adaptation** (light, dark, high contrast, warm, cool, monochrome)
- **Font size customization** (0.8x to 2.0x multiplier)
- **Animation preferences** (none, static, subtle, dynamic)
- **Layout optimization** (linear, hierarchical, network, grid)
- **Accessibility enhancements** for visual impairments

### 🔊 Voice & Audio Personalization
- **Speech rate customization** (80-200 words per minute)
- **Voice type selection** (male, female, neutral)
- **Accent preferences** (US, UK, Australian, neutral)
- **Audio learning styles** (conversational, lecture, interactive)
- **Text-to-speech integration** with personalized settings

### ⏱️ Micro-Learning Optimization
- **Optimal chunk duration** (5-60 minutes based on attention span)
- **Break frequency management** (10-120 minutes)
- **Content density preferences** (light, medium, dense)
- **Chunking strategies** (time-based, concept-based, adaptive)

### 📱 Contextual Adaptation
- **Device optimization** (mobile, tablet, desktop)
- **Time-based adjustments** (available time, time of day)
- **Network quality adaptation** (high, medium, low bandwidth)
- **Environment considerations** (quiet, noisy, public, private)

## Architecture

### Core Services

#### PersonalizationService
Central orchestrator that coordinates all personalization engines:

```typescript
class PersonalizationService {
  async personalizeContent(content, userId, context, interactionHistory)
  applyVisualTheme(preferences)
  speakText(text, voicePreferences)
  getTTSOptions(preferences)
}
```

#### EmotionalIntelligenceService
Detects and adapts to emotional states:

```typescript
class EmotionalIntelligenceService {
  async detectEmotionalState(userId, interactionData)
  async adaptContentToEmotion(content, emotionalState)
  async getEmotionalSupport(emotionalState)
}
```

### Data Models

#### StudentProfile (Extended)
```typescript
interface StudentProfile {
  // Existing fields
  learning_style: LearningStyle;
  preferred_difficulty: DifficultyLevel;
  interests: InterestArea[];
  knowledge_gaps: string[];
  
  // New personalization fields
  emotional_state?: EmotionalLearningState;
  voice_preferences?: VoiceLearningPreferences;
  visual_preferences?: VisualLearningPreferences;
  micro_learning_preferences?: MicroLearningPreferences;
}
```

#### EmotionalLearningState
```typescript
interface EmotionalLearningState {
  current_mood: 'motivated' | 'frustrated' | 'confident' | 'anxious' | 'curious' | 'neutral';
  stress_level: number; // 1-10
  confidence_level: number; // 1-10
  engagement_level: number; // 1-10
  learning_readiness: number; // 1-10
  detected_at: string;
}
```

## Components

### EmotionalStateMonitor
Real-time emotional state tracking and visualization:

```tsx
<EmotionalStateMonitor
  userId={studentId}
  interactionHistory={interactions}
  onEmotionalStateChange={handleStateChange}
  compact={true}
/>
```

### Enhanced StudentProfileForm
Comprehensive preference management:

```tsx
<StudentProfileForm
  userId={userId}
  onSave={handleProfileSave}
/>
```

## Integration

### AITutor Integration
The AITutor component automatically applies personalization:

```typescript
// Detect emotional state
const emotionalState = await emotionalIntelligenceService.detectEmotionalState(
  userId, 
  interactionHistory
);

// Personalize AI response
const personalizedContent = await personalizationService.personalizeContent(
  aiResponse,
  userId,
  context,
  interactionHistory
);

// Apply voice synthesis if enabled
if (profile.voice_preferences?.voice_enabled) {
  personalizationService.speakText(content, profile.voice_preferences);
}
```

## API Endpoints

### Personalization Endpoints
```
POST /api/personalization/emotional-state/
GET  /api/personalization/voice-preferences/
PUT  /api/personalization/voice-preferences/
GET  /api/personalization/visual-preferences/
PUT  /api/personalization/visual-preferences/
POST /api/personalization/adapt-content/
GET  /api/personalization/health/
```

### Enhanced Profile Endpoints
```
GET  /api/course-generator/personalized/profiles/{userId}/
PUT  /api/course-generator/personalized/profiles/{userId}/
```

## Usage Examples

### Basic Personalization
```typescript
import { personalizationService } from '../services/personalizationService';

// Set user profile
personalizationService.setUserProfile(userId, studentProfile);

// Personalize content
const personalizedContent = await personalizationService.personalizeContent(
  originalContent,
  userId,
  {
    deviceType: 'mobile',
    timeOfDay: 14,
    availableTime: 20,
    networkQuality: 'high'
  },
  interactionHistory
);
```

### Emotional State Detection
```typescript
import emotionalIntelligenceService from '../services/emotionalIntelligenceService';

// Detect emotional state
const emotionalState = await emotionalIntelligenceService.detectEmotionalState(
  userId,
  interactionHistory
);

// Adapt content based on emotion
const adaptedContent = await emotionalIntelligenceService.adaptContentToEmotion(
  content,
  emotionalState
);
```

### Visual Theme Application
```typescript
// Apply visual preferences
personalizationService.applyVisualTheme({
  color_scheme: 'dark',
  font_size_multiplier: 1.2,
  animation_preference: 'subtle',
  layout_preference: 'grid'
});
```

### Voice Synthesis
```typescript
// Speak content with personalized voice
await personalizationService.speakText(
  "Hello! Welcome to your personalized learning experience.",
  {
    preferred_speech_rate: 150,
    preferred_voice_type: 'female',
    accent_preference: 'us',
    voice_enabled: true,
    volume_preference: 0.8
  }
);
```

## Testing

### Running Tests
```bash
npm test -- --testPathPattern=personalization
```

### Test Coverage
- PersonalizationService: Content adaptation, visual themes, voice synthesis
- EmotionalIntelligenceService: Emotion detection, content adaptation
- EmotionalStateMonitor: UI component rendering and interactions
- StudentProfileForm: Form validation and submission

## Performance Considerations

### Optimization Strategies
1. **Lazy Loading**: Personalization features load on-demand
2. **Caching**: User profiles cached for quick access
3. **Fallback Mechanisms**: Graceful degradation when services fail
4. **Debounced Updates**: Emotional state detection throttled to prevent overload

### Memory Management
- Interaction history limited to last 50 interactions
- Emotional state cache expires after 30 minutes
- Visual theme changes applied via CSS custom properties

## Accessibility

### WCAG Compliance
- High contrast mode support
- Font size scaling
- Reduced motion options
- Screen reader compatibility
- Keyboard navigation support

### Assistive Technology Integration
- ARIA labels for emotional state indicators
- Voice synthesis for content delivery
- Visual adaptation markers for screen readers

## Future Enhancements

### Planned Features
1. **Biometric Integration**: Heart rate and stress monitoring
2. **Advanced ML Models**: Deep learning for emotion detection
3. **Social Learning**: Peer compatibility matching
4. **Cultural Adaptation**: Content localization beyond language
5. **Predictive Personalization**: Anticipate user needs

### Research Areas
- Circadian rhythm optimization
- Cognitive load measurement
- Learning style validation
- Emotional intelligence accuracy
- Cross-cultural personalization

## Troubleshooting

### Common Issues

#### Personalization Not Applied
- Check if user profile exists
- Verify personalization service initialization
- Ensure interaction history is being tracked

#### Voice Synthesis Not Working
- Confirm browser supports Web Speech API
- Check voice preferences are enabled
- Verify audio permissions

#### Visual Theme Not Applied
- Check CSS custom properties support
- Verify theme markers in content
- Ensure document.documentElement access

### Debug Mode
Enable debug logging:
```typescript
localStorage.setItem('personalization-debug', 'true');
```

## Contributing

### Development Guidelines
1. Follow TypeScript strict mode
2. Write comprehensive tests
3. Document all public APIs
4. Consider accessibility in all features
5. Implement graceful fallbacks

### Code Style
- Use descriptive variable names
- Add JSDoc comments for complex functions
- Follow existing naming conventions
- Implement error boundaries

## License

This personalization system is part of the North Star University platform and follows the same licensing terms.

/**
 * Service Integration Tests
 *
 * Tests to ensure all AI services work together correctly
 * and the service registry manages them properly.
 */

import {
  aiAssistantService,
  chatbotService,
  unifiedAiService,
  studyAssistantService,
  serviceRegistry,
  initializeAIServices,
  SERVICE_KEYS,
  clearServicesCache,
  getServicesStats,
} from '../index';

import { BaseAIService } from '../utils/BaseAIService';
import { AIServiceError } from '../utils/aiServiceUtils';

// Mock axios
jest.mock('../../config/axios', () => ({
  default: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    patch: jest.fn(),
  },
}));

// Mock console methods
beforeAll(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
  console.group = jest.fn();
  console.groupEnd = jest.fn();
});

describe('Service Integration Tests', () => {
  describe('Service Registry Integration', () => {
    test('should register all services during initialization', async () => {
      await initializeAIServices();

      // Check that all services are registered
      Object.values(SERVICE_KEYS).forEach(key => {
        const service = serviceRegistry.get(key);
        expect(service).toBeDefined();
        expect(service).toBeInstanceOf(BaseAIService);
      });
    });

    test('should provide system health status', () => {
      const systemHealth = serviceRegistry.getSystemHealth();

      expect(systemHealth).toHaveProperty('status');
      expect(systemHealth).toHaveProperty('services');
      expect(systemHealth).toHaveProperty('healthyCount');
      expect(systemHealth).toHaveProperty('totalCount');

      expect(['healthy', 'degraded', 'unhealthy']).toContain(
        systemHealth.status
      );
      expect(typeof systemHealth.healthyCount).toBe('number');
      expect(typeof systemHealth.totalCount).toBe('number');
    });

    test('should allow bulk configuration updates', () => {
      const newConfig = {
        timeout: 50000,
        retries: 4,
        fallbackEnabled: false,
      };

      serviceRegistry.updateAllServicesConfig(newConfig);

      // Verify all services got the update
      const services = [
        aiAssistantService,
        chatbotService,
        unifiedAiService,
        studyAssistantService,
      ];
      services.forEach(service => {
        const config = service.getConfig();
        expect(config.timeout).toBe(50000);
        expect(config.retries).toBe(4);
        expect(config.fallbackEnabled).toBe(false);
      });
    });

    test('should track service errors', () => {
      const testError = new AIServiceError('Test error', 'TEST_ERROR', 500);

      serviceRegistry.recordError(SERVICE_KEYS.AI_ASSISTANT, testError);

      const stats = serviceRegistry.getServiceStats(SERVICE_KEYS.AI_ASSISTANT);
      expect(stats).toBeDefined();
      expect(stats!.errorCount).toBeGreaterThan(0);
    });

    test('should allow individual service fallback control', () => {
      // First, ensure we start from a known state - enable fallback for both
      serviceRegistry.setServiceFallback(SERVICE_KEYS.AI_ASSISTANT, true);
      serviceRegistry.setServiceFallback(SERVICE_KEYS.CHATBOT, false);
      
      // Verify initial state
      expect(aiAssistantService.getConfig().fallbackEnabled).toBe(true);
      expect(chatbotService.getConfig().fallbackEnabled).toBe(false);
      
      // Now test the actual functionality
      // Disable fallback for AI Assistant
      serviceRegistry.setServiceFallback(SERVICE_KEYS.AI_ASSISTANT, false);
      expect(aiAssistantService.getConfig().fallbackEnabled).toBe(false);

      // Enable fallback for Chatbot
      serviceRegistry.setServiceFallback(SERVICE_KEYS.CHATBOT, true);
      expect(chatbotService.getConfig().fallbackEnabled).toBe(true);
    });

    test('should provide comprehensive service statistics', () => {
      const allStats = serviceRegistry.getAllServiceStats();

      expect(typeof allStats).toBe('object');
      expect(Object.keys(allStats).length).toBeGreaterThan(0);

      Object.values(allStats).forEach(stats => {
        expect(stats).toHaveProperty('name');
        expect(stats).toHaveProperty('status');
        expect(stats).toHaveProperty('errorCount');
        expect(stats).toHaveProperty('config');
      });
    });
  });

  describe('Cross-Service Functionality', () => {
    test('should handle service dependencies gracefully', async () => {
      // Test scenario where one service depends on another
      // For example, AI Assistant might use Unified AI for analytics

      // Mock successful responses
      const mockAnalytics = { insights: ['test insight'] };
      const mockSuggestions = [{ id: 1, text: 'test suggestion' }];

      // This tests that services can work independently
      await expect(aiAssistantService.getSuggestions()).resolves.not.toThrow();
      await expect(unifiedAiService.getSystemHealth()).resolves.not.toThrow();
    });

    test('should maintain service isolation during errors', async () => {
      // If one service fails, others should continue working
      const services = [
        { service: aiAssistantService, method: 'getSuggestions' },
        { service: chatbotService, method: 'getConversations' },
        { service: unifiedAiService, method: 'getCapabilities' },
        { service: studyAssistantService, method: 'getStudyTopics' },
      ];

      // Test that each service handles its own errors
      for (const { service, method } of services) {
        await expect((service as any)[method]()).resolves.not.toThrow();
      }
    });

    test('should support concurrent service operations', async () => {
      // Test that multiple services can be called simultaneously
      const operations = [
        aiAssistantService.getSuggestions(),
        chatbotService.getConversations(),
        unifiedAiService.getCapabilities(),
        studyAssistantService.getStudyTopics(),
      ];

      await expect(Promise.allSettled(operations)).resolves.not.toThrow();
    });
  });

  describe('Cache Management Integration', () => {
    test('should clear cache for all services', () => {
      clearServicesCache();
      // Should not throw and should work silently
      expect(true).toBe(true);
    });

    test('should provide service statistics', () => {
      const stats = getServicesStats();
      expect(typeof stats).toBe('object');
    });
  });

  describe('Error Propagation and Fallbacks', () => {
    test('should handle cascading failures gracefully', async () => {
      // Simulate a scenario where multiple services fail
      const criticalError = new AIServiceError(
        'Critical system failure',
        'SERVER_ERROR',
        503
      );

      // Services should handle failures independently
      await expect(aiAssistantService.getSuggestions()).resolves.not.toThrow();
      await expect(chatbotService.getConversations()).resolves.not.toThrow();
    });

    test('should provide appropriate fallback responses', async () => {
      // Test that fallback responses are consistent across services
      const fallbackTests = [
        { service: aiAssistantService, method: 'getSuggestions' },
        { service: chatbotService, method: 'getConversations' },
        { service: unifiedAiService, method: 'getCapabilities' },
        { service: studyAssistantService, method: 'getStudyTopics' },
      ];

      for (const { service, method } of fallbackTests) {
        const result = await (service as any)[method]();
        expect(result).toBeDefined();
      }
    });
  });

  describe('Service Health Monitoring', () => {
    test('should perform health checks on all services', async () => {
      const healthResults = await serviceRegistry.checkAllServicesHealth();

      expect(typeof healthResults).toBe('object');
      Object.values(SERVICE_KEYS).forEach(key => {
        expect(healthResults).toHaveProperty(key);
        expect(typeof healthResults[key]).toBe('boolean');
      });
    });

    test('should track service recovery', async () => {
      // Simulate service failure and recovery
      const testError = new AIServiceError(
        'Temporary failure',
        'SERVER_ERROR',
        503
      );
      serviceRegistry.recordError(SERVICE_KEYS.AI_ASSISTANT, testError);

      // Check that error count increased
      let stats = serviceRegistry.getServiceStats(SERVICE_KEYS.AI_ASSISTANT);
      const initialErrorCount = stats!.errorCount;

      // Simulate recovery by performing successful health check
      await serviceRegistry.checkServiceHealth(SERVICE_KEYS.AI_ASSISTANT);

      // Error count should be reset on recovery
      stats = serviceRegistry.getServiceStats(SERVICE_KEYS.AI_ASSISTANT);
      expect(stats!.errorCount).toBeLessThanOrEqual(initialErrorCount);
    });
  });

  describe('Configuration Consistency', () => {
    test('should maintain configuration consistency across services', () => {
      const services = [
        aiAssistantService,
        chatbotService,
        unifiedAiService,
        studyAssistantService,
      ];

      services.forEach(service => {
        const config = service.getConfig();

        // All services should have required configuration properties
        expect(config).toHaveProperty('timeout');
        expect(config).toHaveProperty('retries');
        expect(config).toHaveProperty('fallbackEnabled');

        // Configuration values should be reasonable
        expect(config.timeout).toBeGreaterThan(0);
        expect(config.retries).toBeGreaterThanOrEqual(0);
        expect(typeof config.fallbackEnabled).toBe('boolean');
      });
    });

    test('should allow service-specific configuration overrides', () => {
      // Test that individual services can have different configurations
      const aiAssistantConfig = aiAssistantService.getConfig();
      const unifiedAiConfig = unifiedAiService.getConfig();

      // Unified AI should have longer timeout for ML operations
      expect(unifiedAiConfig.timeout).toBeGreaterThanOrEqual(
        aiAssistantConfig.timeout
      );
    });
  });

  describe('Service Lifecycle Management', () => {
    test('should handle service initialization properly', async () => {
      // Re-initialize services
      await initializeAIServices();

      // All services should be accessible
      Object.values(SERVICE_KEYS).forEach(key => {
        const service = serviceRegistry.get(key);
        expect(service).toBeDefined();
        expect(service).toBeInstanceOf(BaseAIService);
      });
    });

    test('should support service registry operations', () => {
      // Test registry operations
      const testService = new BaseAIService({
        serviceName: 'Test Integration Service',
        baseEndpoint: '/api/v1/test-integration',
      });

      serviceRegistry.register('test_integration', testService);

      const retrievedService = serviceRegistry.get('test_integration');
      expect(retrievedService).toBe(testService);

      // Test service statistics
      const stats = serviceRegistry.getServiceStats('test_integration');
      expect(stats).toBeDefined();
      expect(stats!.name).toBe('Test Integration Service');
    });
  });

  describe('Performance and Reliability', () => {
    test('should handle high-frequency service calls', async () => {
      // Test that services can handle multiple rapid calls
      const rapidCalls = Array(10)
        .fill(null)
        .map(() => aiAssistantService.getSuggestions());

      await expect(Promise.allSettled(rapidCalls)).resolves.not.toThrow();
    });

    test('should maintain service stability under load', async () => {
      // Simulate concurrent operations across all services
      const concurrentOperations = [
        ...Array(5)
          .fill(null)
          .map(() => aiAssistantService.getSuggestions()),
        ...Array(5)
          .fill(null)
          .map(() => chatbotService.getConversations()),
        ...Array(5)
          .fill(null)
          .map(() => unifiedAiService.getCapabilities()),
        ...Array(5)
          .fill(null)
          .map(() => studyAssistantService.getStudyTopics()),
      ];

      const results = await Promise.allSettled(concurrentOperations);

      // Most operations should succeed (allowing for some failures due to mocking)
      const successfulOperations = results.filter(
        result => result.status === 'fulfilled'
      );
      expect(successfulOperations.length).toBeGreaterThan(0);
    });
  });
});

[Unit]
Description=North Star University Celery Worker
After=network.target redis.service postgresql.service
Wants=redis.service postgresql.service

[Service]
Type=forking
User=www-data
Group=www-data
WorkingDirectory=/var/www/north-star-university/backend
Environment=DJANGO_SETTINGS_MODULE=settings.production
EnvironmentFile=/var/www/north-star-university/backend/.env.production
ExecStart=/var/www/north-star-university/venv/bin/celery multi start worker1 \
    -A university_management \
    --pidfile=/var/run/north-star-university/celery-%n.pid \
    --logfile=/var/log/north-star-university/celery-%n.log \
    --loglevel=info \
    --concurrency=2
ExecStop=/var/www/north-star-university/venv/bin/celery multi stopwait worker1 \
    --pidfile=/var/run/north-star-university/celery-%n.pid
ExecReload=/var/www/north-star-university/venv/bin/celery multi restart worker1 \
    -A university_management \
    --pidfile=/var/run/north-star-university/celery-%n.pid \
    --logfile=/var/log/north-star-university/celery-%n.log \
    --loglevel=info \
    --concurrency=2
Restart=on-failure
RestartSec=10

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/www/north-star-university/media
ReadWritePaths=/var/log/north-star-university
ReadWritePaths=/var/run/north-star-university

[Install]
WantedBy=multi-user.target

/**
 * Unified Type Definitions for North Star University
 * 
 * These types match the consolidated backend models and provide
 * a single source of truth for all course-related data structures.
 */

// Base types
export interface BaseModel {
  id: number;
  created_at: string;
  updated_at: string;
}

// Content provider types
export type CourseType = 'STANDARD' | 'GENERATED' | 'HYBRID';
export type ProviderType = 'STANDARD' | 'AI_GENERATED' | 'EXTERNAL' | 'HYBRID';
export type ProgressType = 'COURSE' | 'PATHWAY' | 'ASSESSMENT' | 'GENERATED';

// Unified Course Model (matches backend Course model)
export interface UnifiedCourse extends BaseModel {
  // Basic course information
  title: string;
  description: string;
  course_code: string;
  credits: number;
  semester: string;
  academic_year: string;
  
  // Level information
  required_level: number;
  recommended_level: number;
  
  // Course content and metadata
  learning_objectives: string;
  prerequisites_text: string;
  
  // Dates and capacity
  start_date?: string;
  end_date?: string;
  capacity: number;
  waitlist_capacity: number;
  max_students: number;
  
  // Status flags
  is_published: boolean;
  is_active: boolean;
  
  // Content type classification
  course_type: CourseType;
  
  // Integration flags
  has_ai_content: boolean;
  
  // Enhanced course metadata for different types
  enhanced_metadata: {
    ai_generation?: {
      difficulty_override?: string;
      content_format?: string;
      is_primary?: boolean;
      is_supplementary?: boolean;
    };
    [key: string]: any;
  };
  
  // Content provider configuration
  content_providers: Record<string, any>;
  
  // AI-specific fields (consolidated from course_generator)
  ai_generated_content: {
    weekly_schedule?: WeeklyScheduleItem[];
    lesson_plans?: LessonPlan[];
    assessment_methods?: AssessmentMethod[];
    recommended_readings?: RecommendedReading[];
    sample_quizzes?: SampleQuiz[];
    project_ideas?: ProjectIdea[];
    teaching_tips?: string[];
    skills_gained?: string[];
    additional_resources?: string[];
    textbooks?: Textbook[];
    generated_at?: string;
    created_by_id?: number;
    ai_options?: Record<string, any>;
    provider_type?: ProviderType;
    provider_info?: string;
    provider_name?: string;
    provider_description?: string;
    provider_version?: string;
  };
  
  
  // Gamification settings
  gamification_enabled: boolean;
  gamification_config: {
    badges_enabled?: boolean;
    leaderboard_enabled?: boolean;
    achievements_enabled?: boolean;
    difficulty_levels?: any[];
    learning_paths?: any[];
    engagement_metrics?: Record<string, any>;
  };
  
  // Relations (when populated)
  created_by?: User;
  instructor?: User;
  department?: Department;
  content_provider_instances?: CourseContentProvider[];
}

// Content Provider Model (matches backend CourseContentProvider)
export interface CourseContentProvider extends BaseModel {
  course: number;
  provider_type: ProviderType;
  provider_name: string;
  provider_version: string;
  provider_config: Record<string, any>;
  content_data: Record<string, any>;
  is_active: boolean;
  is_primary: boolean;
  priority: number;
}

// Unified Student Level Model (matches backend StudentLevel)
export interface UnifiedStudentLevel extends BaseModel {
  student: number;
  current_level: number;
  previous_level: number;
  level_updated_at: string;
  level_updated_by?: number;
  level_update_reason: string;
  assessment_id?: number;
  level_history: LevelHistoryEntry[];
  
  // Enhanced level tracking
  level_by_provider: Record<string, number>;
  detailed_progression: Record<string, any>;
  assessment_history: AssessmentHistoryEntry[];
  
  // Relations
  student_user?: User;
  level_updated_by_user?: User;
}

export interface LevelHistoryEntry {
  previous_level: number;
  new_level: number;
  date: string;
  reason: string;
  assessment_id?: number;
  updated_by?: number;
}

export interface AssessmentHistoryEntry {
  assessment_id: number;
  date: string;
  level_before: number;
  level_after: number;
  provider_type: string;
  reason: string;
}

// Unified Progress Model (matches backend UserProgress)
export interface UnifiedProgress extends BaseModel {
  user: number;
  content_reference: number;
  progress_type: ProgressType;
  started_at: string;
  last_activity: string;
  completion_percentage: number;
  is_completed: boolean;
  completed_at?: string;
  
  // Enhanced progress tracking
  provider_progress: Record<string, any>;
  progress_data: Record<string, any>;
  detailed_metrics: Record<string, any>;
  milestones_completed: any[];
  
  // Relations
  user_details?: User;
  content_reference_details?: ContentReference;
}

// Content Reference Model (matches backend ContentReference)
export interface ContentReference extends BaseModel {
  content_type: string;
  object_id: number;
  content_category: string;
  display_name: string;
}

// Supporting types for course content
export interface WeeklyScheduleItem {
  week: number;
  topic: string;
  description: string;
  activities?: string[];
}

export interface LessonPlan {
  title: string;
  objectives: string[];
  content: string;
  activities: string[];
  assessment: string;
}

export interface AssessmentMethod {
  type: string;
  description: string;
  weight: number;
}

export interface RecommendedReading {
  title: string;
  author?: string;
  description?: string;
  url?: string;
}

export interface SampleQuiz {
  title: string;
  questions: QuizQuestion[];
}

export interface QuizQuestion {
  question: string;
  options?: string[];
  correct_answer?: string;
  explanation?: string;
}

export interface ProjectIdea {
  title: string;
  description: string;
  learning_outcomes: string[];
  difficulty: string;
}

export interface Textbook {
  id: number;
  title: string;
  author: string;
  isbn?: string;
  publisher?: string;
  year?: number;
  url?: string;
}

// User and Department types
export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role?: string;
  is_active?: boolean;
}

export interface Department {
  id: number;
  name: string;
  code: string;
  description?: string;
}

// Skill types (matches backend Skill model)
export interface Skill extends BaseModel {
  name: string;
  description: string;
  level: number;
  parent_skill?: number;
  parent_skill_details?: Skill;
  child_skills?: Skill[];
}

export interface StudentSkillProgress extends BaseModel {
  student: number;
  skill: number;
  content_type?: string;
  object_id?: number;
  proficiency_level: number;
  last_assessed: string;
  current_crown_level: number;
  lessons_completed: number;
  total_xp: number;
  is_unlocked: boolean;
  completed_at?: string;
  last_practiced_at?: string;
  strength: number;
  next_review_date?: string;
  
  // Relations
  student_details?: User;
  skill_details?: Skill;
}

// API Response types
export interface UnifiedApiResponse<T> {
  status: 'success' | 'error';
  message?: string;
  data: T;
  meta?: {
    total?: number;
    page?: number;
    page_size?: number;
    total_pages?: number;
  };
  errors?: Record<string, string[]>;
}

// Course content types for rendering
export interface CourseContent {
  id: number | string;
  title: string;
  description?: string;
  content_type: string;
  provider: ProviderType;
  content?: any;
  file?: string;
  url?: string;
  order?: number;
  is_completed?: boolean;
  created_at?: string;
  updated_at?: string;
}


// Assessment types
export interface Assessment extends BaseModel {
  title: string;
  description: string;
  assessment_type: 'PLACEMENT' | 'COURSE' | 'MILESTONE' | 'QUIZ' | 'EXAM' | 'PRACTICE';
  student: number;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'EXPIRED';
  score?: number;
  start_time?: string;
  end_time?: string;
  completed: boolean;
  skill_scores: Record<string, any>;
  feedback: Record<string, any>;
  is_active: boolean;
  is_adaptive: boolean;
  
  // Enhanced fields
  adaptive_progression: Record<string, any>;
  current_difficulty_level: number;
  learning_path: string;
  detailed_results: Record<string, any>;
  initial_level?: number;
  final_level?: number;
  level_changed: boolean;
  time_spent?: number;
}

// Filter and query types
export interface CourseFilters {
  course_type?: CourseType;
  has_ai_content?: boolean;
  required_level?: number;
  semester?: string;
  is_active?: boolean;
  search?: string;
  department?: number;
  instructor?: number;
}

export interface ProgressFilters {
  progress_type?: ProgressType;
  is_completed?: boolean;
  user?: number;
  course?: number;
  date_range?: {
    start: string;
    end: string;
  };
}

// Form types for creating/updating
export interface CourseFormData {
  title: string;
  description: string;
  course_code: string;
  credits: number;
  semester: string;
  academic_year: string;
  required_level: number;
  recommended_level: number;
  learning_objectives: string;
  prerequisites_text: string;
  capacity: number;
  waitlist_capacity: number;
  max_students: number;
  course_type: CourseType;
  has_ai_content: boolean;
  gamification_enabled: boolean;
  is_published: boolean;
  is_active: boolean;
}

// Component prop types
export interface CourseCardProps {
  course: UnifiedCourse;
  showProgress?: boolean;
  showActions?: boolean;
  onEnroll?: (courseId: number) => void;
  onView?: (courseId: number) => void;
  onEdit?: (courseId: number) => void;
}

export interface CourseDetailProps {
  courseId: number;
  showAllContent?: boolean;
  enabledProviders?: ProviderType[];
  defaultProvider?: ProviderType;
}

export interface ProgressTrackerProps {
  userId: number;
  courseId?: number;
  progressType?: ProgressType;
  showDetailed?: boolean;
}

// State management types
export interface UnifiedCourseState {
  courses: UnifiedCourse[];
  currentCourse: UnifiedCourse | null;
  courseContent: CourseContent[];
  contentProviders: CourseContentProvider[];
  loading: boolean;
  error: string | null;
  filters: CourseFilters;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

export interface UnifiedProgressState {
  userProgress: UnifiedProgress[];
  currentProgress: UnifiedProgress | null;
  studentLevel: UnifiedStudentLevel | null;
  skillProgress: StudentSkillProgress[];
  loading: boolean;
  error: string | null;
}

export interface UnifiedAssessmentState {
  assessments: Assessment[];
  currentAssessment: Assessment | null;
  assessmentHistory: Assessment[];
  loading: boolean;
  error: string | null;
}

// Assignment and submission types (unified)
export interface UnifiedAssignment extends BaseModel {
  title: string;
  description: string;
  due_date: string;
  points: number;
  file_attachment?: string;
  course: number;
  
  // Enhanced assignment metadata
  assignment_type: 'HOMEWORK' | 'PROJECT' | 'QUIZ' | 'EXAM' | 'DISCUSSION' | 'LAB';
  is_published: boolean;
  is_group_assignment: boolean;
  allow_late_submission: boolean;
  late_penalty_percentage?: number;
  max_attempts?: number;
  
  // Grading configuration
  grading_type: 'POINTS' | 'PERCENTAGE' | 'LETTER' | 'PASS_FAIL';
  auto_grade_enabled: boolean;
  rubric_data?: Record<string, any>;
  
  // Submission settings
  submission_types: ('FILE' | 'TEXT' | 'URL' | 'MEDIA')[];
  allowed_file_types?: string[];
  max_file_size_mb?: number;
  
  // Relations
  course_details?: UnifiedCourse;
  submissions?: UnifiedSubmission[];
  created_by?: User;
}

export interface UnifiedStudent extends BaseModel {
  user: number;
  student_id: string;
  enrollment_date: string;
  graduation_date?: string;
  academic_status: 'ACTIVE' | 'INACTIVE' | 'GRADUATED' | 'DROPPED' | 'SUSPENDED';
  gpa?: number;
  total_credits: number;
  
  // Enhanced student information
  major?: string;
  minor?: string;
  advisor_id?: number;
  emergency_contact?: Record<string, any>;
  
  // Relations
  user_details?: User;
  advisor?: User;
  enrollments?: CourseEnrollment[];
  student_level?: UnifiedStudentLevel;
}

export interface UnifiedSubmission extends BaseModel {
  assignment: number;
  student: number;
  submitted_at?: string;
  is_late: boolean;
  attempt_number: number;
  
  // Submission content
  submission_text?: string;
  file_attachment?: string;
  submission_url?: string;
  submission_data?: Record<string, any>;
  
  // Status and feedback
  status: 'DRAFT' | 'SUBMITTED' | 'GRADED' | 'RETURNED' | 'RESUBMITTED';
  student_comments?: string;
  instructor_feedback?: string;
  
  // Grading information
  score?: number;
  grade?: string;
  graded_at?: string;
  graded_by?: number;
  
  // Enhanced metadata
  submission_metadata: Record<string, any>;
  plagiarism_score?: number;
  auto_grade_score?: number;
  manual_grade_override?: number;
  
  // Relations
  assignment_details?: UnifiedAssignment;
  student_details?: UnifiedStudent;
  graded_by_user?: User;
}

export interface UnifiedGrade extends BaseModel {
  submission: number;
  score: number;
  max_score: number;
  percentage: number;
  letter_grade?: string;
  
  // Grading details
  grading_criteria: Record<string, any>;
  rubric_scores?: Record<string, number>;
  feedback: string;
  private_feedback?: string;
  
  // Metadata
  graded_at: string;
  graded_by: number;
  is_final: boolean;
  grade_type: 'AUTO' | 'MANUAL' | 'HYBRID';
  
  // Enhanced grading information
  grade_breakdown: Record<string, any>;
  improvement_suggestions?: string[];
  strengths?: string[];
  areas_for_improvement?: string[];
  
  // Relations
  submission_details?: UnifiedSubmission;
  graded_by_user?: User;
}

// Course enrollment types
export interface CourseEnrollment extends BaseModel {
  student: number;
  course: number;
  enrollment_date: string;
  enrollment_status: 'ENROLLED' | 'WAITLISTED' | 'DROPPED' | 'COMPLETED' | 'FAILED';
  final_grade?: string;
  grade_points?: number;
  
  // Enhanced enrollment data
  enrollment_type: 'REGULAR' | 'AUDIT' | 'CREDIT_NO_CREDIT';
  credits_earned?: number;
  attendance_percentage?: number;
  participation_score?: number;
  
  // Relations
  student_details?: UnifiedStudent;
  course_details?: UnifiedCourse;
}

// Assignment form and state types
export interface AssignmentFormData {
  title: string;
  description: string;
  due_date: string;
  points: number;
  file_attachment?: File;
  assignment_type: UnifiedAssignment['assignment_type'];
  is_published: boolean;
  is_group_assignment: boolean;
  allow_late_submission: boolean;
  late_penalty_percentage?: number;
  max_attempts?: number;
  grading_type: UnifiedAssignment['grading_type'];
  auto_grade_enabled: boolean;
  submission_types: UnifiedAssignment['submission_types'];
  allowed_file_types?: string[];
  max_file_size_mb?: number;
}

export interface SubmissionFormData {
  submission_text?: string;
  file_attachment?: File;
  submission_url?: string;
  student_comments?: string;
}

export interface GradeFormData {
  score: number;
  feedback: string;
  private_feedback?: string;
  rubric_scores?: Record<string, number>;
  improvement_suggestions?: string[];
  strengths?: string[];
  areas_for_improvement?: string[];
}

// State management for assignments
export interface UnifiedAssignmentState {
  assignments: UnifiedAssignment[];
  currentAssignment: UnifiedAssignment | null;
  submissions: UnifiedSubmission[];
  grades: UnifiedGrade[];
  loading: boolean;
  error: string | null;
  filters: {
    course?: number;
    assignment_type?: UnifiedAssignment['assignment_type'];
    is_published?: boolean;
    due_date_range?: {
      start: string;
      end: string;
    };
  };
}

// Study Time and Analytics Types
export interface UnifiedStudySession {
  id: number;
  student: number;
  course: number;
  start_time: string;
  end_time?: string;
  duration_minutes?: number;
  activity_type: 'reading' | 'video' | 'practice' | 'assignment' | 'quiz' | 'discussion' | 'other';
  content_id?: number;
  notes?: string;
  effectiveness_rating?: number; // 1-5 scale
  created_at: string;
  updated_at: string;
}

export interface UnifiedStudyGoal {
  id: number;
  student: number;
  course?: number;
  title: string;
  description?: string;
  goal_type: 'daily_hours' | 'weekly_hours' | 'course_completion' | 'grade_target' | 'skill_mastery' | 'custom';
  target_value: number;
  current_progress: number;
  target_date?: string;
  is_active: boolean;
  priority: 'low' | 'medium' | 'high';
  created_at: string;
  updated_at: string;
}

export interface UnifiedStudyAnalytics {
  id: number;
  student: number;
  course?: number;
  period_start: string;
  period_end: string;
  total_study_time_minutes: number;
  avg_session_duration_minutes: number;
  most_productive_time?: string;
  activity_breakdown: Record<UnifiedStudySession['activity_type'], number>;
  course_progress_percentage?: number;
  goal_completion_rate?: number;
  effectiveness_score?: number;
  recommendations?: string[];
  generated_at: string;
}

// Study Time State Management
export interface UnifiedStudyTimeState {
  sessions: UnifiedStudySession[];
  goals: UnifiedStudyGoal[];
  analytics: UnifiedStudyAnalytics[];
  currentSession: UnifiedStudySession | null;
  activeSession: UnifiedStudySession | null;
  loading: boolean;
  error: string | null;
  filters: {
    course?: number;
    date_range?: {
      start: string;
      end: string;
    };
    activity_type?: UnifiedStudySession['activity_type'];
  };
}

// Utility types
export type LoadingState = 'idle' | 'loading' | 'succeeded' | 'failed';

export interface AsyncState<T> {
  data: T | null;
  loading: LoadingState;
  error: string | null;
  lastUpdated?: string;
}

"""AIMonitoringDashboardViewsProvidescomprehensivedashboardviewsforAIservicemonitoringandanalytics."""fromrest_frameworkimportstatusfromrest_framework.decoratorsimportapi_viewpermission_classesfromrest_framework.permissionsimportIsAuthenticatedIsAdminUserfromrest_framework.responseimportResponsefrom django.utils.decoratorsimportmethod_decoratorfrom django.views.decorators.cacheimportcache_pagefrom django.db.modelsimportCountAvgSumMaxMinQfrom django.db.models.functionsimportTruncHourTruncDayfrom django.utilsimport timezonefromdatetimeimport timedeltaimport loggingfrom.ai_service_improvedimportimproved_ai_servicefrom.ai_modelsimportAIServiceMetricAIRequestAIUsageStatisticsAIConfigurationlogger=logging.getLogger(__name__)@api_view(['GET'])@permission_classes([IsAdminUser])@method_decorator(cache_page(60))#Cachefor1minutedefdashboard_overview(request):"""Getcomprehensivedashboardoverview"""try:#Gettimerangesnow=timezone.now()last_hour=now-timedelta(hours=1)last_24h=now-timedelta(hours=24)last_7d=now-timedelta(days=7)last_30d=now-timedelta(days=30)#Currenthealthstatushealth_status=improved_ai_service.get_health_status()#Quickstatsforlast24hoursmetrics_24h=AIServiceMetric.objects.filter(timestamp__gte=last_24h)quick_stats={'total_requests_24h':metrics_24h.count()'successful_requests_24h':metrics_24h.filter(success=True).count()'avg_response_time_24h':metrics_24h.aggregate(avg=Avg('duration'))['avg']or0'total_tokens_24h':metrics_24h.aggregate(sum=Sum('tokens_used'))['sum']or0'total_cost_24h':metrics_24h.aggregate(sum=Sum('cost'))['sum']or0'unique_users_24h':metrics_24h.values('user').distinct().count()}#Successrateifquick_stats['total_requests_24h']>0:quick_stats['success_rate_24h']=(quick_stats['successful_requests_24h']/quick_stats['total_requests_24h'])*100else:quick_stats['success_rate_24h']=0#Servicebreakdownservice_breakdown=metrics_24h.values('service_type').annotate(total=Count('id')successful=Count('id'filter=Q(success=True))avg_duration=Avg('duration')).order_by('-total')#Recenterrorsrecent_errors=metrics_24h.filter(success=Falseerror_type__isnull=False).values('error_type').annotate(count=Count('id')).order_by('-count')[:5]#Hourlytrendforlast24hourshourly_trend=metrics_24h.extra(select={'hour':'EXTRACT(hourFROMtimestamp)'}).values('hour').annotate(requests=Count('id')success_rate=Avg('success')*100avg_duration=Avg('duration')).order_by('hour')#Activerequestsactive_requests=AIRequest.objects.filter(status__in=['queued''processing']).count()#Queuestatusqueue_status={'queued':AIRequest.objects.filter(status='queued').count()'processing':AIRequest.objects.filter(status='processing').count()'avg_wait_time':AIRequest.objects.filter(status='completed'created_at__gte=last_hour).aggregate(avg_wait=Avg('processing_time'))['avg_wait']or0}returnResponse({'health_status':health_status'quick_stats':quick_stats'service_breakdown':list(service_breakdown)'recent_errors':list(recent_errors)'hourly_trend':list(hourly_trend)'active_requests':active_requests'queue_status':queue_status'timestamp':now.isoformat()})exceptExceptionase:logger.error(f"Errorgettingdashboardoverview:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAdminUser])defdetailed_metrics(request):"""Getdetailedmetricswithfilteringoptions"""try:#Getqueryparametersdays=int(request.GET.get('days'7))service_type=request.GET.get('service_type')user_id=request.GET.get('user_id')#Calculatetimerangeend_time=timezone.now()start_time=end_time-timedelta(days=days)#Basequerysetqueryset=AIServiceMetric.objects.filter(timestamp__gte=start_timetimestamp__lte=end_time)#Applyfiltersifservice_type:queryset=queryset.filter(service_type=service_type)ifuser_id:queryset=queryset.filter(user_id=user_id)#Aggregatemetricsaggregated=queryset.aggregate(total_requests=Count('id')successful_requests=Count('id'filter=Q(success=True))failed_requests=Count('id'filter=Q(success=False))avg_duration=Avg('duration')max_duration=Max('duration')min_duration=Min('duration')total_tokens=Sum('tokens_used')total_cost=Sum('cost')unique_users=Count('user'distinct=True))#Calculatesuccessrateifaggregated['total_requests']>0:aggregated['success_rate']=(aggregated['successful_requests']/aggregated['total_requests'])*100else:aggregated['success_rate']=0#Dailybreakdowndaily_breakdown=queryset.extra(select={'date':'DATE(timestamp)'}).values('date').annotate(requests=Count('id')successful=Count('id'filter=Q(success=True))avg_duration=Avg('duration')total_cost=Sum('cost')).order_by('date')#Servicetypebreakdownservice_breakdown=queryset.values('service_type').annotate(requests=Count('id')successful=Count('id'filter=Q(success=True))avg_duration=Avg('duration')total_cost=Sum('cost')).order_by('-requests')#Erroranalysiserror_breakdown=queryset.filter(success=Falseerror_type__isnull=False).values('error_type').annotate(count=Count('id')).order_by('-count')#Performancepercentiles(approximation)durations=list(queryset.values_list('duration'flat=True))durations.sort()percentiles={}ifdurations:percentiles={'p50':durations[int(len(durations)*0.5)]ifdurationselse0'p90':durations[int(len(durations)*0.9)]ifdurationselse0'p95':durations[int(len(durations)*0.95)]ifdurationselse0'p99':durations[int(len(durations)*0.99)]ifdurationselse0}returnResponse({'period':{'start':start_time.isoformat()'end':end_time.isoformat()'days':days}'filters':{'service_type':service_type'user_id':user_id}'aggregated_metrics':aggregated'daily_breakdown':list(daily_breakdown)'service_breakdown':list(service_breakdown)'error_breakdown':list(error_breakdown)'performance_percentiles':percentiles'timestamp':timezone.now().isoformat()})exceptExceptionase:logger.error(f"Errorgettingdetailedmetrics:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAdminUser])defreal_time_status(request):"""Getreal-timestatusinformation"""try:now=timezone.now()last_5_min=now-timedelta(minutes=5)#Recentactivityrecent_metrics=AIServiceMetric.objects.filter(timestamp__gte=last_5_min)#Currentqueuestatusqueue_stats={'queued':AIRequest.objects.filter(status='queued').count()'processing':AIRequest.objects.filter(status='processing').count()'completed_last_5min':AIRequest.objects.filter(status='completed'completed_at__gte=last_5_min).count()'failed_last_5min':AIRequest.objects.filter(status='failed'completed_at__gte=last_5_min).count()}#Recentperformancerecent_performance={'requests_last_5min':recent_metrics.count()'avg_response_time':recent_metrics.aggregate(avg=Avg('duration'))['avg']or0'success_rate':(recent_metrics.filter(success=True).count()/max(recent_metrics.count()1))*100}#Systemhealthindicatorshealth_indicators={'api_key_valid':improved_ai_service.config_manager.get_api_key()isnotNone'service_initialized':improved_ai_service._gemini_modelisnotNone'queue_healthy':queue_stats['queued']<100#Threshold'response_time_healthy':recent_performance['avg_response_time']<30#30sthreshold'error_rate_healthy':recent_performance['success_rate']>90#90%threshold}#Overallhealthscorehealth_score=sum(health_indicators.values())/len(health_indicators)*100returnResponse({'queue_status':queue_stats'recent_performance':recent_performance'health_indicators':health_indicators'health_score':health_score'timestamp':now.isoformat()})exceptExceptionase:logger.error(f"Errorgettingreal-timestatus:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAdminUser])defconfiguration_status(request):"""Getcurrentconfigurationstatus"""try:#Currentconfigurationcurrent_config=improved_ai_service.config_manager.get_config()#Configurationhistoryconfig_history=AIConfiguration.objects.order_by('-created_at')[:10]#APIkeystatusapi_key=improved_ai_service.config_manager.get_api_key()api_key_status={'configured':bool(api_key)'masked':f"{api_key[:4]}...{api_key[-4:]}"ifapi_keyandlen(api_key)>8else"Notconfigured"'last_tested':None#Youcouldaddatesttimestamp}returnResponse({'current_config':{'default_model':current_config.default_model'temperature':current_config.temperature'max_tokens':current_config.max_tokens'timeout':current_config.timeout'retries':current_config.retries'enable_fallback':current_config.enable_fallback'enable_caching':current_config.enable_caching'cache_ttl':current_config.cache_ttl}'api_key_status':api_key_status'config_history':[{'id':str(config.id)'created_at':config.created_at.isoformat()'updated_by':config.updated_by.usernameifconfig.updated_byelseNone'is_active':config.is_active'model':config.default_model}forconfiginconfig_history]'timestamp':timezone.now().isoformat()})exceptExceptionase:logger.error(f"Errorgettingconfigurationstatus:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAdminUser])defperformance_metrics(request):"""Getdetailedperformancemetrics"""try:days=int(request.GET.get('days'7))since=timezone.now()-timedelta(days=days)#Dailyperformancemetricsdaily_metrics=AIServiceMetric.objects.filter(timestamp__gte=since).annotate(day=TruncDay('timestamp')).values('day''service_type').annotate(total_requests=Count('id')successful_requests=Count('id'filter=Q(success=True))avg_duration=Avg('duration')max_duration=Max('duration')min_duration=Min('duration')).order_by('day''service_type')#Responsetimedistributionresponse_time_buckets={'0-1s':0'1-5s':0'5-10s':0'10-30s':0'30s+':0}formetricinAIServiceMetric.objects.filter(timestamp__gte=since):duration=metric.durationifduration<=1:response_time_buckets['0-1s']+=1elifduration<=5:response_time_buckets['1-5s']+=1elifduration<=10:response_time_buckets['5-10s']+=1elifduration<=30:response_time_buckets['10-30s']+=1else:response_time_buckets['30s+']+=1returnResponse({'period_days':days'daily_metrics':list(daily_metrics)'response_time_distribution':response_time_buckets'timestamp':timezone.now().isoformat()})exceptExceptionase:logger.error(f"Errorgettingperformancemetrics:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAdminUser])defcost_analytics(request):"""Getcostanalyticsandusagepatterns"""try:days=int(request.GET.get('days'30))since=timezone.now()-timedelta(days=days)#Costbreakdownbyservicecost_by_service=AIServiceMetric.objects.filter(timestamp__gte=sincecost__isnull=False).values('service_type').annotate(total_cost=Sum('cost')total_tokens=Sum('tokens_used')request_count=Count('id')avg_cost_per_request=Avg('cost')).order_by('-total_cost')#Totalcostcalculationtotal_cost=AIServiceMetric.objects.filter(timestamp__gte=sincecost__isnull=False).aggregate(total=Sum('cost')total_tokens=Sum('tokens_used')total_requests=Count('id'))#Projectedmonthlycostifdays>=7:daily_avg_cost=total_cost['total']/daysiftotal_cost['total']else0projected_monthly_cost=daily_avg_cost*30else:projected_monthly_cost=0returnResponse({'period_days':days'cost_by_service':list(cost_by_service)'projected_monthly_cost':projected_monthly_cost'total_cost':total_cost['total']or0'timestamp':timezone.now().isoformat()})exceptExceptionase:logger.error(f"Errorgettingcostanalytics:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)
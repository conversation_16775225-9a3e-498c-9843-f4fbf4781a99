# Generated by Django 4.2.7 on 2025-07-04 15:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="ContentMapping",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "relationship_type",
                    models.CharField(
                        help_text="Type of relationship between source and target",
                        max_length=50,
                    ),
                ),
            ],
            options={
                "verbose_name": "Content Mapping",
                "verbose_name_plural": "Content Mappings",
            },
        ),
        migrations.CreateModel(
            name="ContentReference",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("object_id", models.PositiveIntegerField()),
                ("content_category", models.CharField(max_length=50)),
                ("display_name", models.CharField(blank=True, max_length=255)),
            ],
            options={
                "verbose_name": "Content Reference",
                "verbose_name_plural": "Content References",
            },
        ),
        migrations.CreateModel(
            name="Skill",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                (
                    "level",
                    models.IntegerField(
                        choices=[
                            (1, "Beginner"),
                            (2, "Elementary"),
                            (3, "Intermediate"),
                            (4, "Advanced"),
                            (5, "Expert"),
                        ],
                        default=1,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="StudentLevel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "current_level",
                    models.IntegerField(
                        choices=[
                            (1, "Beginner"),
                            (2, "Elementary"),
                            (3, "Intermediate"),
                            (4, "Advanced"),
                            (5, "Expert"),
                        ],
                        default=1,
                        help_text="Current academic level of the student",
                    ),
                ),
                (
                    "previous_level",
                    models.IntegerField(
                        choices=[
                            (1, "Beginner"),
                            (2, "Elementary"),
                            (3, "Intermediate"),
                            (4, "Advanced"),
                            (5, "Expert"),
                        ],
                        default=1,
                        help_text="Previous academic level of the student",
                    ),
                ),
                ("level_updated_at", models.DateTimeField(auto_now=True)),
                (
                    "level_update_reason",
                    models.CharField(
                        blank=True,
                        help_text="Reason for the last level update",
                        max_length=255,
                    ),
                ),
                (
                    "assessment_id",
                    models.IntegerField(
                        blank=True,
                        help_text="ID of the assessment that determined this level",
                        null=True,
                    ),
                ),
                (
                    "level_history",
                    models.JSONField(
                        blank=True, default=list, help_text="History of level changes"
                    ),
                ),
                (
                    "level_by_provider",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Track levels per content provider type",
                    ),
                ),
                (
                    "detailed_progression",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Detailed progression data from all apps",
                    ),
                ),
                (
                    "assessment_history",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="History of assessments across all providers",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="StudentSkillProgress",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("object_id", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "proficiency_level",
                    models.FloatField(
                        default=0.0, help_text="Proficiency level from 0.0 to 5.0"
                    ),
                ),
                ("last_assessed", models.DateTimeField(auto_now=True)),
                (
                    "current_crown_level",
                    models.IntegerField(
                        default=0, help_text="Current crown/level achieved"
                    ),
                ),
                ("lessons_completed", models.IntegerField(default=0)),
                ("total_xp", models.IntegerField(default=0)),
                ("is_unlocked", models.BooleanField(default=False)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("last_practiced_at", models.DateTimeField(blank=True, null=True)),
                (
                    "strength",
                    models.FloatField(
                        default=0,
                        help_text="Skill strength from 0-1 for spaced repetition",
                    ),
                ),
                ("next_review_date", models.DateField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name="UserProgress",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("started_at", models.DateTimeField(auto_now_add=True)),
                ("last_activity", models.DateTimeField(auto_now=True)),
                ("completion_percentage", models.FloatField(default=0.0)),
                ("is_completed", models.BooleanField(default=False)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "progress_type",
                    models.CharField(
                        choices=[
                            ("COURSE", "Course Progress"),
                            ("INTERACTIVE", "Interactive Progress"),
                            ("PATHWAY", "Learning Pathway"),
                            ("ASSESSMENT", "Assessment Progress"),
                            ("GENERATED", "AI Generated Content"),
                        ],
                        default="COURSE",
                        help_text="Type of progress being tracked",
                        max_length=20,
                    ),
                ),
                (
                    "provider_progress",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Progress data by content provider",
                    ),
                ),
                (
                    "progress_data",
                    models.JSONField(
                        blank=True, default=dict, help_text="General progress data"
                    ),
                ),
                (
                    "detailed_metrics",
                    models.JSONField(
                        blank=True, default=dict, help_text="Detailed progress metrics"
                    ),
                ),
                (
                    "milestones_completed",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of completed milestone IDs",
                    ),
                ),
                (
                    "content_reference",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.contentreference",
                    ),
                ),
            ],
            options={
                "verbose_name": "User Progress",
                "verbose_name_plural": "User Progress Records",
            },
        ),
    ]

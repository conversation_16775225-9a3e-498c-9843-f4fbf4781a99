"""
Integration tests for authentication system.
"""

import pytest
from django.test import TransactionTestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
import json
from unittest.mock import patch

User = get_user_model()


@pytest.mark.integration
class AuthenticationIntegrationTest(TransactionTestCase):
    """Integration tests for complete authentication workflows."""
    
    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user_data = {
            'username': 'integrationuser',
            'email': '<EMAIL>',
            'password': 'SecurePass123!',
            'password_confirm': 'SecurePass123!',
            'first_name': 'Integration',
            'last_name': 'User',
            'role': 'STUDENT'
        }
    
    def test_complete_user_registration_flow(self):
        """Test complete user registration and verification flow."""
        # 1. Register new user
        register_url = reverse('auth_api:register')
        register_response = self.client.post(
            register_url, self.user_data, format='json'
        )
        
        self.assertEqual(register_response.status_code, status.HTTP_201_CREATED)
        self.assertIn('access', register_response.data)
        self.assertIn('refresh', register_response.data)
        self.assertIn('user', register_response.data)
        
        # Verify user was created in database
        user = User.objects.get(username=self.user_data['username'])
        self.assertEqual(user.email, self.user_data['email'])
        self.assertEqual(user.first_name, self.user_data['first_name'])
        self.assertEqual(user.role, self.user_data['role'])
        
        # 2. Verify automatic login after registration
        access_token = register_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        profile_url = reverse('auth_api:profile')
        profile_response = self.client.get(profile_url)
        
        self.assertEqual(profile_response.status_code, status.HTTP_200_OK)
        self.assertEqual(profile_response.data['username'], self.user_data['username'])
    
    def test_login_logout_flow(self):
        """Test complete login and logout flow."""
        # Create user first
        user = User.objects.create_user(
            username='loginuser',
            email='<EMAIL>',
            password='testpass123',
            role='STUDENT'
        )
        
        # 1. Login
        login_data = {
            'username': 'loginuser',
            'password': 'testpass123'
        }
        login_url = reverse('auth_api:login')
        login_response = self.client.post(login_url, login_data, format='json')
        
        self.assertEqual(login_response.status_code, status.HTTP_200_OK)
        self.assertIn('access', login_response.data)
        self.assertIn('refresh', login_response.data)
        
        # 2. Access protected resource
        access_token = login_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        profile_url = reverse('auth_api:profile')
        profile_response = self.client.get(profile_url)
        self.assertEqual(profile_response.status_code, status.HTTP_200_OK)
        
        # 3. Logout
        refresh_token = login_response.data['refresh']
        logout_url = reverse('auth_api:logout')
        logout_response = self.client.post(
            logout_url, {'refresh': refresh_token}, format='json'
        )
        self.assertEqual(logout_response.status_code, status.HTTP_200_OK)
        
        # 4. Verify token is blacklisted
        profile_response = self.client.get(profile_url)
        self.assertEqual(profile_response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_token_refresh_flow(self):
        """Test token refresh workflow."""
        # Create user and get tokens
        user = User.objects.create_user(
            username='refreshuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        refresh_token = str(refresh)
        
        # 1. Use access token
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        profile_url = reverse('auth_api:profile')
        profile_response = self.client.get(profile_url)
        self.assertEqual(profile_response.status_code, status.HTTP_200_OK)
        
        # 2. Refresh token
        refresh_url = reverse('auth_api:token_refresh')
        refresh_response = self.client.post(
            refresh_url, {'refresh': refresh_token}, format='json'
        )
        self.assertEqual(refresh_response.status_code, status.HTTP_200_OK)
        self.assertIn('access', refresh_response.data)
        
        # 3. Use new access token
        new_access_token = refresh_response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {new_access_token}')
        profile_response = self.client.get(profile_url)
        self.assertEqual(profile_response.status_code, status.HTTP_200_OK)
    
    def test_profile_update_flow(self):
        """Test profile update workflow."""
        # Create and login user
        user = User.objects.create_user(
            username='profileuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Original',
            last_name='Name'
        )
        
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        # 1. Get current profile
        profile_url = reverse('auth_api:profile')
        profile_response = self.client.get(profile_url)
        self.assertEqual(profile_response.status_code, status.HTTP_200_OK)
        self.assertEqual(profile_response.data['first_name'], 'Original')
        
        # 2. Update profile
        update_data = {
            'first_name': 'Updated',
            'last_name': 'Name',
            'email': '<EMAIL>'
        }
        update_response = self.client.patch(
            profile_url, update_data, format='json'
        )
        self.assertEqual(update_response.status_code, status.HTTP_200_OK)
        
        # 3. Verify changes in database
        user.refresh_from_db()
        self.assertEqual(user.first_name, 'Updated')
        self.assertEqual(user.email, '<EMAIL>')
        
        # 4. Verify changes in API response
        profile_response = self.client.get(profile_url)
        self.assertEqual(profile_response.data['first_name'], 'Updated')
        self.assertEqual(profile_response.data['email'], '<EMAIL>')
    
    def test_password_change_flow(self):
        """Test password change workflow."""
        # Create and login user
        user = User.objects.create_user(
            username='passworduser',
            email='<EMAIL>',
            password='oldpass123'
        )
        
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        # 1. Change password
        password_data = {
            'old_password': 'oldpass123',
            'new_password': 'newpass123!',
            'new_password_confirm': 'newpass123!'
        }
        
        change_url = reverse('auth_api:change_password')
        change_response = self.client.post(
            change_url, password_data, format='json'
        )
        self.assertEqual(change_response.status_code, status.HTTP_200_OK)
        
        # 2. Verify old password no longer works
        login_data = {
            'username': 'passworduser',
            'password': 'oldpass123'
        }
        login_url = reverse('auth_api:login')
        login_response = self.client.post(login_url, login_data, format='json')
        self.assertEqual(login_response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # 3. Verify new password works
        login_data['password'] = 'newpass123!'
        login_response = self.client.post(login_url, login_data, format='json')
        self.assertEqual(login_response.status_code, status.HTTP_200_OK)
    
    @patch('auth_api.views.ThreatDetection.detect_brute_force')
    def test_security_integration(self, mock_detect):
        """Test security features integration."""
        # Create user
        user = User.objects.create_user(
            username='securityuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # 1. Test normal login
        mock_detect.return_value = {
            'is_brute_force': False,
            'risk_level': 'LOW'
        }
        
        login_data = {
            'username': 'securityuser',
            'password': 'testpass123'
        }
        login_url = reverse('auth_api:login')
        login_response = self.client.post(login_url, login_data, format='json')
        self.assertEqual(login_response.status_code, status.HTTP_200_OK)
        
        # 2. Test brute force protection
        mock_detect.return_value = {
            'is_brute_force': True,
            'risk_level': 'HIGH'
        }
        
        login_response = self.client.post(login_url, login_data, format='json')
        self.assertEqual(login_response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)
    
    def test_role_based_access_integration(self):
        """Test role-based access control integration."""
        # Create users with different roles
        student = User.objects.create_user(
            username='student',
            email='<EMAIL>',
            password='testpass123',
            role='STUDENT'
        )
        
        professor = User.objects.create_user(
            username='professor',
            email='<EMAIL>',
            password='testpass123',
            role='PROFESSOR'
        )
        
        admin = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Test student access
        student_token = str(RefreshToken.for_user(student).access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {student_token}')
        
        profile_response = self.client.get(reverse('auth_api:profile'))
        self.assertEqual(profile_response.status_code, status.HTTP_200_OK)
        self.assertEqual(profile_response.data['role'], 'STUDENT')
        
        # Test professor access
        professor_token = str(RefreshToken.for_user(professor).access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {professor_token}')
        
        profile_response = self.client.get(reverse('auth_api:profile'))
        self.assertEqual(profile_response.status_code, status.HTTP_200_OK)
        self.assertEqual(profile_response.data['role'], 'PROFESSOR')
        
        # Test admin access
        admin_token = str(RefreshToken.for_user(admin).access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {admin_token}')
        
        profile_response = self.client.get(reverse('auth_api:profile'))
        self.assertEqual(profile_response.status_code, status.HTTP_200_OK)
        self.assertTrue(profile_response.data['is_staff'])
    
    def test_concurrent_sessions_integration(self):
        """Test handling of concurrent user sessions."""
        # Create user
        user = User.objects.create_user(
            username='concurrentuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create multiple sessions
        client1 = APIClient()
        client2 = APIClient()
        
        # Login from first client
        login_data = {
            'username': 'concurrentuser',
            'password': 'testpass123'
        }
        login_url = reverse('auth_api:login')
        
        response1 = client1.post(login_url, login_data, format='json')
        self.assertEqual(response1.status_code, status.HTTP_200_OK)
        
        token1 = response1.data['access']
        client1.credentials(HTTP_AUTHORIZATION=f'Bearer {token1}')
        
        # Login from second client
        response2 = client2.post(login_url, login_data, format='json')
        self.assertEqual(response2.status_code, status.HTTP_200_OK)
        
        token2 = response2.data['access']
        client2.credentials(HTTP_AUTHORIZATION=f'Bearer {token2}')
        
        # Both sessions should work
        profile_url = reverse('auth_api:profile')
        
        profile1 = client1.get(profile_url)
        self.assertEqual(profile1.status_code, status.HTTP_200_OK)
        
        profile2 = client2.get(profile_url)
        self.assertEqual(profile2.status_code, status.HTTP_200_OK)
        
        # Logout from first session
        logout_url = reverse('auth_api:logout')
        logout1 = client1.post(
            logout_url, {'refresh': response1.data['refresh']}, format='json'
        )
        self.assertEqual(logout1.status_code, status.HTTP_200_OK)
        
        # First session should be invalid
        profile1 = client1.get(profile_url)
        self.assertEqual(profile1.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Second session should still work
        profile2 = client2.get(profile_url)
        self.assertEqual(profile2.status_code, status.HTTP_200_OK)
    
    def test_error_handling_integration(self):
        """Test error handling across authentication system."""
        # Test registration with invalid data
        invalid_data = {
            'username': 'ab',  # Too short
            'email': 'invalid-email',
            'password': '123',  # Too weak
            'password_confirm': '456',  # Doesn't match
        }
        
        register_url = reverse('auth_api:register')
        register_response = self.client.post(
            register_url, invalid_data, format='json'
        )
        
        self.assertEqual(register_response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('username', register_response.data)
        self.assertIn('email', register_response.data)
        self.assertIn('password', register_response.data)
        
        # Test login with non-existent user
        login_data = {
            'username': 'nonexistent',
            'password': 'password123'
        }
        login_url = reverse('auth_api:login')
        login_response = self.client.post(login_url, login_data, format='json')
        
        self.assertEqual(login_response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Test accessing protected resource without token
        profile_url = reverse('auth_api:profile')
        profile_response = self.client.get(profile_url)
        
        self.assertEqual(profile_response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Test using invalid token
        self.client.credentials(HTTP_AUTHORIZATION='Bearer invalid-token')
        profile_response = self.client.get(profile_url)
        
        self.assertEqual(profile_response.status_code, status.HTTP_401_UNAUTHORIZED)

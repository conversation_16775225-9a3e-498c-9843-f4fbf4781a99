"""APIViewsforImprovedAIServiceTheseviewsprovideRESTAPIendpointsfortheenhancedAIfunctionality."""fromrest_frameworkimportstatusfromrest_framework.decoratorsimportapi_viewpermission_classesfromrest_framework.permissionsimportIsAuthenticatedIsAdminUserfromrest_framework.responseimportResponsefrom django.utils.decoratorsimportmethod_decoratorfrom django.views.decorators.cacheimportcache_pagefrom django.core.cacheimportcacheimport loggingfrom.ai_service_improvedimportimproved_ai_servicefrom.ai_async_serviceimportRequestPriorityfrom.ai_modelsimportAIRequestAIServiceMetricAIUsageStatisticslogger=logging.getLogger(__name__)@api_view(['POST'])@permission_classes([IsAuthenticated])defgenerate_content(request):"""GenerateAIcontentwithimprovederrorhandling"""try:data=request.dataprompt=data.get('prompt')service_type=data.get('service_type''general')user_id=str(request.user.id)ifrequest.user.is_authenticatedelseNoneifnotprompt:returnResponse({'error':'Promptisrequired'}status=status.HTTP_400_BAD_REQUEST)#Generatecontentcontent=improved_ai_service.generate_content(prompt=promptservice_type=service_typeuser_id=user_idtemperature=data.get('temperature')max_tokens=data.get('max_tokens')context=data.get('context'{}))returnResponse({'content':content'service_type':service_type'timestamp':cache.get('last_ai_request_time'0)})exceptExceptionase:logger.error(f"Erroringenerate_content:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['POST'])@permission_classes([IsAuthenticated])defgenerate_content_async(request):"""GenerateAIcontentasynchronously"""try:data=request.dataprompt=data.get('prompt')service_type=data.get('service_type''general')priority_str=data.get('priority''normal')user_id=str(request.user.id)ifrequest.user.is_authenticatedelseNoneifnotprompt:returnResponse({'error':'Promptisrequired'}status=status.HTTP_400_BAD_REQUEST)#Convertprioritystringtoenumtry:priority=RequestPriority[priority_str.upper()]exceptKeyError:priority=RequestPriority.NORMAL#Submitasyncrequestimportasynciorequest_id=asyncio.run(improved_ai_service.generate_content_async(prompt=promptservice_type=service_typepriority=priorityuser_id=user_idcontext=data.get('context'{})))returnResponse({'request_id':request_id'status':'queued''estimated_wait_time':'30-60seconds'})exceptExceptionase:logger.error(f"Erroringenerate_content_async:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAuthenticated])defasync_status(requestrequest_id):"""GetstatusofasyncAIrequest"""try:status_info=improved_ai_service.get_async_status(request_id)ifnotstatus_info:returnResponse({'error':'Requestnotfound'}status=status.HTTP_404_NOT_FOUND)returnResponse(status_info)exceptExceptionase:logger.error(f"Errorgettingasyncstatus:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAuthenticated])defasync_result(requestrequest_id):"""GetresultofasyncAIrequest"""try:result_info=improved_ai_service.get_async_result(request_id)ifnotresult_info:returnResponse({'error':'Requestnotfound'}status=status.HTTP_404_NOT_FOUND)returnResponse(result_info)exceptExceptionase:logger.error(f"Errorgettingasyncresult:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['POST'])@permission_classes([IsAuthenticated])defanalyze_answer(request):"""AnalyzestudentanswerusingAI"""try:data=request.datastudent_answer=data.get('student_answer')question=data.get('question')user_id=str(request.user.id)ifrequest.user.is_authenticatedelseNoneifnotstudent_answerornotquestion:returnResponse({'error':'Bothstudent_answerandquestionarerequired'}status=status.HTTP_400_BAD_REQUEST)#Analyzeansweranalysis=improved_ai_service.analyze_answer(student_answer=student_answerquestion=questionuser_id=user_id)returnResponse(analysis)exceptExceptionase:logger.error(f"Errorinanalyze_answer:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAuthenticated])@method_decorator(cache_page(60))#Cachefor1minutedefhealth_status(request):"""GetAIservicehealthstatus"""try:health=improved_ai_service.get_health_status()returnResponse(health)exceptExceptionase:logger.error(f"Errorgettinghealthstatus:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAuthenticated])defusage_analytics(request):"""GetAIusageanalytics"""try:days=int(request.GET.get('days'7))analytics=improved_ai_service.get_usage_analytics(days)returnResponse(analytics)exceptExceptionase:logger.error(f"Errorgettingusageanalytics:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['POST'])@permission_classes([IsAdminUser])defupdate_configuration(request):"""UpdateAIserviceconfiguration(adminonly)"""try:config_data=request.data.get('config'{})success=improved_ai_service.update_configuration(**config_data)ifsuccess:returnResponse({'message':'Configurationupdatedsuccessfully'})else:returnResponse({'error':'Failedtoupdateconfiguration'}status=status.HTTP_400_BAD_REQUEST)exceptExceptionase:logger.error(f"Errorupdatingconfiguration:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['POST'])@permission_classes([IsAdminUser])defupdate_api_key(request):"""UpdateAIserviceAPIkey(adminonly)"""try:api_key=request.data.get('api_key')ifnotapi_key:returnResponse({'error':'APIkeyisrequired'}status=status.HTTP_400_BAD_REQUEST)success=improved_ai_service.update_api_key(api_key)ifsuccess:returnResponse({'message':'APIkeyupdatedsuccessfully'})else:returnResponse({'error':'FailedtoupdateAPIkey'}status=status.HTTP_400_BAD_REQUEST)exceptExceptionase:logger.error(f"ErrorupdatingAPIkey:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAdminUser])defservice_metrics(request):"""Getdetailedservicemetrics(adminonly)"""try:from django.db.modelsimportCountAvgSumfromdatetimeimport timedeltafrom django.utilsimport timezone#Getmetricsforthelast24hourssince=timezone.now()-timedelta(hours=24)metrics=AIServiceMetric.objects.filter(timestamp__gte=since).values('service_type').annotate(total_requests=Count('id')successful_requests=Count('id'filter=models.Q(success=True))avg_duration=Avg('duration')total_tokens=Sum('tokens_used')total_cost=Sum('cost'))returnResponse({'period':'24hours''metrics':list(metrics)'timestamp':timezone.now().isoformat()})exceptExceptionase:logger.error(f"Errorgettingservicemetrics:{e}")returnResponse({'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)
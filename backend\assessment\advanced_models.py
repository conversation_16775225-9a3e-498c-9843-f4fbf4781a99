"""
Advanced Assessment Engine Models
Implements adaptive questioning, plagiarism detection, secure browser lockdown, 
and peer assessment capabilities.
"""

import json
import uuid
from datetime import timed<PERSON>ta
from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db import models
from django.utils import timezone
from cryptography.fernet import Fernet
import hashlib


class AdaptiveAssessmentConfig(models.Model):
    """Configuration for adaptive assessment behavior"""
    
    DIFFICULTY_ALGORITHMS = [
        ('item_response_theory', 'Item Response Theory (IRT)'),
        ('cat_adaptive', 'Computer Adaptive Testing (CAT)'),
        ('simple_threshold', 'Simple Threshold Based'),
    ]
    
    name = models.CharField(max_length=200, unique=True)
    algorithm = models.CharField(max_length=50, choices=DIFFICULTY_ALGORITHMS, default='simple_threshold')
    
    # Algorithm parameters
    initial_difficulty = models.FloatField(default=0.5, validators=[MinV<PERSON>ueValidator(0.0), MaxValueValidator(1.0)])
    min_questions = models.PositiveIntegerField(default=5)
    max_questions = models.PositiveIntegerField(default=30)
    stopping_criteria_theta = models.FloatField(default=0.3, help_text="Standard error threshold for stopping")
    difficulty_adjustment_factor = models.FloatField(default=0.2)
    
    # Question selection parameters
    question_selection_method = models.CharField(
        max_length=50,
        choices=[
            ('maximum_information', 'Maximum Information'),
            ('random', 'Random from Pool'),
            ('weighted_random', 'Weighted Random'),
        ],
        default='maximum_information'
    )
    
    # Termination conditions
    enable_time_limit = models.BooleanField(default=True)
    time_limit_minutes = models.PositiveIntegerField(default=60)
    enable_mastery_threshold = models.BooleanField(default=True)
    mastery_threshold = models.FloatField(default=0.8)
    
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Adaptive Assessment Configuration"
        verbose_name_plural = "Adaptive Assessment Configurations"
    
    def __str__(self):
        return f"{self.name} ({self.get_algorithm_display()})"


class SecureAssessmentSession(models.Model):
    """Manages secure browser lockdown and monitoring for high-stakes assessments"""
    
    SESSION_STATUS = [
        ('PENDING', 'Pending Start'),
        ('ACTIVE', 'Active Session'),
        ('PAUSED', 'Paused'),
        ('COMPLETED', 'Completed'),
        ('TERMINATED', 'Terminated'),
        ('FLAGGED', 'Flagged for Review'),
    ]
    
    SECURITY_LEVELS = [
        ('LOW', 'Low Security'),
        ('MEDIUM', 'Medium Security'),
        ('HIGH', 'High Security'),
        ('MAXIMUM', 'Maximum Security'),
    ]
    
    session_id = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    assessment = models.OneToOneField('Assessment', on_delete=models.CASCADE, related_name='secure_session')
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    
    # Security configuration
    security_level = models.CharField(max_length=20, choices=SECURITY_LEVELS, default='MEDIUM')
    require_lockdown_browser = models.BooleanField(default=True)
    disable_copy_paste = models.BooleanField(default=True)
    disable_right_click = models.BooleanField(default=True)
    disable_print_screen = models.BooleanField(default=True)
    block_external_sites = models.BooleanField(default=True)
    enable_screen_recording = models.BooleanField(default=False)
    enable_webcam_monitoring = models.BooleanField(default=False)
    enable_audio_monitoring = models.BooleanField(default=False)
    
    # Session tracking
    status = models.CharField(max_length=20, choices=SESSION_STATUS, default='PENDING')
    start_time = models.DateTimeField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)
    last_activity = models.DateTimeField(auto_now=True)
    
    # Browser and device information
    browser_info = models.JSONField(default=dict, help_text="Browser and system information")
    device_fingerprint = models.CharField(max_length=255, blank=True)
    ip_address = models.GenericIPAddressField()
    
    # Security violations and monitoring
    violation_count = models.PositiveIntegerField(default=0)
    violations_log = models.JSONField(default=list, help_text="Log of security violations")
    focus_loss_count = models.PositiveIntegerField(default=0)
    tab_switch_count = models.PositiveIntegerField(default=0)
    
    # Biometric and behavioral data (if enabled)
    keystroke_patterns = models.JSONField(default=dict, blank=True)
    mouse_movement_patterns = models.JSONField(default=dict, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Secure Assessment Session"
        verbose_name_plural = "Secure Assessment Sessions"
        indexes = [
            models.Index(fields=['session_id']),
            models.Index(fields=['student', 'status']),
            models.Index(fields=['start_time', 'end_time']),
        ]
    
    def __str__(self):
        return f"Secure Session: {self.assessment.title} - {self.student.username}"
    
    def log_violation(self, violation_type, details):
        """Log a security violation"""
        violation = {
            'timestamp': timezone.now().isoformat(),
            'type': violation_type,
            'details': details,
        }
        self.violations_log.append(violation)
        self.violation_count += 1
        self.save(update_fields=['violations_log', 'violation_count'])
    
    def is_session_valid(self):
        """Check if the session is still valid"""
        if self.status != 'ACTIVE':
            return False
        
        # Check for excessive violations
        if self.violation_count > 5:  # Configurable threshold
            return False
        
        # Check session timeout
        if self.last_activity and timezone.now() - self.last_activity > timedelta(minutes=30):
            return False
        
        return True


class PlagiarismDetectionResult(models.Model):
    """Results from AI-powered plagiarism detection analysis"""
    
    DETECTION_STATUS = [
        ('PENDING', 'Analysis Pending'),
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Analysis Complete'),
        ('FAILED', 'Analysis Failed'),
    ]
    
    SIMILARITY_LEVELS = [
        ('LOW', 'Low Similarity (0-30%)'),
        ('MEDIUM', 'Medium Similarity (31-60%)'),
        ('HIGH', 'High Similarity (61-80%)'),
        ('VERY_HIGH', 'Very High Similarity (81-100%)'),
    ]
    
    response = models.OneToOneField('AssessmentResponse', on_delete=models.CASCADE, related_name='plagiarism_result')
    
    # Analysis results
    status = models.CharField(max_length=20, choices=DETECTION_STATUS, default='PENDING')
    overall_similarity_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        null=True, blank=True
    )
    similarity_level = models.CharField(max_length=20, choices=SIMILARITY_LEVELS, null=True, blank=True)
    
    # Detailed analysis
    ai_detection_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        null=True, blank=True,
        help_text="AI-generated content probability"
    )
    semantic_similarity_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        null=True, blank=True
    )
    lexical_similarity_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        null=True, blank=True
    )
    
    # Source detection
    similar_sources = models.JSONField(
        default=list,
        help_text="List of similar sources found online or in database"
    )
    internal_matches = models.JSONField(
        default=list,
        help_text="Matches found within the system's database"
    )
    
    # Analysis metadata
    analysis_engine = models.CharField(max_length=100, default='gemini')
    analysis_timestamp = models.DateTimeField(auto_now_add=True)
    processing_time_seconds = models.FloatField(null=True, blank=True)
    
    # Human review
    requires_human_review = models.BooleanField(default=False)
    human_reviewed = models.BooleanField(default=False)
    human_reviewer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='plagiarism_reviews'
    )
    human_review_notes = models.TextField(blank=True)
    human_review_timestamp = models.DateTimeField(null=True, blank=True)
    
    # Final decision
    is_plagiarized = models.BooleanField(null=True, blank=True)
    confidence_level = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        null=True, blank=True
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Plagiarism Detection Result"
        verbose_name_plural = "Plagiarism Detection Results"
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['similarity_level']),
            models.Index(fields=['requires_human_review']),
        ]
    
    def __str__(self):
        return f"Plagiarism Analysis: {self.response.student.username} - {self.similarity_level or 'Pending'}"
    
    def get_similarity_percentage(self):
        """Convert similarity score to percentage"""
        if self.overall_similarity_score is not None:
            return round(self.overall_similarity_score * 100, 2)
        return None


class PeerAssessment(models.Model):
    """Peer assessment and review system"""
    
    ASSESSMENT_STATUS = [
        ('PENDING', 'Pending Assignment'),
        ('ASSIGNED', 'Assigned to Reviewers'),
        ('IN_PROGRESS', 'Reviews in Progress'),
        ('COMPLETED', 'All Reviews Complete'),
        ('CALIBRATION', 'Calibration Phase'),
    ]
    
    original_assessment = models.ForeignKey('Assessment', on_delete=models.CASCADE, related_name='peer_assessments')
    student_work = models.ForeignKey('AssessmentResponse', on_delete=models.CASCADE, related_name='peer_assessments')
    
    # Peer review configuration
    number_of_reviewers = models.PositiveIntegerField(default=3)
    anonymous_review = models.BooleanField(default=True)
    self_assessment_required = models.BooleanField(default=True)
    
    # Rubric and criteria
    evaluation_criteria = models.JSONField(
        default=list,
        help_text="List of criteria for peer evaluation"
    )
    rubric = models.JSONField(
        default=dict,
        help_text="Detailed rubric for assessment"
    )
    
    # Status and tracking
    status = models.CharField(max_length=20, choices=ASSESSMENT_STATUS, default='PENDING')
    assignment_deadline = models.DateTimeField()
    review_deadline = models.DateTimeField()
    
    # Quality control
    enable_calibration = models.BooleanField(default=True)
    calibration_responses = models.JSONField(default=list, help_text="Calibration sample responses")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Peer Assessment"
        verbose_name_plural = "Peer Assessments"
    
    def __str__(self):
        return f"Peer Assessment: {self.original_assessment.title}"


class PeerReview(models.Model):
    """Individual peer review within a peer assessment"""
    
    REVIEW_STATUS = [
        ('NOT_STARTED', 'Not Started'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('OVERDUE', 'Overdue'),
    ]
    
    peer_assessment = models.ForeignKey(PeerAssessment, on_delete=models.CASCADE, related_name='reviews')
    reviewer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='peer_reviews_given')
    reviewee = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='peer_reviews_received')
    
    # Review content
    scores = models.JSONField(default=dict, help_text="Scores for each criterion")
    overall_score = models.FloatField(null=True, blank=True)
    qualitative_feedback = models.TextField(blank=True)
    suggestions_for_improvement = models.TextField(blank=True)
    
    # Review metadata
    status = models.CharField(max_length=20, choices=REVIEW_STATUS, default='NOT_STARTED')
    time_spent_minutes = models.PositiveIntegerField(null=True, blank=True)
    submitted_at = models.DateTimeField(null=True, blank=True)
    
    # Quality metrics
    review_quality_score = models.FloatField(
        null=True, blank=True,
        help_text="AI-calculated quality of the review"
    )
    helpfulness_rating = models.IntegerField(
        null=True, blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Reviewee's rating of review helpfulness"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Peer Review"
        verbose_name_plural = "Peer Reviews"
        unique_together = ['peer_assessment', 'reviewer', 'reviewee']
        indexes = [
            models.Index(fields=['reviewer', 'status']),
            models.Index(fields=['reviewee', 'submitted_at']),
        ]
    
    def __str__(self):
        return f"Review by {self.reviewer.username} for {self.reviewee.username}"


class SelfReflection(models.Model):
    """Self-reflection component for assessments"""
    
    REFLECTION_TYPES = [
        ('PRE_ASSESSMENT', 'Pre-Assessment Reflection'),
        ('POST_ASSESSMENT', 'Post-Assessment Reflection'),
        ('PEER_REVIEW', 'Peer Review Reflection'),
        ('LEARNING_REFLECTION', 'Learning Process Reflection'),
    ]
    
    assessment = models.ForeignKey('Assessment', on_delete=models.CASCADE, related_name='reflections')
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    reflection_type = models.CharField(max_length=30, choices=REFLECTION_TYPES)
    
    # Reflection prompts and responses
    prompts = models.JSONField(default=list, help_text="Reflection prompts presented to student")
    responses = models.JSONField(default=dict, help_text="Student responses to prompts")
    
    # Learning goals and outcomes
    learning_goals = models.JSONField(default=list, help_text="Student-identified learning goals")
    achieved_outcomes = models.JSONField(default=list, help_text="Self-assessed achieved outcomes")
    
    # Metacognitive elements
    confidence_level = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True, blank=True,
        help_text="Student's confidence in their performance"
    )
    effort_level = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True, blank=True,
        help_text="Student's self-assessed effort level"
    )
    strategy_effectiveness = models.JSONField(
        default=dict,
        help_text="Student's reflection on strategy effectiveness"
    )
    
    # Areas for improvement
    identified_strengths = models.TextField(blank=True)
    identified_weaknesses = models.TextField(blank=True)
    improvement_plans = models.TextField(blank=True)
    
    # Time tracking
    time_spent_minutes = models.PositiveIntegerField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Self Reflection"
        verbose_name_plural = "Self Reflections"
        unique_together = ['assessment', 'student', 'reflection_type']
    
    def __str__(self):
        return f"Reflection: {self.student.username} - {self.get_reflection_type_display()}"


class AdaptiveQuestionSelection(models.Model):
    """Tracks adaptive question selection and difficulty progression"""
    
    assessment = models.ForeignKey('Assessment', on_delete=models.CASCADE, related_name='question_selections')
    question = models.ForeignKey('AssessmentQuestion', on_delete=models.CASCADE)
    
    # Selection metadata
    selection_order = models.PositiveIntegerField()
    difficulty_at_selection = models.FloatField()
    estimated_ability_before = models.FloatField(null=True, blank=True)
    estimated_ability_after = models.FloatField(null=True, blank=True)
    
    # Question performance
    response_time_seconds = models.PositiveIntegerField(null=True, blank=True)
    was_correct = models.BooleanField(null=True, blank=True)
    
    # Adaptive algorithm data
    information_value = models.FloatField(null=True, blank=True)
    selection_reason = models.CharField(max_length=100, blank=True)
    algorithm_state = models.JSONField(default=dict, help_text="Algorithm state at time of selection")
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Adaptive Question Selection"
        verbose_name_plural = "Adaptive Question Selections"
        unique_together = ['assessment', 'selection_order']
        indexes = [
            models.Index(fields=['assessment', 'selection_order']),
        ]
    
    def __str__(self):
        return f"Q{self.selection_order}: {self.question.question_text[:50]}..."


class AssessmentIntegrityLog(models.Model):
    """Comprehensive logging for assessment integrity and security"""
    
    EVENT_TYPES = [
        ('SESSION_START', 'Session Started'),
        ('SESSION_END', 'Session Ended'),
        ('QUESTION_VIEW', 'Question Viewed'),
        ('ANSWER_SUBMIT', 'Answer Submitted'),
        ('FOCUS_LOSS', 'Browser Focus Lost'),
        ('TAB_SWITCH', 'Tab Switch Detected'),
        ('COPY_ATTEMPT', 'Copy Attempt Blocked'),
        ('PASTE_ATTEMPT', 'Paste Attempt Blocked'),
        ('RIGHT_CLICK', 'Right Click Blocked'),
        ('PRINT_SCREEN', 'Print Screen Blocked'),
        ('SUSPICIOUS_ACTIVITY', 'Suspicious Activity'),
        ('VIOLATION', 'Security Violation'),
    ]
    
    assessment = models.ForeignKey('Assessment', on_delete=models.CASCADE, related_name='integrity_logs')
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    secure_session = models.ForeignKey(SecureAssessmentSession, on_delete=models.CASCADE, null=True, blank=True)
    
    # Event details
    event_type = models.CharField(max_length=30, choices=EVENT_TYPES)
    timestamp = models.DateTimeField(auto_now_add=True)
    event_data = models.JSONField(default=dict, help_text="Additional event data")
    
    # Browser and system info
    user_agent = models.TextField(blank=True)
    ip_address = models.GenericIPAddressField()
    screen_resolution = models.CharField(max_length=20, blank=True)
    
    # Risk assessment
    risk_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="Calculated risk score for this event"
    )
    
    class Meta:
        verbose_name = "Assessment Integrity Log"
        verbose_name_plural = "Assessment Integrity Logs"
        indexes = [
            models.Index(fields=['assessment', 'timestamp']),
            models.Index(fields=['student', 'event_type']),
            models.Index(fields=['risk_score']),
        ]
    
    def __str__(self):
        return f"{self.get_event_type_display()}: {self.student.username} at {self.timestamp}"

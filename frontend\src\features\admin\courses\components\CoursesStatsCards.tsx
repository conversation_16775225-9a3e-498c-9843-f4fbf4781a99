import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Paper,
  Typography,
  useTheme,
  alpha,
} from '@mui/material';
import {
  FiBook as LibraryBooksIcon,
  FiCheckCircle as CheckCircleIcon,
  FiX as CancelIcon,
  FiUsers as PeopleIcon,
} from 'react-icons/fi';

interface Course {
  id: number;
  enrolled_count?: number;
  capacity?: number;
}

interface CoursesStatsCardsProps {
  courses: Course[];
  getTranslation: (key: string, defaultValue: string) => string;
}

const CoursesStatsCards: React.FC<CoursesStatsCardsProps> = ({ 
  courses, 
  getTranslation 
}) => {
  const theme = useTheme();

  const totalCourses = courses.length;
  const openCourses = courses.filter(
    course => (course.enrolled_count || 0) < (course.capacity || 30)
  ).length;
  const fullCourses = courses.filter(
    course => (course.enrolled_count || 0) >= (course.capacity || 30)
  ).length;
  const totalEnrollments = courses.reduce(
    (total, course) => total + (course.enrolled_count || 0),
    0
  );

  const stats = [
    {
      value: totalCourses,
      label: getTranslation('courses.totalCourses', 'Total Courses'),
      icon: LibraryBooksIcon,
      color: theme.palette.primary,
    },
    {
      value: openCourses,
      label: getTranslation('courses.openCourses', 'Open Courses'),
      icon: CheckCircleIcon,
      color: theme.palette.success,
    },
    {
      value: fullCourses,
      label: getTranslation('courses.fullCourses', 'Full Courses'),
      icon: CancelIcon,
      color: theme.palette.error,
    },
    {
      value: totalEnrollments,
      label: getTranslation('courses.totalEnrollments', 'Total Enrollments'),
      icon: PeopleIcon,
      color: theme.palette.info,
    },
  ];

  return (
    <Box
      sx={{
        display: 'flex',
        flexWrap: 'wrap',
        gap: 2,
        mb: 4,
      }}
    >
      {stats.map((stat, index) => (
        <Paper
          key={index}
          elevation={2}
          sx={{
            p: 2,
            flex: '1 1 200px',
            borderRadius: 2,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            background: `linear-gradient(135deg, ${alpha(stat.color.light, 0.2)}, ${alpha(stat.color.main, 0.1)})`,
            border: `1px solid ${alpha(stat.color.main, 0.1)}`,
            transition: 'all 0.3s',
            '&:hover': {
              transform: 'translateY(-5px)',
              boxShadow: 4,
            },
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <stat.icon
              size={40}
              style={{
                color: stat.color.main,
                marginRight: theme.spacing(1),
                opacity: 0.8,
              }}
            />
            <Typography variant="h3" sx={{ color: stat.color.main }} fontWeight="bold">
              {stat.value}
            </Typography>
          </Box>
          <Typography variant="subtitle1" color="text.secondary">
            {stat.label}
          </Typography>
        </Paper>
      ))}
    </Box>
  );
};

export default CoursesStatsCards;

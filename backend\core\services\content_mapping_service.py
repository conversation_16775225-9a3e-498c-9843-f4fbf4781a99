"""
Content mapping service module.

This module provides services for mapping content across different apps.
"""
from django.db.models import Q
from ..models import ContentMapping
from .content_reference_service import ContentReferenceService


class ContentMappingService:
    """Service for managing content mappings"""

    def __init__(self):
        self.reference_service = ContentReferenceService()

    def create_mapping(self, source_object, target_object, relationship_type):
        """Create a mapping between two content objects"""
        # Get or create references
        source_ref = self.reference_service.get_reference(source_object)
        if not source_ref:
            source_ref = self.reference_service.create_reference(
                source_object,
                self._get_category_for_object(source_object)
            )

        target_ref = self.reference_service.get_reference(target_object)
        if not target_ref:
            target_ref = self.reference_service.create_reference(
                target_object,
                self._get_category_for_object(target_object)
            )

        # Create the mapping
        mapping, created = ContentMapping.objects.get_or_create(
            source=source_ref,
            target=target_ref,
            relationship_type=relationship_type
        )
        return mapping

    def get_related_content(self, content_object, relationship_type=None):
        """Get related content for an object"""
        # Get the reference
        reference = self.reference_service.get_reference(content_object)
        if not reference:
            return []

        # Get mappings
        query = Q(source=reference) | Q(target=reference)
        if relationship_type:
            query &= Q(relationship_type=relationship_type)

        mappings = ContentMapping.objects.filter(query)

        # Extract related content
        related_content = []
        for mapping in mappings:
            if mapping.source == reference:
                related_content.append({
                    "object": self.reference_service.get_object(mapping.target),
                    "reference": mapping.target,
                    "relationship": mapping.relationship_type,
                    "direction": "outgoing"
                })
            else:
                related_content.append({
                    "object": self.reference_service.get_object(mapping.source),
                    "reference": mapping.source,
                    "relationship": mapping.relationship_type,
                    "direction": "incoming"
                })

        return related_content

    def _get_category_for_object(self, obj):
        """Get the appropriate category for an object based on its type"""
        obj_type = type(obj).__name__.lower()
        if "course" in obj_type:
            return "COURSE"
        elif "material" in obj_type:
            return "COURSE_MATERIAL"
        elif "interactive" in obj_type:
            return "INTERACTIVE"
        elif "generated" in obj_type:
            return "GENERATED"
        else:
            return "OTHER"
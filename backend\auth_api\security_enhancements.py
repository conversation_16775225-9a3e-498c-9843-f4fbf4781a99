"""
Enhanced Security Features for Authentication System

This module provides advanced security features:
- Password strength validation
- Multi-factor authentication support
- Session security enhancements
- Advanced threat detection
- Security audit logging
"""

import hashlib
import hmac
import logging
import re
import secrets
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.utils import timezone
from django.contrib.auth.hashers import check_password
from django.http import HttpRequest

try:
    import pyotp
except ImportError:
    pyotp = None

try:
    from cryptography.fernet import Fernet
except ImportError:
    Fernet = None

logger = logging.getLogger(__name__)
User = get_user_model()


@dataclass
class SecurityEvent:
    """Security event for audit logging"""
    event_type: str
    user_id: Optional[int]
    ip_address: str
    user_agent: str
    timestamp: datetime
    details: Dict
    risk_level: str  # LOW, MEDIUM, HIGH, CRITICAL


class PasswordSecurityValidator:
    """Advanced password security validation"""
    
    # Common passwords to reject
    COMMON_PASSWORDS = {
        'password', '123456', 'password123', 'admin', 'qwerty',
        'letmein', 'welcome', 'monkey', '1234567890', 'abc123'
    }
    
    # Patterns that indicate weak passwords
    WEAK_PATTERNS = [
        r'^(.)\1+$',  # All same character
        r'^\d+$',     # All numbers
        r'^[a-zA-Z]+$',  # All letters
        r'(012|123|234|345|456|567|678|789|890)',  # Sequential numbers
        r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)'  # Sequential letters
    ]
    
    @classmethod
    def validate_password_strength(cls, password: str, user_data: Dict = None) -> Tuple[bool, List[str]]:
        """
        Validate password strength with detailed feedback
        
        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        issues = []
        
        # Length check
        if len(password) < 12:
            issues.append("Password must be at least 12 characters long")
        
        # Character variety checks
        if not re.search(r'[a-z]', password):
            issues.append("Password must contain at least one lowercase letter")
        if not re.search(r'[A-Z]', password):
            issues.append("Password must contain at least one uppercase letter")
        if not re.search(r'\d', password):
            issues.append("Password must contain at least one number")
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            issues.append("Password must contain at least one special character")
        
        # Common password check
        if password.lower() in cls.COMMON_PASSWORDS:
            issues.append("Password is too common and easily guessable")
        
        # Weak pattern checks
        for pattern in cls.WEAK_PATTERNS:
            if re.search(pattern, password.lower()):
                issues.append("Password contains predictable patterns")
                break
        
        # Personal information check
        if user_data:
            personal_info = [
                user_data.get('username', '').lower(),
                user_data.get('email', '').split('@')[0].lower(),
                user_data.get('first_name', '').lower(),
                user_data.get('last_name', '').lower()
            ]
            for info in personal_info:
                if info and len(info) > 2 and info in password.lower():
                    issues.append("Password should not contain personal information")
                    break
        
        # Entropy check (simplified)
        entropy = cls._calculate_entropy(password)
        if entropy < 50:
            issues.append("Password lacks sufficient randomness")
        
        return len(issues) == 0, issues
    
    @classmethod
    def _calculate_entropy(cls, password: str) -> float:
        """Calculate password entropy (simplified version)"""
        charset_size = 0
        if re.search(r'[a-z]', password):
            charset_size += 26
        if re.search(r'[A-Z]', password):
            charset_size += 26
        if re.search(r'\d', password):
            charset_size += 10
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            charset_size += 32
        
        if charset_size == 0:
            return 0
        
        import math
        return len(password) * math.log2(charset_size)


class TwoFactorAuth:
    """Two-factor authentication implementation"""
    
    @staticmethod
    def generate_secret() -> str:
        """Generate a new TOTP secret"""
        if pyotp is None:
            raise ImportError("pyotp is required for 2FA functionality")
        return pyotp.random_base32()
    
    @staticmethod
    def generate_qr_code_url(user_email: str, secret: str) -> str:
        """Generate QR code URL for TOTP setup"""
        if pyotp is None:
            raise ImportError("pyotp is required for 2FA functionality")
        totp = pyotp.TOTP(secret)
        return totp.provisioning_uri(
            name=user_email,
            issuer_name="North Star University"
        )
    
    @staticmethod
    def verify_totp(secret: str, token: str, window: int = 1) -> bool:
        """Verify TOTP token with time window tolerance"""
        if pyotp is None:
            return False
        totp = pyotp.TOTP(secret)
        return totp.verify(token, valid_window=window)
    
    @staticmethod
    def generate_backup_codes(count: int = 10) -> List[str]:
        """Generate backup codes for 2FA recovery"""
        return [secrets.token_hex(4).upper() for _ in range(count)]


class SessionSecurity:
    """Enhanced session security management"""
    
    @staticmethod
    def generate_session_token() -> str:
        """Generate cryptographically secure session token"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def create_session_fingerprint(request: HttpRequest) -> str:
        """Create session fingerprint for additional security"""
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        accept_language = request.META.get('HTTP_ACCEPT_LANGUAGE', '')
        accept_encoding = request.META.get('HTTP_ACCEPT_ENCODING', '')
        
        fingerprint_data = f"{user_agent}:{accept_language}:{accept_encoding}"
        return hashlib.sha256(fingerprint_data.encode()).hexdigest()
    
    @staticmethod
    def validate_session_fingerprint(request: HttpRequest, stored_fingerprint: str) -> bool:
        """Validate session fingerprint"""
        current_fingerprint = SessionSecurity.create_session_fingerprint(request)
        return hmac.compare_digest(current_fingerprint, stored_fingerprint)
    
    @staticmethod
    def detect_session_hijacking(request: HttpRequest, user_id: int) -> bool:
        """Detect potential session hijacking"""
        cache_key = f"session_security:{user_id}"
        stored_data = cache.get(cache_key)
        
        if not stored_data:
            # First time - store current data
            current_data = {
                'ip': SessionSecurity._get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'fingerprint': SessionSecurity.create_session_fingerprint(request)
            }
            cache.set(cache_key, current_data, timeout=3600)
            return False
        
        current_ip = SessionSecurity._get_client_ip(request)
        current_ua = request.META.get('HTTP_USER_AGENT', '')
        current_fingerprint = SessionSecurity.create_session_fingerprint(request)
        
        # Check for suspicious changes
        ip_changed = current_ip != stored_data['ip']
        ua_changed = current_ua != stored_data['user_agent']
        fingerprint_changed = current_fingerprint != stored_data['fingerprint']
        
        # If multiple factors changed, it's suspicious
        changes = sum([ip_changed, ua_changed, fingerprint_changed])
        if changes >= 2:
            logger.warning(f"Potential session hijacking detected for user {user_id}")
            return True
        
        # Update stored data
        cache.set(cache_key, {
            'ip': current_ip,
            'user_agent': current_ua,
            'fingerprint': current_fingerprint
        }, timeout=3600)
        return False
    
    @staticmethod
    def _get_client_ip(request: HttpRequest) -> str:
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ThreatDetection:
    """Advanced threat detection system"""
    
    @staticmethod
    def detect_brute_force(ip_address: str, username: str = None) -> Dict[str, any]:
        """Detect brute force attacks"""
        # Check IP-based attempts
        ip_key = f"brute_force_ip:{ip_address}"
        ip_attempts = cache.get(ip_key, 0)
        
        # Check username-based attempts
        username_attempts = 0
        if username:
            username_key = f"brute_force_user:{username}"
            username_attempts = cache.get(username_key, 0)
        
        return {
            'ip_attempts': ip_attempts,
            'username_attempts': username_attempts,
            'is_brute_force': ip_attempts > 10 or username_attempts > 5,
            'risk_level': 'HIGH' if ip_attempts > 10 or username_attempts > 5 else 'MEDIUM'
        }
    
    @staticmethod
    def detect_credential_stuffing(request: HttpRequest) -> bool:
        """Detect credential stuffing attacks"""
        # Check for rapid login attempts from same IP
        ip = SessionSecurity._get_client_ip(request)
        cache_key = f"credential_stuffing:{ip}"
        attempts = cache.get(cache_key, [])
        current_time = time.time()
        
        # Remove attempts older than 5 minutes
        attempts = [t for t in attempts if current_time - t < 300]
        attempts.append(current_time)
        cache.set(cache_key, attempts, timeout=300)
        
        # If more than 20 attempts in 5 minutes, it's likely credential stuffing
        return len(attempts) > 20


class SecurityAuditLogger:
    """Comprehensive security audit logging"""
    
    @staticmethod
    def log_security_event(event: SecurityEvent):
        """Log security event with structured data"""
        log_data = {
            'event_type': event.event_type,
            'user_id': event.user_id,
            'ip_address': event.ip_address,
            'user_agent': event.user_agent,
            'timestamp': event.timestamp.isoformat(),
            'details': event.details,
            'risk_level': event.risk_level
        }
        
        # Log to Django logger
        if event.risk_level in ['HIGH', 'CRITICAL']:
            logger.error(f"Security Event: {log_data}")
        elif event.risk_level == 'MEDIUM':
            logger.warning(f"Security Event: {log_data}")
        else:
            logger.info(f"Security Event: {log_data}")
        
        # Store in cache for security dashboard
        cache_key = f"security_events:{event.timestamp.date()}"
        events = cache.get(cache_key, [])
        events.append(log_data)
        cache.set(cache_key, events, timeout=86400 * 7)  # Keep for 7 days
    
    @staticmethod
    def log_login_attempt(request: HttpRequest, username: str, success: bool, user_id: int = None):
        """Log login attempt"""
        event = SecurityEvent(
            event_type='LOGIN_ATTEMPT',
            user_id=user_id,
            ip_address=SessionSecurity._get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            timestamp=timezone.now(),
            details={'username': username, 'success': success, 'method': 'password'},
            risk_level='LOW' if success else 'MEDIUM'
        )
        SecurityAuditLogger.log_security_event(event)


# Global instances
password_validator = PasswordSecurityValidator()
session_security = SessionSecurity()
threat_detection = ThreatDetection()
security_logger = SecurityAuditLogger()

if pyotp:
    two_factor_auth = TwoFactorAuth()
else:
    two_factor_auth = None

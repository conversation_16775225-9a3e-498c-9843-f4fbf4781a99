"""AIResilienceandErrorHandlingUtilitiesThismoduleprovidesimprovederrorhandlingretrylogicandfallbackmechanisms."""importasyncioimport loggingimport timefromtypingimportAnyCallableDictOptionalUnionfromfunctoolsimportwrapsfromenumimportEnumimport randomlogger=logging.getLogger(__name__)classAIErrorType(Enum):"""TypesofAIserviceerrors"""API_KEY_INVALID="api_key_invalid"RATE_LIMIT="rate_limit"TIMEOUT="timeout"NETWORK_ERROR="network_error"CONTENT_FILTER="content_filter"QUOTA_EXCEEDED="quota_exceeded"UNKNOWN="unknown"classAIServiceException(Exception):"""EnhancedAIserviceexceptionwitherrorcategorization"""def__init__(selfmessage:strerror_type:AIErrorType=AIErrorType.UNKNOWNretry_after:Optional[int]=Noneoriginal_error:Optional[Exception]=None):super().__init__(message)self.error_type=error_typeself.retry_after=retry_afterself.original_error=original_errorself.timestamp=time.time()classRetryConfig:"""Configurationforretrylogic"""def__init__(selfmax_retries:int=3base_delay:float=1.0max_delay:float=60.0exponential_base:float=2.0):self.max_retries=max_retriesself.base_delay=base_delayself.max_delay=max_delayself.exponential_base=exponential_basedefcategorize_error(error:Exception)->AIErrorType:"""Categorizeerrorsforappropriatehandling"""error_str=str(error).lower()if"apikey"inerror_stror"unauthorized"inerror_str:returnAIErrorType.API_KEY_INVALIDelif"ratelimit"inerror_stror"toomanyrequests"inerror_str:returnAIErrorType.RATE_LIMITelif"timeout"inerror_str:returnAIErrorType.TIMEOUTelif"network"inerror_stror"connection"inerror_str:returnAIErrorType.NETWORK_ERRORelif"contentfilter"inerror_stror"safety"inerror_str:returnAIErrorType.CONTENT_FILTERelif"quota"inerror_stror"limitexceeded"inerror_str:returnAIErrorType.QUOTA_EXCEEDEDelse:returnAIErrorType.UNKNOWNdefwith_retry(retry_config:Optional[RetryConfig]=None):"""DecoratorforaddingretrylogictoAIservicecalls"""ifretry_configisNone:retry_config=RetryConfig()defdecorator(func:Callable)->Callable:@wraps(func)defwrapper(*args**kwargs):last_exception=Noneforattemptinrange(retry_config.max_retries+1):try:returnfunc(*args**kwargs)exceptExceptionase:last_exception=eerror_type=categorize_error(e)#Don'tretrycertainerrortypesiferror_typein[AIErrorType.API_KEY_INVALIDAIErrorType.CONTENT_FILTER]:raiseAIServiceException(f"Non-retryableerror:{str(e)}"error_type=error_typeoriginal_error=e)#Don'tretryonlastattemptifattempt==retry_config.max_retries:break#Calculatedelaywithexponentialbackoffandjitterdelay=min(retry_config.base_delay*(retry_config.exponential_base**attempt)retry_config.max_delay)#Addjittertopreventthunderingherddelay*=(0.5+random.random()*0.5)logger.warning(f"AIservicecallfailed(attempt{attempt+1}/{retry_config.max_retries+1}):{e}."f"Retryingin{delay:.2f}seconds...")time.sleep(delay)#Ifwegethereallretriesfailederror_type=categorize_error(last_exception)raiseAIServiceException(f"AIservicecallfailedafter{retry_config.max_retries+1}attempts:{str(last_exception)}"error_type=error_typeoriginal_error=last_exception)returnwrapperreturndecoratorclassFallbackManager:"""ManagesfallbackresponseswhenAIservicesfail"""def__init__(self):self.fallback_responses={'general':"IapologizebutI'mexperiencingtechnicaldifficulties.Pleasetryagainlater."'arabic':"أعتذر،أواجهصعوباتتقنيةحالياً.يرجىالمحاولةمرةأخرىلاحقاً."'tutoring':"I'munabletoprovidetutoringassistancerightnow.Pleasecontactyourinstructorortryagainlater."'assessment':"Assessmentanalysisistemporarilyunavailable.Pleasereviewmanuallyortryagainlater."'course_generation':"Coursecontentgenerationistemporarilyunavailable.Pleasetryagainlater."}defget_fallback_response(selfcontext:Dict[strAny])->str:"""Getappropriatefallbackresponsebasedoncontext"""service_type=context.get('service_type''general')language=context.get('language''english')iflanguage=='arabic'and'arabic'inself.fallback_responses:returnself.fallback_responses['arabic']returnself.fallback_responses.get(service_typeself.fallback_responses['general'])classCircuitBreaker:"""CircuitbreakerpatternforAIservices"""def__init__(selffailure_threshold:int=5recovery_timeout:int=60):self.failure_threshold=failure_thresholdself.recovery_timeout=recovery_timeoutself.failure_count=0self.last_failure_time=Noneself.state='closed'#closedopenhalf-opendefcall(selffunc:Callable*args**kwargs):"""Executefunctionwithcircuitbreakerprotection"""ifself.state=='open':iftime.time()-self.last_failure_time>self.recovery_timeout:self.state='half-open'logger.info("Circuitbreakermovingtohalf-openstate")else:raiseAIServiceException("AIserviceistemporarilyunavailable(circuitbreakeropen)"error_type=AIErrorType.UNKNOWN)try:result=func(*args**kwargs)ifself.state=='half-open':self.state='closed'self.failure_count=0logger.info("Circuitbreakerclosed-servicerecovered")returnresultexceptExceptionase:self.failure_count+=1self.last_failure_time=time.time()ifself.failure_count>=self.failure_threshold:self.state='open'logger.error(f"Circuitbreakeropenedafter{self.failure_count}failures")raisee#Globalinstancesfallback_manager=FallbackManager()circuit_breaker=CircuitBreaker()
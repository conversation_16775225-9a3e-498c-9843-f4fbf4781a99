"""SecureAuthenticationViewswithEnhancedSecurityFeaturesThismoduleprovidesauthenticationviewswithcomprehensivesecurity:-Advancedratelimitingandthreatdetection-Passwordstrengthvalidation-Two-factorauthentication-Sessionsecurity-Securityauditlogging"""import loggingfromdatetimeimport timedeltafrom django.contrib.authimportauthenticateget_user_modelfrom django.utilsimport timezonefrom django.httpimportJsonResponsefrom django.views.decorators.csrfimportcsrf_exemptfrom django.utils.decoratorsimportmethod_decoratorfromrest_frameworkimportstatusfromrest_framework.decoratorsimportapi_viewpermission_classesfromrest_framework.permissionsimportAllowAnyIsAuthenticatedfromrest_framework.responseimportResponsefromrest_framework.viewsimportAPIViewfromrest_framework_simplejwt.tokensimportRefreshTokenfromrest_framework_simplejwt.viewsimportTokenObtainPairViewfrom.security_enhancementsimport(password_validatortwo_factor_authsession_securitythreat_detectionsecurity_loggercsrf_protectiontoken_managerSecurityEvent)from.serializersimportCustomTokenObtainPairSerializerUserSerializerlogger=logging.getLogger(__name__)User=get_user_model()classSecureTokenObtainPairView(TokenObtainPairView):"""Enhancedtokenviewwithcomprehensivesecurity"""serializer_class=CustomTokenObtainPairSerializerdefpost(selfrequest*args**kwargs):"""Secureloginwiththreatdetectionandauditlogging"""username=request.data.get('username''')password=request.data.get('password''')totp_token=request.data.get('totp_token''')#Logloginattemptsecurity_logger.log_login_attempt(requestusernameFalse)#Threatdetectionbrute_force_check=threat_detection.detect_brute_force(session_security._get_client_ip(request)username)ifbrute_force_check['is_brute_force']:security_logger.log_suspicious_activity(request'BRUTE_FORCE_ATTACK'{'attempts':brute_force_check})returnJsonResponse({'error':'Toomanyfailedattempts.Accounttemporarilylocked.''retry_after':1800#30minutes}status=429)#Checkforcredentialstuffingifthreat_detection.detect_credential_stuffing(request):security_logger.log_suspicious_activity(request'CREDENTIAL_STUFFING'{'ip':session_security._get_client_ip(request)})returnJsonResponse({'error':'Suspiciousactivitydetected.Pleasetryagainlater.''retry_after':300#5minutes}status=429)#Authenticateuseruser=authenticate(username=usernamepassword=password)ifnotuser:returnJsonResponse({'error':'Invalidcredentials'}status=401)#Checkifuserrequires2FAifhasattr(user'profile')andgetattr(user.profile'totp_secret'None):ifnottotp_token:returnJsonResponse({'error':'Two-factorauthenticationrequired''requires_2fa':True}status=200)ifnottwo_factor_auth.verify_totp(user.profile.totp_secrettotp_token):security_logger.log_suspicious_activity(request'INVALID_2FA_TOKEN'{'user_id':user.id'username':username}user_id=user.id)returnJsonResponse({'error':'Invalidtwo-factorauthenticationcode'}status=401)#Analyzeloginpatternsforanomaliespattern_analysis=threat_detection.analyze_login_patterns(user.idrequest)ifpattern_analysis['risk_level']=='HIGH':security_logger.log_suspicious_activity(request'UNUSUAL_LOGIN_PATTERN'pattern_analysisuser_id=user.id)#Couldrequireadditionalverificationhere#Generatetokensrefresh=RefreshToken.for_user(user)access_token=str(refresh.access_token)refresh_token=str(refresh)#Storerefreshtokensecurelyexpires_at=timezone.now()+timedelta(days=7)token_manager.store_refresh_token(user.idrefresh_tokenexpires_at)#Createsessionfingerprintfingerprint=session_security.create_session_fingerprint(request)request.session['fingerprint']=fingerprint#Logsuccessfulloginsecurity_logger.log_login_attempt(requestusernameTrueuser.id)returnJsonResponse({'access':access_token'refresh':refresh_token'user':UserSerializer(user).data'requires_password_change':self._check_password_expiry(user)'security_score':self._calculate_security_score(user)})def_check_password_expiry(selfuser)->bool:"""Checkifpasswordneedstobechanged"""ifhasattr(user'profile')anduser.profile.password_changed_at:days_since_change=(timezone.now()-user.profile.password_changed_at).daysreturndays_since_change>90#90daysreturnFalsedef_calculate_security_score(selfuser)->int:"""Calculateusersecurityscore"""score=50#Basescore#2FAenabledifhasattr(user'profile')andgetattr(user.profile'totp_secret'None):score+=30#Recentpasswordchangeifhasattr(user'profile')anduser.profile.password_changed_at:days_since_change=(timezone.now()-user.profile.password_changed_at).daysifdays_since_change<30:score+=20returnmin(score100)classSecurePasswordChangeView(APIView):"""Securepasswordchangewithvalidation"""permission_classes=[IsAuthenticated]defpost(selfrequest):"""Changepasswordwithsecurityvalidation"""current_password=request.data.get('current_password')new_password=request.data.get('new_password')ifnotcurrent_passwordornotnew_password:returnResponse({'error':'Bothcurrentandnewpasswordsarerequired'}status=400)#Verifycurrentpasswordifnotrequest.user.check_password(current_password):security_logger.log_suspicious_activity(request'INVALID_CURRENT_PASSWORD'{'user_id':request.user.id}user_id=request.user.id)returnResponse({'error':'Currentpasswordisincorrect'}status=400)#Validatenewpasswordstrengthis_validissues=password_validator.validate_password_strength(new_password{'username':request.user.username'email':request.user.email'first_name':request.user.first_name'last_name':request.user.last_name})ifnotis_valid:returnResponse({'error':'Passworddoesnotmeetsecurityrequirements''issues':issues}status=400)#Checkifnewpasswordissameascurrentifrequest.user.check_password(new_password):returnResponse({'error':'Newpasswordmustbedifferentfromcurrentpassword'}status=400)#Changepasswordrequest.user.set_password(new_password)request.user.save()#Updateprofileifhasattr(request.user'profile'):request.user.profile.password_changed_at=timezone.now()request.user.profile.save()#Revokeallexistingrefreshtokenstoken_manager.revoke_refresh_token(request.user.id)#Logpasswordchangesecurity_logger.log_password_change(request.user.idrequest)returnResponse({'message':'Passwordchangedsuccessfully''security_score':self._calculate_security_score(request.user)})def_calculate_security_score(selfuser)->int:"""Calculateusersecurityscore"""score=50#Basescore#2FAenabledifhasattr(user'profile')andgetattr(user.profile'totp_secret'None):score+=30#Recentpasswordchangeifhasattr(user'profile')anduser.profile.password_changed_at:days_since_change=(timezone.now()-user.profile.password_changed_at).daysifdays_since_change<30:score+=20returnmin(score100)classTwoFactorSetupView(APIView):"""Two-factorauthenticationsetup"""permission_classes=[IsAuthenticated]defpost(selfrequest):"""Setup2FAforuser"""action=request.data.get('action')#'setup'or'verify'ifaction=='setup':#GeneratesecretandQRcodesecret=two_factor_auth.generate_secret()qr_url=two_factor_auth.generate_qr_code_url(request.user.emailsecret)backup_codes=two_factor_auth.generate_backup_codes()#Storesecrettemporarily(notactivateduntilverified)request.session['temp_totp_secret']=secretrequest.session['backup_codes']=backup_codesreturnResponse({'secret':secret'qr_url':qr_url'backup_codes':backup_codes})elifaction=='verify':#VerifyTOTPtokenandactivate2FAtoken=request.data.get('token')secret=request.session.get('temp_totp_secret')ifnotsecretornottoken:returnResponse({'error':'Missingsecretortoken'}status=400)iftwo_factor_auth.verify_totp(secrettoken):#Activate2FAifhasattr(request.user'profile'):request.user.profile.totp_secret=secretrequest.user.profile.backup_codes=request.session.get('backup_codes'[])request.user.profile.save()#Cleartemporarydatadelrequest.session['temp_totp_secret']if'backup_codes'inrequest.session:delrequest.session['backup_codes']security_logger.log_security_event(SecurityEvent(event_type='2FA_ENABLED'user_id=request.user.idip_address=session_security._get_client_ip(request)user_agent=request.META.get('HTTP_USER_AGENT''')timestamp=timezone.now()details={}risk_level='LOW'))returnResponse({'message':'Two-factorauthenticationenabledsuccessfully'})else:returnResponse({'error':'Invalidtoken'}status=400)returnResponse({'error':'Invalidaction'}status=400)@api_view(['POST'])@permission_classes([AllowAny])defsecure_register(request):"""Secureuserregistrationwithvalidation"""#Validatepasswordstrengthpassword=request.data.get('password')ifpassword:is_validissues=password_validator.validate_password_strength(passwordrequest.data)ifnotis_valid:returnResponse({'error':'Passworddoesnotmeetsecurityrequirements''issues':issues}status=400)#Checkforsuspiciousregistrationpatternsip=session_security._get_client_ip(request)cache_key=f"registration_attempts:{ip}"attempts=cache.get(cache_key0)ifattempts>5:#Max5registrationsperIPperhoursecurity_logger.log_suspicious_activity(request'EXCESSIVE_REGISTRATIONS'{'ip':ip'attempts':attempts})returnResponse({'error':'Toomanyregistrationattempts.Pleasetryagainlater.'}status=429)#Proceedwithregistrationserializer=UserSerializer(data=request.data)ifserializer.is_valid():user=serializer.save(role="STUDENT"is_active=False)#Incrementregistrationattemptscache.set(cache_keyattempts+1timeout=3600)#Logregistrationsecurity_logger.log_security_event(SecurityEvent(event_type='USER_REGISTRATION'user_id=user.idip_address=ipuser_agent=request.META.get('HTTP_USER_AGENT''')timestamp=timezone.now()details={'username':user.username'email':user.email}risk_level='LOW'))returnResponse({'message':'Registrationsuccessful.Pleasewaitforadminapproval.''user_id':user.id}status=201)returnResponse({'error':'Registrationfailed''details':serializer.errors}status=400)
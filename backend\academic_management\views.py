from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.contrib.auth.models import User
from django.db import transaction
from django.utils import timezone
from django.db.models import Q, F, Sum, Avg, Count
from decimal import Decimal
from datetime import datetime, timedelta

from .models import (
    AcademicTerm, TranscriptRecord, CoursePrerequisite, 
    CourseWaitlist, EnrollmentHistory, AcademicStanding
)
from .serializers import (
    AcademicTermSerializer, TranscriptRecordSerializer, CoursePrerequisiteSerializer,
    CourseWaitlistSerializer, EnrollmentHistorySerializer, AcademicStandingSerializer,
    StudentTranscriptSerializer, PrerequisiteCheckSerializer, WaitlistPositionSerializer,
    DetailedTranscriptRecordSerializer
)
from .services import (
    AcademicTermService, TranscriptService, PrerequisiteService,
    WaitlistService, GPAService, AcademicStandingService
)
from courses.models import Course


class AcademicTermViewSet(viewsets.ModelViewSet):
    """ViewSet for managing academic terms"""
    queryset = AcademicTerm.objects.all()
    serializer_class = AcademicTermSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        return queryset.order_by('-start_date')
    
    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get the current active academic term"""
        current_term = AcademicTermService.get_current_term()
        if current_term:
            serializer = self.get_serializer(current_term)
            return Response(serializer.data)
        return Response(
            {"message": "No active term found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    
    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        """Get upcoming academic terms"""
        upcoming_terms = AcademicTermService.get_upcoming_terms()
        serializer = self.get_serializer(upcoming_terms, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate an academic term"""
        term = self.get_object()
        AcademicTermService.activate_term(term)
        return Response({"message": f"Term {term.name} activated successfully"})


class TranscriptRecordViewSet(viewsets.ModelViewSet):
    """ViewSet for managing transcript records"""
    queryset = TranscriptRecord.objects.all()
    serializer_class = TranscriptRecordSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        student_id = self.request.query_params.get('student_id')
        course_id = self.request.query_params.get('course_id')
        term_id = self.request.query_params.get('term_id')
        
        if student_id:
            queryset = queryset.filter(student_id=student_id)
        if course_id:
            queryset = queryset.filter(course_id=course_id)
        if term_id:
            queryset = queryset.filter(term_id=term_id)
            
        return queryset.select_related('student', 'course', 'term').order_by('-term__start_date')
    
    @action(detail=False, methods=['get'])
    def student_transcript(self, request):
        """Get complete transcript for a student"""
        student_id = request.query_params.get('student_id')
        if not student_id:
            return Response(
                {"error": "student_id parameter is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        student = get_object_or_404(User, id=student_id)
        transcript_data = TranscriptService.generate_official_transcript(student)
        serializer = StudentTranscriptSerializer(transcript_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def gpa_calculation(self, request):
        """Calculate GPA for a student"""
        student_id = request.query_params.get('student_id')
        term_id = request.query_params.get('term_id')
        
        if not student_id:
            return Response(
                {"error": "student_id parameter is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        student = get_object_or_404(User, id=student_id)
        term = None
        if term_id:
            term = get_object_or_404(AcademicTerm, id=term_id)
        
        gpa_data = GPAService.calculate_gpa(student, term)
        return Response(gpa_data)


class CoursePrerequisiteViewSet(viewsets.ModelViewSet):
    """ViewSet for managing course prerequisites"""
    queryset = CoursePrerequisite.objects.all()
    serializer_class = CoursePrerequisiteSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        course_id = self.request.query_params.get('course_id')
        if course_id:
            queryset = queryset.filter(course_id=course_id)
        return queryset.select_related('course', 'prerequisite_course')
    
    @action(detail=False, methods=['post'])
    def check_prerequisites(self, request):
        """Check if student meets prerequisites for a course"""
        student_id = request.data.get('student_id')
        course_id = request.data.get('course_id')
        
        if not student_id or not course_id:
            return Response(
                {"error": "student_id and course_id are required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        student = get_object_or_404(User, id=student_id)
        course = get_object_or_404(Course, id=course_id)
        
        check_result = PrerequisiteService.check_prerequisites(student, course)
        serializer = PrerequisiteCheckSerializer(check_result)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def course_prerequisites(self, request):
        """Get all prerequisites for a course"""
        course_id = request.query_params.get('course_id')
        if not course_id:
            return Response(
                {"error": "course_id parameter is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        course = get_object_or_404(Course, id=course_id)
        prerequisites = PrerequisiteService.get_course_prerequisites(course)
        serializer = self.get_serializer(prerequisites, many=True)
        return Response(serializer.data)


class CourseWaitlistViewSet(viewsets.ModelViewSet):
    """ViewSet for managing course waitlists"""
    queryset = CourseWaitlist.objects.all()
    serializer_class = CourseWaitlistSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        student_id = self.request.query_params.get('student_id')
        course_id = self.request.query_params.get('course_id')
        term_id = self.request.query_params.get('term_id')
        status_filter = self.request.query_params.get('status')
        
        if student_id:
            queryset = queryset.filter(student_id=student_id)
        if course_id:
            queryset = queryset.filter(course_id=course_id)
        if term_id:
            queryset = queryset.filter(term_id=term_id)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
            
        return queryset.select_related('student', 'course', 'term').order_by('position')
    
    @action(detail=False, methods=['post'])
    def add_to_waitlist(self, request):
        """Add student to course waitlist"""
        student_id = request.data.get('student_id')
        course_id = request.data.get('course_id')
        term_id = request.data.get('term_id')
        
        if not all([student_id, course_id, term_id]):
            return Response(
                {"error": "student_id, course_id, and term_id are required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        student = get_object_or_404(User, id=student_id)
        course = get_object_or_404(Course, id=course_id)
        term = get_object_or_404(AcademicTerm, id=term_id)
        
        try:
            waitlist_entry = WaitlistService.add_to_waitlist(student, course, term)
            serializer = self.get_serializer(waitlist_entry)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def remove_from_waitlist(self, request, pk=None):
        """Remove student from waitlist"""
        waitlist_entry = self.get_object()
        WaitlistService.remove_from_waitlist(waitlist_entry)
        return Response({"message": "Successfully removed from waitlist"})
    
    @action(detail=False, methods=['get'])
    def student_position(self, request):
        """Get student's position in waitlist"""
        student_id = request.query_params.get('student_id')
        course_id = request.query_params.get('course_id')
        term_id = request.query_params.get('term_id')
        
        if not all([student_id, course_id, term_id]):
            return Response(
                {"error": "student_id, course_id, and term_id are required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        student = get_object_or_404(User, id=student_id)
        course = get_object_or_404(Course, id=course_id)
        term = get_object_or_404(AcademicTerm, id=term_id)
        
        position_info = WaitlistService.get_waitlist_position(student, course, term)
        if position_info:
            serializer = WaitlistPositionSerializer(position_info)
            return Response(serializer.data)
        
        return Response(
            {"message": "Student not found in waitlist"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    
    @action(detail=False, methods=['post'])
    def process_waitlist(self, request):
        """Process waitlist for a course when spots become available"""
        course_id = request.data.get('course_id')
        term_id = request.data.get('term_id')
        available_spots = request.data.get('available_spots', 1)
        
        if not all([course_id, term_id]):
            return Response(
                {"error": "course_id and term_id are required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        course = get_object_or_404(Course, id=course_id)
        term = get_object_or_404(AcademicTerm, id=term_id)
        
        notified_students = WaitlistService.process_waitlist(course, term, available_spots)
        return Response({
            "message": f"Processed waitlist for {course.name}",
            "notified_students": len(notified_students),
            "student_ids": [student.id for student in notified_students]
        })


class EnrollmentHistoryViewSet(viewsets.ModelViewSet):
    """ViewSet for managing enrollment history"""
    queryset = EnrollmentHistory.objects.all()
    serializer_class = EnrollmentHistorySerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        student_id = self.request.query_params.get('student_id')
        course_id = self.request.query_params.get('course_id')
        term_id = self.request.query_params.get('term_id')
        status_filter = self.request.query_params.get('status')
        
        if student_id:
            queryset = queryset.filter(student_id=student_id)
        if course_id:
            queryset = queryset.filter(course_id=course_id)
        if term_id:
            queryset = queryset.filter(term_id=term_id)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
            
        return queryset.select_related('student', 'course', 'term').order_by('-enrollment_date')
    
    @action(detail=False, methods=['get'])
    def student_history(self, request):
        """Get enrollment history for a student"""
        student_id = request.query_params.get('student_id')
        if not student_id:
            return Response(
                {"error": "student_id parameter is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        queryset = self.get_queryset().filter(student_id=student_id)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def course_enrollment_stats(self, request):
        """Get enrollment statistics for a course"""
        course_id = request.query_params.get('course_id')
        term_id = request.query_params.get('term_id')
        
        if not course_id:
            return Response(
                {"error": "course_id parameter is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        queryset = self.get_queryset().filter(course_id=course_id)
        if term_id:
            queryset = queryset.filter(term_id=term_id)
        
        stats = queryset.aggregate(
            total_enrollments=Count('id'),
            completed=Count('id', filter=Q(status='completed')),
            dropped=Count('id', filter=Q(status='dropped')),
            withdrawn=Count('id', filter=Q(status='withdrawn'))
        )
        
        return Response(stats)


class AcademicStandingViewSet(viewsets.ModelViewSet):
    """ViewSet for managing academic standings"""
    queryset = AcademicStanding.objects.all()
    serializer_class = AcademicStandingSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        student_id = self.request.query_params.get('student_id')
        term_id = self.request.query_params.get('term_id')
        standing = self.request.query_params.get('standing')
        
        if student_id:
            queryset = queryset.filter(student_id=student_id)
        if term_id:
            queryset = queryset.filter(term_id=term_id)
        if standing:
            queryset = queryset.filter(academic_standing=standing)
            
        return queryset.select_related('student', 'term').order_by('-term__start_date')
    
    @action(detail=False, methods=['post'])
    def calculate_standing(self, request):
        """Calculate academic standing for a student"""
        student_id = request.data.get('student_id')
        term_id = request.data.get('term_id')
        
        if not student_id:
            return Response(
                {"error": "student_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        student = get_object_or_404(User, id=student_id)
        term = None
        if term_id:
            term = get_object_or_404(AcademicTerm, id=term_id)
        
        standing = AcademicStandingService.calculate_academic_standing(student, term)
        serializer = self.get_serializer(standing)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def at_risk_students(self, request):
        """Get students who are at academic risk"""
        term_id = request.query_params.get('term_id')
        
        at_risk_students = AcademicStandingService.get_at_risk_students(term_id)
        serializer = self.get_serializer(at_risk_students, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def honors_students(self, request):
        """Get students on honor roll"""
        term_id = request.query_params.get('term_id')
        
        honors_students = AcademicStandingService.get_honors_students(term_id)
        serializer = self.get_serializer(honors_students, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def bulk_calculate(self, request):
        """Calculate academic standings for all students in a term"""
        term_id = request.data.get('term_id')
        
        if not term_id:
            return Response(
                {"error": "term_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        term = get_object_or_404(AcademicTerm, id=term_id)
        
        with transaction.atomic():
            calculated_standings = AcademicStandingService.calculate_all_standings(term)
        
        return Response({
            "message": f"Calculated academic standings for {len(calculated_standings)} students",
            "term": term.name,
            "calculated_count": len(calculated_standings)
        })


# Additional utility views
class AcademicReportsViewSet(viewsets.ViewSet):
    """ViewSet for academic reports and analytics"""
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def gpa_trends(self, request):
        """Get GPA trends over time"""
        student_id = request.query_params.get('student_id')
        if not student_id:
            return Response(
                {"error": "student_id parameter is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        student = get_object_or_404(User, id=student_id)
        standings = AcademicStanding.objects.filter(student=student).order_by('term__start_date')
        
        trends = []
        for standing in standings:
            trends.append({
                'term': standing.term.name,
                'term_gpa': float(standing.gpa),
                'cumulative_gpa': float(standing.cumulative_gpa),
                'credits_earned': standing.credits_earned,
                'academic_standing': standing.academic_standing
            })
        
        return Response(trends)
    
    @action(detail=False, methods=['get'])
    def department_statistics(self, request):
        """Get academic statistics by department"""
        term_id = request.query_params.get('term_id')
        
        # This would need to be implemented based on your department structure
        # For now, returning placeholder data
        return Response({
            "message": "Department statistics endpoint - implementation needed",
            "term_id": term_id
        })
    
    @action(detail=False, methods=['get'])
    def graduation_progress(self, request):
        """Get graduation progress for a student"""
        student_id = request.query_params.get('student_id')
        if not student_id:
            return Response(
                {"error": "student_id parameter is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        student = get_object_or_404(User, id=student_id)
        
        # Calculate graduation progress
        transcript_records = TranscriptRecord.objects.filter(student=student)
        total_credits = sum(record.course.credits for record in transcript_records if record.grade not in ['F', 'W', 'I'])
        
        # This would need more sophisticated logic based on degree requirements
        progress = {
            'student_id': student_id,
            'student_name': student.get_full_name(),
            'total_credits_earned': total_credits,
            'estimated_graduation_date': None,  # Would need degree requirement logic
            'completion_percentage': min(100, (total_credits / 120) * 100)  # Assuming 120 credits for degree
        }
        
        return Response(progress)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def my_academic_standing(request):
    """Get current user's academic standing"""
    try:
        # Get the current user's most recent academic standing
        standing = AcademicStanding.objects.filter(
            student=request.user
        ).select_related('term').order_by('-term__start_date').first()
        
        if not standing:
            return Response({
                "status": "success",
                "data": {
                    "standing": "GOOD",  # Default standing for new students
                    "gpa_at_time": 0.0,
                    "credits_at_time": 0,
                    "message": "No academic standing record found. Default standing applied."
                }
            })
        
        serializer = AcademicStandingSerializer(standing)
        return Response({
            "status": "success",
            "data": serializer.data
        })
        
    except Exception as e:
        return Response(
            {
                "status": "error",
                "error": str(e)
            }, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

"""
AI Services Module

Consolidated AI services for the North Star University platform.
This module provides a unified interface for all AI-related functionality.
"""

try:
    from .services import AIService, AIServiceError
except ImportError:
    # Fallback definitions
    class AIService:
        pass
    
    class AIServiceError(Exception):
        pass

try:
    from .prompts import PromptTemplates
except ImportError:
    # Fallback definition
    class PromptTemplates:
        pass

try:
    from .unified_service import unified_ai_service
except ImportError:
    # Fallback definition
    def unified_ai_service():
        return None

__all__ = [
    'AIService',
    'PromptTemplates',
    'unified_ai_service',
    'AIServiceError'
]

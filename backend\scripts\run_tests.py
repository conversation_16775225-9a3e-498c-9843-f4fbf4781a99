#!/usr/bin/env python
"""
Comprehensive test runner script for North Star University backend.
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings.test')

import django
django.setup()

from django.core.management import execute_from_command_line
from django.test.utils import get_runner
from django.conf import settings


class TestRunner:
    """Comprehensive test runner with multiple test types and reporting."""
    
    def __init__(self):
        self.start_time = time.time()
        self.results = {
            'unit': None,
            'integration': None,
            'api': None,
            'security': None,
            'performance': None,
            'coverage': None
        }
    
    def run_command(self, command, description):
        """Run a command and capture its result."""
        print(f"\n{'='*60}")
        print(f"Running: {description}")
        print(f"Command: {' '.join(command)}")
        print(f"{'='*60}")
        
        try:
            result = subprocess.run(
                command,
                cwd=backend_dir,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            if result.returncode == 0:
                print(f"✅ {description} - PASSED")
                return True
            else:
                print(f"❌ {description} - FAILED")
                print("STDOUT:", result.stdout)
                print("STDERR:", result.stderr)
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {description} - TIMEOUT")
            return False
        except Exception as e:
            print(f"💥 {description} - ERROR: {e}")
            return False
    
    def run_unit_tests(self):
        """Run unit tests with coverage."""
        print("\n🧪 Running Unit Tests...")
        
        # Run tests with coverage
        command = [
            'python', '-m', 'pytest',
            '-v',
            '--tb=short',
            '--cov=.',
            '--cov-report=term-missing',
            '--cov-report=html:htmlcov',
            '--cov-report=xml:coverage.xml',
            '--cov-fail-under=90',
            '-m', 'unit',
            '--maxfail=10',
            '-x'  # Stop on first failure for quick feedback
        ]
        
        self.results['unit'] = self.run_command(command, "Unit Tests")
        return self.results['unit']
    
    def run_integration_tests(self):
        """Run integration tests."""
        print("\n🔗 Running Integration Tests...")
        
        command = [
            'python', '-m', 'pytest',
            'tests/integration/',
            '-v',
            '--tb=short',
            '-m', 'integration',
            '--maxfail=5'
        ]
        
        self.results['integration'] = self.run_command(command, "Integration Tests")
        return self.results['integration']
    
    def run_api_tests(self):
        """Run API tests."""
        print("\n🌐 Running API Tests...")
        
        command = [
            'python', '-m', 'pytest',
            '-v',
            '--tb=short',
            '-m', 'api',
            '--maxfail=5'
        ]
        
        self.results['api'] = self.run_command(command, "API Tests")
        return self.results['api']
    
    def run_security_tests(self):
        """Run security tests."""
        print("\n🔒 Running Security Tests...")
        
        # Run security-specific tests
        command = [
            'python', '-m', 'pytest',
            '-v',
            '--tb=short',
            '-m', 'security',
            '--maxfail=3'
        ]
        
        self.results['security'] = self.run_command(command, "Security Tests")
        
        # Run bandit security scan
        print("\n🔍 Running Bandit Security Scan...")
        bandit_command = [
            'bandit', '-r', '.', 
            '-f', 'json',
            '-o', 'bandit-report.json',
            '--exclude', './venv,./env,./fresh_venv,./node_modules'
        ]
        
        bandit_result = self.run_command(bandit_command, "Bandit Security Scan")
        self.results['security'] = self.results['security'] and bandit_result
        
        return self.results['security']
    
    def run_performance_tests(self):
        """Run performance tests."""
        print("\n⚡ Running Performance Tests...")
        
        command = [
            'python', '-m', 'pytest',
            '-v',
            '--tb=short',
            '-m', 'performance',
            '--maxfail=3'
        ]
        
        self.results['performance'] = self.run_command(command, "Performance Tests")
        return self.results['performance']
    
    def check_coverage(self):
        """Check test coverage."""
        print("\n📊 Checking Test Coverage...")
        
        command = [
            'coverage', 'report',
            '--show-missing',
            '--fail-under=90'
        ]
        
        self.results['coverage'] = self.run_command(command, "Coverage Check")
        return self.results['coverage']
    
    def run_linting(self):
        """Run code linting."""
        print("\n🧹 Running Code Linting...")
        
        # Run flake8
        flake8_command = ['flake8', '.', '--max-line-length=100']
        flake8_result = self.run_command(flake8_command, "Flake8 Linting")
        
        # Run black check
        black_command = ['black', '--check', '.']
        black_result = self.run_command(black_command, "Black Code Formatting")
        
        # Run isort check
        isort_command = ['isort', '--check-only', '.']
        isort_result = self.run_command(isort_command, "Import Sorting")
        
        return flake8_result and black_result and isort_result
    
    def setup_test_environment(self):
        """Set up test environment."""
        print("\n🔧 Setting up test environment...")
        
        # Run migrations
        migrate_command = ['python', 'manage.py', 'migrate', '--settings=settings.test']
        migrate_result = self.run_command(migrate_command, "Database Migrations")
        
        # Collect static files
        collectstatic_command = [
            'python', 'manage.py', 'collectstatic', 
            '--noinput', '--settings=settings.test'
        ]
        collectstatic_result = self.run_command(collectstatic_command, "Collect Static Files")
        
        return migrate_result and collectstatic_result
    
    def generate_report(self):
        """Generate comprehensive test report."""
        end_time = time.time()
        duration = end_time - self.start_time
        
        print(f"\n{'='*80}")
        print("📋 COMPREHENSIVE TEST REPORT")
        print(f"{'='*80}")
        print(f"Total Duration: {duration:.2f} seconds")
        print(f"Start Time: {time.ctime(self.start_time)}")
        print(f"End Time: {time.ctime(end_time)}")
        print(f"{'='*80}")
        
        # Test results summary
        total_tests = len(self.results)
        passed_tests = sum(1 for result in self.results.values() if result is True)
        failed_tests = sum(1 for result in self.results.values() if result is False)
        skipped_tests = sum(1 for result in self.results.values() if result is None)
        
        print(f"Test Categories: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Skipped: {skipped_tests} ⏭️")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print(f"{'='*80}")
        
        # Detailed results
        for test_type, result in self.results.items():
            status = "✅ PASSED" if result is True else "❌ FAILED" if result is False else "⏭️ SKIPPED"
            print(f"{test_type.upper():15} | {status}")
        
        print(f"{'='*80}")
        
        # Overall result
        overall_success = all(result is not False for result in self.results.values())
        if overall_success:
            print("🎉 ALL TESTS PASSED! 🎉")
            return 0
        else:
            print("💥 SOME TESTS FAILED! 💥")
            return 1


def main():
    """Main function to run tests based on command line arguments."""
    parser = argparse.ArgumentParser(description='Comprehensive test runner for North Star University')
    parser.add_argument('--unit', action='store_true', help='Run unit tests')
    parser.add_argument('--integration', action='store_true', help='Run integration tests')
    parser.add_argument('--api', action='store_true', help='Run API tests')
    parser.add_argument('--security', action='store_true', help='Run security tests')
    parser.add_argument('--performance', action='store_true', help='Run performance tests')
    parser.add_argument('--coverage', action='store_true', help='Check test coverage')
    parser.add_argument('--lint', action='store_true', help='Run code linting')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    parser.add_argument('--quick', action='store_true', help='Run quick test suite (unit + lint)')
    parser.add_argument('--setup', action='store_true', help='Setup test environment only')
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    # Setup test environment
    if not runner.setup_test_environment():
        print("❌ Failed to setup test environment")
        return 1
    
    if args.setup:
        print("✅ Test environment setup complete")
        return 0
    
    # Determine which tests to run
    if args.all:
        tests_to_run = ['unit', 'integration', 'api', 'security', 'performance', 'coverage', 'lint']
    elif args.quick:
        tests_to_run = ['unit', 'lint']
    else:
        tests_to_run = []
        if args.unit: tests_to_run.append('unit')
        if args.integration: tests_to_run.append('integration')
        if args.api: tests_to_run.append('api')
        if args.security: tests_to_run.append('security')
        if args.performance: tests_to_run.append('performance')
        if args.coverage: tests_to_run.append('coverage')
        if args.lint: tests_to_run.append('lint')
    
    # If no specific tests specified, run quick suite
    if not tests_to_run:
        tests_to_run = ['unit', 'lint']
    
    # Run selected tests
    for test_type in tests_to_run:
        if test_type == 'unit':
            runner.run_unit_tests()
        elif test_type == 'integration':
            runner.run_integration_tests()
        elif test_type == 'api':
            runner.run_api_tests()
        elif test_type == 'security':
            runner.run_security_tests()
        elif test_type == 'performance':
            runner.run_performance_tests()
        elif test_type == 'coverage':
            runner.check_coverage()
        elif test_type == 'lint':
            runner.run_linting()
    
    # Generate final report
    return runner.generate_report()


if __name__ == '__main__':
    sys.exit(main())

from django.contrib.authimportget_user_modelfromrest_frameworkimportserializersfromrest_framework_simplejwt.serializersimportTokenObtainPairSerializerUser=get_user_model()classCustomTokenObtainPairSerializer(TokenObtainPairSerializer):defvalidate(selfattrs):try:#Firstvalidatecredentialsdata=super().validate(attrs)#Checkifuserexistsandisauthenticatedifnotself.user:raiseserializers.ValidationError({"detail":"Invalidcredentials.Pleasecheckyourusernameandpassword."})#Checkifuserisactive(approvedbyadmin)ifnotself.user.is_active:raiseserializers.ValidationError({"detail":"Youraccountispendingapproval.Pleasewaitforanadministratortoapproveyourregistration."})#Addextraresponsesheredata["user"]={"id":self.user.id"username":self.user.username"email":self.user.email"role":self.user.role"first_name":self.user.first_name"last_name":self.user.last_name"is_active":self.user.is_active"is_staff":self.user.is_staff"is_superuser":self.user.is_superuser}returndataexceptserializers.ValidationError:raiseexceptExceptionase:raiseserializers.ValidationError({"detail":"Anerroroccurredduringauthentication.Pleasetryagain."})classUserSerializer(serializers.ModelSerializer):#Addmotivationandlearning_pathaswrite-onlyfieldsthatarenotpartofthemodelmotivation=serializers.CharField(write_only=Truerequired=False)learning_path=serializers.CharField(write_only=Truerequired=False)classMeta:model=Userfields=["id""username""email""password""first_name""last_name""role""motivation""learning_path"]read_only_fields=["id"]extra_kwargs={"password":{"write_only":True}}defcreate(selfvalidated_data):#Removemotivationandlearning_pathfieldsasthey'renotpartofthemodellearning_path=validated_data.pop("learning_path""general")if"motivation"invalidated_data:validated_data.pop("motivation")password=validated_data.pop("password"None)user=super().create(validated_data)ifpassword:user.set_password(password)user.save()returnuserdefupdate(selfinstancevalidated_data):#Removemotivationfieldasit'snotpartofthemodelif"motivation"invalidated_data:validated_data.pop("motivation")password=validated_data.pop("password"None)user=super().update(instancevalidated_data)ifpassword:user.set_password(password)user.save()returnuser
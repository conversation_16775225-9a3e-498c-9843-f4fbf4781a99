import React, { useState, useEffect, lazy, Suspense } from 'react';
import {
  Box,
  Container,
  Tabs,
  Tab,
  Typography,
  Fade,
  Alert,
  Snackbar,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { RootState } from '../app/store';
import {
  School,
  AutoAwesome,
  Gamepad,
  Assessment,
  Timeline,
} from '../components/icons';
import { CourseListSkeleton } from '../components/courses/LazyLoadWrapper';

// Lazy load components for better code splitting
const UnifiedCourseList = lazy(() => import('../components/courses/UnifiedCourseList'));
const ImprovedCourseGenerator = lazy(() => import('../components/course-generator/ImprovedCourseGenerator'));

// Import services and utilities
import { courseService, ICourse } from '../services/courseService';
import { getLevelValue } from '../utils/levelUtils';

// Use the enhanced ICourse interface from the service
type Course = ICourse;

const PageContainer = styled(Container)(({ theme }) => ({
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(4),
  minHeight: '100vh',
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  '& .MuiTabs-indicator': {
    height: 3,
    borderRadius: '3px 3px 0 0',
    background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
  },
  '& .MuiTab-root': {
    textTransform: 'none',
    fontWeight: 600,
    fontSize: '1rem',
    minHeight: 64,
    '&.Mui-selected': {
      color: theme.palette.primary.main,
    },
  },
}));

const TabPanel = styled(Box)(() => ({
  minHeight: '60vh',
}));

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const CustomTabPanel: React.FC<TabPanelProps> = ({
  children,
  value,
  index,
}) => {
  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`course-tabpanel-${index}`}
      aria-labelledby={`course-tab-${index}`}
    >
      {value === index && (
        <Fade in timeout={300}>
          <TabPanel>{children}</TabPanel>
        </Fade>
      )}
    </div>
  );
};

const UnifiedCoursesPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Get user role from Redux store and navigation
  const { user } = useSelector((state: RootState) => state.auth);
  const userRole = user?.role?.toUpperCase() || 'STUDENT';
  const navigate = useNavigate();

  // EnhancedCourseList handles its own data fetching, so we don't need these methods

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleEnroll = async (courseId: number) => {
    try {
      await courseService.enrollInCourse(courseId);
      showSnackbar('Successfully enrolled in course!', 'success');
    } catch (err: any) {
      console.error('Enrollment error:', err);
      showSnackbar(
        err.message || 'Failed to enroll in course. Please try again.',
        'error'
      );
    }
  };

  const handleViewDetails = (courseId: number) => {
    // Navigate to course details page
    navigate(`/courses/${courseId}`);
  };

  const handleStartLearning = (courseId: number) => {
    // Navigate to course learning interface
    navigate(`/courses/${courseId}/learn`);
  };

  const handleTakeAssessment = (courseId: number) => {
    // Navigate to assessment page
    navigate(`/courses/${courseId}/assessment`);
  };

  const handleBookmark = async (courseId: number) => {
    try {
      // Note: EnhancedCourseList handles bookmark state internally
      showSnackbar('Bookmark updated!', 'success');
    } catch (err: any) {
      console.error('Bookmark error:', err);
      showSnackbar(
        err.message || 'Bookmark feature temporarily unavailable',
        'error'
      );
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error') => {
    setSnackbar({ open: true, message, severity });
  };

  const closeSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Define tabs based on user role
  const allTabs = [
    { label: 'All Courses', icon: <School />, id: 'courses' },
    {
      label: 'Course Generator',
      icon: <AutoAwesome />,
      id: 'generator',
      rolesAllowed: ['ADMIN', 'PROFESSOR'],
    },
    { label: 'Interactive Learning', icon: <Gamepad />, id: 'interactive' },
    { label: 'My Progress', icon: <Timeline />, id: 'progress' },
    { label: 'Assessments', icon: <Assessment />, id: 'assessments' },
  ];

  // Filter tabs based on user role
  const tabs = allTabs.filter(
    tab => !tab.rolesAllowed || tab.rolesAllowed.includes(userRole)
  );

  return (
    <PageContainer maxWidth='xl'>
      {/* Page Header */}
      <Box mb={4} textAlign='center'>
        <Typography variant='h3' fontWeight={700} gutterBottom>
          Course Management Hub
        </Typography>
        <Typography variant='h6' color='text.secondary'>
          {userRole === 'STUDENT'
            ? (() => {
                // Get student level for display
                let studentLevel = 1;
                if (user?.level) {
                  studentLevel = getLevelValue(user.level);
                } else if (user?.level_profile?.current_level) {
                  studentLevel = getLevelValue(
                    user.level_profile.current_level
                  );
                } else {
                  const storedLevel = localStorage.getItem('student_level');
                  if (storedLevel) {
                    studentLevel = parseInt(storedLevel, 10) || 1;
                  }
                }
                return `Discover courses for your level (Level ${studentLevel}) and advance your learning`;
              })()
            : 'Discover, create, and learn with our comprehensive course platform'}
        </Typography>
      </Box>

      {/* Navigation Tabs */}
      <StyledTabs
        value={activeTab}
        onChange={handleTabChange}
        variant='scrollable'
        scrollButtons='auto'
        allowScrollButtonsMobile
      >
        {tabs.map((tab, index) => (
          <Tab
            key={tab.id}
            label={tab.label}
            icon={tab.icon}
            iconPosition='start'
            id={`course-tab-${index}`}
            aria-controls={`course-tabpanel-${index}`}
          />
        ))}
      </StyledTabs>

      {/* Tab Panels */}
      {tabs.map((tab, index) => (
        <CustomTabPanel key={tab.id} value={activeTab} index={index}>
          {tab.id === 'courses' && (
            <Suspense fallback={<CourseListSkeleton />}>
              <UnifiedCourseList
                userRole={userRole as 'STUDENT' | 'PROFESSOR' | 'ADMIN'}
                filter="all"
                showFilters={true}
                onCourseAction={(action, courseId) => {
                  switch (action) {
                    case 'view':
                      handleViewDetails(courseId);
                      break;
                    case 'enroll':
                      handleEnroll(courseId);
                      break;
                    case 'start_learning':
                      handleStartLearning(courseId);
                      break;
                    case 'take_assessment':
                      handleTakeAssessment(courseId);
                      break;
                  }
                }}
                gridColumns={12}
              />
            </Suspense>
          )}

          {tab.id === 'generator' && (
            <Suspense fallback={<CourseListSkeleton />}>
              <ImprovedCourseGenerator />
            </Suspense>
          )}

          {tab.id === 'interactive' && (
            <Box textAlign='center' py={8}>
              <Box sx={{ fontSize: 80, color: 'primary.main', mb: 2 }}>🎮</Box>
              <Typography variant='h4' fontWeight={600} gutterBottom>
                Interactive Learning Coming Soon
              </Typography>
              <Typography variant='body1' color='text.secondary'>
                Enhanced interactive learning tools and features will be available
                here
              </Typography>
            </Box>
          )}

          {tab.id === 'progress' && (
            <Suspense fallback={<CourseListSkeleton />}>
              <UnifiedCourseList
                userRole={userRole as 'STUDENT' | 'PROFESSOR' | 'ADMIN'}
                filter="enrolled"
                showFilters={false}
                onCourseAction={(action, courseId) => {
                  switch (action) {
                    case 'view':
                      handleViewDetails(courseId);
                      break;
                    case 'start_learning':
                      handleStartLearning(courseId);
                      break;
                    case 'take_assessment':
                      handleTakeAssessment(courseId);
                      break;
                  }
                }}
                gridColumns={8}
              />
            </Suspense>
          )}

          {tab.id === 'assessments' && (
            <Box textAlign='center' py={8}>
              <Box sx={{ fontSize: 80, color: 'primary.main', mb: 2 }}>📊</Box>
              <Typography variant='h4' fontWeight={600} gutterBottom>
                Assessments Coming Soon
              </Typography>
              <Typography variant='body1' color='text.secondary'>
                Comprehensive assessment tools and analytics will be available
                here
              </Typography>
            </Box>
          )}
        </CustomTabPanel>
      ))}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={closeSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={closeSnackbar}
          severity={snackbar.severity}
          variant='filled'
          sx={{ borderRadius: '12px' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </PageContainer>
  );
};

export default UnifiedCoursesPage;

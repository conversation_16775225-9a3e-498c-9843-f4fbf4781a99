"""
Enhanced Admin Interface for Course Management

This module provides comprehensive admin interfaces for managing courses,
students, professors, and administrators with improved:
- Search and filtering capabilities
- Data validation and security
- Performance optimizations
- Clear data presentation
"""

from django.contrib import admin
from django.contrib.auth import get_user_model
from django.db.models import Count, Q, Avg
from django.http import HttpResponseRedirect
from django.shortcuts import render
from django.urls import path, reverse
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from django.contrib import messages
from django.contrib.admin import SimpleListFilter

from .models import Course, Department, Enrollment
from .models.course_enhanced import CourseEnhanced, CoursePrerequisite, CourseSection
from users.models import CustomUser
from grades.models import CourseGrade

User = get_user_model()


class EnrollmentCountFilter(SimpleListFilter):
    """Custom filter for enrollment counts"""
    title = _('Enrollment Status')
    parameter_name = 'enrollment_status'

    def lookups(self, request, model_admin):
        return (
            ('full', _('Full Courses')),
            ('available', _('Available Spots')),
            ('empty', _('No Enrollments')),
        )

    def queryset(self, request, queryset):
        if self.value() == 'full':
            return queryset.annotate(
                enrollment_count=Count('enrollments', filter=Q(enrollments__status='APPROVED'))
            ).filter(enrollment_count__gte=models.F('max_students'))
        
        if self.value() == 'available':
            return queryset.annotate(
                enrollment_count=Count('enrollments', filter=Q(enrollments__status='APPROVED'))
            ).filter(enrollment_count__lt=models.F('max_students'))
        
        if self.value() == 'empty':
            return queryset.annotate(
                enrollment_count=Count('enrollments', filter=Q(enrollments__status='APPROVED'))
            ).filter(enrollment_count=0)


class ActiveInstructorFilter(SimpleListFilter):
    """Filter for courses with active professor instructors"""
    title = _('Instructor Status')
    parameter_name = 'instructor_status'

    def lookups(self, request, model_admin):
        return (
            ('assigned', _('Has Instructor')),
            ('unassigned', _('No Instructor')),
            ('active_prof', _('Active Professor')),
        )

    def queryset(self, request, queryset):
        if self.value() == 'assigned':
            return queryset.filter(instructor__isnull=False)
        
        if self.value() == 'unassigned':
            return queryset.filter(instructor__isnull=True)
        
        if self.value() == 'active_prof':
            return queryset.filter(
                instructor__isnull=False,
                instructor__role='PROFESSOR',
                instructor__is_active=True
            )


class CoursePrerequisiteInline(admin.TabularInline):
    """Inline for managing course prerequisites"""
    model = CoursePrerequisite
    fk_name = 'target_course'
    extra = 0
    autocomplete_fields = ['prerequisite_course']


class CourseSectionInline(admin.StackedInline):
    """Inline for managing course sections"""
    model = CourseSection
    extra = 0
    fields = [
        'section_number', 'instructor', 'max_enrollment', 
        'meeting_times', 'is_active'
    ]
    autocomplete_fields = ['instructor']


@admin.register(CourseEnhanced)
class EnhancedCourseAdmin(admin.ModelAdmin):
    """
    Enhanced Course Admin with comprehensive management features
    """
    
    # Display configuration
    list_display = [
        'course_code_link', 'title_truncated', 'instructor_display',
        'department_display', 'enrollment_info', 'level_badges',
        'type_indicator', 'status_indicator', 'performance_indicator'
    ]
    
    list_display_links = ['course_code_link']
    
    list_filter = [
        'is_active', 'primary_type', 'semester', 'academic_year',
        'required_level', 'department', EnrollmentCountFilter,
        ActiveInstructorFilter, 'has_assessment', 'created_at'
    ]
    
    search_fields = [
        'course_code', 'title', 'description',
        'instructor__username', 'instructor__first_name', 'instructor__last_name',
        'department__name', 'department__code'
    ]
    
    autocomplete_fields = ['instructor', 'department']
    filter_horizontal = ['prerequisites', 'next_level_courses', 'skills_required', 'skills_developed']
    
    readonly_fields = [
        'created_at', 'updated_at', 'created_by', 'last_modified_by',
        'enrollment_statistics', 'performance_metrics_display'
    ]
    
    inlines = [CoursePrerequisiteInline, CourseSectionInline]
    
    # Fieldsets for organized display
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('course_code', 'title', 'description', 'credits')
        }),
        
        (_('Scheduling & Assignment'), {
            'fields': ('semester', 'academic_year', 'department', 'instructor'),
            'classes': ('wide',)
        }),
        
        (_('Course Configuration'), {
            'fields': (
                'primary_type', 'required_level', 'recommended_level',
                'max_students', 'typical_session_duration', 'has_assessment'
            ),
            'classes': ('collapse',)
        }),
        
        (_('Relationships'), {
            'fields': ('prerequisites', 'next_level_courses'),
            'classes': ('collapse',)
        }),
        
        (_('Skills'), {
            'fields': ('skills_required', 'skills_developed'),
            'classes': ('collapse',)
        }),
        
        (_('Status & Availability'), {
            'fields': ('is_active', 'start_date', 'end_date'),
            'classes': ('collapse',)
        }),
        
        (_('Statistics'), {
            'fields': ('enrollment_statistics', 'performance_metrics_display'),
            'classes': ('collapse',)
        }),
        
        (_('Metadata'), {
            'fields': ('created_by', 'last_modified_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    # Actions
    actions = [
        'activate_courses', 'deactivate_courses', 'duplicate_course',
        'generate_performance_report', 'bulk_update_instructor'
    ]
    
    # Custom display methods
    def course_code_link(self, obj):
        """Display course code with link to change page"""
        url = reverse('admin:courses_courseenhanced_change', args=[obj.pk])
        return format_html('<a href="{}" title="Edit course"><strong>{}</strong></a>', url, obj.course_code)
    course_code_link.short_description = _('Code')
    course_code_link.admin_order_field = 'course_code'
    
    def title_truncated(self, obj):
        """Display truncated title with tooltip"""
        if len(obj.title) > 30:
            return format_html(
                '<span title="{}">{}</span>',
                obj.title,
                obj.title[:30] + '...'
            )
        return obj.title
    title_truncated.short_description = _('Title')
    title_truncated.admin_order_field = 'title'
    
    def instructor_display(self, obj):
        """Display instructor with role validation"""
        if obj.instructor:
            if obj.instructor.role == 'PROFESSOR':
                return format_html(
                    '<span style="color: green;">👨‍🏫 {}</span>',
                    obj.instructor.get_full_name() or obj.instructor.username
                )
            else:
                return format_html(
                    '<span style="color: red;" title="Invalid role: {}">⚠️ {}</span>',
                    obj.instructor.role,
                    obj.instructor.get_full_name() or obj.instructor.username
                )
        return format_html('<span style="color: #ccc;">Unassigned</span>')
    instructor_display.short_description = _('Instructor')
    instructor_display.admin_order_field = 'instructor__last_name'
    
    def department_display(self, obj):
        """Display department with code"""
        if obj.department:
            return format_html(
                '<span title="{}">{}</span>',
                obj.department.name,
                obj.department.code
            )
        return format_html('<span style="color: #ccc;">-</span>')
    department_display.short_description = _('Dept')
    department_display.admin_order_field = 'department__code'
    
    def enrollment_info(self, obj):
        """Display enrollment information with capacity"""
        enrolled = obj.get_enrollment_count()
        capacity = obj.max_students
        percentage = (enrolled / capacity * 100) if capacity > 0 else 0
        
        if percentage >= 100:
            color = 'red'
            icon = '🔴'
        elif percentage >= 80:
            color = 'orange'
            icon = '🟡'
        else:
            color = 'green'
            icon = '🟢'
        
        return format_html(
            '<span style="color: {};" title="{}% capacity">{} {}/{}</span>',
            color, round(percentage, 1), icon, enrolled, capacity
        )
    enrollment_info.short_description = _('Enrolled')
    
    def level_badges(self, obj):
        """Display level requirements as badges"""
        req_level = obj.required_level
        rec_level = obj.recommended_level
        
        badges = []
        if req_level:
            badges.append(f'<span class="level-badge req" title="Required Level">R{req_level}</span>')
        if rec_level and rec_level != req_level:
            badges.append(f'<span class="level-badge rec" title="Recommended Level">🎯{rec_level}</span>')
        
        return format_html(' '.join(badges)) if badges else '-'
    level_badges.short_description = _('Levels')
    
    def type_indicator(self, obj):
        """Display course type with icons"""
        type_icons = {
            'STANDARD': '📖',
            'INTERACTIVE': '🎮',
            'AI_GENERATED': '🤖',
            'HYBRID': '🔄'
        }
        icon = type_icons.get(obj.primary_type, '❓')
        return format_html(
            '<span title="{}">{}</span>',
            obj.get_primary_type_display(),
            icon
        )
    type_indicator.short_description = _('Type')
    
    def status_indicator(self, obj):
        """Display course status with indicators"""
        if obj.is_active:
            return format_html('<span style="color: green;">✅ Active</span>')
        else:
            return format_html('<span style="color: red;">❌ Inactive</span>')
    status_indicator.short_description = _('Status')
    status_indicator.admin_order_field = 'is_active'
    
    def performance_indicator(self, obj):
        """Display performance indicator"""
        try:
            metrics = obj.get_performance_metrics()
            completion_rate = metrics.get('completion_rate', 0)
            
            if completion_rate >= 80:
                return format_html('<span style="color: green;">📈 {}%</span>', round(completion_rate, 1))
            elif completion_rate >= 60:
                return format_html('<span style="color: orange;">📊 {}%</span>', round(completion_rate, 1))
            else:
                return format_html('<span style="color: red;">📉 {}%</span>', round(completion_rate, 1))
        except:
            return format_html('<span style="color: #ccc;">-</span>')
    performance_indicator.short_description = _('Performance')
    
    def enrollment_statistics(self, obj):
        """Display detailed enrollment statistics"""
        if not obj.pk:
            return "Save the course first to see statistics"
        
        enrolled = obj.get_enrollment_count()
        capacity = obj.max_students
        available = obj.get_available_spots()
        
        return format_html(
            '<div style="background: #f0f0f0; padding: 10px; border-radius: 5px;">'
            '<strong>Enrollment Statistics:</strong><br>'
            '• Currently Enrolled: {}<br>'
            '• Total Capacity: {}<br>'
            '• Available Spots: {}<br>'
            '• Utilization: {}%<br>'
            '</div>',
            enrolled, capacity, available,
            round((enrolled / capacity * 100) if capacity > 0 else 0, 1)
        )
    enrollment_statistics.short_description = _('Enrollment Statistics')
    
    def performance_metrics_display(self, obj):
        """Display performance metrics"""
        if not obj.pk:
            return "Save the course first to see metrics"
        
        try:
            metrics = obj.get_performance_metrics()
            return format_html(
                '<div style="background: #f0f0f0; padding: 10px; border-radius: 5px;">'
                '<strong>Performance Metrics:</strong><br>'
                '• Total Enrolled: {}<br>'
                '• Completion Rate: {}%<br>'
                '• Average Grade: {}<br>'
                '• Dropout Rate: {}%<br>'
                '</div>',
                metrics['total_enrolled'],
                round(metrics['completion_rate'], 1),
                metrics['average_grade'] or 'N/A',
                round(metrics['dropout_rate'], 1)
            )
        except Exception as e:
            return format_html('<span style="color: red;">Error loading metrics: {}</span>', str(e))
    performance_metrics_display.short_description = _('Performance Metrics')
    
    # Admin actions
    def activate_courses(self, request, queryset):
        """Bulk activate selected courses"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} courses activated successfully.')
    activate_courses.short_description = _('Activate selected courses')
    
    def deactivate_courses(self, request, queryset):
        """Bulk deactivate selected courses"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} courses deactivated successfully.')
    deactivate_courses.short_description = _('Deactivate selected courses')
    
    def duplicate_course(self, request, queryset):
        """Duplicate selected courses for next semester"""
        if queryset.count() > 5:
            self.message_user(request, 'Please select 5 or fewer courses to duplicate.', level=messages.WARNING)
            return
        
        duplicated = 0
        for course in queryset:
            # Create a duplicate with next semester
            new_course = CourseEnhanced.objects.get(pk=course.pk)
            new_course.pk = None
            new_course.course_code = f"{course.course_code}_COPY"
            new_course.title = f"{course.title} (Copy)"
            new_course._current_user = request.user
            new_course.save()
            duplicated += 1
        
        self.message_user(request, f'{duplicated} courses duplicated successfully.')
    duplicate_course.short_description = _('Duplicate selected courses')
    
    # Optimization methods
    def get_queryset(self, request):
        """Optimize queryset with select_related and prefetch_related"""
        queryset = super().get_queryset(request)
        return queryset.select_related(
            'instructor', 'department', 'created_by', 'last_modified_by'
        ).prefetch_related(
            'enrollments', 'prerequisites', 'skills_required'
        )
    
    def save_model(self, request, obj, form, change):
        """Override save to track user changes"""
        obj._current_user = request.user
        super().save_model(request, obj, form, change)
    
    # Custom admin views
    def get_urls(self):
        """Add custom URLs for admin views"""
        urls = super().get_urls()
        custom_urls = [
            path(
                '<int:course_id>/performance-report/',
                self.admin_site.admin_view(self.performance_report_view),
                name='course_performance_report'
            ),
        ]
        return custom_urls + urls
    
    def performance_report_view(self, request, course_id):
        """Custom view for detailed performance report"""
        course = self.get_object(request, course_id)
        if course is None:
            return HttpResponseRedirect(reverse('admin:courses_courseenhanced_changelist'))
        
        context = {
            'course': course,
            'metrics': course.get_performance_metrics(),
            'enrollments': course.enrollments.select_related('user').all(),
            'title': f'Performance Report - {course.course_code}',
        }
        return render(request, 'admin/courses/performance_report.html', context)
    
    # CSS for custom styling
    class Media:
        css = {
            'all': ('admin/css/course_admin.css',)
        }
        js = ('admin/js/course_admin.js',)


@admin.register(Department)
class EnhancedDepartmentAdmin(admin.ModelAdmin):
    """Enhanced Department Admin"""
    
    list_display = [
        'code', 'name', 'head_display', 'course_count',
        'professor_count', 'student_count', 'contact_info'
    ]
    
    list_filter = ['created_at']
    search_fields = ['name', 'code', 'description', 'head__username']
    autocomplete_fields = ['head']
    
    readonly_fields = ['created_at', 'updated_at', 'statistics_display']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('code', 'name', 'description', 'head')
        }),
        (_('Contact Information'), {
            'fields': ('email', 'phone', 'website', 'building', 'room'),
            'classes': ('collapse',)
        }),
        (_('Statistics'), {
            'fields': ('statistics_display',),
            'classes': ('collapse',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def head_display(self, obj):
        """Display department head with validation"""
        if obj.head:
            if obj.head.role == 'PROFESSOR':
                return format_html(
                    '<span style="color: green;">👨‍🏫 {}</span>',
                    obj.head.get_full_name() or obj.head.username
                )
            else:
                return format_html(
                    '<span style="color: orange;" title="Role: {}">⚠️ {}</span>',
                    obj.head.role,
                    obj.head.get_full_name() or obj.head.username
                )
        return format_html('<span style="color: #ccc;">No Head Assigned</span>')
    head_display.short_description = _('Department Head')
    
    def course_count(self, obj):
        """Display number of courses in department"""
        count = obj.get_courses_count()
        active_count = obj.get_active_courses_count()
        return format_html(
            '<span title="Total: {}, Active: {}">{} ({})</span>',
            count, active_count, count, active_count
        )
    course_count.short_description = _('Courses (Active)')
    
    def professor_count(self, obj):
        """Display number of professors in department"""
        count = obj.get_professors_count()
        return format_html('<span>👨‍🏫 {}</span>', count)
    professor_count.short_description = _('Professors')
    
    def student_count(self, obj):
        """Display number of students in department"""
        count = obj.get_students_count()
        return format_html('<span>👨‍🎓 {}</span>', count)
    student_count.short_description = _('Students')
    
    def contact_info(self, obj):
        """Display contact information summary"""
        info = []
        if obj.email:
            info.append('📧')
        if obj.phone:
            info.append('📞')
        if obj.website:
            info.append('🌐')
        if obj.building:
            info.append('🏢')
        
        return format_html(' '.join(info)) if info else '-'
    contact_info.short_description = _('Contact')
    
    def statistics_display(self, obj):
        """Display department statistics"""
        if not obj.pk:
            return "Save the department first to see statistics"
        
        return format_html(
            '<div style="background: #f0f0f0; padding: 10px; border-radius: 5px;">'
            '<strong>Department Statistics:</strong><br>'
            '• Total Courses: {}<br>'
            '• Active Courses: {}<br>'
            '• Professors: {}<br>'
            '• Students: {}<br>'
            '</div>',
            obj.get_courses_count(),
            obj.get_active_courses_count(),
            obj.get_professors_count(),
            obj.get_students_count()
        )
    statistics_display.short_description = _('Department Statistics')


@admin.register(Enrollment)
class EnhancedEnrollmentAdmin(admin.ModelAdmin):
    """Enhanced Enrollment Admin with better filtering and display"""
    
    list_display = [
        'student_display', 'course_display', 'status_display',
        'enrollment_type', 'enrollment_date', 'progress_indicator'
    ]
    
    list_filter = [
        'status', 'enrollment_type', 'enrollment_date',
        'course__department', 'course__semester', 'course__academic_year'
    ]
    
    search_fields = [
        'user__username', 'user__first_name', 'user__last_name',
        'course__course_code', 'course__title'
    ]
    
    autocomplete_fields = ['user', 'course']
    
    readonly_fields = ['created_at', 'updated_at', 'enrollment_details']
    
    date_hierarchy = 'enrollment_date'
    
    actions = ['approve_enrollments', 'reject_enrollments', 'complete_enrollments']
    
    def student_display(self, obj):
        """Display student with role validation"""
        if obj.user.role == 'STUDENT':
            return format_html(
                '<a href="{}" title="View student profile">👨‍🎓 {}</a>',
                reverse('admin:users_customuser_change', args=[obj.user.pk]),
                obj.user.get_full_name() or obj.user.username
            )
        else:
            return format_html(
                '<span style="color: red;" title="Invalid role: {}">⚠️ {}</span>',
                obj.user.role,
                obj.user.get_full_name() or obj.user.username
            )
    student_display.short_description = _('Student')
    student_display.admin_order_field = 'user__last_name'
    
    def course_display(self, obj):
        """Display course with link"""
        return format_html(
            '<a href="{}" title="View course details">{} - {}</a>',
            reverse('admin:courses_courseenhanced_change', args=[obj.course.pk]),
            obj.course.course_code,
            obj.course.title[:30] + ('...' if len(obj.course.title) > 30 else '')
        )
    course_display.short_description = _('Course')
    course_display.admin_order_field = 'course__course_code'
    
    def status_display(self, obj):
        """Display status with color coding"""
        status_colors = {
            'PENDING': ('🟡', 'orange'),
            'APPROVED': ('🟢', 'green'),
            'REJECTED': ('🔴', 'red'),
            'COMPLETED': ('✅', 'blue'),
            'DROPPED': ('❌', 'gray')
        }
        icon, color = status_colors.get(obj.status, ('❓', 'black'))
        
        return format_html(
            '<span style="color: {};">{} {}</span>',
            color, icon, obj.get_status_display()
        )
    status_display.short_description = _('Status')
    status_display.admin_order_field = 'status'
    
    def progress_indicator(self, obj):
        """Display enrollment progress"""
        if obj.status == 'COMPLETED':
            try:
                grade = CourseGrade.objects.filter(user=obj.user, course=obj.course).first()
                if grade:
                    return format_html(
                        '<span style="color: green;" title="Grade: {}">🎓 {}</span>',
                        grade.grade, grade.grade
                    )
                return format_html('<span style="color: blue;">✅ Completed</span>')
            except:
                return format_html('<span style="color: blue;">✅ Completed</span>')
        elif obj.status == 'APPROVED':
            # Calculate progress if available
            return format_html('<span style="color: green;">📚 In Progress</span>')
        else:
            return format_html('<span style="color: #ccc;">-</span>')
    progress_indicator.short_description = _('Progress')
    
    def enrollment_details(self, obj):
        """Display detailed enrollment information"""
        if not obj.pk:
            return "Save the enrollment first to see details"
        
        details = []
        details.append(f"Enrolled: {obj.enrollment_date.strftime('%Y-%m-%d')}")
        if obj.completion_date:
            details.append(f"Completed: {obj.completion_date.strftime('%Y-%m-%d')}")
        if obj.notes:
            details.append(f"Notes: {obj.notes}")
        
        return format_html(
            '<div style="background: #f0f0f0; padding: 10px; border-radius: 5px;">'
            '{}'
            '</div>',
            '<br>'.join(details)
        )
    enrollment_details.short_description = _('Enrollment Details')
    
    def approve_enrollments(self, request, queryset):
        """Bulk approve enrollments"""
        updated = 0
        for enrollment in queryset.filter(status='PENDING'):
            can_enroll, message = enrollment.course.can_enroll_student(enrollment.user)
            if can_enroll or enrollment.course.is_full():  # Allow override for admin
                enrollment.approve()
                updated += 1
        
        self.message_user(request, f'{updated} enrollments approved successfully.')
    approve_enrollments.short_description = _('Approve selected enrollments')
    
    def reject_enrollments(self, request, queryset):
        """Bulk reject enrollments"""
        updated = 0
        for enrollment in queryset.filter(status='PENDING'):
            enrollment.reject()
            updated += 1
        
        self.message_user(request, f'{updated} enrollments rejected.')
    reject_enrollments.short_description = _('Reject selected enrollments')
    
    def complete_enrollments(self, request, queryset):
        """Bulk complete enrollments"""
        updated = 0
        for enrollment in queryset.filter(status='APPROVED'):
            enrollment.complete()
            updated += 1
        
        self.message_user(request, f'{updated} enrollments marked as completed.')
    complete_enrollments.short_description = _('Mark selected enrollments as completed')
    
    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related('user', 'course', 'course__department')


# Register the enhanced models
admin.site.unregister(Course)  # Unregister the original if it exists
admin.site.register(CourseEnhanced, EnhancedCourseAdmin)

import logging
import random
from datetime import timedel<PERSON>
from django.core.cache import cache
from django.db.models import Avg, Count, OuterRef, Q
from django.utils import timezone
from rest_framework import status, views, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAdminUser, IsAuthenticated
from rest_framework.response import Response
from users.permissions import IsAdminUser
from utils.consolidated_ai_service import ContentGenerationError
from utils.ai.services import get_ai_service
from core.exceptions import RateLimitError
from utils.ai_config_manager import (
    DEFAULT_GENERATION_CONFIG,
    get_fallback_model_name,
    get_model_name
)
from .ai_assessment_analysis import ai_assessment_analysis
from .models import (
    AIAssessmentController,
    Assessment,
    AssessmentQuestion,
    AssessmentResponse,
    AssessmentSettings,
    CompetencyBadge,
    LearningPathway,
    ProgressionMilestone,
    StudentLevel
)

# Import serializers
from .serializers import AIDecisionSerializer  # Used in AIDecisionViewSet
from .serializers import AIQuestionReviewSerializer  # Used in QuestionManagementViewSet
from .serializers import AssessmentAdminSerializer  # Used in AdminAssessmentViewSet
from .serializers import (
    AssessmentQuestionAdminSerializer  # Used in AdminQuestionViewSet
)
from .serializers import AssessmentQuestionSerializer  # Used in AdminQuestionViewSet
from .serializers import (
    AssessmentSettingsSerializer  # Used in AssessmentSettingsViewSet
)
from .serializers import CompetencyBadgeSerializer  # Used in CompetencyBadgeViewSet
from .serializers import LearningPathwaySerializer  # Used in LearningPathwayViewSet
from .serializers import (
    ProgressionMilestoneSerializer  # Used in ProgressionMilestoneViewSet
)
# from .serializers import SkillSerializer  # Used in SkillViewSet - commented out as Skill model doesn't exist
from .services import AssessmentAIService, AssessmentService

# Define your viewsets, functions, and classes here according to your requirements.

import loggingimport randomfromdatetimeimport timedeltafrom django.core.cacheimportcachefrom django.db.modelsimportAvgCountOuterRefQfrom django.utilsimport timezonefromrest_frameworkimportstatusviewsviewsetsfromrest_framework.decoratorsimportactionfromrest_framework.permissionsimportIsAdminUserIsAuthenticatedfromrest_framework.responseimportResponsefrom users.permissionsimportIsAdminUserfrom utils.consolidated_ai_serviceimportContentGenerationErrorfrom utils.ai.servicesimportget_ai_servicefrom core.exceptionsimportRateLimitErrorfrom utils.ai_config_managerimport(DEFAULT_GENERATION_CONFIGget_fallback_model_nameget_model_name)from.ai_assessment_analysisimportai_assessment_analysisfrom.modelsimport(AIAssessmentControllerAssessmentAssessmentQuestionAssessmentResponseAssessmentSettingsCompetencyBadgeLearningPathwayProgressionMilestoneStudentLevel)#Importserializersfrom.serializersimportAIDecisionSerializer#UsedinAIDecisionViewSetfrom.serializersimportAIQuestionReviewSerializer#UsedinQuestionManagementViewSetfrom.serializersimportAssessmentAdminSerializer#UsedinAdminAssessmentViewSetfrom.serializersimport(AssessmentQuestionAdminSerializer#UsedinAdminQuestionViewSet)from.serializersimportAssessmentQuestionSerializer#UsedinAdminQuestionViewSetfrom.serializersimport(AssessmentSettingsSerializer#UsedinAssessmentSettingsViewSet)from.serializersimportCompetencyBadgeSerializer#UsedinCompetencyBadgeViewSetfrom.serializersimportLearningPathwaySerializer#UsedinLearningPathwayViewSetfrom.serializersimport(ProgressionMilestoneSerializer#UsedinProgressionMilestoneViewSet)#from.serializersimportSkillSerializer#UsedinSkillViewSet-commentedoutasSkillmodeldoesn'texistfrom.servicesimportAssessmentAIServiceAssessmentServicelogger=logging.getLogger(__name__)classAdminAnalyticsView(views.APIView):"""Viewforadminanalyticsdashboard"""permission_classes=[IsAuthenticatedIsAdminUser]defget(selfrequest):try:time_period=request.query_params.get("period""all")iftime_period!="all":date_filter=timezone.now()-timedelta(days={"week":7"month":30"quarter":90}.get(time_period0))time_filter=Q(created_at__gte=date_filter)else:time_filter=Q()#GetstudentstatsfromAssessmentmodelinsteadstudent_stats={"total_students":Assessment.objects.filter(time_filter).values("student_id").distinct().count()"avg_assessment_score":Assessment.objects.filter(time_filtercompleted=True).aggregate(Avg("score"))["score__avg"]or0"approved_count":0#Notapplicableanymore"pending_count":0#Notapplicableanymore}#Getassessmentstatsassessment_stats=Assessment.objects.filter(time_filter).aggregate(total_assessments=Count("id")completed_assessments=Count("id"filter=Q(completed=True))avg_score=Avg("score"filter=Q(completed=True)))#Getrecentactivityrecent_assessments=Assessment.objects.filter(time_filter).order_by("-created_at")[:10]response_data={"student_stats":student_stats"assessment_stats":assessment_stats"recent_assessments":[{"id":assessment.id"student_name":(assessment.student.get_full_name()ifassessment.studentelse"Unknown")"score":assessment.score"completed":assessment.completed"created_at":assessment.created_at.isoformat()}forassessmentinrecent_assessments]"recent_activities":[{"student_name":(assessment.student.get_full_name()ifhasattr(assessment"student")andassessment.studentelse"Unknown")"activity_type":"Assessment""course_title":"Assessment""timestamp":(assessment.end_time.isoformat()ifassessment.end_timeelseassessment.created_at.isoformat())}forassessmentinAssessment.objects.filter(completed=True).order_by("-created_at")[:10]]}returnResponse(response_data)exceptExceptionase:returnResponse({"error":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAdminSettingsView(views.APIView):"""Viewformanagingadminsettings"""permission_classes=[IsAuthenticatedIsAdminUser]defget(selfrequest):try:ai_settings={"confidence_threshold":0.8"max_questions_per_category":5"adaptive_difficulty":True"question_time_limit":60}assessment_rules={"min_questions_required":10"passing_score_percentage":70"allow_retakes":True"retake_waiting_period":7"level_progression_threshold":85}returnResponse({"status":"success""ai_settings":ai_settings"assessment_rules":assessment_rules})exceptExceptionase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)defput(selfrequest):"""Updatesettings"""try:#Getsettingsfromrequestnew_settings=request.datasetting_type=request.query_params.get("type""ai")ifsetting_type=="ai":#UpdateAIsettings#TODO:Savetosettingsmodelpasselse:#Updateassessmentrules#TODO:SavetosettingsmodelpassreturnResponse({"status":"success""message":f"{setting_type}settingsupdatedsuccessfully"})exceptExceptionase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAIQuestionSuggestionsView(views.APIView):"""ViewforgettingAIsuggestionsforimprovingquestions"""permission_classes=[]#Allowanonymousaccessfortestingdefpost(selfrequest):"""GetAIsuggestionsforimprovingaquestion"""try:question_text=request.data.get("question_text")context=request.data.get("context"{})ifnotquestion_text:returnResponse({"status":"error""message":"Questiontextisrequired"}status=status.HTTP_400_BAD_REQUEST)#GetAIsuggestionssuggestions=AssessmentAIService.get_question_suggestions(question_text=question_textcontext=context)returnResponse({"status":"success""suggestions":suggestions})exceptExceptionase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAIQuestionVariationsView(views.APIView):"""ViewforgettingAI-generatedvariationsofaquestion"""permission_classes=[]#Allowanonymousaccessfortestingdefpost(selfrequest):"""GetAI-generatedvariationsofaquestion"""try:question_text=request.data.get("question_text")count=request.data.get("count"3)ifnotquestion_text:returnResponse({"status":"error""message":"Questiontextisrequired"}status=status.HTTP_400_BAD_REQUEST)#GetAIvariationsvariations=AssessmentAIService.get_question_variations(question_text=question_textcount=count)returnResponse({"status":"success""variations":variations})exceptExceptionase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAdminQuestionGenerationView(views.APIView):"""ViewforgeneratingassessmentquestionswithAI"""permission_classes=[IsAuthenticatedIsAdminUser]defpost(selfrequest):"""GenerateassessmentquestionswithAI"""try:#Getparametersfromrequestcategory=request.data.get("category""GENERAL")difficulty_level=request.data.get("difficulty_level"1)count=request.data.get("count"5)question_type=request.data.get("question_type""MULTIPLE_CHOICE")points=request.data.get("points"10)is_placement=request.data.get("is_placement"False)is_public=request.data.get("is_public"True)topic=request.data.get("topic")specific_fields=request.data.get("specificFields"{})logger.info(f"Generating{count}{question_type}questionsin{category}categoryatlevel{difficulty_level}")#CreateaninstanceofAssessmentAIServiceandgeneratequestionsai_service=AssessmentAIService()question_objects=ai_service.generate_questions(category=categorydifficulty_level=difficulty_levelcount=count)#Serializethequestionobjectsserialized_questions=[]forquestioninquestion_objects:#Convertthequestionobjecttoadictionaryserialized_question={"id":None#Willbeassignedwhensavedtodatabase"text":question.text#Useonly'text'fieldforconsistency"question_type":question.question_type"options":question.options"correct_answer":question.correct_answer"category":question.category"difficulty_level":question.difficulty_level"points":10"ai_suggested":True"ai_reviewed":False"is_placement":(specific_fields.get("is_placement"False)ifspecific_fieldselseFalse)"is_public":(specific_fields.get("is_public"True)ifspecific_fieldselseTrue)}serialized_questions.append(serialized_question)returnResponse({"status":"success""questions":serialized_questions})exceptContentGenerationErrorase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)exceptRateLimitErrorase:returnResponse({"status":"error""message":"Ratelimitexceeded.Pleasetryagainlater."}status=status.HTTP_429_TOO_MANY_REQUESTS)exceptExceptionase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAdminAssessmentAnalyticsView(views.APIView):"""Viewforassessmentanalytics"""permission_classes=[IsAuthenticatedIsAdminUser]defget(selfrequest):try:#Timeperiodfiltertime_period=request.query_params.get("period""all")iftime_period!="all":date_filter=timezone.now()-timedelta(days={"week":7"month":30"quarter":90}.get(time_period0))time_filter=Q(created_at__gte=date_filter)else:time_filter=Q()#Getassessmentstatisticsassessment_stats=Assessment.objects.filter(time_filter).aggregate(total_assessments=Count("id")completed_assessments=Count("id"filter=Q(completed=True))avg_score=Avg("score"filter=Q(completed=True))avg_time=Avg("time_spent"filter=Q(completed=True)))#Getleveldistributionlevel_distribution=(Assessment.objects.filter(time_filtercompleted=True).values("level").annotate(count=Count("id")))#Getquestionperformancequestion_performance=(AssessmentQuestion.objects.filter(responses__assessment__created_at__gte=(date_filteriftime_period!="all"elseQ())).annotate(usage_count=Count("responses")correct_count=Count("responses"filter=Q(responses__is_correct=True))).values("id""text""usage_count""correct_count"))#Calculatecorrectpercentageforquestioninquestion_performance:ifquestion["usage_count"]>0:question["correct_percentage"]=round(question["correct_count"]/question["usage_count"]*1001)else:question["correct_percentage"]=0returnResponse({"status":"success""assessment_stats":assessment_stats"level_distribution":level_distribution"question_performance":question_performance})exceptExceptionase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAdminAIDecisionsView(views.APIView):"""ViewformanagingAIdecisions"""permission_classes=[IsAuthenticatedIsAdminUser]defget(selfrequest):"""GetpendingAIdecisionsrequiringreview"""try:#Getfiltersfromqueryparamsdecision_type=request.query_params.get("decision_type")status=request.query_params.get("status""PENDING")#Basequerysetdecisions=AIAssessmentController.objects.all()#Applyfiltersifdecision_type:decisions=decisions.filter(decision_type=decision_type)ifstatus:decisions=decisions.filter(status=status)#Getdecisionsthateitherrequirerevieworhavelowconfidencedecisions=decisions.filter(Q(requires_review=True)|Q(confidence_score__lt=0.7)).select_related("student""assessment")returnResponse({"status":"success""decisions":AIDecisionSerializer(decisionsmany=True).data})exceptExceptionase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)defpost(selfrequestdecision_id=None):"""ReviewandupdateanAIdecision"""try:decision_id=decision_idorrequest.data.get("decision_id")action=request.data.get("action")feedback=request.data.get("feedback")ifnotdecision_idornotaction:returnResponse({"status":"error""message":"DecisionIDandactionarerequired"}status=status.HTTP_400_BAD_REQUEST)#Getdecisiondecision=AIAssessmentController.objects.get(id=decision_id)#Updatedecisionifaction=="approve":decision.status="APPROVED"decision.admin_reviewed=Truedecision.admin_feedback=feedbackdecision.save()#Applythedecision#TODO:Implementdecisionapplicationlogicelifaction=="reject":decision.status="REJECTED"decision.admin_reviewed=Truedecision.admin_feedback=feedbackdecision.save()else:returnResponse({"status":"error""message":f"Invalidaction:{action}"}status=status.HTTP_400_BAD_REQUEST)returnResponse({"status":"success""message":f"Decision{decision_id}{action}edsuccessfully"})exceptAIAssessmentController.DoesNotExist:returnResponse({"status":"error""message":f"DecisionwithID{decision_id}notfound"}status=status.HTTP_404_NOT_FOUND)exceptExceptionase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAISettingsView(views.APIView):"""ViewformanagingAIassessmentsettings"""permission_classes=[IsAuthenticatedIsAdminUser]defget(selfrequest):"""GetcurrentAIsettings"""try:settings={"auto_approval_threshold":0.9"review_threshold":0.7"required_review_types":["LEVEL_CHANGE""LEARNING_PATH"]"ai_settings":{"model_version":get_model_name()"fallback_model":get_fallback_model_name()"temperature":DEFAULT_GENERATION_CONFIG.get("temperature"0.7)"max_response_tokens":DEFAULT_GENERATION_CONFIG.get("max_output_tokens"1000)}"decision_weights":{"assessment_score":0.4"skill_proficiency":0.3"learning_history":0.2"time_factors":0.1}}returnResponse({"status":"success""settings":settings})exceptExceptionase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)defput(selfrequest):"""UpdateAIsettings"""try:settings=request.data.get("settings")ifnotsettings:returnResponse({"status":"error""message":"Settingsdatarequired"}status=status.HTTP_400_BAD_REQUEST)#Validatesettingsrequired_fields=["auto_approval_threshold""review_threshold""required_review_types"]forfieldinrequired_fields:iffieldnotinsettings:returnResponse({"status":"error""message":f"Missingrequiredfield:{field}"}status=status.HTTP_400_BAD_REQUEST)#Savesettings(inarealappthiswouldupdateaSettingsmodelorsimilar)cache.set("ai_assessment_settings"settingstimeout=None)returnResponse({"status":"success""settings":settings})exceptExceptionase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAdminQuestionViewSet(viewsets.ModelViewSet):"""ViewSetformanagingassessmentquestions"""permission_classes=[IsAdminUser]queryset=AssessmentQuestion.objects.all()serializer_class=AssessmentQuestionSerializer@action(detail=Falsemethods=["get"])defperformance(selfrequest):"""Getperformancedataforquestions"""try:#GetquestionIDsfromqueryparamsquestion_ids_param=request.query_params.get("question_ids""")ifnotquestion_ids_param:returnResponse({"status":"error""message":"question_idsparameterisrequired"}status=status.HTTP_400_BAD_REQUEST)#ParsequestionIDstry:question_ids=[int(id)foridinquestion_ids_param.split("")]exceptValueError:returnResponse({"status":"error""message":"Invalidquestion_idsformat.Expectedcomma-separatedintegers."}status=status.HTTP_400_BAD_REQUEST)#Getquestionsquestions=self.get_queryset().filter(id__in=question_ids)#Getperformancedataforeachquestionperformance_data=[]forquestioninquestions:#Getresponsesforthisquestionresponses=AssessmentResponse.objects.filter(question=question)#Calculateperformancemetricstotal_attempts=responses.count()#Iftherearenoresponsesgeneratesampledataiftotal_attempts==0:#Generaterandomdatabasedondifficultylevel#Easierquestionsshouldhavehighersuccessratesifquestion.difficulty_level<=2:#Beginner/Elementarysample_attempts=random.randint(1020)correct_percentage=random.uniform(7090)avg_time=random.uniform(2040)elifquestion.difficulty_level<=4:#Intermediate/Advancedsample_attempts=random.randint(815)correct_percentage=random.uniform(5075)avg_time=random.uniform(3060)else:#Expertsample_attempts=random.randint(512)correct_percentage=random.uniform(3060)avg_time=random.uniform(4590)performance_data.append({"question_id":question.id"text":question.text"difficulty_level":question.difficulty_level"category":question.category"attempts":sample_attempts"correct_attempts":int(sample_attempts*correct_percentage/100)"correct_percentage":correct_percentage"average_time":avg_time})else:#Userealdatacorrect_attempts=responses.filter(is_correct=True).count()correct_percentage=((correct_attempts/total_attempts*100)iftotal_attempts>0else0)#Calculateaveragetimeavg_time=(responses.aggregate(avg_time=Avg("time_spent"))["avg_time"]or0)#Addtoperformancedataperformance_data.append({"question_id":question.id"text":question.text"difficulty_level":question.difficulty_level"category":question.category"attempts":total_attempts"correct_attempts":correct_attempts"correct_percentage":correct_percentage"average_time":avg_time})returnResponse(performance_data)exceptExceptionase:logger.error(f"Errorgettingquestionperformance:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)defget_serializer_class(self):ifself.actionin["list""retrieve"]:returnAssessmentQuestionSerializerreturnAssessmentQuestionAdminSerializer@action(detail=Truemethods=["post"])defvalidate_question(selfrequestpk=None):"""ValidateaquestionwithAI"""question=self.get_object()#ValidatequestionwithAIvalidation_result=AssessmentAIService.validate_question(question)returnResponse(validation_result)@action(detail=Falsemethods=["get"])defstats(selfrequest):"""Getquestionstatistics"""stats={"total_questions":self.get_queryset().count()"by_type":{}"by_difficulty":{}"by_category":{}"ai_generated":self.get_queryset().filter(ai_suggested=True).count()"ai_reviewed":self.get_queryset().filter(ai_reviewed=True).count()}#Getquestiontypedistributionforquestion_type_inAssessmentQuestion.QUESTION_TYPES:count=self.get_queryset().filter(question_type=question_type).count()stats["by_type"][question_type]=count#Getdifficultydistributionforlevellevel_nameinAssessmentQuestion.DIFFICULTY_LEVELS:count=self.get_queryset().filter(difficulty_level=level).count()stats["by_difficulty"][level_name]=count#Getcategorydistributionforcategory_inAssessmentQuestion.CATEGORY_CHOICES:count=self.get_queryset().filter(category=category).count()stats["by_category"][category]=countreturnResponse(stats)@action(detail=Falsemethods=["post"])defbulk_update(selfrequest):"""Bulkupdatequestions"""question_ids=request.data.get("question_ids"[])updates=request.data.get("updates"{})ifnotquestion_idsornotupdates:returnResponse({"error":"Noquestionsorupdatesspecified"}status=status.HTTP_400_BAD_REQUEST)questions=self.get_queryset().filter(id__in=question_ids)questions.update(**updates)returnResponse({"updated_count":len(question_ids)"updates":updates})classAdminAssessmentViewSet(viewsets.ModelViewSet):"""ViewSetformanagingassessments"""permission_classes=[IsAdminUser]queryset=Assessment.objects.all().order_by("-created_at")serializer_class=AssessmentAdminSerializerdefget_queryset(self):"""Returnassessmentswithadditionalfiltering"""queryset=Assessment.objects.all().order_by("-created_at")#Filterbyassessmenttypeifprovidedassessment_type=self.request.query_params.get("assessment_type")ifassessment_type:queryset=queryset.filter(assessment_type=assessment_type)#Filterbystudentifprovidedstudent_id=self.request.query_params.get("student_id")ifstudent_id:queryset=queryset.filter(student_id=student_id)#Filterbystatusifprovidedstatus=self.request.query_params.get("status")ifstatus:queryset=queryset.filter(status=status)#Filterbycompletionstatusifprovidedcompleted=self.request.query_params.get("completed")ifcompletedisnotNone:completed_bool=completed.lower()=="true"queryset=queryset.filter(completed=completed_bool)#Prefetchrelatedresponsestoimproveperformancequeryset=queryset.prefetch_related("responses""responses__question")returnqueryset@action(detail=Falsemethods=["get"])defstudent_assessments(selfrequeststudent_id=None):"""Getassessmentsforstudents"""try:#Getallassessmentsbydefaultnotjustplacementassessmentsassessments=self.get_queryset()#Ifstudent_idisprovidedintheURLorqueryparamsfilterbyitstudent_id=student_idorrequest.query_params.get("student_id")ifstudent_id:assessments=assessments.filter(student_id=student_id)logger.info(f"FilteringassessmentsforstudentID:{student_id}")#Filterbyassessmenttypeifprovidedassessment_type=request.query_params.get("assessment_type")ifassessment_type:assessments=assessments.filter(assessment_type=assessment_type)else:#Defaulttoplacementassessmentsifnotypespecifiedassessments=assessments.filter(assessment_type="PLACEMENT")#Includestudentdetailsassessments=assessments.select_related("student")#Ensureresponsesareprefetchedassessments=assessments.prefetch_related("responses""responses__question")#Getstudentinfofortheresponsestudent_name="Unknown"student_level_info="NotSet"ifstudent_id:try:from django.contrib.authimportget_user_modelUser=get_user_model()student=User.objects.get(id=student_id)student_name=(f"{student.first_name}{student.last_name}"ifstudent.first_nameelsestudent.username)#Getstudentlevelfrom assessment.modelsimportStudentLeveltry:student_level=StudentLevel.objects.get(student_id=student_id)student_level_info=student_level.current_levelexceptStudentLevel.DoesNotExist:student_level_info="NotSet"exceptUser.DoesNotExist:student_name="UnknownStudent"#Processeachassessmenttoincludedetailedresponsesprocessed_assessments=[]forassessmentinassessments:#Getthebaseserializeddataassessment_data=self.get_serializer(assessment).data#Getallresponsesforthisassessmentresponses=assessment.responses.all().select_related("question")#Adddetailedresponsestotheassessmentdatadetailed_responses=[]forresponseinresponses:#Getthestudent'sanswerfromvariousfieldsstudent_answer=response.answer_textifnotstudent_answerandresponse.answer:student_answer=response.answerifnotstudent_answerandresponse.student_answer:ifisinstance(response.student_answerdict):student_answer=str(response.student_answer)else:student_answer=response.student_answer#Getthecorrectanswercorrect_answer=(response.question.correct_answerifresponse.questionelse"Notavailable")#Getquestiontextquestion_text=(response.question.question_textifresponse.questionelse"Questionnotavailable")detailed_response={"id":response.id"question_id":(response.question.idifresponse.questionelseNone)"question_text":question_text"student_answer":student_answeror"""correct_answer":correct_answer"is_correct":response.is_correct"question_type":(response.question.question_typeifresponse.questionelse"MULTIPLE_CHOICE")"difficulty_level":(response.question.difficulty_levelifresponse.questionelse1)"points_earned":response.points_earned"points":response.question.pointsifresponse.questionelse0}detailed_responses.append(detailed_response)#Addthedetailedresponsestotheassessmentdataassessment_data["detailed_responses"]=detailed_responsesprocessed_assessments.append(assessment_data)#Logtheresponsedatafordebugginglogger.info(f"Studentassessmentsresponse:{len(processed_assessments)}assessmentsfound")ifprocessed_assessments:first_assessment=processed_assessments[0]logger.info(f"FirstassessmentID:{first_assessment.get('id')}")logger.info(f"Firstassessmenthas{len(first_assessment.get('detailed_responses'[]))}responses")#LogallassessmentIDsfordebuggingassessment_ids=[a.get("id")forainprocessed_assessments]logger.info(f"AllassessmentIDs:{assessment_ids}")#ReturntheprocesseddatawithstudentinforeturnResponse({"status":"success""student_info":{"id":student_id"name":student_name"level":student_level_info}"data":processed_assessments})exceptExceptionase:logger.error(f"Errorgettingstudentassessments:{str(e)}")returnResponse({"detail":f"Errorgettingstudentassessments:{str(e)}"}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@action(detail=Falsemethods=["get"])defscore_distribution(selfrequest):"""Getscoredistributionforassessments"""try:#Getcompletedassessmentsassessments=self.get_queryset().filter(completed=True)#Groupscoresintobinsof5pointseachscore_ranges={}foriinrange(01015):score_ranges[f"{i}-{i+4}"]=0#Countassessmentsineachscorerangeforassessmentinassessments:score=assessment.scoreor0range_key=f"{(score//5)*5}-{((score//5)*5)+4}"ifrange_keyinscore_ranges:score_ranges[range_key]+=1#Formattheresponseformatted_ranges=[{"range":key"count":value"percentage":((value/assessments.count()*100)ifassessments.count()>0else0)}forkeyvalueinscore_ranges.items()]returnResponse({"status":"success""score_ranges":formatted_ranges"total_assessments":assessments.count()})exceptExceptionase:logger.error(f"Errorgettingscoredistribution:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@action(detail=Falsemethods=["get"])deflevel_requirements(selfrequest):"""Getlevelrequirements"""try:#Definelevelrequirementslevel_requirements=[{"level":1"name":"Beginner""min_score":0"max_score":59"description":"Basicunderstandingofconcepts"}{"level":2"name":"Elementary""min_score":60"max_score":69"description":"Elementaryunderstandingofconcepts"}{"level":3"name":"Intermediate""min_score":70"max_score":79"description":"Intermediateunderstandingofconcepts"}{"level":4"name":"Advanced""min_score":80"max_score":89"description":"Advancedunderstandingofconcepts"}{"level":5"name":"Expert""min_score":90"max_score":100"description":"Expertunderstandingofconcepts"}]returnResponse({"status":"success""level_requirements":level_requirements})exceptExceptionase:logger.error(f"Errorgettinglevelrequirements:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@action(detail=Falsemethods=["get"])defrecent_assessments(selfrequest):"""Getrecentassessments"""try:#Getrecentassessmentslimit=int(request.query_params.get("limit"20))#Getallassessmentsfirstall_assessments=self.get_queryset()#Checkifwehaveanyassessmentsifnotall_assessments.exists():logger.warning("Noassessmentsfoundindatabase")returnResponse({"status":"success""message":"Noassessmentsfound""data":[]})#Simplifythequerytoavoidpotentialerrorstry:#Getthemostrecentassessments#Useonlyfieldsthatdefinitelyexistinthedatabaseassessments=all_assessments.values("id""student_id""assessment_type""status""score""completed""created_at""end_time""title""description").order_by("-created_at")[:limit]exceptExceptionase:logger.warning(f"Errororderingbycreated_at:{str(e)}")#Iforderingfailsjustgettheassessmentswithoutorderingtry:assessments=all_assessments.values("id""student_id""assessment_type""status""score""completed""created_at")[:limit]exceptExceptionasinner_e:logger.warning(f"Errorgettingvalues:{str(inner_e)}")#Lastresort:getminimalfieldsassessments=all_assessments.values("id""student_id")[:limit]#Useasimpleserializationapproachtoavoidpotentialerrorsserialized_data=[]forassessmentinassessments:try:#Getstudentnameifpossiblestudent_name=""student_id=None#Handlebothdictionaryandobjectformatsifisinstance(assessmentdict):#Dictionaryformat(fromvaluesquery)assessment_id=assessment.get("id")student_id=assessment.get("student_id")assessment_type=assessment.get("assessment_type""Unknown")status=assessment.get("status""Unknown")score=assessment.get("score")completed=assessment.get("completed"False)title=assessment.get("title""Assessment")description=assessment.get("description""")#Formatdatesiftheyexistcreated_at=assessment.get("created_at")ifcreated_atandhasattr(created_at"isoformat"):created_at=created_at.isoformat()end_time=assessment.get("end_time")ifend_timeandhasattr(end_time"isoformat"):end_time=end_time.isoformat()else:end_time=Noneelse:#Objectformatassessment_id=assessment.idstudent_id=(assessment.student_idifhasattr(assessment"student_id")elseNone)assessment_type=(assessment.assessment_typeifhasattr(assessment"assessment_type")else"Unknown")status=(assessment.statusifhasattr(assessment"status")else"Unknown")score=(assessment.scoreifhasattr(assessment"score")elseNone)completed=(assessment.completedifhasattr(assessment"completed")elseFalse)title=(assessment.titleifhasattr(assessment"title")else"Assessment")description=(assessment.descriptionifhasattr(assessment"description")else"")created_at=(assessment.created_at.isoformat()ifhasattr(assessment"created_at")elseNone)end_time=(assessment.end_time.isoformat()ifhasattr(assessment"end_time")andassessment.end_timeelseNone)#Getstudentnameifavailableifhasattr(assessment"student")andassessment.student:ifhasattr(assessment.student"get_full_name"):student_name=assessment.student.get_full_name()elifhasattr(assessment.student"username"):student_name=assessment.student.usernameelifhasattr(assessment.student"email"):student_name=assessment.student.email#TrytogetstudentnamefromUsermodelifwehavestudent_idifnotstudent_nameandstudent_id:try:from django.contrib.authimportget_user_modelUser=get_user_model()user=User.objects.get(id=student_id)student_name=(user.get_full_name()ifhasattr(user"get_full_name")elseuser.username)exceptExceptionasuser_e:logger.warning(f"Errorgettingusername:{str(user_e)}")#Createasimpledictionarywithassessmentdataassessment_data={"id":assessment_id"student_id":student_id"student_name":student_name"assessment_type":assessment_type"status":status"score":score"completed":completed"title":title"description":description"created_at":created_at"end_time":end_time}serialized_data.append(assessment_data)exceptExceptionasitem_e:assessment_id=(assessment.get("id")ifisinstance(assessmentdict)elsegetattr(assessment"id""unknown"))logger.error(f"Errorserializingassessment{assessment_id}:{str(item_e)}")#Logtheresponsefordebugginglogger.info(f"Returning{len(serialized_data)}recentassessments")returnResponse({"status":"success""data":serialized_data})exceptExceptionase:logger.error(f"Errorfetchingrecentassessments:{str(e)}")returnResponse({"status":"error""message":str(e)"data":[]}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAssessmentSettingsViewSet(viewsets.ModelViewSet):"""ViewSetformanagingassessmentsettings"""permission_classes=[IsAdminUser]queryset=AssessmentSettings.objects.all()serializer_class=AssessmentSettingsSerializer@action(detail=Truemethods=["patch"])defupdate_difficulty(selfrequestpk=None):"""Updateadaptivedifficultysettings"""settings=self.get_object()adaptive_settings=request.data.get("adaptive_settings"{})ifnotadaptive_settings:returnResponse({"error":"Noadaptivesettingsprovided"}status=status.HTTP_400_BAD_REQUEST)settings.adaptive_difficulty=Truesettings.ai_settings["adaptive"]=adaptive_settingssettings.save()returnResponse(self.get_serializer(settings).data)@action(detail=Falsemethods=["post"])defbulk_create(selfrequest):"""Createmultiplesettingsatonce"""serializer=self.get_serializer(data=request.datamany=True)serializer.is_valid(raise_exception=True)self.perform_create(serializer)returnResponse(serializer.datastatus=status.HTTP_201_CREATED)#classSkillViewSet(viewsets.ModelViewSet):#"""ViewSetformanagingskills"""##permission_classes=[IsAdminUser]#queryset=Skill.objects.all()#serializer_class=SkillSerializer##@action(detail=Falsemethods=["post"])#defbulk_create(selfrequest):#"""Createmultipleskillsatonce"""#serializer=self.get_serializer(data=request.datamany=True)#serializer.is_valid(raise_exception=True)#self.perform_create(serializer)#returnResponse(serializer.datastatus=status.HTTP_201_CREATED)classLearningPathwayViewSet(viewsets.ModelViewSet):"""ViewSetformanaginglearningpathways"""permission_classes=[IsAdminUser]queryset=LearningPathway.objects.all()serializer_class=LearningPathwaySerializerclassProgressionMilestoneViewSet(viewsets.ModelViewSet):"""ViewSetformanagingprogressionmilestones"""permission_classes=[IsAdminUser]queryset=ProgressionMilestone.objects.all()serializer_class=ProgressionMilestoneSerializerclassCompetencyBadgeViewSet(viewsets.ModelViewSet):"""ViewSetformanagingcompetencybadges"""permission_classes=[IsAdminUser]queryset=CompetencyBadge.objects.all()serializer_class=CompetencyBadgeSerializer@action(detail=Falsemethods=["post"])defaward_badge(selfrequest):"""Awardabadgetoastudent"""try:student_id=request.data.get("student_id")badge_id=request.data.get("badge_id")reason=request.data.get("reason""")#AwardbadgelogicherereturnResponse({"message":f"Badge{badge_id}awardedtostudent{student_id}"})exceptExceptionase:returnResponse({"error":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAdminAssessmentStatsViewSet(viewsets.ModelViewSet):"""ViewSetforassessmentstatistics"""permission_classes=[IsAdminUser]serializer_class=AssessmentQuestionSerializerqueryset=Assessment.objects.all()@action(detail=Falsemethods=["get"])defoverview(selfrequest):"""Getassessmentoverviewstatistics"""try:stats={"total_assessments":self.get_queryset().count()"completed_assessments":self.get_queryset().filter(completed=True).count()"average_score":self.get_queryset().filter(completed=True).aggregate(Avg("score"))["score__avg"]or0}returnResponse(stats)exceptExceptionase:returnResponse({"error":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classStudentAssessmentAdminViewSet(viewsets.ModelViewSet):"""ViewSetforadmintomanagestudentassessments"""permission_classes=[IsAdminUser]serializer_class=AssessmentAdminSerializerqueryset=Assessment.objects.all()defget_queryset(self):"""Filterassessmentsbystudentifstudent_idisprovided"""queryset=super().get_queryset()student_id=self.request.query_params.get("student_id")ifstudent_id:queryset=queryset.filter(student_id=student_id)#Prefetchrelatedresponsestoimproveperformancequeryset=queryset.prefetch_related("responses""responses__question")returnquerysetdefget_results(selfrequestpk=None):"""Getdetailedresultsforanassessment"""try:assessment=self.get_object()#Getresponseswithquestionsresponses=assessment.responses.all().select_related("question")response_data=[]forresponseinresponses:response_dict={"id":response.id"question_id":response.question.id"question_text":response.question.text"student_answer":response.answer_text"correct_answer":response.question.correct_answer"is_correct":response.is_correct"points_earned":response.points_earned"points_possible":response.question.points"question_type":response.question.question_type}response_data.append(response_dict)#Getskillanalysisifavailableskill_analysis={}ifhasattr(assessment"skill_scores")andassessment.skill_scores:skill_analysis=assessment.skill_scores#Preparestrengthsandweaknessesstrengths=[]weaknesses=[]ifhasattr(assessment"skill_strengths")andassessment.skill_strengths:strengths=assessment.skill_strengthsifhasattr(assessment"skill_weaknesses")andassessment.skill_weaknesses:weaknesses=assessment.skill_weaknesses#Preparedetailedresultsdetailed_results={"responses":response_data"skill_scores":skill_analysis"time_spent":assessment.time_spent"initial_level":assessment.initial_level"final_level":assessment.final_level"level_changed":assessment.level_changed}#Preparethefullresponseresult={"assessment":AssessmentAdminSerializer(assessment).data"detailed_results":detailed_results"strengths":strengths"weaknesses":weaknesses"recommendations":[]#Addrecommendationslogicifavailable}returnResponse(result)exceptExceptionase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_400_BAD_REQUEST)@action(detail=Truemethods=["get"])defresults(selfrequestpk=None):"""Getdetailedassessmentresultsforadminview"""try:assessment=self.get_object()logger.info(f"Gettingresultsforassessment{pk}")#Alwaysreturnresultsevenforincompleteassessments#Justincludeawarningifit'snotcompletedis_completed=assessment.completedorassessment.status=="COMPLETED"#Gettheassessmentdataassessment_data=self.get_serializer(assessment).data#Addlevelinformationifit'snotalreadyinthedataif"initial_level"notinassessment_dataandhasattr(assessment"initial_level"):assessment_data["initial_level"]=assessment.initial_levelif"final_level"notinassessment_dataandhasattr(assessment"final_level"):assessment_data["final_level"]=assessment.final_levelif"level_changed"notinassessment_dataandhasattr(assessment"level_changed"):assessment_data["level_changed"]=assessment.level_changed#Getleveldisplaynamesfrom.modelsimportStudentLevelinitial_level=assessment.initial_levelor1final_level=assessment.final_levelorinitial_levelassessment_data["initial_level_display"]=dict(StudentLevel.LEVEL_CHOICES).get(initial_level"Beginner")assessment_data["final_level_display"]=dict(StudentLevel.LEVEL_CHOICES).get(final_level"Beginner")#Getdetailedresponsesresponses=assessment.responses.all().select_related("question")logger.info(f"Found{responses.count()}responsesforassessment{pk}")#Formatresponsesforthefrontenddetailed_responses=[]forresponseinresponses:#Makesurewehaveaquestionifnotresponse.question:logger.warning(f"Response{response.id}hasnoassociatedquestion")continue#Getthestudent'sanswerfromvariousfieldsstudent_answer=response.answer_textifnotstudent_answerandresponse.answer:student_answer=response.answerifnotstudent_answerandresponse.student_answer:ifisinstance(response.student_answerdict):student_answer=str(response.student_answer)else:student_answer=response.student_answer#Getthecorrectanswercorrect_answer=response.question.correct_answerifisinstance(correct_answerdict)and"answer"incorrect_answer:correct_answer=correct_answer["answer"]#Getquestiontextensuringwehavesomethingtodisplayquestion_text=""ifhasattr(response.question"text")andresponse.question.text:question_text=response.question.textelif(hasattr(response.question"question_text")andresponse.question.question_text):question_text=response.question.question_textelse:question_text=f"Question{response.question.id}"detailed_response={"id":response.id"question_id":response.question.id"question_text":question_text"student_answer":student_answeror"""correct_answer":correct_answeror"Notavailable""is_correct":response.is_correct"question_type":response.question.question_type"difficulty_level":response.question.difficulty_level"points_earned":response.points_earned"points":response.question.points}detailed_responses.append(detailed_response)#Adddetailedresponsestoassessmentdataassessment_data["detailed_responses"]=detailed_responses#Alsoaddtoresponsesforbackwardcompatibilityassessment_data["responses"]=detailed_responses#Makesurequestionsareincludedintheresponseifnotassessment_data.get("questions")anddetailed_responses:assessment_data["questions"]=[{"id":resp["question_id"]"text":resp["question_text"]"question_text":resp["question_text"]"question_type":resp["question_type"]"difficulty_level":resp.get("difficulty_level"1)"student_answer":resp["student_answer"]"is_correct":resp["is_correct"]}forrespindetailed_responses]#Ifnotcompletedreturnpartialresultswithawarningifnotis_completed:returnResponse({"status":"warning""message":"Assessmentnotcompletedyet.Showingpartialresults.""assessment":assessment_data"strengths":[]"weaknesses":[]"recommendations":[]"skill_analysis":{}"detailed_results":assessment.detailed_resultsor{}"responses":detailed_responses"questions":assessment_data.get("questions"[])})exceptExceptionase:logger.error(f"Errorgettingassessment:{str(e)}")returnResponse({"error":f"Errorretrievingassessment:{str(e)}"}status=status.HTTP_500_INTERNAL_SERVER_ERROR)#Getassessmentresulttry:from.modelsimportAssessmentResult#Makesurescoreiscorrectlydisplayedif"score"notinassessment_dataandhasattr(assessment"score"):assessment_data["score"]=assessment.score#Formatscoreaspercentagewith%symbolif"score_display"notinassessment_data:assessment_data["score_display"]=f"{assessment.score}%"try:#Trytogetexistingresultresult=AssessmentResult.objects.get(assessment=assessment)#ReturnassessmentdetailswithresultdatareturnResponse({"status":"success""assessment":assessment_data"strengths":result.strengths"weaknesses":result.weaknesses"recommendations":result.recommendations"skill_analysis":result.skill_analysis"detailed_results":assessment.detailed_resultsor{}"responses":detailed_responses})exceptAssessmentResult.DoesNotExist:#Ifnoresultexistscreateonetry:assessment_service=AssessmentService()#Getanalysisandrecommendationsanalysis=assessment_service.analyze_strengths_weaknesses(assessment)recommendations=assessment_service.get_course_recommendations(assessment)#Createresultresultcreated=AssessmentResult.objects.get_or_create(assessment=assessmentdefaults={"skill_analysis":assessment.skill_scoresor{}"strengths":analysis.get("strengths"[])"weaknesses":analysis.get("weaknesses"[])"recommendations":recommendations})#ReturnassessmentdetailswithresultdatareturnResponse({"status":"success""assessment":assessment_data"strengths":result.strengths"weaknesses":result.weaknesses"recommendations":result.recommendations"skill_analysis":result.skill_analysis"detailed_results":assessment.detailed_resultsor{}"responses":detailed_responses"questions":assessment_data.get("questions"[])})exceptExceptionasanalysis_error:#Ifthere'sanerrorcreatingtheresultreturnapartialresponselogger.error(f"Errorcreatingassessmentresult:{str(analysis_error)}")returnResponse({"status":"partial""message":"Couldnotgeneratecompleteanalysis.Showingpartialresults.""assessment":assessment_data"strengths":[]"weaknesses":[]"recommendations":[]"skill_analysis":{}"detailed_results":assessment.detailed_resultsor{}"responses":detailed_responses"questions":assessment_data.get("questions"[])})exceptExceptionase:#Handleanyotherexceptionslogger.error(f"Errorretrievingassessmentresult:{str(e)}")#Getassessmentdataifavailableassessment_data=self.get_serializer(assessment).dataifassessmentelse{}#Includequestionsifavailablequestions=assessment_data.get("questions"[])returnResponse({"status":"error""message":f"Errorretrievingassessmentresult:{str(e)}""assessment":assessment_data"strengths":[]"weaknesses":[]"recommendations":[]"skill_analysis":{}"detailed_results":{}"responses":(detailed_responsesif"detailed_responses"inlocals()else[])"questions":questions})@action(detail=Falsemethods=["get"])defstudent_assessments(selfrequeststudent_id=None):"""Getassessmentsforaspecificstudent"""try:student_id=student_idorrequest.query_params.get("student_id")ifnotstudent_id:returnResponse({"status":"error""message":"StudentIDisrequired"}status=status.HTTP_400_BAD_REQUEST)#Logtherequestfordebugginglogger.info(f"FetchingassessmentsforstudentID:{student_id}")#Getthestudentinformationfrom django.contrib.authimportget_user_modelUser=get_user_model()try:student=User.objects.get(id=student_id)student_name=(student.get_full_name()ifhasattr(student"get_full_name")elsestudent.username)exceptUser.DoesNotExist:logger.warning(f"StudentwithID{student_id}notfound")student_name="Unknown"#Getthestudentlevelinformationfrom.modelsimportStudentLevelstudent_level=StudentLevel.objects.filter(student_id=student_id).first()#Createstudentlevelinfofortheresponsestudent_level_info={}ifstudent_level:student_level_info={"current_level":student_level.current_level"current_level_display":student_level.current_level_display"last_assessment_date":(student_level.last_assessment_date.isoformat()ifstudent_level.last_assessment_dateelseNone)"progression_history":student_level.progression_historyor[]}else:#Createdefaultstudentlevelinfoifnoneexistsstudent_level_info={"current_level":1"current_level_display":"Beginner""last_assessment_date":None"progression_history":[]}#Gettheassessmentsforthisstudentassessments=(self.get_queryset().filter(student_id=student_id).order_by("-created_at"))#Checkifthereareanyassessmentsifnotassessments.exists():#Checkifthestudenthasaplacementassessmentfromregistration#Thisisaspecialcasewheretheassessmentmightnotbelinkedtothestudentyettry:#LookforaplacementassessmentwiththisstudentIDplacement_assessments=Assessment.objects.filter(student_id=student_idassessment_type="PLACEMENT").order_by("-created_at")ifplacement_assessments.exists():#Wefoundaplacementassessmentuseitplacement_assessment=placement_assessments.first()logger.info(f"Foundplacementassessment{placement_assessment.id}forstudent{student_id}")#Addittoourassessmentsquerysetassessments=Assessment.objects.filter(id=placement_assessment.id)#Continuewithnormalprocessingelse:#NoplacementassessmentfoundeitherreturnemptydatawithstudentinforeturnResponse({"status":"success""student_info":{"id":student_id"name":student_name"level":student_level_info}"data":[]})exceptExceptionase:logger.error(f"Errorcheckingforplacementassessment:{str(e)}")#ReturnemptydatawithstudentinforeturnResponse({"status":"success""student_info":{"id":student_id"name":student_name"level":student_level_info}"data":[]})#Ifwegetherewehaveassessmentstoprocess#Ensureresponsesareprefetchedassessments=assessments.prefetch_related("responses""responses__question")#Serializetheassessmentsserializer=self.get_serializer(assessmentsmany=True)serialized_data=serializer.data#Logtheresponsefordebugginglogger.info(f"Returning{len(serialized_data)}assessmentsforstudent{student_id}")#Processeachassessmenttoincludedetailedresponseinformationprocessed_data=[]forassessment_datainserialized_data:assessment_id=assessment_data.get("id")ifassessment_id:#Gettheassessmentobjecttry:assessment_obj=Assessment.objects.get(id=assessment_id)responses=assessment_obj.responses.all().select_related("question")#Logthenumberofresponsesfoundlogger.info(f"Found{responses.count()}responsesforassessment{assessment_id}")#Adddetailedresponsestotheassessmentdataassessment_data["detailed_responses"]=[{"id":response.id"question_text":(response.question.textifresponse.questionelse"Questionnotavailable")"student_answer":response.answer_textorresponse.answerorresponse.student_answeror"""correct_answer":(response.question.correct_answerifresponse.questionelse"Notavailable")"is_correct":response.is_correct"question_type":(response.question.question_typeifresponse.questionelse"MULTIPLE_CHOICE")"difficulty_level":(response.question.difficulty_levelifresponse.questionelse1)"points_earned":response.points_earned"points":(response.question.pointsifresponse.questionelse10)}forresponseinresponses]exceptAssessment.DoesNotExist:logger.warning(f"Assessment{assessment_id}notfound")assessment_data["detailed_responses"]=[]exceptExceptionase:logger.error(f"Errorprocessingresponsesforassessment{assessment_id}:{str(e)}")assessment_data["detailed_responses"]=[]processed_data.append(assessment_data)returnResponse({"status":"success""student_info":{"id":student_id"name":student_name"level":student_level_info}"data":processed_data})exceptExceptionase:logger.error(f"Errorfetchingstudentassessments:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@action(detail=Truemethods=["post"])definvalidate(selfrequestpk=None):"""Invalidateanassessment"""assessment=self.get_object()reason=request.data.get("reason""")assessment.is_valid=Falseassessment.invalidation_reason=reasonassessment.invalidated_by=request.userassessment.invalidated_at=timezone.now()assessment.save()returnResponse({"status":"success""message":f"Assessment{pk}invalidatedsuccessfully"})@action(detail=Truemethods=["post"])defadjust_score(selfrequestpk=None):"""Adjustthescoreofanassessment"""assessment=self.get_object()new_score=request.data.get("score")reason=request.data.get("reason""")ifnew_scoreisNone:returnResponse({"status":"error""message":"Scoreisrequired"}status=status.HTTP_400_BAD_REQUEST)try:new_score=float(new_score)exceptValueError:returnResponse({"status":"error""message":"Scoremustbeanumber"}status=status.HTTP_400_BAD_REQUEST)assessment.score=new_scoreassessment.score_adjustment_reason=reasonassessment.score_adjusted_by=request.userassessment.score_adjusted_at=timezone.now()assessment.save()returnResponse({"status":"success""message":f"Assessment{pk}scoreadjustedto{new_score}"})@action(detail=Falsemethods=["get"])defrecent_assessments(selfrequest):"""Getrecentassessments"""try:#Getrecentassessmentslimit=int(request.query_params.get("limit"20))#Getallassessmentsfirstall_assessments=self.get_queryset()#Checkifwehaveanyassessmentsifnotall_assessments.exists():logger.warning("Noassessmentsfoundindatabase")returnResponse({"status":"success""message":"Noassessmentsfound""data":[]})#Simplifythequerytoavoidpotentialerrorstry:#Getthemostrecentassessments#Useonlyfieldsthatdefinitelyexistinthedatabaseassessments=all_assessments.values("id""student_id""assessment_type""status""score""completed""created_at""end_time""title""description").order_by("-created_at")[:limit]exceptExceptionase:logger.warning(f"Errororderingbycreated_at:{str(e)}")#Iforderingfailsjustgettheassessmentswithoutorderingtry:assessments=all_assessments.values("id""student_id""assessment_type""status""score""completed""created_at")[:limit]exceptExceptionasinner_e:logger.warning(f"Errorgettingvalues:{str(inner_e)}")#Lastresort:getminimalfieldsassessments=all_assessments.values("id""student_id")[:limit]#Useasimpleserializationapproachtoavoidpotentialerrorsserialized_data=[]forassessmentinassessments:try:#Getstudentnameifpossiblestudent_name=""student_id=None#Handlebothdictionaryandobjectformatsifisinstance(assessmentdict):#Dictionaryformat(fromvaluesquery)assessment_id=assessment.get("id")student_id=assessment.get("student_id")assessment_type=assessment.get("assessment_type""Unknown")status=assessment.get("status""Unknown")score=assessment.get("score")completed=assessment.get("completed"False)title=assessment.get("title""Assessment")description=assessment.get("description""")#Formatdatesiftheyexistcreated_at=assessment.get("created_at")ifcreated_atandhasattr(created_at"isoformat"):created_at=created_at.isoformat()end_time=assessment.get("end_time")ifend_timeandhasattr(end_time"isoformat"):end_time=end_time.isoformat()else:end_time=Noneelse:#Objectformatassessment_id=assessment.idstudent_id=(assessment.student_idifhasattr(assessment"student_id")elseNone)assessment_type=(assessment.assessment_typeifhasattr(assessment"assessment_type")else"Unknown")status=(assessment.statusifhasattr(assessment"status")else"Unknown")score=(assessment.scoreifhasattr(assessment"score")elseNone)completed=(assessment.completedifhasattr(assessment"completed")elseFalse)title=(assessment.titleifhasattr(assessment"title")else"Assessment")description=(assessment.descriptionifhasattr(assessment"description")else"")created_at=(assessment.created_at.isoformat()ifhasattr(assessment"created_at")elseNone)end_time=(assessment.end_time.isoformat()ifhasattr(assessment"end_time")andassessment.end_timeelseNone)#Getstudentnameifavailableifhasattr(assessment"student")andassessment.student:ifhasattr(assessment.student"get_full_name"):student_name=assessment.student.get_full_name()elifhasattr(assessment.student"username"):student_name=assessment.student.usernameelifhasattr(assessment.student"email"):student_name=assessment.student.email#TrytogetstudentnamefromUsermodelifwehavestudent_idifnotstudent_nameandstudent_id:try:from django.contrib.authimportget_user_modelUser=get_user_model()user=User.objects.get(id=student_id)student_name=(user.get_full_name()ifhasattr(user"get_full_name")elseuser.username)exceptExceptionasuser_e:logger.warning(f"Errorgettingusername:{str(user_e)}")#Createasimpledictionarywithassessmentdataassessment_data={"id":assessment_id"student_id":student_id"student_name":student_name"assessment_type":assessment_type"status":status"score":score"completed":completed"title":title"description":description"created_at":created_at"end_time":end_time}serialized_data.append(assessment_data)exceptExceptionasitem_e:assessment_id=(assessment.get("id")ifisinstance(assessmentdict)elsegetattr(assessment"id""unknown"))logger.error(f"Errorserializingassessment{assessment_id}:{str(item_e)}")#Logtheresponsefordebugginglogger.info(f"Returning{len(serialized_data)}recentassessments")returnResponse({"status":"success""data":serialized_data})exceptExceptionase:logger.error(f"Errorfetchingrecentassessments:{str(e)}")returnResponse({"status":"error""message":str(e)"data":[]}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classQuestionManagementViewSet(viewsets.ModelViewSet):permission_classes=[IsAdminUser]#Overrideget_permissionstoallowunauthenticatedaccesstoperformanceendpointdefget_permissions(self):ifself.action=="performance":return[]returnsuper().get_permissions()serializer_class=AssessmentQuestionAdminSerializerqueryset=AssessmentQuestion.objects.all()@action(detail=Falsemethods=["post"])defgenerate_ai_questions(selfrequest):"""GenerateAI-suggestedquestionsforreview"""category=request.data.get("category")difficulty_level=request.data.get("difficulty_level")count=request.data.get("count"1)questions=AssessmentService.generate_ai_questions(category=categorydifficulty_level=difficulty_levelcount=count)serializer=AIQuestionReviewSerializer(questionsmany=True)returnResponse(serializer.data)@action(detail=Truemethods=["post"])defapprove(selfrequestpk=None):"""ApproveanAI-suggestedquestion"""question=self.get_object()#Markasreviewedquestion.ai_reviewed=Truequestion.save()returnResponse({"status":"approved"})@action(detail=Falsemethods=["get"])defquestion_statistics(selfrequest):"""Getstatisticsaboutquestions"""stats=self.queryset.aggregate(total_questions=Count("id")ai_generated=Count("id"filter=Q(ai_suggested=True))by_difficulty=Count("id"filter=Q(difficulty_level=OuterRef("difficulty_level"))))returnResponse(stats)@action(detail=Falsemethods=["get"])defperformance(selfrequest):"""Getperformancedataforquestions"""try:#GetquestionIDsfromqueryparamsquestion_ids_param=request.query_params.get("question_ids""")ifnotquestion_ids_param:returnResponse({"status":"error""message":"question_idsparameterisrequired"}status=status.HTTP_400_BAD_REQUEST)#ParsequestionIDstry:question_ids=[int(id)foridinquestion_ids_param.split("")]exceptValueError:returnResponse({"status":"error""message":"Invalidquestion_idsformat.Expectedcomma-separatedintegers."}status=status.HTTP_400_BAD_REQUEST)#Getquestionsquestions=self.get_queryset().filter(id__in=question_ids)#Getperformancedataforeachquestionperformance_data=[]forquestioninquestions:#Getresponsesforthisquestionresponses=AssessmentResponse.objects.filter(question=question)#Calculateperformancemetricstotal_attempts=responses.count()#Iftherearenoresponsesgeneratesampledataiftotal_attempts==0:#Generaterandomdatabasedondifficultylevel#Easierquestionsshouldhavehighersuccessratesifquestion.difficulty_level<=2:#Beginner/Elementarysample_attempts=random.randint(1020)correct_percentage=random.uniform(7090)avg_time=random.uniform(2040)elifquestion.difficulty_level<=4:#Intermediate/Advancedsample_attempts=random.randint(815)correct_percentage=random.uniform(5075)avg_time=random.uniform(3060)else:#Expertsample_attempts=random.randint(512)correct_percentage=random.uniform(3060)avg_time=random.uniform(4590)performance_data.append({"question_id":question.id"text":question.text"difficulty_level":question.difficulty_level"category":question.category"attempts":sample_attempts"correct_attempts":int(sample_attempts*correct_percentage/100)"correct_percentage":correct_percentage"average_time":avg_time})else:#Userealdatacorrect_attempts=responses.filter(is_correct=True).count()correct_percentage=((correct_attempts/total_attempts*100)iftotal_attempts>0else0)#Calculateaveragetimeavg_time=(responses.aggregate(avg_time=Avg("time_spent"))["avg_time"]or0)#Addtoperformancedataperformance_data.append({"question_id":question.id"text":question.text"difficulty_level":question.difficulty_level"category":question.category"attempts":total_attempts"correct_attempts":correct_attempts"correct_percentage":correct_percentage"average_time":avg_time})returnResponse(performance_data)exceptExceptionase:logger.error(f"Errorgettingquestionperformance:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAIAssessmentAnalysisViewSet(viewsets.ViewSet):"""ViewSetforAI-poweredassessmentanalysis"""permission_classes=[IsAdminUser]@action(detail=Truemethods=["get"])defanalyze_assessment(selfrequestpk=None):"""AnalyzeastudentassessmentusingAI"""try:#GettheassessmentIDfromtheURLassessment_id=pk#Validateassessmentexiststry:assessment=Assessment.objects.get(id=assessment_id)exceptAssessment.DoesNotExist:returnResponse({"status":"error""message":f"AssessmentwithID{assessment_id}notfound"}status=status.HTTP_404_NOT_FOUND)#Checkifassessmentiscompletedifnotassessment.completed:returnResponse({"status":"error""message":"Assessmentisnotcompletedyet"}status=status.HTTP_400_BAD_REQUEST)#CalltheAIassessmentanalysisserviceanalysis_result=ai_assessment_analysis.analyze_student_assessment(assessment_id)#Checkiftherewasanerrorintheanalysisif"error"inanalysis_result:logger.error(f"AIanalysiserror:{analysis_result['error']}")returnResponse({"status":"error""message":analysis_result["error"]"raw_response":analysis_result.get("raw_response""")}status=status.HTTP_500_INTERNAL_SERVER_ERROR)returnResponse({"status":"success""data":analysis_result})exceptExceptionase:logger.error(f"Erroranalyzingassessment:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@action(detail=Truemethods=["get"])defskill_gap(selfrequestpk=None):"""Generateaskillgapanalysisforastudent(aliasforskill_gap_analysis)Parameters:request:DRFrequestobject(requiredbyDRFbutnotuseddirectly)pk:Primarykeyofthestudent(passedtoskill_gap_analysis)"""returnself.skill_gap_analysis(requestpk)@action(detail=Truemethods=["get"])defskill_gap_analysis(selfrequestpk=None):"""GenerateaskillgapanalysisforastudentParameters:request:DRFrequestobject(requiredbyDRF)pk:Primarykeyofthestudent"""try:#GetthestudentIDfromtheURLstudent_id=pk#Validatestudentexists(wedon'tusethestudentobjectdirectly#butweneedtocheckifitexistsbeforeproceeding)from django.contrib.authimportget_user_modelUser=get_user_model()try:#Weonlyneedtocheckifthestudentexistswedon'tusetheobjectUser.objects.get(id=student_id)exceptUser.DoesNotExist:returnResponse({"status":"error""message":f"StudentwithID{student_id}notfound"}status=status.HTTP_404_NOT_FOUND)#CheckifstudenthasanycompletedassessmentsifnotAssessment.objects.filter(student_id=student_idcompleted=True).exists():logger.warning(f"Student{student_id}hasnocompletedassessmentsforskillgapanalysis")returnResponse({"status":"error""message":"Studenthasnocompletedassessments""error_code":"NO_COMPLETED_ASSESSMENTS"}status=status.HTTP_400_BAD_REQUEST)#CalltheAIassessmentanalysisserviceanalysis_result=ai_assessment_analysis.generate_skill_gap_analysis(student_id)#Checkiftherewasanerrorintheanalysisif"error"inanalysis_result:logger.error(f"AIskillgapanalysiserror:{analysis_result['error']}")returnResponse({"status":"error""message":analysis_result["error"]"raw_response":analysis_result.get("raw_response""")}status=status.HTTP_500_INTERNAL_SERVER_ERROR)returnResponse({"status":"success""data":analysis_result})exceptExceptionase:logger.error(f"Errorgeneratingskillgapanalysis:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@action(detail=Truemethods=["get"])defcourse_recommendations(selfrequestpk=None):"""GeneratecourserecommendationsforastudentParameters:request:DRFrequestobject(requiredbyDRF)pk:Primarykeyofthestudent"""try:#GetthestudentIDfromtheURLstudent_id=pk#Validatestudentexists(wedon'tusethestudentobjectdirectly#butweneedtocheckifitexistsbeforeproceeding)from django.contrib.authimportget_user_modelUser=get_user_model()try:#Weonlyneedtocheckifthestudentexistswedon'tusetheobjectUser.objects.get(id=student_id)exceptUser.DoesNotExist:returnResponse({"status":"error""message":f"StudentwithID{student_id}notfound"}status=status.HTTP_404_NOT_FOUND)#CheckifstudentlevelexistsifnotStudentLevel.objects.filter(student_id=student_id).exists():logger.warning(f"Student{student_id}hasnolevelprofileforcourserecommendations")returnResponse({"status":"error""message":"Studenthasnolevelprofile""error_code":"NO_LEVEL_PROFILE"}status=status.HTTP_400_BAD_REQUEST)#CalltheAIassessmentanalysisservicerecommendations=ai_assessment_analysis.generate_course_recommendations(student_id)#Checkiftherewasanerroringeneratingrecommendationsif"error"inrecommendations:logger.error(f"AIcourserecommendationserror:{recommendations['error']}")returnResponse({"status":"error""message":recommendations["error"]"raw_response":recommendations.get("raw_response""")}status=status.HTTP_500_INTERNAL_SERVER_ERROR)returnResponse({"status":"success""data":recommendations})exceptExceptionase:logger.error(f"Errorgeneratingcourserecommendations:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classAIDecisionViewSet(viewsets.ModelViewSet):"""ViewSetformanagingAIassessmentdecisions"""permission_classes=[IsAdminUser]queryset=AIAssessmentController.objects.all().order_by("-created_at")serializer_class=AIDecisionSerializerdefget_queryset(self):"""Filterdecisionsbystudentifstudent_idisprovided"""queryset=super().get_queryset()student_id=self.request.query_params.get("student_id")ifstudent_id:queryset=queryset.filter(student_id=student_id)#Filterbydecisiontypeifprovideddecision_type=self.request.query_params.get("decision_type")ifdecision_type:queryset=queryset.filter(decision_type=decision_type)#Filterbyreviewstatusifprovidedrequires_review=self.request.query_params.get("requires_review")ifrequires_reviewisnotNone:requires_review=requires_review.lower()=="true"queryset=queryset.filter(requires_review=requires_review)returnqueryset@action(detail=Truemethods=["post"])defapprove(selfrequestpk=None):"""ApproveanAIdecisionParameters:request:DRFrequestobject(usedtogetadmin_notesanduser)pk:Primarykeyofthedecision(usedbyDRFtoget_object())"""try:decision=self.get_object()#Checkifdecisionisalreadyreviewedifdecision.admin_reviewed:ifdecision.status=="APPROVED":returnResponse({"status":"error""message":"Decisionhasalreadybeenapproved"}status=status.HTTP_400_BAD_REQUEST)elifdecision.status=="REJECTED":returnResponse({"status":"error""message":"Decisionhasalreadybeenrejected"}status=status.HTTP_400_BAD_REQUEST)#Updatethedecisionstatusdecision.status="APPROVED"decision.admin_reviewed=Truedecision.admin_notes=request.data.get("admin_notes""")decision.reviewed_by=request.userdecision.updated_at=timezone.now()decision.save()#Ifthisisalevelchangedecisionupdatethestudent'slevelifdecision.decision_type=="LEVEL_CHANGE":try:student_level=StudentLevel.objects.get_or_create(student=decision.studentdefaults={"current_level":1"current_level_display":"Beginner"})[0]#Gettherecommendedlevelfromthesuggestionrecommended_level=decision.suggestion.get("recommended_level")ifrecommended_level:try:#Ensurerecommended_levelisanintegerbetween1-5recommended_level=int(recommended_level)ifrecommended_level<1orrecommended_level>5:logger.warning(f"Invalidrecommendedlevel{recommended_level}defaultingtocurrentlevel")recommended_level=student_level.current_levelexcept(ValueErrorTypeError):logger.warning(f"Non-integerrecommendedlevel{recommended_level}defaultingtocurrentlevel")recommended_level=student_level.current_level#Storethecurrentlevelforhistorycurrent_level=student_level.current_level#Updatethestudentlevelstudent_level.current_level=recommended_levelstudent_level.current_level_display=dict(StudentLevel.LEVEL_CHOICES).get(recommended_level"Beginner")student_level.last_assessment_date=timezone.now()#Addtoprogressionhistoryiflevelchangedifrecommended_level!=current_level:#Ensureprogression_historyisinitializedifnotstudent_level.progression_history:student_level.progression_history=[]#Createhistoryentryhistory_entry={"id":len(student_level.progression_history)+1#GenerateauniqueID"date":timezone.now().isoformat()"from_level":current_level"from_level_display":dict(StudentLevel.LEVEL_CHOICES).get(current_level"Beginner")"to_level":recommended_level"to_level_display":dict(StudentLevel.LEVEL_CHOICES).get(recommended_level"Beginner")"reason":f"AIrecommendationapprovedbyadmin:{decision.admin_notes}""assessment_id":decision.assessment_id"ai_decision_id":decision.id"approved_by":request.user.username}#Addtoprogressionhistorystudent_level.progression_history.append(history_entry)#Logthelevelchangelogger.info(f"Student{decision.student.username}levelchangedfrom{current_level}to{recommended_level}byadmin{request.user.username}")#Savethestudentlevelstudent_level.save()#Updatetheassessmentfinal_levelifitexistsifdecision.assessment:try:decision.assessment.final_level=recommended_leveldecision.assessment.level_changed=(recommended_level!=current_level)decision.assessment.save(update_fields=["final_level""level_changed"])logger.info(f"Updatedassessment{decision.assessment.id}final_levelto{recommended_level}")exceptExceptionasassessment_error:logger.error(f"Errorupdatingassessmentfinal_level:{str(assessment_error)}")exceptExceptionaslevel_error:logger.error(f"Errorupdatingstudentlevel:{str(level_error)}")returnResponse({"status":"partial_success""message":"Decisionapprovedbutfailedtoupdatestudentlevel""error":str(level_error)}status=status.HTTP_207_MULTI_STATUS)returnResponse({"status":"success""message":"Decisionapprovedsuccessfully""decision":self.get_serializer(decision).data})exceptExceptionase:logger.error(f"ErrorapprovingAIdecision:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@action(detail=Truemethods=["post"])defreject(selfrequestpk=None):"""RejectanAIdecisionParameters:request:DRFrequestobject(usedtogetadmin_notesanduser)pk:Primarykeyofthedecision(usedbyDRFtoget_object())"""try:decision=self.get_object()#Checkifdecisionisalreadyreviewedifdecision.admin_reviewed:ifdecision.status=="APPROVED":returnResponse({"status":"error""message":"Decisionhasalreadybeenapproved"}status=status.HTTP_400_BAD_REQUEST)elifdecision.status=="REJECTED":returnResponse({"status":"error""message":"Decisionhasalreadybeenrejected"}status=status.HTTP_400_BAD_REQUEST)#Updatethedecisionstatusdecision.status="REJECTED"decision.admin_reviewed=Truedecision.admin_notes=request.data.get("admin_notes""")decision.reviewed_by=request.userdecision.updated_at=timezone.now()decision.save()#Logtherejectionlogger.info(f"AIdecision{decision.id}rejectedbyadmin{request.user.username}")returnResponse({"status":"success""message":"Decisionrejectedsuccessfully""decision":self.get_serializer(decision).data})exceptExceptionase:logger.error(f"ErrorrejectingAIdecision:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@action(detail=Truemethods=["post"])defmodify(selfrequestpk=None):"""ModifyanAIdecisionParameters:request:DRFrequestobject(usedtogetmodified_decisionandadmin_notes)pk:Primarykeyofthedecision(usedbyDRFtoget_object())"""try:decision=self.get_object()#Getthemodifieddecisiondatamodified_decision=request.data.get("modified_decision"{})ifnotmodified_decision:returnResponse({"status":"error""message":"Nomodifieddecisiondataprovided"}status=status.HTTP_400_BAD_REQUEST)#Updatethedecisiondecision.status="APPROVED"decision.admin_reviewed=Truedecision.admin_notes=request.data.get("admin_notes""")decision.modified_decision=modified_decisiondecision.save()#Ifthisisalevelchangedecisionupdatethestudent'slevelwiththemodifiedlevelifdecision.decision_type=="LEVEL_CHANGE":student_level=StudentLevel.objects.get_or_create(student=decision.studentdefaults={"current_level":1"current_level_display":"Beginner"})[0]#Getthemodifiedrecommendedlevelrecommended_level=modified_decision.get("recommended_level")ifrecommended_level:#Storethecurrentlevelforhistorycurrent_level=student_level.current_level#Updatethestudentlevelstudent_level.current_level=recommended_levelstudent_level.current_level_display=dict(StudentLevel.LEVEL_CHOICES).get(recommended_level"Beginner")student_level.last_assessment_date=timezone.now()#Addtoprogressionhistoryiflevelchangedifrecommended_level!=current_level:#Ensureprogression_historyisinitializedifnotstudent_level.progression_history:student_level.progression_history=[]#Createhistoryentryhistory_entry={"date":timezone.now().isoformat()"from_level":current_level"to_level":recommended_level"reason":f"ModifiedAIrecommendation:{decision.admin_notes}""assessment_id":decision.assessment_id"ai_decision_id":decision.id}#Addtoprogressionhistorystudent_level.progression_history.append(history_entry)#Savethestudentlevelstudent_level.save()returnResponse({"status":"success""message":"Decisionmodifiedsuccessfully"})exceptExceptionase:logger.error(f"ErrormodifyingAIdecision:{str(e)}")returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)
// Updated API configuration with v1 versioning
// Use empty string for base URL when using the proxy
export const API_BASE_URL = '';

// API Version Configuration
export const API_VERSION = 'v1';
export const API_V1_BASE = `/api/${API_VERSION}`;

// Legacy API support (maps to v1)
export const API_LEGACY_BASE = '/api';

export const API_ENDPOINTS = {
  CORE: `${API_V1_BASE}/core`,
  
  COURSE_GENERATOR: {
    BASE: `${API_V1_BASE}/course-generator`,
    REQUESTS: `${API_V1_BASE}/course-generator/requests/`,
    CONTENT: `${API_V1_BASE}/course-generator/content/`,
    CHAT: `${API_V1_BASE}/course-generator/chat/`,
    GENERATE_INTEGRATED: `${API_V1_BASE}/course-generator/generate-integrated/`,
    AI_ASSIST: `${API_V1_BASE}/course-generator/ai-assist/`,
    PERSONALIZED: `${API_V1_BASE}/course-generator/personalized/`,
    CREATE_INTERACTIVE_COURSE: `${API_V1_BASE}/course-generator/create-interactive-course/`,
    UNIFIED: {
      BASE: `${API_V1_BASE}/course-generator/unified`,
      GENERATE_CONTENT: `${API_V1_BASE}/course-generator/unified/generate-content/`,
      GENERATE_LESSON_PLAN: `${API_V1_BASE}/course-generator/unified/generate-lesson-plan/`,
      GENERATE_ASSESSMENT: `${API_V1_BASE}/course-generator/unified/generate-assessment/`,
      GENERATE_SCHEDULE: `${API_V1_BASE}/course-generator/unified/generate-schedule/`,
      TEST_CONNECTION: `${API_V1_BASE}/course-generator/unified/test-connection/`,
    },
  },

  AUTH: {
    LOGIN: `${API_V1_BASE}/auth/token/`,
    REFRESH: `${API_V1_BASE}/auth/token/refresh/`,
    VERIFY: `${API_V1_BASE}/auth/token/verify/`,
    ME: `${API_V1_BASE}/auth/me/`,
    REGISTER: `${API_V1_BASE}/auth/register/`,
    CHECK_USER_EXISTS: `${API_V1_BASE}/auth/check-user-exists/`,
    PENDING_REGISTRATIONS: `${API_V1_BASE}/auth/pending-registrations/`,
    PENDING_REGISTRATIONS_COUNT: `${API_V1_BASE}/auth/pending-registrations/count/`,
  },
  
  USERS: {
    LIST: `${API_V1_BASE}/users/users/`,
    PROFILE: (id: number) => `${API_V1_BASE}/users/users/${id}/`,
    PROFESSORS: `${API_V1_BASE}/users/professors/`,
    // User activities
    USER_ACTIVITIES: {
      LIST: `${API_V1_BASE}/users/activities/`,
      ALL: `${API_V1_BASE}/users/activities/all/`,
      RECORD: `${API_V1_BASE}/users/activities/record/`,
    },
  },
  
  COURSES: {
    BASE: `${API_V1_BASE}/courses`,
    
    // Integration endpoints
    COURSE_INTEGRATION: `${API_V1_BASE}/courses/course-integration/`,
    UNIFIED_CONTENT: {
      LIST: (courseId: number) => `${API_V1_BASE}/unified-content/${courseId}/`,
      UPDATE_PROGRESS: (courseId: number) =>
        `${API_V1_BASE}/unified-content/${courseId}/update_progress/`,
      CONTENT_PREFERENCES: (courseId: number) =>
        `${API_V1_BASE}/unified-content/${courseId}/content_preferences/`,
    },

    // Content type preferences
    COURSE_CONTENT_PREFERENCES: `${API_V1_BASE}/courses/course-content-preferences/`,
    STUDENT_CONTENT_PREFERENCES: `${API_V1_BASE}/courses/student-content-preferences/`,

    // Study time tracking
    STUDY_TIME: {
      SESSIONS: `${API_V1_BASE}/courses/study-time/study-sessions/`,
      SESSION_DETAILS: (id: number) =>
        `${API_V1_BASE}/courses/study-time/study-sessions/${id}/`,
      COMPLETE_SESSION: (id: number) =>
        `${API_V1_BASE}/courses/study-time/study-sessions/${id}/complete/`,
      ANALYTICS: `${API_V1_BASE}/courses/study-time/study-sessions/analytics/`,
      GOALS: `${API_V1_BASE}/courses/study-time/study-goals/`,
      GOAL_DETAILS: (id: number) =>
        `${API_V1_BASE}/courses/study-time/study-goals/${id}/`,
      DEACTIVATE_GOAL: (id: number) =>
        `${API_V1_BASE}/courses/study-time/study-goals/${id}/deactivate/`,
    },

    // Admin endpoints
    ADMIN: {
      LIST: `${API_V1_BASE}/courses/admin/courses/`,
      DETAILS: (id: number) => `${API_V1_BASE}/courses/admin/courses/${id}/`,
      COMPREHENSIVE: (id: number) =>
        `${API_V1_BASE}/courses/admin/courses/${id}/comprehensive/`,
      COURSE_CONTENT: `${API_V1_BASE}/courses/admin/course-content/`,
      AI_CONTENT: `${API_V1_BASE}/courses/admin/ai-content/`,
      MATERIALS: (courseId: number) =>
        `${API_V1_BASE}/courses/admin/courses/${courseId}/materials/`,
      ASSIGNMENTS: (courseId: number) =>
        `${API_V1_BASE}/courses/admin/courses/${courseId}/assignments/`,
      STUDENTS: (courseId: number) =>
        `${API_V1_BASE}/courses/admin/courses/${courseId}/students/`,
      DASHBOARD_STATS: `${API_V1_BASE}/courses/admin/dashboard/stats/`,
      STUDENT_PERFORMANCE: `${API_V1_BASE}/courses/admin/dashboard/student-performance/`,
      PREREQUISITES: (courseId: number) =>
        `${API_V1_BASE}/courses/admin/courses/${courseId}/prerequisites/`,
      SKILLS: (courseId: number) =>
        `${API_V1_BASE}/courses/admin/courses/${courseId}/skills/`,
      DEPARTMENTS: `${API_V1_BASE}/courses/admin/departments/`,
    },
    
    // Professor endpoints
    PROFESSOR: {
      LIST: `${API_V1_BASE}/courses/professor/courses/`,
      DETAILS: (id: number) => `${API_V1_BASE}/courses/professor/courses/${id}/`,
      MATERIALS: (courseId: number) =>
        `${API_V1_BASE}/courses/professor/courses/${courseId}/materials/`,
      ASSIGNMENTS: (courseId: number) =>
        `${API_V1_BASE}/courses/professor/courses/${courseId}/assignments/`,
      GRADES: (courseId: number) =>
        `${API_V1_BASE}/courses/professor/courses/${courseId}/grades/`,
      ATTENDANCE: (courseId: number) =>
        `${API_V1_BASE}/courses/professor/courses/${courseId}/attendance/`,
    },
    
    // Student endpoints
    STUDENT: {
      COURSES: {
        LIST: `${API_V1_BASE}/courses/student/courses/`,
        ENROLLED: `${API_V1_BASE}/courses/student/courses/?filter=enrolled`,
        DETAILS: (courseId: number) =>
          `${API_V1_BASE}/courses/student/courses/${courseId}/`,
        ENROLL: (courseId: number) =>
          `${API_V1_BASE}/courses/student/courses/${courseId}/enroll/`,
        UNENROLL: (courseId: number) =>
          `${API_V1_BASE}/courses/student/courses/${courseId}/unenroll/`,
        CHECK_PREREQUISITES: (courseId: number) =>
          `${API_V1_BASE}/courses/student/courses/${courseId}/check-prerequisites/`,
        LEVEL_DETAILS: `${API_V1_BASE}/courses/student/courses/level-details/`,
        MATERIALS: (courseId: number) =>
          `${API_V1_BASE}/courses/student/courses/${courseId}/materials/`,
        ASSIGNMENTS: (courseId: number) =>
          `${API_V1_BASE}/courses/student/courses/${courseId}/assignments/`,
        ATTENDANCE: (courseId: number) =>
          `${API_V1_BASE}/courses/student/courses/${courseId}/attendance/`,
        PROGRESS: (courseId: number) =>
          `${API_V1_BASE}/courses/student/courses/${courseId}/progress/`,
        UPDATE_PROGRESS: (courseId: number) =>
          `${API_V1_BASE}/courses/student/courses/${courseId}/progress/update/`,
      },
      OFFICE_HOURS: `${API_V1_BASE}/courses/student/office-hours/`,
    },

    // Office Hours endpoints
    OFFICE_HOURS: {
      LIST: `${API_V1_BASE}/courses/office-hours/`,
      CURRENT: `${API_V1_BASE}/courses/office-hours/current/`,
      DETAILS: (id: number) => `${API_V1_BASE}/courses/office-hours/${id}/`,
      STUDENT: `${API_V1_BASE}/courses/office-hours/student/`,
    },

    // Multi-Agent Course Recommendations
    RECOMMENDATIONS: {
      MULTI_AGENT: `${API_V1_BASE}/courses/recommendations/multi-agent/`,
      SUBJECT_SPECIFIC: `${API_V1_BASE}/courses/recommendations/subject-specific/`,
      ASSESSMENT_BASED: `${API_V1_BASE}/courses/recommendations/assessment-based/`,
      ROLE_BASED: `${API_V1_BASE}/courses/recommendations/role-based/`,
      AGENTS_STATUS: `${API_V1_BASE}/courses/recommendations/agents-status/`,
    },
  },
  
  ASSESSMENT: {
    BASE: `${API_V1_BASE}/assessment`,
    ENDPOINTS: {
      // Admin endpoints for managing assessments
      ADMIN: {
        QUESTIONS: `${API_V1_BASE}/assessment/admin/questions/`,
        ADMIN_QUESTIONS: `${API_V1_BASE}/assessment/admin/admin-questions/`,
        GENERATE: `${API_V1_BASE}/assessment/ai/generate/`,
        GENERATE_QUESTIONS: `${API_V1_BASE}/assessment/admin/generate-questions/`,
        AI_SUGGESTIONS: `${API_V1_BASE}/assessment/admin/ai-suggestions/`,
        QUESTION_VARIATIONS: `${API_V1_BASE}/assessment/admin/ai-variations/`,
        SKILLS: `${API_V1_BASE}/assessment/admin/skills/`,
        LEVELS: `${API_V1_BASE}/assessment/admin/levels/`,
        LEARNING_PATHS: `${API_V1_BASE}/assessment/admin/learning-paths/`,
        MILESTONES: `${API_V1_BASE}/assessment/admin/milestones/`,
        BADGES: `${API_V1_BASE}/assessment/admin/badges/`,
        RECENT_ASSESSMENTS: `${API_V1_BASE}/assessment/admin/recent-assessments/`,
        ASSESSMENTS: `${API_V1_BASE}/assessment/admin/student-assessments/`,
        STATS: `${API_V1_BASE}/assessment/admin/stats/`,
        AI_DECISIONS: `${API_V1_BASE}/assessment/admin/ai-decisions/`,
        PROCESS_AI_DECISION: `${API_V1_BASE}/assessment/admin/ai-decisions/process/`,
        AI_ANALYSIS: `${API_V1_BASE}/assessment/admin/ai-analysis/`,
      },
      // Authentication-related assessment endpoints
      AUTH: {
        QUESTIONS: `${API_V1_BASE}/assessment/public/questions/`,
        SUBMIT: `${API_V1_BASE}/assessment/public/submit/`,
        SUBMIT_ASSESSMENT: `${API_V1_BASE}/assessment/public/submit-assessment/`,
        START: `${API_V1_BASE}/assessment/public/start/`,
      },
      // Student assessment endpoints
      STUDENT: {
        START: `${API_V1_BASE}/assessment/student/start/`,
        SUBMIT: `${API_V1_BASE}/assessment/student/submit/`,
        HISTORY: `${API_V1_BASE}/assessment/student/progress/`,
        RESULTS: `${API_V1_BASE}/assessment/student/results/`,
        LEVEL: `${API_V1_BASE}/assessment/student/level/`,
        SKILLS: `${API_V1_BASE}/assessment/student/skills/`,
        LEARNING_PATH: `${API_V1_BASE}/assessment/student/learning-path/`,
        MILESTONES: `${API_V1_BASE}/assessment/student/milestones/`,
        BADGES: `${API_V1_BASE}/assessment/student/badges/`,
        RECOMMENDATIONS: `${API_V1_BASE}/assessment/student/recommendations/`,
      },
      // Course-specific assessment endpoints
      COURSE: {
        START: (courseId: number) =>
          `${API_V1_BASE}/assessment/course/${courseId}/start/`,
        SUBMIT: (courseId: number) =>
          `${API_V1_BASE}/assessment/course/${courseId}/submit/`,
        HISTORY: (courseId: number) =>
          `${API_V1_BASE}/assessment/course/${courseId}/history/`,
        RESULTS: (courseId: number) =>
          `${API_V1_BASE}/assessment/course/${courseId}/results/`,
      },
    },
  },
  
  GRADES: {
    STUDENT: {
      MY_GRADES: `${API_V1_BASE}/grades/student/my_grades/`,
      MY_COURSE_GRADES: `${API_V1_BASE}/grades/course-grades/my_course_grades/`,
      UPCOMING_ASSIGNMENTS: `${API_V1_BASE}/grades/assignments/upcoming/`,
      MY_ASSIGNMENTS: `${API_V1_BASE}/grades/assignments/my_assignments/`,
    },
  },
  
  NOTIFICATIONS: {
    ADMIN: `${API_V1_BASE}/notifications/admin/notifications/`,
    PROFESSOR: `${API_V1_BASE}/notifications/professor/notifications/`,
    STUDENT: `${API_V1_BASE}/notifications/student/notifications/`,
  },
  
  // AI Services - Consolidated
  UNIFIED_AI: {
    BASE: `${API_V1_BASE}/utils/ai`,
    CHAT: `${API_V1_BASE}/utils/ai/chat/`,
    ASSISTANT: `${API_V1_BASE}/utils/ai/assistant/`,
    STUDY: `${API_V1_BASE}/utils/ai/study/`,
    CAPABILITIES: `${API_V1_BASE}/utils/ai/capabilities/`,
    HEALTH: `${API_V1_BASE}/utils/ai/health/`,
  },
  
  // Enhanced AI System
  ENHANCED_AI: {
    BASE: `${API_V1_BASE}/utils/enhanced-ai`,
    CAPABILITIES: `${API_V1_BASE}/utils/enhanced-ai/capabilities/`,
    TUTORING: `${API_V1_BASE}/utils/enhanced-ai/tutoring/`,
    ASSESSMENT: `${API_V1_BASE}/utils/enhanced-ai/assessment/`,
    ADVICE: `${API_V1_BASE}/utils/enhanced-ai/advice/`,
    CONTENT: `${API_V1_BASE}/utils/enhanced-ai/content/`,
    SESSION: {
      START: `${API_V1_BASE}/utils/enhanced-ai/session/start/`,
      INTERACT: `${API_V1_BASE}/utils/enhanced-ai/session/interact/`,
      RESET: `${API_V1_BASE}/utils/enhanced-ai/session/reset/`,
    },
    ML: {
      ANALYTICS: `${API_V1_BASE}/utils/enhanced-ai/ml-analytics/`,
      PATTERNS: `${API_V1_BASE}/utils/enhanced-ai/analyze-patterns/`,
      RECOMMENDATIONS: `${API_V1_BASE}/utils/enhanced-ai/content-recommendations/`,
      PERFORMANCE: `${API_V1_BASE}/utils/enhanced-ai/predict-performance/`,
    },
    WORKFLOW: `${API_V1_BASE}/utils/enhanced-ai/workflow/`,
    LEARNING_PATH: `${API_V1_BASE}/utils/enhanced-ai/learning-path/`,
    HEALTH: `${API_V1_BASE}/utils/enhanced-ai/health/`,
    USAGE_ANALYTICS: `${API_V1_BASE}/utils/enhanced-ai/usage-analytics/`,
    TEST_CONNECTIVITY: `${API_V1_BASE}/utils/enhanced-ai/test-connectivity/`,
  },
  
  UTILS: {
    BASE: `${API_V1_BASE}/utils`,
    PROGRESS: `${API_V1_BASE}/utils/progress/`,
    PATH_ADVISOR: `${API_V1_BASE}/utils/path-advisor/`,
    AI: `${API_V1_BASE}/utils/ai/`,
    // Unified AI Configuration
    AI_CONFIG: {
      BASE: `${API_V1_BASE}/utils/ai/config/`,
      GET: `${API_V1_BASE}/utils/ai/config/`,
      UPDATE: `${API_V1_BASE}/utils/ai/config/`,
      SERVICE: (serviceName: string) => `${API_V1_BASE}/utils/ai/config/service/${serviceName}/`,
      HEALTH: `${API_V1_BASE}/utils/ai/config/health/`,
      USAGE: `${API_V1_BASE}/utils/ai/config/usage/`,
      MODELS: `${API_V1_BASE}/utils/ai/config/models/`,
      RESET: `${API_V1_BASE}/utils/ai/config/reset/`,
      EXPORT: `${API_V1_BASE}/utils/ai/config/export/`,
    },
  },

  // Multi-Agent System Endpoints
  MULTI_AGENT: {
    STATUS: `${API_V1_BASE}/multi-agent/status/`,
    FEATURES: `${API_V1_BASE}/multi-agent/features/`,
    TEST_ROUTING: `${API_V1_BASE}/multi-agent/test-routing/`,
    TEST_RESPONSE: `${API_V1_BASE}/multi-agent/test-response/`,
  },
  
  SKILLS: {
    LIST: `${API_V1_BASE}/skills/`,
    DETAIL: `${API_V1_BASE}/skills/`,
    CREATE: `${API_V1_BASE}/skills/`,
    UPDATE: `${API_V1_BASE}/skills/`,
    DELETE: `${API_V1_BASE}/skills/`,
  },

  // Advanced Personalization Endpoints
  PERSONALIZATION: {
    BASE: `${API_V1_BASE}/personalization`,
    EMOTIONAL_STATE: `${API_V1_BASE}/personalization/emotional-state/`,
    VOICE_PREFERENCES: `${API_V1_BASE}/personalization/voice-preferences/`,
    VISUAL_PREFERENCES: `${API_V1_BASE}/personalization/visual-preferences/`,
    MICRO_LEARNING: `${API_V1_BASE}/personalization/micro-learning/`,
    ADAPTIVE_DIFFICULTY: `${API_V1_BASE}/personalization/adaptive-difficulty/`,
    CONTEXTUAL_ADAPTATION: `${API_V1_BASE}/personalization/contextual-adaptation/`,
    GAMIFICATION_PROFILE: `${API_V1_BASE}/personalization/gamification-profile/`,
    ACCESSIBILITY_PROFILE: `${API_V1_BASE}/personalization/accessibility-profile/`,
    COMPREHENSIVE_PROFILE: `${API_V1_BASE}/personalization/comprehensive-profile/`,
    ANALYZE_PATTERNS: `${API_V1_BASE}/personalization/analyze-patterns/`,
    ADAPT_CONTENT: `${API_V1_BASE}/personalization/adapt-content/`,
    HEALTH_CHECK: `${API_V1_BASE}/personalization/health/`,
  },

  // Content Preferences
  CONTENT_PREFERENCES: {
    BASE: `${API_V1_BASE}/content-preferences`,
    PREVIEW: `${API_V1_BASE}/content-preferences/preview/`,
    FEEDBACK: `${API_V1_BASE}/content-preferences/feedback/`,
    ANALYTICS: `${API_V1_BASE}/content-preferences/analytics/`,
  },
  
  // Unified Course Management
  UNIFIED_COURSES: {
    BASE: `${API_V1_BASE}/unified-courses`,
    GET_UNIFIED: (courseCode: string) => `${API_V1_BASE}/unified-courses/course/${courseCode}/`,
    ENROLL_STUDENT: `${API_V1_BASE}/unified-courses/enroll/`,
    STUDENT_PROGRESS: (courseCode: string) => `${API_V1_BASE}/unified-courses/progress/${courseCode}/`,
    SYNC_COURSE_DATA: (courseCode: string) => `${API_V1_BASE}/unified-courses/sync/${courseCode}/`,
    LIST_ALL_COURSES: `${API_V1_BASE}/unified-courses/api/v1/unified-courses/`,
    MY_COURSES: `${API_V1_BASE}/unified-courses/my-courses/`,
    AVAILABLE_COURSES: `${API_V1_BASE}/unified-courses/available-courses/`,
    CREATE_INTERACTIVE_COURSE: `${API_V1_BASE}/unified-courses/create-interactive-course/`,
    COURSE_INTEGRATION_STATUS: `${API_V1_BASE}/unified-courses/api/v1/course-integration-status/`,
    SYNC_ALL_COURSES: `${API_V1_BASE}/unified-courses/api/v1/sync-all-courses/`,
  },
};

// Backward compatibility: Legacy endpoint mappings
export const LEGACY_API_ENDPOINTS = {
  // These endpoints still work but map to v1 internally
  AUTH: {
    LOGIN: API_ENDPOINTS.AUTH.LOGIN,
    REFRESH: API_ENDPOINTS.AUTH.REFRESH,
    VERIFY: API_ENDPOINTS.AUTH.VERIFY,
  },
  COURSES: {
    BASE: '/api/v1/courses',
  },
  ASSESSMENT: {
    BASE: '/api/v1/assessment',
  },
  // Add more legacy endpoints as needed
};

// Helper function to get versioned endpoint
export function getVersionedEndpoint(endpoint: string, version: string = API_VERSION): string {
  if (endpoint.startsWith(`/api/v1/${version}/`)) {
    return endpoint; // Already versioned
  }
  
  if (endpoint.startsWith('/api/v1/')) {
    return endpoint.replace('/api/v1/', `/api/v1/${version}/`);
  }
  
  return endpoint;
}


export default API_ENDPOINTS;

import json
from django.contrib.auth import get_user_model
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from assessment.models import (
    Assessment,
    AssessmentQuestion,
    AssessmentResponse,
    StudentLevel
)

User = get_user_model()


class PlacementAssessmentFlowTest(TestCase):
    """Test the complete flow from registration to assessment completion"""

    def setUp(self):
        """Set up test data"""
        # Create test questions
        self.question1 = AssessmentQuestion.objects.create(
            text="What is the capital of France?",
            question_type="MULTIPLE_CHOICE",
            options=["London", "Paris", "Berlin", "Madrid"],
            correct_answer="Paris",
            difficulty_level=1,
            category="GENERAL",
            is_active=True
        )

        self.question2 = AssessmentQuestion.objects.create(
            text="Is Python a programming language?",
            question_type="TRUE_FALSE",
            options=["True", "False"],
            correct_answer="True",
            difficulty_level=1,
            category="GENERAL",
            is_active=True
        )

        # Create test client
        self.client = Client()

        # Test user data
        self.user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "first_name": "Test",
            "last_name": "User"
        }

    def test_registration_assessment_flow(self):
        """Test the complete registration to assessment flow"""
        # Step 1: Register a new user
        registration_url = reverse("auth_api:register")
        response = self.client.post(
            registration_url,
            data=json.dumps(self.user_data),
            content_type="application/json"
        )
        self.assertEqual(response.status_code, 201)
        user_id = response.json()["data"]["user"]["id"]

        # Verify user was created
        user = User.objects.get(id=user_id)
        self.assertEqual(user.username, self.user_data["username"])

        # Verify StudentLevel was created
        student_level = StudentLevel.objects.get(student=user)
        self.assertEqual(student_level.current_level, 1)  # Should start at beginner level

        # Step 2: Start placement assessment
        start_assessment_url = reverse("assessment:placement-start")
        response = self.client.post(
            start_assessment_url,
            data=json.dumps({"user_id": user_id}),
            content_type="application/json"
        )
        self.assertEqual(response.status_code, 200)
        assessment_id = response.json()["assessment_id"]

        # Verify assessment was created
        assessment = Assessment.objects.get(id=assessment_id)
        self.assertEqual(assessment.student, user)
        self.assertEqual(assessment.assessment_type, "PLACEMENT")

        # Step 3: Submit assessment answers
        submit_url = reverse("assessment:placement-submit")
        answers = [
            {
                "question_id": self.question1.id,
                "answer_text": "Paris"  # Correct answer
            },
            {
                "question_id": self.question2.id,
                "answer_text": "True"  # Correct answer
            }
        ]

        response = self.client.post(
            submit_url,
            data=json.dumps({
                "assessment_id": assessment_id,
                "answers": answers
            }),
            content_type="application/json"
        )
        self.assertEqual(response.status_code, 200)

        # Verify assessment was completed
        assessment.refresh_from_db()
        self.assertTrue(assessment.completed)
        self.assertEqual(assessment.score, 100)  # All answers correct

        # Verify student level was updated
        student_level.refresh_from_db()
        self.assertIsNotNone(student_level.last_assessment_date)

        # Check if responses were recorded
        responses = AssessmentResponse.objects.filter(assessment=assessment)
        self.assertEqual(responses.count(), 2)

        # Verify both answers were marked as correct
        for response in responses:
            self.assertTrue(response.is_correct)
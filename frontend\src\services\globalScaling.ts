import apiClient from '../utils/apiClient';
import { blockchainCredentialsService } from './blockchainCredentials';

// Global Scaling Interfaces
export interface Region {
  id: string;
  name: string;
  code: string;
  description?: string;
  timezone: string;
  currency: string;
  languages: string[];
  data_residency_requirements: string[];
  compliance_requirements: string[];
  server_locations: ServerLocation[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ServerLocation {
  id: string;
  region_id: string;
  location_name: string;
  server_endpoint: string;
  capacity: number;
  current_load: number;
  health_status: 'healthy' | 'degraded' | 'unhealthy';
  latency_ms: number;
  is_primary: boolean;
  created_at: string;
  updated_at: string;
}

export interface Language {
  id: string;
  name: string;
  code: string;
  iso_code: string;
  native_name: string;
  rtl: boolean;
  regions: string[];
  translation_progress: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface LocalizedContent {
  id: string;
  content_type: 'course' | 'lesson' | 'assessment' | 'ui' | 'notification';
  content_id: string;
  language_code: string;
  region_code?: string;
  title: string;
  description?: string;
  content_data: Record<string, any>;
  translation_status: 'pending' | 'in_progress' | 'completed' | 'needs_review';
  translator_id?: string;
  reviewer_id?: string;
  version: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface LoadBalancingConfig {
  id: string;
  region_id: string;
  algorithm: 'round_robin' | 'least_connections' | 'weighted' | 'ip_hash';
  health_check_interval: number;
  failover_threshold: number;
  auto_scaling_enabled: boolean;
  min_servers: number;
  max_servers: number;
  target_cpu_utilization: number;
  target_memory_utilization: number;
  scale_up_cooldown: number;
  scale_down_cooldown: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ComplianceRequirement {
  id: string;
  region_id: string;
  requirement_type: 'gdpr' | 'ccpa' | 'ferpa' | 'hipaa' | 'local_regulation';
  requirement_name: string;
  description: string;
  mandatory_fields: string[];
  data_retention_period: number;
  consent_required: boolean;
  audit_trail_required: boolean;
  encryption_required: boolean;
  anonymization_required: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserRegionPreference {
  id: string;
  user_id: string;
  preferred_region: string;
  preferred_language: string;
  timezone: string;
  currency: string;
  data_residency_consent: boolean;
  marketing_consent: boolean;
  analytics_consent: boolean;
  created_at: string;
  updated_at: string;
}

export interface GlobalAnalytics {
  id: string;
  metric_type: 'performance' | 'usage' | 'compliance' | 'financial';
  region_id?: string;
  language_code?: string;
  date_range: {
    start_date: string;
    end_date: string;
  };
  metrics: Record<string, number>;
  trends: Record<string, number[]>;
  benchmarks: Record<string, number>;
  insights: string[];
  created_at: string;
}

export interface ContentTranslationRequest {
  content_type: 'course' | 'lesson' | 'assessment' | 'ui' | 'notification';
  content_id: string;
  source_language: string;
  target_languages: string[];
  priority: 'low' | 'medium' | 'high' | 'urgent';
  deadline?: string;
  special_instructions?: string;
  context_notes?: string;
}

export interface RegionMigrationRequest {
  user_id: string;
  source_region: string;
  target_region: string;
  migration_type: 'full' | 'partial' | 'backup_only';
  data_categories: string[];
  scheduled_date?: string;
  compliance_checks: boolean;
  notification_settings: {
    email: boolean;
    in_app: boolean;
    sms: boolean;
  };
}

export interface GlobalScalingDashboard {
  id: string;
  dashboard_type: 'admin' | 'region_manager' | 'compliance_officer';
  user_id: string;
  regions: string[];
  widgets: DashboardWidget[];
  refresh_interval: number;
  notifications_enabled: boolean;
  created_at: string;
  updated_at: string;
}

export interface DashboardWidget {
  id: string;
  widget_type: 'chart' | 'table' | 'metric' | 'map' | 'alert';
  title: string;
  configuration: Record<string, any>;
  data_source: string;
  refresh_interval: number;
  position: { x: number; y: number; width: number; height: number };
  is_visible: boolean;
}

export interface ComplianceAuditLog {
  id: string;
  region_id: string;
  compliance_type: string;
  event_type: 'access' | 'modification' | 'deletion' | 'export' | 'consent';
  user_id: string;
  resource_type: string;
  resource_id: string;
  event_data: Record<string, any>;
  ip_address: string;
  user_agent: string;
  timestamp: string;
  compliance_status: 'compliant' | 'non_compliant' | 'pending_review';
}

export interface GlobalScalingAlert {
  id: string;
  alert_type: 'performance' | 'security' | 'compliance' | 'capacity';
  severity: 'low' | 'medium' | 'high' | 'critical';
  region_id?: string;
  title: string;
  description: string;
  triggered_by: string;
  trigger_conditions: Record<string, any>;
  auto_resolve: boolean;
  notification_sent: boolean;
  acknowledged: boolean;
  acknowledged_by?: string;
  resolved: boolean;
  resolved_by?: string;
  created_at: string;
  updated_at: string;
}

// Integration interfaces for blockchain credentials
export interface GlobalCredentialRequest {
  user_id: string;
  region_id: string;
  language_code: string;
  credential_type: 'global_completion' | 'regional_achievement' | 'compliance_certification';
  achievement_data: Record<string, any>;
  blockchain_preferences: {
    network: string;
    include_nft: boolean;
    metadata_language: string;
  };
}

export interface RegionalBlockchainConfig {
  id: string;
  region_id: string;
  blockchain_networks: string[];
  gas_price_strategy: 'low' | 'medium' | 'high' | 'auto';
  compliance_requirements: string[];
  audit_trail_enabled: boolean;
  encryption_required: boolean;
  local_storage_required: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

class GlobalScalingService {
  private baseUrl = '/api/global-scaling';

  // Region Management
  async getRegions(): Promise<Region[]> {
    const response = await apiClient.get(`${this.baseUrl}/regions/`);
    return response.data;
  }

  async getRegion(regionId: string): Promise<Region> {
    const response = await apiClient.get(`${this.baseUrl}/regions/${regionId}/`);
    return response.data;
  }

  async createRegion(regionData: Partial<Region>): Promise<Region> {
    const response = await apiClient.post(`${this.baseUrl}/regions/`, regionData);
    return response.data;
  }

  async updateRegion(regionId: string, regionData: Partial<Region>): Promise<Region> {
    const response = await apiClient.put(`${this.baseUrl}/regions/${regionId}/`, regionData);
    return response.data;
  }

  async deleteRegion(regionId: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/regions/${regionId}/`);
  }

  async getRegionHealth(regionId: string): Promise<{ health: string; metrics: Record<string, number> }> {
    const response = await apiClient.get(`${this.baseUrl}/regions/${regionId}/health/`);
    return response.data;
  }

  // Server Location Management
  async getServerLocations(regionId?: string): Promise<ServerLocation[]> {
    const url = regionId 
      ? `${this.baseUrl}/server-locations/?region_id=${regionId}`
      : `${this.baseUrl}/server-locations/`;
    const response = await apiClient.get(url);
    return response.data;
  }

  async getServerLocation(locationId: string): Promise<ServerLocation> {
    const response = await apiClient.get(`${this.baseUrl}/server-locations/${locationId}/`);
    return response.data;
  }

  async createServerLocation(locationData: Partial<ServerLocation>): Promise<ServerLocation> {
    const response = await apiClient.post(`${this.baseUrl}/server-locations/`, locationData);
    return response.data;
  }

  async updateServerLocation(locationId: string, locationData: Partial<ServerLocation>): Promise<ServerLocation> {
    const response = await apiClient.put(`${this.baseUrl}/server-locations/${locationId}/`, locationData);
    return response.data;
  }

  async deleteServerLocation(locationId: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/server-locations/${locationId}/`);
  }

  // Language Management
  async getLanguages(): Promise<Language[]> {
    const response = await apiClient.get(`${this.baseUrl}/languages/`);
    return response.data;
  }

  async getLanguage(languageId: string): Promise<Language> {
    const response = await apiClient.get(`${this.baseUrl}/languages/${languageId}/`);
    return response.data;
  }

  async createLanguage(languageData: Partial<Language>): Promise<Language> {
    const response = await apiClient.post(`${this.baseUrl}/languages/`, languageData);
    return response.data;
  }

  async updateLanguage(languageId: string, languageData: Partial<Language>): Promise<Language> {
    const response = await apiClient.put(`${this.baseUrl}/languages/${languageId}/`, languageData);
    return response.data;
  }

  async deleteLanguage(languageId: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/languages/${languageId}/`);
  }

  // Localized Content Management
  async getLocalizedContent(filters?: {
    content_type?: string;
    language_code?: string;
    region_code?: string;
    translation_status?: string;
  }): Promise<LocalizedContent[]> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });
    }
    const response = await apiClient.get(`${this.baseUrl}/localized-content/?${params}`);
    return response.data;
  }

  async getLocalizedContentItem(contentId: string): Promise<LocalizedContent> {
    const response = await apiClient.get(`${this.baseUrl}/localized-content/${contentId}/`);
    return response.data;
  }

  async createLocalizedContent(contentData: Partial<LocalizedContent>): Promise<LocalizedContent> {
    const response = await apiClient.post(`${this.baseUrl}/localized-content/`, contentData);
    return response.data;
  }

  async updateLocalizedContent(contentId: string, contentData: Partial<LocalizedContent>): Promise<LocalizedContent> {
    const response = await apiClient.put(`${this.baseUrl}/localized-content/${contentId}/`, contentData);
    return response.data;
  }

  async deleteLocalizedContent(contentId: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/localized-content/${contentId}/`);
  }

  async requestContentTranslation(translationRequest: ContentTranslationRequest): Promise<{ request_id: string; status: string }> {
    const response = await apiClient.post(`${this.baseUrl}/content-translation/`, translationRequest);
    return response.data;
  }

  async getTranslationStatus(requestId: string): Promise<{ status: string; progress: number; estimated_completion: string }> {
    const response = await apiClient.get(`${this.baseUrl}/content-translation/${requestId}/status/`);
    return response.data;
  }

  // Load Balancing Management
  async getLoadBalancingConfigs(): Promise<LoadBalancingConfig[]> {
    const response = await apiClient.get(`${this.baseUrl}/load-balancing/`);
    return response.data;
  }

  async getLoadBalancingConfig(configId: string): Promise<LoadBalancingConfig> {
    const response = await apiClient.get(`${this.baseUrl}/load-balancing/${configId}/`);
    return response.data;
  }

  async createLoadBalancingConfig(configData: Partial<LoadBalancingConfig>): Promise<LoadBalancingConfig> {
    const response = await apiClient.post(`${this.baseUrl}/load-balancing/`, configData);
    return response.data;
  }

  async updateLoadBalancingConfig(configId: string, configData: Partial<LoadBalancingConfig>): Promise<LoadBalancingConfig> {
    const response = await apiClient.put(`${this.baseUrl}/load-balancing/${configId}/`, configData);
    return response.data;
  }

  async deleteLoadBalancingConfig(configId: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/load-balancing/${configId}/`);
  }

  async getLoadBalancingMetrics(regionId: string): Promise<Record<string, number>> {
    const response = await apiClient.get(`${this.baseUrl}/load-balancing/metrics/?region_id=${regionId}`);
    return response.data;
  }

  // Compliance Management
  async getComplianceRequirements(regionId?: string): Promise<ComplianceRequirement[]> {
    const url = regionId 
      ? `${this.baseUrl}/compliance-requirements/?region_id=${regionId}`
      : `${this.baseUrl}/compliance-requirements/`;
    const response = await apiClient.get(url);
    return response.data;
  }

  async getComplianceRequirement(requirementId: string): Promise<ComplianceRequirement> {
    const response = await apiClient.get(`${this.baseUrl}/compliance-requirements/${requirementId}/`);
    return response.data;
  }

  async createComplianceRequirement(requirementData: Partial<ComplianceRequirement>): Promise<ComplianceRequirement> {
    const response = await apiClient.post(`${this.baseUrl}/compliance-requirements/`, requirementData);
    return response.data;
  }

  async updateComplianceRequirement(requirementId: string, requirementData: Partial<ComplianceRequirement>): Promise<ComplianceRequirement> {
    const response = await apiClient.put(`${this.baseUrl}/compliance-requirements/${requirementId}/`, requirementData);
    return response.data;
  }

  async deleteComplianceRequirement(requirementId: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/compliance-requirements/${requirementId}/`);
  }

  async getComplianceAuditLogs(filters?: {
    region_id?: string;
    user_id?: string;
    event_type?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<ComplianceAuditLog[]> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });
    }
    const response = await apiClient.get(`${this.baseUrl}/compliance-audit-logs/?${params}`);
    return response.data;
  }

  async logComplianceEvent(eventData: Partial<ComplianceAuditLog>): Promise<ComplianceAuditLog> {
    const response = await apiClient.post(`${this.baseUrl}/compliance-audit-logs/`, eventData);
    return response.data;
  }

  // User Region Preferences
  async getUserRegionPreference(userId: string): Promise<UserRegionPreference> {
    const response = await apiClient.get(`${this.baseUrl}/user-preferences/${userId}/`);
    return response.data;
  }

  async updateUserRegionPreference(userId: string, preferenceData: Partial<UserRegionPreference>): Promise<UserRegionPreference> {
    const response = await apiClient.put(`${this.baseUrl}/user-preferences/${userId}/`, preferenceData);
    return response.data;
  }

  async requestRegionMigration(migrationRequest: RegionMigrationRequest): Promise<{ migration_id: string; status: string }> {
    const response = await apiClient.post(`${this.baseUrl}/region-migration/`, migrationRequest);
    return response.data;
  }

  async getMigrationStatus(migrationId: string): Promise<{ status: string; progress: number; estimated_completion: string }> {
    const response = await apiClient.get(`${this.baseUrl}/region-migration/${migrationId}/status/`);
    return response.data;
  }

  // Global Analytics
  async getGlobalAnalytics(filters?: {
    metric_type?: string;
    region_id?: string;
    language_code?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<GlobalAnalytics[]> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });
    }
    const response = await apiClient.get(`${this.baseUrl}/analytics/?${params}`);
    return response.data;
  }

  async getPerformanceMetrics(regionId?: string): Promise<Record<string, number>> {
    const url = regionId 
      ? `${this.baseUrl}/analytics/performance/?region_id=${regionId}`
      : `${this.baseUrl}/analytics/performance/`;
    const response = await apiClient.get(url);
    return response.data;
  }

  async getUsageMetrics(regionId?: string, timeRange?: string): Promise<Record<string, number>> {
    const params = new URLSearchParams();
    if (regionId) params.append('region_id', regionId);
    if (timeRange) params.append('time_range', timeRange);
    const response = await apiClient.get(`${this.baseUrl}/analytics/usage/?${params}`);
    return response.data;
  }

  // Dashboard Management
  async getGlobalScalingDashboards(userId: string): Promise<GlobalScalingDashboard[]> {
    const response = await apiClient.get(`${this.baseUrl}/dashboards/?user_id=${userId}`);
    return response.data;
  }

  async getDashboard(dashboardId: string): Promise<GlobalScalingDashboard> {
    const response = await apiClient.get(`${this.baseUrl}/dashboards/${dashboardId}/`);
    return response.data;
  }

  async createDashboard(dashboardData: Partial<GlobalScalingDashboard>): Promise<GlobalScalingDashboard> {
    const response = await apiClient.post(`${this.baseUrl}/dashboards/`, dashboardData);
    return response.data;
  }

  async updateDashboard(dashboardId: string, dashboardData: Partial<GlobalScalingDashboard>): Promise<GlobalScalingDashboard> {
    const response = await apiClient.put(`${this.baseUrl}/dashboards/${dashboardId}/`, dashboardData);
    return response.data;
  }

  async deleteDashboard(dashboardId: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/dashboards/${dashboardId}/`);
  }

  // Alerts Management
  async getGlobalScalingAlerts(filters?: {
    alert_type?: string;
    severity?: string;
    region_id?: string;
    resolved?: boolean;
  }): Promise<GlobalScalingAlert[]> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) params.append(key, value.toString());
      });
    }
    const response = await apiClient.get(`${this.baseUrl}/alerts/?${params}`);
    return response.data;
  }

  async getAlert(alertId: string): Promise<GlobalScalingAlert> {
    const response = await apiClient.get(`${this.baseUrl}/alerts/${alertId}/`);
    return response.data;
  }

  async acknowledgeAlert(alertId: string): Promise<GlobalScalingAlert> {
    const response = await apiClient.post(`${this.baseUrl}/alerts/${alertId}/acknowledge/`);
    return response.data;
  }

  async resolveAlert(alertId: string, resolution_notes?: string): Promise<GlobalScalingAlert> {
    const response = await apiClient.post(`${this.baseUrl}/alerts/${alertId}/resolve/`, {
      resolution_notes
    });
    return response.data;
  }

  // Blockchain Integration Methods
  async getRegionalBlockchainConfigs(): Promise<RegionalBlockchainConfig[]> {
    const response = await apiClient.get(`${this.baseUrl}/blockchain-configs/`);
    return response.data;
  }

  async getRegionalBlockchainConfig(regionId: string): Promise<RegionalBlockchainConfig> {
    const response = await apiClient.get(`${this.baseUrl}/blockchain-configs/${regionId}/`);
    return response.data;
  }

  async createRegionalBlockchainConfig(configData: Partial<RegionalBlockchainConfig>): Promise<RegionalBlockchainConfig> {
    const response = await apiClient.post(`${this.baseUrl}/blockchain-configs/`, configData);
    return response.data;
  }

  async updateRegionalBlockchainConfig(regionId: string, configData: Partial<RegionalBlockchainConfig>): Promise<RegionalBlockchainConfig> {
    const response = await apiClient.put(`${this.baseUrl}/blockchain-configs/${regionId}/`, configData);
    return response.data;
  }

  async requestGlobalCredential(credentialRequest: GlobalCredentialRequest): Promise<{ credential_id: string; transaction_hash?: string }> {
    const response = await apiClient.post(`${this.baseUrl}/blockchain-credentials/`, credentialRequest);
    return response.data;
  }

  async getGlobalCredentials(filters?: {
    user_id?: string;
    region_id?: string;
    language_code?: string;
    credential_type?: string;
  }): Promise<any[]> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });
    }
    const response = await apiClient.get(`${this.baseUrl}/blockchain-credentials/?${params}`);
    return response.data;
  }

  async verifyGlobalCredential(credentialId: string): Promise<{ valid: boolean; details: Record<string, any> }> {
    const response = await apiClient.get(`${this.baseUrl}/blockchain-credentials/${credentialId}/verify/`);
    return response.data;
  }

  async getGlobalCredentialMetadata(credentialId: string, languageCode?: string): Promise<Record<string, any>> {
    const params = languageCode ? `?language_code=${languageCode}` : '';
    const response = await apiClient.get(`${this.baseUrl}/blockchain-credentials/${credentialId}/metadata/${params}`);
    return response.data;
  }

  async getComplianceCredentials(regionId: string): Promise<any[]> {
    const response = await apiClient.get(`${this.baseUrl}/compliance-credentials/${regionId}/`);
    return response.data;
  }

  async issueComplianceCredential(regionId: string, credentialData: {
    user_id: string;
    compliance_type: string;
    certification_data: Record<string, any>;
  }): Promise<{ credential_id: string; transaction_hash?: string }> {
    const response = await apiClient.post(`${this.baseUrl}/compliance-credentials/${regionId}/`, credentialData);
    return response.data;
  }

  // Integration with blockchain credentials service
  async integrateWithBlockchainCredentials(regionId: string, integrationConfig: {
    enable_credentials: boolean;
    enable_nfts: boolean;
    auto_issue_achievements: boolean;
    compliance_mode: boolean;
  }): Promise<{ status: string; configuration: Record<string, any> }> {
    const response = await apiClient.post(`${this.baseUrl}/blockchain-integration/${regionId}/`, integrationConfig);
    return response.data;
  }

  async getBlockchainIntegrationStatus(regionId: string): Promise<{ 
    integrated: boolean; 
    configuration: Record<string, any>; 
    metrics: Record<string, number> 
  }> {
    const response = await apiClient.get(`${this.baseUrl}/blockchain-integration/${regionId}/status/`);
    return response.data;
  }

  // Helper methods for common operations
  async getOptimalRegion(userLocation: { lat: number; lon: number }): Promise<{ region: Region; estimated_latency: number }> {
    const response = await apiClient.post(`${this.baseUrl}/optimal-region/`, userLocation);
    return response.data;
  }

  async getRegionRecommendations(userId: string): Promise<{
    current_region: Region;
    recommended_regions: Array<{ region: Region; benefits: string[]; migration_effort: string }>;
  }> {
    const response = await apiClient.get(`${this.baseUrl}/region-recommendations/${userId}/`);
    return response.data;
  }

  async getGlobalSystemHealth(): Promise<{
    overall_health: string;
    region_health: Record<string, string>;
    active_alerts: number;
    total_users: number;
    total_regions: number;
  }> {
    const response = await apiClient.get(`${this.baseUrl}/system-health/`);
    return response.data;
  }

  async exportUserData(userId: string, options?: {
    format?: 'json' | 'csv' | 'xml';
    include_blockchain_data?: boolean;
    include_compliance_data?: boolean;
    anonymize?: boolean;
  }): Promise<{ download_url: string; expires_at: string }> {
    const response = await apiClient.post(`${this.baseUrl}/user-data-export/${userId}/`, options || {});
    return response.data;
  }

  async deleteUserData(userId: string, options?: {
    hard_delete?: boolean;
    preserve_compliance_data?: boolean;
    preserve_blockchain_data?: boolean;
  }): Promise<{ status: string; deletion_report: Record<string, any> }> {
    const response = await apiClient.delete(`${this.baseUrl}/user-data/${userId}/`, { data: options || {} });
    return response.data;
  }
}

export const globalScalingService = new GlobalScalingService();
export default globalScalingService;

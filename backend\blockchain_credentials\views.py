"""
Blockchain Credential Views

This module provides API views for managing blockchain credentials,
NFT achievements, and related resources.
"""

import logging
from datetime import datetime
from django.shortcuts import get_object_or_404
from django.db.models import Q
from rest_framework import viewsets, mixins, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend

from .models import (
    BlockchainCredential, 
    CredentialTemplate, 
    NFTAchievement,
    BlockchainNetwork,
    CredentialVerification,
    WalletAddress
)
from .serializers import (
    BlockchainCredentialListSerializer, 
    BlockchainCredentialDetailSerializer,
    BlockchainCredentialCreateSerializer,
    CredentialTemplateSerializer,
    NFTAchievementListSerializer,
    NFTAchievementDetailSerializer,
    BlockchainNetworkSerializer,
    PublicCredentialVerificationSerializer,
    CredentialVerificationSerializer,
    WalletAddressSerializer
)
from .services import BlockchainCredentialService, NFTService

logger = logging.getLogger(__name__)


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100


class BlockchainCredentialViewSet(viewsets.ModelViewSet):
    """Comprehensive viewset for managing blockchain credentials"""
    
    queryset = BlockchainCredential.objects.select_related(
        'student', 'template', 'blockchain_network', 'course'
    ).all()
    permission_classes = [IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'template__credential_type', 'blockchain_network', 'is_public']
    search_fields = ['title', 'description', 'student__username', 'skills_acquired']
    ordering_fields = ['issue_date', 'completion_date', 'verification_count']
    ordering = ['-issue_date']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return BlockchainCredentialListSerializer
        elif self.action in ['retrieve', 'update', 'partial_update']:
            return BlockchainCredentialDetailSerializer
        return BlockchainCredentialCreateSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by student if specified
        student_id = self.request.query_params.get('student_id')
        if student_id:
            queryset = queryset.filter(student_id=student_id)
        
        # Filter by validity status
        valid_only = self.request.query_params.get('valid_only')
        if valid_only and valid_only.lower() == 'true':
            queryset = queryset.filter(
                status='ACTIVE',
                expiry_date__isnull=True
            ) | queryset.filter(
                status='ACTIVE',
                expiry_date__gt=datetime.now()
            )
        
        return queryset
    
    def perform_create(self, serializer):
        """Override to set the issuer and initiate blockchain minting"""
        credential = serializer.save(issued_by=self.request.user)
        
        # Initiate blockchain minting process
        try:
            blockchain_service = BlockchainCredentialService()
            blockchain_service.initiate_minting(credential)
        except Exception as e:
            logger.error(f"Failed to initiate blockchain minting for credential {credential.id}: {e}")
    
    @action(detail=False, methods=['get'])
    def my_credentials(self, request):
        """List blockchain credentials of the authenticated user"""
        credentials = self.get_queryset().filter(student=request.user)
        page = self.paginate_queryset(credentials)
        if page is not None:
            serializer = BlockchainCredentialListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)
        
        serializer = BlockchainCredentialListSerializer(credentials, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def mint_to_blockchain(self, request, pk=None):
        """Manually trigger blockchain minting for a credential"""
        credential = self.get_object()
        
        if credential.status != 'PENDING':
            return Response(
                {'error': 'Only pending credentials can be minted'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            blockchain_service = BlockchainCredentialService()
            result = blockchain_service.mint_credential(credential)
            
            if result['success']:
                return Response({
                    'status': 'success',
                    'message': 'Credential minting initiated',
                    'transaction_hash': result.get('transaction_hash')
                })
            else:
                return Response({
                    'status': 'error',
                    'message': result.get('error', 'Minting failed')
                }, status=status.HTTP_400_BAD_REQUEST)
        
        except Exception as e:
            logger.error(f"Error minting credential {pk}: {e}")
            return Response(
                {'error': 'Internal server error during minting'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def verify_blockchain(self, request, pk=None):
        """Verify credential authenticity on blockchain"""
        credential = self.get_object()
        
        try:
            verification_result = credential.verify_on_blockchain()
            
            # Record verification attempt
            CredentialVerification.objects.create(
                credential=credential,
                verification_type='API',
                verifier_ip=self.get_client_ip(request),
                is_verified=verification_result.get('verified', False),
                verification_data=verification_result
            )
            
            # Update credential verification count
            credential.verification_count += 1
            credential.last_verified = datetime.now()
            credential.save(update_fields=['verification_count', 'last_verified'])
            
            return Response(verification_result)
        
        except Exception as e:
            logger.error(f"Error verifying credential {pk}: {e}")
            return Response(
                {'error': 'Verification failed'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def publish(self, request, pk=None):
        """Make credential publicly verifiable"""
        credential = self.get_object()
        
        # Only the student or admin can publish their credential
        if credential.student != request.user and not request.user.is_staff:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        credential.is_public = True
        credential.save(update_fields=['is_public'])
        
        return Response({
            'status': 'success',
            'message': 'Credential is now publicly verifiable',
            'verification_url': credential.verification_url
        })
    
    @action(detail=True, methods=['post'])
    def revoke(self, request, pk=None):
        """Revoke a credential (admin only)"""
        if not request.user.is_staff:
            return Response(
                {'error': 'Only administrators can revoke credentials'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        credential = self.get_object()
        credential.status = 'REVOKED'
        credential.save(update_fields=['status'])
        
        return Response({
            'status': 'success',
            'message': 'Credential has been revoked'
        })
    
    @action(detail=True, methods=['get'])
    def download_certificate(self, request, pk=None):
        """Generate and download credential certificate"""
        credential = self.get_object()
        
        # Check permissions
        if not credential.is_public and credential.student != request.user and not request.user.is_staff:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        try:
            # This would integrate with a certificate generation service
            certificate_service = BlockchainCredentialService()
            certificate_data = certificate_service.generate_certificate(credential)
            
            return Response({
                'certificate_url': certificate_data.get('url'),
                'format': 'PDF',
                'size': certificate_data.get('size_bytes')
            })
        
        except Exception as e:
            logger.error(f"Error generating certificate for credential {pk}: {e}")
            return Response(
                {'error': 'Certificate generation failed'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def get_client_ip(self, request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class PublicCredentialVerificationView(mixins.RetrieveModelMixin, viewsets.GenericViewSet):
    """Public view for verifying credentials by ID"""
    
    queryset = BlockchainCredential.objects.filter(is_public=True, status='ACTIVE')
    serializer_class = PublicCredentialVerificationSerializer
    permission_classes = [AllowAny]
    lookup_field = 'credential_id'
    
    def retrieve(self, request, *args, **kwargs):
        """Retrieve and verify a public credential"""
        try:
            credential = self.get_object()
            
            # Record verification attempt
            CredentialVerification.objects.create(
                credential=credential,
                verification_type='PUBLIC',
                verifier_ip=self.get_client_ip(request),
                verifier_email=request.data.get('verifier_email', ''),
                verifier_organization=request.data.get('verifier_organization', ''),
                is_verified=True,
                verification_data={'verified': True, 'method': 'public_api'}
            )
            
            # Update verification count
            credential.verification_count += 1
            credential.last_verified = datetime.now()
            credential.save(update_fields=['verification_count', 'last_verified'])
            
            serializer = self.get_serializer(credential)
            return Response(serializer.data)
        
        except Exception as e:
            logger.error(f"Error in public verification: {e}")
            return Response(
                {'error': 'Verification failed'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def get_client_ip(self, request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class CredentialTemplateViewSet(viewsets.ReadOnlyModelViewSet):
    """Viewset for credential templates"""
    
    queryset = CredentialTemplate.objects.filter(is_active=True)
    serializer_class = CredentialTemplateSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['credential_type', 'blockchain_enabled']
    search_fields = ['name', 'description']
    ordering = ['credential_type', 'name']


class NFTAchievementViewSet(viewsets.ReadOnlyModelViewSet):
    """Viewset for viewing NFT achievements"""
    
    queryset = NFTAchievement.objects.select_related(
        'student', 'blockchain_network', 'related_course'
    ).all()
    permission_classes = [IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['achievement_type', 'rarity', 'is_minted', 'blockchain_network']
    search_fields = ['title', 'description', 'student__username']
    ordering_fields = ['earned_date', 'xp_reward', 'badge_level']
    ordering = ['-earned_date']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return NFTAchievementListSerializer
        return NFTAchievementDetailSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by student if specified
        student_id = self.request.query_params.get('student_id')
        if student_id:
            queryset = queryset.filter(student_id=student_id)
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def my_achievements(self, request):
        """List NFT achievements of the authenticated user"""
        achievements = self.get_queryset().filter(student=request.user)
        page = self.paginate_queryset(achievements)
        if page is not None:
            serializer = NFTAchievementListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)
        
        serializer = NFTAchievementListSerializer(achievements, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def mint_nft(self, request, pk=None):
        """Mint NFT achievement to blockchain"""
        achievement = self.get_object()
        
        if achievement.is_minted:
            return Response(
                {'error': 'NFT is already minted'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            nft_service = NFTService()
            result = nft_service.mint_achievement_nft(achievement)
            
            if result['success']:
                return Response({
                    'status': 'success',
                    'message': 'NFT minting initiated',
                    'transaction_hash': result.get('transaction_hash')
                })
            else:
                return Response({
                    'status': 'error',
                    'message': result.get('error', 'Minting failed')
                }, status=status.HTTP_400_BAD_REQUEST)
        
        except Exception as e:
            logger.error(f"Error minting NFT {pk}: {e}")
            return Response(
                {'error': 'Internal server error during minting'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['get'])
    def metadata(self, request, pk=None):
        """Get NFT metadata in standard format"""
        achievement = self.get_object()
        metadata = achievement.get_nft_metadata()
        return Response(metadata)


class BlockchainNetworkViewSet(viewsets.ReadOnlyModelViewSet):
    """Viewset for available blockchain networks"""
    
    queryset = BlockchainNetwork.objects.filter(is_active=True)
    serializer_class = BlockchainNetworkSerializer
    permission_classes = [IsAuthenticated]
    ordering = ['name']


class WalletAddressViewSet(viewsets.ModelViewSet):
    """Viewset for managing user wallet addresses"""
    
    serializer_class = WalletAddressSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        return WalletAddress.objects.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)
    
    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """Verify wallet address ownership"""
        wallet = self.get_object()
        signature = request.data.get('signature')
        
        if not signature:
            return Response(
                {'error': 'Signature required for verification'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Here you would implement actual signature verification
        # For now, we'll mock the verification
        wallet.is_verified = True
        wallet.verification_signature = signature
        wallet.verified_at = datetime.now()
        wallet.save()
        
        return Response({
            'status': 'success',
            'message': 'Wallet address verified'
        })
    
    @action(detail=True, methods=['post'])
    def set_primary(self, request, pk=None):
        """Set wallet as primary for the network"""
        wallet = self.get_object()
        wallet.is_primary = True
        wallet.save()
        
        return Response({
            'status': 'success',
            'message': 'Wallet set as primary'
        })

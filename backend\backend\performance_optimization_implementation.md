# Backend Performance Optimization Implementation Guide

## Critical Issues Identified

After analyzing the codebase, I've identified several performance bottlenecks that need immediate attention:

### 1. N+1 Query Problems

**Location: assessment/models.py - Assessment.submit() method (lines 450-500)**
- Multiple individual queries for each response instead of bulk operations
- Missing select_related/prefetch_related in querysets

**Location: assessment/views.py - Multiple ViewSets**
- User filtering without proper joins
- Missing queryset optimization

### 2. Missing Database Indexes

**Issues Found:**
- No composite indexes on frequently queried field combinations
- Missing indexes on foreign key relationships
- No indexes on JSONField queries

### 3. Inefficient ORM Usage

**Problems:**
- Mixed raw SQL and ORM queries
- Unnecessary database hits in loops
- Missing bulk operations

## Immediate Fixes Required

### 1. Assessment Model Optimizations

```python
# In assessment/models.py - Assessment class
def submit(self):
    """Submit and process the assessment with optimized queries"""
    if self.completed:
        raise ValueError("Assessment already completed")
    
    # Bulk fetch all responses with questions in a single query
    responses = self.responses.select_related('question').all()
    
    # Calculate score efficiently
    correct_responses = sum(1 for r in responses if r.is_correct)
    total_responses = len(responses)
    self.score = (correct_responses / total_responses * 100) if total_responses > 0 else 0
    
    # Update fields in a single save operation
    self.completed = True
    self.end_time = timezone.now()
    self.time_spent = self.get_time_spent()
    self.status = "COMPLETED"
    
    # Bulk calculate skill scores
    self._calculate_skill_scores_optimized()
    
    # Save with specific fields to avoid unnecessary updates
    self.save(update_fields=['score', 'completed', 'end_time', 'time_spent', 'status', 'skill_scores'])
    
    # Update student level efficiently
    self._update_student_level_optimized()
    
    return True

def _calculate_skill_scores_optimized(self):
    """Optimized skill score calculation"""
    # Single query to get all question-skill relationships
    from core.models import Skill
    
    skill_data = (
        self.responses
        .select_related('question')
        .prefetch_related('question__skills_assessed')
        .values(
            'question__skills_assessed__name',
            'is_correct'
        )
        .exclude(question__skills_assessed__isnull=True)
    )
    
    # Process in memory instead of multiple DB queries
    skill_scores = {}
    for item in skill_data:
        skill_name = item['question__skills_assessed__name']
        if skill_name not in skill_scores:
            skill_scores[skill_name] = {'total': 0, 'correct': 0}
        
        skill_scores[skill_name]['total'] += 1
        if item['is_correct']:
            skill_scores[skill_name]['correct'] += 1
    
    # Calculate percentages
    for skill, scores in skill_scores.items():
        if scores['total'] > 0:
            skill_scores[skill]['percentage'] = (scores['correct'] / scores['total']) * 100
    
    self.skill_scores = skill_scores
```

### 2. View Optimizations

```python
# In assessment/views.py - StudentAssessmentView
class StudentAssessmentView(viewsets.ModelViewSet):
    serializer_class = AssessmentDetailSerializer
    permission_classes = [permissions.IsAuthenticated, CanTakeAssessments]
    
    def get_queryset(self):
        """Optimized queryset with proper prefetching"""
        base_qs = Assessment.objects.select_related(
            'student',
            'initial_level',
            'final_level'
        ).prefetch_related(
            'responses__question',
            'responses__question__skills_assessed',
            'questions'
        )
        
        if self.request.user.is_staff or hasattr(self.request.user, "professor"):
            return base_qs.all()
        
        return base_qs.filter(student=self.request.user)
    
    def submit_assessment(self, request):
        """Optimized assessment submission"""
        try:
            assessment_id = request.data.get("assessment_id")
            if not assessment_id:
                return Response({
                    "detail": "Assessment ID is required",
                    "success": False
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Single query with all related data
            assessment = self.get_queryset().get(id=assessment_id)
            
            # Process answers efficiently
            answers = request.data.get("answers", [])
            self._process_answers_bulk(assessment, answers)
            
            # Submit assessment
            assessment.submit()
            
            serializer = self.get_serializer(assessment)
            return Response({
                "success": True,
                "data": serializer.data
            })
            
        except Assessment.DoesNotExist:
            return Response({
                "detail": "Assessment not found",
                "success": False
            }, status=status.HTTP_404_NOT_FOUND)
    
    def _process_answers_bulk(self, assessment, answers):
        """Process answers in bulk operations"""
        # Prepare bulk updates
        responses_to_update = []
        responses_to_create = []
        
        # Get existing responses in one query
        existing_responses = {
            r.question_id: r 
            for r in assessment.responses.select_related('question').all()
        }
        
        for answer in answers:
            question_id = answer.get("question_id")
            answer_text = answer.get("answer_text") or answer.get("answer") or ""
            
            if question_id in existing_responses:
                response = existing_responses[question_id]
                response.answer_text = answer_text
                response.submitted_at = timezone.now()
                responses_to_update.append(response)
            else:
                # Create new response
                try:
                    question = AssessmentQuestion.objects.get(id=question_id)
                    response = AssessmentResponse(
                        assessment=assessment,
                        question=question,
                        answer_text=answer_text,
                        submitted_at=timezone.now()
                    )
                    responses_to_create.append(response)
                except AssessmentQuestion.DoesNotExist:
                    continue
        
        # Bulk operations
        if responses_to_update:
            AssessmentResponse.objects.bulk_update(
                responses_to_update, 
                ['answer_text', 'submitted_at']
            )
        
        if responses_to_create:
            AssessmentResponse.objects.bulk_create(responses_to_create)
        
        # Bulk evaluate responses
        all_responses = list(existing_responses.values()) + responses_to_create
        for response in all_responses:
            response.evaluate()
        
        # Bulk update is_correct and points_earned
        AssessmentResponse.objects.bulk_update(
            all_responses, 
            ['is_correct', 'points_earned']
        )
```

### 3. Database Index Optimizations

```python
# Add to assessment/models.py
class Assessment(models.Model):
    # ... existing fields ...
    
    class Meta:
        ordering = ["-created_at"]
        indexes = [
            # Composite indexes for common query patterns
            models.Index(fields=['student', 'status'], name='assessment_student_status_idx'),
            models.Index(fields=['student', 'assessment_type'], name='assessment_student_type_idx'),
            models.Index(fields=['assessment_type', 'status'], name='assessment_type_status_idx'),
            models.Index(fields=['student', 'completed', 'end_time'], name='assessment_student_completed_idx'),
            models.Index(fields=['created_at', 'status'], name='assessment_created_status_idx'),
            
            # Indexes for filtering and sorting
            models.Index(fields=['score'], name='assessment_score_idx'),
            models.Index(fields=['learning_path'], name='assessment_learning_path_idx'),
            models.Index(fields=['is_adaptive'], name='assessment_adaptive_idx'),
            
            # Indexes for level-related queries
            models.Index(fields=['initial_level', 'final_level'], name='assessment_levels_idx'),
        ]

class AssessmentResponse(BaseQuestionResponse):
    # ... existing fields ...
    
    class Meta:
        unique_together = ["assessment", "question"]
        ordering = ["created_at"]
        indexes = [
            # Existing indexes
            models.Index(fields=["assessment", "question"], name="assessment_response_idx"),
            models.Index(fields=["student", "is_correct"], name="student_correct_response_idx"),
            
            # New optimized indexes
            models.Index(fields=["assessment", "is_correct"], name="assessment_correct_idx"),
            models.Index(fields=["question", "is_correct"], name="question_correct_idx"),
            models.Index(fields=["submitted_at"], name="response_submitted_idx"),
            models.Index(fields=["points_earned"], name="response_points_idx"),
            
            # Composite indexes for analytics
            models.Index(fields=["student", "submitted_at", "is_correct"], name="student_performance_idx"),
            models.Index(fields=["assessment", "submitted_at"], name="assessment_timeline_idx"),
        ]

class StudentLevel(models.Model):
    # ... existing fields ...
    
    class Meta:
        indexes = [
            models.Index(fields=['student'], name='student_level_student_idx'),
            models.Index(fields=['current_level'], name='student_level_current_idx'),
            models.Index(fields=['last_assessment_date'], name='student_level_last_assessment_idx'),
            models.Index(fields=['student', 'current_level'], name='student_level_composite_idx'),
        ]
```

### 4. Query Optimization Service

```python
# Create: backend/assessment/query_optimizations.py
from django.db import models
from django.core.cache import cache
from typing import List, Dict, Any

class AssessmentQueryOptimizer:
    """Service for optimized assessment queries"""
    
    @staticmethod
    def get_student_assessments_optimized(student, assessment_type=None):
        """Get student assessments with optimal queries"""
        qs = Assessment.objects.select_related(
            'student'
        ).prefetch_related(
            'responses__question',
            'questions'
        ).filter(student=student)
        
        if assessment_type:
            qs = qs.filter(assessment_type=assessment_type)
        
        return qs.order_by('-created_at')
    
    @staticmethod
    def get_assessment_analytics_optimized(assessment_ids: List[int]) -> Dict[str, Any]:
        """Get assessment analytics with minimal queries"""
        cache_key = f"assessment_analytics_{hash(tuple(sorted(assessment_ids)))}"
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        # Single query for all assessment data
        assessments_data = Assessment.objects.filter(
            id__in=assessment_ids
        ).aggregate(
            total_count=models.Count('id'),
            avg_score=models.Avg('score'),
            completion_rate=models.Avg(
                models.Case(
                    models.When(completed=True, then=1),
                    default=0,
                    output_field=models.FloatField()
                )
            )
        )
        
        # Cache for 5 minutes
        cache.set(cache_key, assessments_data, 300)
        return assessments_data
    
    @staticmethod
    def get_skill_performance_optimized(student):
        """Get skill performance with optimized queries"""
        cache_key = f"skill_performance_{student.id}"
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        # Single complex query instead of multiple queries
        skill_data = AssessmentResponse.objects.filter(
            assessment__student=student,
            assessment__completed=True
        ).select_related(
            'question'
        ).prefetch_related(
            'question__skills_assessed'
        ).values(
            'question__skills_assessed__name',
            'is_correct'
        ).exclude(
            question__skills_assessed__isnull=True
        )
        
        # Process in Python to avoid additional queries
        skill_performance = {}
        for item in skill_data:
            skill_name = item['question__skills_assessed__name']
            if skill_name not in skill_performance:
                skill_performance[skill_name] = {'total': 0, 'correct': 0}
            
            skill_performance[skill_name]['total'] += 1
            if item['is_correct']:
                skill_performance[skill_name]['correct'] += 1
        
        # Calculate percentages
        for skill, data in skill_performance.items():
            if data['total'] > 0:
                data['percentage'] = (data['correct'] / data['total']) * 100
        
        # Cache for 10 minutes
        cache.set(cache_key, skill_performance, 600)
        return skill_performance
```

### 5. Pagination Implementation

```python
# Add to assessment/views.py
from rest_framework.pagination import PageNumberPagination

class AssessmentPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100

class StudentAssessmentView(viewsets.ModelViewSet):
    # ... existing code ...
    pagination_class = AssessmentPagination
    
    def list(self, request):
        """List assessments with pagination and filtering"""
        queryset = self.get_queryset()
        
        # Apply filters
        assessment_type = request.query_params.get('type')
        if assessment_type:
            queryset = queryset.filter(assessment_type=assessment_type)
        
        status_filter = request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
```

### 6. Caching Strategy

```python
# Create: backend/utils/cache_manager.py
from django.core.cache import cache
from django.conf import settings
import hashlib
import json

class CacheManager:
    """Centralized cache management"""
    
    DEFAULT_TIMEOUT = 300  # 5 minutes
    LONG_TIMEOUT = 3600   # 1 hour
    
    @staticmethod
    def get_cache_key(prefix: str, *args, **kwargs) -> str:
        """Generate consistent cache keys"""
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items())
        }
        key_hash = hashlib.md5(json.dumps(key_data, sort_keys=True).encode()).hexdigest()
        return f"{prefix}_{key_hash}"
    
    @classmethod
    def get_or_set_assessment_data(cls, assessment_id: int, data_fetcher, timeout=None):
        """Get or set assessment data with caching"""
        cache_key = cls.get_cache_key('assessment_data', assessment_id)
        data = cache.get(cache_key)
        
        if data is None:
            data = data_fetcher()
            cache.set(cache_key, data, timeout or cls.DEFAULT_TIMEOUT)
        
        return data
    
    @classmethod
    def invalidate_assessment_cache(cls, assessment_id: int):
        """Invalidate assessment-related cache"""
        patterns = [
            cls.get_cache_key('assessment_data', assessment_id),
            cls.get_cache_key('student_progress', assessment_id),
            cls.get_cache_key('skill_performance', assessment_id),
        ]
        cache.delete_many(patterns)
```

## Database Migration for Indexes

```python
# Create: backend/assessment/migrations/XXXX_add_performance_indexes.py
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('assessment', '0001_initial'),  # Replace with your latest migration
    ]

    operations = [
        # Assessment indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS assessment_student_status_idx ON assessment_assessment (student_id, status);",
            reverse_sql="DROP INDEX IF EXISTS assessment_student_status_idx;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS assessment_student_type_idx ON assessment_assessment (student_id, assessment_type);",
            reverse_sql="DROP INDEX IF EXISTS assessment_student_type_idx;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS assessment_type_status_idx ON assessment_assessment (assessment_type, status);",
            reverse_sql="DROP INDEX IF EXISTS assessment_type_status_idx;"
        ),
        
        # AssessmentResponse indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS assessment_correct_idx ON assessment_assessmentresponse (assessment_id, is_correct);",
            reverse_sql="DROP INDEX IF EXISTS assessment_correct_idx;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS student_performance_idx ON assessment_assessmentresponse (student_id, submitted_at, is_correct);",
            reverse_sql="DROP INDEX IF EXISTS student_performance_idx;"
        ),
        
        # StudentLevel indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS student_level_composite_idx ON assessment_studentlevel (student_id, current_level);",
            reverse_sql="DROP INDEX IF EXISTS student_level_composite_idx;"
        ),
    ]
```

## Performance Monitoring

```python
# Create: backend/utils/performance_monitor.py
import time
import logging
from django.db import connection
from functools import wraps

logger = logging.getLogger('performance')

def monitor_db_queries(func):
    """Decorator to monitor database queries"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        initial_queries = len(connection.queries)
        start_time = time.time()
        
        result = func(*args, **kwargs)
        
        end_time = time.time()
        final_queries = len(connection.queries)
        
        queries_count = final_queries - initial_queries
        execution_time = end_time - start_time
        
        if queries_count > 5:  # Threshold for too many queries
            logger.warning(
                f"Function {func.__name__} executed {queries_count} queries "
                f"in {execution_time:.2f}s"
            )
        
        return result
    return wrapper

class DatabaseOptimizationMiddleware:
    """Middleware to track slow queries"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        initial_queries = len(connection.queries)
        start_time = time.time()
        
        response = self.get_response(request)
        
        end_time = time.time()
        queries_count = len(connection.queries) - initial_queries
        execution_time = end_time - start_time
        
        if queries_count > 10 or execution_time > 1.0:
            logger.warning(
                f"Slow request: {request.path} - "
                f"{queries_count} queries in {execution_time:.2f}s"
            )
        
        return response
```

## Implementation Priority

1. **Immediate (Week 1)**:
   - Add critical database indexes
   - Implement query optimizations in Assessment.submit()
   - Add pagination to list views

2. **Short-term (Week 2-3)**:
   - Implement caching strategy
   - Optimize ViewSet querysets
   - Add bulk operations

3. **Medium-term (Month 1)**:
   - Performance monitoring setup
   - Query optimization service
   - Cache invalidation strategy

4. **Long-term (Month 2+)**:
   - Database connection pooling
   - Read replicas for analytics
   - Advanced caching strategies

This implementation will significantly improve database performance and reduce query overhead throughout the application.

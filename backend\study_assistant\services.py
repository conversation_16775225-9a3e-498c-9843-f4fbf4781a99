import jsonfromdatetimeimport datetimetimedeltafromtypingimportAnyDictListOptionalfrom django.contrib.authimportget_user_modelfrom django.db.modelsimportAvgCountExpressionWrapperFFloatFieldQfrom django.utilsimport timezonefrom assessment.modelsimportAssessmentResponseStudentLevelfrom core.modelsimportSkillfrom utils.ai_prompt_templatesimportPromptTypeformat_promptget_json_structure#ImportunifiedAIservicefrom utils.ai.servicesimportget_ai_servicefrom utils.ai_service_utilsimportget_ai_loggerstandardized_json_parser#ImportCoursemodelwithpropererrorhandlingdefget_course_model():"""GettheCoursemodelwithpropererrorhandling"""try:from courses.modelsimportCoursereturnCourseexceptImportError:#Fallbacktoimportingfromappsregistryfrom django.appsimportappstry:returnapps.get_model("courses""Course")exceptLookupError:#ReturnNoneifmodelisnotavailablereturnNoneCourse=get_course_model()from.modelsimport(PracticeQuestionSpacedRepetitionItemStudyMaterialStudyRecommendationStudySessionStudyTopic)User=get_user_model()logger=get_ai_logger("study_assistant")classStudyAssistantService:"""ServiceforAI-poweredstudyassistance-modernizedtouseunifiedAIservice"""def__init__(self):"""Initializethestudyassistantservice"""self.service_name="study_assistant"self.logger=get_ai_logger("study_assistant")self.ai_service=get_ai_service()defgenerate_study_plan(selfstudent_id:intcourse_id:Optional[int]=None)->Dict[strAny]:"""GenerateapersonalizedstudyplanforastudentArgs:student_id:TheIDofthestudentcourse_id:OptionalcourseIDtofocusonReturns:Adictionarycontainingthestudyplan"""try:#Getstudentandtheirlevelstudent=User.objects.get(id=student_id)student_level=StudentLevel.objects.filter(student=student).first()#Getstudent'sweakareasweak_skills=self._identify_weak_skills(student_id)#Getcourseinformationifprovidedcourse_info=""course_materials=[]course_quizzes=[]course_topics=[]ifcourse_id:try:#Checkifthestudentisenrolledinthiscoursefrom courses.modelsimportCourseContentEnrollmentMaterialcourse=Course.objects.get(id=course_id)#VerifystudentisenrolledinthiscourseifnotEnrollment.objects.filter(user_id=student_idcourse=course).exists():logger.warning(f"Student{student_id}isnotenrolledincourse{course_id}")return{"error":"Youarenotenrolledinthiscourse.Pleaseselectacourseyouareenrolledin."}#Getcoursematerialsmaterials=Material.objects.filter(course=course)ifmaterials.exists():course_materials=[{"title":material.title"description":material.description"url":material.file.urlifmaterial.fileelseNone}formaterialinmaterials[:5]]#GetcourseAIcontentifavailableifcourse.has_ai_content:try:#Trytogetsamplequizzesquizzes=CourseContent.objects.filter(course=coursecontent_type="sample_quizzes").first()ifquizzes:course_quizzes=quizzes.content#Trytogetweeklyschedulefortopicsschedule=CourseContent.objects.filter(course=coursecontent_type="weekly_schedule").first()ifschedule:#Extracttopicsfromweeklyscheduleforweekinschedule.content:if"topics"inweek:course_topics.extend(week["topics"])exceptExceptionase:logger.warning(f"ErrorgettingAIcontent:{str(e)}")#Buildcourseinfostringcourse_info=f"""Course:{course.title}Description:{course.description}Level:{course.get_required_level_display()}"""#Addskillsdevelopedifavailableskills_developed=list(course.skills_developed.all().values_list("name"flat=True))ifskills_developed:course_info+=f"""SkillsDeveloped:{''.join(skills_developed)}"""exceptCourse.DoesNotExist:logger.warning(f"CoursewithID{course_id}notfound")#Usestandardizedprompttemplateweak_areas_text="".join([f"{skill.name}(Strength:{skill.strength:.2f})"forskillinweak_skills[:5]])#Formatpromptusingtemplateprompt=format_prompt(PromptType.STUDY_PLANstudent_level=student_level.levelifstudent_levelelse"Unknown"weak_areas=weak_areas_textcourse_info=course_infoavailable_time=f"Materials:{len(course_materials)}Topics:{len(course_topics)}Quizzes:{len(course_quizzes)ifisinstance(course_quizzeslist)else0}"learning_preferences="Standardacademicapproach")#GetstandardizedJSONstructurestructure=get_json_structure("study_plan")#Addpracticequizzesandcoursematerialstostructurestructure.update({"practice_quizzes":[{"question":"string""options":["string"]"correct_answer":"string""explanation":"string"}]"course_materials":[{"title":"string""description":"string""priority":"string"}]})#Generatestudyplanusingstandardizedserviceresponse=self.generate_structured_content(promptstructure)#Parsetheresponseusingstandardizedparserifisinstance(responsestr):study_plan=standardized_json_parser(responsestructure)else:study_plan=response#Checkforparsingerrorsif"error"instudy_plan:self.logger.error(f"Failedtoparsestudyplan:{study_plan.get('error')}")returnstudy_plan#Createstudyrecommendationsfromtheplanself._create_recommendations_from_plan(student_idstudy_plan)returnstudy_planexceptExceptionase:logger.error(f"Errorgeneratingstudyplan:{str(e)}")return{"error":f"Errorgeneratingstudyplan:{str(e)}"}def_identify_weak_skills(selfstudent_id:int)->List[Dict[strAny]]:"""Identifyastudent'sweakskillsbasedonassessmentperformanceArgs:student_id:TheIDofthestudentReturns:Alistofdictionariescontainingskillinformationandstrength"""#Getstudent'sskillprogressfrom core.modelsimportStudentSkillProgressskill_progress=StudentSkillProgress.objects.filter(student_id=student_id).select_related("skill")#Calculatestrengthforeachskillweak_skills=[]forprogressinskill_progress:#Convertstrengthtoa0-1scaleifit'snotalreadystrength=(progress.strengthif0<=progress.strength<=1elseprogress.strength/100)weak_skills.append({"id":progress.skill.id"name":progress.skill.name"strength":strength"last_practiced":progress.last_practiced_at})#Sortbystrength(ascending)weak_skills.sort(key=lambdax:x["strength"])returnweak_skillsdef_create_recommendations_from_plan(selfstudent_id:intstudy_plan:Dict[strAny])->None:"""CreatestudyrecommendationsfromageneratedstudyplanArgs:student_id:TheIDofthestudentstudy_plan:Thegeneratedstudyplan"""try:#Getfocusareasfromtheplanfocus_areas=study_plan.get("focus_areas"[])#Createrecommendationsforeachfocusareaforareainfocus_areas:skill_name=area.get("skill""")#Trytofindtheskillskills=Skill.objects.filter(name__icontains=skill_name)#Createtherecommendationrecommendation=StudyRecommendation.objects.create(student_id=student_idtitle=f"Focuson{skill_name}"description=f"Improveyour{skill_name}skillsbystudyingthefollowingtopics:"+"".join(area.get("topics"[]))priority=3#Highpriority)#Addtopicstotherecommendationfortopic_nameinarea.get("topics"[]):#Trytofindanexistingtopicorcreateanewonetopic_=StudyTopic.objects.get_or_create(title=topic_namedefaults={"description":f"Topicrelatedto{skill_name}""skill":skills.first()ifskills.exists()elseNone})#Addthetopictotherecommendationrecommendation.topics.add(topic)#Createrecommendationsforreviewtopicsreview_topics=study_plan.get("review_topics"[])fortopicinreview_topics:topic_name=topic.get("name""")priority_text=topic.get("priority""Medium")#Mapprioritytexttonumericvaluepriority_map={"Low":1"Medium":2"High":3}priority=priority_map.get(priority_text2)#Createtherecommendationrecommendation=StudyRecommendation.objects.create(student_id=student_idtitle=f"Review{topic_name}"description=f"Review{topic_name}usingthefollowingresources:"+"".join(topic.get("resources"[]))priority=priority)#Trytofindanexistingtopicorcreateanewonestudy_topic_=StudyTopic.objects.get_or_create(title=topic_namedefaults={"description":f"Reviewtopic:{topic_name}"})#Addthetopictotherecommendationrecommendation.topics.add(study_topic)exceptExceptionase:logger.error(f"Errorcreatingrecommendationsfromplan:{str(e)}")classSpacedRepetitionService:"""Serviceforspacedrepetitionschedulingandreview"""defget_due_items(selfstudent_id:intlimit:int=20)->List[Dict[strAny]]:"""GetitemsdueforreviewtodayArgs:student_id:TheIDofthestudentlimit:MaximumnumberofitemstoreturnReturns:Alistofdictionariescontainingdueitems"""try:#Getitemsdueforreviewtoday=timezone.now().date()due_items=(SpacedRepetitionItem.objects.filter(student_id=student_idnext_review_date__lte=today).select_related("material""material__topic").order_by("next_review_date")[:limit])#Formattheitemsformatted_items=[]foritemindue_items:formatted_items.append({"id":item.id"material_id":item.material.id"title":item.material.title"content":item.material.content"material_type":item.material.material_type"topic":item.material.topic.title"ease_factor":item.ease_factor"interval":item.interval"repetitions":item.repetitions"next_review_date":item.next_review_date.strftime("%Y-%m-%d")"last_review_date":(item.last_review_date.strftime("%Y-%m-%d")ifitem.last_review_dateelseNone)"last_performance":item.last_performance})returnformatted_itemsexceptExceptionase:logger.error(f"Errorgettingdueitems:{str(e)}")return[]defupdate_item_review(selfitem_id:intperformance_rating:int)->Dict[strAny]:"""UpdateaspacedrepetitionitemafterreviewArgs:item_id:TheIDoftheitemperformance_rating:Theperformancerating(0-5)Returns:Adictionarycontainingtheupdatediteminformation"""try:#Gettheitemitem=SpacedRepetitionItem.objects.get(id=item_id)#Updatethereviewscheduleitem.update_review_schedule(performance_rating)#Returntheupdateditemreturn{"id":item.id"material_id":item.material.id"title":item.material.title"ease_factor":item.ease_factor"interval":item.interval"repetitions":item.repetitions"next_review_date":item.next_review_date.strftime("%Y-%m-%d")"last_review_date":(item.last_review_date.strftime("%Y-%m-%d")ifitem.last_review_dateelseNone)"last_performance":item.last_performance}exceptSpacedRepetitionItem.DoesNotExist:logger.error(f"ItemwithID{item_id}notfound")return{"error":f"ItemwithID{item_id}notfound"}exceptExceptionase:logger.error(f"Errorupdatingitemreview:{str(e)}")return{"error":f"Errorupdatingitemreview:{str(e)}"}defcreate_flashcard(selfstudent_id:inttopic_id:intfront:strback:str)->Dict[strAny]:"""CreateanewflashcardforspacedrepetitionArgs:student_id:TheIDofthestudenttopic_id:TheIDofthetopicfront:Thefrontsideoftheflashcardback:ThebacksideoftheflashcardReturns:Adictionarycontainingthecreatedflashcardinformation"""try:#Getthetopictopic=StudyTopic.objects.get(id=topic_id)#Createthematerialmaterial=StudyMaterial.objects.create(topic=topictitle=front[:50]+"..."iflen(front)>50elsefrontcontent=json.dumps({"front":front"back":back})material_type="FLASHCARD")#Createthespacedrepetitionitemitem=SpacedRepetitionItem.objects.create(student_id=student_idmaterial=materialnext_review_date=timezone.now().date()#Duetoday)#Returnthecreateditemreturn{"id":item.id"material_id":material.id"title":material.title"front":front"back":back"topic":topic.title"next_review_date":item.next_review_date.strftime("%Y-%m-%d")}exceptStudyTopic.DoesNotExist:logger.error(f"TopicwithID{topic_id}notfound")return{"error":f"TopicwithID{topic_id}notfound"}exceptExceptionase:logger.error(f"Errorcreatingflashcard:{str(e)}")return{"error":f"Errorcreatingflashcard:{str(e)}"}defgenerate_flashcards(selfstudent_id:inttopic_id:intcount:int=5)->List[Dict[strAny]]:"""GenerateflashcardsusingAIforaspecifictopicArgs:student_id:TheIDofthestudenttopic_id:TheIDofthetopiccount:NumberofflashcardstogenerateReturns:Alistofdictionariescontainingthegeneratedflashcards"""try:#Getthetopictopic=StudyTopic.objects.get(id=topic_id)#GettheAIservicefromtheunifiedAIservice#Usethealreadyimportedai_service#BuildpromptforAIprompt=f"""Generate{count}flashcardsforstudyingthetopic:{topic.title}Topicdescription:{topic.description}Eachflashcardshouldhaveafrontside(question/concept)andabackside(answer/explanation).Maketheflashcardsconciseclearandfocusedonkeyconcepts.FormattheresponseasJSONwiththefollowingstructure:[{{"front":"Questionorconcept""back":"Answerorexplanation"}}]Generateexactly{count}flashcards."""#GenerateflashcardsusingAIresponse=self.ai_service.generate_structured_content(prompt{"flashcards":[]})#Parsetheresponsetry:#Cleanuptheresponsetohandlepotentialformattingissuesif"```json"inresponse:response=response.split("```json")[1].split("```")[0].strip()elif"```"inresponse:response=response.split("```")[1].split("```")[0].strip()flashcards_data=json.loads(response)#Createflashcardscreated_flashcards=[]forfc_datainflashcards_data:front=fc_data.get("front""")back=fc_data.get("back""")iffrontandback:#Createtheflashcardflashcard=self.create_flashcard(student_idtopic_idfrontback)created_flashcards.append(flashcard)returncreated_flashcardsexceptjson.JSONDecodeErrorase:logger.error(f"Failedtoparseflashcards:{str(e)}")return[{"error":"Failedtoparseflashcards""raw_response":response}]exceptStudyTopic.DoesNotExist:logger.error(f"TopicwithID{topic_id}notfound")return[{"error":f"TopicwithID{topic_id}notfound"}]exceptExceptionase:logger.error(f"Errorgeneratingflashcards:{str(e)}")return[{"error":f"Errorgeneratingflashcards:{str(e)}"}]classPracticeQuestionService:"""Serviceforgeneratingandmanagingpracticequestions-modernizedtouseunifiedAIservice"""def__init__(self):"""Initializethepracticequestionservice"""self.service_name="practice_questions"self.logger=get_ai_logger("practice_questions")self.ai_service=get_ai_service()defgenerate_practice_questions(selfstudent_id:inttopic_id:Optional[int]=Noneskill_id:Optional[int]=Nonecount:int=5question_types:Optional[List[str]]=None)->List[Dict[strAny]]:"""GeneratepracticequestionsforastudentArgs:student_id:TheIDofthestudenttopic_id:OptionaltopicIDtofocusonskill_id:OptionalskillIDtofocusoncount:Numberofquestionstogeneratequestion_types:OptionallistofquestiontypestogenerateReturns:Alistofdictionariescontainingthegeneratedquestions"""try:#Setdefaultquestiontypesifnotprovidedifnotquestion_types:question_types=["MULTIPLE_CHOICE""TRUE_FALSE""SHORT_ANSWER""FILL_BLANK"]#Gettopicorskillinformationtopic_info=""iftopic_id:try:topic=StudyTopic.objects.get(id=topic_id)topic_info=f"""Topic:{topic.title}Description:{topic.description}"""exceptStudyTopic.DoesNotExist:logger.warning(f"TopicwithID{topic_id}notfound")skill_info=""ifskill_id:try:skill=Skill.objects.get(id=skill_id)skill_info=f"""Skill:{skill.name}Description:{skill.description}"""exceptSkill.DoesNotExist:logger.warning(f"SkillwithID{skill_id}notfound")#Getstudent'sweakareasifnospecifictopicorskillisprovidedweak_areas_info=""ifnottopic_idandnotskill_id:weak_skills=self._identify_weak_skills(student_id)ifweak_skills:weak_areas_info="Student'sweakareas:\n"forskillinweak_skills[:3]:weak_areas_info+=f"-{skill['name']}\n"#BuildpromptforAIprompt=f"""Generate{count}practicequestionsforastudent.{topic_info}{skill_info}{weak_areas_info}Questiontypestoinclude:{''.join(question_types)}Eachquestionshouldbechallengingbutappropriateforthestudent'slevel.Includeamixofquestiontypesasspecified.FormattheresponseasJSONwiththefollowingstructure:[{{"question_text":"Questiontext""options":["Option1""Option2""Option3""Option4"]//Formultiplechoice"correct_answer":"Correctanswer""explanation":"Explanationoftheanswer""difficulty_level":2//1=Beginner2=Intermediate3=Advanced4=Expert"question_type":"MULTIPLE_CHOICE"//Oneof:MULTIPLE_CHOICETRUE_FALSESHORT_ANSWERFILL_BLANK}}]Generateexactly{count}questions."""#GeneratequestionsusingstandardizedAIserviceresponse=self.generate_structured_content(prompt[])#Parsetheresponseusingstandardizedparserifisinstance(responsestr):questions_data=standardized_json_parser(response[])else:questions_data=response#Checkforparsingerrorsifisinstance(questions_datadict)and"error"inquestions_data:self.logger.error(f"Failedtoparsequestions:{questions_data.get('error')}")return[questions_data]#Createquestionscreated_questions=[]forq_datainquestions_data:#Createthequestionquestion=PracticeQuestion(student_id=student_idtopic_id=topic_idiftopic_idelseNonequestion_text=q_data.get("question_text""")options=q_data.get("options"[])correct_answer=q_data.get("correct_answer""")explanation=q_data.get("explanation""")difficulty_level=q_data.get("difficulty_level"2)question_type=q_data.get("question_type""MULTIPLE_CHOICE"))#Ifnotopicisprovidedtrytofindorcreateoneifnottopic_id:#Trytofindatopicrelatedtothequestionifskill_id:#Usetheskilltofindatopictopic_=StudyTopic.objects.get_or_create(title=f"Practicefor{q_data.get('question_text''')[:30]}..."defaults={"description":q_data.get("explanation""")"skill_id":skill_id})question.topic=topic#Savethequestionquestion.save()#Formatthequestionforresponsecreated_questions.append({"id":question.id"question_text":question.question_text"options":question.options"correct_answer":question.correct_answer"explanation":question.explanation"difficulty_level":question.difficulty_level"question_type":question.question_type"topic":question.topic.titleifquestion.topicelseNone})returncreated_questionsexceptExceptionase:logger.error(f"Errorgeneratingpracticequestions:{str(e)}")return[{"error":f"Errorgeneratingpracticequestions:{str(e)}"}]def_identify_weak_skills(selfstudent_id:int)->List[Dict[strAny]]:"""Identifyastudent'sweakskillsbasedonassessmentperformanceArgs:student_id:TheIDofthestudentReturns:Alistofdictionariescontainingskillinformation"""#Getstudent'sskillprogressfrom core.modelsimportStudentSkillProgressskill_progress=(StudentSkillProgress.objects.filter(student_id=student_id).select_related("skill").order_by("strength")[:5])#Formattheskillsweak_skills=[]forprogressinskill_progress:weak_skills.append({"id":progress.skill.id"name":progress.skill.name"strength":progress.strength})returnweak_skillsdefcheck_answer(selfquestion_id:intstudent_answer:str)->Dict[strAny]:"""Checkastudent'sanswertoapracticequestionArgs:question_id:TheIDofthequestionstudent_answer:Thestudent'sanswerReturns:Adictionarycontainingtheresult"""try:#Getthequestionquestion=PracticeQuestion.objects.get(id=question_id)#Updatequestionstatsquestion.times_answered+=1question.last_answered=timezone.now()#Checktheanswercorrect=False#Differentcheckinglogicbasedonquestiontypeifquestion.question_type=="MULTIPLE_CHOICE":#Formultiplechoicecheckiftheanswermatchesexactlycorrect=student_answer.strip()==question.correct_answer.strip()elifquestion.question_type=="TRUE_FALSE":#Fortrue/falsecheckiftheanswermatches(caseinsensitive)correct=(student_answer.lower().strip()==question.correct_answer.lower().strip())elifquestion.question_type=="FILL_BLANK":#Forfillintheblankcheckiftheanswercontainsthecorrectanswercorrect=(question.correct_answer.lower().strip()instudent_answer.lower().strip())else:#ForshortansweruseAItoevaluatecorrect=self._evaluate_short_answer(questionstudent_answer)#Updatequestionstatsifcorrect:question.times_correct+=1question.save()#Returntheresultreturn{"question_id":question.id"correct":correct"correct_answer":question.correct_answer"explanation":question.explanation"success_rate":question.calculate_success_rate()}exceptPracticeQuestion.DoesNotExist:logger.error(f"QuestionwithID{question_id}notfound")return{"error":f"QuestionwithID{question_id}notfound"}exceptExceptionase:logger.error(f"Errorcheckinganswer:{str(e)}")return{"error":f"Errorcheckinganswer:{str(e)}"}def_evaluate_short_answer(selfquestion:PracticeQuestionstudent_answer:str)->bool:"""EvaluateashortanswerresponseusingAIArgs:question:Thequestionobjectstudent_answer:Thestudent'sanswerReturns:TrueiftheansweriscorrectFalseotherwise"""try:#BuildpromptforAIprompt=f"""Evaluateifthisstudentansweriscorrectforthegivenquestion.Question:{question.question_text}Correctanswer:{question.correct_answer}Studentanswer:{student_answer}Respondwithonly"CORRECT"or"INCORRECT"."""#GetAIevaluationusingstandardizedserviceresponse=self.generate_content(prompt).strip().upper()#Checktheresponsereturn"CORRECT"inresponseexceptExceptionase:logger.error(f"Errorevaluatingshortanswer:{str(e)}")returnFalse#Createsingletoninstancesstudy_assistant_service=StudyAssistantService()spaced_repetition_service=SpacedRepetitionService()practice_question_service=PracticeQuestionService()
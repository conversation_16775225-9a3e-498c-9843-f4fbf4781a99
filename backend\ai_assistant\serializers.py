from rest_framework import serializers
from .models import AIAssistantInteraction, AIAssistantSession, AIAssistantSuggestion


class AIAssistantSuggestionSerializer(serializers.ModelSerializer):
    """Serializer for AI assistant suggestions"""
    
    class Meta:
        model = AIAssistantSuggestion
        fields = [
            "id",
            "text",
            "icon",
            "category",
            "priority",
            "target_user_type"
        ]


class AIAssistantInteractionSerializer(serializers.ModelSerializer):
    """Serializer for AI assistant interactions"""
    
    class Meta:
        model = AIAssistantInteraction
        fields = [
            "id",
            "question",
            "answer",
            "confidence_score",
            "response_time",
            "feedback_rating",
            "created_at",
            "metadata"
        ]
        read_only_fields = ["id", "created_at"]


class AIAssistantSessionSerializer(serializers.ModelSerializer):
    """Serializer for AI assistant sessions"""
    
    interactions = AIAssistantInteractionSerializer(many=True, read_only=True)
    
    class Meta:
        model = AIAssistantSession
        fields = [
            "id",
            "session_id",
            "started_at",
            "ended_at",
            "total_interactions",
            "context",
            "interactions"
        ]
        read_only_fields = ["id", "started_at"]


class AIQuestionSerializer(serializers.Serializer):
    """Serializer for AI question requests"""
    
    question = serializers.CharField(max_length=2000)
    context = serializers.JSONField(required=False, default=dict)
    session_id = serializers.CharField(max_length=100, required=False)


class AIAnswerSerializer(serializers.Serializer):
    """Serializer for AI answer responses"""
    
    answer = serializers.CharField()
    confidence_score = serializers.FloatField()
    response_time = serializers.FloatField()
    session_id = serializers.CharField()
    suggestions = serializers.ListField(
        child=serializers.CharField(),
        required=False
    )

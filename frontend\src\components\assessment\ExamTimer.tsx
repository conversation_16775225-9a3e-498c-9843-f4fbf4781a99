/**
 * ExamTimer Component
 * 
 * Displays a countdown timer for exams with grace period handling and auto-submit
 */

import React, { useMemo } from 'react';
import { Box, Typography, LinearProgress, Alert, Chip } from '@mui/material';
import { AccessTime, Warning, Error } from '@mui/icons-material';
import { styled } from '@mui/material/styles';

const TimerContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.background.paper,
  boxShadow: theme.shadows[2],
  minWidth: 300,
}));

const TimeDisplay = styled(Typography)(({ theme }) => ({
  fontFamily: 'monospace',
  fontSize: '2rem',
  fontWeight: 'bold',
  marginBottom: theme.spacing(1),
}));

const ProgressContainer = styled(Box)(({ theme }) => ({
  width: '100%',
  marginTop: theme.spacing(1),
  marginBottom: theme.spacing(1),
}));

interface ExamTimerProps {
  timeRemainingSeconds: number;
  totalDurationSeconds: number;
  isInGracePeriod: boolean;
  gracePeriodRemaining: number;
  shouldAutoSubmit: boolean;
  onTimeUp?: () => void;
  onGracePeriodEnd?: () => void;
}

export const ExamTimer: React.FC<ExamTimerProps> = ({
  timeRemainingSeconds,
  totalDurationSeconds,
  isInGracePeriod,
  gracePeriodRemaining,
  shouldAutoSubmit,
  onTimeUp,
  onGracePeriodEnd
}) => {
  
  // Format time display
  const formatTime = (seconds: number): string => {
    if (seconds < 0) return '00:00:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Calculate progress percentage
  const progressPercentage = useMemo(() => {
    if (totalDurationSeconds <= 0) return 0;
    const elapsed = totalDurationSeconds - timeRemainingSeconds;
    return Math.min(100, Math.max(0, (elapsed / totalDurationSeconds) * 100));
  }, [timeRemainingSeconds, totalDurationSeconds]);
  
  // Determine timer state and colors
  const timerState = useMemo(() => {
    if (shouldAutoSubmit) {
      return {
        color: 'error' as const,
        severity: 'error' as const,
        message: 'Exam time has ended - Auto-submitting',
        icon: <Error />
      };
    } else if (isInGracePeriod) {
      return {
        color: 'warning' as const,
        severity: 'warning' as const,
        message: `Grace period: ${formatTime(gracePeriodRemaining)} remaining`,
        icon: <Warning />
      };
    } else if (timeRemainingSeconds <= 300) { // 5 minutes
      return {
        color: 'warning' as const,
        severity: 'warning' as const,
        message: 'Less than 5 minutes remaining',
        icon: <Warning />
      };
    } else if (timeRemainingSeconds <= 900) { // 15 minutes
      return {
        color: 'info' as const,
        severity: 'info' as const,
        message: 'Please plan to finish soon',
        icon: <AccessTime />
      };
    } else {
      return {
        color: 'success' as const,
        severity: 'success' as const,
        message: 'Time remaining',
        icon: <AccessTime />
      };
    }
  }, [timeRemainingSeconds, isInGracePeriod, gracePeriodRemaining, shouldAutoSubmit]);
  
  // Time display value
  const displayTime = isInGracePeriod ? gracePeriodRemaining : timeRemainingSeconds;
  const displayLabel = isInGracePeriod ? 'Grace Period' : 'Time Remaining';
  
  return (
    <TimerContainer>
      <Box display="flex" alignItems="center" gap={1} mb={1}>
        {timerState.icon}
        <Typography variant="h6" color={timerState.color}>
          {displayLabel}
        </Typography>
      </Box>
      
      <TimeDisplay color={timerState.color}>
        {formatTime(displayTime)}
      </TimeDisplay>
      
      <ProgressContainer>
        <LinearProgress
          variant="determinate"
          value={progressPercentage}
          color={timerState.color}
          sx={{ height: 8, borderRadius: 4 }}
        />
        <Box display="flex" justifyContent="space-between" mt={0.5}>
          <Typography variant="caption" color="text.secondary">
            {Math.round(progressPercentage)}% Complete
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {formatTime(totalDurationSeconds)}
          </Typography>
        </Box>
      </ProgressContainer>
      
      {(timerState.severity === 'warning' || timerState.severity === 'error') && (
        <Alert 
          severity={timerState.severity} 
          size="small" 
          sx={{ width: '100%', mt: 1 }}
        >
          {timerState.message}
        </Alert>
      )}
      
      {shouldAutoSubmit && (
        <Chip
          label="AUTO-SUBMITTING"
          color="error"
          variant="filled"
          size="small"
          sx={{ mt: 1, animation: 'pulse 2s infinite' }}
        />
      )}
    </TimerContainer>
  );
};

export default ExamTimer;

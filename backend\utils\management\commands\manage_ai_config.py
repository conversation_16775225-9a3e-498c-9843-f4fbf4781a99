fromdjango.core.management.baseimportBaseCommandfromdjango.core.cacheimportcacheclassCommand(BaseCommand):help='ManageAIconfigurationandservicesynchronization'defadd_arguments(selfparser):parser.add_argument('--refresh'action='store_true'help='ForcerefreshallAIservices')parser.add_argument('--status'action='store_true'help='ShowAIconfigurationstatus')parser.add_argument('--clear-cache'action='store_true'help='ClearAIconfigurationcache')parser.add_argument('--test-config'action='store_true'help='TestAIconfigurationconsistency')defhandle(self*args**options):ifoptions['status']:self.show_status()ifoptions['clear_cache']:self.clear_cache()ifoptions['refresh']:self.force_refresh()ifoptions['test_config']:self.test_configuration()defshow_status(self):"""ShowcurrentAIconfigurationstatus."""self.stdout.write(self.style.SUCCESS('\n===AIConfigurationStatus==='))try:fromutils.ai_config_managerimportai_config_managerget_ai_configfromutils.api_key_serviceimportApiKeyService#Getservicestatusstatus=ai_config_manager.get_service_status()self.stdout.write(f"Totalregisteredservices:{status['total_services']}")self.stdout.write(f"Registeredservices:{''.join(status['registered_services'])}")self.stdout.write(f"Configurationversion:{status['configuration_version']}")self.stdout.write(f"Lastupdated:{status['last_updated']}")#Getunifiedconfigconfig=get_ai_config()self.stdout.write(f"\nAPIKeyconfigured:{config.get('api_key_configured'False)}")self.stdout.write(f"APIKeypreview:{config.get('api_key_masked''Notconfigured')}")self.stdout.write(f"Defaultmodel:{config.get('default_model''Notset')}")self.stdout.write(f"Temperature:{config.get('temperature''Notset')}")self.stdout.write(f"Maxtokens:{config.get('max_tokens''Notset')}")self.stdout.write(f"Serviceavailable:{config.get('service_available'False)}")#TestactualAPIkeyvalidityapi_key=ApiKeyService.get_api_key()ifapi_key:is_valid=ApiKeyService.is_actually_valid(api_key)self.stdout.write(f"APIKeyactuallyvalid:{is_valid}")exceptExceptionase:self.stdout.write(self.style.ERROR(f"Errorgettingstatus:{e}"))defclear_cache(self):"""ClearAIconfigurationcache."""self.stdout.write(self.style.WARNING('ClearingAIconfigurationcache...'))try:cache_keys=['unified_ai_config''ai_config_version''frontend_ai_config''unified_ai_config_timestamp''GEMINI_API_KEY''API_KEY_STATUS''API_KEY_ERROR_DETAILS']forkeyincache_keys:cache.delete(key)self.stdout.write(self.style.SUCCESS('Cacheclearedsuccessfully'))exceptExceptionase:self.stdout.write(self.style.ERROR(f"Errorclearingcache:{e}"))defforce_refresh(self):"""ForcerefreshallAIservices."""self.stdout.write(self.style.WARNING('ForcerefreshingallAIservices...'))try:fromutils.ai_config_managerimportforce_refresh_ai_servicesforce_refresh_ai_services()self.stdout.write(self.style.SUCCESS('AllAIservicesrefreshedsuccessfully'))exceptExceptionase:self.stdout.write(self.style.ERROR(f"Errorduringrefresh:{e}"))deftest_configuration(self):"""TestAIconfigurationconsistencyacrossservices."""self.stdout.write(self.style.SUCCESS('\n===TestingAIConfigurationConsistency==='))try:fromutils.api_key_serviceimportApiKeyServicefromutils.ai_config_managerimportget_ai_config#TestAPIkeyserviceapi_key=ApiKeyService.get_api_key()self.stdout.write(f"APIKeyfromservice:{'[PRESENT]'ifapi_keyelse'[MISSING]'}")ifapi_key:is_valid=ApiKeyService.test_api_key(api_key)self.stdout.write(f"APIKeyvalidationtest:{'PASS'ifis_validelse'FAIL'}")#Testunifiedconfigconfig=get_ai_config()self.stdout.write(f"Unifiedconfigavailable:{'YES'ifconfigelse'NO'}")#Testindividualservicesservices_to_test=[('ConsolidatedAIService''utils.ai.services''get_ai_service')('ChatService''chatbot.services.chat_service''GeminiChatService')('SpeechService''interactive_learning.services.speech_service''ArabicSpeechService')]forservice_namemodule_pathclass_nameinservices_to_test:try:module=__import__(module_pathfromlist=[class_name])ifclass_name=='get_ai_service':service_instance=getattr(moduleclass_name)()else:service_class=getattr(moduleclass_name)service_instance=service_class()self.stdout.write(f"{service_name}:AVAILABLE")exceptExceptionase:self.stdout.write(self.style.ERROR(f"{service_name}:ERROR-{e}"))self.stdout.write(self.style.SUCCESS('\nConfigurationtestcompleted'))exceptExceptionase:self.stdout.write(self.style.ERROR(f"Errorduringconfigurationtest:{e}"))
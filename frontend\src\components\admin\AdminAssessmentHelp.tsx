import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import { MdExpandMore as ExpandMoreIcon } from 'react-icons/md';

interface AdminAssessmentHelpProps {
  open: boolean;
  onClose: () => void;
}

const AdminAssessmentHelp: React.FC<AdminAssessmentHelpProps> = ({ open, onClose }) => {
  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Assessment Questions Help</DialogTitle>
      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Question Types</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                <strong>Multiple Choice:</strong> Students select one correct answer from multiple options.
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>True/False:</strong> Students choose between true or false options.
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Short Answer:</strong> Students provide a brief text response.
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Essay:</strong> Students provide a detailed written response.
              </Typography>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Categories</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                <strong>Programming:</strong> Code-related questions and programming concepts.
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Cybersecurity:</strong> Security, encryption, and network protection topics.
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Finance:</strong> Financial concepts, accounting, and business topics.
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Marketing:</strong> Marketing strategies, digital marketing, and promotion.
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>General:</strong> General knowledge and miscellaneous topics.
              </Typography>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Difficulty Levels</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                <strong>Level 1 - Beginner:</strong> Basic concepts and fundamental knowledge.
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Level 2 - Elementary:</strong> Slightly more complex topics with some application.
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Level 3 - Intermediate:</strong> Moderate complexity requiring analysis.
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Level 4 - Advanced:</strong> Complex topics requiring synthesis and evaluation.
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Level 5 - Expert:</strong> Highly complex scenarios requiring expert knowledge.
              </Typography>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Placement Questions</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                Placement questions are used in placement assessments to determine a student's initial skill level.
                These questions should cover a range of difficulty levels and core concepts.
              </Typography>
              <Typography variant="body2" paragraph>
                When creating placement questions, ensure they are:
              </Typography>
              <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
                <li>Representative of key concepts in the subject area</li>
                <li>Varied in difficulty to properly assess different skill levels</li>
                <li>Clear and unambiguous in their wording</li>
                <li>Free from cultural bias or assumptions</li>
              </Typography>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">AI Generation Tips</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                When using AI to generate questions:
              </Typography>
              <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
                <li>Be specific about the topic and learning objectives</li>
                <li>Specify the desired difficulty level clearly</li>
                <li>Review and edit generated questions for accuracy</li>
                <li>Ensure questions align with your curriculum</li>
                <li>Test questions with students before using in assessments</li>
              </Typography>
            </AccordionDetails>
          </Accordion>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AdminAssessmentHelp;

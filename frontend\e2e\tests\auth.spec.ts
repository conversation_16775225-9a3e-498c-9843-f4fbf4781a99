import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should display login page correctly', async ({ page }) => {
    await expect(page).toHaveTitle(/North Star University/);
    await expect(page.locator('h1')).toContainText('Sign In');
    await expect(page.locator('input[name="username"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });

  test('should show validation errors for empty fields', async ({ page }) => {
    await page.click('button[type="submit"]');
    
    await expect(page.locator('text=Username is required')).toBeVisible();
    await expect(page.locator('text=Password is required')).toBeVisible();
  });

  test('should show error for invalid credentials', async ({ page }) => {
    await page.fill('input[name="username"]', 'invaliduser');
    await page.fill('input[name="password"]', 'wrongpassword');
    await page.click('button[type="submit"]');
    
    await expect(page.locator('text=Invalid credentials')).toBeVisible();
  });

  test('should login successfully with valid credentials', async ({ page }) => {
    const username = process.env.TEST_STUDENT_USERNAME || 'testuser';
    const password = process.env.TEST_STUDENT_PASSWORD || 'testpass123';

    await page.fill('input[name="username"]', username);
    await page.fill('input[name="password"]', password);
    await page.click('button[type="submit"]');
    
    // Should redirect to dashboard
    await expect(page).toHaveURL(/\/dashboard/);
    await expect(page.locator('text=Welcome')).toBeVisible();
  });

  test('should navigate to registration page', async ({ page }) => {
    await page.click('text=Sign up');
    
    await expect(page).toHaveURL(/\/register/);
    await expect(page.locator('h1')).toContainText('Sign Up');
  });

  test('should register new user successfully', async ({ page }) => {
    await page.goto('/register');
    
    const timestamp = Date.now();
    const userData = {
      username: `testuser${timestamp}`,
      email: `test${timestamp}@example.com`,
      password: 'SecurePass123!',
      firstName: 'Test',
      lastName: 'User',
    };

    await page.fill('input[name="username"]', userData.username);
    await page.fill('input[name="email"]', userData.email);
    await page.fill('input[name="password"]', userData.password);
    await page.fill('input[name="passwordConfirm"]', userData.password);
    await page.fill('input[name="firstName"]', userData.firstName);
    await page.fill('input[name="lastName"]', userData.lastName);
    await page.selectOption('select[name="role"]', 'STUDENT');
    
    await page.click('button[type="submit"]');
    
    // Should redirect to dashboard after successful registration
    await expect(page).toHaveURL(/\/dashboard/);
    await expect(page.locator('text=Welcome')).toBeVisible();
  });

  test('should logout successfully', async ({ page }) => {
    // Login first
    const username = process.env.TEST_STUDENT_USERNAME || 'testuser';
    const password = process.env.TEST_STUDENT_PASSWORD || 'testpass123';

    await page.fill('input[name="username"]', username);
    await page.fill('input[name="password"]', password);
    await page.click('button[type="submit"]');
    
    await expect(page).toHaveURL(/\/dashboard/);
    
    // Logout
    await page.click('[data-testid="user-menu"]');
    await page.click('text=Logout');
    
    // Should redirect to login page
    await expect(page).toHaveURL(/\/login/);
    await expect(page.locator('h1')).toContainText('Sign In');
  });

  test('should remember user session', async ({ page, context }) => {
    // Login
    const username = process.env.TEST_STUDENT_USERNAME || 'testuser';
    const password = process.env.TEST_STUDENT_PASSWORD || 'testpass123';

    await page.fill('input[name="username"]', username);
    await page.fill('input[name="password"]', password);
    await page.click('button[type="submit"]');
    
    await expect(page).toHaveURL(/\/dashboard/);
    
    // Create new page in same context
    const newPage = await context.newPage();
    await newPage.goto('/');
    
    // Should automatically redirect to dashboard (session remembered)
    await expect(newPage).toHaveURL(/\/dashboard/);
  });

  test('should handle password visibility toggle', async ({ page }) => {
    const passwordInput = page.locator('input[name="password"]');
    const toggleButton = page.locator('[data-testid="password-toggle"]');
    
    // Initially password should be hidden
    await expect(passwordInput).toHaveAttribute('type', 'password');
    
    // Click toggle to show password
    await toggleButton.click();
    await expect(passwordInput).toHaveAttribute('type', 'text');
    
    // Click toggle to hide password again
    await toggleButton.click();
    await expect(passwordInput).toHaveAttribute('type', 'password');
  });

  test('should handle forgot password flow', async ({ page }) => {
    await page.click('text=Forgot password?');
    
    await expect(page).toHaveURL(/\/forgot-password/);
    await expect(page.locator('h1')).toContainText('Reset Password');
    
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.click('button[type="submit"]');
    
    await expect(page.locator('text=Password reset email sent')).toBeVisible();
  });

  test('should validate password strength during registration', async ({ page }) => {
    await page.goto('/register');
    
    // Test weak password
    await page.fill('input[name="password"]', '123');
    await page.blur('input[name="password"]');
    
    await expect(page.locator('text=Password must be at least 8 characters')).toBeVisible();
    
    // Test strong password
    await page.fill('input[name="password"]', 'SecurePass123!');
    await page.blur('input[name="password"]');
    
    await expect(page.locator('[data-testid="password-strength-strong"]')).toBeVisible();
  });

  test('should handle keyboard navigation', async ({ page }) => {
    // Tab through form elements
    await page.keyboard.press('Tab');
    await expect(page.locator('input[name="username"]')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.locator('input[name="password"]')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.locator('button[type="submit"]')).toBeFocused();
    
    // Submit with Enter key
    await page.fill('input[name="username"]', process.env.TEST_STUDENT_USERNAME || 'testuser');
    await page.fill('input[name="password"]', process.env.TEST_STUDENT_PASSWORD || 'testpass123');
    await page.keyboard.press('Enter');
    
    await expect(page).toHaveURL(/\/dashboard/);
  });

  test('should be accessible', async ({ page }) => {
    // Check for proper ARIA labels
    await expect(page.locator('input[name="username"]')).toHaveAttribute('aria-label');
    await expect(page.locator('input[name="password"]')).toHaveAttribute('aria-label');
    
    // Check for proper form structure
    await expect(page.locator('form')).toBeVisible();
    
    // Check for proper heading hierarchy
    await expect(page.locator('h1')).toBeVisible();
    
    // Check color contrast (basic check)
    const submitButton = page.locator('button[type="submit"]');
    const buttonStyles = await submitButton.evaluate((el) => {
      const styles = window.getComputedStyle(el);
      return {
        backgroundColor: styles.backgroundColor,
        color: styles.color,
      };
    });
    
    // Basic contrast check (should not be same color)
    expect(buttonStyles.backgroundColor).not.toBe(buttonStyles.color);
  });

  test('should work on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/');
    
    // Check mobile-specific elements
    await expect(page.locator('input[name="username"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    // Test mobile login
    const username = process.env.TEST_STUDENT_USERNAME || 'testuser';
    const password = process.env.TEST_STUDENT_PASSWORD || 'testpass123';

    await page.fill('input[name="username"]', username);
    await page.fill('input[name="password"]', password);
    await page.click('button[type="submit"]');
    
    await expect(page).toHaveURL(/\/dashboard/);
  });
});

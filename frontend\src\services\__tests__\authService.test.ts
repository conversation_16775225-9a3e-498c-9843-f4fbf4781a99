/**
 * Comprehensive tests for authentication service
 */

import mockAxios from 'jest-mock-axios';
import { authService } from '../auth';
import { API_ENDPOINTS } from '../../config/api';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('AuthService', () => {
  beforeEach(() => {
    mockAxios.reset();
    jest.clearAllMocks();
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
  });

  describe('login', () => {
    it('successfully logs in user', async () => {
      const mockResponse = {
        data: {
          user: {
            id: 1,
            username: 'testuser',
            email: '<EMAIL>',
            role: 'STUDENT',
          },
          access: 'mock-access-token',
          refresh: 'mock-refresh-token',
        },
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      const credentials = {
        username: 'testuser',
        password: 'password123',
      };

      const result = await authService.login(credentials);

      expect(mockAxios.post).toHaveBeenCalledWith(
        API_ENDPOINTS.AUTH.LOGIN,
        credentials
      );
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'access_token',
        'mock-access-token'
      );
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'refresh_token',
        'mock-refresh-token'
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('handles login failure', async () => {
      const mockError = {
        response: {
          status: 401,
          data: {
            detail: 'Invalid credentials',
          },
        },
      };

      mockAxios.post.mockRejectedValue(mockError);

      const credentials = {
        username: 'testuser',
        password: 'wrongpassword',
      };

      await expect(authService.login(credentials)).rejects.toThrow('Invalid credentials');
    });

    it('handles network error during login', async () => {
      const networkError = new Error('Network Error');
      mockAxios.post.mockRejectedValue(networkError);

      const credentials = {
        username: 'testuser',
        password: 'password123',
      };

      await expect(authService.login(credentials)).rejects.toThrow('Network Error');
    });

    it('validates required fields', async () => {
      await expect(authService.login({ username: '', password: 'test' }))
        .rejects.toThrow('Username is required');

      await expect(authService.login({ username: 'test', password: '' }))
        .rejects.toThrow('Password is required');
    });
  });

  describe('register', () => {
    it('successfully registers user', async () => {
      const mockResponse = {
        data: {
          user: {
            id: 1,
            username: 'newuser',
            email: '<EMAIL>',
            role: 'STUDENT',
          },
          access: 'mock-access-token',
          refresh: 'mock-refresh-token',
        },
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      const userData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        password_confirm: 'password123',
        first_name: 'New',
        last_name: 'User',
        role: 'STUDENT',
      };

      const result = await authService.register(userData);

      expect(mockAxios.post).toHaveBeenCalledWith(
        API_ENDPOINTS.AUTH.REGISTER,
        userData
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('handles registration validation errors', async () => {
      const mockError = {
        response: {
          status: 400,
          data: {
            username: ['This username is already taken'],
            email: ['Enter a valid email address'],
          },
        },
      };

      mockAxios.post.mockRejectedValue(mockError);

      const userData = {
        username: 'existinguser',
        email: 'invalid-email',
        password: 'password123',
        password_confirm: 'password123',
      };

      await expect(authService.register(userData)).rejects.toThrow();
    });

    it('validates password confirmation', async () => {
      const userData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        password_confirm: 'different123',
      };

      await expect(authService.register(userData))
        .rejects.toThrow('Passwords do not match');
    });
  });

  describe('logout', () => {
    it('successfully logs out user', async () => {
      localStorageMock.getItem.mockReturnValue('mock-refresh-token');
      mockAxios.post.mockResolvedValue({ data: { success: true } });

      await authService.logout();

      expect(mockAxios.post).toHaveBeenCalledWith(
        API_ENDPOINTS.AUTH.LOGOUT,
        { refresh: 'mock-refresh-token' }
      );
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('access_token');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('refresh_token');
    });

    it('clears tokens even if logout request fails', async () => {
      localStorageMock.getItem.mockReturnValue('mock-refresh-token');
      mockAxios.post.mockRejectedValue(new Error('Server error'));

      await authService.logout();

      expect(localStorageMock.removeItem).toHaveBeenCalledWith('access_token');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('refresh_token');
    });
  });

  describe('refreshToken', () => {
    it('successfully refreshes token', async () => {
      const mockResponse = {
        data: {
          access: 'new-access-token',
        },
      };

      localStorageMock.getItem.mockReturnValue('mock-refresh-token');
      mockAxios.post.mockResolvedValue(mockResponse);

      const result = await authService.refreshToken();

      expect(mockAxios.post).toHaveBeenCalledWith(
        API_ENDPOINTS.AUTH.REFRESH,
        { refresh: 'mock-refresh-token' }
      );
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'access_token',
        'new-access-token'
      );
      expect(result).toBe('new-access-token');
    });

    it('handles refresh token failure', async () => {
      localStorageMock.getItem.mockReturnValue('invalid-refresh-token');
      mockAxios.post.mockRejectedValue({
        response: { status: 401, data: { detail: 'Token is invalid or expired' } }
      });

      await expect(authService.refreshToken()).rejects.toThrow();
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('access_token');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('refresh_token');
    });

    it('throws error when no refresh token available', async () => {
      localStorageMock.getItem.mockReturnValue(null);

      await expect(authService.refreshToken())
        .rejects.toThrow('No refresh token available');
    });
  });

  describe('getCurrentUser', () => {
    it('successfully gets current user', async () => {
      const mockResponse = {
        data: {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          role: 'STUDENT',
          profile: {
            first_name: 'Test',
            last_name: 'User',
          },
        },
      };

      mockAxios.get.mockResolvedValue(mockResponse);

      const result = await authService.getCurrentUser();

      expect(mockAxios.get).toHaveBeenCalledWith(API_ENDPOINTS.AUTH.ME);
      expect(result).toEqual(mockResponse.data);
    });

    it('handles unauthorized access', async () => {
      mockAxios.get.mockRejectedValue({
        response: { status: 401, data: { detail: 'Authentication credentials were not provided' } }
      });

      await expect(authService.getCurrentUser()).rejects.toThrow();
    });
  });

  describe('updateProfile', () => {
    it('successfully updates user profile', async () => {
      const mockResponse = {
        data: {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          first_name: 'Updated',
          last_name: 'Name',
        },
      };

      mockAxios.patch.mockResolvedValue(mockResponse);

      const updateData = {
        email: '<EMAIL>',
        first_name: 'Updated',
        last_name: 'Name',
      };

      const result = await authService.updateProfile(updateData);

      expect(mockAxios.patch).toHaveBeenCalledWith(
        API_ENDPOINTS.AUTH.PROFILE,
        updateData
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('handles profile update validation errors', async () => {
      mockAxios.patch.mockRejectedValue({
        response: {
          status: 400,
          data: {
            email: ['Enter a valid email address'],
          },
        },
      });

      const updateData = {
        email: 'invalid-email',
      };

      await expect(authService.updateProfile(updateData)).rejects.toThrow();
    });
  });

  describe('changePassword', () => {
    it('successfully changes password', async () => {
      mockAxios.post.mockResolvedValue({ data: { success: true } });

      const passwordData = {
        old_password: 'oldpassword123',
        new_password: 'newpassword123',
        new_password_confirm: 'newpassword123',
      };

      await authService.changePassword(passwordData);

      expect(mockAxios.post).toHaveBeenCalledWith(
        API_ENDPOINTS.AUTH.CHANGE_PASSWORD,
        passwordData
      );
    });

    it('validates password confirmation', async () => {
      const passwordData = {
        old_password: 'oldpassword123',
        new_password: 'newpassword123',
        new_password_confirm: 'different123',
      };

      await expect(authService.changePassword(passwordData))
        .rejects.toThrow('New passwords do not match');
    });

    it('handles incorrect old password', async () => {
      mockAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            old_password: ['Incorrect password'],
          },
        },
      });

      const passwordData = {
        old_password: 'wrongpassword',
        new_password: 'newpassword123',
        new_password_confirm: 'newpassword123',
      };

      await expect(authService.changePassword(passwordData)).rejects.toThrow();
    });
  });

  describe('token management', () => {
    it('gets access token from localStorage', () => {
      localStorageMock.getItem.mockReturnValue('mock-access-token');

      const token = authService.getAccessToken();

      expect(localStorageMock.getItem).toHaveBeenCalledWith('access_token');
      expect(token).toBe('mock-access-token');
    });

    it('gets refresh token from localStorage', () => {
      localStorageMock.getItem.mockReturnValue('mock-refresh-token');

      const token = authService.getRefreshToken();

      expect(localStorageMock.getItem).toHaveBeenCalledWith('refresh_token');
      expect(token).toBe('mock-refresh-token');
    });

    it('checks if user is authenticated', () => {
      localStorageMock.getItem.mockReturnValue('mock-access-token');

      const isAuthenticated = authService.isAuthenticated();

      expect(isAuthenticated).toBe(true);
    });

    it('returns false when no access token', () => {
      localStorageMock.getItem.mockReturnValue(null);

      const isAuthenticated = authService.isAuthenticated();

      expect(isAuthenticated).toBe(false);
    });
  });

  describe('error handling', () => {
    it('handles network errors gracefully', async () => {
      const networkError = new Error('Network Error');
      networkError.code = 'NETWORK_ERROR';
      mockAxios.post.mockRejectedValue(networkError);

      await expect(authService.login({ username: 'test', password: 'test' }))
        .rejects.toThrow('Network Error');
    });

    it('handles server errors gracefully', async () => {
      mockAxios.post.mockRejectedValue({
        response: {
          status: 500,
          data: {
            detail: 'Internal server error',
          },
        },
      });

      await expect(authService.login({ username: 'test', password: 'test' }))
        .rejects.toThrow('Internal server error');
    });

    it('handles timeout errors', async () => {
      const timeoutError = new Error('Request timeout');
      timeoutError.code = 'ECONNABORTED';
      mockAxios.post.mockRejectedValue(timeoutError);

      await expect(authService.login({ username: 'test', password: 'test' }))
        .rejects.toThrow('Request timeout');
    });
  });
});

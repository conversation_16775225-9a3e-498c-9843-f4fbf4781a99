from django.db.modelsimportAvgCountFQfrom django.shortcutsimportget_object_or_404from django.utilsimport timezonefromrest_frameworkimportpermissionsstatusviewsviewsetsfromrest_framework.decoratorsimportactionfromrest_framework.responseimportResponsefrom.modelsimport(AssessmentAssessmentResponseCompetencyBadgeLearningAnalyticsLearningPathwayLevelRequirementProgressionMilestoneProgressReportSkillGapStudentBadgeStudentLevelStudentMilestoneStudentProgressTracking)#ImportSkillfromcoreappwhereit'sactuallydefinedfrom core.modelsimportSkillfrom.serializersimport(CompetencyBadgeSerializerLearningAnalyticsSerializerLearningPathwaySerializerLevelRequirementSerializerProgressionMilestoneSerializerProgressReportSerializerSkillGapSerializerSkillSerializerStudentBadgeSerializerStudentLevelSerializerStudentProgressTrackingSerializer)classSkillProgressViewSet(viewsets.ModelViewSet):"""ViewSetforhandlingstudentskillprogressandtracking"""permission_classes=[permissions.IsAuthenticated]defget_serializer_class(self):ifself.action=="skill_gaps":returnSkillGapSerializerelifself.action=="progress_reports":returnProgressReportSerializerelifself.action=="learning_analytics":returnLearningAnalyticsSerializerelse:returnStudentLevelSerializerdefget_queryset(self):"""Returnappropriatequerysetbasedontherequestedaction"""user=self.request.userifself.action=="skill_gaps":returnSkillGap.objects.filter(student=user)elifself.action=="progress_reports":returnProgressReport.objects.filter(student=user)elifself.action=="learning_analytics":returnLearningAnalytics.objects.filter(student=user)else:#DefaulttoStudentLevelreturnStudentLevel.objects.filter(student=user)@action(detail=Falsemethods=["get"])defskill_gaps(selfrequest):"""Getallskillgapsfortheauthenticateduser"""queryset=self.get_queryset()serializer=self.get_serializer(querysetmany=True)returnResponse(serializer.data)@action(detail=Falsemethods=["post"])defresolve_skill_gap(selfrequest):"""Markaskillgapasresolvedwithanewproficiencylevel"""skill_id=request.data.get("skill_id")new_proficiency=request.data.get("new_proficiency")ifnotskill_idornew_proficiencyisNone:returnResponse({"detail":"Bothskill_idandnew_proficiencyarerequired."}status=status.HTTP_400_BAD_REQUEST)try:skill_gap=SkillGap.objects.get(student=request.userskill_id=skill_id)skill_gap.mark_as_resolved(float(new_proficiency))serializer=SkillGapSerializer(skill_gap)returnResponse(serializer.data)exceptSkillGap.DoesNotExist:returnResponse({"detail":"Skillgapnotfound."}status=status.HTTP_404_NOT_FOUND)@action(detail=Falsemethods=["get"])defprogress_reports(selfrequest):"""Getallprogressreportsfortheauthenticateduser"""queryset=self.get_queryset()serializer=self.get_serializer(querysetmany=True)returnResponse(serializer.data)@action(detail=Falsemethods=["post"])defgenerate_report(selfrequest):"""Generateanewprogressreportfortheuser"""report_type=request.data.get("report_type""WEEKLY")custom_start=request.data.get("start_date")custom_end=request.data.get("end_date")#Parsedatesiftheyarestringsifcustom_startandisinstance(custom_startstr):try:custom_start=timezone.datetime.fromisoformat(custom_start)exceptValueError:returnResponse({"detail":"Invalidstart_dateformat."}status=status.HTTP_400_BAD_REQUEST)ifcustom_endandisinstance(custom_endstr):try:custom_end=timezone.datetime.fromisoformat(custom_end)exceptValueError:returnResponse({"detail":"Invalidend_dateformat."}status=status.HTTP_400_BAD_REQUEST)try:report=ProgressReport.generate_report(student=request.userreport_type=report_typecustom_start=custom_startcustom_end=custom_end)serializer=ProgressReportSerializer(report)returnResponse(serializer.data)exceptExceptionase:returnResponse({"detail":f"Errorgeneratingreport:{str(e)}"}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@action(detail=Falsemethods=["get"])deflearning_analytics(selfrequest):"""Getlearninganalyticsfortheauthenticateduser"""queryset=self.get_queryset()#Ifnoanalyticsexistyetcreatethemifnotqueryset.exists():analytics=LearningAnalytics.objects.create(student=request.user)analytics.analyze_learning_patterns()serializer=self.get_serializer(analytics)else:#Getthelatestanalyticsanalytics=queryset.first()#Checkifitneedsupdating(olderthan24hours)iftimezone.now()-analytics.last_updated>timezone.timedelta(hours=24):analytics.analyze_learning_patterns()serializer=self.get_serializer(analytics)returnResponse(serializer.data)@action(detail=Falsemethods=["post"])defupdate_analytics(selfrequest):"""Forceanupdateofthelearninganalytics"""try:analyticscreated=LearningAnalytics.objects.get_or_create(student=request.user)analytics.analyze_learning_patterns()analytics.calculate_learning_pace()serializer=LearningAnalyticsSerializer(analytics)returnResponse(serializer.data)exceptExceptionase:returnResponse({"detail":f"Errorupdatinganalytics:{str(e)}"}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@action(detail=Falsemethods=["get"])defprogression_pathway(selfrequest):"""Getthestudent'sprogressionpathwaywithmilestones"""try:student_level=StudentLevel.objects.get(student=request.user)progression_path=student_level.get_progression_path()ifnotprogression_path:returnResponse({"detail":"Noprogressionpathavailableforthisstudent."}status=status.HTTP_404_NOT_FOUND)#Formattheresponsewithadditionalmilestonedataresult=[]forpathinprogression_path:path_data={"id":path.id"name":path.name"description":path.description"starting_level":path.starting_level"target_level":path.target_level"milestones":[]}#Addmilestonedatafrom courses.modelsimportProgressionMilestonemilestones=ProgressionMilestone.objects.filter(pathway=path).order_by("sequence_order")formilestoneinmilestones:is_completed=False#Checkifthismilestoneiscompletedtry:from courses.modelsimportStudentMilestonestudent_milestone=StudentMilestone.objects.get(student=request.usermilestone=milestone)is_completed=student_milestone.is_completedexcept:passpath_data["milestones"].append({"id":milestone.id"name":milestone.name"description":milestone.description"sequence_order":milestone.sequence_order"is_completed":is_completed})result.append(path_data)returnResponse(result)exceptStudentLevel.DoesNotExist:returnResponse({"detail":"Studentlevelnotfound."}status=status.HTTP_404_NOT_FOUND)classStudentLevelViewSet(viewsets.ModelViewSet):"""ViewSetformanagingstudentlevels"""serializer_class=StudentLevelSerializerpermission_classes=[permissions.IsAuthenticated]defget_queryset(self):returnStudentLevel.objects.filter(student=self.request.user)@action(detail=Falsemethods=["get"])defcurrent_level(selfrequest):"""Getthecurrentleveldetailsfortheauthenticateduser"""try:student_level=self.get_queryset().get()serializer=self.get_serializer(student_level)#Getnextlevelrequirementsnext_level_reqs=student_level.get_next_level_requirements()#Checkprogresstowardsnextlevelprogress=student_level.calculate_level_progress()returnResponse({"status":"success""data":{**serializer.data"next_level_requirements":next_level_reqs"progress_to_next_level":progress}})exceptStudentLevel.DoesNotExist:returnResponse({"detail":"Studentlevelnotfoundforthisuser."}status=status.HTTP_404_NOT_FOUND)@action(detail=Falsemethods=["get"])defnext_level_requirements(selfrequest):"""Getrequirementsforadvancingtothenextlevel"""try:student_level=self.get_queryset().get()requirements=student_level.get_next_level_requirements()ifnotrequirements:returnResponse({"detail":"Nonextlevelavailableorrequirementsnotdefined."}status=status.HTTP_404_NOT_FOUND)returnResponse(requirements)exceptStudentLevel.DoesNotExist:returnResponse({"detail":"Studentlevelnotfound."}status=status.HTTP_404_NOT_FOUND)classSkillViewSet(viewsets.ModelViewSet):"""ViewSetformanagingskills"""queryset=Skill.objects.all()serializer_class=SkillSerializerpermission_classes=[permissions.IsAuthenticated]defget_queryset(self):ifself.request.user.is_staff:returnSkill.objects.all()returnSkill.objects.filter(is_active=True)classLearningPathwayViewSet(viewsets.ModelViewSet):"""ViewSetformanaginglearningpathways"""queryset=LearningPathway.objects.all()serializer_class=LearningPathwaySerializerpermission_classes=[permissions.IsAuthenticated]defget_queryset(self):ifself.request.user.is_staff:returnLearningPathway.objects.all()returnLearningPathway.objects.filter(Q(required_level__lte=F("student_level__current_level"))&Q(is_active=True))classProgressionMilestoneViewSet(viewsets.ModelViewSet):"""ViewSetformanagingprogressionmilestones"""queryset=ProgressionMilestone.objects.all()serializer_class=ProgressionMilestoneSerializerpermission_classes=[permissions.IsAuthenticated]defget_queryset(self):ifself.request.user.is_staff:returnProgressionMilestone.objects.all()returnProgressionMilestone.objects.filter(is_active=True)classCompetencyBadgeViewSet(viewsets.ModelViewSet):"""ViewSetformanagingcompetencybadges"""queryset=CompetencyBadge.objects.all()serializer_class=CompetencyBadgeSerializerpermission_classes=[permissions.IsAuthenticated]defget_queryset(self):ifself.request.user.is_staff:returnCompetencyBadge.objects.all()returnCompetencyBadge.objects.filter(is_active=True)classStudentSkillsView(views.APIView):"""Viewforstudentskillsoperations"""permission_classes=[permissions.IsAuthenticated]defget(selfrequest):#Getstudent'sacquiredskillsfromcompletedcoursescompleted_courses=request.user.enrolled_courses.filter(enrollment__is_completed=True)acquired_skills=set()forcourseincompleted_courses:acquired_skills.update(course.skills_developed)#Getskillsinprogressfromcurrentcoursescurrent_courses=request.user.enrolled_courses.filter(enrollment__is_completed=False)in_progress_skills=set()forcourseincurrent_courses:in_progress_skills.update(course.skills_developed)#Removeacquiredskillsfromin_progressin_progress_skills=in_progress_skills-acquired_skills#Getskillgapsskill_gaps=SkillGap.objects.filter(student=request.useris_resolved=False)returnResponse({"status":"success""data":{"acquired_skills":list(acquired_skills)"in_progress_skills":list(in_progress_skills)"skill_gaps":SkillGapSerializer(skill_gapsmany=True).data}})classStudentLearningPathView(views.APIView):"""Viewforstudentlearningpathoperations"""permission_classes=[permissions.IsAuthenticated]defget(selfrequestpath_id=None):ifpath_id:pathway=get_object_or_404(LearningPathwayid=path_id)serializer=LearningPathwaySerializer(pathwaycontext={"request":request})returnResponse(serializer.data)pathways=LearningPathway.objects.all()serializer=LearningPathwaySerializer(pathwaysmany=Truecontext={"request":request})returnResponse(serializer.data)defpost(selfrequestpath_id):"""Enrollinalearningpath"""pathway=get_object_or_404(LearningPathwayid=path_id)#Checkifstudentmeetsrequirementsifnotpathway.check_eligibility(request.user):returnResponse({"error":"Youdonotmeettherequirementsforthislearningpath"}status=status.HTTP_400_BAD_REQUEST)#Createorupdateprogresstrackingtrackingcreated=StudentProgressTracking.objects.get_or_create(student=request.userlearning_path=pathway)serializer=StudentProgressTrackingSerializer(tracking)returnResponse(serializer.datastatus=status.HTTP_201_CREATEDifcreatedelsestatus.HTTP_200_OK)@action(detail=Truemethods=["get"])defcheck_eligibility(selfrequestpath_id):"""Checkifstudentiseligibleforthelearningpath"""pathway=get_object_or_404(LearningPathwayid=path_id)is_eligible=pathway.check_eligibility(request.user)missing_requirements=pathway.get_missing_requirements(request.user)returnResponse({"is_eligible":is_eligible"missing_requirements":missing_requirements})@action(detail=Truemethods=["get"])defstats(selfrequestpath_id):"""Getdetailedstatisticsaboutthelearningpath"""pathway=get_object_or_404(LearningPathwayid=path_id)tracking=StudentProgressTracking.objects.filter(student=request.userlearning_path=pathway).first()returnResponse({"total_students":pathway.studentprogresstracking_set.count()"completion_rate":pathway.get_completion_rate()"average_completion_time":pathway.get_average_completion_time()"your_progress":(StudentProgressTrackingSerializer(tracking).dataiftrackingelseNone)})@action(detail=Truemethods=["get"])defcertificate(selfrequestpath_id):"""Getcompletioncertificateifavailable"""pathway=get_object_or_404(LearningPathwayid=path_id)tracking=get_object_or_404(StudentProgressTrackingstudent=request.userlearning_path=pathway)ifnottracking.is_completed:returnResponse({"error":"Youhavenotcompletedthislearningpathyet"}status=status.HTTP_400_BAD_REQUEST)#Generateandreturncertificatedatacertificate_data={"student_name":request.user.get_full_name()"path_name":pathway.name"completion_date":tracking.completion_date"certificate_id":f"LP{pathway.id}-{request.user.id}-{tracking.completion_date.strftime('%Y%m%d')}"}returnResponse(certificate_data)classStudentMilestonesView(views.APIView):"""Viewforstudentmilestonesoperations"""permission_classes=[permissions.IsAuthenticated]defget(selfrequest):student_milestones=StudentMilestone.objects.filter(student=request.user).select_related("milestone""milestone__pathway")#Groupmilestonesbypathwaypathways={}forsminstudent_milestones:pathway=sm.milestone.pathwayifpathway.idnotinpathways:pathways[pathway.id]={"pathway":LearningPathwaySerializer(pathway).data"milestones":[]}pathways[pathway.id]["milestones"].append({"milestone":ProgressionMilestoneSerializer(sm.milestone).data"is_completed":sm.is_completed"date_achieved":sm.date_achieved"evidence":sm.evidence})returnResponse({"status":"success""data":list(pathways.values())})classStudentBadgesView(views.APIView):"""Viewforstudentbadgesoperations"""permission_classes=[permissions.IsAuthenticated]defget(selfrequest):student_badges=StudentBadge.objects.filter(student=request.user).select_related("badge")#Getprogresstowardsunearnedbadgesearned_badge_ids=student_badges.values_list("badge_id"flat=True)available_badges=CompetencyBadge.objects.exclude(id__in=earned_badge_ids).filter(is_active=True)#Calculateprogressforeachavailablebadgebadge_progress=[]forbadgeinavailable_badges:progress=badge.calculate_student_progress(request.user)ifprogress>0:badge_progress.append({"badge":CompetencyBadgeSerializer(badge).data"progress":progress})returnResponse({"status":"success""data":{"earned_badges":StudentBadgeSerializer(student_badgesmany=True).data"available_badges":badge_progress}})classStudentRecommendationsView(views.APIView):"""Viewforstudentlearningrecommendations"""permission_classes=[permissions.IsAuthenticated]defget(selfrequest):try:student_level=StudentLevel.objects.get(student=request.user)analytics=LearningAnalytics.objects.get(student=request.user)#Getrecommendationsbasedoncurrentlevelandlearningpatternsrecommendations={"next_courses":[]"skill_improvements":[]"learning_path_suggestions":[]}#Getcourserecommendationsfrom courses.modelsimportCourserecommended_courses=Course.objects.filter(Q(required_level=student_level.current_level)|Q(recommended_level=student_level.current_level)).exclude(id__in=request.user.enrolled_courses.values_list("id"flat=True))[:5]#Getskillgaprecommendationsskill_gaps=SkillGap.objects.filter(student=request.useris_resolved=False)#Getlearningpathrecommendationslearning_paths=LearningPathway.objects.filter(required_level=student_level.current_levelis_active=True).exclude(studentmilestone__student=request.user)#Formatrecommendationsfrom courses.serializersimportCourseSerializerrecommendations["next_courses"]=CourseSerializer(recommended_coursesmany=True).datarecommendations["skill_improvements"]=SkillGapSerializer(skill_gapsmany=True).datarecommendations["learning_path_suggestions"]=LearningPathwaySerializer(learning_pathsmany=True).datareturnResponse({"status":"success""data":{"current_level":student_level.current_level"learning_pace":analytics.learning_pace"best_time_of_day":analytics.best_time_of_day"recommendations":recommendations}})except(StudentLevel.DoesNotExistLearningAnalytics.DoesNotExist):returnResponse({"status":"error""message":"Studentleveloranalyticsnotfound"}status=status.HTTP_404_NOT_FOUND)classLevelRequirementViewSet(viewsets.ModelViewSet):"""ViewSetformanaginglevelrequirements"""queryset=LevelRequirement.objects.all()serializer_class=LevelRequirementSerializerpermission_classes=[permissions.IsAuthenticated]defget_queryset(self):ifself.request.user.is_staff:returnLevelRequirement.objects.all()returnLevelRequirement.objects.filter(level__lte=F("student_level__current_level")is_active=True)@action(detail=Falsemethods=["get"])deffor_level(selfrequest):"""Getrequirementsforaspecificlevel"""level=request.query_params.get("level")ifnotlevel:returnResponse({"detail":"Levelparameterisrequired"}status=status.HTTP_400_BAD_REQUEST)requirements=self.get_queryset().filter(level=level)serializer=self.get_serializer(requirementsmany=True)returnResponse(serializer.data)
#!/bin/bash

# North Star University Update Script
# Use this script to update the application after initial deployment

set -e

# Configuration
PROJECT_NAME="north-star-university"
PROJECT_DIR="/var/www/$PROJECT_NAME"
BACKEND_DIR="$PROJECT_DIR/backend"
FRONTEND_DIR="$PROJECT_DIR/frontend"
VENV_DIR="$PROJECT_DIR/venv"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Stop the application
log "Stopping application services..."
sudo systemctl stop $PROJECT_NAME || true
sudo systemctl stop ${PROJECT_NAME}-celery || true

# Backup database
log "Creating database backup..."
cd $BACKEND_DIR
python manage.py dumpdata --settings=settings.production > backup_$(date +%Y%m%d_%H%M%S).json

# Pull latest code (if using git)
log "Updating code..."
cd $PROJECT_DIR
# git pull origin main  # Uncomment if using git

# Activate virtual environment
source $VENV_DIR/bin/activate

# Update Python dependencies
log "Updating Python dependencies..."
cd $BACKEND_DIR
pip install -r requirements.txt

# Update frontend dependencies and rebuild
log "Updating and rebuilding frontend..."
cd $FRONTEND_DIR
npm install
npm run build

# Copy built frontend
log "Updating frontend files..."
sudo rm -rf $PROJECT_DIR/static_frontend
sudo cp -r $FRONTEND_DIR/dist $PROJECT_DIR/static_frontend
sudo chown -R www-data:www-data $PROJECT_DIR/static_frontend

# Collect static files
log "Collecting static files..."
cd $BACKEND_DIR
python manage.py collectstatic --noinput --settings=settings.production

# Run migrations
log "Running database migrations..."
python manage.py migrate --settings=settings.production

# Set permissions
log "Setting permissions..."
sudo chown -R www-data:www-data $PROJECT_DIR/media
sudo chown -R www-data:www-data $PROJECT_DIR/static

# Restart services
log "Restarting services..."
sudo systemctl start $PROJECT_NAME
sudo systemctl start ${PROJECT_NAME}-celery
sudo systemctl reload nginx

log "Update completed successfully!"

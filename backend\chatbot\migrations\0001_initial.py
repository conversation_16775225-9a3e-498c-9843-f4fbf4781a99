# Generated by Django 4.2.7 on 2025-07-04 15:54

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AIResponse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("content", models.TextField()),
                ("suggested_questions", models.JSO<PERSON>ield(default=list)),
                ("dynamic_content", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ChatConversation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.<PERSON>r<PERSON><PERSON>(
                        blank=True, default="New Conversation", max_length=255
                    ),
                ),
                (
                    "user_role",
                    models.Char<PERSON>ield(
                        blank=True,
                        choices=[
                            ("STUDENT", "Student"),
                            ("PROFESSOR", "Professor"),
                            ("ADMIN", "Admin"),
                        ],
                        max_length=20,
                    ),
                ),
                ("context", models.JSONField(blank=True, default=dict, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="ChatMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("user", "User"),
                            ("assistant", "Assistant"),
                            ("system", "System"),
                        ],
                        max_length=20,
                    ),
                ),
                ("content", models.TextField()),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Stores metadata about the message including learning style indicators",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("received", "Received"),
                            ("completed", "Completed"),
                            ("error", "Error"),
                        ],
                        default="received",
                        max_length=20,
                    ),
                ),
            ],
            options={
                "ordering": ["timestamp"],
            },
        ),
        migrations.CreateModel(
            name="ChatSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("started_at", models.DateTimeField(auto_now_add=True)),
                ("last_message_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("context", models.JSONField(blank=True, default=dict)),
            ],
        ),
        migrations.CreateModel(
            name="UserLearningProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "primary_learning_style",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("VISUAL", "Visual"),
                            ("AUDITORY", "Auditory"),
                            ("READ_WRITE", "Read/Write"),
                            ("KINESTHETIC", "Kinesthetic"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "secondary_learning_style",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("VISUAL", "Visual"),
                            ("AUDITORY", "Auditory"),
                            ("READ_WRITE", "Read/Write"),
                            ("KINESTHETIC", "Kinesthetic"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "comprehension_level",
                    models.IntegerField(
                        choices=[
                            (1, "Beginner"),
                            (2, "Elementary"),
                            (3, "Intermediate"),
                            (4, "Advanced"),
                            (5, "Expert"),
                        ],
                        default=1,
                    ),
                ),
                (
                    "interaction_style",
                    models.CharField(
                        choices=[
                            ("COLLABORATIVE", "Collaborative"),
                            ("INDEPENDENT", "Independent"),
                            ("STRUCTURED", "Structured"),
                            ("FLEXIBLE", "Flexible"),
                        ],
                        default="COLLABORATIVE",
                        max_length=20,
                    ),
                ),
                ("prefers_step_by_step", models.BooleanField(default=True)),
                ("prefers_examples", models.BooleanField(default=True)),
                ("prefers_visual_aids", models.BooleanField(default=True)),
                ("attention_span_minutes", models.IntegerField(default=30)),
                (
                    "pace_preference",
                    models.CharField(
                        choices=[
                            ("SLOW", "Slow and thorough"),
                            ("MODERATE", "Moderate pace"),
                            ("FAST", "Fast-paced"),
                        ],
                        default="MODERATE",
                        max_length=20,
                    ),
                ),
                ("last_updated", models.DateTimeField(auto_now=True)),
                ("interaction_patterns", models.JSONField(default=dict)),
            ],
            options={
                "verbose_name": "User Learning Profile",
                "verbose_name_plural": "User Learning Profiles",
            },
        ),
    ]

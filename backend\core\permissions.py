"""
Core permissions module.

This module contains all permission classes used across the application.
It consolidates permissions from various apps to provide a single source of truth.
"""
from django.conf import settings
from rest_framework import permissions


class BaseRolePermission(permissions.BasePermission):
    """Base class for role-based permissions."""
    role = None
    
    def has_permission(self, request, view):
        return bool(
            request.user and 
            request.user.is_authenticated and 
            getattr(request.user, "role", None) == self.role
        )


class IsAdmin(permissions.BasePermission):
    """Permission to only allow admin users to access the view."""
    
    def has_permission(self, request, view):
        return bool(
            request.user and 
            request.user.is_authenticated and 
            request.user.is_staff
        )


class IsAdminUser(IsAdmin):
    """Alias for IsAdmin for backward compatibility."""
    pass


class IsProfessor(BaseRolePermission):
    """Permission to only allow professors to access the view."""
    role = "PROFESSOR"


class IsInstructor(IsProfessor):
    """Alias for IsProfessor for backward compatibility."""
    pass


class IsProfessorUser(IsProfessor):
    """Alias for IsProfessor for backward compatibility."""
    pass


class IsStudent(BaseRolePermission):
    """Permission to only allow students to access the view."""
    role = "STUDENT"


class IsStudentUser(IsStudent):
    """Alias for IsStudent for backward compatibility."""
    pass


class CanTakeAssessments(permissions.BasePermission):
    """Permission to only allow students to take assessments."""
    
    def has_permission(self, request, view):
        return (
            request.user and 
            getattr(request.user, "role", None) == "STUDENT"
        )
    
    def has_object_permission(self, request, view, obj):
        if hasattr(obj, "student"):
            return obj.student == request.user
        return False

/**
 * Real-time WebSocket Service
 * Handles WebSocket connections for real-time features
 */

import { toast } from 'react-toastify';
import { enhancedAnalyticsService } from './enhancedAnalyticsService';

export interface WebSocketMessage {
  type: string;
  data?: any;
  message?: string;
  timestamp: string;
}

export interface NotificationData {
  id: number;
  title: string;
  message: string;
  type: string;
  created_at: string;
}

export interface RealTimeEventHandlers {
  onNotification?: (notification: NotificationData) => void;
  onAnalyticsUpdate?: (analytics: any) => void;
  onCourseUpdate?: (courseData: any) => void;
  onAssessmentUpdate?: (assessmentData: any) => void;
  onUniversityAnnouncement?: (announcement: any) => void;
  onConnectionEstablished?: () => void;
  onConnectionLost?: () => void;
  onError?: (error: string) => void;
}

class RealTimeService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // 1 second
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private isConnected = false;
  private eventHandlers: RealTimeEventHandlers = {};
  private subscriptions = new Set<string>();
  private messageQueue: any[] = [];

  constructor() {
    this.connect();
  }

  /**
   * Connect to WebSocket
   */
  connect(): void {
    try {
      // Determine WebSocket URL based on current location
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.host;
      const wsUrl = `${protocol}//${host}/ws/realtime/`;

      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);

    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      this.scheduleReconnect();
    }
  }

  /**
   * Handle WebSocket open event
   */
  private handleOpen(): void {
    console.log('WebSocket connected');
    this.isConnected = true;
    this.reconnectAttempts = 0;
    
    // Start heartbeat
    this.startHeartbeat();
    
    // Send queued messages
    this.sendQueuedMessages();
    
    // Restore subscriptions
    this.restoreSubscriptions();
    
    // Notify handlers
    if (this.eventHandlers.onConnectionEstablished) {
      this.eventHandlers.onConnectionEstablished();
    }
  }

  /**
   * Handle WebSocket message
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      switch (message.type) {
        case 'connection_established':
          console.log('Real-time connection established:', message.message);
          break;
          
        case 'initial_data':
          this.handleInitialData(message.data);
          break;
          
        case 'notification':
          this.handleNotification(message.data);
          break;
          
        case 'analytics_update':
          this.handleAnalyticsUpdate(message.data);
          break;
          
        case 'course_update':
          this.handleCourseUpdate(message.data);
          break;
          
        case 'assessment_update':
          this.handleAssessmentUpdate(message.data);
          break;
          
        case 'university_announcement':
          this.handleUniversityAnnouncement(message.data);
          break;
          
        case 'heartbeat_response':
          // Heartbeat acknowledged
          break;
          
        case 'subscription_confirmed':
          console.log('Subscription confirmed:', message);
          break;
          
        case 'activity_tracked':
          // Activity tracking confirmed
          break;
          
        case 'error':
          console.error('WebSocket error:', message.message);
          if (this.eventHandlers.onError) {
            this.eventHandlers.onError(message.message || 'Unknown error');
          }
          break;
          
        default:
          console.log('Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  /**
   * Handle WebSocket close event
   */
  private handleClose(): void {
    console.log('WebSocket disconnected');
    this.isConnected = false;
    this.stopHeartbeat();
    
    if (this.eventHandlers.onConnectionLost) {
      this.eventHandlers.onConnectionLost();
    }
    
    this.scheduleReconnect();
  }

  /**
   * Handle WebSocket error event
   */
  private handleError(error: Event): void {
    console.error('WebSocket error:', error);
    if (this.eventHandlers.onError) {
      this.eventHandlers.onError('Connection error');
    }
  }

  /**
   * Schedule reconnection
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      setTimeout(() => {
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        this.connect();
      }, delay);
    } else {
      console.error('Max reconnection attempts reached');
      toast.error('Connection lost. Please refresh the page.');
    }
  }

  /**
   * Send message to WebSocket
   */
  private sendMessage(message: any): void {
    if (this.isConnected && this.ws) {
      this.ws.send(JSON.stringify(message));
    } else {
      // Queue message for later
      this.messageQueue.push(message);
    }
  }

  /**
   * Send queued messages
   */
  private sendQueuedMessages(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.sendMessage(message);
    }
  }

  /**
   * Start heartbeat
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.sendMessage({
        type: 'heartbeat',
        timestamp: new Date().toISOString(),
      });
    }, 30000); // 30 seconds
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Handle initial data
   */
  private handleInitialData(data: any): void {
    if (data.notifications && this.eventHandlers.onNotification) {
      data.notifications.forEach((notification: NotificationData) => {
        this.eventHandlers.onNotification!(notification);
      });
    }
    
    if (data.real_time_stats && this.eventHandlers.onAnalyticsUpdate) {
      this.eventHandlers.onAnalyticsUpdate(data.real_time_stats);
    }
  }

  /**
   * Handle notification
   */
  private handleNotification(data: NotificationData): void {
    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
      new Notification(data.title, {
        body: data.message,
        icon: '/favicon.ico',
      });
    }
    
    // Show toast notification
    toast.info(data.message, {
      autoClose: 5000,
    });
    
    if (this.eventHandlers.onNotification) {
      this.eventHandlers.onNotification(data);
    }
  }

  /**
   * Handle analytics update
   */
  private handleAnalyticsUpdate(data: any): void {
    if (this.eventHandlers.onAnalyticsUpdate) {
      this.eventHandlers.onAnalyticsUpdate(data);
    }
  }

  /**
   * Handle course update
   */
  private handleCourseUpdate(data: any): void {
    toast.info('Course updated', {
      autoClose: 3000,
    });
    
    if (this.eventHandlers.onCourseUpdate) {
      this.eventHandlers.onCourseUpdate(data);
    }
  }

  /**
   * Handle assessment update
   */
  private handleAssessmentUpdate(data: any): void {
    toast.info('Assessment updated', {
      autoClose: 3000,
    });
    
    if (this.eventHandlers.onAssessmentUpdate) {
      this.eventHandlers.onAssessmentUpdate(data);
    }
  }

  /**
   * Handle university announcement
   */
  private handleUniversityAnnouncement(data: any): void {
    toast.warn(`University Announcement: ${data.title}`, {
      autoClose: 10000,
    });
    
    if (this.eventHandlers.onUniversityAnnouncement) {
      this.eventHandlers.onUniversityAnnouncement(data);
    }
  }

  /**
   * Restore subscriptions after reconnection
   */
  private restoreSubscriptions(): void {
    this.subscriptions.forEach(subscription => {
      this.sendMessage({
        type: 'subscribe_to_updates',
        subscription_type: subscription,
      });
    });
  }

  // Public methods

  /**
   * Set event handlers
   */
  setEventHandlers(handlers: RealTimeEventHandlers): void {
    this.eventHandlers = { ...this.eventHandlers, ...handlers };
  }

  /**
   * Subscribe to course updates
   */
  subscribeToCourseUpdates(courseId: number): void {
    const subscription = `course_${courseId}`;
    this.subscriptions.add(subscription);
    
    this.sendMessage({
      type: 'subscribe_to_updates',
      subscription_type: 'course_updates',
      course_id: courseId,
    });
  }

  /**
   * Subscribe to assessment updates
   */
  subscribeToAssessmentUpdates(): void {
    this.subscriptions.add('assessment_updates');
    
    this.sendMessage({
      type: 'subscribe_to_updates',
      subscription_type: 'assessment_updates',
    });
  }

  /**
   * Request analytics update
   */
  requestAnalyticsUpdate(): void {
    this.sendMessage({
      type: 'request_analytics',
    });
  }

  /**
   * Track activity
   */
  trackActivity(activityType: string, metadata: Record<string, any> = {}): void {
    this.sendMessage({
      type: 'track_activity',
      activity_type: activityType,
      metadata,
    });
  }

  /**
   * Get connection status
   */
  isConnectionActive(): boolean {
    return this.isConnected;
  }

  /**
   * Disconnect WebSocket
   */
  disconnect(): void {
    this.stopHeartbeat();
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
  }

  /**
   * Request browser notification permission
   */
  async requestNotificationPermission(): Promise<NotificationPermission> {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission;
    }
    return 'denied';
  }
}

export const realTimeService = new RealTimeService();

/**
 * Missing translations loader
 * This module loads translations that are missing and causing console errors
 */

import { missingArTranslations } from './ar';
import { missingEnTranslations } from './en';

export const loadMissingTranslations = (i18n: any) => {
  // Load missing Arabic translations
  Object.entries(missingArTranslations).forEach(([namespace, translations]) => {
    // Add to the specific namespace
    i18n.addResourceBundle('ar', namespace, translations, true, true);

    // Also add to the main translation bundle for backward compatibility
    const currentTranslations =
      i18n.getResourceBundle('ar', 'translation') || {};
    i18n.addResourceBundle(
      'ar',
      'translation',
      {
        ...currentTranslations,
        [namespace]: translations,
      },
      true,
      true
    );
  });

  // Load missing English translations
  Object.entries(missingEnTranslations).forEach(([namespace, translations]) => {
    // Add to the specific namespace
    i18n.addResourceBundle('en', namespace, translations, true, true);

    // Also add to the main translation bundle for backward compatibility
    const currentTranslations =
      i18n.getResourceBundle('en', 'translation') || {};
    i18n.addResourceBundle(
      'en',
      'translation',
      {
        ...currentTranslations,
        [namespace]: translations,
      },
      true,
      true
    );
  });

  console.log('Missing translations loaded for both Arabic and English');
};

from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView, TokenVerifyView
from .check_user_exists import check_user_exists
from .views import (
    CustomTokenObtainPairView,
    PendingRegistrationsCountView,
    PendingRegistrationsView,
    RegistrationApprovalView,
    UserProfileView,
    register,
)

urlpatterns = [
    # JWT authentication endpoints
    path("token/", CustomTokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("token/verify/", TokenVerifyView.as_view(), name="token_verify"),
    
    # User endpoints
    path("profile/", UserProfileView.as_view(), name="user-profile"),
    path("me/", UserProfileView.as_view(), name="user-me"),  # Using UserProfileView for both endpoints
    
    # Registration endpoints
    path("register/", register, name="register"),
    path("check-user-exists/", check_user_exists, name="check-user-exists"),
    
    # initial-registration endpoint has been removed as it was a duplicate
    path("pending-registrations/", PendingRegistrationsView.as_view(), name="pending-registrations-list"),
    path("pending-registrations/count/", PendingRegistrationsCountView.as_view(), name="pending-registrations-count"),
    path("pending-registrations/<int:user_id>/approve/", PendingRegistrationsView.as_view(), name="pending-registration-approve"),
    path("pending-registrations/<int:user_id>/reject/", PendingRegistrationsView.as_view(), name="pending-registration-reject"),
]

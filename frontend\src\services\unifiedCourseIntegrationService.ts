import axiosInstance from '../config/axios';
import { API_ENDPOINTS } from '../config/api';
import courseService from './courseService';
import interactiveLearningService from './interactiveLearningService';

export interface UnifiedCourseData {
  id: number;
  title: string;
  description: string;
  course_code: string;
  instructor: any;
  department: any;
  
  // Standard content
  materials: any[];
  assignments: any[];
  announcements: any[];
  
  // AI-generated content
  ai_content: {
    weekly_schedule?: any;
    lesson_plans?: any;
    sample_quizzes?: any;
    recommended_readings?: any;
  } | null;
  
  // Interactive learning content
  interactive_content: {
    lessons: any[];
    achievements: any[];
    progress: any;
    gamification: any;
  } | null;
  
  // Integration metadata
  integration_status: {
    has_ai_content: boolean;
    has_interactive_content: boolean;
    has_standard_content: boolean;
    unified_view_available: boolean;
    last_sync: string;
  };
  
  // Unified index
  content_index: {
    total_items: number;
    by_type: {
      material: number;
      ai_generated: number;
      interactive: number;
      assignment: number;
    };
    searchable_content: string[];
  };
}

export class UnifiedCourseIntegrationService {
  
  /**
   * Get comprehensive course data with all three systems integrated
   */
  async getUnifiedCourseData(courseId: number): Promise<UnifiedCourseData> {
    try {
      console.log(`Fetching unified course data for course ID: ${courseId}`);
      
      // Fetch data from all three systems in parallel
      const [courseData, aiContent, interactiveData] = await Promise.allSettled([
        this.getStandardCourseData(courseId),
        this.getAICourseContent(courseId),
        this.getInteractiveCourseData(courseId)
      ]);
      
      // Process results
      const standardData = courseData.status === 'fulfilled' ? courseData.value : null;
      const aiData = aiContent.status === 'fulfilled' ? aiContent.value : null;
      const interactiveData = interactiveData.status === 'fulfilled' ? interactiveData.value : null;
      
      if (!standardData) {
        throw new Error('Failed to fetch basic course data');
      }
      
      // Build unified data structure
      const unifiedData: UnifiedCourseData = {
        // Basic course info
        id: standardData.id,
        title: standardData.title,
        description: standardData.description,
        course_code: standardData.course_code,
        instructor: standardData.instructor,
        department: standardData.department,
        
        // Standard content
        materials: standardData.materials || [],
        assignments: standardData.assignments || [],
        announcements: standardData.announcements || [],
        
        // AI content
        ai_content: aiData,
        
        // Interactive content  
        interactive_content: interactiveData,
        
        // Integration status
        integration_status: {
          has_ai_content: !!aiData,
          has_interactive_content: !!interactiveData,
          has_standard_content: !!(standardData.materials?.length || standardData.assignments?.length),
          unified_view_available: true,
          last_sync: new Date().toISOString()
        },
        
        // Content index
        content_index: this.buildContentIndex(standardData, aiData, interactiveData)
      };
      
      console.log('Unified course data assembled:', unifiedData);
      return unifiedData;
      
    } catch (error: any) {
      console.error('Error in getUnifiedCourseData:', error);
      throw new Error(`Failed to get unified course data: ${error.message}`);
    }
  }
  
  /**
   * Create interactive version of a course
   */
  async createInteractiveVersion(courseId: number): Promise<any> {
    try {
      console.log(`Creating interactive version for course ${courseId}`);
      
      // First check if interactive version already exists
      const existingInteractive = await this.getInteractiveCourseData(courseId);
      if (existingInteractive) {
        return {
          success: true,
          message: 'Interactive version already exists',
          data: existingInteractive
        };
      }
      
      // Get course data to understand what content to make interactive
      const courseData = await this.getStandardCourseData(courseId);
      if (!courseData) {
        throw new Error('Course not found');
      }
      
      // Create interactive course
      const interactiveResult = await interactiveLearningService.createInteractiveCourse(courseId);
      if (!interactiveResult?.data) {
        throw new Error('Failed to create interactive course');
      }
      
      const interactiveCourseId = interactiveResult.data.interactive_course_id;
      
      // Generate AI content for the interactive course if none exists
      const aiContent = await this.getAICourseContent(courseId);
      if (!aiContent) {
        console.log('Generating AI content for interactive course...');
        try {
          await this.generateAIContentForCourse(courseId);
        } catch (aiError) {
          console.warn('Failed to generate AI content, continuing without it:', aiError);
        }
      }
      
      // Create initial interactive lessons based on course materials
      await this.createInteractiveLessonsFromMaterials(interactiveCourseId, courseData.materials);
      
      console.log('Interactive version created successfully');
      return {
        success: true,
        message: 'Interactive version created and integrated successfully',
        data: {
          interactive_course_id: interactiveCourseId,
          lessons_created: courseData.materials?.length || 0,
          ai_content_available: !!aiContent
        }
      };
      
    } catch (error: any) {
      console.error('Error creating interactive version:', error);
      throw new Error(`Failed to create interactive version: ${error.message}`);
    }
  }
  
  /**
   * Generate AI content for a course
   */
  async generateAIContentForCourse(courseId: number): Promise<any> {
    try {
      console.log(`Generating AI content for course ${courseId}`);
      
      // Get course basic info
      const courseData = await this.getStandardCourseData(courseId);
      if (!courseData) {
        throw new Error('Course not found');
      }
      
      // Generate different types of AI content
      const aiGenerationTasks = [
        this.generateWeeklySchedule(courseId, courseData),
        this.generateLessonPlans(courseId, courseData),
        this.generateSampleQuizzes(courseId, courseData),
        this.generateRecommendedReadings(courseId, courseData)
      ];
      
      const results = await Promise.allSettled(aiGenerationTasks);
      
      // Compile successful results
      const aiContent: any = {};
      if (results[0].status === 'fulfilled') aiContent.weekly_schedule = results[0].value;
      if (results[1].status === 'fulfilled') aiContent.lesson_plans = results[1].value;
      if (results[2].status === 'fulfilled') aiContent.sample_quizzes = results[2].value;
      if (results[3].status === 'fulfilled') aiContent.recommended_readings = results[3].value;
      
      // Save AI content to the backend
      await this.saveAIContent(courseId, aiContent);
      
      console.log('AI content generated successfully');
      return aiContent;
      
    } catch (error: any) {
      console.error('Error generating AI content:', error);
      throw new Error(`Failed to generate AI content: ${error.message}`);
    }
  }
  
  /**
   * Get standard course data (materials, assignments, etc.)
   */
  private async getStandardCourseData(courseId: number): Promise<any> {
    try {
      const courseResponse = await courseService.getAdminCourse(courseId);
      const materialsResponse = await courseService.getCourseMaterials(courseId);
      const assignmentsResponse = await courseService.getAdminCourseAssignments(courseId);
      
      return {
        ...courseResponse.data,
        materials: materialsResponse.data || [],
        assignments: assignmentsResponse.data || [],
        announcements: [] // TODO: Implement announcements if needed
      };
    } catch (error: any) {
      console.error('Error fetching standard course data:', error);
      return null;
    }
  }
  
  /**
   * Get AI-generated content for a course
   */
  private async getAICourseContent(courseId: number): Promise<any> {
    try {
      const aiContent = await courseService.getAIContent(courseId);
      return aiContent;
    } catch (error: any) {
      console.error('Error fetching AI content:', error);
      return null;
    }
  }
  
  /**
   * Get interactive learning data for a course
   */
  private async getInteractiveCourseData(courseId: number): Promise<any> {
    try {
      const interactiveCourses = await interactiveLearningService.getInteractiveCourses(courseId);
      if (!interactiveCourses || interactiveCourses.length === 0) {
        return null;
      }
      
      const interactiveCourse = interactiveCourses[0];
      const lessons = await interactiveLearningService.getLessons(interactiveCourse.id);
      const achievements = await interactiveLearningService.getAchievements(courseId);
      
      return {
        course: interactiveCourse,
        lessons: lessons || [],
        achievements: achievements?.achievements || [],
        progress: {}, // TODO: Get student progress
        gamification: {} // TODO: Get gamification data
      };
    } catch (error: any) {
      console.error('Error fetching interactive data:', error);
      return null;
    }
  }
  
  /**
   * Build content index for search and navigation
   */
  private buildContentIndex(standardData: any, aiData: any, interactiveData: any): any {
    const index = {
      total_items: 0,
      by_type: {
        material: 0,
        ai_generated: 0,
        interactive: 0,
        assignment: 0
      },
      searchable_content: [] as string[]
    };
    
    // Count standard content
    if (standardData?.materials) {
      index.by_type.material = standardData.materials.length;
      index.total_items += standardData.materials.length;
      index.searchable_content.push(...standardData.materials.map((m: any) => m.title).filter(Boolean));
    }
    
    if (standardData?.assignments) {
      index.by_type.assignment = standardData.assignments.length;
      index.total_items += standardData.assignments.length;
      index.searchable_content.push(...standardData.assignments.map((a: any) => a.title).filter(Boolean));
    }
    
    // Count AI content
    if (aiData) {
      const aiContentCount = Object.keys(aiData).length;
      index.by_type.ai_generated = aiContentCount;
      index.total_items += aiContentCount;
      index.searchable_content.push('AI Generated Content');
    }
    
    // Count interactive content
    if (interactiveData?.lessons) {
      index.by_type.interactive = interactiveData.lessons.length;
      index.total_items += interactiveData.lessons.length;
      index.searchable_content.push(...interactiveData.lessons.map((l: any) => l.title).filter(Boolean));
    }
    
    return index;
  }
  
  /**
   * Create interactive lessons from course materials
   */
  private async createInteractiveLessonsFromMaterials(interactiveCourseId: number, materials: any[]): Promise<void> {
    if (!materials || materials.length === 0) {
      console.log('No materials to convert to interactive lessons');
      return;
    }
    
    console.log(`Converting ${materials.length} materials to interactive lessons`);
    
    for (const material of materials.slice(0, 3)) { // Limit to first 3 materials
      try {
        const lessonData = {
          interactive_course: interactiveCourseId,
          title: material.title || 'Interactive Lesson',
          description: material.description || 'Converted from course material',
          lesson_type: 'CONCEPT',
          content: {
            introduction: material.description || 'This lesson was generated from course material.',
            key_points: [
              'Review the main concepts',
              'Practice with exercises',
              'Apply your knowledge'
            ],
            examples: []
          },
          estimated_minutes: 15,
          skill_level: 1
        };
        
        await interactiveLearningService.createLesson(lessonData);
        console.log(`Created interactive lesson from material: ${material.title}`);
      } catch (error) {
        console.warn(`Failed to create lesson from material ${material.title}:`, error);
      }
    }
  }
  
  /**
   * Generate weekly schedule with AI
   */
  private async generateWeeklySchedule(courseId: number, courseData: any): Promise<any> {
    try {
      const response = await axiosInstance.post('/api/v1/course-generator/generate/', {
        course_id: courseId,
        generation_type: 'schedule',
        num_weeks: 14
      });
      return response.data.schedule;
    } catch (error) {
      console.warn('Failed to generate weekly schedule:', error);
      return null;
    }
  }
  
  /**
   * Generate lesson plans with AI
   */
  private async generateLessonPlans(courseId: number, courseData: any): Promise<any> {
    try {
      const response = await axiosInstance.post('/api/v1/course-generator/generate/', {
        course_id: courseId,
        generation_type: 'lesson_plan',
        topic: courseData.title,
        week_number: 1
      });
      return response.data.lesson_plan;
    } catch (error) {
      console.warn('Failed to generate lesson plans:', error);
      return null;
    }
  }
  
  /**
   * Generate sample quizzes with AI
   */
  private async generateSampleQuizzes(courseId: number, courseData: any): Promise<any> {
    try {
      const response = await axiosInstance.post('/api/v1/course-generator/generate/', {
        course_id: courseId,
        generation_type: 'assessment',
        assessment_type: 'quiz'
      });
      return response.data.assessment;
    } catch (error) {
      console.warn('Failed to generate sample quizzes:', error);
      return null;
    }
  }
  
  /**
   * Generate recommended readings with AI
   */
  private async generateRecommendedReadings(courseId: number, courseData: any): Promise<any> {
    try {
      const response = await axiosInstance.post('/api/v1/course-generator/generate/', {
        course_id: courseId,
        generation_type: 'course_content',
        keywords: 'recommended readings, bibliography'
      });
      return response.data.content;
    } catch (error) {
      console.warn('Failed to generate recommended readings:', error);
      return null;
    }
  }
  
  /**
   * Save AI content to backend
   */
  private async saveAIContent(courseId: number, aiContent: any): Promise<void> {
    try {
      await axiosInstance.post(`/api/v1/courses/${courseId}/ai-content/`, aiContent);
      console.log('AI content saved successfully');
    } catch (error) {
      console.warn('Failed to save AI content:', error);
    }
  }
  
  /**
   * Search across all content types
   */
  async searchUnifiedContent(courseId: number, query: string): Promise<any[]> {
    try {
      const unifiedData = await this.getUnifiedCourseData(courseId);
      const results: any[] = [];
      
      const searchQuery = query.toLowerCase();
      
      // Search standard materials
      unifiedData.materials.forEach(material => {
        if (material.title?.toLowerCase().includes(searchQuery) ||
            material.description?.toLowerCase().includes(searchQuery)) {
          results.push({
            ...material,
            content_type: 'MATERIAL',
            source: 'standard'
          });
        }
      });
      
      // Search interactive lessons
      if (unifiedData.interactive_content?.lessons) {
        unifiedData.interactive_content.lessons.forEach(lesson => {
          if (lesson.title?.toLowerCase().includes(searchQuery) ||
              lesson.description?.toLowerCase().includes(searchQuery)) {
            results.push({
              ...lesson,
              content_type: 'INTERACTIVE_LESSON',
              source: 'interactive'
            });
          }
        });
      }
      
      // Search AI content
      if (unifiedData.ai_content) {
        Object.entries(unifiedData.ai_content).forEach(([key, value]) => {
          if (key.toLowerCase().includes(searchQuery)) {
            results.push({
              title: key.replace('_', ' ').toUpperCase(),
              content: value,
              content_type: 'AI_GENERATED',
              source: 'ai'
            });
          }
        });
      }
      
      return results;
    } catch (error: any) {
      console.error('Error searching unified content:', error);
      return [];
    }
  }
  
  /**
   * Get content recommendations based on user progress
   */
  async getContentRecommendations(courseId: number, userId: number): Promise<any[]> {
    try {
      // This would implement intelligent content recommendation
      // based on user progress, learning patterns, etc.
      const unifiedData = await this.getUnifiedCourseData(courseId);
      const recommendations: any[] = [];
      
      // Simple recommendation logic - in real implementation this would be more sophisticated
      if (unifiedData.interactive_content?.lessons?.length > 0) {
        recommendations.push({
          type: 'interactive_lesson',
          title: 'Continue with Interactive Learning',
          description: 'Pick up where you left off with interactive lessons',
          items: unifiedData.interactive_content.lessons.slice(0, 3)
        });
      }
      
      if (unifiedData.ai_content) {
        recommendations.push({
          type: 'ai_content',
          title: 'AI-Generated Study Materials',
          description: 'Explore AI-curated content for this course',
          items: Object.keys(unifiedData.ai_content).map(key => ({
            title: key.replace('_', ' ').toUpperCase(),
            type: key
          }))
        });
      }
      
      return recommendations;
    } catch (error: any) {
      console.error('Error getting content recommendations:', error);
      return [];
    }
  }
}

export const unifiedCourseIntegrationService = new UnifiedCourseIntegrationService();
export default unifiedCourseIntegrationService;

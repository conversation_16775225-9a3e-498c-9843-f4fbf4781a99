/**
 * Centralized Error Handling for Frontend AI Services
 *
 * Provides consistent error handling, user-friendly messages, and error reporting
 * across all AI services in the frontend application.
 */

import {
  AIServiceError,
  AIServiceConnectionError,
  AIServiceTimeoutError,
  AIServiceValidationError,
} from './aiServiceUtils';

// Error message mappings for user-friendly display
export const ERROR_MESSAGES = {
  // Connection errors
  CONNECTION_ERROR:
    'Unable to connect to the AI service. Please check your internet connection and try again.',
  TIMEOUT_ERROR:
    'The request is taking longer than expected. Please try again.',

  // Authentication errors
  AUTH_ERROR: 'Please log in to access AI features.',
  ACCESS_DENIED: 'You do not have permission to access this feature.',

  // Validation errors
  VALIDATION_ERROR: 'Please check your input and try again.',
  INVALID_REQUEST:
    'The request contains invalid data. Please review and try again.',

  // Server errors
  SERVER_ERROR:
    'The AI service is temporarily unavailable. Please try again in a few moments.',
  SERVICE_UNAVAILABLE:
    'AI services are currently under maintenance. Please try again later.',

  // Rate limiting
  RATE_LIMIT:
    'You have made too many requests. Please wait a moment before trying again.',

  // Generic errors
  UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',
  NETWORK_ERROR: 'Network error occurred. Please check your connection.',

  // Feature-specific errors
  CHAT_ERROR: 'Unable to send message. Please try again.',
  GENERATION_ERROR:
    'Content generation failed. Please try again with different parameters.',
  ANALYSIS_ERROR: 'Analysis could not be completed. Please try again.',
  SUGGESTION_ERROR: 'Unable to load suggestions. Please refresh the page.',

  // Fallback messages
  FALLBACK_ACTIVE:
    'AI services are temporarily limited. Some features may not be available.',
  PARTIAL_FAILURE:
    'Some AI features are currently unavailable, but basic functionality is working.',
};

// Error severity levels
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Error categories for analytics and debugging
export enum ErrorCategory {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  VALIDATION = 'validation',
  SERVER = 'server',
  CLIENT = 'client',
  TIMEOUT = 'timeout',
  RATE_LIMIT = 'rate_limit',
  UNKNOWN = 'unknown',
}

export interface ErrorInfo {
  message: string;
  severity: ErrorSeverity;
  category: ErrorCategory;
  userMessage: string;
  retryable: boolean;
  reportable: boolean;
}

/**
 * Get standardized error information from an error
 */
export const getErrorInfo = (error: any): ErrorInfo => {
  if (error instanceof AIServiceError) {
    return mapAIServiceError(error);
  }

  // Handle axios errors
  if (error.response) {
    return mapHttpError(error.response.status, error.response.data);
  }

  // Handle network errors
  if (error.request) {
    return {
      message: 'Network request failed',
      severity: ErrorSeverity.HIGH,
      category: ErrorCategory.NETWORK,
      userMessage: ERROR_MESSAGES.NETWORK_ERROR,
      retryable: true,
      reportable: true,
    };
  }

  // Handle generic errors
  return {
    message: error.message || 'Unknown error',
    severity: ErrorSeverity.MEDIUM,
    category: ErrorCategory.UNKNOWN,
    userMessage: ERROR_MESSAGES.UNKNOWN_ERROR,
    retryable: true,
    reportable: true,
  };
};

/**
 * Map AI service errors to error info
 */
const mapAIServiceError = (error: AIServiceError): ErrorInfo => {
  const baseInfo = {
    message: error.message,
    retryable: true,
    reportable: true,
  };

  switch (error.code) {
    case 'CONNECTION_ERROR':
      return {
        ...baseInfo,
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.NETWORK,
        userMessage: ERROR_MESSAGES.CONNECTION_ERROR,
      };

    case 'TIMEOUT_ERROR':
      return {
        ...baseInfo,
        severity: ErrorSeverity.MEDIUM,
        category: ErrorCategory.TIMEOUT,
        userMessage: ERROR_MESSAGES.TIMEOUT_ERROR,
      };

    case 'VALIDATION_ERROR':
      return {
        ...baseInfo,
        severity: ErrorSeverity.LOW,
        category: ErrorCategory.VALIDATION,
        userMessage: ERROR_MESSAGES.VALIDATION_ERROR,
        retryable: false,
      };

    case 'AUTH_ERROR':
      return {
        ...baseInfo,
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.AUTHENTICATION,
        userMessage: ERROR_MESSAGES.AUTH_ERROR,
        retryable: false,
      };

    case 'RATE_LIMIT':
      return {
        ...baseInfo,
        severity: ErrorSeverity.MEDIUM,
        category: ErrorCategory.RATE_LIMIT,
        userMessage: ERROR_MESSAGES.RATE_LIMIT,
      };

    case 'SERVER_ERROR':
      return {
        ...baseInfo,
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.SERVER,
        userMessage: ERROR_MESSAGES.SERVER_ERROR,
      };

    default:
      return {
        ...baseInfo,
        severity: ErrorSeverity.MEDIUM,
        category: ErrorCategory.UNKNOWN,
        userMessage: ERROR_MESSAGES.UNKNOWN_ERROR,
      };
  }
};

/**
 * Map HTTP status codes to error info
 */
const mapHttpError = (status: number, data: any): ErrorInfo => {
  const message = data?.message || data?.error || `HTTP ${status} error`;

  switch (status) {
    case 400:
      return {
        message,
        severity: ErrorSeverity.LOW,
        category: ErrorCategory.VALIDATION,
        userMessage: ERROR_MESSAGES.VALIDATION_ERROR,
        retryable: false,
        reportable: false,
      };

    case 401:
      return {
        message,
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.AUTHENTICATION,
        userMessage: ERROR_MESSAGES.AUTH_ERROR,
        retryable: false,
        reportable: false,
      };

    case 403:
      return {
        message,
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.AUTHENTICATION,
        userMessage: ERROR_MESSAGES.ACCESS_DENIED,
        retryable: false,
        reportable: false,
      };

    case 404:
      return {
        message,
        severity: ErrorSeverity.MEDIUM,
        category: ErrorCategory.CLIENT,
        userMessage: 'The requested resource was not found.',
        retryable: false,
        reportable: true,
      };

    case 408:
      return {
        message,
        severity: ErrorSeverity.MEDIUM,
        category: ErrorCategory.TIMEOUT,
        userMessage: ERROR_MESSAGES.TIMEOUT_ERROR,
        retryable: true,
        reportable: true,
      };

    case 429:
      return {
        message,
        severity: ErrorSeverity.MEDIUM,
        category: ErrorCategory.RATE_LIMIT,
        userMessage: ERROR_MESSAGES.RATE_LIMIT,
        retryable: true,
        reportable: false,
      };

    case 500:
    case 502:
    case 503:
    case 504:
      return {
        message,
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.SERVER,
        userMessage: ERROR_MESSAGES.SERVER_ERROR,
        retryable: true,
        reportable: true,
      };

    default:
      return {
        message,
        severity: ErrorSeverity.MEDIUM,
        category: ErrorCategory.UNKNOWN,
        userMessage: ERROR_MESSAGES.UNKNOWN_ERROR,
        retryable: true,
        reportable: true,
      };
  }
};

/**
 * Log error for debugging and analytics
 */
export const logError = (
  error: any,
  context: string,
  additionalInfo?: any
): void => {
  const errorInfo = getErrorInfo(error);

  console.group(`🚨 AI Service Error - ${context}`);
  console.error('Error:', error);
  console.log('Error Info:', errorInfo);
  if (additionalInfo) {
    console.log('Additional Info:', additionalInfo);
  }
  console.groupEnd();

  // In production, you might want to send this to an error reporting service
  if (errorInfo.reportable && process.env.NODE_ENV === 'production') {
    // Example: Send to error reporting service
    // errorReportingService.report(error, context, errorInfo, additionalInfo);
  }
};

/**
 * Create user-friendly error message with optional retry information
 */
export const createUserErrorMessage = (
  error: any,
  context?: string
): string => {
  const errorInfo = getErrorInfo(error);
  let message = errorInfo.userMessage;

  if (context) {
    message = `${context}: ${message}`;
  }

  if (errorInfo.retryable) {
    message += ' You can try again.';
  }

  return message;
};

/**
 * Determine if an error should trigger a fallback response
 */
export const shouldUseFallback = (error: any): boolean => {
  const errorInfo = getErrorInfo(error);

  return (
    errorInfo.category === ErrorCategory.SERVER ||
    errorInfo.category === ErrorCategory.NETWORK ||
    errorInfo.category === ErrorCategory.TIMEOUT ||
    errorInfo.severity === ErrorSeverity.HIGH
  );
};

/**
 * Get retry delay based on error type and attempt number
 */
export const getRetryDelay = (error: any, attempt: number): number => {
  const errorInfo = getErrorInfo(error);

  if (!errorInfo.retryable) {
    return 0;
  }

  // Base delay in milliseconds
  let baseDelay = 1000;

  // Adjust based on error category
  switch (errorInfo.category) {
    case ErrorCategory.RATE_LIMIT:
      baseDelay = 5000; // 5 seconds for rate limits
      break;
    case ErrorCategory.SERVER:
      baseDelay = 2000; // 2 seconds for server errors
      break;
    case ErrorCategory.TIMEOUT:
      baseDelay = 3000; // 3 seconds for timeouts
      break;
  }

  // Exponential backoff with jitter
  const exponentialDelay = baseDelay * Math.pow(2, attempt - 1);
  const jitter = Math.random() * 1000; // Add up to 1 second of jitter

  return Math.min(exponentialDelay + jitter, 30000); // Cap at 30 seconds
};

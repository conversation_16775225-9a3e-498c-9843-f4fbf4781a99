/**
 * Personal Analytics Dashboard
 * Shows personalized analytics for individual users
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Avatar,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Tooltip,
  Alert,
  Skeleton,
  Button,
} from '@mui/material';
import {
  TrendingUp,
  School,
  Assessment,
  Star,
  PlayArrow,
  CheckCircle,
  Schedule,
  Lightbulb,
  Refresh,
  Timeline,
  EmojiEvents,
} from '@mui/icons-material';
import { Doughnut, Line, Radar } from 'react-chartjs-2';
import { format, parseISO } from 'date-fns';

import { enhancedAnalyticsService, PersonalAnalytics } from '../../services/enhancedAnalyticsService';
import { realTimeService } from '../../services/realTimeService';
import { useToast } from '../../hooks/useToast';

interface PersonalAnalyticsDashboardProps {
  userId?: number;
}

const PersonalAnalyticsDashboard: React.FC<PersonalAnalyticsDashboardProps> = ({
  userId,
}) => {
  const [analytics, setAnalytics] = useState<PersonalAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const { showToast } = useToast();

  // Load personal analytics
  const loadPersonalAnalytics = useCallback(async () => {
    try {
      setError(null);
      setLoading(true);
      
      const data = await enhancedAnalyticsService.getPersonalAnalytics();
      
      if (data) {
        setAnalytics(data);
        setLastUpdated(new Date());
      } else {
        setError('No analytics data available');
      }
    } catch (err) {
      setError('Failed to load personal analytics');
      console.error('Personal analytics error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Track activity when component mounts
  useEffect(() => {
    enhancedAnalyticsService.trackActivity('view_personal_analytics', {
      page: 'personal_dashboard',
      timestamp: new Date().toISOString(),
    });
  }, []);

  // Initial load
  useEffect(() => {
    loadPersonalAnalytics();
  }, [loadPersonalAnalytics]);

  // Handle refresh
  const handleRefresh = () => {
    loadPersonalAnalytics();
    enhancedAnalyticsService.trackActivity('refresh_personal_analytics');
  };

  // Chart configurations
  const courseCompletionChartData = {
    labels: ['Completed', 'In Progress', 'Not Started'],
    datasets: [
      {
        data: [
          analytics?.course_completion.completed || 0,
          analytics?.course_completion.in_progress || 0,
          analytics?.course_completion.not_started || 0,
        ],
        backgroundColor: [
          '#4CAF50',
          '#FF9800',
          '#9E9E9E',
        ],
        borderWidth: 2,
      },
    ],
  };

  const assessmentPerformanceChartData = {
    labels: analytics?.assessment_performance.recent_performance?.map((_, index) => 
      `Assessment ${index + 1}`
    ) || [],
    datasets: [
      {
        label: 'Score (%)',
        data: analytics?.assessment_performance.recent_performance?.map(perf => perf.score) || [],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.4,
        fill: true,
      },
    ],
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Grid container spacing={3}>
          {[1, 2, 3, 4].map((item) => (
            <Grid item xs={12} sm={6} md={3} key={item}>
              <Card>
                <CardContent>
                  <Skeleton variant="text" height={40} />
                  <Skeleton variant="text" height={60} />
                  <Skeleton variant="rectangular" height={100} />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  if (error || !analytics) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" action={
          <Button onClick={handleRefresh} size="small">
            Retry
          </Button>
        }>
          {error || 'No analytics data available'}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          My Learning Analytics
        </Typography>
        
        <Tooltip title="Refresh Data">
          <IconButton onClick={handleRefresh} disabled={loading}>
            <Refresh />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <School />
                </Avatar>
                <Box>
                  <Typography variant="h4">
                    {analytics.learning_progress.total_courses}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Enrolled Courses
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    {analytics.learning_progress.completed_courses} completed
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                  <TrendingUp />
                </Avatar>
                <Box>
                  <Typography variant="h4">
                    {analytics.learning_progress.average_progress.toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Average Progress
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={analytics.learning_progress.average_progress}
                    sx={{ mt: 1, width: 100 }}
                  />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                  <Assessment />
                </Avatar>
                <Box>
                  <Typography variant="h4">
                    {analytics.assessment_performance.total_assessments}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Assessments Taken
                  </Typography>
                  <Typography variant="body2" color="info.main">
                    {analytics.assessment_performance.average_score.toFixed(1)}% avg score
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <EmojiEvents />
                </Avatar>
                <Box>
                  <Typography variant="h4">
                    {analytics.assessment_performance.best_score.toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Best Score
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    Personal Best
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Course Progress */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Course Progress Overview
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <Doughnut
                  data={courseCompletionChartData}
                  options={{
                    responsive: true,
                    plugins: {
                      legend: {
                        position: 'bottom' as const,
                      },
                    },
                  }}
                />
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-around', mt: 2 }}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="success.main">
                    {analytics.course_completion.completed}
                  </Typography>
                  <Typography variant="body2">Completed</Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="warning.main">
                    {analytics.course_completion.in_progress}
                  </Typography>
                  <Typography variant="body2">In Progress</Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="text.secondary">
                    {analytics.course_completion.not_started}
                  </Typography>
                  <Typography variant="body2">Not Started</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Assessment Performance Trend */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Assessment Performance
              </Typography>
              {analytics.assessment_performance.recent_performance?.length > 0 ? (
                <Line
                  data={assessmentPerformanceChartData}
                  options={{
                    responsive: true,
                    plugins: {
                      legend: {
                        position: 'top' as const,
                      },
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        max: 100,
                      },
                    },
                  }}
                />
              ) : (
                <Typography color="text.secondary">
                  No recent assessment data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Course Details */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                My Courses
              </Typography>
              <List>
                {analytics.learning_progress.course_progress?.slice(0, 5).map((course) => (
                  <ListItem key={course.course_id}>
                    <ListItemIcon>
                      {course.is_completed ? (
                        <CheckCircle color="success" />
                      ) : course.progress_percentage > 0 ? (
                        <PlayArrow color="warning" />
                      ) : (
                        <Schedule color="disabled" />
                      )}
                    </ListItemIcon>
                    <ListItemText
                      primary={course.course_name}
                      secondary={
                        <Box>
                          <LinearProgress
                            variant="determinate"
                            value={course.progress_percentage}
                            sx={{ mt: 1, mb: 1 }}
                          />
                          <Typography variant="body2" color="text.secondary">
                            {course.progress_percentage.toFixed(1)}% complete
                            {course.last_accessed && (
                              <> • Last accessed: {format(parseISO(course.last_accessed), 'MMM dd')}</>
                            )}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Recommendations */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Personalized Recommendations
              </Typography>
              <List>
                {analytics.recommendations?.slice(0, 5).map((recommendation, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <Lightbulb color={recommendation.priority === 'high' ? 'warning' : 'info'} />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {recommendation.title}
                          <Chip
                            label={recommendation.priority}
                            size="small"
                            color={recommendation.priority === 'high' ? 'error' : 'info'}
                          />
                        </Box>
                      }
                      secondary={recommendation.description}
                    />
                  </ListItem>
                ))}
              </List>
              {analytics.recommendations?.length === 0 && (
                <Typography color="text.secondary">
                  No recommendations available at this time
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Activity Summary */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Weekly Activity Summary
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Total Activities This Week
                  </Typography>
                  <Typography variant="h4" color="primary">
                    {analytics.activity_summary.total_activities_week}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Activity Breakdown
                  </Typography>
                  {Object.entries(analytics.activity_summary.activity_summary).map(([activity, count]) => (
                    <Box key={activity} sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                      <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                        {activity.replace(/_/g, ' ')}
                      </Typography>
                      <Chip label={count} size="small" />
                    </Box>
                  ))}
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Last Updated */}
      {lastUpdated && (
        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Last updated: {format(lastUpdated, 'PPpp')}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default PersonalAnalyticsDashboard;

/**
 * Tests for EmotionalIntelligenceService
 */

import emotionalIntelligenceService from '../emotionalIntelligenceService';
import { EmotionalLearningState } from '../personalizedLearningService';

describe('EmotionalIntelligenceService', () => {
  const mockInteractionData = [
    {
      timestamp: new Date().toISOString(),
      message: 'Hello',
      type: 'user_input',
      response_time: 5000
    },
    {
      timestamp: new Date().toISOString(),
      message: 'I need help with this problem',
      type: 'user_input',
      response_time: 15000
    },
    {
      timestamp: new Date().toISOString(),
      message: 'This is confusing',
      type: 'user_input',
      response_time: 25000
    }
  ];

  describe('detectEmotionalState', () => {
    it('should detect emotional state from interaction data', async () => {
      const userId = 'test-user-123';
      
      const emotionalState = await emotionalIntelligenceService.detectEmotionalState(
        userId,
        mockInteractionData
      );

      expect(emotionalState).toBeDefined();
      expect(emotionalState.current_mood).toMatch(/motivated|frustrated|confident|anxious|curious|neutral/);
      expect(emotionalState.stress_level).toBeGreaterThanOrEqual(1);
      expect(emotionalState.stress_level).toBeLessThanOrEqual(10);
      expect(emotionalState.confidence_level).toBeGreaterThanOrEqual(1);
      expect(emotionalState.confidence_level).toBeLessThanOrEqual(10);
      expect(emotionalState.engagement_level).toBeGreaterThanOrEqual(1);
      expect(emotionalState.engagement_level).toBeLessThanOrEqual(10);
      expect(emotionalState.learning_readiness).toBeGreaterThanOrEqual(1);
      expect(emotionalState.learning_readiness).toBeLessThanOrEqual(10);
      expect(emotionalState.detected_at).toBeDefined();
    });

    it('should return neutral state when detection fails', async () => {
      const userId = 'test-user-123';
      
      // Mock console.warn to avoid noise in tests
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      // Pass invalid data to trigger error
      const emotionalState = await emotionalIntelligenceService.detectEmotionalState(
        userId,
        null as any
      );

      expect(emotionalState.current_mood).toBe('neutral');
      expect(emotionalState.stress_level).toBe(3);
      expect(emotionalState.confidence_level).toBe(5);
      expect(emotionalState.engagement_level).toBe(5);
      expect(emotionalState.learning_readiness).toBe(5);
      
      consoleSpy.mockRestore();
    });
  });

  describe('adaptContentToEmotion', () => {
    it('should simplify content for high stress', async () => {
      const content = 'This is a complex explanation with multiple concepts.';
      const highStressState: EmotionalLearningState = {
        current_mood: 'anxious',
        stress_level: 8,
        confidence_level: 3,
        engagement_level: 4,
        learning_readiness: 3,
        detected_at: new Date().toISOString()
      };

      const adaptedContent = await emotionalIntelligenceService.adaptContentToEmotion(
        content,
        highStressState
      );

      expect(adaptedContent).toContain('🌟 **Simplified explanation:**');
      expect(adaptedContent).toContain('💙');
      expect(adaptedContent).toContain('Take your time');
    });

    it('should add encouragement for low confidence', async () => {
      const content = 'Here is the lesson content.';
      const lowConfidenceState: EmotionalLearningState = {
        current_mood: 'anxious',
        stress_level: 5,
        confidence_level: 2,
        engagement_level: 6,
        learning_readiness: 4,
        detected_at: new Date().toISOString()
      };

      const adaptedContent = await emotionalIntelligenceService.adaptContentToEmotion(
        content,
        lowConfidenceState
      );

      expect(adaptedContent).toContain('🌟');
      expect(adaptedContent).toMatch(/You're making great progress|Keep up the excellent work|You've got this|Every step forward/);
    });

    it('should gamify content for low engagement', async () => {
      const content = 'This is the learning material.';
      const lowEngagementState: EmotionalLearningState = {
        current_mood: 'neutral',
        stress_level: 4,
        confidence_level: 6,
        engagement_level: 3,
        learning_readiness: 5,
        detected_at: new Date().toISOString()
      };

      const adaptedContent = await emotionalIntelligenceService.adaptContentToEmotion(
        content,
        lowEngagementState
      );

      expect(adaptedContent).toContain('🎮 **Learning Challenge:**');
      expect(adaptedContent).toContain('🏆 Complete this to earn progress points!');
      expect(adaptedContent).toContain('💡 **Quick Check:**');
      expect(adaptedContent).toContain('🤔 **Reflection:**');
    });

    it('should add break suggestion for frustrated mood', async () => {
      const content = 'Here is the challenging concept.';
      const frustratedState: EmotionalLearningState = {
        current_mood: 'frustrated',
        stress_level: 6,
        confidence_level: 4,
        engagement_level: 5,
        learning_readiness: 4,
        detected_at: new Date().toISOString()
      };

      const adaptedContent = await emotionalIntelligenceService.adaptContentToEmotion(
        content,
        frustratedState
      );

      expect(adaptedContent).toContain('⏸️ **Feeling stuck?**');
      expect(adaptedContent).toContain('5-minute break');
    });

    it('should add reassurance for anxious mood', async () => {
      const content = 'This is a difficult topic.';
      const anxiousState: EmotionalLearningState = {
        current_mood: 'anxious',
        stress_level: 7,
        confidence_level: 3,
        engagement_level: 4,
        learning_readiness: 3,
        detected_at: new Date().toISOString()
      };

      const adaptedContent = await emotionalIntelligenceService.adaptContentToEmotion(
        content,
        anxiousState
      );

      expect(adaptedContent).toContain('🤗 **Don\'t worry**');
      expect(adaptedContent).toContain('common topic that many students find challenging');
      expect(adaptedContent).toContain('✨ **Remember:** Making mistakes is part of learning!');
    });

    it('should return original content for optimal emotional state', async () => {
      const content = 'This is the learning content.';
      const optimalState: EmotionalLearningState = {
        current_mood: 'motivated',
        stress_level: 3,
        confidence_level: 8,
        engagement_level: 9,
        learning_readiness: 8,
        detected_at: new Date().toISOString()
      };

      const adaptedContent = await emotionalIntelligenceService.adaptContentToEmotion(
        content,
        optimalState
      );

      // Should not add stress-relief or confidence-boosting content
      expect(adaptedContent).toBe(content);
    });
  });

  describe('getEmotionalSupport', () => {
    it('should provide stress relief suggestions for high stress', async () => {
      const highStressState: EmotionalLearningState = {
        current_mood: 'anxious',
        stress_level: 9,
        confidence_level: 4,
        engagement_level: 3,
        learning_readiness: 2,
        detected_at: new Date().toISOString()
      };

      const suggestions = await emotionalIntelligenceService.getEmotionalSupport(highStressState);

      expect(suggestions).toContain('Take a 5-minute break and try some deep breathing');
      expect(suggestions).toContain('Consider breaking this into smaller chunks');
      expect(suggestions).toContain('Remember: it\'s okay to take your time');
    });

    it('should provide confidence boosting suggestions for low confidence', async () => {
      const lowConfidenceState: EmotionalLearningState = {
        current_mood: 'anxious',
        stress_level: 4,
        confidence_level: 2,
        engagement_level: 5,
        learning_readiness: 4,
        detected_at: new Date().toISOString()
      };

      const suggestions = await emotionalIntelligenceService.getEmotionalSupport(lowConfidenceState);

      expect(suggestions).toContain('You\'re doing great! Every expert was once a beginner');
      expect(suggestions).toContain('Try reviewing the basics first');
      expect(suggestions).toContain('Consider asking for help - it\'s a sign of strength');
    });

    it('should provide engagement suggestions for low engagement', async () => {
      const lowEngagementState: EmotionalLearningState = {
        current_mood: 'neutral',
        stress_level: 3,
        confidence_level: 6,
        engagement_level: 3,
        learning_readiness: 5,
        detected_at: new Date().toISOString()
      };

      const suggestions = await emotionalIntelligenceService.getEmotionalSupport(lowEngagementState);

      expect(suggestions).toContain('Try a different learning approach');
      expect(suggestions).toContain('Set a small, achievable goal');
      expect(suggestions).toContain('Reward yourself for progress made');
    });

    it('should return empty array for optimal emotional state', async () => {
      const optimalState: EmotionalLearningState = {
        current_mood: 'motivated',
        stress_level: 2,
        confidence_level: 8,
        engagement_level: 9,
        learning_readiness: 8,
        detected_at: new Date().toISOString()
      };

      const suggestions = await emotionalIntelligenceService.getEmotionalSupport(optimalState);

      expect(suggestions).toHaveLength(0);
    });
  });
});

"""UnifiedBaseCourseSerializersThesebaseserializersconsolidateduplicatecourseserializationlogicacrossall3courseapps.Eachappcanextendtheseforspecificneeds.Thiseliminates:-Duplicatecoursefielddefinitions-Redundantprogresscalculationlogic-InconsistentAPIresponseformats-Multipleenrollmentserializers"""fromrest_frameworkimportserializersfrom django.contrib.authimportget_user_modelfrom django.appsimportappsfrom core.services.unified_course_serviceimportunified_course_serviceUser=get_user_model()classBaseCourseSerializer(serializers.ModelSerializer):"""BaseserializerforCoursemodelwithcommonfieldsandmethodsThisreplacesduplicatecourseserializersin:-courses/serializers.py-interactive_learning/serializers.py-course_generator/serializers.py"""#Commoncalculatedfieldsdepartment_name=serializers.SerializerMethodField()instructor_name=serializers.SerializerMethodField()enrollment_count=serializers.SerializerMethodField()available_spots=serializers.SerializerMethodField()#Integrationflags(unifiedacrossallapps)has_interactive_content=serializers.BooleanField(read_only=True)has_ai_content=serializers.BooleanField(read_only=True)has_assessment=serializers.BooleanField(read_only=True)classMeta:model=None#Setinsubclassesfields=['id''course_code''title''description''credits''semester''academic_year''required_level''recommended_level''capacity''max_students''is_published''is_active''start_date''end_date''department_name''instructor_name''enrollment_count''available_spots''has_interactive_content''has_ai_content''has_assessment''created_at''updated_at']read_only_fields=['created_at''updated_at''enrollment_count''available_spots''has_interactive_content''has_ai_content''has_assessment']defget_department_name(selfobj):"""Getdepartmentname"""returnobj.department.nameifobj.departmentelseNonedefget_instructor_name(selfobj):"""Getinstructorfullname"""ifobj.instructor:returnf"{obj.instructor.first_name}{obj.instructor.last_name}".strip()returnNonedefget_enrollment_count(selfobj):"""Getcurrentenrollmentcount"""try:returnobj.enrollments.filter(status='APPROVED').count()except:return0defget_available_spots(selfobj):"""Getavailableenrollmentspots"""try:enrolled=obj.enrollments.filter(status='APPROVED').count()returnmax(0obj.max_students-enrolled)except:returnobj.max_studentsclassUnifiedCourseSerializer(BaseCourseSerializer):"""UnifiedcourseserializerthatincludesdatafromallappsThisreplacestheneedforseparateserializersineachappbyincludingallcourse-relateddatainoneresponse."""#Extendedfieldsfromunifiedserviceinteractive_features=serializers.SerializerMethodField()ai_content=serializers.SerializerMethodField()enrollment_stats=serializers.SerializerMethodField()classMeta(BaseCourseSerializer.Meta):fields=BaseCourseSerializer.Meta.fields+['interactive_features''ai_content''enrollment_stats']defget_interactive_features(selfobj):"""Getinteractivefeaturesifavailable"""try:course_data=unified_course_service.get_unified_course_data(obj.course_code)returncourse_data.get('interactive_features')except:returnNonedefget_ai_content(selfobj):"""GetAIcontentifavailable"""try:course_data=unified_course_service.get_unified_course_data(obj.course_code)returncourse_data.get('ai_content')except:returnNonedefget_enrollment_stats(selfobj):"""Getenrollmentstatistics"""try:course_data=unified_course_service.get_unified_course_data(obj.course_code)returncourse_data.get('enrollment_stats')except:return{}classBaseCourseProgressSerializer(serializers.ModelSerializer):"""BaseserializerforcourseprogressacrossallappsThisconsolidatesprogresstrackinglogicfrom:-courses.CourseProgress-interactive_learning.StudentInteractiveProgress-AIcontentinteractiontracking"""#Commonprogressfieldscourse_code=serializers.SerializerMethodField()course_title=serializers.SerializerMethodField()completion_percentage=serializers.FloatField(read_only=True)last_activity=serializers.DateTimeField(read_only=True)#Progressbreakdownmain_progress=serializers.SerializerMethodField()interactive_progress=serializers.SerializerMethodField()ai_interaction=serializers.SerializerMethodField()classMeta:model=None#Setinsubclassesfields=['id''course_code''course_title''completion_percentage''last_activity''main_progress''interactive_progress''ai_interaction''created_at''updated_at']read_only_fields=['created_at''updated_at''main_progress''interactive_progress''ai_interaction']defget_course_code(selfobj):"""Getcoursecode"""returnobj.course.course_codeifhasattr(obj'course')andobj.courseelseNonedefget_course_title(selfobj):"""Getcoursetitle"""returnobj.course.titleifhasattr(obj'course')andobj.courseelseNonedefget_main_progress(selfobj):"""Getmaincourseprogress"""try:ifhasattr(obj'course')andhasattr(obj'user'):progress_data=unified_course_service.get_student_unified_progress(user=obj.usercourse_code=obj.course.course_code)returnprogress_data.get('main_progress'{})except:passreturn{}defget_interactive_progress(selfobj):"""Getinteractivelearningprogress"""try:ifhasattr(obj'course')andhasattr(obj'user'):progress_data=unified_course_service.get_student_unified_progress(user=obj.usercourse_code=obj.course.course_code)returnprogress_data.get('interactive_progress')except:passreturnNonedefget_ai_interaction(selfobj):"""GetAIcontentinteractiondata"""try:ifhasattr(obj'course')andhasattr(obj'user'):progress_data=unified_course_service.get_student_unified_progress(user=obj.usercourse_code=obj.course.course_code)returnprogress_data.get('ai_interaction')except:passreturnNoneclassBaseEnrollmentSerializer(serializers.ModelSerializer):"""BaseserializerforcourseenrollmentThisconsolidatesenrollmentlogicfrommultipleappsandprovidesunifiedenrollmentinterface."""#Coursedetailscourse_details=BaseCourseSerializer(source='course'read_only=True)student_name=serializers.SerializerMethodField()#Enrollmentstatuscan_enroll=serializers.SerializerMethodField()enrollment_message=serializers.SerializerMethodField()classMeta:model=None#Setinsubclassesfields=['id''user''course''course_details''student_name''status''enrollment_type''enrollment_date''completion_date''can_enroll''enrollment_message''created_at''updated_at']read_only_fields=['created_at''updated_at''course_details''student_name''can_enroll''enrollment_message']defget_student_name(selfobj):"""Getstudentfullname"""ifobj.user:returnf"{obj.user.first_name}{obj.user.last_name}".strip()returnNonedefget_can_enroll(selfobj):"""Checkifenrollmentispossible"""try:#Checkcapacityifobj.course.max_students<=obj.course.enrollments.filter(status='APPROVED').count():returnFalse#Checkifalreadyenrolledifobj.statusin['APPROVED''PENDING']:returnFalse#Checkcourserequirementsifobj.course.required_level>getattr(obj.user'current_level'1):returnFalsereturnTrueexcept:returnFalsedefget_enrollment_message(selfobj):"""Getenrollmentstatusmessage"""ifnotself.get_can_enroll(obj):ifobj.status=='APPROVED':return"Alreadyenrolled"elifobj.status=='PENDING':return"Enrollmentpendingapproval"elifobj.course.max_students<=obj.course.enrollments.filter(status='APPROVED').count():return"Courseisfull"elifobj.course.required_level>getattr(obj.user'current_level'1):return"Levelrequirementnotmet"return"Canenroll"classUnifiedEnrollmentSerializer(BaseEnrollmentSerializer):"""UnifiedenrollmentserializerwithprogresstrackingThisprovidescompleteenrollmentdataincludingprogressacrossallcoursefeatures."""#Progressdataprogress_data=serializers.SerializerMethodField()classMeta(BaseEnrollmentSerializer.Meta):fields=BaseEnrollmentSerializer.Meta.fields+['progress_data']defget_progress_data(selfobj):"""Getunifiedprogressdataforthisenrollment"""try:ifobj.status=='APPROVED':returnunified_course_service.get_student_unified_progress(user=obj.usercourse_code=obj.course.course_code)except:passreturnNone#ConveniencefunctiontogetthecorrectCoursemodeldefget_course_model():"""GettheCoursemodeldynamically"""returnapps.get_model('courses''Course')defget_enrollment_model():"""GettheEnrollmentmodeldynamically"""returnapps.get_model('courses''Enrollment')defget_course_progress_model():"""GettheCourseProgressmodeldynamically"""returnapps.get_model('courses''CourseProgress')#SetMeta.modelforbaseserializersBaseCourseSerializer.Meta.model=get_course_model()UnifiedCourseSerializer.Meta.model=get_course_model()BaseEnrollmentSerializer.Meta.model=get_enrollment_model()UnifiedEnrollmentSerializer.Meta.model=get_enrollment_model()BaseCourseProgressSerializer.Meta.model=get_course_progress_model()
# Temporarily disable this file due to missing models
# from django.contrib import admin
# from .models import CourseVisibilitySettings, StudentCoursePreference

# @admin.register(CourseVisibilitySettings)
# class CourseVisibilitySettingsAdmin(admin.ModelAdmin):
#     list_display = (
#         'courses_enabled',
#         'show_standard_courses',
#         'show_interactive_courses',
#         'show_ai_generated_courses',
#         'default_course_type',
#         'allow_student_choice',
#         'force_course_type'
#     )
#     list_filter = (
#         'courses_enabled',
#         'show_standard_courses',
#         'show_interactive_courses',
#         'show_ai_generated_courses',
#         'default_course_type',
#         'allow_student_choice',
#         'force_course_type'
#     )
#     search_fields = ('default_course_type',)
#     readonly_fields = ('created_at', 'updated_at', 'updated_by')
#     
#     def save_model(self, request, obj, form, change):
#         obj.updated_by = request.user  # Track who updated the settings
#         super().save_model(request, obj, form, change)

# @admin.register(StudentCoursePreference)
# class StudentCoursePreferenceAdmin(admin.ModelAdmin):
#     list_display = (
#         'student',
#         'preferred_course_type',
#         'prefer_gamification',
#         'prefer_ai_personalization'
#     )
#     list_filter = (
#         'preferred_course_type',
#         'prefer_gamification',
#         'prefer_ai_personalization'
#     )
#     search_fields = (
#         'student__username',
#         'student__email',
#         'preferred_course_type'
#     )

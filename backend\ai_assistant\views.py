from django.utils.decoratorsimportmethod_decoratorfrom django.views.decorators.csrfimportcsrf_exemptfromrest_frameworkimportstatusfromrest_framework.decoratorsimportapi_viewpermission_classesfromrest_framework.permissionsimportIsAuthenticatedfromrest_framework.responseimportResponsefromrest_framework.viewsimportAPIViewfrom core.api_responseimportcreate_error_responsecreate_responsefrom.serializersimport(AIAnswerSerializerAIAssistantSuggestionSerializerAIQuestionSerializer)from.servicesimportai_assistant_serviceclassAIAssistantSuggestionsView(APIView):"""APIviewforgettingAIassistantsuggestions"""permission_classes=[IsAuthenticated]defget(selfrequest):"""GetAIassistantsuggestions"""try:user_type=getattr(request.user"user_type""all")suggestions=ai_assistant_service.get_suggestions(user=request.useruser_type=user_type)returncreate_response(data={"suggestions":suggestions}message="Suggestionsretrievedsuccessfully")exceptExceptionase:returncreate_error_response(message=f"Errorretrievingsuggestions:{str(e)}"status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)classAIAssistantAskView(APIView):"""APIviewforaskingquestionstotheAIassistant"""permission_classes=[IsAuthenticated]defpost(selfrequest):"""ProcessaquestionandreturnanAI-generatedanswer"""try:serializer=AIQuestionSerializer(data=request.data)ifnotserializer.is_valid():returncreate_error_response(message="Invalidquestionformat"errors=serializer.errorsstatus_code=status.HTTP_400_BAD_REQUEST)question=serializer.validated_data["question"]context=serializer.validated_data.get("context"{})session_id=serializer.validated_data.get("session_id")#Addusercontextcontext.update({"user_id":request.user.id"user_type":getattr(request.user"user_type""unknown")"username":request.user.username})result=ai_assistant_service.ask_question(question=questionuser=request.usercontext=contextsession_id=session_id)returncreate_response(data=resultmessage="Questionprocessedsuccessfully")exceptExceptionase:returncreate_error_response(message=f"Errorprocessingquestion:{str(e)}"status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(["GET"])@permission_classes([IsAuthenticated])defget_ai_suggestions(request):"""GetAIassistantsuggestions(function-basedviewforcompatibility)"""try:user_type=getattr(request.user"user_type""all")suggestions=ai_assistant_service.get_suggestions(user=request.useruser_type=user_type)returnResponse({"status":"success""suggestions":suggestions})exceptExceptionase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(["POST"])@permission_classes([IsAuthenticated])defask_ai_question(request):"""AskaquestiontotheAIassistant(function-basedviewforcompatibility)"""try:serializer=AIQuestionSerializer(data=request.data)ifnotserializer.is_valid():returnResponse({"status":"error""message":"Invalidquestionformat""errors":serializer.errors}status=status.HTTP_400_BAD_REQUEST)question=serializer.validated_data["question"]context=serializer.validated_data.get("context"{})session_id=serializer.validated_data.get("session_id")#Addusercontextcontext.update({"user_id":request.user.id"user_type":getattr(request.user"user_type""unknown")"username":request.user.username})result=ai_assistant_service.ask_question(question=questionuser=request.usercontext=contextsession_id=session_id)returnResponse({"status":"success""data":result})exceptExceptionase:returnResponse({"status":"error""message":str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)
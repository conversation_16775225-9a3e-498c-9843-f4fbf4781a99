/**
 * Base AI Service Class
 *
 * Standardized base class for all frontend AI services to ensure consistency
 * and eliminate code duplication across different AI service implementations.
 */

import {
  makeAIRequest,
  handleAIServiceError,
  createFallbackResponse,
  AIServiceConfig,
  AIServiceError,
  StandardizedAIResponse,
} from './aiServiceUtils';

export interface BaseAIServiceOptions {
  serviceName: string;
  baseEndpoint: string;
  config?: AIServiceConfig;
  fallbackEnabled?: boolean;
}

export abstract class BaseAIService {
  protected serviceName: string;
  protected baseEndpoint: string;
  protected config: AIServiceConfig;
  protected fallbackEnabled: boolean;

  constructor(options: BaseAIServiceOptions) {
    this.serviceName = options.serviceName;
    this.baseEndpoint = options.baseEndpoint;
    this.config = {
      timeout: 30000,
      retries: 2,
      fallbackEnabled: true,
      cacheEnabled: false,
      cacheTTL: 300000,
      ...options.config,
    };
    // Sync fallbackEnabled with config
    this.fallbackEnabled = this.config.fallbackEnabled ?? true;
  }

  /**
   * Make a standardized GET request
   */
  protected async get<T = any>(
    endpoint: string,
    params?: any,
    config?: Partial<AIServiceConfig>
  ): Promise<T> {
    const fullEndpoint = this.buildEndpoint(endpoint);
    const requestConfig = { ...this.config, ...config };

    try {
      return await makeAIRequest<T>(
        'GET',
        fullEndpoint,
        params,
        requestConfig,
        this.serviceName
      );
    } catch (error) {
      return this.handleServiceError(error, 'GET', endpoint);
    }
  }

  /**
   * Make a standardized POST request
   */
  protected async post<T = any>(
    endpoint: string,
    data?: any,
    config?: Partial<AIServiceConfig>
  ): Promise<T> {
    const fullEndpoint = this.buildEndpoint(endpoint);
    const requestConfig = { ...this.config, ...config };

    try {
      return await makeAIRequest<T>(
        'POST',
        fullEndpoint,
        data,
        requestConfig,
        this.serviceName
      );
    } catch (error) {
      return this.handleServiceError(error, 'POST', endpoint, data);
    }
  }

  /**
   * Make a standardized PUT request
   */
  protected async put<T = any>(
    endpoint: string,
    data?: any,
    config?: Partial<AIServiceConfig>
  ): Promise<T> {
    const fullEndpoint = this.buildEndpoint(endpoint);
    const requestConfig = { ...this.config, ...config };

    try {
      return await makeAIRequest<T>(
        'PUT',
        fullEndpoint,
        data,
        requestConfig,
        this.serviceName
      );
    } catch (error) {
      return this.handleServiceError(error, 'PUT', endpoint, data);
    }
  }

  /**
   * Make a standardized DELETE request
   */
  protected async delete<T = any>(
    endpoint: string,
    config?: Partial<AIServiceConfig>
  ): Promise<T> {
    const fullEndpoint = this.buildEndpoint(endpoint);
    const requestConfig = { ...this.config, ...config };

    try {
      return await makeAIRequest<T>(
        'DELETE',
        fullEndpoint,
        undefined,
        requestConfig,
        this.serviceName
      );
    } catch (error) {
      return this.handleServiceError(error, 'DELETE', endpoint);
    }
  }

  /**
   * Make a standardized PATCH request
   */
  protected async patch<T = any>(
    endpoint: string,
    data?: any,
    config?: Partial<AIServiceConfig>
  ): Promise<T> {
    const fullEndpoint = this.buildEndpoint(endpoint);
    const requestConfig = { ...this.config, ...config };

    try {
      return await makeAIRequest<T>(
        'PATCH',
        fullEndpoint,
        data,
        requestConfig,
        this.serviceName
      );
    } catch (error) {
      return this.handleServiceError(error, 'PATCH', endpoint, data);
    }
  }

  /**
   * Build full endpoint URL
   */
  private buildEndpoint(endpoint: string): string {
    // Remove leading slash from endpoint if present
    const cleanEndpoint = endpoint.startsWith('/')
      ? endpoint.slice(1)
      : endpoint;

    // Ensure base endpoint ends with slash
    const baseWithSlash = this.baseEndpoint.endsWith('/')
      ? this.baseEndpoint
      : `${this.baseEndpoint}/`;

    return `${baseWithSlash}${cleanEndpoint}`;
  }

  /**
   * Handle service errors with fallback support
   */
  private handleServiceError<T = any>(
    error: any,
    method: string,
    endpoint: string,
    data?: any
  ): T {
    console.error(`${this.serviceName} ${method} ${endpoint} failed:`, error);

    // If fallback is enabled and this is a specific type of error, return fallback
    if (this.fallbackEnabled && this.shouldUseFallback(error)) {
      const fallbackType = this.determineFallbackType(endpoint);
      return createFallbackResponse<T>(fallbackType);
    }

    // Otherwise, re-throw the error
    throw error;
  }

  /**
   * Determine if fallback should be used for this error
   */
  private shouldUseFallback(error: any): boolean {
    // Use fallback for server errors, timeouts, and connection issues
    return (
      error instanceof AIServiceError &&
      (error.code === 'SERVER_ERROR' ||
        error.code === 'TIMEOUT_ERROR' ||
        error.code === 'CONNECTION_ERROR' ||
        error.statusCode === 503 ||
        error.statusCode === 500)
    );
  }

  /**
   * Determine fallback type based on endpoint
   */
  private determineFallbackType(
    endpoint: string
  ): 'general' | 'suggestions' | 'content' | 'analysis' {
    if (endpoint.includes('suggestion')) return 'suggestions';
    if (endpoint.includes('content') || endpoint.includes('generate'))
      return 'content';
    if (endpoint.includes('analyz') || endpoint.includes('predict'))
      return 'analysis';
    return 'general';
  }

  /**
   * Get service health status
   */
  public async getHealthStatus(): Promise<{
    status: string;
    service: string;
    timestamp: number;
  }> {
    try {
      // Try a simple health check endpoint
      await this.get('health/', undefined, { timeout: 5000, retries: 0 });
      return {
        status: 'healthy',
        service: this.serviceName,
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        service: this.serviceName,
        timestamp: Date.now(),
      };
    }
  }

  /**
   * Update service configuration
   */
  public updateConfig(newConfig: Partial<AIServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current service configuration
   */
  public getConfig(): AIServiceConfig {
    // Return the current config with up-to-date fallbackEnabled value
    return { ...this.config, fallbackEnabled: this.fallbackEnabled };
  }

  /**
   * Enable or disable fallback responses
   */
  public setFallbackEnabled(enabled: boolean): void {
    this.fallbackEnabled = enabled;
    // Also update the config object to maintain sync
    this.config = { ...this.config, fallbackEnabled: enabled };
  }

  /**
   * Get service information
   */
  public getServiceInfo(): {
    name: string;
    endpoint: string;
    config: AIServiceConfig;
  } {
    return {
      name: this.serviceName,
      endpoint: this.baseEndpoint,
      config: this.getConfig(),
    };
  }
}

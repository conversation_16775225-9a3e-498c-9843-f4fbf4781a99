from django.urlsimportincludepathfromrest_framework.routersimportDefaultRouterfrom.progression_viewsimport(CompetencyBadgeViewSetLearningPathwayViewSetProgressionMilestoneViewSetSkillProgressViewSetStudentBadgesViewStudentLearningPathViewStudentLevelViewSetStudentMilestonesView)app_name="assessment-progression"router=DefaultRouter()#Studentprogressionroutesrouter.register(r"levels"StudentLevelViewSetbasename="level")router.register(r"skills"SkillProgressViewSetbasename="skill")router.register(r"milestones"ProgressionMilestoneViewSetbasename="milestone")router.register(r"pathways"LearningPathwayViewSetbasename="pathway")router.register(r"badges"CompetencyBadgeViewSetbasename="badge")urlpatterns=[#IncluderouterURLspath(""include((router.urlsapp_name)))#Studentprogressionendpointspath("current-level/"StudentLevelViewSet.as_view({"get":"current_level"})name="current-level")path("skill-gaps/"SkillProgressViewSet.as_view({"get":"skill_gaps"})name="skill-gaps")#Achievementtrackingpath("achievements/"StudentBadgesView.as_view()name="achievements")path("next-milestone/"ProgressionMilestoneViewSet.as_view({"get":"next_milestone"})name="next-milestone")#Learningpathwayspath("recommended-pathways/"LearningPathwayViewSet.as_view({"get":"recommended"})name="recommended-pathways")path("pathway-progress/<int:pk>/"LearningPathwayViewSet.as_view({"get":"progress"})name="pathway-progress")#Studentviewspath("learning-path/"StudentLearningPathView.as_view()name="learning-path")path("milestones/"StudentMilestonesView.as_view()name="milestones")]
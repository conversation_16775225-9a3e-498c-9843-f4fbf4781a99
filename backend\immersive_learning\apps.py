from django.apps import AppConfig


class ImmersiveLearningConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'immersive_learning'
    verbose_name = 'Immersive Learning (VR/AR)'
    
    def ready(self):
        """Initialize immersive learning signals and services"""
        try:
            import immersive_learning.signals
        except ImportError:
            pass

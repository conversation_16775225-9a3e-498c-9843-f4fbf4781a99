/**
 * ✅ Prerequisites Checking Component
 * 
 * Features:
 * - Check course eligibility for individual courses
 * - Batch prerequisite checking for multiple courses
 * - Detailed missing requirement information
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  TextField,
  Autocomplete,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  CircularProgress,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  CheckCircle,
  Cancel,
  Warning,
  Search,
  PlaylistAddCheck,
  School,
  Grade,
  Person,
  Assignment
} from '@mui/icons-material';
import { academicService, PrerequisiteCheck } from '../../services/academicService';

interface PrerequisitesCheckerProps {
  studentId?: number;
  courseId?: number;
}

const PrerequisitesChecker: React.FC<PrerequisitesCheckerProps> = ({ 
  studentId, 
  courseId 
}) => {
  const [selectedCourseId, setSelectedCourseId] = useState<number>(courseId || 0);
  const [selectedStudentId, setSelectedStudentId] = useState<number>(studentId || 0);
  const [prerequisiteCheck, setPrerequisiteCheck] = useState<PrerequisiteCheck | null>(null);
  const [batchResults, setBatchResults] = useState<PrerequisiteCheck[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [batchCourseIds, setBatchCourseIds] = useState<string>('');
  const [showBatchDialog, setShowBatchDialog] = useState(false);

  const handleSingleCheck = async () => {
    if (!selectedCourseId) {
      setError('Please select a course to check');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const result = await academicService.checkPrerequisites(
        selectedCourseId, 
        selectedStudentId || undefined
      );
      setPrerequisiteCheck(result);
    } catch (err: any) {
      setError(err.message || 'Failed to check prerequisites');
    } finally {
      setLoading(false);
    }
  };

  const handleBatchCheck = async () => {
    const courseIds = batchCourseIds
      .split(',')
      .map(id => parseInt(id.trim()))
      .filter(id => !isNaN(id));

    if (courseIds.length === 0) {
      setError('Please enter valid course IDs');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const results = await academicService.batchCheckPrerequisites(
        courseIds,
        selectedStudentId || undefined
      );
      setBatchResults(results);
      setShowBatchDialog(false);
    } catch (err: any) {
      setError(err.message || 'Failed to perform batch check');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (eligible: boolean) => {
    return eligible ? (
      <CheckCircle color="success" />
    ) : (
      <Cancel color="error" />
    );
  };

  const getStatusColor = (eligible: boolean) => {
    return eligible ? 'success' : 'error';
  };

  const getRequirementTypeIcon = (type: string) => {
    switch (type) {
      case 'COURSE':
        return <School />;
      case 'GPA':
        return <Grade />;
      case 'CREDITS':
        return <Assignment />;
      case 'STANDING':
        return <Person />;
      default:
        return <Warning />;
    }
  };

  const getRequirementTypeColor = (type: string) => {
    switch (type) {
      case 'COURSE':
        return 'primary';
      case 'GPA':
        return 'warning';
      case 'CREDITS':
        return 'info';
      case 'STANDING':
        return 'secondary';
      default:
        return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CheckCircle color="primary" />
          Prerequisites Checking
        </Typography>
        <Button
          variant="outlined"
          startIcon={<PlaylistAddCheck />}
          onClick={() => setShowBatchDialog(true)}
        >
          Batch Check
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Single Course Check */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Individual Course Check
          </Typography>
          
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Course ID"
                type="number"
                value={selectedCourseId || ''}
                onChange={(e) => setSelectedCourseId(parseInt(e.target.value) || 0)}
                placeholder="Enter course ID"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Student ID (optional)"
                type="number"
                value={selectedStudentId || ''}
                onChange={(e) => setSelectedStudentId(parseInt(e.target.value) || 0)}
                placeholder="Current user if empty"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Button
                fullWidth
                variant="contained"
                startIcon={<Search />}
                onClick={handleSingleCheck}
                disabled={loading}
                sx={{ height: '56px' }}
              >
                {loading ? <CircularProgress size={24} /> : 'Check Prerequisites'}
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Single Check Results */}
      {prerequisiteCheck && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box display="flex" alignItems="center" gap={2} mb={2}>
              {getStatusIcon(prerequisiteCheck.eligible)}
              <Typography variant="h6">
                Course Eligibility Result
              </Typography>
              <Chip
                label={prerequisiteCheck.eligible ? 'Eligible' : 'Not Eligible'}
                color={getStatusColor(prerequisiteCheck.eligible)}
              />
            </Box>

            <Typography variant="body2" color="text.secondary" gutterBottom>
              Course ID: {prerequisiteCheck.course_id} | Student ID: {prerequisiteCheck.student_id}
            </Typography>

            {prerequisiteCheck.missing_prerequisites.length > 0 && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="h6" color="error" gutterBottom>
                  Missing Prerequisites
                </Typography>
                <List>
                  {prerequisiteCheck.missing_prerequisites.map((req, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        {getRequirementTypeIcon(req.type)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Chip
                              label={req.type}
                              color={getRequirementTypeColor(req.type)}
                              size="small"
                            />
                            <Typography>{req.requirement}</Typography>
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2">
                              Required: {req.required_value}
                            </Typography>
                            {req.current_value && (
                              <Typography variant="body2" color="text.secondary">
                                Current: {req.current_value}
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}

            {prerequisiteCheck.warnings.length > 0 && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="h6" color="warning.main" gutterBottom>
                  Warnings
                </Typography>
                <List>
                  {prerequisiteCheck.warnings.map((warning, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <Warning color="warning" />
                      </ListItemIcon>
                      <ListItemText primary={warning} />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}

            <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
              Checked at: {new Date(prerequisiteCheck.checked_at).toLocaleString()}
            </Typography>
          </CardContent>
        </Card>
      )}

      {/* Batch Results */}
      {batchResults.length > 0 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Batch Check Results
            </Typography>
            
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Course ID</TableCell>
                    <TableCell align="center">Eligible</TableCell>
                    <TableCell align="center">Missing Prerequisites</TableCell>
                    <TableCell align="center">Warnings</TableCell>
                    <TableCell>Checked At</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {batchResults.map((result, index) => (
                    <TableRow key={index}>
                      <TableCell>{result.course_id}</TableCell>
                      <TableCell align="center">
                        <Chip
                          icon={getStatusIcon(result.eligible)}
                          label={result.eligible ? 'Yes' : 'No'}
                          color={getStatusColor(result.eligible)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={result.missing_prerequisites.length}
                          color={result.missing_prerequisites.length === 0 ? 'success' : 'error'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={result.warnings.length}
                          color={result.warnings.length === 0 ? 'default' : 'warning'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {new Date(result.checked_at).toLocaleDateString()}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {/* Batch Check Dialog */}
      <Dialog open={showBatchDialog} onClose={() => setShowBatchDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Batch Prerequisites Check</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Course IDs"
              multiline
              rows={4}
              value={batchCourseIds}
              onChange={(e) => setBatchCourseIds(e.target.value)}
              placeholder="Enter course IDs separated by commas (e.g., 1, 2, 3, 4)"
              helperText="Enter multiple course IDs separated by commas to check prerequisites for all of them at once"
            />
            
            <TextField
              fullWidth
              label="Student ID (optional)"
              type="number"
              value={selectedStudentId || ''}
              onChange={(e) => setSelectedStudentId(parseInt(e.target.value) || 0)}
              placeholder="Leave empty to use current user"
              sx={{ mt: 2 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowBatchDialog(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            onClick={handleBatchCheck}
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : 'Check All'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PrerequisitesChecker;

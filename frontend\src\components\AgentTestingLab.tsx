import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON>graphy,
  TextField,
  Button,
  Grid,
  Paper,
  Chip,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  CircularProgress,
} from '@mui/material';
import {
  Send as SendIcon,
  Science as TestIcon,
  Psychology as BrainIcon,
  Route as RouteIcon,
} from '@mui/icons-material';
import multiAgentService from '../services/multiAgentService';

// 🧪 Agent Testing Lab - Interactive testing of AI agent routing and responses

interface TestResult {
  type: 'routing' | 'response';
  input: string;
  output: any;
  timestamp: Date;
  success: boolean;
}

const AgentTestingLab: React.FC = () => {
  const [testMessage, setTestMessage] = useState('');
  const [selectedRole, setSelectedRole] = useState('STUDENT');
  const [selectedAgent, setSelectedAgent] = useState('math_tutor');
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [loading, setLoading] = useState(false);

  const roles = [
    { code: 'STUDENT', name: 'Student', icon: '🎓' },
    { code: 'PROFESSOR', name: 'Professor', icon: '👨‍🏫' },
    { code: 'ADMIN', name: 'Admin', icon: '👑' },
  ];

  const agentTypes = [
    { code: 'math_tutor', name: 'Math Tutor', icon: '🔢' },
    { code: 'science_tutor', name: 'Science Tutor', icon: '🔬' },
    { code: 'language_tutor', name: 'Language Tutor', icon: '📝' },
    { code: 'advisor', name: 'Career Advisor', icon: '🎯' },
    { code: 'assessor', name: 'Assessor', icon: '📊' },
    { code: 'content_creator', name: 'Content Creator', icon: '✨' },
    { code: 'tutor', name: 'General Tutor', icon: '🎓' },
  ];

  const sampleMessages = {
    STUDENT: [
      'Help me understand quadratic equations',
      'Explain photosynthesis process',
      'How do I write a better essay?',
      'What career path should I choose?',
      'I need help with calculus derivatives',
    ],
    PROFESSOR: [
      'Create a biology quiz for high school students',
      'Generate lesson content on cellular respiration',
      'Design an assessment rubric for essays',
      'Help me plan a chemistry lab experiment',
      'Create practice problems for algebra',
    ],
    ADMIN: [
      'Analyze system-wide learning patterns',
      'Create institutional assessment strategy',
      'Generate performance analytics report',
      'Plan curriculum improvement initiatives',
      'Monitor student engagement metrics',
    ],
  };

  const testAgentRouting = async () => {
    if (!testMessage.trim()) return;

    setLoading(true);
    try {
      // Test local prediction first
      const localPrediction = multiAgentService.predictAgent(
        testMessage,
        selectedRole
      );

      // Try backend routing test
      let backendResult = null;
      try {
        const response = await multiAgentService.testAgentRouting({
          message: testMessage,
          context: { user_role: selectedRole },
        });
        backendResult = response.data;
      } catch (error) {
        console.log('Backend test failed, using local prediction');
      }

      const result: TestResult = {
        type: 'routing',
        input: testMessage,
        output: {
          local_prediction: localPrediction,
          backend_result: backendResult,
          role: selectedRole,
        },
        timestamp: new Date(),
        success: true,
      };

      setTestResults(prev => [result, ...prev.slice(0, 9)]); // Keep last 10 results
    } catch (error) {
      const result: TestResult = {
        type: 'routing',
        input: testMessage,
        output: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        timestamp: new Date(),
        success: false,
      };
      setTestResults(prev => [result, ...prev.slice(0, 9)]);
    } finally {
      setLoading(false);
    }
  };

  const testAgentResponse = async () => {
    if (!testMessage.trim()) return;

    setLoading(true);
    try {
      // Try backend agent test
      let backendResult = null;
      try {
        const response = await multiAgentService.testAgentResponse({
          agent_type: selectedAgent,
          message: testMessage,
          context: { user_role: selectedRole },
        });
        backendResult = response.data;
      } catch (error) {
        console.log('Backend agent test failed');
      }

      const result: TestResult = {
        type: 'response',
        input: testMessage,
        output: {
          agent_type: selectedAgent,
          backend_result: backendResult,
          role: selectedRole,
          mock_response: `Mock response from ${selectedAgent} for: "${testMessage}"`,
        },
        timestamp: new Date(),
        success: true,
      };

      setTestResults(prev => [result, ...prev.slice(0, 9)]);
    } catch (error) {
      const result: TestResult = {
        type: 'response',
        input: testMessage,
        output: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        timestamp: new Date(),
        success: false,
      };
      setTestResults(prev => [result, ...prev.slice(0, 9)]);
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant='h4' gutterBottom>
        🧪 Agent Testing Lab
      </Typography>

      <Alert severity='info' sx={{ mb: 3 }}>
        Test how different messages are routed to AI agents and see their
        responses. This helps you understand the multi-agent system behavior.
      </Alert>

      <Grid container spacing={3}>
        {/* Testing Controls */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant='h6' gutterBottom>
                🎯 Test Configuration
              </Typography>

              {/* Role Selection */}
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>User Role</InputLabel>
                <Select
                  value={selectedRole}
                  label='User Role'
                  onChange={e => setSelectedRole(e.target.value)}
                >
                  {roles.map(role => (
                    <MenuItem key={role.code} value={role.code}>
                      {role.icon} {role.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Agent Selection for Direct Testing */}
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Target Agent (for direct testing)</InputLabel>
                <Select
                  value={selectedAgent}
                  label='Target Agent'
                  onChange={e => setSelectedAgent(e.target.value)}
                >
                  {agentTypes.map(agent => (
                    <MenuItem key={agent.code} value={agent.code}>
                      {agent.icon} {agent.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Test Message Input */}
              <TextField
                fullWidth
                multiline
                rows={3}
                label='Test Message'
                value={testMessage}
                onChange={e => setTestMessage(e.target.value)}
                placeholder='Enter a message to test agent routing...'
                sx={{ mb: 2 }}
              />

              {/* Test Buttons */}
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <Button
                  variant='contained'
                  startIcon={
                    loading ? <CircularProgress size={16} /> : <RouteIcon />
                  }
                  onClick={testAgentRouting}
                  disabled={loading || !testMessage.trim()}
                  color='primary'
                >
                  Test Routing
                </Button>
                <Button
                  variant='outlined'
                  startIcon={
                    loading ? <CircularProgress size={16} /> : <SendIcon />
                  }
                  onClick={testAgentResponse}
                  disabled={loading || !testMessage.trim()}
                  color='secondary'
                >
                  Test Response
                </Button>
              </Box>

              {/* Sample Messages */}
              <Typography variant='subtitle2' gutterBottom>
                Sample Messages for{' '}
                {roles.find(r => r.code === selectedRole)?.name}:
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {sampleMessages[
                  selectedRole as keyof typeof sampleMessages
                ]?.map((sample, index) => (
                  <Chip
                    key={index}
                    label={sample}
                    onClick={() => setTestMessage(sample)}
                    variant='outlined'
                    size='small'
                    clickable
                  />
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Test Results */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 2,
                }}
              >
                <Typography variant='h6'>📊 Test Results</Typography>
                <Button
                  size='small'
                  onClick={clearResults}
                  disabled={testResults.length === 0}
                >
                  Clear
                </Button>
              </Box>

              {testResults.length === 0 ? (
                <Alert severity='info'>
                  No test results yet. Run some tests to see the results here.
                </Alert>
              ) : (
                <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
                  {testResults.map((result, index) => (
                    <Paper
                      key={index}
                      sx={{
                        p: 2,
                        mb: 2,
                        bgcolor: result.success
                          ? 'success.light'
                          : 'error.light',
                        opacity: 0.9,
                      }}
                    >
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', mb: 1 }}
                      >
                        <Chip
                          icon={
                            result.type === 'routing' ? (
                              <RouteIcon />
                            ) : (
                              <BrainIcon />
                            )
                          }
                          label={
                            result.type === 'routing'
                              ? 'Routing Test'
                              : 'Response Test'
                          }
                          size='small'
                          color={result.success ? 'success' : 'error'}
                        />
                        <Typography variant='caption' sx={{ ml: 1 }}>
                          {result.timestamp.toLocaleTimeString()}
                        </Typography>
                      </Box>

                      <Typography
                        variant='body2'
                        sx={{ fontWeight: 'bold', mb: 1 }}
                      >
                        Input: "{result.input}"
                      </Typography>

                      {result.success ? (
                        <Box>
                          {result.type === 'routing' &&
                            result.output.local_prediction && (
                              <Typography variant='body2'>
                                🎯 Predicted Agent:{' '}
                                {result.output.local_prediction.icon}{' '}
                                {result.output.local_prediction.name}
                                <br />
                                💭 Reasoning:{' '}
                                {result.output.local_prediction.reasoning}
                              </Typography>
                            )}
                          {result.type === 'response' && (
                            <Typography variant='body2'>
                              🤖 Agent:{' '}
                              {
                                agentTypes.find(
                                  a => a.code === result.output.agent_type
                                )?.icon
                              }{' '}
                              {
                                agentTypes.find(
                                  a => a.code === result.output.agent_type
                                )?.name
                              }
                              <br />
                              💬 Response: {result.output.mock_response}
                            </Typography>
                          )}
                        </Box>
                      ) : (
                        <Typography variant='body2' color='error'>
                          ❌ Error: {result.output.error}
                        </Typography>
                      )}
                    </Paper>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AgentTestingLab;

"""SyrianDialectAPIViewsProvidesendpointsforSyriandialectAIfunctionality"""fromrest_framework.viewsimportAPIViewfromrest_framework.responseimportResponsefromrest_frameworkimportstatusfrom django.httpimportJsonResponsefrom.ai.servicesimportget_ai_serviceimport loggingimportyamlimport oslogger=logging.getLogger(__name__)classSyrianDialectConfigView(APIView):"""APIendpointforSyriandialectconfiguration"""defget(selfrequest):"""GetSyriandialectconfiguration"""try:ai_service=get_ai_service()config=ai_service.syrian_configreturnResponse({'success':True'config':config'message':'Syriandialectconfigurationretrievedsuccessfully'})exceptExceptionase:logger.error(f"ErrorretrievingSyriandialectconfig:{e}")returnResponse({'success':False'error':'Failedtoretrieveconfiguration'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classSyrianDialectConversionView(APIView):"""APIendpointforconvertingtexttoSyriandialect"""defpost(selfrequest):"""ConvertformalArabictoSyriandialect"""try:text=request.data.get('text''')subject=request.data.get('subject'None)ifnottext:returnResponse({'success':False'error':'Textisrequired'}status=status.HTTP_400_BAD_REQUEST)ai_service=get_ai_service()#ConverttoSyriandialectconverted_text=ai_service._convert_to_syrian_dialect(text)#Addculturalcontextifsubjectprovidedifsubject:converted_text=ai_service._add_syrian_cultural_context(converted_textsubject)returnResponse({'success':True'original_text':text'converted_text':converted_text'subject':subject'is_arabic':ai_service._detect_arabic(text)'is_syrian_dialect':ai_service._detect_syrian_dialect(converted_text)})exceptExceptionase:logger.error(f"ErrorconvertingtoSyriandialect:{e}")returnResponse({'success':False'error':'Failedtoconverttext'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classSyrianAIResponseView(APIView):"""EnhancedAIresponsewithSyriandialectsupport"""defpost(selfrequest):"""GenerateAIresponsewithSyriandialectsupport"""try:prompt=request.data.get('prompt''')subject=request.data.get('subject''general')user_level=request.data.get('user_level''beginner')prefer_syrian=request.data.get('prefer_syrian_dialect'True)ifnotprompt:returnResponse({'success':False'error':'Promptisrequired'}status=status.HTTP_400_BAD_REQUEST)ai_service=get_ai_service()#GenerateSyriandialectresponseresponse=ai_service.generate_syrian_response(promptsubject=subjectuser_level=user_levelprefer_syrian=prefer_syrian)returnResponse(response)exceptExceptionase:logger.error(f"ErrorgeneratingSyrianAIresponse:{e}")error_msg="عذراً،صارعنديمشكلةتقنية.جربتانيبعدشوي."returnResponse({'success':False'content':error_msg'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classSyrianCulturalContextView(APIView):"""APIforSyrianculturalcontextandexamples"""defget(selfrequest):"""GetSyrianculturalcontextforeducationalcontent"""try:subject=request.GET.get('subject''general')ai_service=get_ai_service()cultural_context=ai_service.syrian_config.get('cultural_context'{})#Subject-specificcontextsubject_context={'general':{'greetings':['أهلاًوسهلاً''مرحبافيك''أهلين']'encouragement':['يلابقدرعليك''ماشاءاللهعليك''حلوكتير']'transitions':['طيب''يعني''بالمناسبة''وبعدين']}'math':{'currency':cultural_context.get('currency''ليرةسورية')'measurements':['متر''كيلومتر''كيلو''جرام']'examples':['سوقالحميدية''جامعبنيأمية''قلعةدمشق']}'science':{'geography':cultural_context.get('geography'[])'weather':['شتاءبارد''صيفحار''ربيعمعتدل']'nature':['زيتون''عنب''قمح''لوز']}'history':{'places':cultural_context.get('places'[])'landmarks':['قلعةحلب''كراكالشوابك''تدمر''بصرى']'periods':['العصرالأموي''العصرالعباسي''العصرالعثماني']}}context=subject_context.get(subjectsubject_context['general'])returnResponse({'success':True'subject':subject'cultural_context':context'base_context':cultural_context})exceptExceptionase:logger.error(f"Errorretrievingculturalcontext:{e}")returnResponse({'success':False'error':'Failedtoretrieveculturalcontext'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classSyrianDialectTestView(APIView):"""TestendpointforSyriandialectfunctionality"""defpost(selfrequest):"""TestSyriandialectconversionandAIresponse"""try:test_type=request.data.get('test_type''conversion')text=request.data.get('text''يمكننيمساعدتكفيتعلمالرياضيات')ai_service=get_ai_service()results={}iftest_type=='conversion':#Testdialectconversionresults['original']=textresults['converted']=ai_service._convert_to_syrian_dialect(text)results['is_arabic']=ai_service._detect_arabic(text)results['is_syrian']=ai_service._detect_syrian_dialect(results['converted'])eliftest_type=='ai_response':#TestAIresponsegenerationresponse=ai_service.generate_syrian_response(textsubject='math'prefer_syrian=True)results=responseeliftest_type=='cultural_context':#Testculturalcontextadditionresults['original']=textresults['with_context']=ai_service._add_syrian_cultural_context(text'math')returnResponse({'success':True'test_type':test_type'results':results'config_loaded':bool(ai_service.syrian_config)})exceptExceptionase:logger.error(f"ErrorinSyriandialecttest:{e}")returnResponse({'success':False'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)classSyrianDialectStatusView(APIView):"""StatusendpointforSyriandialectsystem"""defget(selfrequest):"""GetstatusofSyriandialectsystem"""try:ai_service=get_ai_service()#Checkifconfigurationisloadedconfig_loaded=bool(ai_service.syrian_config)emotions_loaded=bool(ai_service.syrian_config.get('emotions'))templates_loaded=bool(ai_service.syrian_config.get('templates'))transitions_loaded=bool(ai_service.syrian_config.get('transitions'))#Testbasicfunctionalitytest_text="يمكننيمساعدتك"converted=ai_service._convert_to_syrian_dialect(test_text)conversion_working=converted!=test_textreturnResponse({'success':True'status':{'config_loaded':config_loaded'emotions_loaded':emotions_loaded'templates_loaded':templates_loaded'transitions_loaded':transitions_loaded'conversion_working':conversion_working'ai_service_available':ai_service.gemini_modelisnotNone}'test_conversion':{'original':test_text'converted':converted}})exceptExceptionase:logger.error(f"ErrorcheckingSyriandialectstatus:{e}")returnResponse({'success':False'error':str(e)}status=status.HTTP_500_INTERNAL_SERVER_ERROR)
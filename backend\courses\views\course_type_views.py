"""CourseTypeAPIViewsThismoduleprovidesAPIendpointsformanagingcoursetypesstudentpreferencesandadminvisibilitysettings."""fromrest_frameworkimportstatusfromrest_framework.decoratorsimportapi_viewpermission_classesfromrest_framework.permissionsimportIsAuthenticatedIsAdminUserfromrest_framework.responseimportResponsefrom django.contrib.authimportget_user_modelfrom courses.servicesimportCourseTypeServicefrom courses.modelsimportCoursefrom courses.serializers.course_serializersimportCourseSerializerUser=get_user_model()@api_view(['GET'])@permission_classes([IsAuthenticated])defget_student_courses(request):"""Getallcoursesvisibletothecurrentstudentbasedonadminsettingsandstudentpreferences.QueryParameters:-level:Optionallevelfilter(1-5)-course_type:Optionalcoursetypefilter(STANDARDINTERACTIVEAI_GENERATED)"""try:#Getlevelfilterfromqueryparamslevel_filter=request.GET.get('level')iflevel_filter:try:level_filter=int(level_filter)iflevel_filter<1orlevel_filter>5:returnResponse({'error':'Levelmustbebetween1and5'}status=status.HTTP_400_BAD_REQUEST)exceptValueError:returnResponse({'error':'Levelmustbeavalidinteger'}status=status.HTTP_400_BAD_REQUEST)#Getvisiblecoursesforthestudentcourses=CourseTypeService.get_visible_courses_for_student(request.userlevel_filter=level_filter)#Applycoursetypefilterifprovidedcourse_type_filter=request.GET.get('course_type')ifcourse_type_filter:ifcourse_type_filter=='STANDARD':courses=courses.filter(has_interactive_content=Falsehas_ai_content=False)elifcourse_type_filter=='INTERACTIVE':courses=courses.filter(has_interactive_content=True)elifcourse_type_filter=='AI_GENERATED':courses=courses.filter(has_ai_content=True)elifcourse_type_filter=='HYBRID':courses=courses.filter(has_interactive_content=Truehas_ai_content=True)#Serializethecoursesserializer=CourseSerializer(coursesmany=Truecontext={'request':request})#Getstudentpreferencespreferences=CourseTypeService.get_student_course_preferences(request.user)#Getadminsettingsadmin_settings=CourseTypeService.get_admin_visibility_settings()returnResponse({'success':True'courses':serializer.data'student_preferences':preferences'admin_settings':{'visible_types':admin_settings['visible_types']'allow_student_choice':admin_settings['allow_student_choice']'force_course_type':admin_settings['force_course_type']}'total_count':courses.count()})exceptExceptionase:returnResponse({'error':f'Failedtogetcourses:{str(e)}'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAuthenticated])defget_course_details(requestcourse_id):"""Getdetailedinformationaboutaspecificcourseincludingallavailabletypes."""try:course_data=CourseTypeService.get_course_details_with_type(course_idrequest.user)if'error'incourse_data:returnResponse(course_datastatus=status.HTTP_404_NOT_FOUNDif'notfound'incourse_data['error']elsestatus.HTTP_403_FORBIDDEN)returnResponse({'success':True'course':course_data})exceptExceptionase:returnResponse({'error':f'Failedtogetcoursedetails:{str(e)}'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET''PUT'])@permission_classes([IsAuthenticated])defstudent_course_preferences(request):"""Getorupdatestudentcoursepreferences.GET:GetcurrentpreferencesPUT:UpdatepreferenceswithJSONdata:{"preferred_course_type":"INTERACTIVE"//optional"prefer_gamification":true//optional"prefer_ai_personalization":true//optional}"""ifrequest.method=='GET':try:preferences=CourseTypeService.get_student_course_preferences(request.user)returnResponse({'success':True'preferences':preferences})exceptExceptionase:returnResponse({'error':f'Failedtogetpreferences:{str(e)}'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)elifrequest.method=='PUT':try:preferences_data=request.dataresult=CourseTypeService.update_student_course_preferences(request.userpreferences_data)if'error'inresult:returnResponse(resultstatus=status.HTTP_400_BAD_REQUEST)returnResponse({'success':True'preferences':result'message':'Preferencesupdatedsuccessfully'})exceptExceptionase:returnResponse({'error':f'Failedtoupdatepreferences:{str(e)}'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAdminUser])defadmin_visibility_settings(request):"""Getcurrentadminvisibilitysettings."""try:settings=CourseTypeService.get_admin_visibility_settings()statistics=CourseTypeService.get_course_statistics()returnResponse({'success':True'settings':settings'statistics':statistics})exceptExceptionase:returnResponse({'error':f'Failedtogetsettings:{str(e)}'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['GET'])@permission_classes([IsAuthenticated])defcourse_type_statistics(request):"""Getstatisticsaboutcoursetypes(availabletoallauthenticatedusers)."""try:statistics=CourseTypeService.get_course_statistics()returnResponse({'success':True'statistics':statistics})exceptExceptionase:returnResponse({'error':f'Failedtogetstatistics:{str(e)}'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)@api_view(['POST'])@permission_classes([IsAuthenticated])defswitch_course_type(requestcourse_id):"""Switchbetweendifferenttypesofthesamecourse.POSTdata:{"course_type":"INTERACTIVE|AI_GENERATED|STANDARD"}"""try:course_type=request.data.get('course_type')ifnotcourse_type:returnResponse({'error':'course_typeisrequired'}status=status.HTTP_400_BAD_REQUEST)ifcourse_typenotin['STANDARD''INTERACTIVE''AI_GENERATED']:returnResponse({'error':'Invalidcourse_type.MustbeSTANDARDINTERACTIVEorAI_GENERATED'}status=status.HTTP_400_BAD_REQUEST)#Getcoursedetailstoverifyaccessandavailabletypescourse_data=CourseTypeService.get_course_details_with_type(course_idrequest.user)if'error'incourse_data:returnResponse(course_datastatus=status.HTTP_404_NOT_FOUNDif'notfound'incourse_data['error']elsestatus.HTTP_403_FORBIDDEN)#Checkiftherequestedtypeisavailableavailable_types=[t['type']fortincourse_data['available_types']]ifcourse_typenotinavailable_types:returnResponse({'error':f'Coursetype{course_type}isnotavailableforthiscourse'}status=status.HTTP_400_BAD_REQUEST)#Updatestudentpreferenceforthiscoursetypepreferences_data={'preferred_course_type':course_type}result=CourseTypeService.update_student_course_preferences(request.userpreferences_data)if'error'inresult:returnResponse(resultstatus=status.HTTP_400_BAD_REQUEST)returnResponse({'success':True'message':f'Switchedto{course_type}coursetype''preferences':result})exceptExceptionase:returnResponse({'error':f'Failedtoswitchcoursetype:{str(e)}'}status=status.HTTP_500_INTERNAL_SERVER_ERROR)
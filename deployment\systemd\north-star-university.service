[Unit]
Description=North Star University Django Application
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=/var/www/north-star-university/backend
Environment=DJANGO_SETTINGS_MODULE=settings.production
EnvironmentFile=/var/www/north-star-university/backend/.env.production
ExecStart=/var/www/north-star-university/venv/bin/gunicorn \
    --config /var/www/north-star-university/deployment/gunicorn/gunicorn.conf.py \
    university_management.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=on-failure
RestartSec=10

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/www/north-star-university/media
ReadWritePaths=/var/www/north-star-university/static
ReadWritePaths=/var/log/north-star-university
ReadWritePaths=/var/run/north-star-university

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target

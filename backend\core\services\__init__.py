"""
Core services package. 
This package contains service classes that provide business logic for the core functionality of the application.
"""

try:
    from .content_mapping_service import ContentMappingService
except ImportError:
    ContentMappingService = None

try:
    from .content_preference_service import ContentPreferenceService
except ImportError:
    ContentPreferenceService = None

try:
    from .content_provider import ContentProviderService
except ImportError:
    ContentProviderService = None

try:
    from .content_reference_service import ContentReferenceService
except ImportError:
    ContentReferenceService = None

try:
    from .content_sync_service import ContentSyncService
except ImportError:
    ContentSyncService = None

try:
    from .progress_service import ProgressService, UnifiedProgressService, progress_service
except ImportError:
    ProgressService = None
    UnifiedProgressService = None
    progress_service = None

__all__ = [
    "progress_service",
    "UnifiedProgressService", 
    "ProgressService",
    "ContentReferenceService",
    "ContentMappingService",
    "ContentSyncService", 
    "ContentProviderService",
    "ContentPreferenceService"
]

#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript and JavaScript files
function findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Function to identify and log external URLs
function findExternalUrls(content, filePath) {
  const externalUrlPatterns = [
    // Common external APIs and services
    /https:\/\/images\.unsplash\.com\/[^'"]+/g,
    /https:\/\/source\.unsplash\.com\/[^'"]+/g,
    /https:\/\/randomuser\.me\/[^'"]+/g,
    /https:\/\/api\.placeholder\.com\/[^'"]+/g,
    /https:\/\/picsum\.photos\/[^'"]+/g,
    /https:\/\/jsonplaceholder\.typicode\.com\/[^'"]+/g,
    // Hardcoded localhost URLs (different from proxy)
    /http:\/\/localhost:[0-9]+\/api/g,
    /https:\/\/localhost:[0-9]+\/api/g,
    // Other external services
    /https:\/\/linkedin\.com/g,
    /https:\/\/twitter\.com/g,
    /mailto:[^'"]+/g,
  ];
  
  const foundUrls = [];
  
  externalUrlPatterns.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      foundUrls.push(...matches);
    }
  });
  
  return foundUrls;
}

// Function to replace external URLs with backend placeholders
function replaceExternalUrls(content) {
  let updatedContent = content;
  let changesMade = false;
  
  // Replace Unsplash URLs with backend placeholder
  const unsplashPattern = /https:\/\/images\.unsplash\.com\/[^'"]+/g;
  if (unsplashPattern.test(updatedContent)) {
    updatedContent = updatedContent.replace(unsplashPattern, '/api/v1/static/placeholder-image.jpg');
    changesMade = true;
  }
  
  const sourceUnsplashPattern = /https:\/\/source\.unsplash\.com\/[^'"]+/g;
  if (sourceUnsplashPattern.test(updatedContent)) {
    updatedContent = updatedContent.replace(sourceUnsplashPattern, '/api/v1/static/placeholder-image.jpg');
    changesMade = true;
  }
  
  // Replace RandomUser API with backend placeholder
  const randomUserPattern = /https:\/\/randomuser\.me\/api\/portraits\/[^'"]+/g;
  if (randomUserPattern.test(updatedContent)) {
    updatedContent = updatedContent.replace(randomUserPattern, '/api/v1/static/default-avatar.jpg');
    changesMade = true;
  }
  
  // Replace hardcoded localhost URLs with relative paths
  const localhostPattern = /https?:\/\/localhost:[0-9]+\/api/g;
  if (localhostPattern.test(updatedContent)) {
    updatedContent = updatedContent.replace(localhostPattern, '/api/v1');
    changesMade = true;
  }
  
  return { content: updatedContent, changed: changesMade };
}

// Main function
function main() {
  const srcDir = path.join(__dirname, '..', 'src');
  const files = findFiles(srcDir);
  
  let totalFiles = 0;
  let filesWithExternalUrls = 0;
  let changedFiles = 0;
  const urlsFound = new Map();
  
  console.log(`Scanning ${files.length} files for external URLs...`);
  
  files.forEach(filePath => {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      totalFiles++;
      
      const externalUrls = findExternalUrls(content, filePath);
      
      if (externalUrls.length > 0) {
        filesWithExternalUrls++;
        const relativePath = path.relative(srcDir, filePath);
        
        console.log(`\nFound external URLs in: ${relativePath}`);
        externalUrls.forEach(url => {
          console.log(`  - ${url}`);
          
          // Track URL frequency
          if (urlsFound.has(url)) {
            urlsFound.set(url, urlsFound.get(url) + 1);
          } else {
            urlsFound.set(url, 1);
          }
        });
      }
      
      // Optionally replace URLs (uncomment to enable)
      const result = replaceExternalUrls(content);
      if (result.changed) {
        fs.writeFileSync(filePath, result.content, 'utf8');
        changedFiles++;
        console.log(`  → Would update: ${path.relative(srcDir, filePath)}`);
      }
      
    } catch (error) {
      console.error(`Error processing ${filePath}:`, error.message);
    }
  });
  
  console.log(`\n=== SUMMARY ===`);
  console.log(`Total files scanned: ${totalFiles}`);
  console.log(`Files with external URLs: ${filesWithExternalUrls}`);
  console.log(`Files that would be changed: ${changedFiles}`);
  
  if (urlsFound.size > 0) {
    console.log(`\n=== MOST COMMON EXTERNAL URLS ===`);
    const sortedUrls = Array.from(urlsFound.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10);
    
    sortedUrls.forEach(([url, count]) => {
      console.log(`${count}x - ${url}`);
    });
  }
  
  console.log(`\n=== RECOMMENDATIONS ===`);
  console.log(`1. Replace Unsplash URLs with: /api/v1/static/placeholder-image.jpg`);
  console.log(`2. Replace RandomUser URLs with: /api/v1/static/default-avatar.jpg`);
  console.log(`3. Ensure all API calls use relative paths: /api/v1/...`);
  console.log(`4. Set up backend static file serving for placeholders`);
  console.log(`\nTo apply changes, uncomment the fs.writeFileSync line in the script.`);
}

if (require.main === module) {
  main();
}

module.exports = { findExternalUrls, replaceExternalUrls };

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Tab,
  Tabs,
  Alert,
  LinearProgress,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  VideoCall,
  Security,
  Analytics,
  School,
  Upload,
  Download,
  Share,
  PlayArrow,
  Stop,
  People,
  Assessment,
  TrendingUp,
  SmartToy,
  VoiceChat
} from '@mui/icons-material';
import {
  plagiarismDetectionService,
  videoConferencingService,
  predictiveAnalyticsService,
  adaptiveLearningService
} from '../../services';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`advanced-tabpanel-${index}`}
      aria-labelledby={`advanced-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const AdvancedFeaturesDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);

  // Plagiarism Detection State
  const [plagiarismFile, setPlagiarismFile] = useState<File | null>(null);
  const [plagiarismReport, setPlagiarismReport] = useState<any>(null);
  const [analysisProgress, setAnalysisProgress] = useState(0);

  // Video Conferencing State
  const [meetingDialog, setMeetingDialog] = useState(false);
  const [meetings, setMeetings] = useState<any[]>([]);
  const [newMeeting, setNewMeeting] = useState({
    title: '',
    description: '',
    startTime: '',
    duration: 60,
    providerId: 'zoom'
  });

  // Analytics State
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [studentRisk, setStudentRisk] = useState<any>(null);

  // Adaptive Learning State
  const [learningRecommendations, setLearningRecommendations] = useState<any[]>([]);
  const [voiceRecording, setVoiceRecording] = useState(false);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Plagiarism Detection Functions
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setPlagiarismFile(file);
    setLoading(true);
    setAnalysisProgress(0);

    try {
      const submission = {
        studentId: 'demo-student',
        studentName: 'Demo Student',
        assignmentId: 'demo-assignment',
        assignmentTitle: 'Demo Assignment',
        courseId: 'demo-course',
        file,
        metadata: {
          submissionDate: new Date().toISOString(),
          dueDate: new Date().toISOString(),
          language: 'en'
        }
      };

      const result = await plagiarismDetectionService.submitForAnalysis(
        submission,
        'turnitin'
      );

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setAnalysisProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            return 100;
          }
          return prev + 10;
        });
      }, 500);

      // Simulate getting the report after analysis
      setTimeout(async () => {
        const report = await plagiarismDetectionService.getReport(result.reportId);
        setPlagiarismReport(report);
        setMessage({ type: 'success', text: 'Plagiarism analysis completed successfully!' });
        setLoading(false);
      }, 5000);

    } catch (error) {
      console.error('Plagiarism analysis error:', error);
      setMessage({ type: 'error', text: 'Failed to analyze document for plagiarism' });
      setLoading(false);
    }
  };

  // Video Conferencing Functions
  const scheduleMeeting = async () => {
    setLoading(true);
    try {
      const schedule = {
        title: newMeeting.title,
        description: newMeeting.description,
        startTime: newMeeting.startTime,
        duration: newMeeting.duration,
        timezone: 'UTC',
        isRecurring: false,
        hostId: 'demo-host',
        hostName: 'Demo Host',
        hostEmail: '<EMAIL>'
      };

      const participants = [
        {
          userId: 'demo-participant',
          name: 'Demo Participant',
          email: '<EMAIL>',
          role: 'participant' as const,
          isRequired: true,
          permissions: {
            canShare: true,
            canRecord: false,
            canManageParticipants: false,
            canUsePoll: true,
            canUseWhiteboard: true
          }
        }
      ];

      const options = {
        passwordProtected: true,
        waitingRoom: true,
        muteOnEntry: true,
        allowJoinBeforeHost: false,
        autoRecord: true,
        recordingLocation: 'cloud' as const,
        enableBreakoutRooms: false,
        enableChat: true,
        enablePolls: true,
        enableWhiteboard: false,
        enableLiveStreaming: false,
        enableTranscription: true
      };

      const meeting = await videoConferencingService.scheduleMeeting(
        newMeeting.providerId,
        schedule,
        participants,
        options
      );

      setMeetings(prev => [...prev, meeting]);
      setMeetingDialog(false);
      setNewMeeting({
        title: '',
        description: '',
        startTime: '',
        duration: 60,
        providerId: 'zoom'
      });
      setMessage({ type: 'success', text: 'Meeting scheduled successfully!' });
    } catch (error) {
      console.error('Meeting scheduling error:', error);
      setMessage({ type: 'error', text: 'Failed to schedule meeting' });
    }
    setLoading(false);
  };

  const startInstantMeeting = async () => {
    setLoading(true);
    try {
      const instantMeeting = await videoConferencingService.createInstantMeeting(
        'zoom',
        'demo-host',
        {
          passwordProtected: false,
          waitingRoom: false,
          muteOnEntry: true,
          enableChat: true,
          enablePolls: false,
          enableWhiteboard: true
        }
      );

      setMessage({ 
        type: 'success', 
        text: `Instant meeting created! Meeting ID: ${instantMeeting.meetingNumber}` 
      });
    } catch (error) {
      console.error('Instant meeting error:', error);
      setMessage({ type: 'error', text: 'Failed to create instant meeting' });
    }
    setLoading(false);
  };

  // Analytics Functions
  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      const [dashboard, riskProfile] = await Promise.all([
        predictiveAnalyticsService.getInstitutionDashboard(),
        predictiveAnalyticsService.analyzeStudentRisk('demo-student')
      ]);

      setAnalyticsData(dashboard);
      setStudentRisk(riskProfile);
      setMessage({ type: 'success', text: 'Analytics data loaded successfully!' });
    } catch (error) {
      console.error('Analytics loading error:', error);
      setMessage({ type: 'error', text: 'Failed to load analytics data' });
    }
    setLoading(false);
  };

  // Adaptive Learning Functions
  const loadAdaptiveRecommendations = async () => {
    setLoading(true);
    try {
      const recommendations = await adaptiveLearningService.generateAdaptiveRecommendations(
        'demo-student',
        'demo-course',
        'mathematics'
      );

      setLearningRecommendations(recommendations);
      setMessage({ type: 'success', text: 'Learning recommendations generated!' });
    } catch (error) {
      console.error('Adaptive learning error:', error);
      setMessage({ type: 'error', text: 'Failed to generate recommendations' });
    }
    setLoading(false);
  };

  const handleVoiceInteraction = async () => {
    if (!voiceRecording) {
      setVoiceRecording(true);
      setMessage({ type: 'info', text: 'Voice recording started...' });
      
      // Simulate voice recording
      setTimeout(async () => {
        setVoiceRecording(false);
        try {
          // Create a mock audio blob
          const audioBlob = new Blob(['mock audio data'], { type: 'audio/wav' });
          
          const result = await adaptiveLearningService.processVoiceInteraction(
            'demo-student',
            audioBlob,
            'learning assistance'
          );

          setMessage({ 
            type: 'success', 
            text: `Voice processed: "${result.transcription}" - ${result.response}` 
          });
        } catch (error) {
          console.error('Voice interaction error:', error);
          setMessage({ type: 'error', text: 'Failed to process voice interaction' });
        }
      }, 3000);
    }
  };

  useEffect(() => {
    // Load initial data
    if (activeTab === 2) {
      loadAnalyticsData();
    }
  }, [activeTab]);

  return (
    <Box sx={{ width: '100%', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Advanced Features Demo
      </Typography>

      {message && (
        <Alert 
          severity={message.type} 
          onClose={() => setMessage(null)}
          sx={{ mb: 2 }}
        >
          {message.text}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab icon={<Security />} label="Plagiarism Detection" />
          <Tab icon={<VideoCall />} label="Video Conferencing" />
          <Tab icon={<Analytics />} label="Predictive Analytics" />
          <Tab icon={<School />} label="Adaptive Learning" />
        </Tabs>
      </Box>

      {/* Plagiarism Detection Tab */}
      <TabPanel value={activeTab} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Upload Document for Analysis
                </Typography>
                <input
                  type="file"
                  accept=".pdf,.doc,.docx,.txt"
                  onChange={handleFileUpload}
                  style={{ marginBottom: 16 }}
                />
                {loading && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2">Analyzing document...</Typography>
                    <LinearProgress variant="determinate" value={analysisProgress} />
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            {plagiarismReport && (
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Plagiarism Report
                  </Typography>
                  <Typography variant="body1">
                    Overall Similarity: {plagiarismReport.overallSimilarity}%
                  </Typography>
                  <Typography variant="body2">
                    Matches Found: {plagiarismReport.matches?.length || 0}
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    {plagiarismReport.flags?.map((flag: string, index: number) => (
                      <Chip key={index} label={flag} color="warning" sx={{ mr: 1 }} />
                    ))}
                  </Box>
                </CardContent>
              </Card>
            )}
          </Grid>
        </Grid>
      </TabPanel>

      {/* Video Conferencing Tab */}
      <TabPanel value={activeTab} index={1}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Meeting Controls
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                  <Button
                    variant="contained"
                    startIcon={<VideoCall />}
                    onClick={startInstantMeeting}
                    disabled={loading}
                  >
                    Start Instant Meeting
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<People />}
                    onClick={() => setMeetingDialog(true)}
                  >
                    Schedule Meeting
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Scheduled Meetings
                </Typography>
                <List>
                  {meetings.map((meeting, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <VideoCall />
                      </ListItemIcon>
                      <ListItemText
                        primary={meeting.title}
                        secondary={`${meeting.startTime} - ${meeting.duration} min`}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Predictive Analytics Tab */}
      <TabPanel value={activeTab} index={2}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            {analyticsData && (
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Institution Dashboard
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Typography>Total Students: {analyticsData.overview.totalStudents}</Typography>
                    <Typography>Active Students: {analyticsData.overview.activeStudents}</Typography>
                    <Typography>At Risk: {analyticsData.overview.atRiskStudents}</Typography>
                    <Typography>Avg Performance: {analyticsData.overview.averagePerformance}%</Typography>
                  </Box>
                </CardContent>
              </Card>
            )}
          </Grid>
          <Grid item xs={12} md={6}>
            {studentRisk && (
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Student Risk Analysis
                  </Typography>
                  <Chip 
                    label={`Risk Level: ${studentRisk.riskLevel}`}
                    color={studentRisk.riskLevel === 'low' ? 'success' : 
                           studentRisk.riskLevel === 'medium' ? 'warning' : 'error'}
                  />
                  <Typography variant="body2" sx={{ mt: 2 }}>
                    Dropout Probability: {(studentRisk.predictions?.dropoutProbability * 100).toFixed(1)}%
                  </Typography>
                  <Typography variant="body2">
                    Expected Grade: {studentRisk.predictions?.expectedGrade}
                  </Typography>
                </CardContent>
              </Card>
            )}
          </Grid>
        </Grid>
      </TabPanel>

      {/* Adaptive Learning Tab */}
      <TabPanel value={activeTab} index={3}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  AI Learning Tools
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                  <Button
                    variant="contained"
                    startIcon={<SmartToy />}
                    onClick={loadAdaptiveRecommendations}
                    disabled={loading}
                  >
                    Get Recommendations
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<VoiceChat />}
                    onClick={handleVoiceInteraction}
                    disabled={voiceRecording}
                    color={voiceRecording ? 'error' : 'primary'}
                  >
                    {voiceRecording ? 'Recording...' : 'Voice Interaction'}
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Learning Recommendations
                </Typography>
                <List>
                  {learningRecommendations.map((rec, index) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={rec.contentType}
                        secondary={`Difficulty: ${rec.difficulty} - ${rec.adaptationReason}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Meeting Schedule Dialog */}
      <Dialog open={meetingDialog} onClose={() => setMeetingDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Schedule New Meeting</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Meeting Title"
                value={newMeeting.title}
                onChange={(e) => setNewMeeting(prev => ({ ...prev, title: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Description"
                value={newMeeting.description}
                onChange={(e) => setNewMeeting(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="datetime-local"
                label="Start Time"
                value={newMeeting.startTime}
                onChange={(e) => setNewMeeting(prev => ({ ...prev, startTime: e.target.value }))}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="Duration (minutes)"
                value={newMeeting.duration}
                onChange={(e) => setNewMeeting(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Video Provider</InputLabel>
                <Select
                  value={newMeeting.providerId}
                  label="Video Provider"
                  onChange={(e) => setNewMeeting(prev => ({ ...prev, providerId: e.target.value }))}
                >
                  <MenuItem value="zoom">Zoom</MenuItem>
                  <MenuItem value="teams">Microsoft Teams</MenuItem>
                  <MenuItem value="webex">Cisco WebEx</MenuItem>
                  <MenuItem value="googlemeet">Google Meet</MenuItem>
                  <MenuItem value="bigbluebutton">BigBlueButton</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setMeetingDialog(false)}>Cancel</Button>
          <Button onClick={scheduleMeeting} variant="contained" disabled={loading}>
            Schedule Meeting
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdvancedFeaturesDemo;

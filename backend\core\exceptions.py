"""
Core exceptions module.

This module defines all custom exceptions used across the application.
These exceptions are designed to be caught by the custom exception handler
to provide consistent error responses.
"""

from rest_framework import status
from rest_framework.exceptions import APIException


class BaseAPIException(APIException):
    """Base exception for all API exceptions"""
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    default_detail = "A server error occurred."
    default_code = "server_error"
    
    def __init__(self, detail=None, code=None):
        if detail is None:
            detail = self.default_detail
        if code is None:
            code = self.default_code
        super().__init__(detail, code)


class ValidationError(BaseAPIException):
    """Exception for validation errors"""
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "Invalid input."
    default_code = "invalid"


class NotFoundError(BaseAPIException):
    """Exception for resource not found errors"""
    status_code = status.HTTP_404_NOT_FOUND
    default_detail = "Resource not found."
    default_code = "not_found"


class AuthenticationError(BaseAPIException):
    """Exception for authentication errors"""
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = "Authentication failed."
    default_code = "authentication_failed"


class PermissionDeniedError(BaseAPIException):
    """Exception for permission denied errors"""
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = "Permission denied."
    default_code = "permission_denied"


class RateLimitError(BaseAPIException):
    """Exception for rate limit errors"""
    status_code = status.HTTP_429_TOO_MANY_REQUESTS
    default_detail = "Rate limit exceeded."
    default_code = "rate_limit_exceeded"


class ConfigurationError(BaseAPIException):
    """Exception for configuration errors"""
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    default_detail = "Configuration error."
    default_code = "configuration_error"


class ExternalServiceError(BaseAPIException):
    """Exception for external service errors"""
    status_code = status.HTTP_503_SERVICE_UNAVAILABLE
    default_detail = "External service unavailable."
    default_code = "external_service_error"

import React, { ReactNode } from 'react';
import { Box, BoxProps, useTheme, alpha } from '@mui/material';
import { styled } from '@mui/material/styles';
import { glassmorphismTokens } from '../theme/glassmorphism';

// Define props for the GlassmorphicContainer component
interface GlassmorphicContainerProps extends BoxProps {
  children: ReactNode;
  blur?: number;
  opacity?: number;
  borderOpacity?: number;
  borderRadius?: number;
  hoverEffect?: boolean;
  activeEffect?: boolean;
  variant?: 'default' | 'card' | 'button' | 'container' | 'modal';
}

// Create a styled component for the glassmorphic container
const StyledGlassmorphicBox = styled(Box, {
  shouldForwardProp: prop =>
    prop !== 'blur' &&
    prop !== 'opacity' &&
    prop !== 'borderOpacity' &&
    prop !== 'hoverEffect' &&
    prop !== 'activeEffect' &&
    prop !== 'variant',
})<GlassmorphicContainerProps>(({
  theme,
  blur = 10,
  opacity = 0.7,
  borderOpacity = 0.3,
  borderRadius = 16,
  hoverEffect = false,
  activeEffect = false,
  variant = 'default',
}) => {
  const isDark = theme.palette.mode === 'dark';
  const isRTL = theme.direction === 'rtl';

  // Get tokens from theme
  const tokens = isDark ? glassmorphismTokens.dark : glassmorphismTokens.light;
  const common = glassmorphismTokens.common;

  // Base styles
  const baseStyles = {
    backgroundColor: theme.palette.background.paper,
    backdropFilter: `blur(${blur}px)`,
    WebkitBackdropFilter: `blur(${blur}px)`,
    border: `1px solid ${theme.palette.divider}`,
    boxShadow: tokens.shadow,
    borderRadius: `${borderRadius}px`,
    transition: common.transition,
    direction: isRTL ? 'rtl' : 'ltr',
  };

  // Hover effect
  const hoverStyles = hoverEffect
    ? {
        '&:hover': {
          boxShadow: tokens.hoverShadow,
          transform:
            variant === 'button' || variant === 'card'
              ? 'translateY(-5px)'
              : 'none',
        },
      }
    : {};

  // Active effect
  const activeStyles = activeEffect
    ? {
        '&:active': {
          boxShadow: tokens.activeShadow,
          transform: 'translateY(0)',
        },
      }
    : {};

  // Variant-specific styles
  const variantStyles = {
    default: {},
    card: {
      padding: theme.spacing(3),
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
    },
    button: {
      padding: theme.spacing(1, 2),
      cursor: 'pointer',
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: parseInt(common.buttonBorderRadius),
      fontWeight: 500,
    },
    container: {
      padding: theme.spacing(3),
    },
    modal: {
      borderRadius: parseInt(common.borderRadius),
    },
  };

  return {
    ...baseStyles,
    ...hoverStyles,
    ...activeStyles,
    ...variantStyles[variant],
  };
});

// GlassmorphicContainer component
const GlassmorphicContainer: React.FC<GlassmorphicContainerProps> = props => {
  return <StyledGlassmorphicBox {...props} />;
};

export default GlassmorphicContainer;

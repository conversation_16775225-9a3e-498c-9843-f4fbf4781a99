from datetime import timedelta
from django.conf import settings
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# Import models from core app
from core.models import BaseModel, Skill

# Use string reference to avoid circular imports
# from courses.models import Course


class StudySession(BaseModel):
    """
    Model to track student study sessions
    """
    student = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="ai_study_sessions"
    )
    start_time = models.DateTimeField(auto_now_add=True)
    end_time = models.DateTimeField(null=True, blank=True)
    duration_minutes = models.PositiveIntegerField(default=0)
    focus_score = models.FloatField(
        default=0.0,
        help_text=_("AI-calculated focus score from 0-1")
    )
    notes = models.TextField(blank=True)
    
    def end_session(self):
        """
        End the study session and calculate duration
        """
        if not self.end_time:
            self.end_time = timezone.now()
        
        # Calculate duration in minutes
        duration = self.end_time - self.start_time
        self.duration_minutes = int(duration.total_seconds() / 60)
        self.save()
    
    def __str__(self):
        return f"{self.student.username}'s session on {self.start_time.strftime('%Y-%m-%d %H:%M')}"


class StudyTopic(BaseModel):
    """
    Model for study topics that can be reviewed with spaced repetition
    """
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    skill = models.ForeignKey(
        Skill,
        on_delete=models.CASCADE,
        related_name="study_topics"
    )
    course = models.ForeignKey(
        "courses.Course",
        on_delete=models.CASCADE,
        related_name="study_topics",
        null=True,
        blank=True
    )
    difficulty_level = models.PositiveSmallIntegerField(
        choices=[
            (1, "Beginner"),
            (2, "Intermediate"),
            (3, "Advanced"),
            (4, "Expert")
        ],
        default=1
    )
    
    def __str__(self):
        return self.title


class StudyMaterial(BaseModel):
    """
    Model for study materials associated with topics
    """
    topic = models.ForeignKey(
        StudyTopic,
        on_delete=models.CASCADE,
        related_name="materials"
    )
    title = models.CharField(max_length=255)
    content = models.TextField()
    material_type = models.CharField(
        max_length=50,
        choices=[
            ("NOTE", "Note"),
            ("FLASHCARD", "Flashcard"),
            ("SUMMARY", "Summary"),
            ("CONCEPT", "Concept"),
            ("FORMULA", "Formula")
        ],
        default="NOTE"
    )
    ai_generated = models.BooleanField(default=False)
    
    def __str__(self):
        return self.title

/**
 * Mock Data Generators for Testing
 * 
 * Provides utility functions to generate mock data for tests
 */

// Mock Student Data Generator
export const generateMockStudent = (overrides: any = {}) => {
  return {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    first_name: 'Test',
    last_name: 'User',
    role: 'STUDENT',
    is_active: true,
    date_joined: '2023-01-01T00:00:00Z',
    current_level: 2,
    gpa: 3.5,
    total_credits: 30,
    ...overrides,
  };
};

// Mock Course Data Generator
export const generateMockCourse = (overrides: any = {}) => {
  return {
    id: 1,
    title: 'Introduction to Computer Science',
    code: 'CS101',
    description: 'A comprehensive introduction to computer science fundamentals',
    credits: 3,
    level: 'BEGINNER',
    instructor: {
      id: 1,
      name: 'Dr. <PERSON>',
      email: '<EMAIL>',
      avatar: null,
      bio: 'Professor of Computer Science',
      rating: 4.5,
    },
    enrollment_count: 25,
    max_students: 30,
    duration: '16 weeks',
    rating: 4.2,
    thumbnail: null,
    tags: ['programming', 'fundamentals', 'computer-science'],
    price: 0,
    is_enrolled: false,
    is_favorite: false,
    progress: 0,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    start_date: '2023-09-01',
    end_date: '2023-12-15',
    category: 'Computer Science',
    prerequisites: [],
    learning_outcomes: [
      'Understand basic programming concepts',
      'Learn problem-solving techniques',
      'Develop algorithmic thinking',
    ],
    ...overrides,
  };
};

// Mock Assessment Data Generator
export const generateMockAssessment = (overrides: any = {}) => {
  return {
    id: 1,
    title: 'Midterm Exam',
    description: 'Comprehensive midterm examination',
    course_id: 1,
    total_points: 100,
    time_limit: 120,
    attempts_allowed: 1,
    is_published: true,
    due_date: '2023-10-15T23:59:59Z',
    created_at: '2023-09-01T00:00:00Z',
    updated_at: '2023-09-01T00:00:00Z',
    questions: [
      {
        id: 1,
        question_text: 'What is the capital of France?',
        question_type: 'MULTIPLE_CHOICE',
        points: 10,
        options: [
          { id: 1, text: 'London', is_correct: false },
          { id: 2, text: 'Berlin', is_correct: false },
          { id: 3, text: 'Paris', is_correct: true },
          { id: 4, text: 'Madrid', is_correct: false },
        ],
        difficulty: 'EASY',
        explanation: 'Paris is the capital and largest city of France.',
        order: 1,
      },
    ],
    instructions: 'Please read all questions carefully before answering.',
    passing_score: 70,
    randomize_questions: false,
    show_results_immediately: true,
    ...overrides,
  };
};

// Mock Question Data Generator
export const generateMockQuestion = (overrides: any = {}) => {
  return {
    id: 1,
    question_text: 'What is 2 + 2?',
    question_type: 'MULTIPLE_CHOICE',
    points: 5,
    options: [
      { id: 1, text: '3', is_correct: false },
      { id: 2, text: '4', is_correct: true },
      { id: 3, text: '5', is_correct: false },
      { id: 4, text: '6', is_correct: false },
    ],
    difficulty: 'EASY',
    explanation: '2 + 2 equals 4.',
    order: 1,
    ...overrides,
  };
};

// Mock Grade Data Generator
export const generateMockGrade = (overrides: any = {}) => {
  return {
    id: 1,
    student_id: 1,
    course_id: 1,
    course_code: 'CS101',
    course_title: 'Introduction to Computer Science',
    credits: 3,
    score: 85.0,
    grade: 'B+',
    comments: 'Good work',
    semester: 'Fall 2023',
    academic_year: '2023-2024',
    created_at: '2023-12-15T00:00:00Z',
    updated_at: '2023-12-15T00:00:00Z',
    ...overrides,
  };
};

// Mock Notification Data Generator
export const generateMockNotification = (overrides: any = {}) => {
  return {
    id: 1,
    title: 'New Assignment Posted',
    message: 'A new assignment has been posted for CS101',
    type: 'ASSIGNMENT',
    is_read: false,
    created_at: '2023-10-01T10:00:00Z',
    user_id: 1,
    course_id: 1,
    ...overrides,
  };
};

// Mock API Response Generator
export const generateMockApiResponse = (data: any, overrides: any = {}) => {
  return {
    status: 'success',
    message: 'Operation completed successfully',
    data,
    timestamp: new Date().toISOString(),
    ...overrides,
  };
};

// Mock Error Response Generator
export const generateMockErrorResponse = (message: string = 'An error occurred', overrides: any = {}) => {
  return {
    status: 'error',
    message,
    error_code: 'GENERIC_ERROR',
    timestamp: new Date().toISOString(),
    ...overrides,
  };
};

// Mock Paginated Response Generator
export const generateMockPaginatedResponse = (results: any[], overrides: any = {}) => {
  return {
    status: 'success',
    data: {
      results,
      count: results.length,
      page: 1,
      total_pages: 1,
      page_size: results.length,
      has_next: false,
      has_previous: false,
    },
    ...overrides,
  };
};

// Mock User Event Data Generator
export const generateMockUserEvent = (overrides: any = {}) => {
  return {
    id: 1,
    user_id: 1,
    event_type: 'LOGIN',
    event_data: {},
    timestamp: new Date().toISOString(),
    ip_address: '127.0.0.1',
    user_agent: 'Mozilla/5.0 (Test Browser)',
    ...overrides,
  };
};

// Mock Learning Path Data Generator
export const generateMockLearningPath = (overrides: any = {}) => {
  return {
    id: 1,
    title: 'Web Development Fundamentals',
    description: 'Learn the basics of web development',
    courses: [1, 2, 3],
    estimated_duration: '12 weeks',
    difficulty: 'BEGINNER',
    prerequisites: [],
    learning_outcomes: [
      'Build responsive websites',
      'Understand HTML, CSS, and JavaScript',
      'Deploy web applications',
    ],
    progress: 0,
    is_enrolled: false,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    ...overrides,
  };
};

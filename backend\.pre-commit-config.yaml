# Pre-commit configuration for code quality enforcement
# Install with: pip install pre-commit && pre-commit install

repos:
  # General code quality
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: debug-statements
      - id: check-docstring-first

  # Python code formatting
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=88]

  # Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]

  # Linting
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,W503]
        additional_dependencies: [flake8-docstrings]

  # Security checks
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, ., -f, json, -o, bandit-report.json]
        exclude: ^tests/

  # Django specific checks
  - repo: local
    hooks:
      - id: django-check
        name: Django Check
        entry: python manage.py check
        language: system
        pass_filenames: false
        files: \.py$

      - id: django-migrations-check
        name: Django Migrations Check
        entry: python manage.py makemigrations --check --dry-run
        language: system
        pass_filenames: false
        files: \.py$

  # Custom code quality checks
  - repo: local
    hooks:
      - id: naming-conventions
        name: Check Naming Conventions
        entry: python manage.py fix_naming_conventions --dry-run
        language: system
        pass_filenames: false
        files: \.py$

      - id: circular-imports
        name: Check Circular Imports
        entry: python manage.py fix_circular_imports --analyze-only
        language: system
        pass_filenames: false
        files: \.py$

# Configuration for specific tools
default_language_version:
  python: python3.9

# Exclude patterns
exclude: |
  (?x)^(
      migrations/.*|
      __pycache__/.*|
      \.venv/.*|
      build/.*|
      dist/.*
  )$

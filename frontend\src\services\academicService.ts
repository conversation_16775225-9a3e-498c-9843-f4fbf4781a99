/**
 * Academic Management Service
 * 
 * This service provides frontend integration with the backend academic management system,
 * including transcript management, academic calendar, prerequisites enforcement, GPA tracking,
 * waitlist management, and enrollment history.
 */

import axiosInstance from '../config/axios';
import { ApiResponse, SuccessResponse, ErrorResponse } from '../types/api';

// Type definitions
export interface AcademicTerm {
  id: number;
  name: string;
  year: number;
  term_type: 'FALL' | 'SPRING' | 'SUMMER';
  start_date: string;
  end_date: string;
  registration_start: string;
  registration_end: string;
  is_current: boolean;
  is_registration_open: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface TranscriptRecord {
  id: number;
  student: number;
  course: {
    id: number;
    course_code: string;
    title: string;
    credits: number;
  };
  term: AcademicTerm;
  grade: string;
  credits_earned: number;
  grade_points: number;
  is_transfer: boolean;
  transfer_institution?: string;
  created_at: string;
  updated_at: string;
}

export interface Transcript {
  student: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  terms: Array<{
    term: AcademicTerm;
    records: TranscriptRecord[];
    term_gpa: number;
    term_credits: number;
  }>;
  cumulative_gpa: number;
  total_credits: number;
  generated_at: string;
}

export interface PrerequisiteCheck {
  course_id: number;
  student_id: number;
  eligible: boolean;
  missing_prerequisites: Array<{
    type: 'COURSE' | 'GPA' | 'CREDITS' | 'STANDING';
    requirement: string;
    current_value?: string;
    required_value: string;
  }>;
  warnings: string[];
  checked_at: string;
}

export interface GpaUpdate {
  student_id: number;
  term_id?: number;
  cumulative_gpa: number;
  term_gpa?: number;
  total_credits: number;
  term_credits?: number;
  updated_at: string;
}

export interface AcademicStanding {
  id: number;
  student: number;
  standing: 'GOOD' | 'PROBATION' | 'SUSPENSION' | 'DISMISSAL';
  term: AcademicTerm;
  gpa_at_time: number;
  credits_at_time: number;
  reason?: string;
  effective_date: string;
  created_at: string;
}

export interface WaitlistEntry {
  id: number;
  student: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  course: {
    id: number;
    course_code: string;
    title: string;
    max_students: number;
  };
  position: number;
  priority: number;
  added_at: string;
  notified_at?: string;
  expires_at?: string;
  status: 'WAITING' | 'NOTIFIED' | 'ENROLLED' | 'EXPIRED' | 'CANCELLED';
}

export interface EnrollmentHistoryEntry {
  id: number;
  student: number;
  course: {
    id: number;
    course_code: string;
    title: string;
  };
  action: 'ENROLLED' | 'DROPPED' | 'WAITLISTED' | 'AUTO_ENROLLED';
  timestamp: string;
  term?: AcademicTerm;
  reason?: string;
  performed_by?: {
    id: number;
    username: string;
    role: string;
  };
}

export interface AtRiskStudent {
  student: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  current_gpa: number;
  risk_factors: string[];
  recommended_actions: string[];
  last_updated: string;
}

/**
 * Academic Management Service
 */
export const academicService = {
  // ==================== ACADEMIC TERMS ====================
  
  /**
   * Get all academic terms
   */
  getTerms: async (): Promise<AcademicTerm[]> => {
    try {
      const response = await axiosInstance.get('/api/v1/academic/terms/');
      
      if (response.data?.status === 'success') {
        return response.data.data;
      } else if (Array.isArray(response.data)) {
        return response.data;
      }
      
      return response.data?.data || [];
    } catch (error: any) {
      console.error('Error fetching academic terms:', error);
      return [];
    }
  },

  /**
   * Get current academic term
   */
  getCurrentTerm: async (): Promise<AcademicTerm | null> => {
    try {
      const response = await axiosInstance.get('/api/v1/academic/terms/current/');
      
      if (response.data?.status === 'success') {
        return response.data.data;
      }
      
      return response.data || null;
    } catch (error: any) {
      console.error('Error fetching current term:', error);
      return null;
    }
  },

  /**
   * Get registration term (term for which registration is currently open)
   */
  getRegistrationTerm: async (): Promise<AcademicTerm | null> => {
    try {
      const response = await axiosInstance.get('/api/v1/academic/terms/registration/');
      
      if (response.data?.status === 'success') {
        return response.data.data;
      }
      
      return response.data || null;
    } catch (error: any) {
      console.error('Error fetching registration term:', error);
      return null;
    }
  },

  /**
   * Create a new academic term (admin only)
   */
  createTerm: async (termData: Partial<AcademicTerm>): Promise<AcademicTerm> => {
    try {
      const response = await axiosInstance.post('/api/v1/academic/terms/', termData);
      
      if (response.data?.status === 'success') {
        return response.data.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Error creating academic term:', error);
      throw error;
    }
  },

  /**
   * Update an academic term (admin only)
   */
  updateTerm: async (termId: number, termData: Partial<AcademicTerm>): Promise<AcademicTerm> => {
    try {
      const response = await axiosInstance.put(`/api/v1/academic/terms/${termId}/`, termData);
      
      if (response.data?.status === 'success') {
        return response.data.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Error updating academic term:', error);
      throw error;
    }
  },

  // ==================== TRANSCRIPTS ====================

  /**
   * Get student transcript
   */
  getTranscript: async (studentId?: number): Promise<Transcript> => {
    try {
      const url = studentId 
        ? `/api/v1/academic/transcripts/${studentId}/`
        : '/api/v1/academic/transcripts/my-transcript/';
      
      const response = await axiosInstance.get(url);
      
      if (response.data?.status === 'success') {
        return response.data.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Error fetching transcript:', error);
      throw error;
    }
  },

  /**
   * Generate official transcript
   */
  generateOfficialTranscript: async (studentId?: number): Promise<Blob> => {
    try {
      const url = studentId 
        ? `/api/v1/academic/transcripts/${studentId}/official/`
        : '/api/v1/academic/transcripts/my-transcript/official/';
      
      const response = await axiosInstance.get(url, {
        responseType: 'blob'
      });
      
      return response.data;
    } catch (error: any) {
      console.error('Error generating official transcript:', error);
      throw error;
    }
  },

  /**
   * Add transcript record (admin only)
   */
  addTranscriptRecord: async (recordData: Partial<TranscriptRecord>): Promise<TranscriptRecord> => {
    try {
      const response = await axiosInstance.post('/api/v1/academic/transcript-records/', recordData);
      
      if (response.data?.status === 'success') {
        return response.data.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Error adding transcript record:', error);
      throw error;
    }
  },

  /**
   * Update transcript record (admin only)
   */
  updateTranscriptRecord: async (recordId: number, recordData: Partial<TranscriptRecord>): Promise<TranscriptRecord> => {
    try {
      const response = await axiosInstance.put(`/api/v1/academic/transcript-records/${recordId}/`, recordData);
      
      if (response.data?.status === 'success') {
        return response.data.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Error updating transcript record:', error);
      throw error;
    }
  },

  // ==================== PREREQUISITES ====================

  /**
   * Check enrollment eligibility for a course
   */
  checkPrerequisites: async (courseId: number, studentId?: number): Promise<PrerequisiteCheck> => {
    try {
      const url = studentId 
        ? `/api/v1/academic/prerequisites/check/${courseId}/?student_id=${studentId}`
        : `/api/v1/academic/prerequisites/check/${courseId}/`;
      
      const response = await axiosInstance.get(url);
      
      if (response.data?.status === 'success') {
        return response.data.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Error checking prerequisites:', error);
      throw error;
    }
  },

  /**
   * Batch check prerequisites for multiple courses
   */
  batchCheckPrerequisites: async (courseIds: number[], studentId?: number): Promise<PrerequisiteCheck[]> => {
    try {
      const response = await axiosInstance.post('/api/v1/academic/prerequisites/batch-check/', {
        course_ids: courseIds,
        student_id: studentId
      });
      
      if (response.data?.status === 'success') {
        return response.data.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Error batch checking prerequisites:', error);
      throw error;
    }
  },

  // ==================== GPA AND ACADEMIC STANDING ====================

  /**
   * Update student GPA
   */
  updateGpa: async (studentId: number, termId?: number): Promise<GpaUpdate> => {
    try {
      const response = await axiosInstance.post('/api/v1/academic/gpa/update/', {
        student_id: studentId,
        term_id: termId
      });
      
      if (response.data?.status === 'success') {
        return response.data.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Error updating GPA:', error);
      throw error;
    }
  },

  /**
   * Get student's academic standing history
   */
  getAcademicStanding: async (studentId?: number): Promise<AcademicStanding[]> => {
    try {
      const url = studentId 
        ? `/api/v1/academic/standing/${studentId}/`
        : '/api/v1/academic/standing/my-standing/';
      
      const response = await axiosInstance.get(url);
      
      if (response.data?.status === 'success') {
        return response.data.data;
      } else if (Array.isArray(response.data)) {
        return response.data;
      }
      
      return response.data?.data || [];
    } catch (error: any) {
      console.error('Error fetching academic standing:', error);
      return [];
    }
  },

  /**
   * Update academic standing (admin only)
   */
  updateAcademicStanding: async (studentId: number, termId: number): Promise<AcademicStanding> => {
    try {
      const response = await axiosInstance.post('/api/v1/academic/standing/update/', {
        student_id: studentId,
        term_id: termId
      });
      
      if (response.data?.status === 'success') {
        return response.data.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Error updating academic standing:', error);
      throw error;
    }
  },

  /**
   * Get at-risk students (admin/professor only)
   */
  getAtRiskStudents: async (gpaThreshold?: number): Promise<AtRiskStudent[]> => {
    try {
      const params = gpaThreshold ? { gpa_threshold: gpaThreshold } : {};
      const response = await axiosInstance.get('/api/v1/academic/at-risk-students/', { params });
      
      if (response.data?.status === 'success') {
        return response.data.data;
      } else if (Array.isArray(response.data)) {
        return response.data;
      }
      
      return response.data?.data || [];
    } catch (error: any) {
      console.error('Error fetching at-risk students:', error);
      return [];
    }
  },

  // ==================== WAITLIST MANAGEMENT ====================

  /**
   * Add student to course waitlist
   */
  joinWaitlist: async (courseId: number, priority?: number): Promise<WaitlistEntry> => {
    try {
      const response = await axiosInstance.post('/api/v1/academic/waitlist/join/', {
        course_id: courseId,
        priority: priority
      });
      
      if (response.data?.status === 'success') {
        return response.data.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Error joining waitlist:', error);
      throw error;
    }
  },

  /**
   * Remove student from course waitlist
   */
  leaveWaitlist: async (courseId: number): Promise<void> => {
    try {
      await axiosInstance.post('/api/v1/academic/waitlist/leave/', {
        course_id: courseId
      });
    } catch (error: any) {
      console.error('Error leaving waitlist:', error);
      throw error;
    }
  },

  /**
   * Get student's waitlist entries
   */
  getMyWaitlistEntries: async (): Promise<WaitlistEntry[]> => {
    try {
      const response = await axiosInstance.get('/api/v1/academic/waitlist/my-entries/');
      
      if (response.data?.status === 'success') {
        return response.data.data;
      } else if (Array.isArray(response.data)) {
        return response.data;
      }
      
      return response.data?.data || [];
    } catch (error: any) {
      console.error('Error fetching waitlist entries:', error);
      return [];
    }
  },

  /**
   * Get course waitlist (admin/professor only)
   */
  getCourseWaitlist: async (courseId: number): Promise<WaitlistEntry[]> => {
    try {
      const response = await axiosInstance.get(`/api/v1/academic/waitlist/course/${courseId}/`);
      
      if (response.data?.status === 'success') {
        return response.data.data;
      } else if (Array.isArray(response.data)) {
        return response.data;
      }
      
      return response.data?.data || [];
    } catch (error: any) {
      console.error('Error fetching course waitlist:', error);
      return [];
    }
  },

  /**
   * Process waitlist notifications (admin only)
   */
  processWaitlistNotifications: async (courseId?: number): Promise<void> => {
    try {
      const data = courseId ? { course_id: courseId } : {};
      await axiosInstance.post('/api/v1/academic/waitlist/process-notifications/', data);
    } catch (error: any) {
      console.error('Error processing waitlist notifications:', error);
      throw error;
    }
  },

  /**
   * Auto-enroll from waitlist (admin only)
   */
  autoEnrollFromWaitlist: async (courseId: number, count?: number): Promise<number> => {
    try {
      const response = await axiosInstance.post('/api/v1/academic/waitlist/auto-enroll/', {
        course_id: courseId,
        count: count
      });
      
      if (response.data?.status === 'success') {
        return response.data.data.enrolled_count;
      }
      
      return response.data?.enrolled_count || 0;
    } catch (error: any) {
      console.error('Error auto-enrolling from waitlist:', error);
      throw error;
    }
  },

  // ==================== ENROLLMENT HISTORY ====================

  /**
   * Get student's enrollment history
   */
  getEnrollmentHistory: async (studentId?: number): Promise<EnrollmentHistoryEntry[]> => {
    try {
      const url = studentId 
        ? `/api/v1/academic/enrollment-history/${studentId}/`
        : '/api/v1/academic/enrollment-history/my-history/';
      
      const response = await axiosInstance.get(url);
      
      if (response.data?.status === 'success') {
        return response.data.data;
      } else if (Array.isArray(response.data)) {
        return response.data;
      }
      
      return response.data?.data || [];
    } catch (error: any) {
      console.error('Error fetching enrollment history:', error);
      return [];
    }
  },

  /**
   * Get course enrollment history (admin/professor only)
   */
  getCourseEnrollmentHistory: async (courseId: number): Promise<EnrollmentHistoryEntry[]> => {
    try {
      const response = await axiosInstance.get(`/api/v1/academic/enrollment-history/course/${courseId}/`);
      
      if (response.data?.status === 'success') {
        return response.data.data;
      } else if (Array.isArray(response.data)) {
        return response.data;
      }
      
      return response.data?.data || [];
    } catch (error: any) {
      console.error('Error fetching course enrollment history:', error);
      return [];
    }
  },

  /**
   * Record enrollment action (used internally by enrollment system)
   */
  recordEnrollmentAction: async (actionData: {
    student_id: number;
    course_id: number;
    action: string;
    term_id?: number;
    reason?: string;
  }): Promise<EnrollmentHistoryEntry> => {
    try {
      const response = await axiosInstance.post('/api/v1/academic/enrollment-history/record/', actionData);
      
      if (response.data?.status === 'success') {
        return response.data.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Error recording enrollment action:', error);
      throw error;
    }
  },

  // ==================== DASHBOARD AND ANALYTICS ====================

  /**
   * Get academic dashboard stats (admin only)
   */
  getDashboardStats: async (): Promise<any> => {
    try {
      const response = await axiosInstance.get('/api/v1/academic/dashboard/stats/');
      
      if (response.data?.status === 'success') {
        return response.data.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Error fetching dashboard stats:', error);
      return {};
    }
  },

  /**
   * Get student academic summary
   */
  getStudentAcademicSummary: async (studentId?: number): Promise<any> => {
    try {
      const url = studentId 
        ? `/api/v1/academic/student-summary/${studentId}/`
        : '/api/v1/academic/student-summary/my-summary/';
      
      const response = await axiosInstance.get(url);
      
      if (response.data?.status === 'success') {
        return response.data.data;
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Error fetching student academic summary:', error);
      return {};
    }
  },

  // ==================== UTILITY METHODS ====================

  /**
   * Calculate GPA from transcript records
   */
  calculateGPA: (records: TranscriptRecord[]): number => {
    if (!records || records.length === 0) return 0;
    
    let totalPoints = 0;
    let totalCredits = 0;
    
    records.forEach(record => {
      totalPoints += record.grade_points;
      totalCredits += record.credits_earned;
    });
    
    return totalCredits > 0 ? totalPoints / totalCredits : 0;
  },

  /**
   * Format GPA for display
   */
  formatGPA: (gpa: number): string => {
    return gpa.toFixed(2);
  },

  /**
   * Get grade letter from points
   */
  getGradeLetter: (gradePoints: number, credits: number): string => {
    if (credits === 0) return 'F';
    
    const gpa = gradePoints / credits;
    
    if (gpa >= 3.7) return 'A';
    if (gpa >= 3.3) return 'A-';
    if (gpa >= 3.0) return 'B+';
    if (gpa >= 2.7) return 'B';
    if (gpa >= 2.3) return 'B-';
    if (gpa >= 2.0) return 'C+';
    if (gpa >= 1.7) return 'C';
    if (gpa >= 1.3) return 'C-';
    if (gpa >= 1.0) return 'D+';
    if (gpa >= 0.7) return 'D';
    if (gpa >= 0.4) return 'D-';
    return 'F';
  },

  /**
   * Check if registration is currently open
   */
  isRegistrationOpen: async (): Promise<boolean> => {
    try {
      const term = await academicService.getRegistrationTerm();
      return term !== null;
    } catch (error) {
      return false;
    }
  },

  /**
   * Get academic standing color for UI
   */
  getStandingColor: (standing: string): string => {
    switch (standing) {
      case 'GOOD': return 'green';
      case 'PROBATION': return 'orange';
      case 'SUSPENSION': return 'red';
      case 'DISMISSAL': return 'red';
      default: return 'gray';
    }
  }
};

export default academicService;

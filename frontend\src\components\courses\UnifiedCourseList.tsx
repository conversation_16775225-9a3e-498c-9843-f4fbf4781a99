import React, { useState, useMemo, useCallback, memo } from 'react';
// Optimized imports for better tree shaking
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Chip from '@mui/material/Chip';
import InputAdornment from '@mui/material/InputAdornment';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import Skeleton from '@mui/material/Skeleton';
import Alert from '@mui/material/Alert';
import Fade from '@mui/material/Fade';
// Keep lightweight layout component as named import
import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import { Search, FilterList, ViewModule, ViewList } from '../icons';
import { alpha } from '@mui/material/styles';
import SuperCourseCard from './SuperCourseCard';
import { Course } from '../../types/courses';

const FilterContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(3),
  background: 'rgba(255, 255, 255, 0.05)',
  backdropFilter: 'blur(10px)',
  borderRadius: '16px',
  border: '1px solid rgba(255, 255, 255, 0.1)',
}));

const SearchField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '12px',
    background: 'rgba(255, 255, 255, 0.05)',
    '&:hover': {
      background: 'rgba(255, 255, 255, 0.08)',
    },
    '&.Mui-focused': {
      background: 'rgba(255, 255, 255, 0.1)',
    },
  },
}));

const StyledSelect = styled(Select)(({ theme }) => ({
  borderRadius: '12px',
  background: 'rgba(255, 255, 255, 0.05)',
  '&:hover': {
    background: 'rgba(255, 255, 255, 0.08)',
  },
  '&.Mui-focused': {
    background: 'rgba(255, 255, 255, 0.1)',
  },
}));

const ViewToggle = styled(ToggleButtonGroup)(({ theme }) => ({
  '& .MuiToggleButton-root': {
    borderRadius: '8px',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    color: theme.palette.text.secondary,
    '&.Mui-selected': {
      background: 'rgba(33, 150, 243, 0.2)',
      color: theme.palette.primary.main,
      '&:hover': {
        background: 'rgba(33, 150, 243, 0.3)',
      },
    },
    '&:hover': {
      background: 'rgba(255, 255, 255, 0.05)',
    },
  },
}));

const ResultsHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.spacing(3),
  padding: theme.spacing(2),
  background: 'rgba(255, 255, 255, 0.03)',
  borderRadius: '12px',
  border: '1px solid rgba(255, 255, 255, 0.1)',
}));

const EmptyState = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(6),
  background: 'rgba(255, 255, 255, 0.05)',
  borderRadius: '16px',
  border: '1px solid rgba(255, 255, 255, 0.1)',
}));

interface UnifiedCourseListProps {
  courses: Course[];
  loading?: boolean;
  error?: string;
  onEnroll?: (courseId: number) => void;
  onViewDetails?: (courseId: number) => void;
  onStartLearning?: (courseId: number) => void;
  onTakeAssessment?: (courseId: number) => void;
  onBookmark?: (courseId: number) => void;
  enrolledCourses?: number[];
  bookmarkedCourses?: number[];
  showProgress?: boolean;
  courseProgress?: Record<number, number>;
  showInteractiveFeatures?: boolean;
  title?: string;
  subtitle?: string;
}

const UnifiedCourseList: React.FC<UnifiedCourseListProps> = ({
  courses,
  loading = false,
  error,
  onEnroll,
  onViewDetails,
  onStartLearning,
  onTakeAssessment,
  onBookmark,
  enrolledCourses = [],
  bookmarkedCourses = [],
  showProgress = false,
  courseProgress = {},
  showInteractiveFeatures = false,
  title = 'Available Courses',
  subtitle = 'Discover and enroll in courses that match your interests',
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Extract unique departments and levels
  const departments = useMemo(() => {
    const depts = courses
      .map(course => course.department?.name)
      .filter(Boolean)
      .filter((dept, index, arr) => arr.indexOf(dept) === index);
    return depts;
  }, [courses]);

  const levels = useMemo(() => {
    const lvls = courses
      .map(course => course.level)
      .filter(Boolean)
      .filter((level, index, arr) => arr.indexOf(level) === index);
    return lvls;
  }, [courses]);

  // Filter courses based on search and filters
  const filteredCourses = useMemo(() => {
    return courses.filter(course => {
      const matchesSearch =
        !searchTerm ||
        course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.course_code?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesDepartment =
        !selectedDepartment || course.department?.name === selectedDepartment;

      const matchesLevel =
        !selectedLevel || String(course.level) === selectedLevel;

      const matchesStatus =
        !selectedStatus ||
        (selectedStatus === 'enrolled' &&
          enrolledCourses.includes(course.id)) ||
        (selectedStatus === 'available' &&
          course.is_active &&
          !enrolledCourses.includes(course.id)) ||
        (selectedStatus === 'bookmarked' &&
          bookmarkedCourses.includes(course.id));

      return (
        matchesSearch && matchesDepartment && matchesLevel && matchesStatus
      );
    });
  }, [
    courses,
    searchTerm,
    selectedDepartment,
    selectedLevel,
    selectedStatus,
    enrolledCourses,
    bookmarkedCourses,
  ]);

  const clearFilters = useCallback(() => {
    setSearchTerm('');
    setSelectedDepartment('');
    setSelectedLevel('');
    setSelectedStatus('');
  }, []);

  const activeFiltersCount = useMemo(() => [
    searchTerm,
    selectedDepartment,
    selectedLevel,
    selectedStatus,
  ].filter(Boolean).length, [searchTerm, selectedDepartment, selectedLevel, selectedStatus]);

  if (error) {
    return (
      <Alert severity='error' sx={{ borderRadius: '12px', mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Typography variant='h4' fontWeight={700} gutterBottom>
          {title}
        </Typography>
        <Typography variant='body1' color='text.secondary'>
          {subtitle}
        </Typography>
      </Box>

      {/* Filters */}
      <FilterContainer>
        <Grid container spacing={3} alignItems='center'>
          <Grid item xs={12} md={4}>
            <SearchField
              fullWidth
              placeholder='Search courses...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position='start'>
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth>
              <InputLabel>Department</InputLabel>
              <StyledSelect
                value={selectedDepartment}
                onChange={e => setSelectedDepartment(e.target.value)}
                label='Department'
              >
                <MenuItem value=''>All Departments</MenuItem>
                {departments.map(dept => (
                  <MenuItem key={dept} value={dept}>
                    {dept}
                  </MenuItem>
                ))}
              </StyledSelect>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth>
              <InputLabel>Level</InputLabel>
              <StyledSelect
                value={selectedLevel}
                onChange={e => setSelectedLevel(e.target.value)}
                label='Level'
              >
                <MenuItem value=''>All Levels</MenuItem>
                {levels.map(level => (
                  <MenuItem key={level} value={String(level)}>
                    {level}
                  </MenuItem>
                ))}
              </StyledSelect>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <StyledSelect
                value={selectedStatus}
                onChange={e => setSelectedStatus(e.target.value)}
                label='Status'
              >
                <MenuItem value=''>All Courses</MenuItem>
                <MenuItem value='available'>Available</MenuItem>
                <MenuItem value='enrolled'>Enrolled</MenuItem>
                <MenuItem value='bookmarked'>Bookmarked</MenuItem>
              </StyledSelect>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={2}>
            <Box
              display='flex'
              justifyContent='space-between'
              alignItems='center'
            >
              <ViewToggle
                value={viewMode}
                exclusive
                onChange={(_, newMode) => newMode && setViewMode(newMode)}
                size='small'
              >
                <ToggleButton value='grid'>
                  <ViewModule />
                </ToggleButton>
                <ToggleButton value='list'>
                  <ViewList />
                </ToggleButton>
              </ViewToggle>
            </Box>
          </Grid>
        </Grid>

        {/* Active Filters */}
        {activeFiltersCount > 0 && (
          <Box
            mt={2}
            display='flex'
            gap={1}
            alignItems='center'
            flexWrap='wrap'
          >
            <Typography variant='body2' color='text.secondary'>
              Active filters:
            </Typography>
            {searchTerm && (
              <Chip
                label={`Search: "${searchTerm}"`}
                size='small'
                onDelete={() => setSearchTerm('')}
                color='primary'
                variant='outlined'
              />
            )}
            {selectedDepartment && (
              <Chip
                label={`Department: ${selectedDepartment}`}
                size='small'
                onDelete={() => setSelectedDepartment('')}
                color='secondary'
                variant='outlined'
              />
            )}
            {selectedLevel && (
              <Chip
                label={`Level: ${selectedLevel}`}
                size='small'
                onDelete={() => setSelectedLevel('')}
                color='info'
                variant='outlined'
              />
            )}
            {selectedStatus && (
              <Chip
                label={`Status: ${selectedStatus}`}
                size='small'
                onDelete={() => setSelectedStatus('')}
                color='warning'
                variant='outlined'
              />
            )}
            <Chip
              label='Clear all'
              size='small'
              onClick={clearFilters}
              variant='outlined'
              sx={{ ml: 1 }}
            />
          </Box>
        )}
      </FilterContainer>

      {/* Results Header */}
      <ResultsHeader>
        <Typography variant='h6' fontWeight={600}>
          {loading
            ? 'Loading...'
            : `${filteredCourses.length} course${filteredCourses.length !== 1 ? 's' : ''} found`}
        </Typography>
        <Box display='flex' alignItems='center' gap={1}>
          <FilterList />
          <Typography variant='body2' color='text.secondary'>
            {activeFiltersCount} filter{activeFiltersCount !== 1 ? 's' : ''}{' '}
            applied
          </Typography>
        </Box>
      </ResultsHeader>

      {/* Course Grid/List */}
      {loading ? (
        <Grid container spacing={3}>
          {Array.from({ length: 6 }).map((_, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Skeleton
                variant='rectangular'
                height={400}
                sx={{ borderRadius: '20px' }}
              />
            </Grid>
          ))}
        </Grid>
      ) : filteredCourses.length === 0 ? (
        <Fade in>
          <EmptyState>
            <Typography variant='h6' gutterBottom>
              No courses found
            </Typography>
            <Typography variant='body2' color='text.secondary' mb={2}>
              Try adjusting your search criteria or filters
            </Typography>
            {activeFiltersCount > 0 && (
              <Chip
                label='Clear all filters'
                onClick={clearFilters}
                color='primary'
                variant='outlined'
              />
            )}
          </EmptyState>
        </Fade>
      ) : (
        <Fade in>
          <Grid container spacing={3}>
            {filteredCourses.map(course => (
              <Grid
                item
                xs={12}
                sm={viewMode === 'grid' ? 6 : 12}
                md={viewMode === 'grid' ? 4 : 12}
                key={course.id}
              >
                <SuperCourseCard
                  course={course}
                  onEnroll={onEnroll}
                  onViewDetails={onViewDetails}
                  onStartLearning={onStartLearning}
                  onTakeAssessment={onTakeAssessment}
                  onBookmark={onBookmark}
                  isEnrolled={enrolledCourses.includes(course.id)}
                  isBookmarked={bookmarkedCourses.includes(course.id)}
                  showProgress={showProgress}
                  progress={courseProgress[course.id] || 0}
                  showInteractiveFeatures={showInteractiveFeatures}
                  variant={viewMode === 'list' ? 'compact' : 'default'}
                />
              </Grid>
            ))}
          </Grid>
        </Fade>
      )}
    </Box>
  );
};

export default memo(UnifiedCourseList);

import {
  createTheme,
  alpha,
  PaletteMode,
  ThemeOptions,
} from '@mui/material/styles';
import { glassmorphismTokens, getGlassmorphismStyles } from './glassmorphism';
import { getMuiGlassmorphismOverrides } from './muiGlassmorphism';
import getFontConfig from './fonts';

/**
 * Create a unified theme that combines the features of both existing themes.
 * This theme supports both light and dark modes and includes glassmorphism effects.
 *
 * This is the central theme implementation for the application.
 * It replaces the separate macOSTheme and ThemeProvider implementations.
 */
export const createUnifiedTheme = (
  mode: PaletteMode = 'dark',
  direction: 'ltr' | 'rtl' = 'ltr'
): ThemeOptions => {
  const isDark = mode === 'dark';

  return {
    palette: {
      mode,
      // macOS inspired color palette
      primary: {
        main: '#007aff', // Apple blue
        light: '#5ac8fa',
        lighter: 'rgba(90, 200, 250, 0.08)',
        dark: '#0062cc',
        contrastText: '#ffffff',
      },
      secondary: {
        main: '#5856d6', // Apple purple
        light: '#7a78e2',
        dark: '#3634a3',
        contrastText: '#ffffff',
      },
      error: {
        main: '#ff3b30', // Apple red
        light: '#ff6b61',
        dark: '#c70024',
        contrastText: '#ffffff',
      },
      warning: {
        main: '#ff9500', // Apple orange
        light: '#ffb340',
        dark: '#c76500',
        contrastText: '#ffffff',
      },
      info: {
        main: '#64d2ff', // Apple light blue
        light: '#9fe7ff',
        dark: '#0099cc',
        contrastText: '#ffffff',
      },
      success: {
        main: '#34c759', // Apple green
        light: '#70da87',
        dark: '#248a3d',
        contrastText: '#ffffff',
      },
      grey: {
        50: '#f9fafb',
        100: '#f3f4f6',
        200: '#e5e7eb',
        300: '#d1d5db',
        400: '#9ca3af',
        500: '#6b7280',
        600: '#4b5563',
        700: '#374151',
        800: '#1f2937',
        900: '#111827',
      },
      background: {
        default: isDark ? '#121212' : '#f5f5f7',
        paper: isDark ? 'rgba(30, 30, 30, 0.9)' : 'rgba(255, 255, 255, 0.8)', // Enhanced opacity for better contrast
        paperLight: isDark
          ? 'rgba(40, 40, 45, 0.85)'
          : 'rgba(250, 250, 252, 0.85)', // Lighter paper variant
        paperDark: isDark
          ? 'rgba(20, 20, 25, 0.95)'
          : 'rgba(240, 240, 245, 0.95)', // Darker paper variant
      },
      text: {
        primary: isDark ? 'rgba(255, 255, 255, 0.98)' : '#1d1d1f', // Enhanced opacity for better contrast
        secondary: isDark ? 'rgba(255, 255, 255, 0.8)' : '#86868b', // Enhanced opacity for better contrast
        tertiary: isDark ? 'rgba(255, 255, 255, 0.65)' : 'rgba(0, 0, 0, 0.6)', // Additional text color for subtle elements
      },
      divider: isDark ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.08)', // Enhanced divider contrast
    },
    shape: {
      borderRadius: 8,
    },
    typography: {
      // Use font configuration from fonts.ts
      ...getFontConfig(direction === 'rtl'),
      // Add size and line height configurations
      h1: {
        ...getFontConfig(direction === 'rtl').h1,
        fontSize: '2.5rem',
        lineHeight: 1.2,
      },
      h2: {
        ...getFontConfig(direction === 'rtl').h2,
        fontSize: '2rem',
        lineHeight: 1.3,
      },
      h3: {
        ...getFontConfig(direction === 'rtl').h3,
        fontSize: '1.75rem',
        lineHeight: 1.3,
      },
      h4: {
        ...getFontConfig(direction === 'rtl').h4,
        fontSize: '1.5rem',
        lineHeight: 1.4,
      },
      h5: {
        ...getFontConfig(direction === 'rtl').h5,
        fontSize: '1.25rem',
        lineHeight: 1.4,
      },
      h6: {
        ...getFontConfig(direction === 'rtl').h6,
        fontSize: '1.125rem',
        lineHeight: 1.4,
      },
      subtitle1: {
        ...getFontConfig(direction === 'rtl').subtitle1,
        fontSize: '1rem',
        lineHeight: 1.5,
      },
      subtitle2: {
        ...getFontConfig(direction === 'rtl').subtitle2,
        fontSize: '0.875rem',
        lineHeight: 1.5,
      },
      body1: {
        ...getFontConfig(direction === 'rtl').body1,
        fontSize: '1rem',
        lineHeight: 1.5,
      },
      body2: {
        ...getFontConfig(direction === 'rtl').body2,
        fontSize: '0.875rem',
        lineHeight: 1.5,
      },
      button: {
        ...getFontConfig(direction === 'rtl').button,
        fontWeight: 500,
      },
    },
    components: {
      // Glassmorphism and macOS-inspired component styling
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: glassmorphismTokens.common.buttonBorderRadius,
            textTransform: 'none',
            fontWeight: 500,
            padding: '8px 20px',
            transition: glassmorphismTokens.common.transition,
          },
          contained: {
            backgroundColor: isDark ? '#323232' : '#ffffff',
            backdropFilter: `blur(${glassmorphismTokens.common.blur})`,
            WebkitBackdropFilter: `blur(${glassmorphismTokens.common.blur})`,
            boxShadow: isDark
              ? glassmorphismTokens.dark.shadow
              : glassmorphismTokens.light.shadow,
            border: isDark
              ? '1px solid rgba(255, 255, 255, 0.1)'
              : '1px solid rgba(255, 255, 255, 0.18)',
            '&:hover': {
              boxShadow: isDark
                ? glassmorphismTokens.dark.hoverShadow
                : glassmorphismTokens.light.hoverShadow,
              transform: 'translateY(-2px)',
            },
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: glassmorphismTokens.common.borderRadius,
            background: isDark
              ? glassmorphismTokens.dark.background
              : glassmorphismTokens.light.background,
            backdropFilter: `blur(${glassmorphismTokens.common.blur})`,
            WebkitBackdropFilter: `blur(${glassmorphismTokens.common.blur})`,
            border: isDark
              ? `1px solid ${glassmorphismTokens.dark.border}`
              : `1px solid ${glassmorphismTokens.light.border}`,
            boxShadow: isDark
              ? glassmorphismTokens.dark.shadow
              : glassmorphismTokens.light.shadow,
          },
        },
      },
      MuiPaper: {
        defaultProps: {
          elevation: 0,
        },
        styleOverrides: {
          root: {
            borderRadius: glassmorphismTokens.common.borderRadius,
            background: isDark
              ? glassmorphismTokens.dark.background
              : glassmorphismTokens.light.background,
            backdropFilter: `blur(${glassmorphismTokens.common.blur})`,
            WebkitBackdropFilter: `blur(${glassmorphismTokens.common.blur})`,
            border: isDark
              ? `1px solid ${glassmorphismTokens.dark.border}`
              : `1px solid ${glassmorphismTokens.light.border}`,
            boxShadow: isDark
              ? '0 8px 32px 0 rgba(0, 0, 0, 0.3)'
              : '0 8px 32px 0 rgba(31, 38, 135, 0.1)',
          },
        },
      },
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            scrollbarWidth: 'thin',
            '&::-webkit-scrollbar': {
              width: '8px',
              height: '8px',
            },
            '&::-webkit-scrollbar-track': {
              background: isDark ? '#2d2d2d' : '#f1f1f1',
            },
            '&::-webkit-scrollbar-thumb': {
              background: isDark ? '#5c5c5c' : '#c1c1c1',
              borderRadius: '4px',
            },
            '&::-webkit-scrollbar-thumb:hover': {
              background: isDark ? '#6e6e6e' : '#a8a8a8',
            },
            // RTL support is now handled by the typography configuration
          },
          // Global RTL fixes
          'html[dir="rtl"] .MuiButton-startIcon': {
            marginLeft: '8px',
            marginRight: '0',
          },
          'html[dir="rtl"] .MuiButton-endIcon': {
            marginRight: '8px',
            marginLeft: '0',
          },
        },
      },
      // RTL-specific component overrides
      MuiFormControlLabel: {
        styleOverrides: {
          root: {
            '&.MuiFormControlLabel-root': {
              '[dir="rtl"] &': {
                marginLeft: 0,
                marginRight: -11,
              },
            },
            '&.MuiFormControlLabel-labelPlacementStart': {
              '[dir="rtl"] &': {
                marginLeft: 16,
                marginRight: 0,
              },
            },
          },
        },
      },
      MuiListItem: {
        styleOverrides: {
          root: {
            '[dir="rtl"] &': {
              textAlign: 'right',
              direction: 'rtl',
            },
          },
        },
      },
      MuiListItemIcon: {
        styleOverrides: {
          root: {
            '[dir="rtl"] &': {
              marginRight: 0,
              marginLeft: 16,
            },
          },
        },
      },
      MuiListItemText: {
        styleOverrides: {
          root: {
            '[dir="rtl"] &': {
              textAlign: 'right',
            },
          },
        },
      },
      // Add glassmorphism overrides
      // Create a minimal theme object with the necessary properties
      ...getMuiGlassmorphismOverrides({
        palette: { mode },
        spacing: 8, // Default spacing unit
        shape: { borderRadius: 8 },
      }),
    },
    direction,
  };
};

// Export a default theme (dark mode)
const theme = createUnifiedTheme('dark');
export default theme;
